import json
from datetime import timed<PERSON><PERSON>

import yaml
from airflow import DAG
from airflow.decorators import task
from airflow.exceptions import AirflowException
from airflow.models import Variable
from kubernetes import client, config
from metadata.manual_pii import MANUAL_PII

from metadata.pii import EMAIL_FIELD_MARK, EMAIL_FIELDS, SCHEMAS

args = {"owner": "airflow", "start_date": "2022-08-01", "retries": 1, "retry_delay": timedelta(minutes=5)}


def get_configmap_names(env):
    """
    Fetches the names of all configmaps in a given namespace that have a specific label.

    Args:
        env (str): The namespace to fetch configmaps from.

    Returns:
        list: A list of configmap names.

    Raises:
        AirflowException: If no configmaps are found in the given namespace.
    """
    label = "app=pii"
    v1 = client.CoreV1Api()
    configmap_list = v1.list_namespaced_config_map(namespace=env, label_selector=label)
    configmap_names = [item.metadata.name for item in configmap_list.items]
    if not configmap_names:
        raise AirflowException(f"Task cannot fetch list of configmaps from {env} namespace.")
    return configmap_names


def get_pii_schemas(env):
    """
    Fetches PII schemas from configmaps in a given namespace.

    Args:
        env (str): The namespace to fetch configmaps from.

    Returns:
        dict: A dictionary containing PII schemas.
    """
    schema_dict = {}
    schema_for_env = {}
    configmap_names = get_configmap_names(env=env)

    for schema in SCHEMAS[env]:
        configmap_name = "-".join(["data", env, schema, "pii"]).replace("_", "-")
        if configmap_name in configmap_names:
            print(f"Reading configmap {configmap_name} from {env} namespace.")
            schema_for_env[schema] = read_pii(env, configmap_name)
        else:
            print(f"Configmap for {schema} does not exist.")
        if schema in EMAIL_FIELDS.keys():
            for table in EMAIL_FIELDS[schema]:
                if table in schema_for_env[schema]:
                    table_pii_fields = schema_for_env[schema][table]
                    for column in EMAIL_FIELDS[schema][table]:
                        table_pii_fields[table_pii_fields.index(column)] = EMAIL_FIELD_MARK + column
                else:
                    schema_for_env[schema][table] = []
                    for column in EMAIL_FIELDS[schema][table]:
                        schema_for_env[schema][table].append(EMAIL_FIELD_MARK + column)
    schema_dict["pii_fields"] = schema_for_env
    return schema_dict


def read_pii(env, configmap_name):
    """
    Reads PII data from a configmap in a given namespace.

    Args:
        env (str): The namespace where the configmap is located.
        configmap_name (str): The name of the configmap to read from.

    Returns:
        dict: A dictionary containing the PII data.
    """
    v1 = client.CoreV1Api()
    configmap = v1.read_namespaced_config_map(name=configmap_name, namespace=env)
    configmap_data = configmap.data
    dict_to_update = {}
    yaml_data = yaml.safe_load(configmap_data["pii.yml"])
    if type(yaml_data["tables"]) == dict:
        for elem in yaml_data["tables"]:
            dict_to_update[elem] = yaml_data["tables"][elem]
    return dict_to_update


def merge_manual_settings(pii_table_dict, manual_settings):
    """
    Merges manual PII settings into a dictionary of PII table data.

    Args:
        pii_table_dict (dict): The dictionary of PII table data.
        manual_settings (dict): The dictionary of manual PII settings.

    Returns:
        dict: The updated dictionary of PII table data.
    """
    for schema, tables in manual_settings.items():
        print(f"Schema: {schema}\nTables: {tables}")
        if schema not in pii_table_dict["pii_fields"]:
            pii_table_dict["pii_fields"][schema] = tables
            print("in if")
            print(pii_table_dict["pii_fields"][schema])
        else:
            for table, fields in tables.items():
                if table not in pii_table_dict["pii_fields"][schema]:
                    pii_table_dict["pii_fields"][schema][table] = fields
                    print(pii_table_dict["pii_fields"][schema][table])
                else:
                    pii_table_dict["pii_fields"][schema][table].extend(fields)
                    pii_table_dict["pii_fields"][schema][table] = list(set(pii_table_dict["pii_fields"][schema][table]))  # Remove duplicates
                    print(pii_table_dict["pii_fields"][schema][table])
    return pii_table_dict


@task(task_id="add_pii_values")
def add_pii_values():
    """
    Airflow task to add PII values to Airflow Variables.

    This task fetches PII schemas from configmaps in a given namespace, merges in manual PII settings,
    and then adds the resulting PII values to Airflow Variables.

    Raises:
        AirflowException: If the PII Airflow Variable is set to empty.
    """
    env = Variable.get("env")
    config.load_incluster_config()
    pii_table_dict = get_pii_schemas(env=env)
    print(pii_table_dict)

    if not pii_table_dict:
        raise AirflowException("PII Airflow Variable should not be set to empty.")


    manual_pii_settings = MANUAL_PII
    print(manual_pii_settings)
    pii_table_dict = merge_manual_settings(pii_table_dict, manual_pii_settings)
    print(pii_table_dict)

    for keys in pii_table_dict.keys():
        print(keys)
        Variable.set(keys, json.dumps(pii_table_dict[keys]))


with DAG(dag_id="misc_update_pii", default_args=args, schedule_interval="*/30 * * * *", catchup=False) as dag:
    add_pii_values()