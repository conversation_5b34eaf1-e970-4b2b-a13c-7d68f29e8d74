from metadata.appsflyer import <PERSON><PERSON><PERSON><PERSON><PERSON>_CONFIG
from metadata.eber import <PERSON><PERSON><PERSON>_CONFIG
from metadata.gsuite import GSUITE_CONFIG
from metadata.jira import <PERSON>IRA_CONFIG
from metadata.salesforce import SALESFORCE_CONFIG
from metadata.sfmc import SFMC_CONFIG
from metadata.shopify import SH<PERSON><PERSON>Y_CONFIG
from metadata.zendesk import ZENDESK_CONFIG
from common.stringcase import snake_case

INCLUDED_TABLES = {
    "appsflyer_raw_data_reports": {
        "upstream_external_dag_id": "datalake_appsflyer",
        "primary_keys": ["apps_flyer_id", "event_time"],
        "tables": [table for table in APPSFLYER_CONFIG["raw_data_reports"]["app_ids"].keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/appsflyer/delta/raw_data_reports/app_id={table}",
        "object_uri": "gs://{gs_raw_bucket}/appsflyer/objects/{object_path}/app_id={table}",
        "object_path": "raw_data_reports",
        "object_check_date": "t-2"
    },
    "appsflyer_aggregated_report": {
        "upstream_external_dag_id": "datalake_appsflyer",
        "primary_keys": ["date", "media_source", "campaign_id", "adset_id", "ad_id", "af_attribution_flag"],
        "tables": [table for table in APPSFLYER_CONFIG["aggregated_report"]["app_ids"].keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/appsflyer/delta/aggregated_report/app_id={table}",
        "object_uri": "gs://{gs_raw_bucket}/appsflyer/objects/{object_path}/app_id={table}",
        "object_path": "aggregated_report",
        "object_check_date": "t-2"
    },
    "eber": {
        "primary_keys": ["id", "system_id", "nv_created_month"],
        "tables": [table for table in EBER_CONFIG.keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/eber/delta/{table}",
        "object_check_date": "t-2"
    },
    "gsuite": {
        "primary_keys": ["driver_id"],
        "tables": [table for table in GSUITE_CONFIG.keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/gsuite/delta/{table}",
        "object_check_date": "t-2"
    },
    "jira": {
        "primary_keys": ["id", "nv_created_month"],
        "tables": [table for table in JIRA_CONFIG.keys()],
        "main_table_uri": "gs://nv-data-{env}-datalake/jira/delta/{table}"
    },
    "salescloud": {
        "upstream_external_dag_id": "datalake_salesforce_sales_cloud",
        "primary_keys": ["id", "nv_created_month"],
        "tables": [snake_case(table["name"]) for table in SALESFORCE_CONFIG["objects"]],
        "main_table_uri": "gs://nv-data-{env}-data-lake/salesforce/sales_cloud/delta/{table}",
        "object_path": "salesforce/sales_cloud",
        "skip_check_new_data_exist_tables": [
            "user_role", "account_team_member", "case_assignee_mapping_c", "content_note", "issue_type_mapping_c",
            "group", "group_member", "record_type", "target_c", "user", "user_role"
         ],
        "object_check_date": "t-2",
        "skip_date_filter": ["user_role", "group", "group_member"]
    },
    "sfmc_campaign": {
        "upstream_external_dag_id": "datalake_salesforce_sales_cloud",
        "primary_keys": SFMC_CONFIG["campaign"]["primary_keys"],
        "tables": ["campaign"],
        "main_table_uri": "gs://nv-data-{env}-data-lake/sfmc/delta/campaign",
        "object_path": "sfmc",
    },
    "sfmc_mobile_push_demographics": {
        "upstream_external_dag_id": "datalake_salesforce_sales_cloud",
        "primary_keys": SFMC_CONFIG["mobile_push_demographics"]["primary_keys"],
        "tables": ["mobile_push_demographics"],
        "main_table_uri": "gs://nv-data-{env}-data-lake/sfmc/delta/mobile_push_demographics",
        "object_path": "sfmc",
    },
    "shopify": {
        "primary_keys": ["id", "system_id", "nv_created_month"],
        "tables": [table for table in SHOPIFY_CONFIG.keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/shopify/delta/{table}",
    },
    "zendesk": {
        "primary_keys": ["id", "system_id", "nv_created_month"],
        "tables": [table for table in ZENDESK_CONFIG.keys()],
        "main_table_uri": "gs://nv-data-{env}-data-lake/zendesk/delta/{table}",
        "skip_check_new_data_exist_tables": [
            "brands", "groups", "organizations", "ticket_fields", "ticket_forms", "satisfaction_ratings"
         ]
    },
}