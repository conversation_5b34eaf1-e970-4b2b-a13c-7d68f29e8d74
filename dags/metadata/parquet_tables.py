from metadata import data_science_tables
from metadata.constants import BI_SENSITIVE_REPORTS_BASE_URI, DATALAKE_BASE_URI, DATAWAREHOUSE_BASE_URI, MASKED_DATA_WAREHOUSE_BASE_URI


class DataScience:
    def __init__(self, env="prod"):
        self.SIMILAR_ADDRESSES = data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).SIMILAR_ADDRESSES


class DataWarehouse:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = BI_SENSITIVE_REPORTS_BASE_URI.format(env)
        self._URI_BASE = MASKED_DATA_WAREHOUSE_BASE_URI.format(env)
        self.CALENDAR = f"{self._URI_BASE}/calendar"
        self.CALENDAR_RECOVERY = f"{self._URI_BASE}/calendar_recovery"
        self.CALENDAR_SHIPPER = f"{self._URI_BASE}/calendar_shipper"
        self.CALENDAR_SORT = f"{self._URI_BASE}/calendar_sort"
        self.CALENDAR_SPEED = f"{self._URI_BASE}/calendar_speed"
        self.DRIVER_CIF_MIGRATION_REPORT = f"{self._URI_BASE}/driver_cif_migration_report"
        self.ETL_TASKS_MAPPING = f"{self._URI_BASE}/etl_tasks_mapping"
        self.ETL_TASKS_LOG_FULL = f"{self._URI_BASE}/etl_tasks_log_full"
        self.HUB_DRIVING_DISTANCES = f"{self._URI_BASE}/hub_driving_distances"
        self.SELLER_OPERATIONAL_METRICS = f"{self._BI_SENSITIVE_BASE}/seller_operational_metrics"
        self.SS_SEGMENTATION_DAILY = f"{self._URI_BASE}/ss_segmentation_daily"


class DriverLocation:
    def __init__(self, env="prod"):
        self.DRIVER_LOCATION = f"{DATALAKE_BASE_URI.format(env)}/driver_location"


class GSheets:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = f"{BI_SENSITIVE_REPORTS_BASE_URI.format(env)}/gsheets"
        self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/gsheets"
        self.CUSTOM_MAPPING_MY = f"{self._URI_BASE}/custom_mapping/my_master"
        self.CUSTOM_MAPPING_VN = f"{self._URI_BASE}/custom_mapping/vn_master"
        self.SALES_TARGETS_BU = f"{self._URI_BASE}/sales_targets_bu/sheet1"
        self.NINJA_MART_SALES_TARGETS = f"{self._URI_BASE}/ninja_mart_sales_target/sales_target_monthly"
        self.SFMC_CAMPAIGN_NAMING = f"{self._URI_BASE}/sfmc_campaign_naming/sfmc_campaign_naming"
        self.COST_CARD_HUB_MAPPING = f"{self._BI_SENSITIVE_BASE}/cost_data/hub_mapping"
        self.COST_CARD_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_pricing_config"
        self.COST_CARD_TIKTOK_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_pricing_config"
        self.COST_CARD_COST_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_cost_config"
        self.COST_CARD_AIRSEA_COST_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_airhaul_seahaul_cost_config"
        self.COST_CARD_COST_CONFIG_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_cost_config"
        self.COST_CARD_MM_DRIVER_MAPPING_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_mm_driver_mapping"
        self.COST_CARD_MM_HUB_MAPPING_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_mm_hub_mapping"
        self.HUB_DRIVING_DISTANCES_MY = f"{self._URI_BASE}/driving_distances/my"
        self.HUB_DRIVING_DISTANCES_PH = f"{self._URI_BASE}/driving_distances/ph"
        self.HUB_DRIVING_DISTANCES_SG = f"{self._URI_BASE}/driving_distances/sg"
        self.HUB_DRIVING_DISTANCES_TH = f"{self._URI_BASE}/driving_distances/th"
        self.HUB_DRIVING_DISTANCES_VN = f"{self._URI_BASE}/driving_distances/vn"
        self.ID_LM_PAYROLL_EX_JABO_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/ex_jabo_parcel_points"
        self.ID_LM_PAYROLL_JABO_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/jabo_parcel_points"
        self.ID_LM_PAYROLL_PARCEL_RATES = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/parcel_rates"
        self.ID_LM_PAYROLL_VOLUME_BONUSES = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/volume_bonuses"
        self.ID_LM_PAYROLL_HUB_DATA = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/hub_data"
        self.ID_LM_PAYROLL_ZONE_DATA = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/zone_data"
        self.ID_LM_PAYROLL_DENSITY_PRIORITY = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/density_priority"
        self.ID_LM_SUCCESS_RATE_GOAL = f"{self._BI_SENSITIVE_BASE}/success_rate_goal"
        self.MM_TRIP_STITCH_COST = f"{self._BI_SENSITIVE_BASE}/mm_cost/mm_trip_stitch_cost"
        self.TRIP_COST_TIERS = f"{self._BI_SENSITIVE_BASE}/mm_cost/trip_cost_tiers"
        self.VENDOR_DEVICES = f"{self._URI_BASE}/vendor_device_id/sheet3"
        self.VN_MISSORT_EXCLUSIONS = f"{self._URI_BASE}/vn_missort_exclusions/sheet1"
        self.ID_FRAUD_EMAIL_DOMAIN_WHITELIST = f"{self._URI_BASE}/id_fraud/email_domain_whitelist"
        self.ID_FRAUD_SHIPPER_WHITELIST_BY_MITRA = f"{self._URI_BASE}/id_fraud/shipper_whitelist_by_mitra"
        self.ID_FRAUD_COD_SUCCESS_SHIPPER_LIST = f"{self._URI_BASE}/id_fraud/cod_success"
        self.ID_FRAUD_SLA_BREACH_SHIPPER_LIST = f"{self._URI_BASE}/id_fraud/sla_breach"
        self.ID_FRAUD_SHIPPER_BLACKLIST = f"{self._URI_BASE}/id_fraud/fraud_shippers"
        self.ND_MM_SLA_JAN21_JUL21 = f"{self._URI_BASE}/nd_mm_sla_jan21_jul21/sheet1"
        self.ID_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/id"
        self.MY_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/my"
        self.PH_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/ph"
        self.SG_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/sg"
        self.TH_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/th"
        self.VN_FM_SHIPPER_EXCLUSION_LIST = f"{self._URI_BASE}/reg_fm_shipper_exclusion_list/vn"
        self.SHIPPER_PICKUP_CUTOFF = f"{self._URI_BASE}/cutoff_for_n+0_shipper_pickup_kpi/sheet1"


class OrderNPSSubmissions:
    def __init__(self, env="prod"):
        self.ORDER_NPS_SUBMISSIONS = f"{DATALAKE_BASE_URI.format(env)}/order_nps_submissions"
