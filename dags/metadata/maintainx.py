MAINTAINX_MAX_RESULTS_LIMIT = 200
MAINTAINX_CONFIG = {
    "assets": {
        "schema": {
            "id": "str",
            "name": "object",
            "barcode": "str",
            "description": "str",
            "team_ids": "object",
            "asset_types": "object",
            "parent_id": "str",
            "location_id": "str",
            "thumbnail": "object",
            "attachments": "object",
            "extra_fields": "object",
            "vendor_ids": "object",
            "created_at": "datetime64",
            "updated_at": "datetime64",
            "deleted_at": "str",
            "status": "object",
        },
        "nested_columns": {"thumbnail", "attachments", "extra_fields", "status"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "time_fields": ("created_at", "updated_at"),
    },
    "workOrders": {
        "schema": {
            "id": "str",
            "sequential_id": "str",
            "asset_id": "str",
            "attachments": "object",
            "categories": "object",
            "completed_at": "datetime64",
            "completer_id": "str",
            "created_at": "datetime64",
            "creator_id": "str",
            "deleted_at": "datetime64",
            "description": "str",
            "due_date_is_full_day": "str",
            "due_date": "datetime64",
            "external_data": "object",
            "start_date": "datetime64",
            "location_id": "str",
            "next_id": "str",
            "organization_id": "str",
            "previous_id": "str",
            "priority": "str",
            "recurrence_info": "object",
            "requester_id": "str",
            "status": "str",
            "thumbnail": "object",
            "title": "str",
            "updated_at": "datetime64",
            "extra_fields": "object",
            "vendor_ids": "object",
            "assignee_ids": "object",
            "team_ids": "object",
            "procedure": "object",
            "progress": "object",
            "assignees": "object",
            "asset": "object",
            "location": "object",
            "parts": "object",
            "times": "object",
            "time_items": "object",
            "expenditures": "object",
        },
        "nested_columns": {
            "attachments",
            "categories",
            "external_data",
            "recurrence_info",
            "thumbnail",
            "extra_fields",
            "vendor_ids",
            "assignee_ids",
            "team_ids",
            "procedure",
            "progress",
            "assignees",
            "asset",
            "location",
            "parts",
            "times",
            "time_items",
            "expenditures",
        },
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "time_fields": ("completed_at", "created_at", "deleted_at", "due_date", "start_date", "updated_at"),
    },
}
