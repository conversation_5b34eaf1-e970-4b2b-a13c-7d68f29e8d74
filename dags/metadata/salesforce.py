"""
Salesforce objects to pull into our data ecosystem

Steps for adding a new object:
1. Check if the object exists in Salesforce Enterprise WSDL
2. Check if the object has any field type that is not accounted for in salesforce_hook._PRIMITIVE_TYPE_TO_PANDAS_TYPE
    - if yes, handle it
    - if not, proceed
3. Check what should be the created_at_field and updated_at_field for that object
"""
SALESFORCE_CONFIG = {
    "objects": [
        {"name": "Account", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "ContentDocument", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "ContentDocumentLink", "created_at_field": None, "updated_at_field": "SystemModstamp"},
        {"name": "AccountContactRelation", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "AccountHistory", "created_at_field": "CreatedDate", "updated_at_field": "CreatedDate"},
        {"name": "AccountTeamMember", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Case", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Case_Assignee_Mapping__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {
            "name": "Case_Escalation_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate",
        },
        {"name": "CaseComment", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "CaseHistory", "created_at_field": "CreatedDate", "updated_at_field": "CreatedDate"},
        {"name": "CaseTeamMember", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Contact", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "ContentNote", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "ContentVersion", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Issue_Type_Mapping__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "EmailMessage", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Event", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {
            "name": "et4ae5__IndividualEmailResult__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate",
        },
        {
            "name": "et4ae5__IndividualLink__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate",
        },
        {"name": "FeedItem", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Group", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "GroupMember", "created_at_field": None, "updated_at_field": "SystemModstamp"},
        {"name": "Lead", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "LoginHistory", "created_at_field": "LoginTime", "updated_at_field": "LoginTime"},
        {"name": "ND_Rate_Card__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Opportunity", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "RecordType", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {
            "name": "pandadoc__PandaDocDocument__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate",
        },
        {"name": "pandadoc__PandaDocLog__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "SBQQ__Quote__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "SBQQ__QuoteLine__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Target__c", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "Task", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "User", "created_at_field": "CreatedDate", "updated_at_field": "LastModifiedDate"},
        {"name": "UserRole", "created_at_field": None, "updated_at_field": "LastModifiedDate"},
        {
            "name": "ApexTestResultLimits",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "VisualforceAccessMetrics",
            "created_at_field": "MetricsDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LiveChatTranscriptShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LiveChatTranscript",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LiveChatTranscriptHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LiveChatVisitor",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "FileSearchActivity",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Report",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__RecordJob__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "TopicUserEvent",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "DuplicateRecordSet",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "PermissionSetAssignment",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LightningUsageByFlexiPageMetrics",
            "created_at_field": "MetricsDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Product_Catalog__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "TopicAssignment",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PermissionSetTabSetting",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Pricer_Rate_Card__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "VerificationHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "CollaborationGroupRecord",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__Quote__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Status_Change_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ForecastingItem",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SetupAuditTrail",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LiveAgentSession",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LiveAgentSessionHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LiveAgentSessionShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LightningUsageByBrowserMetrics",
            "created_at_field": "MetricsDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SetupEntityAccess",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserServicePresence",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "UserServicePresenceShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LiveChatTranscriptEvent",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ProcessInstanceNode",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ProcessInstance",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OrgMetricScanResult",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ForecastingFact",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FeedRevision",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteLine__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Case_Escalation_History__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "DuplicateRecordItem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityLineItem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ConversationContextEntry",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityContactRole",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ND_Rate_Card__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__Quote__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "pandadoc__DocStatus__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Attachment",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "CampaignMember",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "FeedComment",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FieldPermissions",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AgentWorkShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AgentWork",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LoginIp",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ProcessInstanceStep",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Log__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LightningUsageByAppTypeMetrics",
            "created_at_field": "MetricsDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexTestResult",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "IdpEventLog",
            "created_at_field": None,
            "updated_at_field": "Timestamp"
        },
        {
            "name": "LogEntry__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Log__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "API_Logs__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "API_Logs__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ContactFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Opportunity__hd",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Chaser_Case_ReOpen_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Duplicate_Request__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ContentDocumentHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Log__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "UserFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AccountFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "EventWhoRelation",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ContentVersionHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LoginGeo",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TaskFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityFieldHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LightningUsageByPageMetrics",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EventFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Reply_Validation_Fail_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "EventRelation",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ContactHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ETA_Compliance_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LeadFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Case_ETA_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Case_Response_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Case_Escalations_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AccountShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "LeadShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "CaseShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        }
    ]
}