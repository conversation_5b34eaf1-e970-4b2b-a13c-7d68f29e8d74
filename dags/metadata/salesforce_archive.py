SALESFORCE_ARCHIVE_CONFIG = {
    "objects": [
        {
            "name": "ActiveScratchOrg",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Organization",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LoggerSettings__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LiveChatUserConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LiveChatDeployment",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LiveChatButton",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LEXMAGICMOVER__NAMConfig__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PlatformCachePartition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "GrantedByLicense",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DuplicateJobMatchingRuleDefinition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DuplicateJobMatchingRule",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DuplicateJobDefinition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DuplicateJob",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Customised_Price_Book__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Csv_Values__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EmailDomainKey",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProfileSkill",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProfileSkillHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ProfileSkillUser",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "rh2__PS_Settings__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "pandadoc__Pricing_Item_Mapping__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__ET4AE_Config__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__Configuration__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__Campaign_Member_Configuration__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__Business_Unit__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserPermissionAccess",
            "created_at_field": None,
            "updated_at_field": "LastCacheUpdate"
        },
        {
            "name": "UserConfigTransferButton",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SocialPost",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SamlSsoConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteTemplate__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteTemplate__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "SBQQ__ImportFormat__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProfileSkillUserHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ProfileSkillUserFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Community",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "NinjaVanAPICredentials__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "rh2__Rollup_Helper_Record_Scope__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AuthProvider",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ApexEmailNotification",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CampaignInfluenceModel",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ActiveScratchOrgShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AuthConfigProviders",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ByPass_Automation__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ActiveScratchOrgHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "AuthConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TodayGoal",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CspTrustedSite",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Domain",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ServiceChannelStatusField",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ServiceChannelStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentWorkspace",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DeclinedEventRelation",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EmailServicesFunction",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__CustomActionCondition__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TodayGoalShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AssignmentRule",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TaskPriority",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CodeBuilder__Workspace__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PlatformCachePartitionType",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "rh2__PS_Describe__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OrderStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OmniSupervisorConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OmniSupervisorConfigGroup",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SolutionStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexLog",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MacroShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Id_Upload_CSV_Granular_Header_Order__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Site",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Id_Email_To_Case_CSV_Header_Order__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "BrandTemplate",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LightningExperienceTheme",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PresenceUserConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MacroHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Pricebook2History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "DomainSite",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "BrandingSet",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ServiceChannel",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Macro",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "rh2__PS_Rollup_Audit__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "rh2__HS_Filter__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "pandadoc__Recipient_Map__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContractStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "QueueRoutingConfig",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexTestSuite",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CollaborationGroupMemberRequest",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WaveAutoInstallRequest",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__SearchFilter__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentWorkspaceDoc",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "pandadoc__PandaDoc_JsonBulder_Mapping__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentWorkspacePermission",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "pandadoc__Object_Tokens__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Pricebook2",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CorsWhitelistEntry",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TaskStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FieldSecurityClassification",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Ninja_Van_API_Credentials__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__ImportColumn__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__LineColumn__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteTerm__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CustomNotificationType",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ScratchOrgInfoShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ScratchOrgInfo",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ExternalEventMapping",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "pandadoc__TriggerSetting__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Ninja_Van_API_Credentials__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ExternalEventMappingShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ServicePresenceStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__AttributeSet__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DatedConversionRate",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CurrencyType",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__TermCondition__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Id_Upload_CSV_Required_Fields__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OrgWideEmailAddress",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LeadStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PartnerRole",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__TemplateSection__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ScratchOrgInfoHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "WorkBadgeDefinitionShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "MacroInstruction",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__TemplateContent__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ForecastingType",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WorkBadgeDefinition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WorkAccess",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Quote",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserLicense",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserAccountTeamMember",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MatchingRule",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SiteHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ForecastingCategoryMapping",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FeedPollChoice",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "BusinessHours",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "DuplicateRule",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "pandadoc__Settings__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__SendDefinition__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OpportunityTeamMemberHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "CustomPermission",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FiscalYearSettings",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LiveChatUserConfigUser",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ListEmailRecipientSource",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FeedPollVote",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Announcement",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MatchingRuleItem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CaseStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PackageLicense",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ListEmail",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ListEmailShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Upload_CSV_Header_Order__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WorkAccessShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "TenantUsageEntitlement",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__SummaryVariable__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__ConfigurationAttribute__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PermissionSetGroup",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexTestRunResult",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteLineGroup__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "QuickText",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteLineGroup__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "QuickTextShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__ProductAttribute__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserPackageLicense",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ConnectedApplication",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Issue_Type_Mapping__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "UserEmailPreferredPerson",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserEmailPreferredPersonShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "BusinessProcess",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "BrandingSetProperty",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OnboardingMetrics",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UiFormulaCriterion",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Translation",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UiFormulaRule",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Discount_Catalog__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Discount_Catalog__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ND_Discount_Catalog__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "OpportunityStage",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__ProductAttributeSet__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FlowInterviewShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "FlowInterview",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PermissionSetLicense",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__ProductOption__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__PriceRule__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__CustomAction__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__FieldSetMetadata__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OmniSupervisorConfigUser",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Campaign",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OrgMetric",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Synched_Ratecard__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Synched_Ratecard__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Lead_Assignment_Mapping__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Marketing__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "RecordTypeLocalization",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AppMenuItem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProcessDefinition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ForecastingTypeToCategory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WebLink",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Note",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "TestSuiteMembership",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__PriceCondition__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AuraDefinitionBundle",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Profile",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexComponent",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ListViewChart",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CaseTeamRole",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ExternalEvent",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Lead_Assignee__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexTrigger",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MacroUsage",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MacroUsageShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "UserAppMenuCustomizationShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "UserAppMenuCustomization",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "FlowDefinitionView",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "SBQQ__QuoteDocument__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "StaticResource",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CollaborationGroup",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Holiday",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "RecordType",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__LookupQuery__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PresenceUserConfigUser",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "cooby__WhatsApp_chat__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Issue_Type_Mapping__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Document",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Hub_SLA__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProcessInstanceWorkitem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PermissionSetGroupComponent",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ProcessNode",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CronTrigger",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "cooby__WhatsApp_user__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Exception_Log__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexPage",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__PriceAction__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CampaignMemberStatus",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "QueueSobject",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Assignee_Mapping__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "LightningExitByPageMetrics",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Period",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__InstallProcessorLog__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PermissionSet",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentFolder",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AuraDefinitionInfo",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "AuraDefinition",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "WebLinkLocalization",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__QuoteDocument__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "UserRole",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PromptAction",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Product2History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Product2",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EmailServicesAddress",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "SBQQ__ColumnMetadata__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "LightningToggleMetrics",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ApexTestQueueItem",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PromptActionShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Dashboard",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserPreference",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EmailTemplate",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Folder",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "CollaborationGroupMember",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ListView",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentWorkspaceMember",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "EntityDefinition",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ContentDocumentFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Hub_Details__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_SLA_History__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "OpportunityTeamMember",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OrgMetricScanSummary",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Group",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "AuthSession",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "XB_Rate_Card__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "XB_Rate_Card__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "Case_Assignee_Mapping__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "PendingServiceRouting",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Owner__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "RecentlyViewed",
            "created_at_field": None,
            "updated_at_field": "LastViewedDate"
        },
        {
            "name": "ContentNote",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ApexClass",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__Automated_Send__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "OauthToken",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "PricebookEntryHistory",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "PricebookEntry",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ChatterActivity",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentAsset",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "EntitySubscription",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "PermissionSetLicenseAssign",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Target__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "cooby__Message__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Owner__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Pricer_Rate_Card__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "UserAppInfo",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__AggregateLink__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "QuickTextUsage",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "QuickTextUsageShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "UndecidedEventRelation",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "UserLogin",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "User",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Calendar",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "MSA__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Topic",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "et4ae5__Automated_Send__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "ContentDistributionView",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ObjectPermissions",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Picklist_Helper__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Picklist_Helper__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "ND_Picklist_Helper__History",
            "created_at_field": "CreatedDate",
            "updated_at_field": "CreatedDate"
        },
        {
            "name": "CollaborationGroupFeed",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ContentDistribution",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_SLA_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "GroupMember",
            "created_at_field": None,
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Product_Catalog__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ND_Product_Catalog__Share",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "Chaser_Case_ReOpen_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Duplicate_Request__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Status_Change_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_ETA_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Response_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Escalations_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "ETA_Compliance_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Case_Update_Log__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        },
        {
            "name": "Reply_Validation_Fail_History__c",
            "created_at_field": "CreatedDate",
            "updated_at_field": "SystemModstamp"
        }
]
}
