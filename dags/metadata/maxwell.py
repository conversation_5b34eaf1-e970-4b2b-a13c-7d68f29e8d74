MAXWELL_SCHEMAS = {
    "dev": {"mysql_qa": ["route_qa_gl", "driver_qa_gl"]},
    "prod": {
        "cluster_1": ["dash_prod_gl", "loyalty_prod_gl", "shipper_prod_gl"],
        "cluster_2": [
            "3pl_prod_gl",
            "direct_prod_gl",
            "sort_prod_gl",
            "ticketing_prod_gl",
        ],
        "cluster_3": [
            "notifications_email_prod_gl",
            "notifications_prod_gl",
            "notifications_sms_prod_gl",
            "notifications_v2_prod_gl",
        ],
        "cluster_4": ["events_prod_gl"],
        "cluster_5": ["control_prod_gl", "ocreate_prod_gl"],
        "core_id": ["core_prod_id"],
        "core_mm_my": ["core_prod_mm", "core_prod_my"],
        "core_ph": ["core_prod_ph"],
        "core_sg_th": ["core_prod_sg", "core_prod_th"],
        "core_vn": ["core_prod_vn"],
        "cx": ["consignee_prod_gl", "sns_prod_gl", "url_shortener_gl"],
        "dp": ["dp_prod_gl"],
        "driver": ["driver_prod_gl"],
        "hub": ["movement_trip_prod_gl", "hub_prod_gl"],
        "misc": ["addressing_prod_gl", "wms_prod_gl"],
        "route": ["route_prod_gl"],
        "sort": ["sort_vendor_prod_gl"],
        "station": ["station_prod_gl"],
    },
}
