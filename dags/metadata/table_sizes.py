TABLE_SIZES = {
    "dev": {
        "compaction": {
            "large": [
                "pii_delta_3pl_prod_gl_events",
                "pii_delta_3pl_prod_gl_documents",
                "pii_delta_events_prod_gl_order_events",
                "pii_delta_3pl_prod_gl_async_task_tab",
                "pii_delta_3pl_prod_gl_bag_events"
            ]
        },
        "cdc": {
            "large": [
                "pii_delta_3pl_prod_gl_events",
                "pii_delta_3pl_prod_gl_documents",
                "pii_delta_events_prod_gl_order_events",
                "pii_delta_3pl_prod_gl_async_task_tab",
                "pii_delta_3pl_prod_gl_bag_events"
            ],
            "large_l2": [
                "pii_delta_pricing_prod_gl_pricing_orders"
            ],
            "large_l3": [
                "pii_delta_core_prod_my_orders"
            ]
        },
        "cdc_hive": {
            "medium": [
                "3pl_prod_gl.bags",
                "3pl_prod_gl.services",
                "3pl_prod_gl.shipment_events",
                "3pl_prod_gl.shipments",
                "3pl_prod_gl.shippers",
                "3pl_prod_gl.vendor_orders",
                "aaa_prod_gl.user_info",
                "aaa_prod_gl.users",
                "aaa_prod_gl.users_groups",
                "billing_prod_gl.priced_orders",
                "core_prod_id.blobs",
                "core_prod_th.inbound_scans",
                "core_prod_id.cod_inbounds",
                "core_prod_id.order_batches",
                "core_prod_id.order_batch_items",
                "core_prod_id.orders",
                "core_prod_id.outbound_scans",
                "core_prod_id.route_waypoint",
                "core_prod_id.transaction_blob",
                "core_prod_id.transactions",
                "core_prod_id.waypoints",
                "core_prod_id.order_jaro_scores_v2",
                "core_prod_id.warehouse_sweeps",
                "core_prod_id.waypoints_photos",
                "core_prod_my.orders",
                "core_prod_my.outbound_scans",
                "core_prod_my.reservations",
                "core_prod_my.waypoints",
                "core_prod_ph.orders",
                "core_prod_ph.outbound_scans",
                "core_prod_ph.reservations",
                "core_prod_ph.waypoints",
                "core_prod_ph.order_jaro_scores_v2",
                "core_prod_ph.order_pickups",
                "core_prod_ph.order_tags",
                "core_prod_ph.route_logs",
                "core_prod_ph.route_waypoint",
                "core_prod_ph.transactions",
                "core_prod_ph.transaction_failure_reason",
                "core_prod_sg.cod_inbounds",
                "core_prod_sg.cod_collections",
                "core_prod_sg.cods",
                "core_prod_sg.inbound_scans",
                "core_prod_sg.order_jaro_scores_v2",
                "core_prod_sg.outbound_scans",
                "core_prod_sg.route_waypoint",
                "core_prod_sg.transaction_blob",
                "core_prod_sg.transactions",
                "core_prod_sg.warehouse_sweeps",
                "core_prod_th.inbound_scans",
                "core_prod_th.transaction_failure_reason",
                "core_prod_my.cod_collections",
                "core_prod_my.inbound_scans",
                "core_prod_th.waypoints",
                "core_prod_vn.blobs",
                "core_prod_vn.cod_collections",
                "core_prod_vn.cods",
                "core_prod_vn.inbound_scans",
                "core_prod_vn.order_jaro_scores_v2",
                "core_prod_vn.order_details",
                "core_prod_vn.order_pickups",
                "core_prod_vn.route_logs",
                "core_prod_vn.route_waypoints",
                "core_prod_mm.cods",
                "core_prod_mm.orders",
                "core_prod_mm.reservations",
                "core_prod_mm.transactions",
                "core_prod_mm.waypoints",
                "core_prod_mm.transaction_failure_reason",
                "core_prod_mm.route_logs",
                "dp_prod_gl.receipts",
                "drivers_prod_gl.driver_contacts",
                "drivers_prod_gl.driver_devices",
                "drivers_prod_gl.driver_vehicles",
                "drivers_prod_gl.drivers",
                "drivers_prod_gl.driver_zone_preferences",
                "epic_prod_gl.external_orders",
                "events_prod_gl.route_events",
                "ocreate_prod_gl.generated_tracking_ids",
                "dp_prod_gl.dp_job_orders",
                "sns_prod_gl.messages"
            ]
        }
    },
    "prod": {
        "cdc": {
            "large": [
                "3pl_prod_gl.parcels",
                "3pl_prod_gl.export_events",
                "3pl_prod_gl.exported_event_files",
                "3pl_prod_gl.vendor_requests",
                "3pl_prod_gl.async_task_tab",
                "process_cdc_messages_3pl_prod_gl_parcels",
                "process_cdc_messages_core_prod_vn_orders",
                "billing_prod_gl.cod_orders",
                "route_prod_gl.waypoints",
                "core_prod_id.transactions",
                "core_prod_id.waypoints",
                "core_prod_id.order_batch_items",
                "core_prod_ph.order_batches",
                "core_prod_ph.order_batch_items",
                "core_prod_ph.orders",
                "core_prod_ph.transactions",
                "core_prod_ph.waypoints",
                "station_prod_gl.parcels",
                "sort_vendor_prod_gl.parcel_measurement_scan",
                "process_cdc_messages_pricing_prod_gl_pricing_orders",
                "process_cdc_messages_pricing_prod_gl_pricing_orders_history",
                "route_prod_gl.waypoints",
                "core_prod_vn.orders",
                "dp_prod_gl.consignee_info",
                "sns_prod_gl.chat_ticket_queue_entries",
                "pricing_prod_gl.pricing_evt_dlq",
                "ninjamart_report.user_order_status_log",
                "pricing_prod_gl.pricing_orders_history",
                "delta_ninjamart_report_user_order_status_log",
                "pii_delta_dp_prod_gl_consignee_info",
                "pii_delta_driver_prod_gl_parcels",
                "process_cdc_messages_core_prod_id_orders",
                "merge_delta_shipper_sla_days",
                "pii_delta_3pl_prod_gl_events",
                "pii_delta_3pl_prod_gl_documents",
                "pii_delta_events_prod_gl_order_events",
                "pii_delta_3pl_prod_gl_async_task_tab",
                "pii_delta_3pl_prod_gl_bag_events"
            ],
            "large_l2": [
                "driver_prod_gl.parcels"
            ],
            "large_l3": [
                "pii_delta_core_prod_my_orders",
                "pii_delta_pod_validation_prod_gl_assignments",
                "pii_delta_billing_prod_gl_cod_orders"
            ],
            "medium_legacy_datetimeRebaseModeInWrite": [
                "pii_delta_route_prod_gl_waypoints"
            ],
            "recovery": [],
            "recovery_2": [],
            "recovery_events_3": [],
            "recovery_orders":["pii_delta_core_prod_id_orders", "pii_delta_pricing_prod_gl_pricing_orders"],
        },
        "cdc_hive": {
            "medium": [
                "3pl_prod_gl.bags",
                "3pl_prod_gl.services",
                "3pl_prod_gl.shipment_events",
                "3pl_prod_gl.shipments",
                "3pl_prod_gl.shippers",
                "3pl_prod_gl.vendor_orders",
                "aaa_prod_gl.user_info",
                "aaa_prod_gl.users",
                "aaa_prod_gl.users_groups",
                "billing_prod_gl.priced_orders",
                "core_prod_id.blobs",
                "core_prod_th.inbound_scans",
                "core_prod_id.cod_inbounds",
                "core_prod_id.order_batches",
                "core_prod_id.order_batch_items",
                "core_prod_id.orders",
                "core_prod_id.outbound_scans",
                "core_prod_id.route_waypoint",
                "core_prod_id.transaction_blob",
                "core_prod_id.transactions",
                "core_prod_id.waypoints",
                "core_prod_id.order_jaro_scores_v2",
                "core_prod_id.warehouse_sweeps",
                "core_prod_id.waypoints_photos",
                "core_prod_my.orders",
                "core_prod_my.outbound_scans",
                "core_prod_my.reservations",
                "core_prod_my.waypoints",
                "core_prod_ph.orders",
                "core_prod_ph.outbound_scans",
                "core_prod_ph.reservations",
                "core_prod_ph.waypoints",
                "core_prod_ph.order_jaro_scores_v2",
                "core_prod_ph.order_pickups",
                "core_prod_ph.order_tags",
                "core_prod_ph.route_logs",
                "core_prod_ph.route_waypoint",
                "core_prod_ph.transactions",
                "core_prod_ph.transaction_failure_reason",
                "core_prod_sg.cod_inbounds",
                "core_prod_sg.cod_collections",
                "core_prod_sg.cods",
                "core_prod_sg.inbound_scans",
                "core_prod_sg.order_jaro_scores_v2",
                "core_prod_sg.outbound_scans",
                "core_prod_sg.route_waypoint",
                "core_prod_sg.transaction_blob",
                "core_prod_sg.transactions",
                "core_prod_sg.warehouse_sweeps",
                "core_prod_th.inbound_scans",
                "core_prod_th.transaction_failure_reason",
                "core_prod_my.cod_collections",
                "core_prod_my.inbound_scans",
                "core_prod_th.waypoints",
                "core_prod_vn.blobs",
                "core_prod_vn.cod_collections",
                "core_prod_vn.cods",
                "core_prod_vn.inbound_scans",
                "core_prod_vn.order_jaro_scores_v2",
                "core_prod_vn.order_details",
                "core_prod_vn.order_pickups",
                "core_prod_vn.route_logs",
                "core_prod_vn.route_waypoints",
                "core_prod_mm.cods",
                "core_prod_mm.orders",
                "core_prod_mm.reservations",
                "core_prod_mm.transactions",
                "core_prod_mm.waypoints",
                "core_prod_mm.transaction_failure_reason",
                "core_prod_mm.route_logs",
                "dp_prod_gl.receipts",
                "drivers_prod_gl.driver_contacts",
                "drivers_prod_gl.driver_devices",
                "drivers_prod_gl.driver_vehicles",
                "drivers_prod_gl.drivers",
                "drivers_prod_gl.driver_zone_preferences",
                "epic_prod_gl.external_orders",
                "events_prod_gl.route_events",
                "ocreate_prod_gl.generated_tracking_ids",
                "dp_prod_gl.dp_job_orders",
                "sns_prod_gl.messages"
            ],
            "large": [
                "route_prod_gl.waypoints",
                "pricing_prod_gl.pricing_orders",
                "route_prod_gl.sr_coverages",
                "3pl_prod_gl.parcels",
                "address_appraiser_prod_gl.order_coordinates",
                "core_prod_my.transactions",
                "first_mile_prod_gl.shipment_first_mile_addresses",
                "events_prod_gl.order_events",
                "hub_prod_gl.shipment_orders",
                "shipper_prod_gl.shipper_addresses",
                "shipper_prod_gl.shipper_settings",
                "shipper_prod_gl.shippers",
                "route_prod_gl.route_logs",
                "route_prod_gl.route_tags",
                "route_prod_gl.waypoint_photos",
                "ocreate_prod_gl.reserve_tracking_ids",
                "drivers_prod_gl.driver_contacts",
                "drivers_prod_gl.driver_devices",
                "drivers_prod_gl.driver_vehicles",
                "first_mile_prod_gl.shipper_first_mile_addresses",
                "core_prod_vn.waypoints",
                "core_prod_vn.transactions",
                "core_prod_vn.transaction_failure_reason",
                "ticketing_prod_gl.ticket_logs",
                "core_prod_sg.orders"
            ]
        },
        "compaction": {
            "large": [
                "billing_prod_gl.priced_orders",
                "core_prod_id.blobs",
                "core_prod_id.order_batch_items",
                "core_prod_id.orders",
                "core_prod_id.transactions",
                "core_prod_id.waypoints",
                "core_prod_ph.orders",
                "core_prod_ph.transactions",
                "core_prod_ph.waypoints",
                "core_prod_vn.inbound_scans",
                "core_prod_vn.orders",
                "events_prod_gl.order_events",
                "hub_prod_gl.scans",
                "hub_prod_gl.shipment_orders",
                "ocreate_prod_gl.reserve_tracking_ids",
                "control_prod_gl.proofs",
                "control_prod_gl.proof_jobs"
            ],
            "larger": [
                "events_prod_gl.pickup_events"
            ]
        },
        "dwh": {
            "medium": [
                "poh_metrics_masked_id",
                "poh_metrics_masked_ph",
                "poh_metrics_masked_sg",
                "cod_orders_to_examine_masked_id",
                "high_cod_price_scrapping_report_masked_id",
                "pets_tickets_enriched_masked_mm",
                "pets_tickets_enriched_masked_my",
                "pets_tickets_enriched_masked_ph",
                "pets_tickets_enriched_masked_sg",
                "pets_tickets_enriched_masked_vn",
                "address_verification_accuracy_report_masked_vn",
                "cisp_terminal_status_report_masked_id",
                "country_completion_vol_daily_masked_my",
                "country_completion_vol_daily_masked_id",
                "country_completion_vol_daily_masked_sg",
                "country_completion_vol_daily_masked_vn",
                "country_completion_vol_daily_masked_ph",
                "delivery_transaction_events_masked_id",
                "delivery_transaction_events_masked_ph",
                "fleet_performance_base_data_masked_mm",
                "fleet_performance_base_data_masked_ph",
                "fleet_performance_base_data_masked_vn",
                "first_terminal_status_events_masked_gl",
                "fm_shipper_exclusions_masked_gl",
                "hub_relation_schedules_enriched_masked_gl",
                "last_mile_push_off_report_masked_id",
                "lazada_intercepted_orders_masked_id",
                "liquidation_parcels_masked_my",
                "liquidation_parcels_masked_ph",
                "liquidation_parcels_masked_vn",
                "liquidation_parcels_masked_sg",
                "on_hold_events_masked_gl",
                "order_department_movements_masked_my",
                "order_department_movements_masked_sg",
                "proofs_enriched_masked_mm",
                "proofs_enriched_masked_th",
                "order_deliveries_masked_id",
                "order_deliveries_masked_ph",
                "order_dp_dropoffs_masked_ph",
                "order_force_successes_masked_id",
                "order_hub_movements_masked_id",
                "order_inbounds_masked_id",
                "order_movements_masked_ph",
                "order_movements_masked_my",
                "order_movements_masked_vn",
                "order_pickups_masked_ph",
                "order_rts_trigger_locations_masked_id",
                "order_rts_triggers_masked_id",
                "pickup_scan_events_masked_gl",
                "pickup_transaction_events_masked_id",
                "salesforce_account_enriched_masked_gl",
                "salesforce_activity_enriched_masked_gl",
                "salesforce_lead_enriched_masked_gl",
                "salesforce_opportunity_enriched_masked_gl",
                "salesforce_user_enriched_masked_gl",
                "sfmc_shippers_export_masked_gl",
                "shipment_orders_enriched_base_masked_gl",
                "shipments_enriched_masked_gl",
                "shipper_attributes_masked_gl",
                "sg_route_zone_success_rate_masked_sg",
                "sla_reports_base_masked_sg",
                "sla_reports_base_masked_vn",
                "ticket_closure_performance_report_masked_ph",
                "ticket_closure_performance_report_masked_my",
                "ticket_closure_performance_report_masked_sg",
                "ticket_closure_performance_report_masked_vn",
                "ge_validation_last_mile_push_off_masked_report",
                "ge_validation_reservations_masked_enriched",
                "ge_validation_order_rts_masked_triggers",
                "ge_validation_order_masked_deliveries",
                "ge_validation_order_masked_inbounds",
                "ge_validation_shipment_orders_enriched_masked_base",
                "ge_validation_order_masked_destinations",
                "ge_validation_service_breach_masked_report",
                "update_address_events_masked_gl",
                "update_address_verification_events_masked_gl",
                "update_status_events_masked_gl",
                "pets_tickets_enriched_base_masked_gl",
                "pod_validation_tasks_enriched_masked_ph",
                "pod_validation_tasks_enriched_masked_sg",
                "pod_validation_tasks_enriched_masked_vn",
                "salesforce_cs_case_enriched_masked_gl",
                "poh_metrics_masked_vn",
                "drivers_enriched_masked_gl",
                "shipper_milestones_masked_gl",
                "salesforce_ss_case_enriched_masked_gl",
                "xb_parcels_enriched_masked_gl",
                "gamification_id_pickup_events_masked_id",
                "gamification_id_planned_parcels_events_masked_id",
                "gamification_id_monthly_report_base_masked_id",
                "gamification_id_monthly_report_masked_id",
                "gamification_my_planned_parcels_events_masked_my",
                "gamification_my_monthly_report_masked_my",
                "gamification_my_daily_report_masked_my",
                "gamification_my_daily_report_with_free_parcels_masked_my",
                "gamification_my_daily_report_without_free_parcels_masked_my",
                "lnk_orders_shippers_masked_id",
                "lnk_orders_shippers_masked_mm",
                "lnk_orders_shippers_masked_my",
                "lnk_orders_shippers_masked_ph",
                "lnk_orders_shippers_masked_sg",
                "lnk_orders_shippers_masked_vn",
                "id_fm_payroll_monthly_masked_id",
                "shipper_ref_sender_recipient_masked_mm",
                "shipper_ref_sender_recipient_masked_my",
                "shipper_ref_sender_recipient_masked_ph",
                "shipper_ref_sender_recipient_masked_sg",
                "shipper_ref_sender_recipient_masked_vn",
                "shipper_ref_addresses_masked_mm",
                "shipper_ref_addresses_masked_my",
                "shipper_ref_addresses_masked_ph",
                "shipper_ref_addresses_masked_sg",
                "shipper_ref_addresses_masked_vn",
                "shipper_ref_platform_info_masked_mm",
                "shipper_ref_platform_info_masked_my",
                "shipper_ref_platform_info_masked_ph",
                "shipper_ref_platform_info_masked_sg",
                "shipper_ref_platform_info_masked_vn",
                "shipper_ref_items_masked_mm",
                "shipper_ref_items_masked_my",
                "shipper_ref_items_masked_ph",
                "shipper_ref_items_masked_sg",
                "shipper_ref_items_masked_vn",
                "shipper_ref_data_masked_mm",
                "shipper_ref_data_masked_my",
                "shipper_ref_data_masked_ph",
                "shipper_ref_data_masked_sg",
                "shipper_ref_data_masked_vn",
                "transit_time_report_masked_th",
                "workday_sort_productivity_masked_th",
                "action-after-ticket-closure-base-masked-th",
                "av_before_inbound_masked_th",
                "robochat_messages_enriched_masked_id",
                "fleet_performance_base_data_masked_th",
                "shipper_claims_report_masked_th",
                "transit_time_speed_report_masked_th",
                "rts_rates_kpi_masked_th",
                "update_cash_events_enriched_masked_th",
                "combined_shipment_compliance_kpi_fy24_masked_my",
                "combined_shipment_compliance_kpi_fy24_masked_id",
                "combined_shipment_compliance_kpi_fy24_masked_ph",
                "combined_shipment_compliance_kpi_fy24_masked_vn",
                "station_keyword_module_adoption_masked_id",
                "station_keyword_module_adoption_masked_ph",
                "station_keyword_module_accuracy_masked_id",
                "station_keyword_module_adoption_masked_ph",
                "mixpanel_operator_station_keyword_base_masked_gl",
                "last_mile_processing_time_masked_id",
                "last_mile_processing_time_masked_my",
                "last_mile_processing_time_masked_ph",
                "last_mile_processing_time_masked_sg",
                "last_mile_processing_time_masked_vn",
                "update_tags_events_masked_gl",
                "last_mile_daily_push_off_adherence_masked_sg",
                "orders_jobs_assignments_enriched_masked_id",
                "orders_jobs_assignments_enriched_masked_mm",
                "orders_jobs_assignments_enriched_masked_my",
                "orders_jobs_assignments_enriched_masked_ph",
                "orders_jobs_assignments_enriched_masked_sg",
                "orders_jobs_assignments_enriched_masked_th",
                "orders_jobs_assignments_enriched_masked_vn",
                "pod_validation_tasks_enriched_masked_th",
                "first_mile_routing_metrics_enriched_masked_id",
                "first_mile_routing_metrics_enriched_masked_my",
                "first_mile_routing_metrics_enriched_masked_ph",
                "first_mile_routing_metrics_enriched_masked_sg",
                "first_mile_routing_metrics_enriched_masked_vn",
                "reservation_change_of_route_events_masked_gl",
                "reservation_remove_from_route_events_masked_gl",
                "order_profits_sg_masked_sg",
                "id_first_mile_auto_routing_addresses_masked_id"
            ],
            "large": [
                "action_after_ticket_closure_base_masked_my",
                "action_after_ticket_closure_base_masked_ph",
                "action_after_ticket_closure_base_masked_vn",
                "dws_scans_events_masked_id",
                "latest_shipment_report_masked_id",
                "lazada_orders_masked_id",
                "mitra_orders_masked_id",
                "update_consignee_email_events_masked_id",
                "prioritised_lazada_orders_masked_id",
                "dws_variance_report_masked_id",
                "consignee_email_domain_report_masked_id",
                "known_fraud_shipper_report_masked_id",
                "names_per_consignee_email_report_masked_id",
                "lazada_fraud_flags_masked_id",
                "lazada_orders_enriched_masked_id",
                "mitra_trend_report_masked_id",
                "suspicious_lazada_orders_report_masked_id",
                "high_value_lazmall_orders_report_masked_id",
                "archived_scan_result_enriched_masked_id",
                "archived_scan_result_enriched_masked_sg",
                "sort_compliance_masked_ph",
                "sort_compliance_masked_vn",
                "sort_compliance_masked_sg",
                "proofs_enriched_masked_my",
                "proofs_enriched_masked_ph",
                "proofs_enriched_masked_sg",
                "proofs_enriched_masked_vn",
                "av_before_inbound_masked_id",
                "av_before_inbound_masked_my",
                "av_before_inbound_masked_ph",
                "av_before_inbound_masked_sg",
                "av_before_inbound_masked_vn",
                "fleet_performance_base_data_masked_id",
                "fleet_performance_base_data_masked_my",
                "fleet_performance_base_data_masked_sg",
                "route_classification_masked_id",
                "last_mile_daily_push_off_adherence_masked_id",
                "last_mile_daily_push_off_adherence_masked_my",
                "last_mile_daily_push_off_adherence_masked_ph",
                "last_mile_daily_push_off_adherence_masked_vn",
                "selected_granular_events_masked_gl",
                "add_to_shipment_events_masked_gl",
                "ticket_resolved_events_masked_gl",
                "order_department_movements_masked_id",
                "order_department_movements_masked_ph",
                "order_department_movements_masked_vn",
                "order_destinations_masked_ph",
                "order_destinations_masked_my",
                "order_movements_base_masked_gl",
                "order_profits_ph_masked_ph",
                "seller_order_features_masked_id",
                "shipper_claims_report_masked_ph",
                "shipper_claims_report_masked_sg",
                "shipper_claims_report_masked_vn",
                "ticket_creation_performance_masked_ph",
                "ge_validation_cisp_report_masked_base",
                "ge_validation_cisp_completion_masked_report",
                "ge_validation_cisp_terminal_status_masked_report",
                "ge_validation_shipment_orders_masked_enriched",
                "ge_validation_reserve_tracking_ids_masked_enriched",
                "ge_validation_scan_result_masked_enriched",
                "ge_validation_transit_time_masked_report",
                "ge_validation_transit_time_speed_masked_report",
                "ge_validation_sla_reports_masked_base",
                "cisp_prior_report_masked_my",
                "cisp_prior_report_masked_vn",
                "cisp_prior_report_masked_ph",
                "cisp_prior_report_masked_mm",
                "cost_card_events_my_monthly_snapshot_masked_my",
                "cost_card_events_ph_monthly_snapshot_masked_ph",
                "cost_card_monthly_snapshot_masked_ph",
                "cost_card_monthly_snapshot_masked_vn",
                "cost_card_monthly_snapshot_masked_my",
                "address_verification_accuracy_report_masked_my",
                "address_verification_accuracy_report_masked_ph",
                "auto_address_verification_accuracy_report_masked_ph",
                "auto_address_verification_accuracy_report_masked_sg",
                "auto_address_verification_accuracy_report_masked_vn",
                "zendesk_tickets_enriched_masked_gl",
                "pets_tickets_enriched_masked_id",
                "transit_time_speed_report_masked_ph",
                "transit_time_speed_report_masked_vn",
                "delivery_failure_events_masked_gl",
                "order_pickups_masked_my",
                "order_pickups_masked_id",
                "n0_pickup_sla_kpi_masked_my",
                "n0_pickup_sla_kpi_masked_ph",
                "n0_pickup_sla_kpi_masked_sg",
                "n0_pickup_sla_kpi_masked_vn",
                "n0_pickup_sla_kpi_base_masked_my",
                "n0_pickup_sla_kpi_base_masked_ph",
                "n0_pickup_sla_kpi_base_masked_sg",
                "n0_pickup_sla_kpi_base_masked_vn",
                "direct_orders_enriched_masked_gl",
                "force_success_events_masked_gl",
                "third_party_transfer_masked_events",
                "order_destinations_masked_vn",
                "order_pickups_masked_vn",
                "order_third_party_transfers_masked_id",
                "middle_mile_driver_app_adoption_kpi_masked_id",
                "middle_mile_driver_app_adoption_kpi_masked_my",
                "middle_mile_driver_app_adoption_kpi_masked_ph",
                "middle_mile_driver_app_adoption_kpi_masked_vn",
                "rts_rates_kpi_masked_id",
                "rts_rates_kpi_masked_my",
                "rts_rates_kpi_masked_ph",
                "rts_rates_kpi_masked_sg",
                "rts_rates_kpi_masked_vn",
                "warehouse_speed_report_masked_my",
                "warehouse_speed_report_masked_ph",
                "warehouse_speed_report_masked_sg",
                "warehouse_speed_report_masked_vn",
                "workday_sort_productivity_masked_my",
                "workday_sort_productivity_masked_ph",
                "workday_sort_productivity_masked_sg",
                "workday_sort_productivity_masked_vn",
                "milkrun_enriched_masked_id",
                "order_third_party_transfers_masked_mm",
                "pod_validation_tasks_enriched_masked_mm",
                "middle_mile_trip_relationships_masked_gl",
                "cost_card_masked_sg",
                "order_events_pickup_success_masked_gl",
                "order_events_update_contact_information_masked_gl",
                "id_lm_payroll_monthly_masked_id",
                "id_lm_payroll_monthly_adjusted_masked_id",
                "id_lm_payroll_monthly_trial_masked_id",
                "gamification_my_planned_parcels_events_masked_my",
                "gamification_my_base_data_masked_my",
                "sort_ats_compliance_base_masked_my",
                "sort_ats_compliance_base_masked_ph",
                "sort_ats_compliance_base_masked_sg",
                "sort_ats_compliance_base_masked_vn",
                "sort_ats_compliance_masked_ph",
                "sort_ats_compliance_masked_sg",
                "sort_ats_compliance_masked_vn",
                "ticket_creation_last_scans_masked_sg",
                "reservations_enriched_masked_mm",
                "scan_result_enriched_masked_sg",
                "order_milestones_masked_mm",
                "order_milestones_masked_sg",
                "shipper_completion_vol_daily_masked_gl",
                "transit_time_report_masked_sg",
                "transit_time_report_masked_vn",
                "archived_scan_result_enriched_masked_ph",
                "archived_scan_result_enriched_masked_my",
                "archived_scan_result_enriched_masked_vn",
                "id_fm_payroll_daily_masked_id",
                "shipper_completion_vol_daily_full_masked_gl",
                "shipper_completion_vol_monthly_masked_gl",
                "shipper_ref_sender_recipient_masked_id",
                "shipper_ref_addresses_masked_id",
                "shipper_ref_data_masked_id",
                "shipper_ref_platform_info_masked_id",
                "shipper_ref_items_masked_id",
                "scan_result_enriched_masked_th",
                "direct_boxes_enriched_masked_th",
                "order_milestones_masked_th",
                "ticket_creation_last_scans_masked_th",
                "archived_sort_mistakes_enriched_masked_th",
                "driver_start_route_events_masked_gl",
                "matched_consignee_contact_email_masked_vn",
                "matched_consignee_contact_email_masked_id",
                "matched_consignee_contact_email_cm_masked_vn",
                "matched_consignee_contact_email_cm_masked_id",
                "order_hub_history_base_masked_id",
                "order_hub_history_base_masked_my",
                "first_mile_volume_orders_masked_id",
                "first_mile_volume_orders_masked_my",
                "first_mile_volume_orders_masked_ph",
                "first_mile_volume_orders_masked_sg",
                "first_mile_volume_orders_masked_th",
                "first_mile_volume_orders_masked_vn",
                "sla_extensions_masked_gl",
                "original_consignee_information_masked_id"
            ],
            "large_l2": [
                "action_after_ticket_closure_report_masked_id",
                "action_after_ticket_closure_report_masked_ph",
                "order_pickups_id_masked",
                "order_hub_history_intermediate_masked_id",
                "order_hub_history_latest_trips_masked_id",
                "order_hub_history_masked_id",
                "order_hub_history_intermediate_masked_my",
                "order_hub_history_latest_trips_masked_my",
                "order_hub_history_masked_my",
                "sns_prod_gl_webhook_logs.snapshot",
                "sns_prod_gl_chat_ticket_queue_entries.check_mismatch",
                "sns_prod_gl_chat_ticket_queue_entries.patch_mismatch",
                "cisp_prior_report_masked_id",
                "laz_sla_breach_report_masked_id",
                "address_verification_accuracy_report_masked_id",
                "archived_sort_mistakes_enriched_masked_ph",
                "archived_sort_mistakes_enriched_masked_my",
                "archived_sort_mistakes_enriched_masked_sg",
                "archived_sort_mistakes_enriched_masked_vn",
                "direct_order_items_enriched_masked_gl",
                "av_before_inbound_order_events_verified_masked_gl",
                "av_before_inbound_order_events_unverified_masked_gl",
                "proofs_enriched_masked_id",
                "active_orders_masked_gl",
                "sort_ats_compliance_base_masked_id",
                "sort_ats_compliance_masked_my",
                "sort_ats_compliance_masked_id",
                "ticket_closure_performance_report_masked_id",
                "ge_validation_order_masked_milestones",
                "ge_validation_order_masked_movements",
                "ge_validation_shipper_claims_masked_report",
                "ge_validation_orders_masked_enriched",
                "implanted_manifest_scan_events_masked_gl",
                "ticket_creation_last_scans_masked_vn",
                "ticket_creation_last_scans_masked_my",
                "ticket_creation_last_scans_masked_ph",
                "parcel_measurement_scan_enriched_masked_gl",
                "order_milestones_masked_my",
                "order_milestones_masked_ph",
                "order_milestones_masked_vn",
                "direct_boxes_enriched_masked_my",
                "direct_boxes_enriched_masked_vn",
                "direct_boxes_enriched_masked_ph",
                "valid_scan_events_masked_gl",
                "scan_result_enriched_masked_ph",
                "scan_result_enriched_masked_my",
                "scan_result_enriched_masked_vn",
                "sg_sla_enriched_masked_sg",
                "sla_reports_base_masked_my",
                "sla_reports_base_masked_ph",
                "pricing_pricing_details_masked_gl",
                "waypoints_enriched_masked_mm",
                "workday_sort_productivity_masked_id",
                "archived_scan_result_enriched_masked_th",
                "n0_pickup_sla_kpi_masked_id",
                "lazada_orders_base_id",
                "cost_card_events_id_masked_id",
                "cost_card_events_my_masked_my",
                "cost_card_events_ph_masked_ph",
                "cost_card_events_vn_masked_vn"
            ],
            "large_l3": [
                "auto_address_verification_accuracy_report_masked_id",
                "driver_pickup_scan_events_masked_gl",
                "id_lm_payroll_daily_masked_id",
                "id_lm_payroll_daily_adjusted_masked_id",
                "id_lm_payroll_daily_trial_masked_id",
                "invoice_dispute_case_enriched_masked_gl",
                "route_prod_gl_route_group_references.snapshot",
                "order_movements_masked_id",
                "archived_sort_mistakes_enriched_masked_id",
                "reservations_enriched_masked_ph",
                "reservations_enriched_masked_sg",
                "reservations_enriched_masked_my",
                "gamification_my_reservations_enriched_masked_my",
                "partnership_webhook_orders_enriched_masked_gl",
                "shipment_orders_enriched_masked_gl",
                "sort_mistakes_enriched_masked_gl",
                "transit_time_report_masked_ph",
                "transit_time_speed_report_masked_id",
                "waypoints_enriched_masked_my",
                "waypoints_enriched_masked_vn",
                "waypoints_enriched_masked_id",
                "waypoints_enriched_masked_ph",
                "warehouse_scan_events_masked_id",
                "movement_trips_enriched_masked_gl",
                "shipper_claims_report_masked_id",
                "sla_reports_base_masked_id",
                "lm_claw_master_masked_my",
                "reservations_enriched_masked_th",
                "priced_orders_revenue_masked_ph",
                "priced_orders_revenue_masked_vn",
                "priced_orders_revenue_masked_my",
                "warehouse_speed_report_masked_id",
                "order_destinations_masked_id",
                "direct_boxes_enriched_masked_id",
                "liquidation_parcels_masked_id",
                "hub_inbound_events_masked_gl",
                "ticket_creation_performance_masked_my",
                "sort_compliance_masked_id",
                "sort_compliance_masked_my",
                "matched_shipper_contact_email_masked_vn"
            ],
            "large_l4": [
                "shipper_pickup_assignees_masked_gl",
                "order_milestones_masked_id",
                "reserve_tracking_ids_enriched_masked_gl",
                "pod_validation_tasks_enriched_masked_id",
                "pod_validation_tasks_enriched_masked_my",
                "parcel_sweeper_events_masked_gl",
                "shipper_claims_report_masked_my",
                "ticket_creation_last_scans_masked_id",
                "gamification_id_daily_report_masked_id",
                "reservations_enriched_masked_vn",
                "action_after_ticket_closure_base_masked_id"
            ],
            "large_l5": [
                "auto_address_verification_accuracy_report_masked_my",
                "reservations_enriched_masked_id"
            ],
            "exclusive": [
                "transit_time_report_masked_my",
                "transit_time_report_masked_id",
                "transit_time_speed_report_masked_my",
                "dim_weight_scans_base_masked_gl",
                "mmcc_webhook_masked_gl",
                "partnership_webhook_inter_orders_enriched_masked_gl",
                "partnership_webhook_masked_gl"
            ],
            "exclusive_60": [
                "orders_enriched_masked_sg",
                "orders_enriched_masked_mm",
                "orders_enriched_masked_my",
                "orders_enriched_masked_ph",
                "orders_enriched_masked_vn",
                "orders_enriched_masked_th",
                "cost_card_masked_ph",
                "cost_card_masked_my",
                "cost_card_masked_vn",
                "calculated_revenue_masked_my",
                "calculated_revenue_masked_ph",
                "calculated_revenue_masked_vn",
                "scan_result_enriched_masked_id",
                "n0_pickup_sla_kpi_base_masked_id",
                "consignee_email_order_features_masked_id",
                "dim_weight_scans_masked_gl"
            ],
            "exclusive_200": [
                "orders_enriched_masked_id",
                "xb_core_components_masked_gl",
                "partnership_webhook_orders_masked_gl"
            ],
            "exclusive_200_v1": [
                "action_after_ticket_closure_base_masked_id"
            ]
        }
    }
}