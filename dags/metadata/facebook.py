FACEBOOK_CONFIG = {
    "ad": {
        "fields": [
            "account_name",
            "account_id",
            "campaign_name",
            "campaign_id",
            "adset_name",
            "adset_id",
            "ad_name",
            "ad_id",
            "objective",
            "buying_type",
            "optimization_goal",
            "impressions",
            "clicks",
            "inline_post_engagement",
            "account_currency",
            "spend",
            "reach",
            "frequency",
            "actions",
            "reach",
            "clicks",
            "cpm",
            "cpc",
            "ctr",
            "video_p25_watched_actions",
            "video_p50_watched_actions",
            "video_p75_watched_actions",
            "video_p100_watched_actions",
            "video_play_actions",
            "cost_per_thruplay",
            "estimated_ad_recallers",
        ],
        "schema": {
            "int": [
                "video_p25_watched_actions",
                "video_p50_watched_actions",
                "video_p75_watched_actions",
                "video_p100_watched_actions",
                "video_play_actions",
                "impressions",
                "clicks",
                "reach",
            ],
            "float": ["spend", "frequency", "cpm", "cpc", "ctr", "cost_per_thruplay"],
            "datetime": ["date_start", "date_stop"],
            "nest_columns": [
                "video_p25_watched_actions",
                "video_p50_watched_actions",
                "video_p75_watched_actions",
                "video_p100_watched_actions",
                "video_play_actions",
                "cost_per_thruplay",
            ],
        },
    },
}
PARAMETERS_TEMPLATE = {"time_range": {}, "breakdowns": ["publisher_platform"], "time_increment": "1", "level": "ad"}
ACCOUNT_ID_MAPPING = {
    "****************": "sg",
    "****************": "id",
    "****************": "th",
    "***************": "vn",
    "****************": "my",
    "***************": "ph",
    "****************": "vn",
    "***************": "ph",
    "****************": "vn",
    "***************": "sg",
    "***************": "ph",
    "***************": "id",
    "****************": "id"
}
