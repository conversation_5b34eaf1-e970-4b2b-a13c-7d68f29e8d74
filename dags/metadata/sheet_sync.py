OBS_GSHEETS_CONFIG = {
    "sheets": [
        {
            "obs_url": "obs://nv-data-prod-data-warehouse/action_after_ticket_closure_base/measurement_datetime=latest/system_id=my/created_month=2023-11/",
            "input_data_format": "parquet",
            "operation_type": "overwrite",
            "sheet_name": "Sheet1",
            "sheet_url": "https://docs.google.com/spreadsheets/d/14rNktL30WzRMtzf0AFLgMsNggQ1PQlYVJY9Yv95QhHE/edit#gid=0",
        },
        {
            "obs_url": "obs://nv-data-prod-data-warehouse/action_after_ticket_closure_base/measurement_datetime=latest/system_id=id/created_month=2019-07/part-00099-a7bcacb2-7b93-4236-abae-463f8a00b2c9.c000.snappy.parquet",
            "input_data_format": "parquet",
            "operation_type": "overwrite",
            "sheet_name": "Sheet2",
            "sheet_url": "https://docs.google.com/spreadsheets/d/14rNktL30WzRMtzf0AFLgMsNggQ1PQlYVJY9Yv95QhHE/edit#gid=0",
            "columns": ["pets_id", "order_id", "resolution_datetime", "non_missing_flag", "recovery_scan_hub"],
        },
        {
            "obs_url": "obs://nv-data-prod-data-warehouse/lm_claw_master/measurement_datetime=latest/system_id=my",
            "input_data_format": "parquet",
            "operation_type": "overwrite",
            "sheet_name": "LM_CLAW_MASTER",
            "sheet_url": "https://docs.google.com/spreadsheets/d/1yEdKc31wHrS_pyykIgUgxFwrtRNjZF31px7NzFm8apQ/edit#gid=0",
        },
    ],
    "7am_sgt": [
        {
            "obs_url": "obs://nv-data-prod-data-warehouse/id_first_mile_auto_routing_addresses/measurement_datetime=latest/system_id=id/",
            "input_data_format": "parquet",
            "operation_type": "overwrite",
            "sheet_name": "id_fm_addresses",
            "sheet_url": "https://docs.google.com/spreadsheets/d/1IcEgN7SNjN0iV8C2I8TWxtnFn_ZpC6Yuc_1Q3fQWbt4/edit#gid=0",
        },
        {
            "obs_url": "obs://nv-data-prod-data-warehouse/id_first_mile_auto_routing_addresses_weekly/measurement_datetime=latest/system_id=id/",
            "input_data_format": "parquet",
            "operation_type": "overwrite",
            "sheet_name": "id_fm_addresses_weekly",
            "sheet_url": "https://docs.google.com/spreadsheets/d/1IcEgN7SNjN0iV8C2I8TWxtnFn_ZpC6Yuc_1Q3fQWbt4/edit#gid=0",
        },
    ]
}