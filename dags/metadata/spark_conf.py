SPARK_CONF = {
    "dev": {
        "cdc": {
            "small": {
                "spark.executor.cores": "4",
                "spark.executor.instances": "1",
                "spark.executor.memory": "5400m",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.sql.adaptive.enabled": "true",
            },
            "medium": {
                "spark.driver.cores": "2",
                "spark.driver.memory": "5400m",
                "spark.executor.cores": "8",
                "spark.executor.instances": "2",
                "spark.executor.memory": "10800m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.shuffle.partitions": "240",
            },
            "medium_legacy_datetimeRebaseModeInWrite": {
                "spark.driver.cores": "2",
                "spark.driver.memory": "5400m",
                "spark.executor.cores": "8",
                "spark.executor.instances": "2",
                "spark.executor.memory": "10800m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "LEGACY",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.shuffle.partitions": "240",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large": {
                "spark.driver.cores": "2",
                "spark.driver.memory": "5400m",
                "spark.executor.cores": "10",
                "spark.executor.instances": "3",
                "spark.executor.memory": "10800m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "960",
            },
            "large_l2": {
                "spark.driver.cores": "4",
                "spark.driver.memory": "11200m",
                "spark.executor.cores": "16",
                "spark.executor.instances": "4",
                "spark.executor.memory": "40400m",
                "spark.kubernetes.driver.request.cores": "1800m",
                "spark.kubernetes.executor.request.cores": "7200m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l3": {
                "spark.driver.cores": "4",
                "spark.driver.memory": "11200m",
                "spark.executor.cores": "10",
                "spark.executor.instances": "5",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.driver.request.cores": "1800m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
        },
        "cdc_hive": {
            "small": {"spark.executor.instances": "1", "spark.executor.memory": "1g", "spark.driver.memory": "1g"},
            "large": {"spark.executor.instances": "2", "spark.executor.memory": "5g", "spark.driver.memory": "5g"},
        },
        "compaction": {
            "medium": {
                "spark.executor.instances": "1",
                "spark.executor.memory": "5400m",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "2",
                "spark.driver.memory": "5400m",
                "spark.driver.cores": "1",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
            },
            "large": {
                "spark.executor.instances": "2",
                "spark.executor.memory": "5500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "2",
                "spark.driver.memory": "5500m",
                "spark.driver.cores": "1",
            },
            "larger": {
                "spark.executor.instances": "10",
                "spark.executor.memory": "17000m",
                "spark.sql.adaptive.enabled": "true",
                "spark.executor.cores": "4",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
            },
        },
        "dwh": {
            "small": {
                "spark.executor.instances": "20",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "120",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "medium": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l2": {
                "spark.executor.instances": "34",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "480",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l3": {
                "spark.executor.instances": "55",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "3",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "800",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l4": {
                "spark.executor.instances": "60",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "3",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
        },
        "gdrive": {
            "small": {
                "spark.driver.memory": "4000m",
                "spark.driver.cores": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
            "large": {
                "spark.driver.memory": "17000m",
                "spark.driver.cores": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "2g",
                "spark.sql.execution.arrow.enabled": "true",
                "spark.sql.execution.arrow.pyspark.enabled": "true",
            },
        },
    },
    "prod": {
        "cdc": {
            "small": {
                "spark.driver.cores": "1",
                "spark.driver.memory": "2800m",
                "spark.executor.cores": "4",
                "spark.executor.instances": "1",
                "spark.executor.memory": "5600m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "medium": {
                "spark.driver.cores": "2",
                "spark.driver.memory": "5600m",
                "spark.executor.cores": "8",
                "spark.executor.instances": "5",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.shuffle.partitions": "240",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "medium_legacy_datetimeRebaseModeInWrite": {
                "spark.driver.cores": "2",
                "spark.driver.memory": "5600m",
                "spark.executor.cores": "8",
                "spark.executor.instances": "5",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "LEGACY",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.shuffle.partitions": "240",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large": {
                "spark.driver.cores": "4",
                "spark.driver.memory": "5600m",
                "spark.executor.cores": "10",
                "spark.executor.instances": "28",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.shuffle.partitions": "960",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l2": {
                "spark.driver.cores": "4",
                "spark.driver.memory": "5600m",
                "spark.executor.cores": "10",
                "spark.executor.instances": "56",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l3": {
                "spark.driver.cores": "4",
                "spark.driver.memory": "11200m",
                "spark.executor.cores": "10",
                "spark.executor.instances": "56",
                "spark.executor.memory": "48g",
                "spark.kubernetes.driver.request.cores": "1800m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "recovery": {
                "spark.driver.memory": "1g",
                "spark.executor.instances": "300",
                "spark.executor.memory": "48g",
                "spark.executor.memoryOverhead": "1g",
                "spark.kubernetes.driver.request.cores": "3",
                "spark.kubernetes.executor.request.cores": "3",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "recovery_2": {
                "spark.driver.memory": "1g",
                "spark.executor.instances": "400",
                "spark.executor.memory": "2g",
                "spark.executor.memoryOverhead": "1g",
                "spark.kubernetes.driver.request.cores": "2",
                "spark.kubernetes.executor.request.cores": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "recovery_events_3": {
                "spark.driver.memory": "4g",
                "spark.executor.instances": "60",
                "spark.executor.memory": "50g",
                "spark.executor.memoryOverhead": "1g",
                "spark.kubernetes.driver.request.cores": "4",
                "spark.kubernetes.executor.request.cores": "6",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "recovery_orders": {
                "spark.driver.memory": "40400m",
                "spark.executor.instances": "60",
                "spark.executor.memory": "80200m",
                "spark.driver.cores": "6",
                "spark.kubernetes.driver.request.cores": "4800m",
                "spark.kubernetes.executor.request.cores": "5000m",
                "spark.executor.cores": "6",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.files.maxPartitionBytes": "2048m",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "spark.sql.ansi.enabled": "false",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
                "spark.task.maxFailures": "2",
                "spark.rdd.compress": "true",
                "spark.checkpoint.compress": "true",
                "spark.storage.replication": "2",
                "spark.kubernetes.executor.request.ephemeral-storage": "10Gi",
                "spark.kubernetes.executor.limit.ephemeral-storage": "15Gi",
                "spark.memory.storageFraction": "0.3",
                "spark.sql.shuffle.partitions": "2048",
                "spark.databricks.delta.merge.repartitionBeforeWrite.enabled": "true",
                "spark.databricks.delta.merge.optimizeWrite.enabled": "true",
                "spark.databricks.delta.optimize.maxFileSize": "536870912", # 512MB
                "spark.databricks.delta.optimize.minFileSize": "67108864",  # 64MB
                "spark.databricks.delta.autoCompact.enabled": "true",
                "spark.databricks.delta.properties.defaults.enableChangeDataFeed": "true",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "spark.sql.broadcastTimeout": "1200",
                "spark.sql.autoBroadcastJoinThreshold": "104857600",  # 100MB
                "spark.sql.adaptive.skewJoin.enabled": "true",
                "spark.sql.adaptive.coalescePartitions.enabled": "true"
            }
        },
        "cdc_hive": {
            "small": {"spark.executor.instances": "2", "spark.executor.memory": "4g", "spark.driver.memory": "2g"},
            "medium": {"spark.executor.instances": "3", "spark.executor.memory": "6g", "spark.driver.memory": "4g"},
            "large": {"spark.executor.instances": "5", "spark.executor.memory": "8g", "spark.driver.memory": "6g"},
        },
        "compaction": {
            "medium": {
                "spark.executor.instances": "4",
                "spark.executor.memory": "22000m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "8",
                "spark.driver.memory": "5500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "1",
                "spark.sql.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large": {
                "spark.executor.instances": "36",
                "spark.executor.memory": "22000m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "8",
                "spark.driver.memory": "11000m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "larger": {
                "spark.executor.instances": "46",
                "spark.executor.memory": "44000m",
                "spark.executor.cores": "6",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.memory": "22000m",
                "spark.driver.cores": "3",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
        },
        "dwh": {
            "small": {
                "spark.dynamicAllocation.maxExecutors": "5",
                "spark.executor.cores": "2",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "32",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "medium": {
                "spark.dynamicAllocation.maxExecutors": "40",
                "spark.executor.cores": "2",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "160",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large": {
                "spark.dynamicAllocation.maxExecutors": "40",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l2": {
                "spark.dynamicAllocation.maxExecutors": "34",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "480",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l3": {
                "spark.dynamicAllocation.maxExecutors": "48",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "800",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l4": {
                "spark.dynamicAllocation.maxExecutors": "60",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "large_l5": {
                "spark.dynamicAllocation.maxExecutors": "40",
                "spark.executor.cores": "6",
                "spark.executor.memory": "21960m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "exclusive": {
                "spark.dynamicAllocation.maxExecutors": "40",
                "spark.executor.cores": "6",
                "spark.executor.memory": "22960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "exclusive_60": {
                "spark.dynamicAllocation.maxExecutors": "100",
                "spark.executor.cores": "6",
                "spark.executor.memory": "8960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.kubernetes.executor.request.cores": "2600m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "exclusive_200": {
                "spark.dynamicAllocation.maxExecutors": "200",
                "spark.executor.cores": "6",
                "spark.memory.storageFraction": "0.3",
                "spark.executor.memory": "8960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.kubernetes.executor.request.cores": "2200m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
            "exclusive_200_v1": {
                "spark.dynamicAllocation.maxExecutors": "200",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "2600m",
                "spark.executor.memory": "8300m",
                "spark.driver.memory": "6500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "2048",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.sql.broadcastTimeout": "600",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.spill.compress": "true",
                "spark.sql.shuffle.compress": "true",
                "spark.sql.sources.partitionOverwriteMode": "dynamic",
            },
        },
        "gdrive": {
            "small": {"spark.driver.memory": "4000m", "spark.driver.cores": "2", "spark.sql.adaptive.enabled": "true"},
            "large": {
                "spark.driver.memory": "34000m",
                "spark.driver.cores": "2",
                "spark.executor.cores": "3",
                "spark.executor.instances": "5",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.execution.arrow.enabled": "true",
                "spark.sql.execution.arrow.pyspark.enabled": "true",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
            },
        },
    },
}

SPARK_DRIVER_CONF = {
    "spark.kubernetes.driver.annotation.run-id": "{{ run_id }}",
    "spark.kubernetes.driver.annotation.task-instance-key": "{{ task_instance_key_str }}",
    "spark.kubernetes.driver.label.dag_id": "{{ ti.dag_id }}",
    "spark.kubernetes.driver.label.ts": "{{ ts_nodash }}"
}