from metadata.constants import BI_SENSITIVE_REPORTS_BASE_URI, MASKED_DATALAKE_BASE_URI, MASKED_DATA_WAREHOUSE_BASE_URI


class DataWarehouse:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = BI_SENSITIVE_REPORTS_BASE_URI.format(env)
        self._MASKED_URI_BASE = MASKED_DATA_WAREHOUSE_BASE_URI.format(env)
        self.ACTIVE_ORDERS = f"{self._MASKED_URI_BASE}/active_orders"
        self.CALENDAR = f"{self._MASKED_URI_BASE}/calendar"
        self.CALENDAR_RECOVERY = f"{self._MASKED_URI_BASE}/calendar_recovery"
        self.CALENDAR_SHIPPER = f"{self._MASKED_URI_BASE}/calendar_shipper"
        self.CALENDAR_SORT = f"{self._MASKED_URI_BASE}/calendar_sort"
        self.CALENDAR_SPEED = f"{self._MASKED_URI_BASE}/calendar_speed"
        self.DRIVER_CIF_MIGRATION_REPORT = f"{self._MASKED_URI_BASE}/driver_cif_migration_report"
        self.ETL_TASKS_MAPPING = f"{self._MASKED_URI_BASE}/etl_tasks_mapping"
        self.ETL_TASKS_LOG_FULL = f"{self._MASKED_URI_BASE}/etl_tasks_log_full"
        self.FS_SEGMENTATION_DAILY = f"{self._MASKED_URI_BASE}/fs_segmentation_daily"
        self.HUB_DRIVING_DISTANCES = f"{self._MASKED_URI_BASE}/hub_driving_distances"
        self.MMCC_WEBHOOK_ORDERS = f"{self._MASKED_URI_BASE}/mmcc_webhook_orders"
        self.MMCC_WEBHOOK_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/mmcc_webhook_orders_enriched"
        self.NINJA_MART_PL_ACTUALS = f"{self._BI_SENSITIVE_BASE}/gsheets/ninja_mart_pl/actuals"
        self.NINJA_MART_PL_TARGETS = f"{self._BI_SENSITIVE_BASE}/gsheets/ninja_mart_pl/targets"
        self.PARTNERSHIP_PREFIX = f"{self._MASKED_URI_BASE}/partnership_prefix"
        self.PARTNERSHIP_WEBHOOK = f"{self._MASKED_URI_BASE}/partnership_webhook"
        self.PARTNERSHIP_WEBHOOK_ORDERS = f"{self._MASKED_URI_BASE}/partnership_webhook_orders"
        self.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/partnership_webhook_orders_enriched"
        self.PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/partnership_webhook_inter_orders_enriched"
        self.SALESFORCE_CONTENT_VERSION_ENRICHED_MASKED = f"{self._MASKED_URI_BASE}/salesforce_content_version_enriched_masked"
        self.SELLER_OPERATIONAL_METRICS = f"{self._BI_SENSITIVE_BASE}/seller_operational_metrics"
        self.SS_SEGMENTATION_DAILY = f"{self._MASKED_URI_BASE}/ss_segmentation_daily"
        self.XB_EVENTS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_events_enriched"
        self.XB_PARCELS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_parcels_enriched"


class DriverLocation:
    def __init__(self, env="prod"):
        self.DRIVER_LOCATION = f"{MASKED_DATALAKE_BASE_URI.format(env)}/driver_location"


class GSheets:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = f"{BI_SENSITIVE_REPORTS_BASE_URI.format(env)}/gsheets"
        self._MASKED_URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/gsheets"
        self.CUSTOM_MAPPING_MY = f"{self._MASKED_URI_BASE}/custom_mapping/my_master"
        self.CUSTOM_MAPPING_VN = f"{self._MASKED_URI_BASE}/custom_mapping/vn_master"
        self.SALES_TARGETS_BU = f"{self._MASKED_URI_BASE}/sales_targets_bu/sheet1"
        self.NINJA_MART_SALES_TARGETS = f"{self._MASKED_URI_BASE}/ninja_mart_sales_target/sales_target_monthly"
        self.NINJA_MART_SKU_MARGINS = f"{self._MASKED_URI_BASE}/ninja_mart_sku_margins/consolidated"
        self.COST_CARD_WEIGHT_BINS = f"{self._BI_SENSITIVE_BASE}/cost_data/weight_bins"
        self.COST_CARD_HUB_MAPPING = f"{self._BI_SENSITIVE_BASE}/cost_data/hub_mapping"
        self.COST_CARD_BILLING_ZONE_MAPPING_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_billing_zone_mapping"
        self.COST_CARD_L2_MAP_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_l2_map"
        self.COST_CARD_AIRSEA_COST_CONFIG_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_airhaul_seahaul_cost_config"
        self.COST_CARD_COST_CONFIG_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_cost_config"
        self.COST_CARD_PRIMARY_TRIP_COST_CONFIG_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_pri_cost_config"
        self.COST_CARD_DRIVER_COST_CONFIG_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_driver_cost_config"
        self.COST_CARD_VEHICLE_COST_CONFIG_ID = f"{self._BI_SENSITIVE_BASE}/id_cost_data/id_vehicle_cost_config"
        self.COST_CARD_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_pricing_config"
        self.COST_CARD_SHEIN_XB_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_shein_xb_pricing_config"
        self.COST_CARD_TIKTOK_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_pricing_config"
        self.COST_CARD_TIKTOK_PRICING_TIER_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_pricing_tier_config"
        self.COST_CARD_TIKTOK_XB_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_xb_pricing_config"
        self.COST_CARD_TIKTOK_XB_TP_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_xb_tp_pricing_config"
        self.COST_CARD_TIKTOK_XB_TP_RATE_REFERENCE_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_tiktok_xb_tp_rate_reference"
        self.COST_CARD_XB_TP_PRICING_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_xb_tp_pricing_config"
        self.COST_CARD_XB_TP_SHIPPERS_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_xb_tp_shippers"
        self.COST_CARD_LAZADA_DISCOUNT_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_lazada_discount_config"
        self.COST_CARD_COST_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_cost_config"
        self.COST_CARD_MM_CONFIG_SEA_LANDHAUL_EXCLUSION = f"{self._BI_SENSITIVE_BASE}/ph_cost_card_mm_config/ph_sea_landhaul_exclusion"
        self.COST_CARD_MM_CONFIG_SECONDARY_PH = f"{self._BI_SENSITIVE_BASE}/ph_cost_card_mm_config/ph_cost_config_sec"
        self.COST_CARD_MM_CONFIG_REF_ID_COST_PH = f"{self._BI_SENSITIVE_BASE}/ph_cost_card_mm_config/ref_id_cost"
        self.COST_CARD_MM_CONFIG_REF_ID_TRIP_ID_PH = f"{self._BI_SENSITIVE_BASE}/ph_cost_card_mm_config/ref_id_trip_id"
        self.COST_CARD_AIRSEA_COST_CONFIG_PH = f"{self._BI_SENSITIVE_BASE}/cost_data/ph_airhaul_seahaul_cost_config"
        self.COST_CARD_COST_CONFIG_MY = f"{self._BI_SENSITIVE_BASE}/cost_data/my_cost_config"
        self.COST_CARD_DRIVER_COST_CONFIG_MY = f"{self._BI_SENSITIVE_BASE}/cost_data/my_driver_cost_config"
        self.COST_CARD_VEHICLE_COST_CONFIG_MY = f"{self._BI_SENSITIVE_BASE}/cost_data/my_vehicle_cost_config"
        self.COST_CARD_AIRSEA_COST_CONFIG_MY = f"{self._BI_SENSITIVE_BASE}/cost_data/my_airhaul_seahaul_cost_config"
        self.COST_CARD_XB_TP_STANDARD_RATE_CARD_SG = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/xb_tp_standard_rate_card"
        self.COST_CARD_XB_TP_SHIPPER_WEIGHT_CONFIG_SG = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/xb_tp_shipper_weight_config"
        self.COST_CARD_XB_TP_SHIPPER_RATE_CARD_SG = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/xb_tp_shipper_rate_card"
        self.COST_CARD_COST_CONFIG_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_cost_config"
        self.COST_CARD_SHOPEE_DISCOUNT_CONFIG_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_shopee_discount_config"
        self.COST_CARD_MM_DRIVER_MAPPING_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_mm_driver_mapping"
        self.COST_CARD_MM_HUB_MAPPING_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_mm_hub_mapping"
        self.COST_CARD_RTS_DRIVER_TYPES_VN = f"{self._BI_SENSITIVE_BASE}/cost_data/vn_rts_driver_types"
        self.DP_EVENT_NAME_MAPPING = f"{self._MASKED_URI_BASE}/dp_event_name_mapping/master"
        self.FMO_DATA_CLAIM_TO_BI_CLAIM = f"{self._MASKED_URI_BASE}/fmo_data_claim_to_bi/claim"
        self.FMO_DATA_CLAIM_TO_BI_CLAIM_2024 = f"{self._MASKED_URI_BASE}/fmo_data_claim_to_bi/claim_2024"
        self.FMO_DATA_CLAIM_TO_BI_CLAIM_2025 = f"{self._MASKED_URI_BASE}/fmo_data_claim_to_bi/claim_2025"
        self.HUB_DRIVING_DISTANCES_MY = f"{self._MASKED_URI_BASE}/driving_distances/my"
        self.HUB_DRIVING_DISTANCES_PH = f"{self._MASKED_URI_BASE}/driving_distances/ph"
        self.HUB_DRIVING_DISTANCES_SG = f"{self._MASKED_URI_BASE}/driving_distances/sg"
        self.HUB_DRIVING_DISTANCES_TH = f"{self._MASKED_URI_BASE}/driving_distances/th"
        self.HUB_DRIVING_DISTANCES_VN = f"{self._MASKED_URI_BASE}/driving_distances/vn"
        self.HUB_DRIVING_DISTANCES_ID_1 = f"{self._MASKED_URI_BASE}/driving_distances_id_1/distance"
        self.HUB_DRIVING_DISTANCES_ID_2 = f"{self._MASKED_URI_BASE}/driving_distances_id_2/distance"
        self.HUB_DRIVING_DISTANCES_ID_3 = f"{self._MASKED_URI_BASE}/driving_distances_id_3/distance"
        self.HUB_TO_MSH_MAPPING_ID = f"{self._MASKED_URI_BASE}/hub_to_msh_mapping/id"
        self.ID_AF_SF_SCHEDULES = f"{self._MASKED_URI_BASE}/af_sf_schedules/id"
        self.ID_CLASSIFICATION_DRIVER_MAPPING = f"{self._MASKED_URI_BASE}/id_classification_master_data/driver_type_mapping"
        self.ID_CLASSIFICATION_STOPS_THRESHOLD = f"{self._MASKED_URI_BASE}/id_classification_master_data/stops_threshold"
        self.ID_FM_PAYROLL_RATES = f"{self._BI_SENSITIVE_BASE}/id_fm_payroll_data/rates"
        self.ID_FM_PAYROLL_VOLUME_BONUSES = f"{self._BI_SENSITIVE_BASE}/id_fm_payroll_data/volume_bonuses"
        self.ID_FM_PAYROLL_DENSITY_UMK = f"{self._BI_SENSITIVE_BASE}/id_fm_payroll_data/density_umk"
        self.ID_FRAUD_FLAGGED_ORDERS_OWR = f"{self._MASKED_URI_BASE}/id_fraud_flagged_orders/owr"
        self.ID_FRAUD_FLAGGED_ORDERS_ML_MODEL = f"{self._MASKED_URI_BASE}/id_fraud_flagged_orders/ml_model"
        self.ID_FRAUD_PHOTO_REQUEST = f"{self._MASKED_URI_BASE}/id_fraud_photo_data/request"
        self.ID_FRAUD_PHOTO_RESPONSE = f"{self._MASKED_URI_BASE}/id_fraud_photo_data/response"
        self.ID_HUB_CONNECTIONS = f"{self._MASKED_URI_BASE}/hub_connections/id"
        self.ID_LM_PAYROLL_EX_JABO_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/ex_jabo_parcel_points"
        self.ID_LM_PAYROLL_JABO_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/jabo_parcel_points"
        self.ID_LM_PAYROLL_DELIVERY_PARCEL_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/delivery_parcel_points"
        self.ID_LM_PAYROLL_PICKUP_PARCEL_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/pickup_parcel_points"
        self.ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/delivery_parcel_exception_points"
        self.ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS_MAP = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/delivery_parcel_exception_points_map"
        self.ID_LM_PAYROLL_PARCEL_RATES = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/parcel_rates"
        self.ID_LM_PAYROLL_VOLUME_BONUSES = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/volume_bonuses"
        self.ID_LM_PAYROLL_HUB_DATA_ADJUSTED = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/hub_data_adjusted"
        self.ID_LM_PAYROLL_ZONE_DATA_ADJUSTED = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/zone_data_adjusted"
        self.ID_LM_PAYROLL_HUB_DATA_TRIAL = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/hub_data_trial"
        self.ID_LM_PAYROLL_ZONE_DATA_TRIAL = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/zone_data_trial"
        self.ID_LM_PAYROLL_DENSITY_PRIORITY = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_data/density_priority"
        self.ID_LM_SUCCESS_RATE_GOAL = f"{self._BI_SENSITIVE_BASE}/success_rate_goal/sheet1"
        self.ID_LM_MONTHLY_BONUS = f"{self._BI_SENSITIVE_BASE}/compile_sph_bonus/raw"
        self.ID_LM_MONTHLY_DEDUCTION = f"{self._BI_SENSITIVE_BASE}/compile_deduct_rider/raw"
        self.ID_MILKRUN_CONNECTIONS = f"{self._MASKED_URI_BASE}/milkrun_connections/id"
        self.LEAD_GEN_TEAM_MAPPING = f"{self._MASKED_URI_BASE}/media_source_mapping/lead_gen_team"
        self.MATCHED_SHIPPERS_MASTER = f"{self._MASKED_URI_BASE}/appsflyer-mixpanel_matched_shippers/master"
        self.MARKETING_ACTUAL_SPENDS_NON_API_SG_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/sg_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_MY_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/my_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_PH_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/ph_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_TH_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/th_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_ID_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/id_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_VN_FY23 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/vn_fy23"
        self.MARKETING_ACTUAL_SPENDS_NON_API_SG_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/sg_fy24"
        self.MARKETING_ACTUAL_SPENDS_NON_API_MY_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/my_fy24"
        self.MARKETING_ACTUAL_SPENDS_NON_API_PH_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/ph_fy24"
        self.MARKETING_ACTUAL_SPENDS_NON_API_TH_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/th_fy24"
        self.MARKETING_ACTUAL_SPENDS_NON_API_ID_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/id_fy24"
        self.MARKETING_ACTUAL_SPENDS_NON_API_VN_FY24 = f"{self._MASKED_URI_BASE}/marketing_actual_spends_non_api/vn_fy24"
        self.MARKETING_PLANNED_SPENDS_SG_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/sg_fy23"
        self.MARKETING_PLANNED_SPENDS_MY_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/my_fy23"
        self.MARKETING_PLANNED_SPENDS_PH_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/ph_fy23"
        self.MARKETING_PLANNED_SPENDS_TH_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/th_fy23"
        self.MARKETING_PLANNED_SPENDS_ID_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/id_fy23"
        self.MARKETING_PLANNED_SPENDS_VN_FY23 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/vn_fy23"
        self.MARKETING_PLANNED_SPENDS_SG_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/sg_fy24"
        self.MARKETING_PLANNED_SPENDS_MY_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/my_fy24"
        self.MARKETING_PLANNED_SPENDS_PH_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/ph_fy24"
        self.MARKETING_PLANNED_SPENDS_TH_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/th_fy24"
        self.MARKETING_PLANNED_SPENDS_ID_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/id_fy24"
        self.MARKETING_PLANNED_SPENDS_VN_FY24 = f"{self._MASKED_URI_BASE}/marketing_planned_spends/vn_fy24"
        self.MARKETING_UNCONVENTIONAL_CAMPAIGNS = f"{self._MASKED_URI_BASE}/marketing_unconventional_campaigns/unconventional_campaigns"
        self.MM_VEHICLE_TYPES = f"{self._MASKED_URI_BASE}/mm_vehicle_types/mm_vehicle_type_table"
        self.NB_LEADS_NOT_IN_SF = f"{self._MASKED_URI_BASE}/nb_leads_not_in_sf/my"
        self.NINJA_BUDDIES_SALES_TERRITORY_GROUPING = f"{self._MASKED_URI_BASE}/ninja_buddies_sales_territory_grouping/sales_territory"
        self.MM_TRIP_STITCH_COST = f"{self._BI_SENSITIVE_BASE}/mm_cost/mm_trip_stitch_cost"
        self.PARTNERSHIP_SHIPPER_MASTER_LIST = f"{self._MASKED_URI_BASE}/partnership_shipper_master_list/partnership_shipper_list"
        self.PARTNERSHIP_WEBHOOK_STATUS_CONFIG = f"{self._MASKED_URI_BASE}/partnership_webhook_configuration/status_config"
        self.PARTNERSHIP_WEBHOOK_SLA_CONFIG = f"{self._MASKED_URI_BASE}/partnership_webhook_configuration/sla_config"
        self.PLATFORM_MAPPING = f"{self._MASKED_URI_BASE}/media_source_mapping/platform"
        self.TRIP_COST_TIERS = f"{self._BI_SENSITIVE_BASE}/mm_cost/trip_cost_tiers"
        self.VENDOR_DEVICES = f"{self._MASKED_URI_BASE}/vendor_device_id/sheet3"
        self.VN_MISSORT_EXCLUSIONS = f"{self._MASKED_URI_BASE}/vn_missort_exclusions/sheet1"
        self.ID_FRAUD_EMAIL_DOMAIN_WHITELIST = f"{self._MASKED_URI_BASE}/id_fraud/email_domain_whitelist"
        self.ID_FRAUD_SHIPPER_WHITELIST_BY_MITRA = f"{self._MASKED_URI_BASE}/id_fraud/shipper_whitelist_by_mitra"
        self.ID_FRAUD_COD_SUCCESS_SHIPPER_LIST = f"{self._MASKED_URI_BASE}/id_fraud/cod_success"
        self.ID_FRAUD_SLA_BREACH_SHIPPER_LIST = f"{self._MASKED_URI_BASE}/id_fraud/sla_breach"
        self.ID_FRAUD_SHIPPER_BLACKLIST = f"{self._MASKED_URI_BASE}/id_fraud/fraud_shippers"
        self.ND_MM_SLA_JAN21_JUL21 = f"{self._MASKED_URI_BASE}/nd_mm_sla_jan21_jul21/sheet1"
        self.ID_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/id"
        self.MY_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/my"
        self.PH_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/ph"
        self.SG_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/sg"
        self.TH_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/th"
        self.VN_FM_SHIPPER_EXCLUSION_LIST = f"{self._MASKED_URI_BASE}/reg_fm_shipper_exclusion_list/vn"
        self.SHIPPER_PICKUP_CUTOFF = f"{self._MASKED_URI_BASE}/cutoff_for_n+0_shipper_pickup_kpi/sheet1"
        self.SF_TASK_RIDER_TYPE = f"{self._MASKED_URI_BASE}/sf_task_rider_type/id"
        self.SG_COST_CARD_POSTCODE = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/sg_postcode_map"
        self.SG_COST_CARD_FM_DRIVER_HOUR = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/fm_driver_hour"
        self.SG_COST_CARD_COST_SEGMENT = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/cost_segment"
        self.SG_COST_CARD_EXCLUDED_SHIPPER = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/excluded_shipper"
        self.SG_COST_CARD_TIME_PER_PARCEL = f"{self._BI_SENSITIVE_BASE}/sg_cost_config/time_per_parcel"
        self.SG_SLA_DEST_ZONE = f"{self._MASKED_URI_BASE}/sg_sla_config/dest_zone"
        self.MY_LM_PAYROLL_DATA_DELIVERY_RATES = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_delivery_rates"
        self.MY_LM_PAYROLL_DATA_RESERVATION_RATES = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_reservation_rates"
        self.MY_LM_PAYROLL_DATA_RESERVATION_RATES_DEV = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_reservation_rates_dev"
        self.MY_LM_PAYROLL_DATA_FREE_PARCELS = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_free_parcels"
        self.MY_LM_PAYROLL_DATA_INDP_SR = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_indp_sr"
        self.MY_LM_PAYROLL_DATA_BALLOON_TARGETS = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_balloon_targets"
        self.MY_LM_PAYROLL_DATA_LOGIC = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/logic"
        self.MY_LM_PAYROLL_DATA_LOGIC_DEV = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/logic_dev"
        self.MY_LM_PAYROLL_DATA_DELIVERY_RATES_DEV = f"{self._BI_SENSITIVE_BASE}/my_payroll_automation_master_sheet/overall_delivery_rates_dev"
        self.TIKTOK_LOGISTIC_PARTNER_PLATFORM_DATA_REPORT = f"{self._MASKED_URI_BASE}/daily_report_input_to_matabase/tt_lsp_data"
        self.TIKTOK_INTERNAL_TICKET_DATA = f"{self._MASKED_URI_BASE}/daily_report_input_to_matabase/internal_ticket_data"
        self.TIKTOK_TRACKER_DATA_REPORT = f"{self._MASKED_URI_BASE}/daily_report_input_to_matabase/tt_tracker_data"
        self.REG_BI_FX_RATES_CONSOLIDATED = f"{self._MASKED_URI_BASE}/reg_bi_fx_rates_consolidated/sheet1"
        self.XB_SHIPPER_RATES_PERIODS = f"{self._MASKED_URI_BASE}/xb_shipper_rates/periods"
        self.XB_SHIPPER_RATES_RATE = f"{self._MASKED_URI_BASE}/xb_shipper_rates/rate"
        self.XB_SHIPPER_RATES_RATE_REGIONS = f"{self._MASKED_URI_BASE}/xb_shipper_rates/rate_regions"
        self.XB_SHIPPER_RATES_REGIONS = f"{self._MASKED_URI_BASE}/xb_shipper_rates/regions"
        self.XB_SHIPPER_RATES_SHIPPER_PROFILE = f"{self._MASKED_URI_BASE}/xb_shipper_rates/shipper_profile"
        self.XB_SHIPPER_RATES_WEIGHT_RANGES = f"{self._MASKED_URI_BASE}/xb_shipper_rates/weight_ranges"
        self.XB_TP_RATES_TP_RATES_INFO = f"{self._MASKED_URI_BASE}/xb_tp_rates/tp_rates_info"
        self.XB_TP_RATES_TP_RATES_STATUS = f"{self._MASKED_URI_BASE}/xb_tp_rates/tp_rates_status"
        self.XB_TP_RATES_TP_RATES_WEIGHT_RANGE = f"{self._MASKED_URI_BASE}/xb_tp_rates/tp_rates_weight_range"
        self.COURIER_DISCIPLINE_REPORT_TARGETS = f"{self._MASKED_URI_BASE}/courier_discipline_master_data/targets"
        self.COURIER_DISCIPLINE_REPORT_SUSPENSION_CONFIGS = f"{self._MASKED_URI_BASE}/courier_discipline_master_data/suspension_configs"
        self.COURIER_DISCIPLINE_REPORT_SUSPENSION_LEVELS = f"{self._MASKED_URI_BASE}/courier_discipline_master_data/suspension_levels"
        self.PH_LM_MANUAL_REGION_MAPPING = f"{self._MASKED_URI_BASE}/lm_push_off_d0_mailing_list/ph_region_mapping"
        self.PH_LM_PAYROLL_COURIER_TYPE = f"{self._BI_SENSITIVE_BASE}/ph_payroll_automation/courier_type"
        self.PH_LM_PAYROLL_OTHER_CONFIG = f"{self._BI_SENSITIVE_BASE}/ph_payroll_automation/other_config"
        self.PH_LM_PAYROLL_PARCEL_SIZE_INCENTIVE = f"{self._BI_SENSITIVE_BASE}/ph_payroll_automation/parcel_size_incentive"
        self.PH_LM_PAYROLL_REGION_SUCCESS_RATE_CONFIG = f"{self._BI_SENSITIVE_BASE}/ph_payroll_automation/region_success_rate_config"
        self.PH_LM_PAYROLL_PPR_PAY_SCHEME_MATRIX_CLEANED = f"{self._BI_SENSITIVE_BASE}/ph_payroll_automation/ppr_pay_scheme_matrix_cleaned"
        self.COURIER_DISCIPLINE_REPORT_DRIVER_TYPES = f"{self._MASKED_URI_BASE}/courier_discipline_master_data/driver_types"
        self.COURIER_DISCIPLINE_REPORT_TARGETS_WEEKEND = f"{self._MASKED_URI_BASE}/courier_discipline_master_data/targets_weekend"
        self.MART_MASTER_DATA_VN_LOVS = f"{self._MASKED_URI_BASE}/vn_mart_master_data_for_bi_ingestion/mart_vn_lovs"
        self.MART_MASTER_DATA_MY_LOVS = f"{self._MASKED_URI_BASE}/my_mart_master_data_for_bi_ingestion/mart_my_lovs"
        self.ID_AIRPORT_RA_LATLONG = f"{self._MASKED_URI_BASE}/id_airport_ra_latlong/config"
        self.TIKTOK_LLP_ID_CX_MASKED = f"{self._MASKED_URI_BASE}/tiktok_lpp_2025_for_id_cx/raw_data"


class OrderNPSSubmissions:
    def __init__(self, env="prod"):
        self.ORDER_NPS_SUBMISSIONS = f"{MASKED_DATALAKE_BASE_URI.format(env)}/order_nps_submissions"