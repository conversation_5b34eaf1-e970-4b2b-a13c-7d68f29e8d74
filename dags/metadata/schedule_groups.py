SCHEDULE_GROUPS_CONFIG = {
    "prod": {
        "HIGH": [
            "dp_prod_gl",
            "driver_prod_gl",
            "epi_prod_gl",
            "events_prod_gl",
            "hub_prod_gl",
            "route_prod_gl",
            "billing_prod_gl",
            "script_engine_prod_gl",
            "shipper_prod_gl",
            "3pl_prod_gl",
            "direct_prod_gl",
            "sort_prod_gl",
            "ticketing_prod_gl",
            "xb_ops_prod_gl",
            "core_prod_mm",
            "core_prod_my",
            "core_prod_sg",
            "core_prod_th",
            "core_prod_id",
            "core_prod_ph",
            "core_prod_vn",
            "xb_operations_prod_gl"
        ],
        "MEDIUM": [
            "pod_validation_prod_gl",
            "pricing_prod_gl",
            "3pl_oms_prod_gl",
            "sns_platforms_prod_gl",
            "mart_sp_mgmt_prod_gl",
            "notifications_push_prod_gl",
            "address_appraiser_prod_gl",
            "goals_prod_gl",
            "argus_prod_gl",
            "cod_prod_gl",
            "direct_supplier_prod_gl",
            "fdm_prod_gl",
            "first_mile_prod_gl",
            "hub_usrs_prod_gl",
            "mart_promotion_prod_gl",
            "movement_trip_prod_gl",
            "pa_job_search_prod_gl",
            "order_prod_gl",
            "recovery_comms_prod_gl",
            "sort_mistake_prod_gl"
        ],
        "LOW": [
            "aaa_prod_gl",
            "loyalty_prod_gl",
            "overwatch_prod_gl",
            "reports_prod_gl",
            "notifications_voice_prod_gl",
            "addressing_prod_gl",
            "consignee_prod_gl",
            "sns_prod_gl",
            "url_shortener_gl",
            "wms_prod_gl",
            "dash_prod_gl",
            "station_prod_gl",
            "sort_vendor_prod_gl",
            "webhook_history_prod_gl",
            "control_prod_gl",
            "ocreate_prod_gl",
            "notifications_email_prod_gl",
            "notifications_prod_gl",
            "notifications_sms_prod_gl",
            "notifications_v2_prod_gl",
            "automation"
        ]
    },
    "dev": {
        "HIGH": [
            "dp_dev_gl",
            "driver_dev_gl",
            "epi_dev_gl",
            "events_dev_gl",
            "hub_dev_gl",
            "route_dev_gl",
            "billing_dev_gl",
            "script_engine_dev_gl",
            "shipper_dev_gl",
            "3pl_dev_gl",
            "direct_dev_gl",
            "sort_dev_gl",
            "ticketing_dev_gl",
            "xb_ops_dev_gl",
            "core_dev_mm",
            "core_dev_my",
            "core_dev_sg",
            "core_dev_th",
            "core_dev_id",
            "core_dev_ph",
            "core_dev_vn",
            "xb_operations_dev_gl"
        ],
        "MEDIUM": [
            "pod_validation_dev_gl",
            "pricing_dev_gl",
            "3pl_oms_dev_gl",
            "sns_platforms_dev_gl",
            "mart_sp_mgmt_dev_gl",
            "notifications_push_dev_gl",
            "address_appraiser_dev_gl",
            "goals_dev_gl",
            "argus_dev_gl",
            "cod_dev_gl",
            "direct_supplier_dev_gl",
            "fdm_dev_gl",
            "first_mile_dev_gl",
            "hub_usrs_dev_gl",
            "mart_promotion_dev_gl",
            "movement_trip_dev_gl",
            "pa_job_search_dev_gl",
            "order_dev_gl",
            "recovery_comms_dev_gl",
            "sort_mistake_dev_gl"
        ],
        "LOW": [
            "aaa_dev_gl",
            "loyalty_dev_gl",
            "overwatch_dev_gl",
            "reports_dev_gl",
            "notifications_voice_dev_gl",
            "addressing_dev_gl",
            "consignee_dev_gl",
            "sns_dev_gl",
            "url_shortener_gl",
            "wms_dev_gl",
            "dash_dev_gl",
            "station_dev_gl",
            "sort_vendor_dev_gl",
            "webhook_history_dev_gl",
            "control_dev_gl",
            "ocreate_dev_gl",
            "notifications_email_dev_gl",
            "notifications_dev_gl",
            "notifications_sms_dev_gl",
            "notifications_v2_dev_gl",
            "automation"
        ]
    }
}

from config.config import Config

env = "dev" if Config.ENV == "dev" else "prod"
SCHEDULE_GROUPS = SCHEDULE_GROUPS_CONFIG[env]