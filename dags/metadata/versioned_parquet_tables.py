from metadata.constants import BI_SENSITIVE_REPORTS_BASE_URI, DATAWAREHOUSE_BASE_URI, MASKED_DATA_WAREHOUSE_BASE_URI


class DataWarehouse:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = BI_SENSITIVE_REPORTS_BASE_URI.format(env)
        self._URI_BASE = DATAWAREHOUSE_BASE_URI.format(env)
        self._MASKED_URI_BASE = MASKED_DATA_WAREHOUSE_BASE_URI.format(env)
        self.ACTION_AFTER_TICKET_CLOSURE_BASE = f"{self._URI_BASE}/action_after_ticket_closure_base"
        self.ACTION_AFTER_TICKET_CLOSURE_DAILY = f"{self._URI_BASE}/action_after_ticket_closure_daily"
        self.ACTION_AFTER_TICKET_CLOSURE_REPORT = f"{self._URI_BASE}/action_after_ticket_closure_report"
        self.ACTIVE_ORDERS = f"{self._URI_BASE}/active_orders"
        self.ADD_TO_SHIPMENT_EVENTS = f"{self._URI_BASE}/add_to_shipment_events"
        self.ADDRESS_VERIFICATION_ACCURACY_REPORT = f"{self._URI_BASE}/address_verification_accuracy_report"
        self.AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT = f"{self._URI_BASE}/auto_address_verification_accuracy_report"
        self.CANCELLED_EVENTS = f"{self._URI_BASE}/cancelled_events"
        self.CISP_COMPLETION_REPORT = f"{self._URI_BASE}/cisp_completion_report"
        self.CISP_COMPLETION_REPORT_DAILY = f"{self._URI_BASE}/cisp_completion_report_daily"
        self.CISP_PRIOR_REPORT = f"{self._URI_BASE}/cisp_prior_report"
        self.CISP_PRIOR_REPORT_DAILY = f"{self._URI_BASE}/cisp_prior_report_daily"
        self.CISP_REPORT_BASE = f"{self._URI_BASE}/cisp_report_base"
        self.CISP_TERMINAL_STATUS_REPORT = f"{self._URI_BASE}/cisp_terminal_status_report"
        self.CISP_TERMINAL_STATUS_REPORT_DAILY = f"{self._URI_BASE}/cisp_terminal_status_report_daily"
        self.COD_ORDERS_TO_EXAMINE = f"{self._URI_BASE}/cod_orders_to_examine"
        self.COUNTRY_COMPLETION_VOL_DAILY = f"{self._URI_BASE}/country_completion_vol_daily"
        self.DELIVERY_FAILURE_EVENTS = f"{self._URI_BASE}/delivery_failure_events"
        self.DELIVERY_TRANSACTION_EVENTS = f"{self._URI_BASE}/delivery_transaction_events"
        self.DIM_WEIGHT_SCANS = f"{self._URI_BASE}/dim_weight_scans"
        self.DIM_WEIGHT_SCANS_BASE = f"{self._URI_BASE}/dim_weight_scans_base"
        self.DIMENSION_DATES = f"{self._URI_BASE}/dimension_dates"
        self.DP_OPERATING_HOURS_ENRICHED = f"{self._URI_BASE}/dp_operating_hours_enriched"
        self.DP_ORDER_EVENTS = f"{self._URI_BASE}/dp_order_events"
        self.DP_PARTNERS_ENRICHED = f"{self._URI_BASE}/dp_partners_enriched"
        self.DP_RESERVATION_EVENTS_ENRICHED = f"{self._URI_BASE}/dp_reservation_events_enriched"
        self.DP_RESERVATIONS_ENRICHED = f"{self._URI_BASE}/dp_reservations_enriched"
        self.DP_SHIPPER_VOL_DAILY = f"{self._URI_BASE}/dp_shipper_vol_daily"
        self.DPS_ENRICHED = f"{self._URI_BASE}/dps_enriched"
        self.DRIVER_RANKING_ENRICHED = f"{self._URI_BASE}/driver_ranking_enriched"
        self.DRIVER_SCAN_EVENTS = f"{self._URI_BASE}/driver_scan_events"
        self.DRIVER_TYPES_ENRICHED = f"{self._URI_BASE}/driver_types_enriched"
        self.DRIVERS_ENRICHED = f"{self._URI_BASE}/drivers_enriched"
        self.EXCLUSION_REQUESTS = f"{self._URI_BASE}/exclusion_requests"
        self.EXCLUSION_REQUEST_DETAILS = f"{self._URI_BASE}/exclusion_request_details"
        self.EXTERNAL_XDOCK_ORDER_MAPPINGS_ENRICHED = f"{self._URI_BASE}/external_xdock_order_mappings_enriched"
        self.FM_SHIPPER_EXCLUSIONS = f"{self._URI_BASE}/fm_shipper_exclusions"
        self.FLEET_PERFORMANCE_BASE_DATA = f"{self._URI_BASE}/fleet_performance_base_data"
        self.FIELD_AND_CORP_SALES_SHIPPER_EXPORT = f"{self._URI_BASE}/field_and_corp_sales_shipper_export"
        self.FIELD_SALES_SHORT_TERM_RETENTION = f"{self._URI_BASE}/field_sales_short_term_retention"
        self.FIELD_SALES_SHORT_TERM_RETENTION_BASE = f"{self._URI_BASE}/field_sales_short_term_retention_base"
        self.FIRST_TERMINAL_STATUS_EVENTS = f"{self._URI_BASE}/first_terminal_status_events"
        self.FORCE_SUCCESS_EVENTS = f"{self._URI_BASE}/force_success_events"
        self.FS_SEGMENTATION_DAILY = f"{self._URI_BASE}/fs_segmentation_daily"
        self.HIGH_COD_PRICE_SCRAPPING_REPORT = f"{self._URI_BASE}/high_cod_price_scrapping_report"
        self.HUB_COMPLETION_VOL_DAILY = f"{self._URI_BASE}/hub_completion_vol_daily"
        self.HUB_INBOUND_EVENTS = f"{self._MASKED_URI_BASE}/hub_inbound_events"
        self.HUB_INBOUND_VOL_DAILY = f"{self._URI_BASE}/hub_inbound_vol_daily"
        self.HUB_RELATION_SCHEDULES_ENRICHED = f"{self._URI_BASE}/hub_relation_schedules_enriched"
        self.HUB_SWEEP_REPORT = f"{self._URI_BASE}/hub_sweep_report"
        self.HUBS_ENRICHED = f"{self._URI_BASE}/hubs_enriched"
        self.ID_LM_PAYROLL_DAILY = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_daily"
        self.ID_LM_PAYROLL_MONTHLY = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_monthly"
        self.ID_MANUAL_AV_LEADTIME = f"{self._URI_BASE}/id_manual_av_leadtime"
        self.IMPLANTED_MANIFEST_SCAN_EVENTS = f"{self._URI_BASE}/implanted_manifest_scan_events"
        self.IMPLANTED_MANIFEST_SCAN_EVENTS_ENRICHED = f"{self._URI_BASE}/implanted_manifest_scan_events_enriched"
        self.JIRA_ISSUE_EVENTS = f"{self._URI_BASE}/jira_issue_events"
        self.JIRA_ISSUES_ENRICHED = f"{self._URI_BASE}/jira_issues_enriched"
        self.JIRA_SPRINT_ISSUES = f"{self._URI_BASE}/jira_sprint_issues"
        self.JIRA_SPRINTS = f"{self._URI_BASE}/jira_sprints"
        self.LAST_MILE_PUSH_OFF_CUTOFFS = f"{self._URI_BASE}/last_mile_push_off_cutoffs"
        self.LAST_MILE_PUSH_OFF_REPORT = f"{self._URI_BASE}/last_mile_push_off_report"
        self.LAST_MILE_TIMESLOT_ADHERENCE_COURIER_REPORT = (
            f"{self._URI_BASE}/last_mile_timeslot_adherence_courier_report"
        )
        self.LAST_MILE_TIMESLOT_ADHERENCE_REPORT = f"{self._URI_BASE}/last_mile_timeslot_adherence_report"
        self.LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT = (
            f"{self._URI_BASE}/last_mile_timeslot_adherence_shipper_report"
        )
        self.LATEST_SHIPMENT_REPORT = f"{self._URI_BASE}/latest_shipment_report"
        self.LIQUIDATION_PARCELS = f"{self._URI_BASE}/liquidation_parcels"
        self.MIDDLE_MILE_TRIP_RELATIONSHIPS = f"{self._URI_BASE}/middle_mile_trip_relationships"
        self.MILKRUN_ENRICHED = f"{self._URI_BASE}/milkrun_enriched"
        self.MISSORT_DAILY_REPORT = f"{self._URI_BASE}/missort_daily_report"
        self.MISSORT_KPI = f"{self._URI_BASE}/missort_kpi"
        self.MM_DRIVERS_ENRICHED = f"{self._URI_BASE}/mm_drivers_enriched"
        self.MOVEMENT_TRIPS_ENRICHED = f"{self._URI_BASE}/movement_trips_enriched"
        self.N0_PICKUP_SLA_KPI = f"{self._URI_BASE}/n0_pickup_sla_kpi"
        self.NINJA_BUDDIES_BUDDY_PAYOUT = f"{self._URI_BASE}/ninja_buddies_buddy_payout"
        self.NINJA_BUDDIES_FRIEND_PAYOUT = f"{self._URI_BASE}/ninja_buddies_friend_payout"
        self.NINJA_BUDDIES_LEADS = f"{self._URI_BASE}/ninja_buddies_leads"
        self.NINJA_REWARDS_MONTHLY = f"{self._URI_BASE}/ninja_rewards_monthly"
        self.NINJA_REWARDS_USERS = f"{self._URI_BASE}/ninja_rewards_users"
        self.NINJA_REWARDS_USERS_BASE = f"{self._URI_BASE}/ninja_rewards_users_base"
        self.ON_HOLD_EVENTS = f"{self._URI_BASE}/on_hold_events"
        self.ORDER_DELIVERIES = f"{self._URI_BASE}/order_deliveries"
        self.ORDER_DEPARTMENT_MOVEMENTS = f"{self._URI_BASE}/order_department_movements"
        self.ORDER_DESTINATIONS = f"{self._URI_BASE}/order_destinations"
        self.ORDER_DP_MILESTONES = f"{self._URI_BASE}/order_dp_milestones"
        self.ORDER_EVENTS_PICKUP_SUCCESS = f"{self._URI_BASE}/order_events_pickup_success"
        self.ORDER_FORCE_SUCCESSES = f"{self._URI_BASE}/order_force_successes"
        self.ORDER_FOURTH_PARTY_HANDOVERS = f"{self._URI_BASE}/order_fourth_party_handovers"
        self.ORDER_HUB_MOVEMENTS = f"{self._URI_BASE}/order_hub_movements"
        self.ORDER_INBOUNDS = f"{self._URI_BASE}/order_inbounds"
        self.ORDER_MILESTONES = f"{self._URI_BASE}/order_milestones"
        self.ORDER_MOVEMENTS = f"{self._URI_BASE}/order_movements"
        self.ORDER_MOVEMENTS_BASE = f"{self._URI_BASE}/order_movements_base"
        self.ORDER_PICKUPS = f"{self._URI_BASE}/order_pickups"
        self.ORDER_RTS_TRIGGER_LOCATIONS = f"{self._URI_BASE}/order_rts_trigger_locations"
        self.ORDER_RTS_TRIGGERS = f"{self._URI_BASE}/order_rts_triggers"
        self.ORDER_TAGS_ENRICHED = f"{self._URI_BASE}/order_tags_enriched"
        self.ORDER_THIRD_PARTY_TRANSFERS = f"{self._URI_BASE}/order_third_party_transfers"
        self.ORDERS_ENRICHED = f"{self._URI_BASE}/orders_enriched"
        self.PARCEL_MEASUREMENT_SCAN_ENRICHED = f"{self._URI_BASE}/parcel_measurement_scan_enriched"
        self.PARCEL_SWEEPER_EVENTS = f"{self._URI_BASE}/parcel_sweeper_events"
        self.PARENT_SHIPPER_LIFETIME_VALUES = f"{self._URI_BASE}/parent_shipper_lifetime_values"
        self.PARENT_SHIPPER_LIFETIME_VALUES_BASE = f"{self._URI_BASE}/parent_shipper_lifetime_values_base"
        self.PETS_TICKETS_ENRICHED = f"{self._URI_BASE}/pets_tickets_enriched"
        self.PETS_TICKETS_ENRICHED_BASE = f"{self._URI_BASE}/pets_tickets_enriched_base"
        self.PETS_TICKETS_RESOLVED_DAILY = f"{self._URI_BASE}/pets_tickets_resolved_daily"
        self.PICKUP_SCAN_EVENTS = f"{self._URI_BASE}/pickup_scan_events"
        self.PICKUP_TRANSACTION_EVENTS = f"{self._URI_BASE}/pickup_transaction_events"
        self.POD_VALIDATION_TASKS_ENRICHED = f"{self._URI_BASE}/pod_validation_tasks_enriched"
        self.POH_ORDER_METRICS = f"{self._URI_BASE}/proof_of_handover_order_metrics"
        self.POH_METRICS = f"{self._URI_BASE}/proof_of_handover_metrics"
        self.RESERVATIONS_ENRICHED = f"{self._URI_BASE}/reservations_enriched"
        self.RESERVATION_ROUTED_EVENTS = f"{self._URI_BASE}/reservation_routed_events"
        self.RESERVE_TRACKING_IDS_ENRICHED = f"{self._URI_BASE}/reserve_tracking_ids_enriched"
        self.ROUTE_LOGS_ENRICHED = f"{self._URI_BASE}/route_logs_enriched"
        self.RTS_RATES_KPI = f"{self._URI_BASE}/rts_rates_kpi"
        self.RTS_TRIGGER_EVENTS = f"{self._URI_BASE}/rts_trigger_events"
        self.SALES_TARGETS_BU = f"{self._URI_BASE}/sales_targets_bu"
        self.SALESFORCE_ACCOUNT_ENRICHED = f"{self._URI_BASE}/salesforce_account_enriched"
        self.SALESFORCE_ACTIVITY_ENRICHED = f"{self._URI_BASE}/salesforce_activity_enriched"
        self.SALESFORCE_CASE_ENRICHED = f"{self._URI_BASE}/salesforce_case_enriched"
        self.SALESFORCE_CLAIM_ENRICHED = f"{self._URI_BASE}/salesforce_claim_enriched"
        self.SALESFORCE_CLAIM_ENRICHED_BASE = f"{self._URI_BASE}/salesforce_claim_enriched_base"
        self.SALESFORCE_CONTACT_ENRICHED = f"{self._URI_BASE}/salesforce_contact_enriched"
        self.SALESFORCE_CS_CASE_ENRICHED = f"{self._URI_BASE}/salesforce_cs_case_enriched"
        self.SALESFORCE_LEAD_ENRICHED = f"{self._URI_BASE}/salesforce_lead_enriched"
        self.SALESFORCE_OPPORTUNITY_ENRICHED = f"{self._URI_BASE}/salesforce_opportunity_enriched"
        self.SALESFORCE_SHIPPER_EXPORT = f"{self._URI_BASE}/salesforce_shipper_export"
        self.SALESFORCE_RECORD_TYPE_ENRICHED = f"{self._URI_BASE}/salesforce_record_type_enriched"
        self.SALESFORCE_USER_ENRICHED = f"{self._URI_BASE}/salesforce_user_enriched"
        self.SALESPERSONS_ENRICHED = f"{self._URI_BASE}/salespersons_enriched"
        self.SCAN_RESULT_ENRICHED = f"{self._URI_BASE}/scan_result_enriched"
        self.SERVICE_BREACH_REPORT = f"{self._URI_BASE}/service_breach_report"
        self.SFMC_SHIPPERS_EXPORT = f"{self._URI_BASE}/sfmc_shippers_export"
        self.SG_SLA_ENRICHED = f"{self._URI_BASE}/sg_sla_enriched"
        self.SHIPMENT_HUB_MILESTONES = f"{self._URI_BASE}/shipment_hub_milestones"
        self.SHIPMENT_ORDERS_ENRICHED = f"{self._URI_BASE}/shipment_orders_enriched"
        self.SHIPMENT_ORDERS_ENRICHED_BASE = f"{self._URI_BASE}/shipment_orders_enriched_base"
        self.SHIPMENT_PATHS = f"{self._URI_BASE}/shipment_paths"
        self.SHIPMENTS_ENRICHED = f"{self._URI_BASE}/shipments_enriched"
        self.SHIPPER_ATTRIBUTES = f"{self._URI_BASE}/shipper_attributes"
        self.SHIPPER_CLAIMS_REPORT = f"{self._URI_BASE}/shipper_claims_report"
        self.SHIPPER_COMPLETION_VOL_DAILY = f"{self._URI_BASE}/shipper_completion_vol_daily"
        self.SHIPPER_COMPLETION_VOL_DAILY_FULL = f"{self._URI_BASE}/shipper_completion_vol_daily_full"
        self.SHIPPER_COMPLETION_VOL_MONTHLY = f"{self._URI_BASE}/shipper_completion_vol_monthly"
        self.SHIPPER_CREATION_VOL_DAILY = f"{self._URI_BASE}/shipper_creation_vol_daily"
        self.SHIPPER_INBOUND_VOL_DAILY = f"{self._URI_BASE}/shipper_inbound_vol_daily"
        self.SHIPPER_INBOUND_VOL_MONTHLY = f"{self._URI_BASE}/shipper_inbound_vol_monthly"
        self.SHIPPER_LIFETIME_VALUES = f"{self._URI_BASE}/shipper_lifetime_values"
        self.SHIPPER_LIFETIME_VALUES_BASE = f"{self._URI_BASE}/shipper_lifetime_values_base"
        self.SHIPPER_MILESTONES = f"{self._URI_BASE}/shipper_milestones"
        self.SHIPPER_PICKUP_ASSIGNEES = f"{self._URI_BASE}/shipper_pickup_assignees"
        self.SHIPPER_SEGMENTATION_DAILY = f"{self._URI_BASE}/shipper_segmentation_daily"
        self.SHIPPER_SLA_DAYS = f"{self._URI_BASE}/shipper_sla_days"
        self.SHIPPERS_ENRICHED = f"{self._URI_BASE}/shippers_enriched"
        self.SHOPIFY_ORDERS = f"{self._URI_BASE}/shopify_orders"
        self.SLA_EXTENSIONS = f"{self._URI_BASE}/sla_extensions"
        self.SLA_REPORTS_BASE = f"{self._URI_BASE}/sla_reports_base"
        self.SORT_COMPLIANCE = f"{self._URI_BASE}/sort_compliance"
        self.SORT_ATS_COMPLIANCE = f"{self._URI_BASE}/sort_ats_compliance"
        self.SS_SEGMENTATION_DAILY = f"{self._URI_BASE}/ss_segmentation_daily"
        self.THIRD_PARTY_TRANSFER_EVENTS = f"{self._URI_BASE}/third_party_transfer_events"
        self.TICKET_CLOSURE_PERFORMANCE = f"{self._URI_BASE}/ticket_closure_performance"
        self.TICKET_CREATION_LAST_SCANS = f"{self._URI_BASE}/ticket_creation_last_scans"
        self.TICKET_CREATION_PERFORMANCE = f"{self._URI_BASE}/ticket_creation_performance"
        self.TICKET_CREATION_PERFORMANCE_DAILY = f"{self._URI_BASE}/ticket_creation_performance_daily"
        self.TICKET_CREATION_PERFORMANCE_REPORT = f"{self._URI_BASE}/ticket_creation_performance_report"
        self.TICKET_RESOLVED_EVENTS = f"{self._URI_BASE}/ticket_resolved_events"
        self.TRANSIT_TIME_REPORT = f"{self._URI_BASE}/transit_time_report"
        self.TRANSIT_TIME_SPEED_REPORT = f"{self._URI_BASE}/transit_time_speed_report"
        self.UPDATE_ADDRESS_EVENTS = f"{self._URI_BASE}/update_address_events"
        self.UPDATE_ADDRESS_VERIFICATION_EVENTS = f"{self._URI_BASE}/update_address_verification_events"
        self.UPDATE_CASH_EVENTS = f"{self._URI_BASE}/update_cash_events"
        self.UPDATE_CASH_EVENTS_ENRICHED = f"{self._URI_BASE}/update_cash_events_enriched"
        self.UPDATE_STATUS_EVENTS = f"{self._URI_BASE}/update_status_events"
        self.VALID_SCAN_EVENTS = f"{self._URI_BASE}/valid_scan_events"
        self.WAREHOUSE_SCAN_EVENTS = f"{self._URI_BASE}/warehouse_scan_events"
        self.WAREHOUSE_SPEED_REPORT = f"{self._URI_BASE}/warehouse_speed_report"
        self.WAREHOUSE_SPEED_REPORT_EVENTS = f"{self._URI_BASE}/warehouse_speed_report_events"
        self.WAYPOINTS_ENRICHED = f"{self._URI_BASE}/waypoints_enriched"
        self.WAYPOINT_PHOTOS_ENRICHED = f"{self._URI_BASE}/waypoint_photos_enriched"
        self.XB_OUTBOUND_ORDERS = f"{self._URI_BASE}/xb_outbound_orders"
        self.ZENDESK_TICKETS_ENRICHED = f"{self._URI_BASE}/zendesk_tickets_enriched"
