DATA_AUDIT_TABLES = {
    "dev": {"route_qa_gl": {"route_logs": ""}},
    "prod": {
        "addressing_prod_gl": {"zones": ["latitude", "longitude"], "mains": ""},
        "consignee_prod_gl": {
            "consignees": ["phone_number", "source"],
            "preferences": ["key_column7", "key_column8"],
        },
        "core_prod_id": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_vn": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_ph": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_sg": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_my": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_mm": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "core_prod_th": {
            "transactions": "",
            "inbound_scans": "",
            "blobs": "",
            "reservations": "",
            "cods": "",
            "orders": "",
            "order_details": "",
        },
        "route_prod_gl": {"route_logs": ""},
        "sort_prod_gl": {"hubs": ""},
        "driver_prod_gl": {"drivers": "", "driver_types": ""},
        "events_prod_gl": {"order_events": ""},
        "hub_prod_gl": {"scans": "", "shipments": "", "shipment_orders": ""},
        "dp_prod_gl": {"dps": "", "dp_reservations": ""},
        "shipper_prod_gl": {"shippers": ""},
        "pricing_prod_gl": {"pricing_orders_history": ""},
        "billing_prod_gl": {"priced_orders": ""},
    },
}
