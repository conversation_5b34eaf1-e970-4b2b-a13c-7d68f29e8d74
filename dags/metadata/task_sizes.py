CDC_TASK_SIZES = {
    "delta task id (e.g. pii_delta_3pl_prod_gl_async_task_tab)": "size, based on metadata.spark_conf",

    "delta_ninjamart_report_user_order_status_log": "large",
    "pii_delta_3pl_prod_gl_async_task_tab": "large_l3",
    "pii_delta_3pl_prod_gl_bag_events": "large_l3",
    "pii_delta_3pl_prod_gl_documents": "large",
    "pii_delta_3pl_prod_gl_events": "large",
    "pii_delta_billing_prod_gl_cod_orders": "large_l3",
    "pii_delta_billing_prod_gl_priced_orders": "large_l3",
    "pii_delta_core_prod_id_orders": "large_l3",
    "pii_delta_core_prod_id_transactions": "large_l3",
    "pii_delta_core_prod_my_orders": "large_l3",
    "pii_delta_core_prod_ph_orders": "large_l2",
    "pii_delta_dp_prod_gl_consignee_info": "large",
    "pii_delta_driver_prod_gl_parcels": "large_l3",
    "pii_delta_events_prod_gl_order_events": "large",
    "pii_delta_pod_validation_prod_gl_assignments": "large_l3",
    "pii_delta_pricing_prod_gl_pricing_evt_dlq": "large_l3",
    "pii_delta_pricing_prod_gl_pricing_orders": "large_l3",
    "pii_delta_route_prod_gl_waypoints": "medium_legacy_datetimeRebaseModeInWrite",
    "pii_delta_sns_prod_gl_messages": "large",
    "pii_delta_xb_operations_prod_gl_receiving_task_items": "large",
    "process_cdc_messages_3pl_prod_gl_parcels": "large",
    "process_cdc_messages_core_prod_id_orders"
    "process_cdc_messages_core_prod_vn_orders": "large",
    "process_cdc_messages_events_prod_gl_order_events": "large",
    "process_cdc_messages_pricing_prod_gl_pricing_orders": "large",
    "process_cdc_messages_pricing_prod_gl_pricing_orders_history": "large",
}
