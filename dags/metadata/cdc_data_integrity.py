# Tables excluded are those with many hard deletions unless stated otherwise.
# Tables with many hard deletions are excluded because their total row counts for historical dates are highly mutable,
# leading to false alerts and is unsuitable for our existing method of testing.
EXCLUDED_TABLES = {
    "dev": {"dp_analytics_dev_gl": ["aggregate_volumes_schedules"], "3pl_dev_gl": ["events"]},
    "qa": {"3pl_qa_gl": ["events"]},
    "prod": {
        "consignee_prod_gl": [
            "preferences",
        ],
        "core_prod_id": [
            "route_waypoint",
        ],
        "core_prod_mm": [
            "route_waypoint",
        ],
        "core_prod_my": [
            "route_waypoint",
        ],
        "core_prod_ph": [
            "route_waypoint",
        ],
        "core_prod_sg": [
            "route_waypoint",
        ],
        "core_prod_th": [
            "route_waypoint",
        ],
        "core_prod_vn": [
            "route_waypoint",
        ],
        "driver_prod_gl": [
            "cash_transactions",
            "fcm_tokens",
            "jobs",
            "parcels",
            "routes",
            "waypoints",
        ],
        "events_prod_gl": [
            "drained_events",
        ],
        "route_prod_gl": [
            "rules",
        ],
        "sort_prod_gl": [
            "delete_zone_counter",
            # See https://jira.ninjavan.co/projects/DATA/issues/DATA-633
            "path_changes_trackers",
        ],
        "vault_prod_gl": [
            "vaults",
        ],
        "xb_ops_prod_gl": [
            "invoice_allocated_costs",
        ],
    },
}

DB_MW_REPLICATION = {
    "data-prod-global-data-engr-1-gl": ["aaa_prod_gl", "dash_prod_gl", "loyalty_prod_gl", "shipper_prod_gl"],
    "data-prod-global-data-engr-2-gl": ["3pl_prod_gl", "direct_prod_gl", "sort_prod_gl",
                                        "ticketing_prod_gl", "xb_ops_prod_gl"],
    "data-prod-global-data-engr-3-gl": ["notifications_email_prod_gl", "notifications_prod_gl",
                                        "notifications_sms_prod_gl", "notifications_v2_prod_gl"],
    "data-prod-global-data-engr-4-gl": ["events_prod_gl"],
    "data-prod-global-data-engr-5-gl": ["control_prod_gl", "ocreate_prod_gl"],
    "data-prod-global-data-engr-cx-gl": ["consignee_prod_gl", "overwatch_prod_gl", "sns_prod_gl", "url_shortener_gl"],
    "data-prod-global-data-engr-dp-gl": ["dp_prod_gl"],
    "data-prod-global-data-engr-driver-gl": ["driver_prod_gl"],
    "data-prod-global-data-engr-hub-gl": ["movement_trip_prod_gl", "hub_prod_gl"],
    "data-prod-global-data-engr-id": ["core_prod_id"],
    "data-prod-global-data-engr-misc-gl": ["addressing_prod_gl", "epi_prod_gl", "reports_prod_gl", "wms_prod_gl"],
    "data-prod-global-data-engr-my-mm": ["core_prod_mm", "core_prod_my"],
    "data-prod-global-data-engr-ph": ["core_prod_ph"],
    "data-prod-global-data-engr-route-gl": ["route_prod_gl"],
    "data-prod-global-data-engr-sg-th": ["core_prod_sg", "core_prod_th"],
    "data-prod-global-data-engr-sort-search-gl": ["sort_vendor_prod_gl"],
    "data-prod-global-data-engr-station-gl": ["station_prod_gl"],
    "data-prod-global-data-engr-vn": ["core_prod_vn"]
}

MW_DB_MAPPING = {
    'aaa_prod_gl': 'data-prod-global-data-engr-1-gl',
    'dash_prod_gl': 'data-prod-global-data-engr-1-gl',
    'loyalty_prod_gl': 'data-prod-global-data-engr-1-gl',
    'shipper_prod_gl': 'data-prod-global-data-engr-1-gl',
    '3pl_prod_gl': 'data-prod-global-data-engr-2-gl',
    'direct_prod_gl': 'data-prod-global-data-engr-2-gl',
    'sort_prod_gl': 'data-prod-global-data-engr-2-gl',
    'ticketing_prod_gl': 'data-prod-global-data-engr-2-gl',
    'xb_ops_prod_gl': 'data-prod-global-data-engr-2-gl',
    'notifications_email_prod_gl': 'data-prod-global-data-engr-3-gl',
    'notifications_prod_gl': 'data-prod-global-data-engr-3-gl',
    'notifications_sms_prod_gl': 'data-prod-global-data-engr-3-gl',
    'notifications_v2_prod_gl': 'data-prod-global-data-engr-3-gl',
    'events_prod_gl': 'data-prod-global-data-engr-4-gl',
    'control_prod_gl': 'data-prod-global-data-engr-5-gl',
    'ocreate_prod_gl': 'data-prod-global-data-engr-5-gl',
    'consignee_prod_gl': 'data-prod-global-data-engr-cx-gl',
    'overwatch_prod_gl': 'data-prod-global-data-engr-cx-gl',
    'sns_prod_gl': 'data-prod-global-data-engr-cx-gl',
    'url_shortener_gl': 'data-prod-global-data-engr-cx-gl',
    'dp_prod_gl': 'data-prod-global-data-engr-dp-gl',
    'driver_prod_gl': 'data-prod-global-data-engr-driver-gl',
    'movement_trip_prod_gl': 'data-prod-global-data-engr-hub-gl',
    'hub_prod_gl': 'data-prod-global-data-engr-hub-gl',
    'core_prod_id': 'data-prod-global-data-engr-id',
    'addressing_prod_gl': 'data-prod-global-data-engr-misc-gl',
    'epi_prod_gl': 'data-prod-global-data-engr-misc-gl',
    'reports_prod_gl': 'data-prod-global-data-engr-misc-gl',
    'wms_prod_gl': 'data-prod-global-data-engr-misc-gl',
    'core_prod_mm': 'data-prod-global-data-engr-my-mm',
    'core_prod_my': 'data-prod-global-data-engr-my-mm',
    'core_prod_ph': 'data-prod-global-data-engr-ph',
    'route_prod_gl': 'data-prod-global-data-engr-route-gl',
    'core_prod_sg': 'data-prod-global-data-engr-sg-th',
    'core_prod_th': 'data-prod-global-data-engr-sg-th',
    'sort_vendor_prod_gl': 'data-prod-global-data-engr-sort-search-gl',
    'station_prod_gl': 'data-prod-global-data-engr-station-gl',
    'core_prod_vn': 'data-prod-global-data-engr-vn'
}

MAXWELL_DB_SCHEMAS = {
    'prod': [
        "data_engr_2_gl",
        "data_engr_3_gl",
        "data_engr_1_gl",
        "data_engr_4_gl",
        "data_engr_5_gl",
        "data_engr_cx_gl",
        "data_engr_dp_gl",
        "data_engr_driver_gl",
        "data_engr_hub_gl",
        "data_engr_id",
        "data_engr_misc_gl",
        "data_engr_my_mm",
        "data_engr_ph",
        "data_engr_route_gl",
        "data_engr_sg_th",
      # "data_engr_sort_search_gl",
        "data_engr_station_gl",
        "data_engr_vn"
    ],
    "dev": [
        "data_engr_gl"
    ]
}
