AMAST_CONFIG = {
    'vn': {
        'vw_van_photo_checkin': {
            'filter_column': 'updated_at',
            'primary_keys': ['outlet_id', 'check_in'],
            'partition_column': 'date'
        },
        'van_visits': {'filter_column': 'updated_at', 'primary_keys': ['id'], 'partition_column': 'created_at'},
        'vans': {'filter_column': 'updated_at', 'primary_keys': ['id']},
        'sites': {'filter_column': 'updated_at', 'primary_keys': ['id']},
        'outlets': {'filter_column': 'updated_at', 'primary_keys': ['id']},
        'lovs': {'filter_column': 'updated_at', 'primary_keys': ['id', 'uuid', 'sequence']},
        'outlet_conversion': {'filter_column': 'created_date', 'primary_keys': ['outlet_id']},
        'outlet_listings' : {'filter_column': 'updated_at', 'primary_keys': ['outlet_id'],'partition_column': 'created_at'},
    },
    'my': {
        "deal_history": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at",
            "rate": True
        },
        "inventory_daily_snapshot": {
            "filter_column": "updated_at",
            "primary_keys": ["site_id", "date"],
            "partition_column": "created_at",
            "rate": True
        },
        "rmo_payment_logs": {
            "filter_column": "updated_at",
            "primary_keys": ["rmo_id", "inv_date", "invoice_id"],
            "partition_column": "created_at",
            "rate": True
        },
        "key_accounts": { "filter_column": "updated_at", "primary_keys": ["id"],
            "rate": True },
        "outlet_contacts": {
            "filter_column": "updated_at",
            "primary_keys": ["outlet_id", "contact_id"],
            "partition_column": "created_at",
            "rate": True
        },
        "user_sites": {
            "filter_column": "updated_at",
            "primary_keys": ["user_id", "site_id"],
            "rate": True
        },
        "invoices": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "invoice_details": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "invoice_bundle_details": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "void_invoices": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "credit_notes": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "credit_note_details": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "outlets": { "filter_column": "updated_at", "primary_keys": ["id"] },
        "vans": { "filter_column": "updated_at", "primary_keys": ["id"] },
        "printouts": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "skus": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
        "sku_uom_conversions": {
            "filter_column": "updated_at",
            "primary_keys": ["sku_id", "from_uom_id", "to_uom_id"],
            "partition_column": "created_at"
        },
        "sku_uoms": {
            "filter_column": "updated_at",
            "primary_keys": ["sku_id", "uom_id"],
            "partition_column": "created_at"
        },
        "outlet_credit": {
            "filter_column": "updated_at",
            "primary_keys": ["outlet_id"],
            "partition_column": "created_at"
        },
        "lovs": {
            "filter_column": "updated_at",
            "primary_keys": ["id", "uuid", "sequence"]
        },
        "principals": { "filter_column": "updated_at", "primary_keys": ["id"] },
        "van_visits": {
            "filter_column": "updated_at",
            "primary_keys": ["id"],
            "partition_column": "created_at"
        },
         "settlement": {
             "filter_column": "updated_at",
             "primary_keys": ["id"],
             "partition_column": "created_at"
         },
        "stock_count": {
            "filter_column": "updated_at",
            "primary_keys": ["id", "date", "outlet_id", "sku_id"],
            "partition_column": "created_at"
        },
         "rmos": {
             "filter_column": "updated_at",
             "primary_keys": ["id"],
             "partition_column": "created_at"
         },
         "van_stock_daily_snapshot": {
             "filter_column": "updated_at",
             "primary_keys": ["id", "van_id", "date", "sku_id", "uom_id", "condition"],
             "partition_column": "created_at"
         },
         "van_stock": {
             "filter_column": "updated_at",
             "primary_keys": ["van_id", "sku_id", "uom_id", "condition"],
             "partition_column": "created_at"
         },
         "van_requests": {
             "filter_column": "updated_at",
             "primary_keys": ["id"],
             "partition_column": "created_at"
         },
         "van_request_details": {
             "filter_column": "updated_at",
             "primary_keys": ["van_req_id", "uom_id", "sku_id"],
             "partition_column": "created_at"
         },
         "inventory_daily_log": {
             "filter_column": "updated_at",
             "primary_keys": ["site_id", "date", "sku_id", "uom_id", "condition"],
             "partition_column": "created_at"
         },
         "inventory": {
             "filter_column": "updated_at",
             "primary_keys": ["site_id", "sku_id", "uom_id", "condition"],
             "partition_column": "created_at"
         },
        'outlet_listings' : {'filter_column': 'updated_at', 'primary_keys': ['outlet_id'],'partition_column': 'created_at'},
    }
}
