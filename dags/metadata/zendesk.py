ZENDESK_CONFIG = {
    "brands": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/{entity}.json",
        "schema": {
            "active": "bool",
            "brand_url": "unicode",
            "created_at": "datetime64",
            "default": "bool",
            "has_help_center": "bool",
            "help_center_state": "unicode",
            "host_mapping": "unicode",
            "id": "Int64",
            "is_deleted": "bool",
            "logo": "unicode",
            "name": "unicode",
            "signature_template": "unicode",
            "subdomain": "unicode",
            "ticket_form_ids": "object",
            "updated_at": "datetime64",
            "url": "unicode",
        },
        "nested_columns": {"ticket_form_ids"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "all",
    },
    "groups": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/{entity}.json",
        "schema": {
            "created_at": "datetime64",
            "default": "bool",
            "deleted": "bool",
            "description": "unicode",
            "id": "Int64",
            "is_public": "bool",
            "name": "unicode",
            "updated_at": "datetime64",
            "url": "unicode",
        },
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "all",
    },
    "organizations": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/incremental/{entity}.json",
        "schema": {
            "created_at": "datetime64",
            "deleted_at": "datetime64",
            "details": "unicode",
            "domain_names": "object",
            "external_id": "unicode",
            "group_id": "Int64",
            "id": "Int64",
            "name": "unicode",
            "notes": "unicode",
            "organization_fields": "object",
            "shared_comments": "bool",
            "shared_tickets": "bool",
            "tags": "object",
            "updated_at": "datetime64",
            "url": "unicode",
        },
        "nested_columns": {"domain_names", "organization_fields", "tags"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "incremental",
    },
    "satisfaction_ratings": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/{entity}.json",
        "schema": {
            "assignee_id": "Int64",
            "comment": "unicode",
            "created_at": "datetime64",
            "group_id": "Int64",
            "id": "Int64",
            "reason": "unicode",
            "reason_id": "Int64",
            "requester_id": "Int64",
            "score": "unicode",
            "ticket_id": "Int64",
            "updated_at": "datetime64",
            "url": "unicode",
        },
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "incremental",
    },
    "ticket_fields": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/{entity}.json",
        "schema": {
            "active": "bool",
            "agent_description": "unicode",
            "collapsed_for_agents": "bool",
            "created_at": "datetime64",
            "custom_field_options": "object",
            "custom_statuses": "object",
            "description": "unicode",
            "editable_in_portal": "bool",
            "id": "Int64",
            "key": "unicode",
            "position": "Int64",
            "raw_description": "unicode",
            "raw_title": "unicode",
            "raw_title_in_portal": "unicode",
            "regexp_for_validation": "unicode",
            "removable": "bool",
            "required": "bool",
            "required_in_portal": "bool",
            "sub_type_id": "Int64",
            "system_field_options": "unicode",
            "tag": "unicode",
            "title": "unicode",
            "title_in_portal": "unicode",
            "type": "unicode",
            "updated_at": "datetime64",
            "url": "unicode",
            "visible_in_portal": "bool",
        },
        "nested_columns": {"custom_field_options"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "all",
    },
    "ticket_forms": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/{entity}.json",
        "schema": {
            "active": "bool",
            "agent_conditions": "object",
            "created_at": "datetime64",
            "default": "bool",
            "display_name": "unicode",
            "end_user_conditions": "object",
            "end_user_visible": "bool",
            "id": "Int64",
            "name": "unicode",
            "position": "Int64",
            "raw_display_name": "unicode",
            "raw_name": "unicode",
            "ticket_field_ids": "object",
            "updated_at": "datetime64",
            "url": "unicode",
        },
        "nested_columns": {"agent_conditions", "end_user_conditions", "ticket_field_ids"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "all",
    },
    "ticket_metric_events": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/incremental/{entity}.json",
        "schema": {
            "deleted": "bool",
            "id": "Int64",
            "instance_id": "Int64",
            "metric": "unicode",
            "sla": "object",
            "status": "object",
            "ticket_id": "Int64",
            "time": "datetime64",
            "type": "unicode",
        },
        "nested_columns": {"sla", "status"},
        "created_at_field": "time",
        "updated_at_field": "time",
        "fetch_type": "incremental",
    },
    "tickets": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/incremental/{entity}.json",
        "schema": {
            "allow_attachments": "bool",
            "allow_channelback": "bool",
            "assignee_id": "Int64",
            "brand_id": "Int64",
            "collaborator_ids": "object",
            "created_at": "datetime64",
            "custom_fields": "object",
            "custom_status_id": "Int64",
            "deleted_ticket_form_id": "Int64",
            "description": "unicode",
            "due_at": "datetime64",
            "email_cc_ids": "object",
            "external_id": "Int64",
            "fields": "object",
            "follower_ids": "object",
            "followup_ids": "object",
            "forum_topic_id": "Int64",
            "from_messaging_channel": "bool",
            "generated_timestamp": "datetime64[s]",
            "group_id": "Int64",
            "has_incidents": "bool",
            "id": "Int64",
            "is_public": "bool",
            "organization_id": "Int64",
            "priority": "unicode",
            "problem_id": "Int64",
            "raw_subject": "unicode",
            "recipient": "unicode",
            "requester_id": "Int64",
            "satisfaction_rating": "object",
            "sharing_agreement_ids": "object",
            "status": "unicode",
            "subject": "unicode",
            "submitter_id": "Int64",
            "tags": "object",
            "ticket_form_id": "Int64",
            "type": "unicode",
            "updated_at": "datetime64",
            "url": "unicode",
            "via": "object",
        },
        "nested_columns": {
            "collaborator_ids",
            "custom_fields",
            "email_cc_ids",
            "fields",
            "follower_ids",
            "followup_ids",
            "satisfaction_rating",
            "sharing_agreement_ids",
            "tags",
            "via",
        },
        "created_at_field": "created_at",
        "updated_at_field": "generated_timestamp",
        "fetch_type": "incremental",
    },
    "users": {
        "system_ids": ("sg", "my", "id", "vn", "th", "ph"),
        "path": "/api/v2/incremental/{entity}.json",
        "schema": {
            "active": "bool",
            "alias": "unicode",
            "chat_only": "bool",
            "created_at": "datetime64",
            "custom_role_id": "Int64",
            "default_group_id": "Int64",
            "details": "unicode",
            "email": "unicode",
            "external_id": "unicode",
            "iana_time_zone": "unicode",
            "id": "Int64",
            "last_login_at": "datetime64",
            "locale": "unicode",
            "locale_id": "Int64",
            "moderator": "bool",
            "name": "unicode",
            "notes": "unicode",
            "only_private_comments": "bool",
            "organization_id": "Int64",
            "permanently_deleted": "bool",
            "phone": "unicode",
            "photo": "unicode",
            "report_csv": "bool",
            "restricted_agent": "bool",
            "role": "unicode",
            "role_type": "Int64",
            "shared": "bool",
            "shared_agent": "bool",
            "shared_phone_number": "bool",
            "signature": "unicode",
            "suspended": "bool",
            "tags": "object",
            "ticket_restriction": "unicode",
            "time_zone": "unicode",
            "two_factor_auth_enabled": "bool",
            "updated_at": "datetime64",
            "url": "unicode",
            "user_fields": "object",
            "verified": "bool",
        },
        "nested_columns": {"tags", "user_fields"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "fetch_type": "incremental",
    },
}