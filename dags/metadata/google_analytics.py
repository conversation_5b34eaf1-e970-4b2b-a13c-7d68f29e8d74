GOOGLE_ANALYTICS_MAX_RESULTS_LIMIT = int(1e5)
GA_PROPERTIES_MAP = {
    "my": "346902929",
    "sg": "347528399",
    "vn": "347897719",
    "th": "347940156",
    "id": "347955495",
    "ph": "347955610",
    "mm": "348030583",
}
GA_CONFIG = {
    "metrics": {
        "dimensions": {
            "date",
            "deviceCategory",
            "eventName",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
            "conversions",
        },
        "filter": True,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "conversions": "float",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
    "source_medium": {
        "dimensions": {
            "date",
            "deviceCategory",
            "eventName",
            "sessionSourceMedium",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
        },
        "filter": True,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "session_source_medium": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
    "channel": {
        "dimensions": {
            "date",
            "deviceCategory",
            "eventName",
            "sessionDefaultChannelGroup",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
        },
        "filter": False,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "session_default_channel_group": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
    "user_type": {
        "dimensions": {
            "date",
            "deviceCategory",
            "eventName",
            "newVsReturning",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
        },
        "filter": True,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "new_vs_returning": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
    "landing_page": {
        "dimensions": {
            "date",
            "deviceCategory",
            "eventName",
            "landingPage",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
        },
        "filter": True,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "landing_page": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
    "country": {
        "dimensions": {
            "date",
            "country",
            "deviceCategory",
            "eventName",
        },
        "metrics": {
            "totalUsers",
            "averageSessionDuration",
            "bounceRate",
            "sessions",
            "newUsers",
            "screenPageViewsPerSession",
            "engagementRate",
            "screenPageViews",
        },
        "filter": True,
        "schema": {
            "date": "datetime64",
            "device_category": "str",
            "event_name": "str",
            "country": "str",
            "total_users": "int",
            "average_session_duration": "float",
            "bounce_rate": "float",
            "sessions": "int",
            "new_users": "int",
            "screen_page_views_per_session": "float",
            "engagement_rate": "float",
            "screen_page_views": "int",
            "website_id": "str",
            "system_id": "str",
        },
        "time_fields": ["date"],
        "updated_at_field": "date",
    },
}
