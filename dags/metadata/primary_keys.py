# Define primary keys for different tables
PRIMARY_KEYS = {
  "automation.jira_ts_tickets": [
    "key"
  ],
  "direct_prod_gl.request_order_logistic_lms": [
    "request_order_id"
  ],
  "direct_prod_gl.request_order_logistic_xbs": [
    "request_order_id"
  ],
  "sort_prod_gl.inbound_parcel_images": [
    "system_id",
    "barcode"
  ],
  "sns_prod_gl.sns_audits": [
    "id",
    "created_at"
  ],
  "notifications_sms_prod_gl.sms_audits": [
    "id",
    "created_at"
  ],
  "notifications_email_prod_gl.email_audits": [
    "id",
    "created_at"
  ],
  "notifications_voice_prod_gl.callout_requests": [
    "id",
    "created_at"
  ],
  "notifications_voice_prod_gl.callout_jobs": [
    "id",
    "created_at"
  ],
  "notifications_voice_prod_gl.vonage_event_webhook_logs": [
    "created_at"
  ],
  "notifications_prod_gl.webhook_history": [],
  "dash_prod_gl.shipper_address_alias": [
    "user_id",
    "address_id"
  ],
  "sort_vendor_prod_gl.inbound_parcel_images": [
    "system_id",
    "barcode"
  ],
  "sort_vendor_prod_gl.vendors": [
    "user_id"
  ],
  "sort_vendor_prod_gl.vendors_device_access": [
    "user_id",
    "device_id"
  ],
  "sort_vendor_prod_gl.vendors_system_access": [
    "user_id",
    "system_id"
  ],
  "pod_validation_prod_gl.assignments": [
    "task_id",
    "user_id"
  ],
  "pod_validation_prod_gl.task_shippers": [
    "task_id"
  ],
  "dash_prod_gl.tokens_metadata": [
    "token"
  ],
  "sort_vendor_prod_gl.parcel_measurement_scan": [
    "id",
    "partition_date"
  ],
  "fdm_prod_gl.delivery_failures": [
    "id",
    "created_at"
  ],
  "fdm_prod_gl.delivery_failure_counts": [
    "id",
    "created_at"
  ],
  "pricing_prod_gl.pricing_orders_history": [
    "id",
    "trigger_time"
  ],
  "dash_prod_gl.shipper_panel_users": [],
  "dash_prod_gl.dvt_orders": [],
  "control_prod_gl.pickup_appointment_jobs_search_pickup_tags": [],
  "billing_prod_gl.journals": [
    "id",
    "created_at"
  ],
  "station_prod_gl.collections": [
    "id",
    "job_service_end_date_local"
  ],
  "core_prod_sg.parcel_sizes": [],
  "billing_prod_gl.transactions": [
    "id",
    "transaction_local_date"
  ],
  "billing_prod_gl.ledgers": [
    "id",
    "created_local_date"
  ],
  "billing_prod_gl.cod_orders": [
    "id",
    "completed_local_date"
  ],
  "billing_prod_gl.invoiced_orders": [
    "id",
    "invoiced_local_date"
  ],
  "billing_prod_gl.priced_orders": [
    "id",
    "completed_local_date"
  ],
  "sort_mistake_prod_gl.sort_nodes_compat_map": [
    "new_node_id"
  ],
  "dash_prod_gl.verifications": [],
  "dash_prod_gl.draft_orders": [],
  "control_prod_gl.pickup_appointment_jobs_search": [],
  "billing_prod_gl.ledger_orders": [
    "id",
    "created_at"
  ],
  "billing_prod_gl.picked_up_orders": [
    "id",
    "pickup_time"
  ],
  "sort_vendor_prod_gl.parcel_measurement_v2": [],
  "automation.qa_stf_devices": [
    "udid"
  ],
  "loyalty_prod_gl.merchants": [
    "merchant_phone"
  ],
  "loyalty_prod_gl.orders": [
    "tracking_number"
  ],
  "route_prod_gl.waypoint_photos_testing": [
    "legacy_id",
    "system_id"
  ],
  "sort_vendor_prod_gl.parcel_images_v2": [
    "id",
    "partition_date"
  ],
  "ninjamart_order.sales_person_info": [
    "sales_person_id"
  ],
  "direct_supplier_prod_gl.supplier_payment_terms": [
    "supplier_id",
    "payment_term_id"
  ],
  "direct_supplier_prod_gl.supplier_main_markets": [
    "supplier_id",
    "main_market_id"
  ],
  "movement_trip_prod_gl.schema_version_datafixes": [
    "installed_rank"
  ],
  "movement_trip_prod_gl.schema_version": [
    "installed_rank"
  ],
  "direct_supplier_prod_gl.schema_version_datafixes": [
    "installed_rank"
  ],
  "direct_supplier_prod_gl.schema_version": [
    "installed_rank"
  ],
  "direct_supplier_prod_gl.categories": [
    "category_id"
  ],
  "first_mile_prod_gl.rollover_order_job_mappings": [
    "id",
    "created_at"
  ],
  "address_appraiser_prod_gl.predicted_order_coordinates": [
    "system_id",
    "order_id",
    "created_at"
  ],
  "driver_prod_gl.failure_reason_requirements": [
    "name"
  ],
  "fdm_prod_gl.order_hubs": [
    "id",
    "created_at"
  ],
  "fdm_prod_gl.invalid_delivery_failure_counts": [
    "id",
    "created_at"
  ],
  "first_mile_prod_gl.pickup_receipts": [
    "id",
    "created_at"
  ],
  "first_mile_prod_gl.pickup_attempt_orders": [
    "id",
    "created_at"
  ],
  "first_mile_prod_gl.similar_addresses": [
    "id",
    "system_id"
  ],
  "first_mile_prod_gl.async_requests": [
    "id",
    "created_at"
  ],
  "first_mile_prod_gl.async_sub_requests": [
    "id",
    "created_at"
  ],
  "webhook_history_prod_gl.mmcc_webhook_history": [
    "uuid",
    "logged_at",
    "big_bag_no",
    "master_awb",
    "log_type",
    "webhook_request_url"
  ],
  "xb_ops_prod_gl.invoice_allocated_costs": []
}