# These schemas have been migrated to the CDC pipeline where PII fields are obfuscated.
EMAIL_FIELD_MARK = "EMAIL_FIELD_MARK"
EMAIL_FIELDS = {
    "core_prod_id": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_mm": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_my": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_ph": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_sg": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_th": {
        "orders": ["from_email", "to_email"],
    },
    "core_prod_vn": {
        "orders": ["from_email", "to_email"],
    },
    "ticketing_prod_gl": {
        "users": ["email"],
    },
}
SCHEMAS = {
    "dev": ["route_qa_gl", "assets_proxy_qa_gl"],
    "prod": [
        "3pl_prod_gl",
        "3pl_oms_prod_gl",
        "aaa_prod_gl",
        "address_appraiser_prod_gl",
        "addressing_prod_gl",
        "argus_prod_gl",
        "billing_prod_gl",
        "cod_prod_gl",
        "consignee_prod_gl",
        "control_prod_gl",
        "core_prod_id",
        "core_prod_mm",
        "core_prod_my",
        "core_prod_ph",
        "core_prod_sg",
        "core_prod_th",
        "core_prod_vn",
        "notifications_voice_prod_gl",
        "dash_prod_gl",
        "direct_prod_gl",
        "direct_supplier_prod_gl",
        "dp_prod_gl",
        "driver_prod_gl",
        "driver_vantage_prod_gl",
        "epi_prod_gl",
        "events_prod_gl",
        "fdm_prod_gl",
        "first_mile_prod_gl",
        "goals_prod_gl",
        "hub_prod_gl",
        "hub_usrs_prod_gl",
        "loyalty_prod_gl",
        "mart_promotion_prod_gl",
        "movement_trip_prod_gl",
        "notifications_email_prod_gl",
        "notifications_prod_gl",
        "notifications_sms_prod_gl",
        "notifications_v2_prod_gl",
        "ocreate_prod_gl",
        "overwatch_prod_gl",
        "pa_job_search_prod_gl",
        "pod_validation_prod_gl",
        "pricing_prod_gl",
        "route_prod_gl",
        "script_engine_prod_gl",
        "sda_prod_gl",
        "shipper_prod_gl",
        "mart_sp_mgmt_prod_gl",
        "sns_prod_gl",
        "sort_mistake_prod_gl",
        "sort_prod_gl",
        "sort_vendor_prod_gl",
        "station_prod_gl",
        "sns_platforms_prod_gl",
        "ticketing_prod_gl",
        "url_shortener_gl",
        "wallet_prod_gl",
        "wms_prod_gl",
        "xb_operations_prod_gl",
        "developer_support_automation_prod_gl",
        "webhook_receiver_prod_gl",
        "mart_ecommerce_prod_gl",
    ],
}
