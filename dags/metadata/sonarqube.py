SONARQUBE_CONFIG = {
    "measures": {
        "path": "measures/search_history",
        "schema": {
            "id": "object",
            "project": "object",
            "coverage": "float64",
            "bugs": "float64",
            "duplicated_lines_density": "float64",
            "ncloc": "float64",
            "ncloc_language_distribution": "object",
            "sqale_rating": "float64",
            "security_hotspots_reviewed": "float64",
            "code_smells": "float64",
            "vulnerabilities": "float64",
            "reliability_rating": "float64",
            "security_rating": "float64",
            "security_review_rating": "float64",
            "alert_status": "object",
            "complexity": "float64",
            "cognitive_complexity": "float64",
            "created_at": "datetime64",
        },
        "non_metric_fields": ["id", "project", "created_at"],
        "updated_at_field": "created_at",  # this table is append only
    }
}
