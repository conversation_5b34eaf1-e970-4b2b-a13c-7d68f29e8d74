from metadata import data_warehouse

GSUITE_CONFIG = {
    "users": {
        "method": "search",
        "schema": {
            "primary_email": "str",
            "is_enrolled_in_2_sv": "bool",
            "is_enforced_in_2_sv": "bool",
            "creation_time": "datetime64",
        },
        "dwh_key_column": "cif_email",
        "response_key_column": "primary_email",
        "data_key": "emails",
        "updated_at_field": "updated_at",
        "created_at_field": "creation_time",
        "dwh_dag_id": data_warehouse.FleetDAG.DAG_ID,
        "dwh_task_name": data_warehouse.FleetDAG.Task.DRIVER_CIF_MIGRATION_REPORT_MASKED,
        "primary_column": "driver_id",
    },
}
