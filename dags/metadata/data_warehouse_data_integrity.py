from metadata.data_warehouse import (
    <PERSON><PERSON><PERSON>,
    ActiveOrdersDAG,
    CalendarDAG,
    <PERSON>berDAG,
    FleetDAG,
    HubsDAG,
    JiraDAG,
    JotformDAG,
    MiddleMileDAG,
    OrderEventsDAG,
    OrdersDAG,
    OrderSLADAG,
    OrderTagsDAG,
    RecoveryDAG,
    SalesDAG,
    SalesforceDAG,
    ShippersDAG,
    ZendeskDAG,
)

DATA_WAREHOUSE_TABLES = [
    ActiveOrdersDAG.Task.ACTIVE_ORDERS,
    CalendarDAG.Task.CALENDAR,
    CalendarDAG.Task.CALENDAR_RECOVERY,
    DPDAG.Task.DPS_ENRICHED,
    DPDAG.Task.DP_PARTNERS_ENRICHED,
    DPDAG.Task.DP_SHIPPER_VOL_DAILY,
    DPDAG.Task.DP_RESERVATIONS_ENRICHED,
    DPDAG.Task.DP_OPERATING_HOURS_ENRICHED,
    EberDAG.Task.NINJA_REWARDS_MONTHLY,
    EberDAG.Task.NINJA_REWARDS_USERS,
    EberDAG.Task.NINJA_REWARDS_USERS_BASE,
    FleetDAG.Task.CISP_COMPLETION_REPORT,
    FleetDAG.Task.CISP_COMPLETION_REPORT_DAILY,
    FleetDAG.Task.CISP_REPORT_BASE,
    FleetDAG.Task.CISP_TERMINAL_STATUS_REPORT,
    FleetDAG.Task.CISP_TERMINAL_STATUS_REPORT_DAILY,
    FleetDAG.Task.DRIVER_TYPES_ENRICHED,
    FleetDAG.Task.DRIVERS_ENRICHED,
    FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA,
    FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS,
    FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT,
    FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_COURIER_REPORT,
    FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_REPORT,
    FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT,
    FleetDAG.Task.RESERVATIONS_ENRICHED,
    HubsDAG.Task.DIM_WEIGHT_SCANS,
    HubsDAG.Task.DIM_WEIGHT_SCANS_BASE,
    HubsDAG.Task.HUB_RELATION_SCHEDULES_ENRICHED,
    HubsDAG.Task.HUB_SWEEP_REPORT,
    HubsDAG.Task.HUBS_ENRICHED,
    HubsDAG.Task.SCAN_RESULT_ENRICHED,
    JiraDAG.Task.JIRA_ISSUE_EVENTS,
    JiraDAG.Task.JIRA_ISSUES_ENRICHED,
    JiraDAG.Task.JIRA_SPRINT_ISSUES,
    JiraDAG.Task.JIRA_SPRINTS,
    JotformDAG.Task.ENRICHED_NPS_DATA,
    MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED,
    MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES,
    MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED,
    MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_BASE,
    MiddleMileDAG.Task.SHIPMENT_PATHS,
    MiddleMileDAG.Task.SHIPMENTS_ENRICHED,
    OrderEventsDAG.Task.ORDER_MOVEMENTS,
    OrderSLADAG.Task.SERVICE_BREACH_REPORT,
    OrderSLADAG.Task.SHIPPER_CLAIMS_REPORT,
    OrderSLADAG.Task.SHIPPER_SLA_DAYS,
    OrderSLADAG.Task.SLA_EXTENSIONS,
    OrderSLADAG.Task.SLA_REPORTS_BASE,
    OrderSLADAG.Task.TRANSIT_TIME_REPORT,
    OrderSLADAG.Task.TRANSIT_TIME_SPEED_REPORT,
    OrdersDAG.Task.ORDER_DELIVERIES,
    OrdersDAG.Task.ORDER_DESTINATIONS,
    OrdersDAG.Task.ORDER_DP_MILESTONES,
    OrdersDAG.Task.ORDER_FORCE_SUCCESSES,
    OrdersDAG.Task.ORDER_INBOUNDS,
    OrdersDAG.Task.ORDER_MILESTONES,
    OrdersDAG.Task.ORDER_PICKUPS,
    OrdersDAG.Task.ORDER_RTS_TRIGGER_LOCATIONS,
    OrdersDAG.Task.ORDER_RTS_TRIGGERS,
    OrdersDAG.Task.ORDER_THIRD_PARTY_TRANSFERS,
    OrdersDAG.Task.ORDERS_ENRICHED,
    OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED,
    OrdersDAG.Task.XB_OUTBOUND_ORDERS,
    OrderTagsDAG.Task.ORDER_TAGS_ENRICHED,
    RecoveryDAG.Task.EXCLUSION_REQUESTS,
    RecoveryDAG.Task.EXCLUSION_REQUEST_DETAILS,
    RecoveryDAG.Task.PETS_TICKETS_ENRICHED,
    RecoveryDAG.Task.PETS_TICKETS_RESOLVED_DAILY,
    SalesDAG.Task.SALESPERSONS_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_ACTIVITY_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED,
    SalesforceDAG.Task.SALESFORCE_USER_ENRICHED,
    ShippersDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES_BASE,
    ShippersDAG.Task.SHIPPER_ATTRIBUTES,
    ShippersDAG.Task.SHIPPER_LIFETIME_VALUES_BASE,
    ShippersDAG.Task.SHIPPER_MILESTONES,
    ShippersDAG.Task.SHIPPERS_ENRICHED,
    ZendeskDAG.Task.ZENDESK_TICKETS_ENRICHED,
]

# Todo: Add ShipperLifetimeValueDAG.Task.SHIPPER_LIFETIME_VALUES and
#  ShipperLifetimeValueDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES back after modifying the code to find the correct
#  table paths (those tables dont have measurement_datetime=latest)
