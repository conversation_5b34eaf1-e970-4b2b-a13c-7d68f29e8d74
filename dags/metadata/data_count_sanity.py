DATA_COUNT_TABLES = {
    "core_prod_id": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_vn": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_ph": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_sg": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_my": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "order_sla": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_mm": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "core_prod_th": {
        "transactions": "",
        "inbound_scans": "",
        "blobs": "",
        "reservations": "",
        "cods": "",
        "orders": "",
        "order_details": "",
        "delivery_types": "",
        "salespersons": "",
        "third_party_shippers": "",
        "warehouse_sweeps": "",
        "transaction_blob": ""
    },
    "route_prod_gl": {
        "route_logs": "",
        "route_tags": "",
        "tags": ""
    },
    "sort_prod_gl": {
        "hubs": "",
        "dummy_hub_map": "",
        "regions": ""
    },
    "driver_prod_gl": {
        "drivers": "",
        "driver_types": "",
        "failure_reasons": ""
    },
    "events_prod_gl": {
        "order_events": ""
    },
    "hub_prod_gl": {
        "scans": "",
        "shipments": "",
        "shipment_orders": "",
        "shipment_events": ""
    },
    "dp_prod_gl": {
        "dps": "",
        "dp_reservations": "",
        "partners": ""
    },
    "epi_prod_gl": {
        "external_xdock_order_mappings": ""
    },
    "shipper_prod_gl": {
        "shippers": "",
        "marketplace_sellers": ""
    },
    "xb_operations_prod_gl": {
        "export_job_items": "",
        "transits_tab": "",
        "warehouses_tab": "",
        "export_jobs": "",
        "transits_bags_tab": ""
    },
    "3pl_prod_gl": {
        "events": "",
        "parcels": "",
        "parcel_items": "",
        "shippers": "",
        "shipment_parcels": "",
        "audit_logs": "",
        "internal_statuses": "",
        "products": "",
        "services": "",
        "shipments": "",
        "vendors": ""
    },
    "control_prod_gl": {
        "pickup_appointment_jobs": "",
        "proof_jobs": "",
        "proof_reservations": "",
        "proof_tracking_ids": "",
        "proof_transactions": "",
        "proofs": ""
    },
    "addressing_prod_gl": {
        "zones": ""
    },
}
