SFMC_TIME_FORMAT = "M/D/YYYY h:mm:ss A"
SFMC_CONFIG = {
    "campaign": {
        "customer_key": "57C32EAD-35A4-495B-89D7-E4076005537A",
        "schema": {
            "mobile_device_opt_out_status": "object",
            "open_date_time": "object",
            "from_name": "object",
            "triggeredsendcustomerkey": "object",
            "mobile_device_opt_in_status": "object",
            "domain": "object",
            "email_subscriber_status": "object",
            "sales_channel": "object",
            "jobid": "object",
            "zalo_id": "object",
            "source_system_object": "object",
            "email_name": "object",
            "subscriber_key": "object",
            "emailsubject": "object",
            "bounce_category": "object",
            "sent_date_time": "object",
            "bounce_date_time": "object",
            "shopify_id": "object",
            "sf_contact_id": "object",
            "unsubscribe_date_time": "object",
            "system_id": "object",
            "shipper_id": "object",
            "zalo_id_opt_in_status": "object",
            "email_engagement_persona": "object",
            "shipper_tier": "object",
            "mobile_device_id": "object",
            "from_email": "object",
            "email_open_likelihood": "object",
            "source_system": "object",
            "sf_account_id": "object",
            "lifecycle": "object",
            "zalo_id_opt_out_status": "object",
            "email_click_likelihood": "object",
            "click_date_time": "object",
            "bouncetype": "object",
            "updated_at": "object",
            "created_date": "object"
        },
        "time_fields": [
            "open_date_time",
            "sent_date_time",
            "bounce_date_time",
            "unsubscribe_date_time",
            "click_date_time",
            "updated_at",
            "created_date"
        ],
        "time_format": "M/D/YYYY h:mm:ss A",
        "primary_keys": [
            "subscriber_key",
            "email_name",
            "sent_date_time"
        ],
        "created_at_field": "sent_date_time"
    },
    "mobile_push_demographics": {
        "customer_key": "6F3B61FA-BAAB-4A1A-BC6E-BD9ECD134738",
        "schema": {
            "platform_version": "object",
            "utcoffset": "object",
            "state": "object",
            "shipper_flow": "object",
            "device_type": "object",
            "badge": "object",
            "first_name": "object",
            "modified_date": "datetime64",
            "alias": "object",
            "created_by": "object",
            "contact_id": "object",
            "apid": "object",
            "opt_in_status_id": "object",
            "opt_in_method_id": "object",
            "time_zone": "object",
            "city": "object",
            "zip_code": "object",
            "channel": "object",
            "opt_in_date": "datetime64",
            "source_object_id": "object",
            "shipper_name": "object",
            "modified_by": "object",
            "last_name": "object",
            "country": "object",
            "hardware_id": "object",
            "is_honor_dst": "object",
            "system_token": "object",
            "opt_out_date": "datetime64",
            "opt_out_method_id": "object",
            "source": "object",
            "phone": "object",
            "created_date": "datetime64",
            "sf_contact_id": "object",
            "provider_token": "object",
            "platform": "object",
            "device": "object",
            "status": "object",
            "dwh_shipper_id": "object",
            "shipper_id": "object",
            "email": "object",
            "location_enabled": "object",
            "device_id": "object",
            "opt_out_status_id": "object",
            "locale": "object",
            "updated_at": "object",
        },
        "time_fields": ("modified_date", "opt_in_date", "opt_out_date", "created_date", "updated_at"),
        "time_format": "M/D/YYYY h:mm:ss A",
        "primary_keys": ("contact_id",),
        "created_at_field": "created_date",
    },
    "mobile_push_statistics": {
        "customer_key": "EF19796A-AD0C-4BC2-B446-7570974890F4",
        "schema": {
            "app_name": "object",
            "message_name": "object",
            "message_id": "object",
            "template": "object",
            "format": "object",
            "campaigns": "object",
            "device_id": "object",
            "subscriber_key": "object",
            "date_time_send": "datetime64",
            "message_content": "object",
            "message_opened": "object",
            "open_date": "datetime64",
            "time_in_app": "object",
            "platform": "object",
            "platform_version": "object",
            "status": "object",
            "push_job_id": "object",
            "system_token": "object",
            "ios_media_url": "object",
            "android_media_url": "object",
            "updated_at": "object",
        },
        "time_fields": ("date_time_send", "open_date", "updated_at"),
        "time_format": "M/D/YYYY h:mm:ss A",
        "primary_keys": (
            "subscriber_key",
            "message_name",
            "date_time_send",
            "device_id",
        ),
        "created_at_field": "date_time_send",
    },
    "mobile_push_address": {
        "customer_key": "16B7BE25-CEBF-41E2-9FB2-CE6EEC8D74A7",
        "schema": {
            "contact_id": "object",
            "device_id": "object",
            "apid": "object",
            "status": "object",
            "source": "object",
            "platform": "object",
            "platform_version": "object",
            "opt_out_status_id": "object",
            "opt_out_method_id": "object",
            "opt_in_status_id": "object",
            "opt_in_method_id": "object",
            "channel": "object",
            "system_token": "object",
            "provider_token": "object",
            "badge": "object",
            "location_enabled": "object",
            "timezone": "object",
            "device": "object",
            "hardware_id": "object",
            "device_type": "object",
            "email": "object",
            "phone": "object",
            "country": "object",
            "shipper_flow": "object",
            "shipper_name": "object",
            "shipper_id": "object",
            "locale": "object",
            "created_date": "datetime64",
            "modified_date": "datetime64",
            "opt_out_date": "datetime64",
            "opt_in_date": "datetime64",
            "updated_at": "object",
        },
        "time_fields": ("created_date", "modified_date", "opt_out_date", "opt_in_date", "updated_at"),
        "time_format": "M/D/YYYY h:mm:ss A",
        "primary_keys": ("contact_id",),
        "created_at_field": "created_date",
    },
}
