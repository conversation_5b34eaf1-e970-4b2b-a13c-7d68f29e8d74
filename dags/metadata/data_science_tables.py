from metadata.constants import DATASCIENCE_BASE_URI


class AddressVerificationMY:
    def __init__(self, env="prod"):
        base_prefix = "address_verification/my"
        bucket_name = f"nv-data-{env}-datascience"
        self.ACCURACY_FOLDER = f"gs://{bucket_name}/{base_prefix}/accuracies"


class FM_SIMILAR_ADDRESS_GROUPING:
    def __init__(self, env="prod"):
        self._URI_BASE = f"{DATASCIENCE_BASE_URI.format(env)}"
        self._PROJECT_BASE = "fm_similar_address_grouping"
        self.INTERMEDIATE_TABLES = f"{self._URI_BASE}/{self._PROJECT_BASE}/inter_tables"
        self.PROCESSED_DATALAKE = f"{self._URI_BASE}/{self._PROJECT_BASE}/datalake"
        self.SIMILAR_ADDRESSES = f"{self._URI_BASE}/{self._PROJECT_BASE}/similar_addresses"
        self.SOURCE_ADDRESSES = f"{self._URI_BASE}/delta/shipper_prod_gl/shipper_addresses"


class PODAutoValidation:
    def __init__(self, env="prod"):
        self._URI_BASE = DATASCIENCE_BASE_URI.format(env)
        self.INGEST = f"{self._URI_BASE}/pod_auto_validation/data"
        self.LATEST_PHOTO_CREATED_AT = f"{self._URI_BASE}/pod_auto_validation/latest_photo_created_at"
