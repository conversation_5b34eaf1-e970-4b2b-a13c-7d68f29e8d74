# variables to generate DWH queries with jinja templates
PSAT = {
    "forms": [
        {
            "form_id": 92812293196968,
            "legacy_shipper_id": 261,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "parcel_send_out_method": 16,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 265,
            "sales_platform_bukalapak_flag": 268,
            "sales_platform_facebook_flag": 268,
            "sales_platform_instagram_flag": 268,
            "sales_platform_jd_flag": 268,
            "sales_platform_lazada_flag": 268,
            "sales_platform_others_flag": 268,
            "sales_platform_others_comment": 268,
            "sales_platform_ownweb_flag": 268,
            "sales_platform_retailshop_flag": 268,
            "sales_platform_shopee_flag": 268,
            "sales_platform_tiktok_flag": 268,
            "sales_platform_tokko_flag": 268,
            "sales_platform_tokopedia_flag": 268,
            "sales_platform_zilingo_flag": 268,
            "sales_platform_using_nv_bukalapak_flag": 270,
            "sales_platform_using_nv_facebook_flag": 270,
            "sales_platform_using_nv_instagram_flag": 270,
            "sales_platform_using_nv_jd_flag": 270,
            "sales_platform_using_nv_lazada_flag": 270,
            "sales_platform_using_nv_others_flag": 270,
            "sales_platform_using_nv_others_comment": 270,
            "sales_platform_using_nv_ownweb_flag": 270,
            "sales_platform_using_nv_retailshop_flag": 270,
            "sales_platform_using_nv_shopee_flag": 270,
            "sales_platform_using_nv_tiktok_flag": 270,
            "sales_platform_using_nv_tokko_flag": 270,
            "sales_platform_using_nv_tokopedia_flag": 270,
            "sales_platform_using_nv_zilingo_flag": 270,
            "product_category_sold": 272,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 279,
            "ninja_satisfaction_order_booking_system_reliability_flag": 279,
            "ninja_satisfaction_app_ease_of_use_flag": 279,
            "ninja_satisfaction_app_reliability_flag": 279,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 279,
            "ninja_satisfaction_pickup_driver_attitude_flag": 279,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 279,
            "ninja_satisfaction_drop_off_experience_flag": 279,
            "ninja_satisfaction_drop_off_ease_flag": 279,
            "ninja_satisfaction_drop_off_speed_flag": 279,
            "ninja_satisfaction_drop_off_environment_flag": 279,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 279,
            "ninja_satisfaction_drop_off_accessibility_flag": 279,
            "ninja_satisfaction_delivery_area_coverage_flag": 279,
            "ninja_satisfaction_delivery_speed_flag": 279,
            "ninja_satisfaction_delivery_success_rate_flag": 279,
            "ninja_satisfaction_rider_attempts_flag": 279,
            "ninja_satisfaction_driver_attitude_flag": 279,
            "ninja_satisfaction_parcel_tracking_flag": 279,
            "ninja_satisfaction_parcel_handling_flag": 279,
            "ninja_satisfaction_rts_service_flag": 279,
            "ninja_satisfaction_rts_speed_flag": 279,
            "ninja_satisfaction_quality_of_resolution_flag": 279,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 279,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 279,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 279,
            "ninja_satisfaction_billing_accuracy_flag": 279,
            "ninja_satisfaction_speed_of_reimbursement_flag": 279,
            "ninja_satisfaction_speed_of_cod_remit_flag": 279,
            "ninja_satisfaction_pricing_cod_rate_flag": 279,
            "ninja_satisfaction_pricing_shipping_rate_flag": 279,
            "ninja_satisfaction_none_flag": 279,
            "ninja_satisfaction_support_channel": 281,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 280,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 280,
            "ninja_dissatisfaction_app_ease_of_use_flag": 280,
            "ninja_dissatisfaction_app_reliability_flag": 280,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 280,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 280,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 280,
            "ninja_dissatisfaction_drop_off_experience_flag": 280,
            "ninja_dissatisfaction_drop_off_ease_flag": 280,
            "ninja_dissatisfaction_drop_off_speed_flag": 280,
            "ninja_dissatisfaction_drop_off_environment_flag": 280,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 280,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 280,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 280,
            "ninja_dissatisfaction_delivery_speed_flag": 280,
            "ninja_dissatisfaction_delivery_success_rate_flag": 280,
            "ninja_dissatisfaction_rider_attempts_flag": 280,
            "ninja_dissatisfaction_driver_attitude_flag": 280,
            "ninja_dissatisfaction_parcel_tracking_flag": 280,
            "ninja_dissatisfaction_parcel_handling_flag": 280,
            "ninja_dissatisfaction_rts_service_flag": 280,
            "ninja_dissatisfaction_rts_speed_flag": 280,
            "ninja_dissatisfaction_quality_of_resolution_flag": 280,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 280,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 280,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 280,
            "ninja_dissatisfaction_billing_accuracy_flag": 280,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 280,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 280,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 280,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 280,
            "ninja_dissatisfaction_none_flag": 280,
            "ninja_dissatisfaction_support_channel": 283,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 284,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 284,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 284,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 284,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 284,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 284,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 284,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 284,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 284,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 284,
            "ninja_dissatisfaction_support_others_flag": 284,
            "ninja_dissatisfaction_support_others_comment": 284,
            "parcel_send_out_method_competitor": 288,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 294,
            "competitor_satisfaction_order_booking_system_reliability_flag": 294,
            "competitor_satisfaction_app_ease_of_use_flag": 294,
            "competitor_satisfaction_app_reliability_flag": 294,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 294,
            "competitor_satisfaction_pickup_driver_attitude_flag": 294,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 294,
            "competitor_satisfaction_drop_off_experience_flag": 294,
            "competitor_satisfaction_drop_off_ease_flag": 294,
            "competitor_satisfaction_drop_off_speed_flag": 294,
            "competitor_satisfaction_drop_off_environment_flag": 294,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 294,
            "competitor_satisfaction_drop_off_accessibility_flag": 294,
            "competitor_satisfaction_delivery_area_coverage_flag": 294,
            "competitor_satisfaction_delivery_speed_flag": 294,
            "competitor_satisfaction_delivery_success_rate_flag": 294,
            "competitor_satisfaction_rider_attempts_flag": 294,
            "competitor_satisfaction_driver_attitude_flag": 294,
            "competitor_satisfaction_parcel_tracking_flag": 294,
            "competitor_satisfaction_parcel_handling_flag": 294,
            "competitor_satisfaction_rts_service_flag": 294,
            "competitor_satisfaction_rts_speed_flag": 294,
            "competitor_satisfaction_quality_of_resolution_flag": 294,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 294,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 294,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 294,
            "competitor_satisfaction_billing_accuracy_flag": 294,
            "competitor_satisfaction_speed_of_reimbursement_flag": 294,
            "competitor_satisfaction_speed_of_cod_remit_flag": 294,
            "competitor_satisfaction_pricing_cod_rate_flag": 294,
            "competitor_satisfaction_pricing_shipping_rate_flag": 294,
            "competitor_satisfaction_none_flag": 294,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 295,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 295,
            "competitor_dissatisfaction_app_ease_of_use_flag": 295,
            "competitor_dissatisfaction_app_reliability_flag": 295,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 295,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 295,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 295,
            "competitor_dissatisfaction_drop_off_experience_flag": 295,
            "competitor_dissatisfaction_drop_off_ease_flag": 295,
            "competitor_dissatisfaction_drop_off_speed_flag": 295,
            "competitor_dissatisfaction_drop_off_environment_flag": 295,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 295,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 295,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 295,
            "competitor_dissatisfaction_delivery_speed_flag": 295,
            "competitor_dissatisfaction_delivery_success_rate_flag": 295,
            "competitor_dissatisfaction_rider_attempts_flag": 295,
            "competitor_dissatisfaction_driver_attitude_flag": 295,
            "competitor_dissatisfaction_parcel_tracking_flag": 295,
            "competitor_dissatisfaction_parcel_handling_flag": 295,
            "competitor_dissatisfaction_rts_service_flag": 295,
            "competitor_dissatisfaction_rts_speed_flag": 295,
            "competitor_dissatisfaction_quality_of_resolution_flag": 295,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 295,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 295,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 295,
            "competitor_dissatisfaction_billing_accuracy_flag": 295,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 295,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 295,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 295,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 295,
            "competitor_dissatisfaction_none_flag": 295,
            "fs_am_flag": 301,
            "cx_force_belonging": 326,
            "cx_force_certainty": 326,
            "cx_force_control": 326,
            "cx_force_enjoyment": 326,
            "cx_force_fair_treatment": 326,
            "cx_force_none": 326,
            "cx_force_status": 326,
        },
        {
            "form_id": 92812586396975,
            "legacy_shipper_id": 276,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "parcel_send_out_method": 16,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 278,
            "sales_platform_carousell_flag": 284,
            "sales_platform_facebook_flag": 284,
            "sales_platform_instagram_flag": 284,
            "sales_platform_lazada_flag": 284,
            "sales_platform_lelong_flag": 284,
            "sales_platform_others_flag": 284,
            "sales_platform_others_comment": 284,
            "sales_platform_ownweb_flag": 284,
            "sales_platform_prestomall_flag": 284,
            "sales_platform_pgmall_flag": 284,
            "sales_platform_qoo10_flag": 284,
            "sales_platform_retailshop_flag": 284,
            "sales_platform_shopee_flag": 284,
            "sales_platform_tiktok_flag": 284,
            "sales_platform_vettons_flag": 284,
            "sales_platform_zalora_flag": 284,
            "sales_platform_using_nv_carousell_flag": 285,
            "sales_platform_using_nv_facebook_flag": 285,
            "sales_platform_using_nv_instagram_flag": 285,
            "sales_platform_using_nv_lazada_flag": 285,
            "sales_platform_using_nv_lelong_flag": 285,
            "sales_platform_using_nv_others_flag": 285,
            "sales_platform_using_nv_others_comment": 285,
            "sales_platform_using_nv_ownweb_flag": 285,
            "sales_platform_using_nv_prestomall_flag": 285,
            "sales_platform_using_nv_pgmall_flag": 285,
            "sales_platform_using_nv_qoo10_flag": 285,
            "sales_platform_using_nv_retailshop_flag": 285,
            "sales_platform_using_nv_shopee_flag": 285,
            "sales_platform_using_nv_tiktok_flag": 285,
            "sales_platform_using_nv_vettons_flag": 285,
            "sales_platform_using_nv_zalora_flag": 285,
            "product_category_sold": 288,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 295,
            "ninja_satisfaction_order_booking_system_reliability_flag": 295,
            "ninja_satisfaction_app_ease_of_use_flag": 295,
            "ninja_satisfaction_app_reliability_flag": 295,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 295,
            "ninja_satisfaction_pickup_driver_attitude_flag": 295,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 295,
            "ninja_satisfaction_drop_off_experience_flag": 295,
            "ninja_satisfaction_drop_off_ease_flag": 295,
            "ninja_satisfaction_drop_off_speed_flag": 295,
            "ninja_satisfaction_drop_off_environment_flag": 295,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 295,
            "ninja_satisfaction_drop_off_accessibility_flag": 295,
            "ninja_satisfaction_delivery_area_coverage_flag": 295,
            "ninja_satisfaction_delivery_speed_flag": 295,
            "ninja_satisfaction_delivery_success_rate_flag": 295,
            "ninja_satisfaction_rider_attempts_flag": 295,
            "ninja_satisfaction_driver_attitude_flag": 295,
            "ninja_satisfaction_parcel_tracking_flag": 295,
            "ninja_satisfaction_parcel_handling_flag": 295,
            "ninja_satisfaction_rts_service_flag": 295,
            "ninja_satisfaction_rts_speed_flag": 295,
            "ninja_satisfaction_quality_of_resolution_flag": 295,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 295,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 295,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 295,
            "ninja_satisfaction_billing_accuracy_flag": 295,
            "ninja_satisfaction_speed_of_reimbursement_flag": 295,
            "ninja_satisfaction_speed_of_cod_remit_flag": 295,
            "ninja_satisfaction_pricing_cod_rate_flag": 295,
            "ninja_satisfaction_pricing_shipping_rate_flag": 295,
            "ninja_satisfaction_none_flag": 295,
            "ninja_satisfaction_support_channel": 296,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 299,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 299,
            "ninja_dissatisfaction_app_ease_of_use_flag": 299,
            "ninja_dissatisfaction_app_reliability_flag": 299,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 299,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 299,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 299,
            "ninja_dissatisfaction_drop_off_experience_flag": 299,
            "ninja_dissatisfaction_drop_off_ease_flag": 299,
            "ninja_dissatisfaction_drop_off_speed_flag": 299,
            "ninja_dissatisfaction_drop_off_environment_flag": 299,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 299,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 299,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 299,
            "ninja_dissatisfaction_delivery_speed_flag": 299,
            "ninja_dissatisfaction_delivery_success_rate_flag": 299,
            "ninja_dissatisfaction_rider_attempts_flag": 299,
            "ninja_dissatisfaction_driver_attitude_flag": 299,
            "ninja_dissatisfaction_parcel_tracking_flag": 299,
            "ninja_dissatisfaction_parcel_handling_flag": 299,
            "ninja_dissatisfaction_rts_service_flag": 299,
            "ninja_dissatisfaction_rts_speed_flag": 299,
            "ninja_dissatisfaction_quality_of_resolution_flag": 299,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 299,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 299,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 299,
            "ninja_dissatisfaction_billing_accuracy_flag": 299,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 299,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 299,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 299,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 299,
            "ninja_dissatisfaction_none_flag": 299,
            "ninja_dissatisfaction_support_channel": 306,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 307,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 307,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 307,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 307,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 307,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 307,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 307,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 307,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 307,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 307,
            "ninja_dissatisfaction_support_others_flag": 307,
            "ninja_dissatisfaction_support_others_comment": 307,
            "parcel_send_out_method_competitor": 301,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 303,
            "competitor_satisfaction_order_booking_system_reliability_flag": 303,
            "competitor_satisfaction_app_ease_of_use_flag": 303,
            "competitor_satisfaction_app_reliability_flag": 303,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 303,
            "competitor_satisfaction_pickup_driver_attitude_flag": 303,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 303,
            "competitor_satisfaction_drop_off_experience_flag": 303,
            "competitor_satisfaction_drop_off_ease_flag": 303,
            "competitor_satisfaction_drop_off_speed_flag": 303,
            "competitor_satisfaction_drop_off_environment_flag": 303,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 303,
            "competitor_satisfaction_drop_off_accessibility_flag": 303,
            "competitor_satisfaction_delivery_area_coverage_flag": 303,
            "competitor_satisfaction_delivery_speed_flag": 303,
            "competitor_satisfaction_delivery_success_rate_flag": 303,
            "competitor_satisfaction_rider_attempts_flag": 303,
            "competitor_satisfaction_driver_attitude_flag": 303,
            "competitor_satisfaction_parcel_tracking_flag": 303,
            "competitor_satisfaction_parcel_handling_flag": 303,
            "competitor_satisfaction_rts_service_flag": 303,
            "competitor_satisfaction_rts_speed_flag": 303,
            "competitor_satisfaction_quality_of_resolution_flag": 303,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 303,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 303,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 303,
            "competitor_satisfaction_billing_accuracy_flag": 303,
            "competitor_satisfaction_speed_of_reimbursement_flag": 303,
            "competitor_satisfaction_speed_of_cod_remit_flag": 303,
            "competitor_satisfaction_pricing_cod_rate_flag": 303,
            "competitor_satisfaction_pricing_shipping_rate_flag": 303,
            "competitor_satisfaction_none_flag": 303,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 305,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 305,
            "competitor_dissatisfaction_app_ease_of_use_flag": 305,
            "competitor_dissatisfaction_app_reliability_flag": 305,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 305,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 305,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 305,
            "competitor_dissatisfaction_drop_off_experience_flag": 305,
            "competitor_dissatisfaction_drop_off_ease_flag": 305,
            "competitor_dissatisfaction_drop_off_speed_flag": 305,
            "competitor_dissatisfaction_drop_off_environment_flag": 305,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 305,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 305,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 305,
            "competitor_dissatisfaction_delivery_speed_flag": 305,
            "competitor_dissatisfaction_delivery_success_rate_flag": 305,
            "competitor_dissatisfaction_rider_attempts_flag": 305,
            "competitor_dissatisfaction_driver_attitude_flag": 305,
            "competitor_dissatisfaction_parcel_tracking_flag": 305,
            "competitor_dissatisfaction_parcel_handling_flag": 305,
            "competitor_dissatisfaction_rts_service_flag": 305,
            "competitor_dissatisfaction_rts_speed_flag": 305,
            "competitor_dissatisfaction_quality_of_resolution_flag": 305,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 305,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 305,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 305,
            "competitor_dissatisfaction_billing_accuracy_flag": 305,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 305,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 305,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 305,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 305,
            "competitor_dissatisfaction_none_flag": 305,
            "fs_am_flag": 318,
            "cx_force_belonging": 356,
            "cx_force_certainty": 356,
            "cx_force_control": 356,
            "cx_force_enjoyment": 356,
            "cx_force_fair_treatment": 356,
            "cx_force_none": 356,
            "cx_force_status": 356,
        },
        {
            "form_id": 92813190496968,
            "legacy_shipper_id": 261,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 263,
            "sales_platform_beautymnl_flag": 264,
            "sales_platform_ebay_flag": 264,
            "sales_platform_carousell_flag": 264,
            "sales_platform_facebook_flag": 264,
            "sales_platform_instagram_flag": 264,
            "sales_platform_lazada_flag": 264,
            "sales_platform_others_flag": 264,
            "sales_platform_others_comment": 264,
            "sales_platform_ownweb_flag": 264,
            "sales_platform_retailshop_flag": 264,
            "sales_platform_shopee_flag": 264,
            "sales_platform_tiktok_flag": 264,
            "sales_platform_zalora_flag": 264,
            "sales_platform_zilingo_flag": 264,
            "sales_platform_using_nv_beautymnl_flag": 265,
            "sales_platform_using_nv_ebay_flag": 265,
            "sales_platform_using_nv_carousell_flag": 265,
            "sales_platform_using_nv_facebook_flag": 265,
            "sales_platform_using_nv_instagram_flag": 265,
            "sales_platform_using_nv_lazada_flag": 265,
            "sales_platform_using_nv_others_flag": 265,
            "sales_platform_using_nv_others_comment": 265,
            "sales_platform_using_nv_ownweb_flag": 265,
            "sales_platform_using_nv_retailshop_flag": 265,
            "sales_platform_using_nv_shopee_flag": 265,
            "sales_platform_using_nv_tiktok_flag": 265,
            "sales_platform_using_nv_zalora_flag": 265,
            "sales_platform_using_nv_zilingo_flag": 265,
            "product_category_sold": 406,
            "parcel_send_out_method": 267,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 361,
            "ninja_satisfaction_order_booking_system_reliability_flag": 361,
            "ninja_satisfaction_app_ease_of_use_flag": 362,
            "ninja_satisfaction_app_reliability_flag": 362,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 365,
            "ninja_satisfaction_pickup_driver_attitude_flag": 365,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 365,
            "ninja_satisfaction_drop_off_experience_flag": 366,
            "ninja_satisfaction_drop_off_ease_flag": 366,
            "ninja_satisfaction_drop_off_speed_flag": 366,
            "ninja_satisfaction_drop_off_environment_flag": 366,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 366,
            "ninja_satisfaction_drop_off_accessibility_flag": 366,
            "ninja_satisfaction_delivery_area_coverage_flag": 367,
            "ninja_satisfaction_delivery_speed_flag": 367,
            "ninja_satisfaction_delivery_success_rate_flag": 367,
            "ninja_satisfaction_rider_attempts_flag": 367,
            "ninja_satisfaction_driver_attitude_flag": 367,
            "ninja_satisfaction_parcel_tracking_flag": 368,
            "ninja_satisfaction_parcel_handling_flag": 368,
            "ninja_satisfaction_rts_service_flag": 369,
            "ninja_satisfaction_rts_speed_flag": 369,
            "ninja_satisfaction_quality_of_resolution_flag": 370,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 370,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 370,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 370,
            "ninja_satisfaction_billing_accuracy_flag": 371,
            "ninja_satisfaction_speed_of_reimbursement_flag": 371,
            "ninja_satisfaction_speed_of_cod_remit_flag": 371,
            "ninja_satisfaction_pricing_cod_rate_flag": 372,
            "ninja_satisfaction_pricing_shipping_rate_flag": 372,
            "ninja_satisfaction_none_flag": 429,
            "ninja_satisfaction_support_channel": 289,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 373,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 373,
            "ninja_dissatisfaction_app_ease_of_use_flag": 374,
            "ninja_dissatisfaction_app_reliability_flag": 374,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 375,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 375,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 375,
            "ninja_dissatisfaction_drop_off_experience_flag": 376,
            "ninja_dissatisfaction_drop_off_ease_flag": 376,
            "ninja_dissatisfaction_drop_off_speed_flag": 376,
            "ninja_dissatisfaction_drop_off_environment_flag": 376,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 376,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 376,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 377,
            "ninja_dissatisfaction_delivery_speed_flag": 377,
            "ninja_dissatisfaction_delivery_success_rate_flag": 377,
            "ninja_dissatisfaction_rider_attempts_flag": 377,
            "ninja_dissatisfaction_driver_attitude_flag": 377,
            "ninja_dissatisfaction_parcel_tracking_flag": 378,
            "ninja_dissatisfaction_parcel_handling_flag": 378,
            "ninja_dissatisfaction_rts_service_flag": 379,
            "ninja_dissatisfaction_rts_speed_flag": 379,
            "ninja_dissatisfaction_quality_of_resolution_flag": 380,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 380,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 380,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 380,
            "ninja_dissatisfaction_billing_accuracy_flag": 381,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 381,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 381,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 382,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 382,
            "ninja_dissatisfaction_none_flag": 428,
            "ninja_dissatisfaction_support_channel": 314,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 316,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 316,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 316,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 316,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 316,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 316,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 316,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 316,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 316,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 316,
            "ninja_dissatisfaction_support_others_flag": 316,
            "ninja_dissatisfaction_support_others_comment": 316,
            "parcel_send_out_method_competitor": 319,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 384,
            "competitor_satisfaction_order_booking_system_reliability_flag": 384,
            "competitor_satisfaction_app_ease_of_use_flag": 385,
            "competitor_satisfaction_app_reliability_flag": 385,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 386,
            "competitor_satisfaction_pickup_driver_attitude_flag": 386,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 386,
            "competitor_satisfaction_drop_off_experience_flag": 387,
            "competitor_satisfaction_drop_off_ease_flag": 387,
            "competitor_satisfaction_drop_off_speed_flag": 387,
            "competitor_satisfaction_drop_off_environment_flag": 387,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 387,
            "competitor_satisfaction_drop_off_accessibility_flag": 387,
            "competitor_satisfaction_delivery_area_coverage_flag": 388,
            "competitor_satisfaction_delivery_speed_flag": 388,
            "competitor_satisfaction_delivery_success_rate_flag": 388,
            "competitor_satisfaction_rider_attempts_flag": 388,
            "competitor_satisfaction_driver_attitude_flag": 388,
            "competitor_satisfaction_parcel_tracking_flag": 389,
            "competitor_satisfaction_parcel_handling_flag": 389,
            "competitor_satisfaction_rts_service_flag": 390,
            "competitor_satisfaction_rts_speed_flag": 390,
            "competitor_satisfaction_quality_of_resolution_flag": 391,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 391,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 391,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 391,
            "competitor_satisfaction_billing_accuracy_flag": 392,
            "competitor_satisfaction_speed_of_reimbursement_flag": 392,
            "competitor_satisfaction_speed_of_cod_remit_flag": 392,
            "competitor_satisfaction_pricing_cod_rate_flag": 393,
            "competitor_satisfaction_pricing_shipping_rate_flag": 393,
            "competitor_satisfaction_none_flag": 430,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 394,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 394,
            "competitor_dissatisfaction_app_ease_of_use_flag": 395,
            "competitor_dissatisfaction_app_reliability_flag": 395,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 396,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 396,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 396,
            "competitor_dissatisfaction_drop_off_experience_flag": 397,
            "competitor_dissatisfaction_drop_off_ease_flag": 397,
            "competitor_dissatisfaction_drop_off_speed_flag": 397,
            "competitor_dissatisfaction_drop_off_environment_flag": 397,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 397,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 397,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 398,
            "competitor_dissatisfaction_delivery_speed_flag": 398,
            "competitor_dissatisfaction_delivery_success_rate_flag": 398,
            "competitor_dissatisfaction_rider_attempts_flag": 398,
            "competitor_dissatisfaction_driver_attitude_flag": 398,
            "competitor_dissatisfaction_parcel_tracking_flag": 399,
            "competitor_dissatisfaction_parcel_handling_flag": 399,
            "competitor_dissatisfaction_rts_service_flag": 400,
            "competitor_dissatisfaction_rts_speed_flag": 400,
            "competitor_dissatisfaction_quality_of_resolution_flag": 401,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 401,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 401,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 401,
            "competitor_dissatisfaction_billing_accuracy_flag": 402,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 402,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 402,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 403,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 403,
            "competitor_dissatisfaction_none_flag": 431,
            "fs_am_flag": 410,
            "cx_force_belonging": 439,
            "cx_force_certainty": 439,
            "cx_force_control": 439,
            "cx_force_enjoyment": 439,
            "cx_force_fair_treatment": 439,
            "cx_force_none": 439,
            "cx_force_status": 439,
        },
        {
            "form_id": 92812527996976,
            "legacy_shipper_id": 262,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "parcel_send_out_method": 16,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 264,
            "sales_platform_amazon_flag": 266,
            "sales_platform_carousell_flag": 266,
            "sales_platform_facebook_flag": 266,
            "sales_platform_instagram_flag": 266,
            "sales_platform_lazada_flag": 266,
            "sales_platform_others_flag": 266,
            "sales_platform_others_comment": 266,
            "sales_platform_ownweb_flag": 266,
            "sales_platform_qoo10_flag": 266,
            "sales_platform_retailshop_flag": 266,
            "sales_platform_shopee_flag": 266,
            "sales_platform_telegram_flag": 266,
            "sales_platform_tiktok_flag": 266,
            "sales_platform_wechat_flag": 266,
            "sales_platform_zalora_flag": 266,
            "sales_platform_using_nv_amazon_flag": 267,
            "sales_platform_using_nv_carousell_flag": 267,
            "sales_platform_using_nv_facebook_flag": 267,
            "sales_platform_using_nv_instagram_flag": 267,
            "sales_platform_using_nv_lazada_flag": 267,
            "sales_platform_using_nv_others_flag": 267,
            "sales_platform_using_nv_others_comment": 267,
            "sales_platform_using_nv_ownweb_flag": 267,
            "sales_platform_using_nv_qoo10_flag": 267,
            "sales_platform_using_nv_retailshop_flag": 267,
            "sales_platform_using_nv_shopee_flag": 267,
            "sales_platform_using_nv_telegram_flag": 267,
            "sales_platform_using_nv_tiktok_flag": 267,
            "sales_platform_using_nv_wechat_flag": 267,
            "sales_platform_using_nv_zalora_flag": 267,
            "product_category_sold": 271,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 277,
            "ninja_satisfaction_order_booking_system_reliability_flag": 277,
            "ninja_satisfaction_app_ease_of_use_flag": 277,
            "ninja_satisfaction_app_reliability_flag": 277,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 277,
            "ninja_satisfaction_pickup_driver_attitude_flag": 277,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 277,
            "ninja_satisfaction_drop_off_experience_flag": 277,
            "ninja_satisfaction_drop_off_ease_flag": 277,
            "ninja_satisfaction_drop_off_speed_flag": 277,
            "ninja_satisfaction_drop_off_environment_flag": 277,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 277,
            "ninja_satisfaction_drop_off_accessibility_flag": 277,
            "ninja_satisfaction_delivery_area_coverage_flag": 277,
            "ninja_satisfaction_delivery_speed_flag": 277,
            "ninja_satisfaction_delivery_success_rate_flag": 277,
            "ninja_satisfaction_rider_attempts_flag": 277,
            "ninja_satisfaction_driver_attitude_flag": 277,
            "ninja_satisfaction_parcel_tracking_flag": 277,
            "ninja_satisfaction_parcel_handling_flag": 277,
            "ninja_satisfaction_rts_service_flag": 277,
            "ninja_satisfaction_rts_speed_flag": 277,
            "ninja_satisfaction_quality_of_resolution_flag": 277,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 277,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 277,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 277,
            "ninja_satisfaction_billing_accuracy_flag": 277,
            "ninja_satisfaction_speed_of_reimbursement_flag": 277,
            "ninja_satisfaction_speed_of_cod_remit_flag": 277,
            "ninja_satisfaction_pricing_cod_rate_flag": 277,
            "ninja_satisfaction_pricing_shipping_rate_flag": 277,
            "ninja_satisfaction_none_flag": 277,
            "ninja_satisfaction_support_channel": 284,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 278,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 278,
            "ninja_dissatisfaction_app_ease_of_use_flag": 278,
            "ninja_dissatisfaction_app_reliability_flag": 278,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 278,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 278,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 278,
            "ninja_dissatisfaction_drop_off_experience_flag": 278,
            "ninja_dissatisfaction_drop_off_ease_flag": 278,
            "ninja_dissatisfaction_drop_off_speed_flag": 278,
            "ninja_dissatisfaction_drop_off_environment_flag": 278,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 278,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 278,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 278,
            "ninja_dissatisfaction_delivery_speed_flag": 278,
            "ninja_dissatisfaction_delivery_success_rate_flag": 278,
            "ninja_dissatisfaction_rider_attempts_flag": 278,
            "ninja_dissatisfaction_driver_attitude_flag": 278,
            "ninja_dissatisfaction_parcel_tracking_flag": 278,
            "ninja_dissatisfaction_parcel_handling_flag": 278,
            "ninja_dissatisfaction_rts_service_flag": 278,
            "ninja_dissatisfaction_rts_speed_flag": 278,
            "ninja_dissatisfaction_quality_of_resolution_flag": 278,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 278,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 278,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 278,
            "ninja_dissatisfaction_billing_accuracy_flag": 278,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 278,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 278,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 278,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 278,
            "ninja_dissatisfaction_none_flag": 278,
            "ninja_dissatisfaction_support_channel": 285,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 287,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 287,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 287,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 287,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 287,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 287,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 287,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 287,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 287,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 287,
            "ninja_dissatisfaction_support_others_flag": 287,
            "ninja_dissatisfaction_support_others_comment": 287,
            "parcel_send_out_method_competitor": 295,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 281,
            "competitor_satisfaction_order_booking_system_reliability_flag": 281,
            "competitor_satisfaction_app_ease_of_use_flag": 281,
            "competitor_satisfaction_app_reliability_flag": 281,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 281,
            "competitor_satisfaction_pickup_driver_attitude_flag": 281,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 281,
            "competitor_satisfaction_drop_off_experience_flag": 281,
            "competitor_satisfaction_drop_off_ease_flag": 281,
            "competitor_satisfaction_drop_off_speed_flag": 281,
            "competitor_satisfaction_drop_off_environment_flag": 281,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 281,
            "competitor_satisfaction_drop_off_accessibility_flag": 281,
            "competitor_satisfaction_delivery_area_coverage_flag": 281,
            "competitor_satisfaction_delivery_speed_flag": 281,
            "competitor_satisfaction_delivery_success_rate_flag": 281,
            "competitor_satisfaction_rider_attempts_flag": 281,
            "competitor_satisfaction_driver_attitude_flag": 281,
            "competitor_satisfaction_parcel_tracking_flag": 281,
            "competitor_satisfaction_parcel_handling_flag": 281,
            "competitor_satisfaction_rts_service_flag": 281,
            "competitor_satisfaction_rts_speed_flag": 281,
            "competitor_satisfaction_quality_of_resolution_flag": 281,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 281,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 281,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 281,
            "competitor_satisfaction_billing_accuracy_flag": 281,
            "competitor_satisfaction_speed_of_reimbursement_flag": 281,
            "competitor_satisfaction_speed_of_cod_remit_flag": 281,
            "competitor_satisfaction_pricing_cod_rate_flag": 281,
            "competitor_satisfaction_pricing_shipping_rate_flag": 281,
            "competitor_satisfaction_none_flag": 281,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 283,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 283,
            "competitor_dissatisfaction_app_ease_of_use_flag": 283,
            "competitor_dissatisfaction_app_reliability_flag": 283,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 283,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 283,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 283,
            "competitor_dissatisfaction_drop_off_experience_flag": 283,
            "competitor_dissatisfaction_drop_off_ease_flag": 283,
            "competitor_dissatisfaction_drop_off_speed_flag": 283,
            "competitor_dissatisfaction_drop_off_environment_flag": 283,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 283,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 283,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 283,
            "competitor_dissatisfaction_delivery_speed_flag": 283,
            "competitor_dissatisfaction_delivery_success_rate_flag": 283,
            "competitor_dissatisfaction_rider_attempts_flag": 283,
            "competitor_dissatisfaction_driver_attitude_flag": 283,
            "competitor_dissatisfaction_parcel_tracking_flag": 283,
            "competitor_dissatisfaction_parcel_handling_flag": 283,
            "competitor_dissatisfaction_rts_service_flag": 283,
            "competitor_dissatisfaction_rts_speed_flag": 283,
            "competitor_dissatisfaction_quality_of_resolution_flag": 283,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 283,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 283,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 283,
            "competitor_dissatisfaction_billing_accuracy_flag": 283,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 283,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 283,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 283,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 283,
            "competitor_dissatisfaction_none_flag": 283,
            "fs_am_flag": 299,
            "cx_force_belonging": 318,
            "cx_force_certainty": 318,
            "cx_force_control": 318,
            "cx_force_enjoyment": 318,
            "cx_force_fair_treatment": 318,
            "cx_force_none": 318,
            "cx_force_status": 318,
        },
        {
            "form_id": 92812216696968,
            "legacy_shipper_id": 261,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "parcel_send_out_method": 16,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 302,
            "sales_platform_chilindo_flag": 265,
            "sales_platform_facebook_flag": 265,
            "sales_platform_instagram_flag": 265,
            "sales_platform_jdcentral_flag": 265,
            "sales_platform_kaidee_flag": 265,
            "sales_platform_lazada_flag": 265,
            "sales_platform_others_flag": 265,
            "sales_platform_others_comment": 265,
            "sales_platform_ownweb_flag": 265,
            "sales_platform_retailshop_flag": 265,
            "sales_platform_shopee_flag": 265,
            "sales_platform_tiktok_flag": 265,
            "sales_platform_using_nv_chilindo_flag": 267,
            "sales_platform_using_nv_facebook_flag": 267,
            "sales_platform_using_nv_instagram_flag": 267,
            "sales_platform_using_nv_jdcentral_flag": 267,
            "sales_platform_using_nv_kaidee_flag": 267,
            "sales_platform_using_nv_lazada_flag": 267,
            "sales_platform_using_nv_others_flag": 267,
            "sales_platform_using_nv_others_comment": 267,
            "sales_platform_using_nv_ownweb_flag": 267,
            "sales_platform_using_nv_retailshop_flag": 267,
            "sales_platform_using_nv_shopee_flag": 267,
            "sales_platform_using_nv_tiktok_flag": 267,
            "product_category_sold": 268,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 276,
            "ninja_satisfaction_order_booking_system_reliability_flag": 276,
            "ninja_satisfaction_app_ease_of_use_flag": 276,
            "ninja_satisfaction_app_reliability_flag": 276,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 276,
            "ninja_satisfaction_pickup_driver_attitude_flag": 276,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 276,
            "ninja_satisfaction_drop_off_experience_flag": 276,
            "ninja_satisfaction_drop_off_ease_flag": 276,
            "ninja_satisfaction_drop_off_speed_flag": 276,
            "ninja_satisfaction_drop_off_environment_flag": 276,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 276,
            "ninja_satisfaction_drop_off_accessibility_flag": 276,
            "ninja_satisfaction_delivery_area_coverage_flag": 276,
            "ninja_satisfaction_delivery_speed_flag": 276,
            "ninja_satisfaction_delivery_success_rate_flag": 276,
            "ninja_satisfaction_rider_attempts_flag": 276,
            "ninja_satisfaction_driver_attitude_flag": 276,
            "ninja_satisfaction_parcel_tracking_flag": 276,
            "ninja_satisfaction_parcel_handling_flag": 276,
            "ninja_satisfaction_rts_service_flag": 276,
            "ninja_satisfaction_rts_speed_flag": 276,
            "ninja_satisfaction_quality_of_resolution_flag": 276,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 276,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 276,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 276,
            "ninja_satisfaction_billing_accuracy_flag": 276,
            "ninja_satisfaction_speed_of_reimbursement_flag": 276,
            "ninja_satisfaction_speed_of_cod_remit_flag": 276,
            "ninja_satisfaction_pricing_cod_rate_flag": 276,
            "ninja_satisfaction_pricing_shipping_rate_flag": 276,
            "ninja_satisfaction_none_flag": 276,
            "ninja_satisfaction_support_channel": 277,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 280,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 280,
            "ninja_dissatisfaction_app_ease_of_use_flag": 280,
            "ninja_dissatisfaction_app_reliability_flag": 280,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 280,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 280,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 280,
            "ninja_dissatisfaction_drop_off_experience_flag": 280,
            "ninja_dissatisfaction_drop_off_ease_flag": 280,
            "ninja_dissatisfaction_drop_off_speed_flag": 280,
            "ninja_dissatisfaction_drop_off_environment_flag": 280,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 280,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 280,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 280,
            "ninja_dissatisfaction_delivery_speed_flag": 280,
            "ninja_dissatisfaction_delivery_success_rate_flag": 280,
            "ninja_dissatisfaction_rider_attempts_flag": 280,
            "ninja_dissatisfaction_driver_attitude_flag": 280,
            "ninja_dissatisfaction_parcel_tracking_flag": 280,
            "ninja_dissatisfaction_parcel_handling_flag": 280,
            "ninja_dissatisfaction_rts_service_flag": 280,
            "ninja_dissatisfaction_rts_speed_flag": 280,
            "ninja_dissatisfaction_quality_of_resolution_flag": 280,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 280,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 280,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 280,
            "ninja_dissatisfaction_billing_accuracy_flag": 280,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 280,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 280,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 280,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 280,
            "ninja_dissatisfaction_none_flag": 280,
            "ninja_dissatisfaction_support_channel": 282,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 281,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 281,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 281,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 281,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 281,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 281,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 281,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 281,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 281,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 281,
            "ninja_dissatisfaction_support_others_flag": 281,
            "ninja_dissatisfaction_support_others_comment": 281,
            "parcel_send_out_method_competitor": 301,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 290,
            "competitor_satisfaction_order_booking_system_reliability_flag": 290,
            "competitor_satisfaction_app_ease_of_use_flag": 290,
            "competitor_satisfaction_app_reliability_flag": 290,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 290,
            "competitor_satisfaction_pickup_driver_attitude_flag": 290,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 290,
            "competitor_satisfaction_drop_off_experience_flag": 290,
            "competitor_satisfaction_drop_off_ease_flag": 290,
            "competitor_satisfaction_drop_off_speed_flag": 290,
            "competitor_satisfaction_drop_off_environment_flag": 290,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 290,
            "competitor_satisfaction_drop_off_accessibility_flag": 290,
            "competitor_satisfaction_delivery_area_coverage_flag": 290,
            "competitor_satisfaction_delivery_speed_flag": 290,
            "competitor_satisfaction_delivery_success_rate_flag": 290,
            "competitor_satisfaction_rider_attempts_flag": 290,
            "competitor_satisfaction_driver_attitude_flag": 290,
            "competitor_satisfaction_parcel_tracking_flag": 290,
            "competitor_satisfaction_parcel_handling_flag": 290,
            "competitor_satisfaction_rts_service_flag": 290,
            "competitor_satisfaction_rts_speed_flag": 290,
            "competitor_satisfaction_quality_of_resolution_flag": 290,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 290,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 290,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 290,
            "competitor_satisfaction_billing_accuracy_flag": 290,
            "competitor_satisfaction_speed_of_reimbursement_flag": 290,
            "competitor_satisfaction_speed_of_cod_remit_flag": 290,
            "competitor_satisfaction_pricing_cod_rate_flag": 290,
            "competitor_satisfaction_pricing_shipping_rate_flag": 290,
            "competitor_satisfaction_none_flag": 290,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 291,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 291,
            "competitor_dissatisfaction_app_ease_of_use_flag": 291,
            "competitor_dissatisfaction_app_reliability_flag": 291,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 291,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 291,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 291,
            "competitor_dissatisfaction_drop_off_experience_flag": 291,
            "competitor_dissatisfaction_drop_off_ease_flag": 291,
            "competitor_dissatisfaction_drop_off_speed_flag": 291,
            "competitor_dissatisfaction_drop_off_environment_flag": 291,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 291,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 291,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 291,
            "competitor_dissatisfaction_delivery_speed_flag": 291,
            "competitor_dissatisfaction_delivery_success_rate_flag": 291,
            "competitor_dissatisfaction_rider_attempts_flag": 291,
            "competitor_dissatisfaction_driver_attitude_flag": 291,
            "competitor_dissatisfaction_parcel_tracking_flag": 291,
            "competitor_dissatisfaction_parcel_handling_flag": 291,
            "competitor_dissatisfaction_rts_service_flag": 291,
            "competitor_dissatisfaction_rts_speed_flag": 291,
            "competitor_dissatisfaction_quality_of_resolution_flag": 291,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 291,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 291,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 291,
            "competitor_dissatisfaction_billing_accuracy_flag": 291,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 291,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 291,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 291,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 291,
            "competitor_dissatisfaction_none_flag": 291,
            "fs_am_flag": 312,
            "cx_force_belonging": 331,
            "cx_force_certainty": 331,
            "cx_force_control": 331,
            "cx_force_enjoyment": 331,
            "cx_force_fair_treatment": 331,
            "cx_force_none": 331,
            "cx_force_status": 331,
        },
        {
            "form_id": 92819118996979,
            "legacy_shipper_id": 260,
            "total_parcels_shipped_past_week": 141,
            "shipment_frequency": 13,
            "area_coverage_factor_flag": 240,
            "proximity_of_collection_or_drop_off_points_factor_flag": 240,
            "parcel_pickup_speed_factor_flag": 240,
            "punctuality_of_parcel_pickup_factor_flag": 240,
            "delivery_speed_factor_flag": 240,
            "punctuality_of_delivery_factor_flag": 240,
            "parcel_tracking_factor_flag": 240,
            "order_system_ease_of_use_factor_flag": 240,
            "recovery_and_issue_resolution_factor_flag": 240,
            "competitive_prices_factor_flag": 240,
            "customer_service_or_account_management_factor_flag": 240,
            "brand_reputation_and_trust_factor_flag": 240,
            "parcel_handling_factor_flag": 240,
            "ninja_recommendation_score": 241,
            "ninja_satisfaction_score": 242,
            "ninja_satisfaction_score_order_booking_ease_of_navigation": 243,
            "ninja_satisfaction_score_order_booking_system_reliability": 243,
            "ninja_satisfaction_score_parcel_tracking": 243,
            "ninja_satisfaction_score_pickup_driver_punctuality": 243,
            "ninja_satisfaction_score_pickup_driver_attitude": 243,
            "ninja_satisfaction_score_drop_off_experience": 243,
            "ninja_satisfaction_score_drop_off_ease": 243,
            "ninja_satisfaction_score_drop_off_accessibility": 243,
            "ninja_satisfaction_score_delivery_speed": 243,
            "ninja_satisfaction_score_rts_service": 243,
            "ninja_satisfaction_score_quality_of_resolution": 243,
            "ninja_satisfaction_score_billing_accuracy": 243,
            "ninja_service_dissatisfaction_reason": 244,
            "ninja_service_comment": 245,
            "competitor_name": 246,
            "competitor_recommendation_score": 247,
            "competitor_satisfaction_score": 248,
            "competitor_satisfaction_score_order_booking_ease_of_navigation": 249,
            "competitor_satisfaction_score_order_booking_system_reliability": 249,
            "competitor_satisfaction_score_parcel_tracking": 249,
            "competitor_satisfaction_score_pickup_driver_punctuality": 249,
            "competitor_satisfaction_score_pickup_driver_attitude": 249,
            "competitor_satisfaction_score_drop_off_experience": 249,
            "competitor_satisfaction_score_drop_off_ease": 249,
            "competitor_satisfaction_score_drop_off_accessibility": 249,
            "competitor_satisfaction_score_delivery_speed": 249,
            "competitor_satisfaction_score_rts_service": 249,
            "competitor_satisfaction_score_quality_of_resolution": 249,
            "competitor_satisfaction_score_billing_accuracy": 249,
            "competitor_service_dissatisfaction_reason": 250,
            "competitor_service_comment": 251,
            "am_vs_non_serviceable": 264,
            "sales_platform_facebook_flag": 267,
            "sales_platform_instagram_flag": 267,
            "sales_platform_lazada_flag": 267,
            "sales_platform_others_flag": 267,
            "sales_platform_others_comment": 267,
            "sales_platform_ownweb_flag": 267,
            "sales_platform_retailshop_flag": 267,
            "sales_platform_sendo_flag": 267,
            "sales_platform_shopee_flag": 267,
            "sales_platform_tiki_flag": 267,
            "sales_platform_tiktok_flag": 267,
            "sales_platform_zalo_flag": 267,
            "sales_platform_using_nv_facebook_flag": 268,
            "sales_platform_using_nv_instagram_flag": 268,
            "sales_platform_using_nv_lazada_flag": 268,
            "sales_platform_using_nv_others_flag": 268,
            "sales_platform_using_nv_others_comment": 268,
            "sales_platform_using_nv_ownweb_flag": 268,
            "sales_platform_using_nv_retailshop_flag": 268,
            "sales_platform_using_nv_sendo_flag": 268,
            "sales_platform_using_nv_shopee_flag": 268,
            "sales_platform_using_nv_tiki_flag": 268,
            "sales_platform_using_nv_tiktok_flag": 268,
            "sales_platform_using_nv_zalo_flag": 268,
            "product_category_sold": 271,
            "parcel_send_out_method": 273,
            "ninja_satisfaction_order_booking_ease_of_navigation_flag": 338,
            "ninja_satisfaction_order_booking_system_reliability_flag": 338,
            "ninja_satisfaction_app_ease_of_use_flag": 338,
            "ninja_satisfaction_app_reliability_flag": 338,
            "ninja_satisfaction_pickup_driver_punctuality_flag": 338,
            "ninja_satisfaction_pickup_driver_attitude_flag": 338,
            "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 338,
            "ninja_satisfaction_drop_off_experience_flag": 338,
            "ninja_satisfaction_drop_off_ease_flag": 338,
            "ninja_satisfaction_drop_off_speed_flag": 338,
            "ninja_satisfaction_drop_off_environment_flag": 338,
            "ninja_satisfaction_drop_off_staff_attitude_flag": 338,
            "ninja_satisfaction_drop_off_accessibility_flag": 338,
            "ninja_satisfaction_delivery_area_coverage_flag": 338,
            "ninja_satisfaction_delivery_speed_flag": 338,
            "ninja_satisfaction_delivery_success_rate_flag": 338,
            "ninja_satisfaction_rider_attempts_flag": 338,
            "ninja_satisfaction_driver_attitude_flag": 338,
            "ninja_satisfaction_parcel_tracking_flag": 338,
            "ninja_satisfaction_parcel_handling_flag": 338,
            "ninja_satisfaction_rts_service_flag": 338,
            "ninja_satisfaction_rts_speed_flag": 338,
            "ninja_satisfaction_quality_of_resolution_flag": 338,
            "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 338,
            "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": 338,
            "ninja_satisfaction_ease_of_reaching_support_staff_flag": 338,
            "ninja_satisfaction_billing_accuracy_flag": 338,
            "ninja_satisfaction_speed_of_reimbursement_flag": 338,
            "ninja_satisfaction_speed_of_cod_remit_flag": 338,
            "ninja_satisfaction_pricing_cod_rate_flag": 338,
            "ninja_satisfaction_pricing_shipping_rate_flag": 338,
            "ninja_satisfaction_none_flag": 338,
            "ninja_satisfaction_support_channel": 290,
            "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": 339,
            "ninja_dissatisfaction_order_booking_system_reliability_flag": 339,
            "ninja_dissatisfaction_app_ease_of_use_flag": 339,
            "ninja_dissatisfaction_app_reliability_flag": 339,
            "ninja_dissatisfaction_pickup_driver_punctuality_flag": 339,
            "ninja_dissatisfaction_pickup_driver_attitude_flag": 339,
            "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 339,
            "ninja_dissatisfaction_drop_off_experience_flag": 339,
            "ninja_dissatisfaction_drop_off_ease_flag": 339,
            "ninja_dissatisfaction_drop_off_speed_flag": 339,
            "ninja_dissatisfaction_drop_off_environment_flag": 339,
            "ninja_dissatisfaction_drop_off_staff_attitude_flag": 339,
            "ninja_dissatisfaction_drop_off_accessibility_flag": 339,
            "ninja_dissatisfaction_delivery_area_coverage_flag": 339,
            "ninja_dissatisfaction_delivery_speed_flag": 339,
            "ninja_dissatisfaction_delivery_success_rate_flag": 339,
            "ninja_dissatisfaction_rider_attempts_flag": 339,
            "ninja_dissatisfaction_driver_attitude_flag": 339,
            "ninja_dissatisfaction_parcel_tracking_flag": 339,
            "ninja_dissatisfaction_parcel_handling_flag": 339,
            "ninja_dissatisfaction_rts_service_flag": 339,
            "ninja_dissatisfaction_rts_speed_flag": 339,
            "ninja_dissatisfaction_quality_of_resolution_flag": 339,
            "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 339,
            "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": 339,
            "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": 339,
            "ninja_dissatisfaction_billing_accuracy_flag": 339,
            "ninja_dissatisfaction_speed_of_reimbursement_flag": 339,
            "ninja_dissatisfaction_speed_of_cod_remit_flag": 339,
            "ninja_dissatisfaction_pricing_cod_rate_flag": 339,
            "ninja_dissatisfaction_pricing_shipping_rate_flag": 339,
            "ninja_dissatisfaction_none_flag": 339,
            "ninja_dissatisfaction_support_channel": 304,
            "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": 306,
            "ninja_dissatisfaction_support_queries_shipping_rates_flag": 306,
            "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": 306,
            "ninja_dissatisfaction_support_rescheduling_delivery_flag": 306,
            "ninja_dissatisfaction_support_delivery_staff_issue_flag": 306,
            "ninja_dissatisfaction_support_late_slow_delivery_flag": 306,
            "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": 306,
            "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": 306,
            "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": 306,
            "ninja_dissatisfaction_support_late_cod_remittance_flag": 306,
            "ninja_dissatisfaction_support_others_flag": 306,
            "ninja_dissatisfaction_support_others_comment": 306,
            "parcel_send_out_method_competitor": 309,
            "competitor_satisfaction_order_booking_ease_of_navigation_flag": 340,
            "competitor_satisfaction_order_booking_system_reliability_flag": 340,
            "competitor_satisfaction_app_ease_of_use_flag": 340,
            "competitor_satisfaction_app_reliability_flag": 340,
            "competitor_satisfaction_pickup_driver_punctuality_flag": 340,
            "competitor_satisfaction_pickup_driver_attitude_flag": 340,
            "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": 340,
            "competitor_satisfaction_drop_off_experience_flag": 340,
            "competitor_satisfaction_drop_off_ease_flag": 340,
            "competitor_satisfaction_drop_off_speed_flag": 340,
            "competitor_satisfaction_drop_off_environment_flag": 340,
            "competitor_satisfaction_drop_off_staff_attitude_flag": 340,
            "competitor_satisfaction_drop_off_accessibility_flag": 340,
            "competitor_satisfaction_delivery_area_coverage_flag": 340,
            "competitor_satisfaction_delivery_speed_flag": 340,
            "competitor_satisfaction_delivery_success_rate_flag": 340,
            "competitor_satisfaction_rider_attempts_flag": 340,
            "competitor_satisfaction_driver_attitude_flag": 340,
            "competitor_satisfaction_parcel_tracking_flag": 340,
            "competitor_satisfaction_parcel_handling_flag": 340,
            "competitor_satisfaction_rts_service_flag": 340,
            "competitor_satisfaction_rts_speed_flag": 340,
            "competitor_satisfaction_quality_of_resolution_flag": 340,
            "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 340,
            "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": 340,
            "competitor_satisfaction_ease_of_reaching_support_staff_flag": 340,
            "competitor_satisfaction_billing_accuracy_flag": 340,
            "competitor_satisfaction_speed_of_reimbursement_flag": 340,
            "competitor_satisfaction_speed_of_cod_remit_flag": 340,
            "competitor_satisfaction_pricing_cod_rate_flag": 340,
            "competitor_satisfaction_pricing_shipping_rate_flag": 340,
            "competitor_satisfaction_none_flag": 340,
            "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": 341,
            "competitor_dissatisfaction_order_booking_system_reliability_flag": 341,
            "competitor_dissatisfaction_app_ease_of_use_flag": 341,
            "competitor_dissatisfaction_app_reliability_flag": 341,
            "competitor_dissatisfaction_pickup_driver_punctuality_flag": 341,
            "competitor_dissatisfaction_pickup_driver_attitude_flag": 341,
            "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": 341,
            "competitor_dissatisfaction_drop_off_experience_flag": 341,
            "competitor_dissatisfaction_drop_off_ease_flag": 341,
            "competitor_dissatisfaction_drop_off_speed_flag": 341,
            "competitor_dissatisfaction_drop_off_environment_flag": 341,
            "competitor_dissatisfaction_drop_off_staff_attitude_flag": 341,
            "competitor_dissatisfaction_drop_off_accessibility_flag": 341,
            "competitor_dissatisfaction_delivery_area_coverage_flag": 341,
            "competitor_dissatisfaction_delivery_speed_flag": 341,
            "competitor_dissatisfaction_delivery_success_rate_flag": 341,
            "competitor_dissatisfaction_rider_attempts_flag": 341,
            "competitor_dissatisfaction_driver_attitude_flag": 341,
            "competitor_dissatisfaction_parcel_tracking_flag": 341,
            "competitor_dissatisfaction_parcel_handling_flag": 341,
            "competitor_dissatisfaction_rts_service_flag": 341,
            "competitor_dissatisfaction_rts_speed_flag": 341,
            "competitor_dissatisfaction_quality_of_resolution_flag": 341,
            "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": 341,
            "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": 341,
            "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": 341,
            "competitor_dissatisfaction_billing_accuracy_flag": 341,
            "competitor_dissatisfaction_speed_of_reimbursement_flag": 341,
            "competitor_dissatisfaction_speed_of_cod_remit_flag": 341,
            "competitor_dissatisfaction_pricing_cod_rate_flag": 341,
            "competitor_dissatisfaction_pricing_shipping_rate_flag": 341,
            "competitor_dissatisfaction_none_flag": 341,
            "fs_am_flag": 344,
            "cx_force_belonging": 373,
            "cx_force_certainty": 373,
            "cx_force_control": 373,
            "cx_force_enjoyment": 373,
            "cx_force_fair_treatment": 373,
            "cx_force_none": 373,
            "cx_force_status": 373,
        },
    ],
    "column_config": {
        "legacy_shipper_id": {"type": "flat"},
        "total_parcels_shipped_past_week": {"type": "flat"},
        "shipment_frequency": {"type": "flat"},
        "parcel_send_out_method": {"type": "flat"},
        "area_coverage_factor_flag": {"type": "boolean", "regex": "Area coverage", "group": "provider_choice_factors"},
        "proximity_of_collection_or_drop_off_points_factor_flag": {
            "type": "boolean",
            "regex": "Proximity of collection / drop-off points",
            "group": "provider_choice_factors",
        },
        "parcel_pickup_speed_factor_flag": {
            "type": "boolean",
            "regex": "Parcel pickup speed",
            "group": "provider_choice_factors",
        },
        "punctuality_of_parcel_pickup_factor_flag": {
            "type": "boolean",
            "regex": "Punctuality of parcel pickup",
            "group": "provider_choice_factors",
        },
        "delivery_speed_factor_flag": {
            "type": "boolean",
            "regex": "Delivery speed",
            "group": "provider_choice_factors",
        },
        "punctuality_of_delivery_factor_flag": {
            "type": "boolean",
            "regex": "Punctuality of delivery",
            "group": "provider_choice_factors",
        },
        "parcel_tracking_factor_flag": {
            "type": "boolean",
            "regex": "Parcel tracking",
            "group": "provider_choice_factors",
        },
        "order_system_ease_of_use_factor_flag": {
            "type": "boolean",
            "regex": "Order system",
            "group": "provider_choice_factors",
        },
        "recovery_and_issue_resolution_factor_flag": {
            "type": "boolean",
            "regex": "Recovery and issue resolution",
            "group": "provider_choice_factors",
        },
        "competitive_prices_factor_flag": {
            "type": "boolean",
            "regex": "Competitive prices",
            "group": "provider_choice_factors",
        },
        "customer_service_or_account_management_factor_flag": {
            "type": "boolean",
            "regex": "Customer service / account management",
            "group": "provider_choice_factors",
        },
        "brand_reputation_and_trust_factor_flag": {
            "type": "boolean",
            "regex": "Brand reputation and trust",
            "group": "provider_choice_factors",
        },
        "parcel_handling_factor_flag": {
            "type": "boolean",
            "regex": "Parcel handling",
            "group": "provider_choice_factors",
        },
        "ninja_recommendation_score": {"type": "flat"},
        "ninja_satisfaction_score": {"type": "flat"},
        "ninja_satisfaction_score_order_booking_ease_of_navigation": {
            "type": "subtype",
            "detail": "Order booking system - Ease of navigation",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_order_booking_system_reliability": {
            "type": "subtype",
            "detail": "Order booking system - System reliability with no downtime",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_parcel_tracking": {
            "type": "subtype",
            "detail": "Parcel tracking feature",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_pickup_driver_punctuality": {
            "type": "subtype",
            "detail": "Pick up - Punctuality of parcel pickup (according to agreed schedule)",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_pickup_driver_attitude": {
            "type": "subtype",
            "detail": "Pick up - Attitude of pick up staff",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_drop_off_experience": {
            "type": "subtype",
            "detail": "Drop off - Overall experience when dropping off parcels",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_drop_off_ease": {
            "type": "subtype",
            "detail": "Drop off - Ease of drop off process",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_drop_off_accessibility": {
            "type": "subtype",
            "detail": "Drop off - Accessibility of drop off points",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_delivery_speed": {
            "type": "subtype",
            "detail": "Speed of delivery",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_rts_service": {
            "type": "subtype",
            "detail": "Return-To-Shipper (RTS) service",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_quality_of_resolution": {
            "type": "subtype",
            "detail": "Account manager/customer service/shipper support staff - Ability to help to solve my problem",
            "group": "satisfaction_scores",
        },
        "ninja_satisfaction_score_billing_accuracy": {
            "type": "subtype",
            "detail": "Billing - Accuracy",
            "group": "satisfaction_scores",
        },
        "ninja_service_dissatisfaction_reason": {"type": "flat"},
        "ninja_service_comment": {"type": "flat"},
        "competitor_name": {"type": "flat"},
        "competitor_recommendation_score": {"type": "flat"},
        "competitor_satisfaction_score": {"type": "flat"},
        "competitor_satisfaction_score_order_booking_ease_of_navigation": {
            "type": "subtype",
            "detail": "Order booking system - Ease of navigation",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_order_booking_system_reliability": {
            "type": "subtype",
            "detail": "Order booking system - System reliability with no downtime",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_parcel_tracking": {
            "type": "subtype",
            "detail": "Parcel tracking feature",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_pickup_driver_punctuality": {
            "type": "subtype",
            "detail": "Pick up - Punctuality of parcel pickup (according to agreed schedule)",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_pickup_driver_attitude": {
            "type": "subtype",
            "detail": "Pick up - Attitude of pick up staff",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_drop_off_experience": {
            "type": "subtype",
            "detail": "Drop off - Overall experience when dropping off parcels",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_drop_off_ease": {
            "type": "subtype",
            "detail": "Drop off - Ease of drop off process",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_drop_off_accessibility": {
            "type": "subtype",
            "detail": "Drop off - Accessibility of drop off points",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_delivery_speed": {
            "type": "subtype",
            "detail": "Speed of delivery",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_rts_service": {
            "type": "subtype",
            "detail": "Return-To-Shipper (RTS) service",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_quality_of_resolution": {
            "type": "subtype",
            "detail": "Account manager/customer service/shipper support staff - Ability to help to solve my problem",
            "group": "satisfaction_scores",
        },
        "competitor_satisfaction_score_billing_accuracy": {
            "type": "subtype",
            "detail": "Billing - Accuracy",
            "group": "satisfaction_scores",
        },
        "competitor_service_dissatisfaction_reason": {"type": "flat"},
        "competitor_service_comment": {"type": "flat"},
        "am_vs_non_serviceable": {"type": "flat"},
        "sales_platform_amazon_flag": {"type": "boolean", "regex": "Amazon", "group": "sales_platforms"},
        "sales_platform_beautymnl_flag": {"type": "boolean", "regex": "Beauty", "group": "sales_platforms"},
        "sales_platform_bukalapak_flag": {"type": "boolean", "regex": "Bukalapak", "group": "sales_platforms"},
        "sales_platform_carousell_flag": {"type": "boolean", "regex": "Carousell", "group": "sales_platforms"},
        "sales_platform_chilindo_flag": {"type": "boolean", "regex": "Chilindo", "group": "sales_platforms"},
        "sales_platform_ebay_flag": {"type": "boolean", "regex": "eBay", "group": "sales_platforms"},
        "sales_platform_facebook_flag": {"type": "boolean", "regex": "Facebook", "group": "sales_platforms"},
        "sales_platform_instagram_flag": {"type": "boolean", "regex": "Instagram", "group": "sales_platforms"},
        "sales_platform_jdcentral_flag": {"type": "boolean", "regex": "JD Central", "group": "sales_platforms"},
        "sales_platform_jd_flag": {"type": "boolean", "regex": "JD.ID", "group": "sales_platforms"},
        "sales_platform_kaidee_flag": {"type": "boolean", "regex": "Kaidee", "group": "sales_platforms"},
        "sales_platform_lazada_flag": {"type": "boolean", "regex": "Lazada", "group": "sales_platforms"},
        "sales_platform_lelong_flag": {"type": "boolean", "regex": "Lelong", "group": "sales_platforms"},
        "sales_platform_others_flag": {"type": "boolean", "regex": "[Oo]ther", "group": "sales_platforms"},
        "sales_platform_others_comment": {"type": "subtype", "detail": "other", "group": "sales_platforms_comment"},
        "sales_platform_ownweb_flag": {"type": "boolean", "regex": "Own website", "group": "sales_platforms"},
        "sales_platform_prestomall_flag": {"type": "boolean", "regex": "PrestoMall", "group": "sales_platforms"},
        "sales_platform_pgmall_flag": {"type": "boolean", "regex": "PG Mall", "group": "sales_platforms"},
        "sales_platform_qoo10_flag": {"type": "boolean", "regex": "Qoo10", "group": "sales_platforms"},
        "sales_platform_retailshop_flag": {"type": "boolean", "regex": "Retail", "group": "sales_platforms"},
        "sales_platform_sendo_flag": {"type": "boolean", "regex": "Sendo", "group": "sales_platforms"},
        "sales_platform_shopee_flag": {"type": "boolean", "regex": "Shopee", "group": "sales_platforms"},
        "sales_platform_telegram_flag": {"type": "boolean", "regex": "Telegram", "group": "sales_platforms"},
        "sales_platform_tiki_flag": {"type": "boolean", "regex": "Tiki", "group": "sales_platforms"},
        "sales_platform_tiktok_flag": {"type": "boolean", "regex": "TikTok", "group": "sales_platforms"},
        "sales_platform_tokko_flag": {"type": "boolean", "regex": "Tokko", "group": "sales_platforms"},
        "sales_platform_tokopedia_flag": {"type": "boolean", "regex": "Tokopedia", "group": "sales_platforms"},
        "sales_platform_vettons_flag": {"type": "boolean", "regex": "Vettons", "group": "sales_platforms"},
        "sales_platform_wechat_flag": {"type": "boolean", "regex": "Wechat", "group": "sales_platforms"},
        "sales_platform_zalo_flag": {"type": "boolean", "regex": "Zalo", "group": "sales_platforms"},
        "sales_platform_zalora_flag": {"type": "boolean", "regex": "Zalora", "group": "sales_platforms"},
        "sales_platform_zilingo_flag": {"type": "boolean", "regex": "Zilingo", "group": "sales_platforms"},
        "sales_platform_using_nv_amazon_flag": {
            "type": "boolean",
            "regex": "Amazon",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_beautymnl_flag": {
            "type": "boolean",
            "regex": "Beauty",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_bukalapak_flag": {
            "type": "boolean",
            "regex": "Bukalapak",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_carousell_flag": {
            "type": "boolean",
            "regex": "Carousell",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_chilindo_flag": {
            "type": "boolean",
            "regex": "Chilindo",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_ebay_flag": {"type": "boolean", "regex": "eBay", "group": "sales_platforms_using_nv"},
        "sales_platform_using_nv_facebook_flag": {
            "type": "boolean",
            "regex": "Facebook",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_instagram_flag": {
            "type": "boolean",
            "regex": "Instagram",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_jdcentral_flag": {
            "type": "boolean",
            "regex": "JD Central",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_jd_flag": {"type": "boolean", "regex": "JD.ID", "group": "sales_platforms_using_nv"},
        "sales_platform_using_nv_kaidee_flag": {
            "type": "boolean",
            "regex": "Kaidee",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_lazada_flag": {
            "type": "boolean",
            "regex": "Lazada",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_lelong_flag": {
            "type": "boolean",
            "regex": "Lelong",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_others_flag": {
            "type": "boolean",
            "regex": "[Oo]ther",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_others_comment": {
            "type": "subtype",
            "detail": "other",
            "group": "sales_platforms_comment",
        },
        "sales_platform_using_nv_ownweb_flag": {
            "type": "boolean",
            "regex": "Own website",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_prestomall_flag": {
            "type": "boolean",
            "regex": "PrestoMall",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_pgmall_flag": {
            "type": "boolean",
            "regex": "PG Mall",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_qoo10_flag": {
            "type": "boolean",
            "regex": "Qoo10",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_retailshop_flag": {
            "type": "boolean",
            "regex": "Retail",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_sendo_flag": {
            "type": "boolean",
            "regex": "Sendo",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_shopee_flag": {
            "type": "boolean",
            "regex": "Shopee",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_telegram_flag": {
            "type": "boolean",
            "regex": "Telegram",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_tiki_flag": {"type": "boolean", "regex": "Tiki", "group": "sales_platforms_using_nv"},
        "sales_platform_using_nv_tiktok_flag": {
            "type": "boolean",
            "regex": "TikTok",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_tokko_flag": {
            "type": "boolean",
            "regex": "Tokko",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_tokopedia_flag": {
            "type": "boolean",
            "regex": "Tokopedia",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_vettons_flag": {
            "type": "boolean",
            "regex": "Vettons",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_wechat_flag": {
            "type": "boolean",
            "regex": "Wechat",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_zalo_flag": {"type": "boolean", "regex": "Zalo", "group": "sales_platforms_using_nv"},
        "sales_platform_using_nv_zalora_flag": {
            "type": "boolean",
            "regex": "Zalora",
            "group": "sales_platforms_using_nv",
        },
        "sales_platform_using_nv_zilingo_flag": {
            "type": "boolean",
            "regex": "Zilingo",
            "group": "sales_platforms_using_nv",
        },
        "product_category_sold": {"type": "flat"},
        "ninja_satisfaction_order_booking_ease_of_navigation_flag": {
            "type": "boolean",
            "regex": "Ease of navigation",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_order_booking_system_reliability_flag": {
            "type": "boolean",
            "regex": "System reliability",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_app_ease_of_use_flag": {
            "type": "boolean",
            "regex": "Ease of use",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_app_reliability_flag": {
            "type": "boolean",
            "regex": "App reliability",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_pickup_driver_punctuality_flag": {
            "type": "boolean",
            "regex": "Punctuality of parcel pickup",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_pickup_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of pick up staff",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_pickup_ease_to_arrange_pick_ups_flag": {
            "type": "boolean",
            "regex": "Ease to arrange pick ups",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_experience_flag": {
            "type": "boolean",
            "regex": "Overall experience when dropping off parcels",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_ease_flag": {
            "type": "boolean",
            "regex": "Ease of drop off process",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_speed_flag": {
            "type": "boolean",
            "regex": "speed of processing parcels",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_environment_flag": {
            "type": "boolean",
            "regex": "Environment",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_staff_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of staff",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_drop_off_accessibility_flag": {
            "type": "boolean",
            "regex": "Accessibility of drop off points",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_delivery_area_coverage_flag": {
            "type": "boolean",
            "regex": "Area coverage",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_delivery_speed_flag": {
            "type": "boolean",
            # The data comes like ["Speed of issue resolution for other queries","Speed"]. We include quotation marks
            # for `"Speed"` because we don't want to wrongly pick up other factors that contain the word `Speed`.
            "regex": '(?i)Delivery Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_delivery_success_rate_flag": {
            "type": "boolean",
            "regex": "Success rate",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_rider_attempts_flag": {
            "type": "boolean",
            "regex": "Attempts by rider",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of driver",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_parcel_tracking_flag": {
            "type": "boolean",
            "regex": "Tracking feature",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_parcel_handling_flag": {
            "type": "boolean",
            "regex": "Handling",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_rts_service_flag": {
            "type": "boolean",
            "regex": "Overall service",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_rts_speed_flag": {
            "type": "boolean",
            "regex": 'RTS Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_quality_of_resolution_flag": {
            "type": "boolean",
            "regex": "Ability to help to solve my problem",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": {
            "type": "boolean",
            "regex": "Speed of resolution for late/lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_speed_of_resolution_for_other_queries_flag": {
            "type": "boolean",
            "regex": "Speed of issue resolution for other queries",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_ease_of_reaching_support_staff_flag": {
            "type": "boolean",
            "regex": "Ease of reaching support staff",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_billing_accuracy_flag": {
            "type": "boolean",
            "regex": "Accuracy",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_speed_of_reimbursement_flag": {
            "type": "boolean",
            "regex": "Speed of reimbursement for lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_speed_of_cod_remit_flag": {
            "type": "boolean",
            "regex": "Speed of COD remittance",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_pricing_cod_rate_flag": {
            "type": "boolean",
            "regex": "COD rates",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_pricing_shipping_rate_flag": {
            "type": "boolean",
            "regex": "Shipping rates",
            "group": "satisfaction_factors",
        },
        "ninja_satisfaction_none_flag": {
            "type": "boolean",
            "regex": "None of the above",
        },
        "ninja_satisfaction_support_channel": {"type": "flat"},
        "ninja_dissatisfaction_order_booking_ease_of_navigation_flag": {
            "type": "boolean",
            "regex": "Ease of navigation",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_order_booking_system_reliability_flag": {
            "type": "boolean",
            "regex": "System reliability",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_app_ease_of_use_flag": {
            "type": "boolean",
            "regex": "Ease of use",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_app_reliability_flag": {
            "type": "boolean",
            "regex": "App reliability",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_pickup_driver_punctuality_flag": {
            "type": "boolean",
            "regex": "Punctuality of parcel pickup",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_pickup_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of pick up staff",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": {
            "type": "boolean",
            "regex": "Ease to arrange pick ups",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_experience_flag": {
            "type": "boolean",
            "regex": "Overall experience when dropping off parcels",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_ease_flag": {
            "type": "boolean",
            "regex": "Ease of drop off process",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_speed_flag": {
            "type": "boolean",
            "regex": "speed of processing parcels",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_environment_flag": {
            "type": "boolean",
            "regex": "Environment",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_staff_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of staff",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_drop_off_accessibility_flag": {
            "type": "boolean",
            "regex": "Accessibility of drop off points",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_delivery_area_coverage_flag": {
            "type": "boolean",
            "regex": "Area coverage",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_delivery_speed_flag": {
            "type": "boolean",
            "regex": '(?i)Delivery Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_delivery_success_rate_flag": {
            "type": "boolean",
            "regex": "Success rate",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_rider_attempts_flag": {
            "type": "boolean",
            "regex": "Attempts by rider",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of driver",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_parcel_tracking_flag": {
            "type": "boolean",
            "regex": "Tracking feature",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_parcel_handling_flag": {
            "type": "boolean",
            "regex": "Handling",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_rts_service_flag": {
            "type": "boolean",
            "regex": "Overall service",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_rts_speed_flag": {
            "type": "boolean",
            "regex": 'RTS Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_quality_of_resolution_flag": {
            "type": "boolean",
            "regex": "Ability to help to solve my problem",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": {
            "type": "boolean",
            "regex": "Speed of resolution for late/lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_speed_of_resolution_for_other_queries_flag": {
            "type": "boolean",
            "regex": "Speed of issue resolution for other queries",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_ease_of_reaching_support_staff_flag": {
            "type": "boolean",
            "regex": "Ease of reaching support staff",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_billing_accuracy_flag": {
            "type": "boolean",
            "regex": "Accuracy",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_speed_of_reimbursement_flag": {
            "type": "boolean",
            "regex": "Speed of reimbursement for lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_speed_of_cod_remit_flag": {
            "type": "boolean",
            "regex": "Speed of COD remittance",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_pricing_cod_rate_flag": {
            "type": "boolean",
            "regex": "COD rates",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_pricing_shipping_rate_flag": {
            "type": "boolean",
            "regex": "Shipping rates",
            "group": "satisfaction_factors",
        },
        "ninja_dissatisfaction_none_flag": {
            "type": "boolean",
            "regex": "None of the above",
        },
        "ninja_dissatisfaction_support_channel": {"type": "flat"},
        "ninja_dissatisfaction_support_problems_dashboard_order_creation_flag": {
            "type": "boolean",
            "regex": "Problems on Ninja dashboard/order creation",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_queries_shipping_rates_flag": {
            "type": "boolean",
            "regex": "Queries on shipping rates",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_rescheduling_pick_up_late_no_turn_up_flag": {
            "type": "boolean",
            "regex": "Rescheduling of pick up when rider/driver was late/did not turn up",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_rescheduling_delivery_flag": {
            "type": "boolean",
            "regex": "Rescheduling delivery",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_delivery_staff_issue_flag": {
            "type": "boolean",
            "regex": "Issues related to the delivery staff",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_late_slow_delivery_flag": {
            "type": "boolean",
            "regex": "Late/slow delivery",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_missing_parcel_tracking_delay_confirmation_lost_damaged_parcel_flag": {
            "type": "boolean",
            "regex": "Tracking of missing parcel / Delay in confirmation of lost/damaged parcels",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_delay_reimbursement_lost_damaged_parcels_flag": {
            "type": "boolean",
            "regex": "Delay in reimbursement of lost/damaged parcels",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_inaccurate_billing_issues_flag": {
            "type": "boolean",
            "regex": "Inaccurate billing issues",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_late_cod_remittance_flag": {
            "type": "boolean",
            "regex": "Late COD remittance",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_others_flag": {
            "type": "boolean",
            "regex": "other",
            "group": "support_dissatisfaction_factors",
        },
        "ninja_dissatisfaction_support_others_comment": {"type": "subtype", "detail": "other"},
        "parcel_send_out_method_competitor": {"type": "flat"},
        "competitor_satisfaction_order_booking_ease_of_navigation_flag": {
            "type": "boolean",
            "regex": "Ease of navigation",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_order_booking_system_reliability_flag": {
            "type": "boolean",
            "regex": "System reliability",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_app_ease_of_use_flag": {
            "type": "boolean",
            "regex": "Ease of use",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_app_reliability_flag": {
            "type": "boolean",
            "regex": "App reliability",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_pickup_driver_punctuality_flag": {
            "type": "boolean",
            "regex": "Punctuality of parcel pickup",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_pickup_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of pick up staff",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_pickup_ease_to_arrange_pick_ups_flag": {
            "type": "boolean",
            "regex": "Ease to arrange pick ups",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_experience_flag": {
            "type": "boolean",
            "regex": "Overall experience when dropping off parcels",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_ease_flag": {
            "type": "boolean",
            "regex": "Ease of drop off process",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_speed_flag": {
            "type": "boolean",
            "regex": "speed of processing parcels",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_environment_flag": {
            "type": "boolean",
            "regex": "Environment",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_staff_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of staff",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_drop_off_accessibility_flag": {
            "type": "boolean",
            "regex": "Accessibility of drop off points",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_delivery_area_coverage_flag": {
            "type": "boolean",
            "regex": "Area coverage",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_delivery_speed_flag": {
            "type": "boolean",
            "regex": '(?i)Delivery Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_delivery_success_rate_flag": {
            "type": "boolean",
            "regex": "Success rate",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_rider_attempts_flag": {
            "type": "boolean",
            "regex": "Attempts by rider",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of driver",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_parcel_tracking_flag": {
            "type": "boolean",
            "regex": "Tracking feature",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_parcel_handling_flag": {
            "type": "boolean",
            "regex": "Handling",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_rts_service_flag": {
            "type": "boolean",
            "regex": "Overall service",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_rts_speed_flag": {
            "type": "boolean",
            "regex": 'RTS Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_quality_of_resolution_flag": {
            "type": "boolean",
            "regex": "Ability to help to solve my problem",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": {
            "type": "boolean",
            "regex": "Speed of resolution for late/lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_speed_of_resolution_for_other_queries_flag": {
            "type": "boolean",
            "regex": "Speed of issue resolution for other queries",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_ease_of_reaching_support_staff_flag": {
            "type": "boolean",
            "regex": "Ease of reaching support staff",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_billing_accuracy_flag": {
            "type": "boolean",
            "regex": "Accuracy",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_speed_of_reimbursement_flag": {
            "type": "boolean",
            "regex": "Speed of reimbursement for lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_speed_of_cod_remit_flag": {
            "type": "boolean",
            "regex": "Speed of COD remittance",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_pricing_cod_rate_flag": {
            "type": "boolean",
            "regex": "COD rates",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_pricing_shipping_rate_flag": {
            "type": "boolean",
            "regex": "Shipping rates",
            "group": "satisfaction_factors",
        },
        "competitor_satisfaction_none_flag": {
            "type": "boolean",
            "regex": "None of the above",
        },
        "competitor_dissatisfaction_order_booking_ease_of_navigation_flag": {
            "type": "boolean",
            "regex": "Ease of navigation",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_order_booking_system_reliability_flag": {
            "type": "boolean",
            "regex": "System reliability",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_app_ease_of_use_flag": {
            "type": "boolean",
            "regex": "Ease of use",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_app_reliability_flag": {
            "type": "boolean",
            "regex": "App reliability",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_pickup_driver_punctuality_flag": {
            "type": "boolean",
            "regex": "Punctuality of parcel pickup",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_pickup_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of pick up staff",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_pickup_ease_to_arrange_pick_ups_flag": {
            "type": "boolean",
            "regex": "Ease to arrange pick ups",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_experience_flag": {
            "type": "boolean",
            "regex": "Overall experience when dropping off parcels",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_ease_flag": {
            "type": "boolean",
            "regex": "Ease of drop off process",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_speed_flag": {
            "type": "boolean",
            "regex": "speed of processing parcels",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_environment_flag": {
            "type": "boolean",
            "regex": "Environment",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_staff_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of staff",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_drop_off_accessibility_flag": {
            "type": "boolean",
            "regex": "Accessibility of drop off points",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_delivery_area_coverage_flag": {
            "type": "boolean",
            "regex": "Area coverage",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_delivery_speed_flag": {
            "type": "boolean",
            "regex": '(?i)Delivery Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_delivery_success_rate_flag": {
            "type": "boolean",
            "regex": "Success rate",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_rider_attempts_flag": {
            "type": "boolean",
            "regex": "Attempts by rider",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_driver_attitude_flag": {
            "type": "boolean",
            "regex": "Attitude of driver",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_parcel_tracking_flag": {
            "type": "boolean",
            "regex": "Tracking feature",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_parcel_handling_flag": {
            "type": "boolean",
            "regex": "Handling",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_rts_service_flag": {
            "type": "boolean",
            "regex": "Overall service",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_rts_speed_flag": {
            "type": "boolean",
            "regex": 'RTS Speed|"Speed"',
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_quality_of_resolution_flag": {
            "type": "boolean",
            "regex": "Ability to help to solve my problem",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_speed_of_resolution_for_late_lost_missing_parcel_flag": {
            "type": "boolean",
            "regex": "Speed of resolution for late/lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_speed_of_resolution_for_other_queries_flag": {
            "type": "boolean",
            "regex": "Speed of issue resolution for other queries",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_ease_of_reaching_support_staff_flag": {
            "type": "boolean",
            "regex": "Ease of reaching support staff",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_billing_accuracy_flag": {
            "type": "boolean",
            "regex": "Accuracy",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_speed_of_reimbursement_flag": {
            "type": "boolean",
            "regex": "Speed of reimbursement for lost/damaged parcels",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_speed_of_cod_remit_flag": {
            "type": "boolean",
            "regex": "Speed of COD remittance",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_pricing_cod_rate_flag": {
            "type": "boolean",
            "regex": "COD rates",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_pricing_shipping_rate_flag": {
            "type": "boolean",
            "regex": "Shipping rates",
            "group": "satisfaction_factors",
        },
        "competitor_dissatisfaction_none_flag": {
            "type": "boolean",
            "regex": "None of the above",
        },
        "fs_am_flag": {"type": "flat"},
        "cx_force_belonging": {
            "type": "boolean",
            "regex": "Makes me feel like I have a strong relationship with them",
            "group": "cx_force",
        },
        "cx_force_certainty": {
            "type": "boolean",
            "regex": "Is transparent and clear",
            "group": "cx_force",
        },
        "cx_force_control": {
            "type": "boolean",
            "regex": "Makes me feel like I am in control of the situation",
            "group": "cx_force",
        },
        "cx_force_enjoyment": {
            "type": "boolean",
            "regex": "Makes my life easier so that I can focus on other things",
            "group": "cx_force",
        },
        "cx_force_fair_treatment": {
            "type": "boolean",
            "regex": "Treats me fairly",
            "group": "cx_force",
        },
        "cx_force_none": {
            "type": "boolean",
            "regex": "None of the above",
            "group": "cx_force",
        },
        "cx_force_status": {
            "type": "boolean",
            "regex": "Makes me feel valued & respected",
            "group": "cx_force",
        },
    },
    "business_region_sector_map": [
        {
            "system_id": "my",
            "sector_to_business_regions": {
                "North": ("Perlis", "Kedah", "Pulau Pinang", "Penang"),
                "Central": ("Perak", "Melaka", "Negeri Sembilan"),
                "KV": ("Selangor", "Wilayah Persekutuan", "Kuala Lumpur"),
                "East": ("Kelantan", "Terengganu", "Pahang"),
                "South": ("Johor",),
                "East Malaysia": ("Sarawak", "Sabah"),
            },
        },
        {
            "system_id": "vn",
            "sector_to_business_regions": {
                "Hồ Chí Minh": ("Hồ Chí Minh",),
                "Hà Nội": ("Hà Nội",),
                "Bắc Trung Bộ": ("Hà Tĩnh", "Nghệ An", "Quảng Bình", "Quảng Trị", "Thanh Hóa", "Thừa Thiên Huế"),
                "Đông Bắc Bộ": (
                    "Bắc Giang",
                    "Bắc Kạn",
                    "Cao Bằng",
                    "Hà Giang",
                    "Lạng Sơn",
                    "Phú Thọ",
                    "Quảng Ninh",
                    "Thái Nguyên",
                    "Tuyên Quang",
                ),
                "Đồng Bằng Sông Cửu Long": (
                    "An Giang",
                    "Bạc Liêu",
                    "Bến Tre",
                    "Cà Mau",
                    "Cần Thơ",
                    "Đồng Tháp",
                    "Hậu Giang",
                    "Kiên Giang",
                    "Long An",
                    "Sóc Trăng",
                    "Tiền Giang",
                    "Trà Vinh",
                    "Vĩnh Long",
                ),
                "Đồng bằng sông hồng": (
                    "Bắc Ninh",
                    "Hà Nam",
                    "Hải Dương",
                    "Hải Phòng",
                    "Hưng Yên",
                    "Nam Định",
                    "Ninh Bình",
                    "Thái Bình",
                    "Vĩnh Phúc",
                ),
                "Đông Nam Bộ": ("Bà Rịa - Vũng Tàu", "Bình Dương", "Bình Phước", "Đồng Nai", "Tây Ninh"),
                "Nam Trung Bộ": (
                    "Bình Định",
                    "Bình Thuận",
                    "Đà Nẵng",
                    "Khánh Hòa",
                    "Ninh Thuận",
                    "Phú Yên",
                    "Quảng Nam",
                    "Quảng Ngãi",
                ),
                "Tây Bắc Bộ": (
                    "Điện Biên",
                    "Hòa Bình",
                    "Lai Châu",
                    "Lào Cai",
                    "Sơn La",
                    "Yên Bái",
                ),
                "Tây Nguyên": ("Đắk Nông", "Đắk Lắk", "Gia Lai", "Lâm Đồng", "Kon Tum"),
            },
        },
        {
            "system_id": "th",
            "sector_to_business_regions": {
                "BKKK": ("Bangkok",),
                "GBKK": ("Samut Prakan", "Nonthaburi", "Samut prakan", "Pathum Thani"),
                "UCTL": (
                    "Ang Thong",
                    "Chai nat",
                    "Chai Nat",
                    "Lopburi",
                    "Sing Buri",
                    "Phetchabun",
                    "Samut Sakhon",
                    "Saraburi",
                    "Uthai Thani",
                    "Nakhon Pathom",
                    "Nakhon Nayok",
                    "Phra Nakhon Si Ayutthaya",
                    "Suphan Buri",
                    "NAKHON SAWAN",
                    "Nakhon Sawan",
                ),
                "UEST": ("Chonburi", "Rayong", "Chanthaburi", "Trat", "Chachoengsao", "Prachin Buri", "Sa Kaeo"),
                "UNEL": ("Nakhon Ratchasima", "Buriram", "Surin", "Sisaket", "Ubon Ratchathani", "Chaiyaphum"),
                "UNEU": (
                    "Loei",
                    "Maha Sarakham",
                    "Udon Thani",
                    "Yasothon",
                    "Nong Bua Lamphu",
                    "Amnat Charoen",
                    "Roi Et",
                    "Mukdahan",
                    "Bueng Kan",
                    "Nakhon Phanom",
                    "Kalasin",
                    "Sakon Nakhon",
                    "Khon Kaen",
                    "Nong Khai",
                ),
                "UNHU": ("Chiang Mai", "Lamphun", "Lampang", "Phayao", "Chiang Rai", "Mae Hong Son"),
                "USHL": (
                    "Nakhon Si Thammarat",
                    "Krabi",
                    "Songkhla",
                    "Satun",
                    "Trang",
                    "Phatthalung",
                    "Pattani",
                    "Yala",
                    "Narathiwat",
                ),
                "USHU": ("Phang Nga", "Phuket", "Surat Thani", "Ranong", "Chumphon", "Phang Nga"),
                "UWST": ("Ratchaburi", "Kanchanaburi", "Samut Songkhram", "Phetchaburi", "Prachuap Khiri Khan"),
                "UNHL": ("Tak", "Sukhothai", "Phichit", "Uttaradit", "Phrae", "Phitsanulok", "Kamphaeng Phet"),
            },
        },
        {
            "system_id": "id",
            "sector_to_business_regions": {
                "Sumatra": (
                    "Aceh",
                    "Bengkulu",
                    "Jambi",
                    "Kepulauan Riau",
                    "Riau",
                    "Sumatera Barat",
                    "Sumatera Selatan",
                    "Lampung",
                    "Sumatera Utara",
                ),
                "Java": ("Jawa Barat", "Jawa Tengah", "DI Yogyakarta", "Jawa Timur", "Banten"),
                "Bali": ("Bali",),
                "Kalimantan": (
                    "Kalimantan Barat",
                    "Kalimantan Timur",
                    "Kalimantan Tengah",
                    "Kalimantan Utara",
                    "Kalimantan Selatan",
                ),
                "Sulawesi": (
                    "Gorontalo",
                    "Sulawesi Barat",
                    "Sulawesi Selatan",
                    "Sulawesi Tengah",
                    "Sulawesi Tenggara",
                    "Sulawesi Utara",
                ),
                "Nusa Tenggara": ("Nusa Tenggara Barat", "Nusa Tenggara Timur"),
                "Papua": ("Papua", "Papua Barat"),
                "Bangka Belitung": ("Kepulauan Bangka Belitung",),
                "Jakarta": ("DKI Jakarta",),
                "Maluku": ("Maluku", "Maluku Utara"),
            },
        },
        {
            "system_id": "ph",
            "sector_to_business_regions": {
                "Metro Manila": ("Metro Manila", "Pasay", "Pasig", "Paranaque", "Manila"),
                "North Luzon": (
                    "Ilocos Norte",
                    "Pangasinan",
                    "Quirino",
                    "Ilocos Sur",
                    "La Union",
                    "Abra",
                    "Mountain Province",
                    "Kalinga",
                    "Nueva Vizcaya",
                    "Benguet",
                ),
                "South Luzon": (
                    "Camarines Sur",
                    "Marinduque",
                    "Palawan",
                    "Albay",
                    "Laguna",
                    "Oriental Mindoro",
                    "Masbate",
                    "Camarines Norte",
                    "Quezon",
                    "Catanduanes",
                    "Naga",
                    "Batangas",
                    "Sorsogon",
                    "Occidental Mindoro",
                ),
                "Central Luzon": ("Mexico", "Pampanga", "Aurora", "Zambales", "Bataan", "Tarlac", "Nueva Ecija"),
                "Visayas": (
                    "Isabela",
                    "Aklan",
                    "Leyte",
                    "Iloilo",
                    "Southern Leyte",
                    "Northern Samar",
                    "Biliran",
                    "Negros Occidental",
                    "Capiz",
                    "Eastern Samar",
                    "Samar / Western Samar",
                    "Guimaras",
                    "Romblon",
                    "Bohol",
                    "Antique",
                    "Cebu",
                    "Negros Oriental",
                    "Siquijor",
                ),
                "Mindanao": (
                    "Misamis Oriental",
                    "Zamboanga del Norte",
                    "Davao del Sur",
                    "Zamboanga del Sur",
                    "Surigao del Norte",
                    "Basilan",
                    "Cagayan",
                    "Misamis Occidental",
                    "Tawi-Tawi",
                    "Maguindanao",
                    "Surigao del Sur",
                    "Agusan del sur",
                    "Sultan Kudarat",
                    "Dinagat Islands",
                    "Catulanan Pequeno",
                    "South Cotabato",
                    "Bukidnon",
                    "Compostela Valley / Davao de Oro",
                    "Agusan del Sur",
                    "Zamboanga Sibugay",
                    "Davao del Norte",
                    "Cotabato / North Cotabato",
                    "Agusan del Norte",
                    "Davao Oriental",
                    "Lanao del Norte",
                    "Camiguin",
                ),
            },
        },
    ],
    "form_district_map": [
        {
            "form_id": 92812293196968,
            "business_district": [316, 317, 318],
        },
        {
            "form_id": 92812586396975,
            "business_district": [258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 268, 269, 270, 271],
        },
    ],
    "adhoc_fields": [
        "external_submission_id",
        "legacy_shipper_id",
        "total_parcels_shipped_past_week",
        "shipment_frequency",
        "sales_platform_blibli_flag",
        "sales_platform_bukalapak_flag",
        "sales_platform_carousell_flag",
        "sales_platform_chilindo_flag",
        "sales_platform_dekoruma_flag",
        "sales_platform_ebay_flag",
        "sales_platform_facebook_flag",
        "sales_platform_instagram_flag",
        "sales_platform_jd_flag",
        "sales_platform_jdcentral_flag",
        "sales_platform_kaidee_flag",
        "sales_platform_lazada_flag",
        "sales_platform_lelong_flag",
        "sales_platform_olx_flag",
        "sales_platform_others_flag",
        "sales_platform_ownweb_flag",
        "sales_platform_pgmall_flag",
        "sales_platform_prestomall_flag",
        "sales_platform_qoo10_flag",
        "sales_platform_retailshop_flag",
        "sales_platform_sendo_flag",
        "sales_platform_shopee_flag",
        "sales_platform_telegram_flag",
        "sales_platform_tiki_flag",
        "sales_platform_tokopedia_flag",
        "sales_platform_using_nv_beautymnl_flag",
        "sales_platform_using_nv_blibli_flag",
        "sales_platform_using_nv_bukalapak_flag",
        "sales_platform_using_nv_carousell_flag",
        "sales_platform_using_nv_chilindo_flag",
        "sales_platform_using_nv_dekoruma_flag",
        "sales_platform_using_nv_ebay_flag",
        "sales_platform_using_nv_facebook_flag",
        "sales_platform_using_nv_instagram_flag",
        "sales_platform_using_nv_jd_flag",
        "sales_platform_using_nv_jdcentral_flag",
        "sales_platform_using_nv_kaidee_flag",
        "sales_platform_using_nv_lazada_flag",
        "sales_platform_using_nv_lelong_flag",
        "sales_platform_using_nv_olx_flag",
        "sales_platform_using_nv_others_flag",
        "sales_platform_using_nv_ownweb_flag",
        "sales_platform_using_nv_pgmall_flag",
        "sales_platform_using_nv_prestomall_flag",
        "sales_platform_using_nv_qoo10_flag",
        "sales_platform_using_nv_retailshop_flag",
        "sales_platform_using_nv_sendo_flag",
        "sales_platform_using_nv_shopee_flag",
        "sales_platform_using_nv_telegram_flag",
        "sales_platform_using_nv_tiki_flag",
        "sales_platform_using_nv_tokopedia_flag",
        "sales_platform_using_nv_vettons_flag",
        "sales_platform_using_nv_wechat_flag",
        "sales_platform_using_nv_zalora_flag",
        "sales_platform_using_nv_zilingo_flag",
        "sales_platform_vettons_flag",
        "sales_platform_wechat_flag",
        "sales_platform_zalora_flag",
        "sales_platform_zilingo_flag",
        "3pl_volume_percentage_2go",
        "3pl_volume_percentage_abxexpress",
        "3pl_volume_percentage_ahamove",
        "3pl_volume_percentage_air21",
        "3pl_volume_percentage_alphafast",
        "3pl_volume_percentage_anteraja",
        "3pl_volume_percentage_aramex",
        "3pl_volume_percentage_bestexpress",
        "3pl_volume_percentage_bestvinacapital",
        "3pl_volume_percentage_citylink",
        "3pl_volume_percentage_cjcentury",
        "3pl_volume_percentage_cjlogistics",
        "3pl_volume_percentage_dhl",
        "3pl_volume_percentage_easyparcel",
        "3pl_volume_percentage_entrego",
        "3pl_volume_percentage_flashexpress",
        "3pl_volume_percentage_gdex",
        "3pl_volume_percentage_giaohangnhanh",
        "3pl_volume_percentage_giaohangtietkiem",
        "3pl_volume_percentage_gogoexpress",
        "3pl_volume_percentage_gojek",
        "3pl_volume_percentage_grab",
        "3pl_volume_percentage_grabexpress",
        "3pl_volume_percentage_idexpress",
        "3pl_volume_percentage_jne",
        "3pl_volume_percentage_jnt",
        "3pl_volume_percentage_jrsexpress",
        "3pl_volume_percentage_kerry",
        "3pl_volume_percentage_lalamove",
        "3pl_volume_percentage_lbcexpress",
        "3pl_volume_percentage_lex",
        "3pl_volume_percentage_mrspeedy",
        "3pl_volume_percentage_nationwide",
        "3pl_volume_percentage_nhattinpost",
        "3pl_volume_percentage_nimexpress",
        "3pl_volume_percentage_ninjavan",
        "3pl_volume_percentage_others",
        "3pl_volume_percentage_pgeon",
        "3pl_volume_percentage_posid",
        "3pl_volume_percentage_poslaju",
        "3pl_volume_percentage_qexpress",
        "3pl_volume_percentage_scgexpress",
        "3pl_volume_percentage_ship60",
        "3pl_volume_percentage_sicepat",
        "3pl_volume_percentage_singpost",
        "3pl_volume_percentage_skynet",
        "3pl_volume_percentage_spx",
        "3pl_volume_percentage_thaipost",
        "3pl_volume_percentage_tiki",
        "3pl_volume_percentage_tnt",
        "3pl_volume_percentage_urbanfox",
        "3pl_volume_percentage_vietnampost",
        "3pl_volume_percentage_viettelpost",
        "3pl_volume_percentage_wahana",
        "3pl_volume_percentage_xpost",
        "3pl_volume_percentage_zoom",
        "sales_platform_volume_percentage_beautymnl",
        "sales_platform_volume_percentage_blibli",
        "sales_platform_volume_percentage_bukalapak",
        "sales_platform_volume_percentage_carousell",
        "sales_platform_volume_percentage_chilindo",
        "sales_platform_volume_percentage_dekoruma",
        "sales_platform_volume_percentage_ebay",
        "sales_platform_volume_percentage_facebook",
        "sales_platform_volume_percentage_instagram",
        "sales_platform_volume_percentage_jd",
        "sales_platform_volume_percentage_jdcentral",
        "sales_platform_volume_percentage_kaidee",
        "sales_platform_volume_percentage_lazada",
        "sales_platform_volume_percentage_lelong",
        "sales_platform_volume_percentage_olx",
        "sales_platform_volume_percentage_others",
        "sales_platform_volume_percentage_ownweb",
        "sales_platform_volume_percentage_pgmall",
        "sales_platform_volume_percentage_prestomall",
        "sales_platform_volume_percentage_qoo10",
        "sales_platform_volume_percentage_retailshop",
        "sales_platform_volume_percentage_sendo",
        "sales_platform_volume_percentage_shopee",
        "sales_platform_volume_percentage_telegram",
        "sales_platform_volume_percentage_tiki",
        "sales_platform_volume_percentage_tokopedia",
        "sales_platform_volume_percentage_vettons",
        "sales_platform_volume_percentage_wechat",
        "sales_platform_volume_percentage_zalora",
        "sales_platform_volume_percentage_zilingo",
        "parcel_send_out_method",
        "pickup_arrangement_regular_timeslot_flag",
        "pickup_arrangement_unscheduled_flag",
        "reason_for_choosing_nv",
        "ninja_recommendation_score",
        "ninja_service_comment",
        "competitor_name",
        "competitor_brand",
        "reason_for_choosing_competitor",
        "parcel_send_out_method_competitor",
        "competitor_recommendation_score",
        "competitor_service_comment",
        "am_vs_non_serviceable",
        "competitor_satisfaction_score_billing_accuracy",
        "competitor_satisfaction_score_delivery_speed",
        "competitor_satisfaction_score_delivery_success_rate",
        "competitor_satisfaction_score_drop_off_accessibility",
        "competitor_satisfaction_score_drop_off_ease",
        "competitor_satisfaction_score_drop_off_experience",
        "competitor_satisfaction_score_order_booking_ease_of_navigation",
        "competitor_satisfaction_score_order_booking_system_reliability",
        "competitor_satisfaction_score_parcel_tracking",
        "competitor_satisfaction_score_pickup_driver_attitude",
        "competitor_satisfaction_score_pickup_driver_punctuality",
        "competitor_satisfaction_score_quality_of_resolution",
        "competitor_satisfaction_score_rts_service",
        "competitor_satisfaction_score_speed_of_reimbursement",
        "competitor_satisfaction_score_speed_of_resolution_for_late_lost_missing_parcel",
        "ninja_satisfaction_score_billing_accuracy",
        "ninja_satisfaction_score_delivery_speed",
        "ninja_satisfaction_score_delivery_success_rate",
        "ninja_satisfaction_score_drop_off_accessibility",
        "ninja_satisfaction_score_drop_off_ease",
        "ninja_satisfaction_score_drop_off_experience",
        "ninja_satisfaction_score_order_booking_ease_of_navigation",
        "ninja_satisfaction_score_order_booking_system_reliability",
        "ninja_satisfaction_score_parcel_tracking",
        "ninja_satisfaction_score_pickup_driver_attitude",
        "ninja_satisfaction_score_pickup_driver_punctuality",
        "ninja_satisfaction_score_quality_of_resolution",
        "ninja_satisfaction_score_rts_service",
        "ninja_satisfaction_score_speed_of_reimbursement",
        "ninja_satisfaction_score_speed_of_resolution_for_late_lost_missing_parcel",
        "ninja_satisfaction_score",
        "competitor_satisfaction_score",
    ],
}

PSAT_ADHOC = {
    "columns": {
        "3pl_volume_percentage_2go": {"type": "flat", "form_id_to_field_id": {"210538675204959": "43"}},
        "3pl_volume_percentage_abxexpress": {"type": "flat", "form_id_to_field_id": {"210390725440953": "49"}},
        "3pl_volume_percentage_ahamove": {"type": "flat", "form_id_to_field_id": {"210537962104956": "37"}},
        "3pl_volume_percentage_air21": {"type": "flat", "form_id_to_field_id": {"210538675204959": "44"}},
        "3pl_volume_percentage_alphafast": {"type": "flat", "form_id_to_field_id": {"210538981104958": "37"}},
        "3pl_volume_percentage_anteraja": {"type": "flat", "form_id_to_field_id": {"210540764504954": "49"}},
        "3pl_volume_percentage_aramex": {"type": "flat", "form_id_to_field_id": {"210390725440953": "50"}},
        "3pl_volume_percentage_bestexpress": {"type": "flat", "form_id_to_field_id": {"210538981104958": "38"}},
        "3pl_volume_percentage_bestvinacapital": {"type": "flat", "form_id_to_field_id": {"210537962104956": "38"}},
        "3pl_volume_percentage_citylink": {"type": "flat", "form_id_to_field_id": {"210390725440953": "51"}},
        "3pl_volume_percentage_cjcentury": {"type": "flat", "form_id_to_field_id": {"210390725440953": "52"}},
        "3pl_volume_percentage_cjlogistics": {"type": "flat", "form_id_to_field_id": {"210540583504953": "43"}},
        "3pl_volume_percentage_dhl": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "53",
                "210537962104956": "39",
                "210538981104958": "39",
                "210540583504953": "44",
            },
        },
        "3pl_volume_percentage_easyparcel": {"type": "flat", "form_id_to_field_id": {"210390725440953": "54"}},
        "3pl_volume_percentage_entrego": {"type": "flat", "form_id_to_field_id": {"210538675204959": "45"}},
        "3pl_volume_percentage_flashexpress": {"type": "flat", "form_id_to_field_id": {"210538981104958": "40"}},
        "3pl_volume_percentage_gdex": {"type": "flat", "form_id_to_field_id": {"210390725440953": "55"}},
        "3pl_volume_percentage_giaohangnhanh": {"type": "flat", "form_id_to_field_id": {"210537962104956": "40"}},
        "3pl_volume_percentage_giaohangtietkiem": {"type": "flat", "form_id_to_field_id": {"210537962104956": "41"}},
        "3pl_volume_percentage_gogoexpress": {"type": "flat", "form_id_to_field_id": {"210538675204959": "46"}},
        "3pl_volume_percentage_gojek": {"type": "flat", "form_id_to_field_id": {"210540764504954": "50"}},
        "3pl_volume_percentage_grab": {
            "type": "flat",
            "form_id_to_field_id": {"210537962104956": "42", "210540764504954": "51"},
        },
        "3pl_volume_percentage_grabexpress": {"type": "flat", "form_id_to_field_id": {"210538675204959": "47"}},
        "3pl_volume_percentage_idexpress": {"type": "flat", "form_id_to_field_id": {"210540764504954": "52"}},
        "3pl_volume_percentage_jne": {"type": "flat", "form_id_to_field_id": {"210540764504954": "54"}},
        "3pl_volume_percentage_jnt": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "56",
                "210537962104956": "43",
                "210538981104958": "41",
                "210540583504953": "45",
                "210538675204959": "48",
                "210540764504954": "53",
            },
        },
        "3pl_volume_percentage_jrsexpress": {"type": "flat", "form_id_to_field_id": {"210538675204959": "49"}},
        "3pl_volume_percentage_kerry": {"type": "flat", "form_id_to_field_id": {"210538981104958": "42"}},
        "3pl_volume_percentage_lalamove": {
            "type": "flat",
            "form_id_to_field_id": {"210538981104958": "43", "210538675204959": "50"},
        },
        "3pl_volume_percentage_lbcexpress": {"type": "flat", "form_id_to_field_id": {"210538675204959": "52"}},
        "3pl_volume_percentage_lex": {"type": "flat", "form_id_to_field_id": {"210538675204959": "51"}},
        "3pl_volume_percentage_mrspeedy": {"type": "flat", "form_id_to_field_id": {"210538675204959": "53"}},
        "3pl_volume_percentage_nationwide": {"type": "flat", "form_id_to_field_id": {"210390725440953": "57"}},
        "3pl_volume_percentage_nhattinpost": {"type": "flat", "form_id_to_field_id": {"210537962104956": "44"}},
        "3pl_volume_percentage_nimexpress": {"type": "flat", "form_id_to_field_id": {"210538981104958": "44"}},
        "3pl_volume_percentage_ninjavan": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "58",
                "210537962104956": "45",
                "210538981104958": "45",
                "210540583504953": "46",
                "210538675204959": "54",
                "210540764504954": "55",
            },
        },
        "3pl_volume_percentage_others": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "63",
                "210537962104956": "49",
                "210538981104958": "48",
                "210540583504953": "51",
                "210538675204959": "58",
                "210540764504954": "60",
            },
        },
        "3pl_volume_percentage_pgeon": {"type": "flat", "form_id_to_field_id": {"210390725440953": "59"}},
        "3pl_volume_percentage_posid": {"type": "flat", "form_id_to_field_id": {"210540764504954": "56"}},
        "3pl_volume_percentage_poslaju": {"type": "flat", "form_id_to_field_id": {"210390725440953": "60"}},
        "3pl_volume_percentage_qexpress": {"type": "flat", "form_id_to_field_id": {"210540583504953": "47"}},
        "3pl_volume_percentage_scgexpress": {"type": "flat", "form_id_to_field_id": {"210538981104958": "46"}},
        "3pl_volume_percentage_ship60": {"type": "flat", "form_id_to_field_id": {"210537962104956": "46"}},
        "3pl_volume_percentage_sicepat": {"type": "flat", "form_id_to_field_id": {"210540764504954": "57"}},
        "3pl_volume_percentage_singpost": {"type": "flat", "form_id_to_field_id": {"210540583504953": "48"}},
        "3pl_volume_percentage_skynet": {"type": "flat", "form_id_to_field_id": {"210390725440953": "61"}},
        "3pl_volume_percentage_spx": {"type": "flat", "form_id_to_field_id": {"210538675204959": "55"}},
        "3pl_volume_percentage_thaipost": {"type": "flat", "form_id_to_field_id": {"210538981104958": "47"}},
        "3pl_volume_percentage_tiki": {"type": "flat", "form_id_to_field_id": {"210540764504954": "58"}},
        "3pl_volume_percentage_tnt": {"type": "flat", "form_id_to_field_id": {"210540583504953": "49"}},
        "3pl_volume_percentage_urbanfox": {"type": "flat", "form_id_to_field_id": {"210540583504953": "50"}},
        "3pl_volume_percentage_vietnampost": {"type": "flat", "form_id_to_field_id": {"210537962104956": "47"}},
        "3pl_volume_percentage_viettelpost": {"type": "flat", "form_id_to_field_id": {"210537962104956": "48"}},
        "3pl_volume_percentage_wahana": {"type": "flat", "form_id_to_field_id": {"210540764504954": "59"}},
        "3pl_volume_percentage_xpost": {"type": "flat", "form_id_to_field_id": {"210538675204959": "56"}},
        "3pl_volume_percentage_zoom": {
            "type": "flat",
            "form_id_to_field_id": {"210390725440953": "62", "210538675204959": "57"},
        },
        "am_vs_non_serviceable": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "162",
                "210537962104956": "198",
                "210538981104958": "206",
                "210540583504953": "134",
                "210538675204959": "145",
                "210540764504954": "180",
            },
        },
        "business_region": {
            "type": "coalesce",
            "form_id_to_field_values": {
                "210390725440953": range(69, 83),
                "210537962104956": range(55, 119),
                "210538981104958": range(49, 126),
                "210540764504954": range(66, 100),
            },
        },
        "competitor_brand": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "121",
                "210537962104956": "157",
                "210538981104958": "165",
                "210540583504953": "94",
                "210538675204959": "103",
                "210540764504954": "138",
            },
        },
        "competitor_name": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "120",
                "210537962104956": "156",
                "210538981104958": "164",
                "210540583504953": "93",
                "210538675204959": "102",
                "210540764504954": "137",
            },
        },
        "competitor_recommendation_score": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "144",
                "210537962104956": "180",
                "210538981104958": "188",
                "210540583504953": "116",
                "210538675204959": "126",
                "210540764504954": "161",
            },
        },
        "competitor_satisfaction_score": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "145",
                "210537962104956": "181",
                "210538981104958": "189",
                "210540583504953": "117",
                "210538675204959": "127",
                "210540764504954": "162",
            },
        },
        "competitor_satisfaction_score_billing_accuracy": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "159",
                "210537962104956": "195",
                "210538981104958": "203",
                "210540583504953": "131",
                "210538675204959": "142",
                "210540764504954": "176",
            },
        },
        "competitor_satisfaction_score_delivery_speed": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "154",
                "210537962104956": "190",
                "210538981104958": "198",
                "210540583504953": "126",
                "210538675204959": "136",
                "210540764504954": "171",
            },
        },
        "competitor_satisfaction_score_delivery_success_rate": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "155",
                "210537962104956": "191",
                "210538981104958": "199",
                "210540583504953": "127",
                "210538675204959": "137",
                "210540764504954": "172",
            },
        },
        "competitor_satisfaction_score_drop_off_accessibility": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "152",
                "210537962104956": "188",
                "210538981104958": "196",
                "210540583504953": "124",
                "210538675204959": "134",
                "210540764504954": "169",
            },
        },
        "competitor_satisfaction_score_drop_off_ease": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "151",
                "210537962104956": "187",
                "210538981104958": "195",
                "210540583504953": "123",
                "210538675204959": "133",
                "210540764504954": "168",
            },
        },
        "competitor_satisfaction_score_drop_off_experience": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "150",
                "210537962104956": "186",
                "210538981104958": "194",
                "210540583504953": "122",
                "210538675204959": "132",
                "210540764504954": "167",
            },
        },
        "competitor_satisfaction_score_order_booking_ease_of_navigation": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "146",
                "210537962104956": "182",
                "210538981104958": "190",
                "210540583504953": "118",
                "210538675204959": "128",
                "210540764504954": "163",
            },
        },
        "competitor_satisfaction_score_order_booking_system_reliability": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "147",
                "210537962104956": "183",
                "210538981104958": "191",
                "210540583504953": "119",
                "210538675204959": "129",
                "210540764504954": "164",
            },
        },
        "competitor_satisfaction_score_parcel_tracking": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "153",
                "210537962104956": "189",
                "210538981104958": "197",
                "210540583504953": "125",
                "210538675204959": "135",
                "210540764504954": "170",
            },
        },
        "competitor_satisfaction_score_pickup_driver_attitude": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "149",
                "210537962104956": "185",
                "210538981104958": "193",
                "210540583504953": "121",
                "210538675204959": "131",
                "210540764504954": "166",
            },
        },
        "competitor_satisfaction_score_pickup_driver_punctuality": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "148",
                "210537962104956": "184",
                "210538981104958": "192",
                "210540583504953": "120",
                "210538675204959": "130",
                "210540764504954": "165",
            },
        },
        "competitor_satisfaction_score_quality_of_resolution": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "157",
                "210537962104956": "193",
                "210538981104958": "201",
                "210540583504953": "129",
                "210538675204959": "140",
                "210540764504954": "174",
            },
        },
        "competitor_satisfaction_score_rts_service": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "156",
                "210537962104956": "192",
                "210538981104958": "200",
                "210540583504953": "128",
                "210538675204959": "139",
                "210540764504954": "173",
            },
        },
        "competitor_satisfaction_score_speed_of_reimbursement": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "160",
                "210537962104956": "196",
                "210538981104958": "204",
                "210540583504953": "132",
                "210538675204959": "143",
                "210540764504954": "177",
            },
        },
        "competitor_satisfaction_score_speed_of_resolution_for_late_lost_missing_parcel": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "158",
                "210537962104956": "194",
                "210538981104958": "202",
                "210540583504953": "130",
                "210538675204959": "141",
                "210540764504954": "175",
            },
        },
        "competitor_service_comment": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "161",
                "210537962104956": "197",
                "210538981104958": "205",
                "210540583504953": "133",
                "210538675204959": "144",
                "210540764504954": "178",
            },
        },
        "email": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "3",
                "210537962104956": "3",
                "210538981104958": "3",
                "210540583504953": "3",
                "210538675204959": "147",
                "210540764504954": "3",
            },
        },
        "external_submission_id": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "1",
                "210537962104956": "1",
                "210538981104958": "1",
                "210540583504953": "1",
                "210538675204959": "1",
                "210540764504954": "1",
            },
        },
        "legacy_shipper_id": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "163",
                "210537962104956": "199",
                "210538981104958": "207",
                "210540583504953": "135",
                "210538675204959": "146",
                "210540764504954": "179",
            },
        },
        "ninja_recommendation_score": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "102",
                "210537962104956": "138",
                "210538981104958": "149",
                "210540583504953": "75",
                "210538675204959": "83",
                "210540764504954": "119",
            },
        },
        "ninja_satisfaction_score": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "103",
                "210537962104956": "139",
                "210538981104958": "150",
                "210540583504953": "76",
                "210538675204959": "84",
                "210540764504954": "120",
            },
        },
        "ninja_satisfaction_score_billing_accuracy": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "117",
                "210537962104956": "153",
                "210538981104958": "161",
                "210540583504953": "90",
                "210538675204959": "99",
                "210540764504954": "134",
            },
        },
        "ninja_satisfaction_score_delivery_speed": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "112",
                "210537962104956": "148",
                "210538981104958": "156",
                "210540583504953": "85",
                "210538675204959": "93",
                "210540764504954": "129",
            },
        },
        "ninja_satisfaction_score_delivery_success_rate": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "113",
                "210537962104956": "149",
                "210538981104958": "157",
                "210540583504953": "86",
                "210538675204959": "94",
                "210540764504954": "130",
            },
        },
        "ninja_satisfaction_score_drop_off_accessibility": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "110",
                "210537962104956": "146",
                "210540583504953": "83",
                "210538675204959": "91",
                "210540764504954": "127",
            },
        },
        "ninja_satisfaction_score_drop_off_ease": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "109",
                "210537962104956": "145",
                "210540583504953": "82",
                "210538675204959": "90",
                "210540764504954": "126",
            },
        },
        "ninja_satisfaction_score_drop_off_experience": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "108",
                "210537962104956": "144",
                "210540583504953": "81",
                "210538675204959": "89",
                "210540764504954": "125",
            },
        },
        "ninja_satisfaction_score_order_booking_ease_of_navigation": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "104",
                "210537962104956": "140",
                "210538981104958": "151",
                "210540583504953": "77",
                "210538675204959": "85",
                "210540764504954": "121",
            },
        },
        "ninja_satisfaction_score_order_booking_system_reliability": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "105",
                "210537962104956": "141",
                "210538981104958": "152",
                "210540583504953": "78",
                "210538675204959": "86",
                "210540764504954": "122",
            },
        },
        "ninja_satisfaction_score_parcel_tracking": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "111",
                "210537962104956": "147",
                "210538981104958": "155",
                "210540583504953": "84",
                "210538675204959": "92",
                "210540764504954": "128",
            },
        },
        "ninja_satisfaction_score_pickup_driver_attitude": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "107",
                "210537962104956": "143",
                "210538981104958": "154",
                "210540583504953": "80",
                "210538675204959": "88",
                "210540764504954": "124",
            },
        },
        "ninja_satisfaction_score_pickup_driver_punctuality": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "106",
                "210537962104956": "142",
                "210538981104958": "153",
                "210540583504953": "79",
                "210538675204959": "87",
                "210540764504954": "123",
            },
        },
        "ninja_satisfaction_score_quality_of_resolution": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "115",
                "210537962104956": "151",
                "210538981104958": "159",
                "210540583504953": "88",
                "210538675204959": "97",
                "210540764504954": "132",
            },
        },
        "ninja_satisfaction_score_rts_service": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "114",
                "210537962104956": "150",
                "210538981104958": "158",
                "210540583504953": "87",
                "210538675204959": "95",
                "210540764504954": "131",
            },
        },
        "ninja_satisfaction_score_speed_of_reimbursement": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "118",
                "210537962104956": "154",
                "210538981104958": "162",
                "210540583504953": "91",
                "210538675204959": "100",
                "210540764504954": "135",
            },
        },
        "ninja_satisfaction_score_speed_of_resolution_for_late_lost_missing_parcel": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "116",
                "210537962104956": "152",
                "210538981104958": "160",
                "210540583504953": "89",
                "210538675204959": "98",
                "210540764504954": "133",
            },
        },
        "ninja_service_comment": {
            "type": "flat",
            "form_id_to_field_id": {
                "210537962104956": "155",
                "210538981104958": "163",
                "210540583504953": "92",
                "210538675204959": "101",
                "210540764504954": "136",
            },
        },
        "ninja_service_comment_my": {"type": "dict", "form_id_to_field_id": {"210390725440953": "119"}},
        "parcel_send_out_method_competitor": {
            "type": "coalesce",
            "form_id_to_field_values": {
                "210390725440953": range(141, 144),
                "210537962104956": range(177, 180),
                "210538981104958": range(185, 188),
                "210540583504953": range(113, 116),
                "210538675204959": range(123, 126),
                "210540764504954": range(158, 161),
            },
        },
        "parcel_send_out_method": {
            "type": "coalesce",
            "form_id_to_field_values": {
                "210390725440953": range(64, 67),
                "210537962104956": range(50, 53),
                "210538981104958": range(145, 147),
                "210540583504953": range(52, 55),
                "210538675204959": range(59, 62),
                "210540764504954": range(61, 64),
            },
        },
        "pickup_arrangement_regular_timeslot_flag": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "67",
                "210537962104956": "53",
                "210538981104958": "147",
                "210540583504953": "55",
                "210538675204959": "62",
                "210540764504954": "64",
            },
        },
        "pickup_arrangement_unscheduled_flag": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "68",
                "210537962104956": "54",
                "210538981104958": "148",
                "210540583504953": "56",
                "210538675204959": "63",
                "210540764504954": "65",
            },
        },
        "reason_for_choosing_competitor": {
            "type": "coalesce",
            "form_id_to_field_values": {
                "210390725440953": range(122, 139),
                "210537962104956": range(158, 175),
                "210538981104958": range(166, 183),
                "210540583504953": range(95, 111),
                "210538675204959": range(104, 121),
                "210540764504954": range(139, 156),
            },
        },
        "reason_for_choosing_competitor_others_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "139",
                "210537962104956": "175",
                "210538981104958": "183",
                "210540583504953": "111",
                "210538675204959": "121",
                "210540764504954": "156",
            },
        },
        "reason_for_choosing_competitor_others_answer": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "140",
                "210537962104956": "176",
                "210538981104958": "184",
                "210540583504953": "112",
                "210538675204959": "122",
                "210540764504954": "157",
            },
        },
        "reason_for_choosing_nv": {
            "type": "coalesce",
            "form_id_to_field_values": {
                "210390725440953": range(83, 100),
                "210537962104956": range(119, 136),
                "210538981104958": range(126, 143),
                "210540583504953": range(57, 73),
                "210538675204959": range(64, 81),
                "210540764504954": range(100, 117),
            },
        },
        "reason_for_choosing_nv_others_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "100",
                "210537962104956": "136",
                "210538981104958": "143",
                "210540583504953": "73",
                "210538675204959": "81",
                "210540764504954": "117",
            },
        },
        "reason_for_choosing_nv_others_answer": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "101",
                "210537962104956": "137",
                "210538981104958": "144",
                "210540583504953": "74",
                "210538675204959": "82",
                "210540764504954": "118",
            },
        },
        "sales_platform_beautymnl": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "8"}},
        "sales_platform_blibli_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "6"}},
        "sales_platform_bukalapak_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "7"}},
        "sales_platform_carousell_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210390725440953": "6", "210540583504953": "9"},
        },
        "sales_platform_chilindo_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "10"}},
        "sales_platform_dekoruma_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "8"}},
        "sales_platform_ebay_flag": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "6"}},
        "sales_platform_facebook_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "15",
                "210537962104956": "10",
                "210538981104958": "11",
                "210540583504953": "11",
                "210538675204959": "13",
                "210540764504954": "15",
            },
        },
        "sales_platform_instagram_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "16",
                "210537962104956": "11",
                "210538981104958": "12",
                "210540583504953": "14",
                "210538675204959": "14",
                "210540764504954": "16",
            },
        },
        "sales_platform_jd_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "9"}},
        "sales_platform_jdcentral_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "8"}},
        "sales_platform_kaidee_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "9"}},
        "sales_platform_lazada_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210538675204959": "7",
                "210390725440953": "7",
                "210537962104956": "6",
                "210538981104958": "6",
                "210540583504953": "6",
                "210540764504954": "10",
            },
        },
        "sales_platform_lelong_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "8"}},
        "sales_platform_olx_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210538675204959": "9", "210540764504954": "11"},
        },
        "sales_platform_others_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "20",
                "210537962104956": "16",
                "210538981104958": "16",
                "210540583504953": "18",
                "210538675204959": "18",
                "210540764504954": "20",
            },
        },
        "sales_platform_ownweb_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "17",
                "210537962104956": "13",
                "210538981104958": "13",
                "210540583504953": "15",
                "210538675204959": "15",
                "210540764504954": "17",
            },
        },
        "sales_platform_pgmall_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "14"}},
        "sales_platform_prestomall_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "13"}},
        "sales_platform_qoo10_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "12",
                "210540583504953": "8",
                "210538675204959": "12",
                "210540764504954": "12",
            },
        },
        "sales_platform_retailshop_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "18",
                "210537962104956": "14",
                "210538981104958": "14",
                "210540583504953": "16",
                "210538675204959": "16",
                "210540764504954": "18",
            },
        },
        "sales_platform_sendo_flag": {"type": "boolean", "form_id_to_field_id": {"210537962104956": "7"}},
        "sales_platform_shopee_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "10",
                "210537962104956": "8",
                "210538981104958": "7",
                "210540583504953": "7",
                "210538675204959": "10",
                "210540764504954": "13",
            },
        },
        "sales_platform_telegram_flag": {"type": "boolean", "form_id_to_field_id": {"210540583504953": "12"}},
        "sales_platform_tiki_flag": {"type": "boolean", "form_id_to_field_id": {"210537962104956": "9"}},
        "sales_platform_tokopedia_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "14"}},
        "sales_platform_using_nv_beautymnl_flag": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "33"}},
        "sales_platform_using_nv_blibli_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "35"}},
        "sales_platform_using_nv_bukalapak_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "36"}},
        "sales_platform_using_nv_carousell_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210390725440953": "35", "210540583504953": "34"},
        },
        "sales_platform_using_nv_chilindo_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "31"}},
        "sales_platform_using_nv_dekoruma_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "37"}},
        "sales_platform_using_nv_ebay_flag": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "31"}},
        "sales_platform_using_nv_facebook_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "44",
                "210537962104956": "31",
                "210538981104958": "32",
                "210540583504953": "36",
                "210538675204959": "38",
                "210540764504954": "44",
            },
        },
        "sales_platform_using_nv_instagram_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "45",
                "210537962104956": "32",
                "210538981104958": "33",
                "210540583504953": "39",
                "210538675204959": "39",
                "210540764504954": "45",
            },
        },
        "sales_platform_using_nv_jd_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "38"}},
        "sales_platform_using_nv_jdcentral_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "29"}},
        "sales_platform_using_nv_kaidee_flag": {"type": "boolean", "form_id_to_field_id": {"210538981104958": "30"}},
        "sales_platform_using_nv_lazada_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "36",
                "210537962104956": "27",
                "210538981104958": "27",
                "210540583504953": "31",
                "210538675204959": "32",
                "210540764504954": "39",
            },
        },
        "sales_platform_using_nv_lelong_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "37"}},
        "sales_platform_using_nv_olx_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210538675204959": "34", "210540764504954": "40"},
        },
        "sales_platform_using_nv_others_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "48",
                "210537962104956": "36",
                "210538981104958": "36",
                "210540583504953": "42",
                "210538675204959": "42",
                "210540764504954": "48",
            },
        },
        "sales_platform_using_nv_ownweb_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "46",
                "210537962104956": "34",
                "210538981104958": "34",
                "210540583504953": "40",
                "210538675204959": "40",
                "210540764504954": "46",
            },
        },
        "sales_platform_using_nv_pgmall_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "43"}},
        "sales_platform_using_nv_prestomall_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210390725440953": "42"},
        },
        "sales_platform_using_nv_qoo10_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210390725440953": "41", "210540583504953": "33", "210540764504954": "41"},
        },
        "sales_platform_using_nv_retailshop_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "47",
                "210537962104956": "35",
                "210538981104958": "35",
                "210540583504953": "41",
                "210538675204959": "41",
                "210540764504954": "47",
            },
        },
        "sales_platform_using_nv_sendo_flag": {"type": "boolean", "form_id_to_field_id": {"210537962104956": "28"}},
        "sales_platform_using_nv_shopee_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "39",
                "210537962104956": "29",
                "210538981104958": "28",
                "210540583504953": "32",
                "210538675204959": "35",
                "210540764504954": "42",
            },
        },
        "sales_platform_using_nv_telegram_flag": {"type": "boolean", "form_id_to_field_id": {"210540583504953": "37"}},
        "sales_platform_using_nv_tiki_flag": {"type": "boolean", "form_id_to_field_id": {"210537962104956": "30"}},
        "sales_platform_using_nv_tokopedia_flag": {"type": "boolean", "form_id_to_field_id": {"210540764504954": "43"}},
        "sales_platform_using_nv_vettons_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "38"}},
        "sales_platform_using_nv_wechat_flag": {"type": "boolean", "form_id_to_field_id": {"210540583504953": "38"}},
        "sales_platform_using_nv_zalora_flag": {
            "type": "boolean",
            "form_id_to_field_id": {
                "210390725440953": "40",
                "210537962104956": "33",
                "210540583504953": "35",
                "210538675204959": "36",
            },
        },
        "sales_platform_using_nv_zilingo_flag": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "37"}},
        "sales_platform_vettons_flag": {"type": "boolean", "form_id_to_field_id": {"210390725440953": "9"}},
        "sales_platform_volume_percentage_beautymnl": {
            "type": "flat",
            "form_id_to_field_id": {"210538675204959": "21"},
        },
        "sales_platform_volume_percentage_blibli": {"type": "flat", "form_id_to_field_id": {"210540764504954": "21"}},
        "sales_platform_volume_percentage_bukalapak": {
            "type": "flat",
            "form_id_to_field_id": {"210540764504954": "22"},
        },
        "sales_platform_volume_percentage_carousell": {
            "type": "flat",
            "form_id_to_field_id": {"210390725440953": "21", "210540583504953": "22"},
        },
        "sales_platform_volume_percentage_chilindo": {"type": "flat", "form_id_to_field_id": {"210538981104958": "21"}},
        "sales_platform_volume_percentage_dekoruma": {"type": "flat", "form_id_to_field_id": {"210540764504954": "23"}},
        "sales_platform_volume_percentage_ebay": {"type": "flat", "form_id_to_field_id": {"210538675204959": "19"}},
        "sales_platform_volume_percentage_facebook": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "30",
                "210537962104956": "21",
                "210538981104958": "22",
                "210540583504953": "24",
                "210538675204959": "26",
                "210540764504954": "30",
            },
        },
        "sales_platform_volume_percentage_instagram": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "31",
                "210537962104956": "22",
                "210538981104958": "23",
                "210540583504953": "27",
                "210538675204959": "27",
                "210540764504954": "31",
            },
        },
        "sales_platform_volume_percentage_jd": {"type": "flat", "form_id_to_field_id": {"210540764504954": "24"}},
        "sales_platform_volume_percentage_jdcentral": {
            "type": "flat",
            "form_id_to_field_id": {"210538981104958": "19"},
        },
        "sales_platform_volume_percentage_kaidee": {"type": "flat", "form_id_to_field_id": {"210538981104958": "20"}},
        "sales_platform_volume_percentage_lazada": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "22",
                "210537962104956": "17",
                "210538981104958": "17",
                "210540583504953": "19",
                "210538675204959": "20",
                "210540764504954": "25",
            },
        },
        "sales_platform_volume_percentage_lelong": {"type": "flat", "form_id_to_field_id": {"210390725440953": "23"}},
        "sales_platform_volume_percentage_olx": {
            "type": "flat",
            "form_id_to_field_id": {"210538675204959": "22", "210540764504954": "26"},
        },
        "sales_platform_volume_percentage_others": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "34",
                "210537962104956": "26",
                "210538981104958": "26",
                "210540583504953": "30",
                "210538675204959": "30",
                "210540764504954": "34",
            },
        },
        "sales_platform_volume_percentage_ownweb": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "32",
                "210537962104956": "24",
                "210538981104958": "24",
                "210540583504953": "28",
                "210538675204959": "28",
                "210540764504954": "32",
            },
        },
        "sales_platform_volume_percentage_pgmall": {"type": "flat", "form_id_to_field_id": {"210390725440953": "29"}},
        "sales_platform_volume_percentage_prestomall": {
            "type": "flat",
            "form_id_to_field_id": {"210390725440953": "28"},
        },
        "sales_platform_volume_percentage_qoo10": {
            "type": "flat",
            "form_id_to_field_id": {"210390725440953": "27", "210540583504953": "21", "210540764504954": "27"},
        },
        "sales_platform_volume_percentage_retailshop": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "33",
                "210537962104956": "25",
                "210538981104958": "25",
                "210540583504953": "29",
                "210538675204959": "29",
                "210540764504954": "33",
            },
        },
        "sales_platform_volume_percentage_sendo": {"type": "flat", "form_id_to_field_id": {"210537962104956": "18"}},
        "sales_platform_volume_percentage_shopee": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "25",
                "210537962104956": "19",
                "210538981104958": "18",
                "210540583504953": "20",
                "210538675204959": "23",
                "210540764504954": "28",
            },
        },
        "sales_platform_volume_percentage_telegram": {"type": "flat", "form_id_to_field_id": {"210540583504953": "25"}},
        "sales_platform_volume_percentage_tiki": {"type": "flat", "form_id_to_field_id": {"210537962104956": "20"}},
        "sales_platform_volume_percentage_tokopedia": {
            "type": "flat",
            "form_id_to_field_id": {"210540764504954": "29"},
        },
        "sales_platform_volume_percentage_vettons": {"type": "flat", "form_id_to_field_id": {"210390725440953": "24"}},
        "sales_platform_volume_percentage_wechat": {"type": "flat", "form_id_to_field_id": {"210540583504953": "26"}},
        "sales_platform_volume_percentage_zalora": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "26",
                "210537962104956": "23",
                "210540583504953": "23",
                "210538675204959": "24",
            },
        },
        "sales_platform_volume_percentage_zilingo": {"type": "flat", "form_id_to_field_id": {"210538675204959": "25"}},
        "sales_platform_wechat_flag": {"type": "boolean", "form_id_to_field_id": {"210540583504953": "13"}},
        "sales_platform_zalora_flag": {
            "type": "boolean",
            "form_id_to_field_id": {"210390725440953": "11", "210537962104956": "12", "210540583504953": "10"},
        },
        "sales_platform_zilingo_flag": {"type": "boolean", "form_id_to_field_id": {"210538675204959": "11"}},
        "shipment_frequency": {
            "type": "dict",
            "form_id_to_field_id": {
                "210390725440953": "5",
                "210537962104956": "5",
                "210540764504954": "5",
                "210538981104958": "5",
                "210540583504953": "5",
                "210538675204959": "5",
            },
        },
        "submission_datetime": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "2",
                "210537962104956": "2",
                "210538981104958": "2",
                "210540583504953": "2",
                "210538675204959": "2",
                "210540764504954": "2",
            },
        },
        "total_parcels_shipped_past_week": {
            "type": "flat",
            "form_id_to_field_id": {
                "210390725440953": "4",
                "210537962104956": "4",
                "210538981104958": "4",
                "210540583504953": "4",
                "210538675204959": "4",
                "210540764504954": "4",
            },
        },
    }
}

PSAT_SSM = {
    "forms": [
        {
            "form_id": 92812293196968,
            "legacy_shipper_id": 261,
            "am_name": 302,
            "am_satisfaction_score": 304,
            "am_accessibility_rating": 307,
            "am_responsiveness_rating": 307,
            "am_proactiveness_rating": 307,
            "am_solve_problem_rating": 307,
            "am_communication_rating": 307,
            "am_knowledge_business_rating": 307,
            "am_knowledge_nv_rating": 307,
            "am_value_add_rating": 307,
            "am_comment_feedback": 309,
        },
        {
            "form_id": 92812216696968,
            "legacy_shipper_id": 261,
            "am_name": 313,
            "am_satisfaction_score": 316,
            "am_accessibility_rating": 318,
            "am_responsiveness_rating": 318,
            "am_proactiveness_rating": 318,
            "am_solve_problem_rating": 318,
            "am_communication_rating": 318,
            "am_knowledge_business_rating": 318,
            "am_knowledge_nv_rating": 318,
            "am_value_add_rating": 318,
            "am_comment_feedback": 320,
        },
        {
            "form_id": 92813190496968,
            "legacy_shipper_id": 261,
            "am_name": 413,
            "am_satisfaction_score": 427,
            "am_accessibility_rating": 421,
            "am_responsiveness_rating": 421,
            "am_proactiveness_rating": 421,
            "am_solve_problem_rating": 421,
            "am_communication_rating": 421,
            "am_knowledge_business_rating": 421,
            "am_knowledge_nv_rating": 421,
            "am_value_add_rating": 421,
            "am_comment_feedback": 422,
        },
        {
            "form_id": 92819118996979,
            "legacy_shipper_id": 260,
            "am_name": 351,
            "am_satisfaction_score": 353,
            "am_accessibility_rating": 355,
            "am_responsiveness_rating": 355,
            "am_proactiveness_rating": 355,
            "am_solve_problem_rating": 355,
            "am_communication_rating": 355,
            "am_knowledge_business_rating": 355,
            "am_knowledge_nv_rating": 355,
            "am_value_add_rating": 355,
            "am_comment_feedback": 358,
        },
        {
            "form_id": 92812527996976,
            "legacy_shipper_id": 262,
            "am_name": 300,
            "am_satisfaction_score": 303,
            "am_accessibility_rating": 306,
            "am_responsiveness_rating": 306,
            "am_proactiveness_rating": 306,
            "am_solve_problem_rating": 306,
            "am_communication_rating": 306,
            "am_knowledge_business_rating": 306,
            "am_knowledge_nv_rating": 306,
            "am_value_add_rating": 306,
            "am_comment_feedback": 308,
        },
        {
            "form_id": 92812586396975,
            "legacy_shipper_id": 276,
            "am_name": 341,
            "am_satisfaction_score": 328,
            "am_accessibility_rating": 344,
            "am_responsiveness_rating": 344,
            "am_proactiveness_rating": 344,
            "am_solve_problem_rating": 344,
            "am_communication_rating": 344,
            "am_knowledge_business_rating": 344,
            "am_knowledge_nv_rating": 344,
            "am_value_add_rating": 344,
            "am_comment_feedback": 347,
        },
    ],
    "column_config": {
        "legacy_shipper_id": {"type": "flat"},
        "am_name": {"type": "flat"},
        "am_satisfaction_score": {"type": "flat"},
        "am_accessibility_rating": {
            "type": "subtype",
            "detail": "Accessibility - able to easily contact him/her",
            "group": "am_ratings",
        },
        "am_responsiveness_rating": {
            "type": "subtype",
            "detail": "Responsiveness - speed of responding to issues / requests",
            "group": "am_ratings",
        },
        "am_proactiveness_rating": {
            "type": "subtype",
            "detail": "Proactiveness - provides updates, follow-ups on issues and recommending solutions",
            "group": "am_ratings",
        },
        "am_solve_problem_rating": {
            "type": "subtype",
            "detail": "Ability to solve problems - helps you resolve issues in an efficient and effective manner",
            "group": "am_ratings",
        },
        "am_communication_rating": {
            "type": "subtype",
            "detail": "Communication - clear and effective communication in emails / calls / meetings",
            "group": "am_ratings",
        },
        "am_knowledge_business_rating": {
            "type": "subtype",
            "detail": "Knowledge - understand your business and your business needs",
            "group": "am_ratings",
        },
        "am_knowledge_nv_rating": {
            "type": "subtype",
            "detail": "Knowledge - thorough knowledge of Ninja products, services and processes",
            "group": "am_ratings",
        },
        "am_value_add_rating": {
            "type": "subtype",
            "detail": "Value add - Value adds to my shipping experience/business",
            "group": "am_ratings",
        },
        "am_comment_feedback": {"type": "flat"},
    },
}

NPS = {
    "order_nps_submission_columns": [
        {
            "name": "tracking_id",
            "question_id_to_form_ids": {
                21: (
                    63625264590459,
                    63628528006457,
                    63628695658475,
                    63628983190466,
                    63629276236463,
                    80381768796474,
                    80381828796471,
                    80382034396457,
                    80382049996469,
                    80382125396458,
                ),
                22: (63628615677468, 63628615677468, 80381519196462),
                33: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                ),
            },
            "type": "flat",
        },
        {
            "name": "nps_score",
            "question_id_to_form_ids": {
                3: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                ),
                4: (
                    63625264590459,
                    63628528006457,
                    63628615677468,
                    63628695658475,
                    63628983190466,
                    63629276236463,
                    80381519196462,
                    80381768796474,
                    80381828796471,
                    80382034396457,
                    80382049996469,
                    80382125396458,
                ),
            },
            "type": "flat",
        },
        {
            "name": "nps_reason",
            "question_id_to_form_ids": {
                20: (
                    63625264590459,
                    63628528006457,
                    63628615677468,
                    63628695658475,
                    63628983190466,
                    63629276236463,
                    80381519196462,
                    80381768796474,
                    80381828796471,
                    80382034396457,
                    80382049996469,
                    80382125396458,
                ),
                31: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                ),
            },
            "type": "flat",
        },
        {
            "name": "driver_rating",
            "question_id_to_form_ids": {
                8: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                ),
                24: (80382034396457,),
                25: (80381828796471,),
                26: (80381768796474, 80382049996469, 80382125396458),
                28: (80381519196462,),
            },
            "type": "flat",
        },
        {
            "name": "driver_rating_details1",
            "question_id_to_form_ids": {
                9: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Was pleasant and courteous",
        },
        {
            "name": "driver_rating_details2",
            "question_id_to_form_ids": {
                9: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Contacted me before delivery",
        },
        {
            "name": "driver_rating_details3",
            "question_id_to_form_ids": {
                9: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Delivered the parcel to address stated on parcel",
        },
        {
            "name": "driver_rating_details4",
            "question_id_to_form_ids": {
                9: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Obtained my signature upon delivery",
        },
        {
            "name": "driver_rating_details5",
            "question_id_to_form_ids": {
                9: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Delivered the parcel in good condition",
        },
        {
            "name": "ninja_box_rating",
            "question_id_to_form_ids": {
                11: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_box_rating_details1",
            "question_id_to_form_ids": {
                12: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_box_rating_details2",
            "question_id_to_form_ids": {
                14: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_box_rating_details3",
            "question_id_to_form_ids": {
                15: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_box_rating_details4",
            "question_id_to_form_ids": {
                16: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating",
            "question_id_to_form_ids": {
                20: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details1",
            "question_id_to_form_ids": {
                25: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details2",
            "question_id_to_form_ids": {
                24: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details3",
            "question_id_to_form_ids": {
                23: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details4",
            "question_id_to_form_ids": {
                21: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details5",
            "question_id_to_form_ids": {
                22: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "cs_rating",
            "question_id_to_form_ids": {
                29: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "flat",
        },
        {
            "name": "cs_rating_details1",
            "question_id_to_form_ids": {
                30: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Responded to my enquiry within 2 hours",
        },
        {
            "name": "cs_rating_details2",
            "question_id_to_form_ids": {
                30: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Understood my issue",
        },
        {
            "name": "cs_rating_details3",
            "question_id_to_form_ids": {
                30: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Able to resolve my issue",
        },
        {
            "name": "cs_rating_details4",
            "question_id_to_form_ids": {
                30: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Followed up on my enquiry",
        },
        {
            "name": "cs_rating_details5",
            "question_id_to_form_ids": {
                30: (
                    91489057515465,
                    91693453571464,
                    91708519075463,
                    91708636480463,
                    91740395167463,
                    91740448166461,
                    91740746137460,
                    91740857431460,
                    91740896644469,
                    91740945384465,
                    91741389890470,
                    91741518685466,
                )
            },
            "type": "subtype",
            "detail": "Was pleasant and courteous",
        },
    ],
    "delivery_success_feedback_columns": [
        {
            "name": "tracking_id",
            "question_id_to_form_ids": {
                9: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "flat",
        },
        {
            "name": "source",
            "question_id_to_form_ids": {
                11: (201638866840867, 201638880340858, 201638984340863),
                20: (201638458540861, 201638474640860, 201638545240855),
            },
            "type": "flat",
        },
        {
            "name": "nps_score",
            "question_id_to_form_ids": {
                4: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "flat",
        },
        {
            "name": "dp_collected_flag",
            "question_id_to_form_ids": {
                10: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "I collected my parcel from stores / locker boxes",
        },
        {
            "name": "driver_rating",
            "question_id_to_form_ids": {
                2: (
                    201638866840867,
                    201638880340858,
                    201638984340863,
                ),
                17: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                ),
            },
            "type": "flat",
        },
        {
            "name": "delivery_excellence_details1",
            "question_id_to_form_ids": {
                6: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Courier called before delivery",
        },
        {
            "name": "delivery_excellence_details2",
            "question_id_to_form_ids": {
                6: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Courier was nicely dressed",
        },
        {
            "name": "delivery_excellence_details3",
            "question_id_to_form_ids": {
                6: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Courier was friendly",
        },
        {
            "name": "delivery_excellence_details4",
            "question_id_to_form_ids": {
                6: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Followed my special instructions",
        },
        {
            "name": "delivery_excellence_details5",
            "question_id_to_form_ids": {
                6: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Parcel was in good condition",
        },
        {
            "name": "delivery_improvement_details1",
            "question_id_to_form_ids": {
                7: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Call me before delivery",
        },
        {
            "name": "delivery_improvement_details2",
            "question_id_to_form_ids": {
                7: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Be better dressed",
        },
        {
            "name": "delivery_improvement_details3",
            "question_id_to_form_ids": {
                7: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Be more friendly",
        },
        {
            "name": "delivery_improvement_details4",
            "question_id_to_form_ids": {
                7: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Follow my special instructions",
        },
        {
            "name": "delivery_improvement_details5",
            "question_id_to_form_ids": {
                7: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Improve parcel condition",
        },
        {
            "name": "nv_uniform_flag",
            "question_id_to_form_ids": {21: (201638545240855,)},
            "type": "boolean",
            "detail": "YES",
        },
        {
            "name": "ninja_point_flag",
            "question_id_to_form_ids": {
                18: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Ninja Point",
        },
        {
            "name": "ninja_box_flag",
            "question_id_to_form_ids": {
                18: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "boolean",
            "detail": "Ninja Box",
        },
        {
            "name": "collection_rating",
            "question_id_to_form_ids": {
                2: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "flat",
        },
        {
            "name": "ninja_point_rating_details1",
            "question_id_to_form_ids": {
                16: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Process was SIMPLE",
        },
        {
            "name": "ninja_point_rating_details2",
            "question_id_to_form_ids": {
                16: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Got my parcel ON TIME",
        },
        {
            "name": "ninja_point_rating_details3",
            "question_id_to_form_ids": {
                16: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "CLEAR COMMUNICATION about my parcel status",
        },
        {
            "name": "ninja_point_rating_details4",
            "question_id_to_form_ids": {
                16: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Location addresses of collection points were ACCURATE\xa0",
        },
        {
            "name": "ninja_point_rating_details5",
            "question_id_to_form_ids": {
                16: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Staff processed QUICKLY\xa0",
        },
        {
            "name": "ninja_box_rating_details1",
            "question_id_to_form_ids": {
                13: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Process was SIMPLE",
        },
        {
            "name": "ninja_box_rating_details2",
            "question_id_to_form_ids": {
                13: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Got my parcel ON TIME",
        },
        {
            "name": "ninja_box_rating_details3",
            "question_id_to_form_ids": {
                13: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "CLEAR COMMUNICATION about my parcel status",
        },
        {
            "name": "ninja_box_rating_details4",
            "question_id_to_form_ids": {
                13: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "emoji5",
            "detail": "Location addresses of Ninja Boxes were ACCURATE\xa0",
        },
        {
            "name": "feedback",
            "question_id_to_form_ids": {
                8: (
                    201638458540861,
                    201638474640860,
                    201638545240855,
                    201638866840867,
                    201638880340858,
                    201638984340863,
                )
            },
            "type": "flat",
        },
        {
            "name": "notification_updates_rating",
            "question_id_to_form_ids": {
                12: (201638880340858, 201638866840867, 201638984340863),
                21: (201638474640860,),
                22: (201638458540861, 201638545240855),
            },
            "type": "flat",
        },
        {
            "name": "notification_real_time_updates_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Real-time notification updates with no delays",
        },
        {
            "name": "notification_info_on_point_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Information shown can be more to the point",
        },
        {
            "name": "notification_customise_want_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Allow me to customise the notifications I want to receive",
        },
        {
            "name": "notification_customise_where_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Allow me to customise where I want to receive the notifications",
        },
        {
            "name": "notification_no_multi_channels_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Do not send me notifications through multiple channels",
        },
        {
            "name": "notification_more_frequent_updates_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "More frequent updates on my parcel status",
        },
        {
            "name": "notification_less_frequent_updates_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Less frequent updates on my parcel status",
        },
        {
            "name": "notification_self_help_capabilities_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Provide more self-help capabilities within the notification",
        },
        {
            "name": "notification_more_order_details_flag",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "boolean",
            "detail": "Provide more order details information within the notification",
        },
        {
            "name": "notification_malay_language_flag",
            "question_id_to_form_ids": {23: (201638545240855,)},
            "type": "boolean",
            "detail": "Show notifications in Malay language",
        },
        {
            "name": "notification_other_comments",
            "question_id_to_form_ids": {
                13: (201638880340858, 201638866840867, 201638984340863),
                22: (201638474640860,),
                23: (201638458540861, 201638545240855),
            },
            "type": "subtype",
            "detail": "other",
        },
    ],
}