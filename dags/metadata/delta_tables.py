from metadata.constants import D<PERSON><PERSON>AKE_BASE_URI, MASKED_DATALAKE_BASE_URI, DataLakeGcsBucketUris, DataLakeObsBucketUris, LegacyGcsBucketUris

DAG_PREFIX = "datalake_cdc"
TASK_PREFIX = "pii_delta"


class DbDataSource:
    def __init__(self, env, is_masked):
        self._bucket_uri = DataLakeObsBucketUris(env).db
        if is_masked:
            self._bucket_uri = DataLakeObsBucketUris(env).db


class ExternalDbDataSource:
    def __init__(self, env, is_masked):
        self._bucket_uri = DATALAKE_BASE_URI.format(env)
        if is_masked:
            self._bucket_uri = MASKED_DATALAKE_BASE_URI.format(env)


class ThreePlProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/3pl_prod_gl"
        self.AUDIT_LOGS = f"{self._URI_BASE}/audit_logs"
        self.BAGS = f"{self._URI_BASE}/bags"
        self.EVENTS = f"{self._URI_BASE}/events"
        self.EVENTS_2019 = f"{self._URI_BASE}/events_2019"
        self.EVENTS_2020 = f"{self._URI_BASE}/events_2020"
        self.EVENTS_2021 = f"{self._URI_BASE}/events_2021"
        self.EVENTS_2022 = f"{self._URI_BASE}/events_2022"
        self.EVENTS_2023 = f"{self._URI_BASE}/events_2023"
        self.INTERNAL_STATUSES = f"{self._URI_BASE}/internal_statuses"
        self.PARCEL_ITEMS = f"{self._URI_BASE}/parcel_items"
        self.PARCELS = f"{self._URI_BASE}/parcels"
        self.PRODUCTS = f"{self._URI_BASE}/products"
        self.SERVICES = f"{self._URI_BASE}/services"
        self.SHIPMENT_EVENTS = f"{self._URI_BASE}/shipment_events"
        self.SHIPMENTS = f"{self._URI_BASE}/shipments"
        self.SHIPPERS = f"{self._URI_BASE}/shippers"
        self.SHIPMENT_PARCELS = f"{self._URI_BASE}/shipment_parcels"
        self.VENDOR_PORTS = f"{self._URI_BASE}/vendor_ports"
        self.VENDOR_RATE_CARDS = f"{self._URI_BASE}/vendor_rate_cards"
        self.VENDOR_STATUSES = f"{self._URI_BASE}/vendor_statuses"
        self.VENDORS = f"{self._URI_BASE}/vendors"


class ThreePlOmsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/3pl_oms_prod_gl"
        self.THIRD_PARTY_VENDORS = f"{self._URI_BASE}/third_party_vendors"
        self.THIRD_PARTY_ORDERS = f"{self._URI_BASE}/third_party_orders"


class AAAProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/aaa_prod_gl"
        self.USER_INFO = f"{self._URI_BASE}/user_info"


class AddressingProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/addressing_prod_gl"
        self.ZONES = f"{self._URI_BASE}/zones"

    DAG_ID = f"{DAG_PREFIX}_addressing_prod_gl"

    class Task:
        ZONES = f"{TASK_PREFIX}_addressing_prod_gl_zones"

class AddressingProdGLv2(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/addressing_prod_gl"
        self.ZONES = f"{self._URI_BASE}/zones"

    DAG_ID = f"{DAG_PREFIX}_addressing_prod_gl_v2"

    class Task:
        ZONES = f"{TASK_PREFIX}_addressing_prod_gl_zones"


class Amast:
    def __init__(self, country, env="prod", is_masked=False):
        if is_masked:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/amast/{{}}/{country}"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/amast/{{}}/{country}"
        self.CREDIT_NOTE_DETAILS = f"{self._URI_BASE.format('credit_note_details')}"
        self.CREDIT_NOTES = f"{self._URI_BASE.format('credit_notes')}"
        self.INVOICES = f"{self._URI_BASE.format('invoices')}"
        self.INVOICE_DETAILS = f"{self._URI_BASE.format('invoice_details')}"
        self.INVOICE_BUNDLE_DETAILS = f"{self._URI_BASE.format('invoice_bundle_details')}"
        self.LOVS = f"{self._URI_BASE.format('lovs')}"
        self.OUTLET_CREDIT = f"{self._URI_BASE.format('outlet_credit')}"
        self.OUTLET_VISIT_PLANS = f"{self._URI_BASE.format('outlet_visit_plans')}"
        self.OUTLETS = f"{self._URI_BASE.format('outlets')}"
        self.PRINCIPALS = f"{self._URI_BASE.format('principals')}"
        self.PRINTOUTS = f"{self._URI_BASE.format('printouts')}"
        self.SITES = f"{self._URI_BASE.format('sites')}"
        self.SKU_UOM_CONVERSIONS = f"{self._URI_BASE.format('sku_uom_conversions')}"
        self.SKU_UOMS = f"{self._URI_BASE.format('sku_uoms')}"
        self.SKUS = f"{self._URI_BASE.format('skus')}"
        self.OUTLET_VISIT_PLANS = f"{self._URI_BASE.format('outlet_visit_plans')}"
        self.VAN_VISITS = f"{self._URI_BASE.format('van_visits')}"
        self.VANS = f"{self._URI_BASE.format('vans')}"
        self.VOID_INVOICES = f"{self._URI_BASE.format('void_invoices')}"


class Appsflyer:
    def __init__(self, env="prod", is_masked=False):
        if is_masked:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/appsflyer/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/appsflyer/delta"
        self.RAW_DATA_REPORTS = f"{self._URI_BASE}/raw_data_reports"
        self.AGGREGATED_REPORT = f"{self._URI_BASE}/aggregated_report"


class BillingProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/billing_prod_gl"
        self.INVOICE_DISPUTES = f"{self._URI_BASE}/invoice_disputes"
        self.INVOICE_DISPUTE_TIDS = f"{self._URI_BASE}/invoice_dispute_tids"
        self.INVOICE_DISPUTE_TID_ISSUES = f"{self._URI_BASE}/invoice_dispute_tid_issues"
        self.INVOICE_DISPUTE_TYPES = f"{self._URI_BASE}/invoice_dispute_types"
        self.PRICED_ORDERS = f"{self._URI_BASE}/priced_orders"
        self.VOLUME_DISCOUNT_ORDERS = f"{self._URI_BASE}/volume_discount_orders"


class ControlProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/control_prod_gl"
        self.PARCEL_PICKUP_JOBS = f"{self._URI_BASE}/parcel_pickup_jobs"
        self.PICKUP_APPOINTMENT_JOBS = f"{self._URI_BASE}/pickup_appointment_jobs"
        self.PICKUP_APPOINTMENT_JOBS_PICKUP_TAGS = f"{self._URI_BASE}/pickup_appointment_jobs_pickup_tags"
        self.PICKUP_APPOINTMENT_JOBS_ORDERS = f"{self._URI_BASE}/pickup_appointment_jobs_orders"
        self.PICKUP_TAGS = f"{self._URI_BASE}/pickup_tags"
        self.PROOFS = f"{self._URI_BASE}/proofs"
        self.PROOF_JOBS = f"{self._URI_BASE}/proof_jobs"
        self.PROOF_RESERVATIONS = f"{self._URI_BASE}/proof_reservations"
        self.PROOF_TRACKING_IDS = f"{self._URI_BASE}/proof_tracking_ids"
        self.PROOF_TRANSACTIONS = f"{self._URI_BASE}/proof_transactions"
        self.PUDO_PICKUP_APPOINTMENT_JOBS = f"{self._URI_BASE}/pudo_pickup_appointment_jobs"

    DAG_ID = f"{DAG_PREFIX}_control_prod_gl"

    class Task:
        PICKUP_APPOINTMENT_JOBS = f"{TASK_PREFIX}_control_prod_gl_pickup_appointment_jobs"


class CoreProdGL(DbDataSource):
    def __init__(self, system_id, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/core_prod_{system_id}"
        self.BLOBS = f"{self._URI_BASE}/blobs"
        self.CODS = f"{self._URI_BASE}/cods"
        self.DELIVERY_TYPES = f"{self._URI_BASE}/delivery_types"
        self.INBOUND_SCANS = f"{self._URI_BASE}/inbound_scans"
        self.INDUSTRIES = f"{self._URI_BASE}/industries"
        self.ORDER_DETAILS = f"{self._URI_BASE}/order_details"
        self.ORDER_PICKUPS = f"{self._URI_BASE}/order_pickups"
        self.ORDER_SLA = f"{self._URI_BASE}/order_sla"
        self.ORDER_TAG_NAMES = f"{self._URI_BASE}/order_tag_names"
        self.ORDER_TAGS = f"{self._URI_BASE}/order_tags"
        self.ORDERS = f"{self._URI_BASE}/orders"
        self.RESERVATION_BLOB = f"{self._URI_BASE}/reservation_blob"
        self.RESERVATION_FAILURE_REASON = f"{self._URI_BASE}/reservation_failure_reason"
        self.RESERVATIONS = f"{self._URI_BASE}/reservations"
        self.ROUTE_LOGS = f"{self._URI_BASE}/route_logs"
        self.ROUTE_WAYPOINT = f"{self._URI_BASE}/route_waypoint"
        self.SALESPERSONS = f"{self._URI_BASE}/salespersons"
        self.TRANSACTION_BLOB = f"{self._URI_BASE}/transaction_blob"
        self.TRANSACTION_FAILURE_REASON = f"{self._URI_BASE}/transaction_failure_reason"
        self.TRANSACTIONS = f"{self._URI_BASE}/transactions"
        self.THIRD_PARTY_SHIPPERS = f"{self._URI_BASE}/third_party_shippers"
        self.WAREHOUSE_SWEEPS = f"{self._URI_BASE}/warehouse_sweeps"
        self.WAYPOINTS = f"{self._URI_BASE}/waypoints"

    DAG_ID_ID = f"{DAG_PREFIX}_core_prod_id"
    DAG_ID_MY = f"{DAG_PREFIX}_core_prod_my"

    class Task_ID:
        ORDERS = f"{TASK_PREFIX}_core_prod_id_orders"
        TRANSACTIONS = f"{TASK_PREFIX}_core_prod_id_transactions"

    class Task_MY:
        ORDERS = f"{TASK_PREFIX}_core_prod_my_orders"
        TRANSACTIONS = f"{TASK_PREFIX}_core_prod_my_transactions"
        INBOUND_SCANS = f"{TASK_PREFIX}_core_prod_my_inbound_scans"
        ROUTE_WAYPOINT = f"{TASK_PREFIX}_core_prod_my_route_waypoint"
        RESERVATIONS = f"{TASK_PREFIX}_core_prod_my_reservations"


class ConsigneeProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self.CONSIGNEE_TO_CONSIGNEE_REQUESTS = f"{self._bucket_uri}/consignee_prod_gl/consignee_to_consignee_requests"
        self.RATINGS = f"{self._bucket_uri}/consignee_prod_gl/ratings"


class DirectProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/direct_prod_gl"
        self.AR_INVOICES = f"{self._URI_BASE}/ar_invoices"
        self.AR_INVOICE_HISTORIES = f"{self._URI_BASE}/ar_invoice_histories"
        self.AR_PAYMENTS = f"{self._URI_BASE}/ar_payments"
        self.BOX_ITEMS = f"{self._URI_BASE}/box_items"
        self.BOXES = f"{self._URI_BASE}/boxes"
        self.BOX_STATUS_HISTORIES = f"{self._URI_BASE}/box_status_histories"
        self.CROSSBORDER_PROVIDERS = f"{self._URI_BASE}/crossborder_providers"
        self.EVENT_HISTORIES = f"{self._URI_BASE}/event_histories"
        self.FM_FEE_ISSUE_HISTORIES = f"{self._URI_BASE}/fm_fee_issue_histories"
        self.INVOICE_HISTORIES = f"{self._URI_BASE}/invoice_histories"
        self.LEVY_RATES = f"{self._URI_BASE}/levy_rates"
        self.ITEM_ISSUE_HISTORIES = f"{self._URI_BASE}/item_issue_histories"
        self.ITEM_ISSUE_TYPES = f"{self._URI_BASE}/item_issue_types"
        self.ITEM_ISSUES = f"{self._URI_BASE}/item_issues"
        self.ITEM_ISSUES_ITEM_ISSUES_TYPES = f"{self._URI_BASE}/item_issues_item_issues_types"
        self.PAYMENT_METHODS = f"{self._URI_BASE}/payment_methods"
        self.PURCHASE_ORDER_PAYMENTS = f"{self._URI_BASE}/purchase_order_payments"
        self.PURCHASE_ORDERS = f"{self._URI_BASE}/purchase_orders"
        self.REQUEST_ORDER_LOGISTIC_XBS = f"{self._URI_BASE}/request_order_logistic_xbs"
        self.REQUEST_ORDER_ITEMS = f"{self._URI_BASE}/request_order_items"
        self.REQUEST_ORDER_METADATA = f"{self._URI_BASE}/request_order_metadata"
        self.REQUEST_ORDERS = f"{self._URI_BASE}/request_orders"
        self.RESERVATIONS = f"{self._URI_BASE}/reservations"
        self.SERVICE_FEES = f"{self._URI_BASE}/service_fees"
        self.SHIPPERS = f"{self._URI_BASE}/shippers"
        self.SHIPPER_FINANCING_STATUSES = f"{self._URI_BASE}/shipper_financing_statuses"
        self.STAFFS = f"{self._URI_BASE}/staffs"
        self.SUPPLIERS = f"{self._URI_BASE}/suppliers"
        self.WAYPOINTS = f"{self._URI_BASE}/waypoints"
        self.XB_RATES = f"{self._URI_BASE}/xb_rates"


class DirectSupplierProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/direct_supplier_prod_gl"
        self.SUPPLIERS = f"{self._URI_BASE}/suppliers"


class DPProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/dp_prod_gl"
        self.DP_OPERATING_HOURS = f"{self._URI_BASE}/dp_operating_hours"
        self.DP_RESERVATION_EVENTS = f"{self._URI_BASE}/dp_reservation_events"
        self.DP_RESERVATIONS = f"{self._URI_BASE}/dp_reservations"
        self.DPS = f"{self._URI_BASE}/dps"
        self.PARTNERS = f"{self._URI_BASE}/partners"


class DriverProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/driver_prod_gl"
        self.DRIVER_CONTACTS = f"{self._URI_BASE}/driver_contacts"
        self.DRIVER_TYPES = f"{self._URI_BASE}/driver_types"
        self.DRIVER_ZONE_PREFERENCES = f"{self._URI_BASE}/driver_zone_preferences"
        self.DRIVERS = f"{self._URI_BASE}/drivers"
        self.FAILURE_REASON_CODES = f"{self._URI_BASE}/failure_reason_codes"
        self.FAILURE_REASONS = f"{self._URI_BASE}/failure_reasons"
        self.HUB_HANDOVER_PARCELS = f"{self._URI_BASE}/hub_handover_parcels"
        self.HUB_HANDOVERS = f"{self._URI_BASE}/hub_handovers"
        self.INVALID_ATTEMPTS = f"{self._URI_BASE}/invalid_attempts"
        self.ROUTE_HUB_HANDOVERS = f"{self._URI_BASE}/route_hub_handovers"

    DAG_ID = f"{DAG_PREFIX}_driver_prod_gl"

    class Task:
        DRIVERS = f"{TASK_PREFIX}_driver_prod_gl_drivers"
        DRIVER_TYPES = f"{TASK_PREFIX}_driver_prod_gl_driver_types"


class DriverVantageProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/driver_vantage_prod_gl"
        self.PENALTY_V2 = f"{self._URI_BASE}/penalty_v2"
        self.SUSPENSION_V2 = f"{self._URI_BASE}/suspension_v2"


class Eber(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        if is_masked == True:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/eber/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/eber/delta"
        self.MEMBER_TRANSACTIONS = f"{self._URI_BASE}/member_transactions"
        self.POINT_TRANSACTIONS = f"{self._URI_BASE}/point_transactions"
        self.USERS = f"{self._URI_BASE}/users"


class EPIProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/epi_prod_gl"
        self.EXTERNAL_LINKED_ACCOUNTS = f"{self._URI_BASE}/external_linked_accounts"
        self.EXTERNAL_LINKED_ORDERS = f"{self._URI_BASE}/external_linked_orders"
        self.EXTERNAL_XDOCK_ORDER_MAPPINGS = f"{self._URI_BASE}/external_xdock_order_mappings"


class EventsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/events_prod_gl"
        self.PICKUP_EVENTS = f"{self._URI_BASE}/pickup_events"
        self.ORDER_EVENT_TYPES = f"{self._URI_BASE}/order_event_types"
        self.ORDER_EVENTS = f"{self._URI_BASE}/order_events"

    DAG_ID = f"{DAG_PREFIX}_events_prod_gl"

    class Task:
        ORDER_EVENTS = f"{TASK_PREFIX}_events_prod_gl_order_events"


class FacebookAds:
    def __init__(self, env="prod", is_masked=False):
        if is_masked:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/facebook/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/facebook/delta"
        self.FACEBOOK_ADS = f"{self._URI_BASE}/ad"


class FirstMileProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/first_mile_prod_gl"
        self.SHIPPER_FIRST_MILE_ADDRESSES = f"{self._URI_BASE}/shipper_first_mile_addresses"


class GDrive:
    def __init__(self, env="prod"):
        self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/gdrive/delta"
        self.DP_PARTNERS_ENRICHED = f"{self._URI_BASE}/dp_partners_enriched"
        self.DPS_ENRICHED = f"{self._URI_BASE}/dps_enriched"
        self.DRIVER_TYPES_ENRICHED = f"{self._URI_BASE}/driver_types_enriched"
        self.EXCLUSION_REQUESTS = f"{self._URI_BASE}/exclusion_requests"
        self.EXCLUSION_REQUEST_DETAILS = f"{self._URI_BASE}/exclusion_request_details"
        self.HOLIDAYS = f"{self._URI_BASE}/holidays"
        self.HOLIDAYS_RECOVERY = f"{self._URI_BASE}/holidays_recovery"
        self.HOLIDAYS_SHIPPER = f"{self._URI_BASE}/holidays_shipper"
        self.HOLIDAYS_SORT = f"{self._URI_BASE}/holidays_sort"
        self.HOLIDAYS_SPEED = f"{self._URI_BASE}/holidays_speed"
        self.HUBS_ENRICHED = f"{self._URI_BASE}/hubs_enriched"
        self.LEADS_NOT_IN_SF = f"{self._URI_BASE}/leads_not_in_sf"
        self.LONGTAIL_SLA_HOURS = f"{self._URI_BASE}/longtail_sla_hours"
        self.SALESPERSONS_ENRICHED_ID = f"{self._URI_BASE}/salespersons_enriched_id"
        self.SALESPERSONS_ENRICHED_MM = f"{self._URI_BASE}/salespersons_enriched_mm"
        self.SALESPERSONS_ENRICHED_MY = f"{self._URI_BASE}/salespersons_enriched_my"
        self.SALESPERSONS_ENRICHED_PH = f"{self._URI_BASE}/salespersons_enriched_ph"
        self.SALESPERSONS_ENRICHED_SG = f"{self._URI_BASE}/salespersons_enriched_sg"
        self.SALESPERSONS_ENRICHED_TH = f"{self._URI_BASE}/salespersons_enriched_th"
        self.SALESPERSONS_ENRICHED_VN = f"{self._URI_BASE}/salespersons_enriched_vn"
        self.SHIPPER_ATTRIBUTES = f"{self._URI_BASE}/shipper_attributes"
        self.SHIPPER_SLA_DAYS = f"{self._URI_BASE}/shipper_sla_days"
        self.SLA_EXTENSIONS = f"{self._URI_BASE}/sla_extensions"
        self.SLA_HARDCODED = f"{self._URI_BASE}/sla_hardcoded"


class GoogleAds:
    def __init__(self, env="prod", is_masked=False):
        if is_masked:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/google_ads/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/google_ads/delta"
        self.GOOGLE_ADS = f"{self._URI_BASE}/ad"


class Gsuite(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/gsuite/delta"
        self.USERS = f"{self._URI_BASE}/users"


class HubProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/hub_prod_gl"
        self.FLIGHT_INFO = f"{self._URI_BASE}/flight_info"
        self.FLIGHT_MAWB = f"{self._URI_BASE}/flight_mawb"
        self.HUB_RELATION_SCHEDULES = f"{self._URI_BASE}/hub_relation_schedules"
        self.HUB_RELATIONS = f"{self._URI_BASE}/hub_relations"
        self.MM_DRIVER_DETAILS = f"{self._URI_BASE}/mm_driver_details"
        self.MOVEMENT_EVENTS = f"{self._URI_BASE}/movement_events"
        self.MOVEMENT_TRIP_DRIVERS = f"{self._URI_BASE}/movement_trip_drivers"
        self.MOVEMENT_TRIP_EVENTS = f"{self._URI_BASE}/movement_trip_events"
        self.MOVEMENT_TRIPS = f"{self._URI_BASE}/movement_trips"
        self.LANDHAUL_VENDORS = f"{self._URI_BASE}/landhaul_vendors"
        self.SCANS = f"{self._URI_BASE}/scans"
        self.SEA_VENDORS = f"{self._URI_BASE}/sea_vendors"
        self.SEAWAY_BILLS = f"{self._URI_BASE}/seaway_bills"
        self.SHIPMENT_DIMENSIONS = f"{self._URI_BASE}/shipment_dimensions"
        self.SHIPMENT_EVENTS = f"{self._URI_BASE}/shipment_events"
        self.SHIPMENT_EXT_AWBS = f"{self._URI_BASE}/shipment_ext_awbs"
        self.SHIPMENT_ORDERS = f"{self._URI_BASE}/shipment_orders"
        self.SHIPMENT_SEAWAY_BILLS = f"{self._URI_BASE}/shipment_seaway_bills"
        self.SHIPMENT_TRIPS = f"{self._URI_BASE}/shipment_trips"
        self.SHIPMENTS = f"{self._URI_BASE}/shipments"
        self.TRIP_CANCELLATION_REASONS = f"{self._URI_BASE}/trip_cancellation_reasons"
        self.TRIP_SAFETY_CHECKS = f"{self._URI_BASE}/trip_safety_checks"
        self.TRIP_SHIPMENT_SCANS = f"{self._URI_BASE}/trip_shipment_scans"
        self.TRIP_UNSCANNED_SHIPMENTS = f"{self._URI_BASE}/trip_unscanned_shipments"
        self.TRUCK_TYPES = f"{self._URI_BASE}/truck_types"
        self.TRUCK_UTILIZATIONS = f"{self._URI_BASE}/truck_utilizations"
        self.VENDORS = f"{self._URI_BASE}/vendors"


class Jira(ExternalDbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/jira/delta"
        self.ISSUES = f"{self._URI_BASE}/issues"


class Jotform(ExternalDbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/jotform/delta"
        self.CUSTOMER_EFFORT_SCORE = f"{self._URI_BASE}/customer_effort_score"
        self.CUSTOMER_EFFORT_SCORE_FSR = f"{self._URI_BASE}/customer_effort_score_fsr"
        self.DELIVERY_SUCCESS_FEEDBACK = f"{self._URI_BASE}/delivery_success_feedback"
        self.ORDER_NPS_SUBMISSIONS = f"{self._URI_BASE}/order_nps_submissions"


class Kafka(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/kafka"
        self.WEBHOOK_HISTORY = f"{self._URI_BASE}/prod-webhook-push-act-webhook-push-completed"


class MartSPMgmtProdGL:
    def __init__(self, env="prod", is_masked=False):
        if is_masked:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/db/mart_sp_mgmt_prod_gl/{{}}/"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/db/mart_sp_mgmt_prod_gl/{{}}/"
        self.ORDER_INVENTORIES = f"{self._URI_BASE.format('order_inventories')}"
        self.ORDERS = f"{self._URI_BASE.format('orders')}"
        self.PAYMENT_METHODS = f"{self._URI_BASE.format('payment_methods')}"
        self.SALESPERSONS = f"{self._URI_BASE.format('salepersons')}"
        self.WAREHOUSES = f"{self._URI_BASE.format('warehouses')}"


class MovementTripProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/movement_trip_prod_gl"
        self.TRIPS_VEHICLE = f"{self._URI_BASE}/trips_vehicle"
        self.TRUCK_TYPES = f"{self._URI_BASE}/truck_types"
        self.VEHICLES= f"{self._URI_BASE}/vehicles"



class NotificationsV2ProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/notifications_v2_prod_gl"
        self.CALLOUT_POLICIES = f"{self._URI_BASE}/callout_policies"
        self.CHAT_MESSAGE_POLICIES = f"{self._URI_BASE}/chat_message_policies"
        self.SCHEDULED_CALLOUTS = f"{self._URI_BASE}/scheduled_callouts"
        self.SCHEDULED_CHAT_MESSAGES= f"{self._URI_BASE}/scheduled_chat_messages"


class NotificationsVoiceProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/notifications_voice_prod_gl"
        self.CALLOUT_JOBS = f"{self._URI_BASE}/callout_jobs"
        self.VONAGE_EVENT_WEBHOOK_LOGS = f"{self._URI_BASE}/vonage_event_webhook_logs"


class OCreateProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/ocreate_prod_gl"
        self.RESERVE_TRACKING_IDS = f"{self._URI_BASE}/reserve_tracking_ids"


class OrderProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/order_prod_gl"
        self.MULTI_PIECE_SHIPMENTS = f"{self._URI_BASE}/multi_piece_shipments"
        self.MULTI_PIECE_SHIPMENT_ORDERS = f"{self._URI_BASE}/multi_piece_shipment_orders"


class PaJobSearchProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/pa_job_search_prod_gl"
        self.PICKUP_APPOINTMENT_JOBS_SEARCH = f"{self._URI_BASE}/pickup_appointment_jobs_search"


class PodValidationProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/pod_validation_prod_gl"
        self.ASSIGNMENTS = f"{self._URI_BASE}/assignments"
        self.TASKS = f"{self._URI_BASE}/tasks"
        self.PARCELS = f"{self._URI_BASE}/parcels"
        self.PHOTOS = f"{self._URI_BASE}/photos"
        self.INVALID_POD_REASONS = f"{self._URI_BASE}/invalid_pod_reasons"


class PricingProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/pricing_prod_gl"
        self.PRICING_ORDERS = f"{self._URI_BASE}/pricing_orders"
        self.PRICING_ORDERS_HISTORY = f"{self._URI_BASE}/pricing_orders_history"


class RouteProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/route_prod_gl"
        self.JOB_WAYPOINTS = f"{self._URI_BASE}/job_waypoints"
        self.MILKRUN_GROUPS = f"{self._URI_BASE}/milkrun_groups"
        self.MILKRUN_SAMS = f"{self._URI_BASE}/milkrun_sams"
        self.ROUTE_LOGS = f"{self._URI_BASE}/route_logs"
        self.ROUTE_TAGS = f"{self._URI_BASE}/route_tags"
        self.TAGS = f"{self._URI_BASE}/tags"
        self.WAYPOINTS = f"{self._URI_BASE}/waypoints"
        self.WAYPOINT_PHOTOS = f"{self._URI_BASE}/waypoint_photos"

    DAG_ID = f"{DAG_PREFIX}_route_prod_gl"

    class Task:
        ROUTE_LOGS = f"{TASK_PREFIX}_route_prod_gl_route_logs"
        JOB_WAYPOINTS = f"{TASK_PREFIX}_route_prod_gl_job_waypoints"
        WAYPOINTS = f"{TASK_PREFIX}_route_prod_gl_waypoints"


class RecoveryCommsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/recovery_comms_prod_gl"
        self.DELIVERY_FAILURE_ORDERS = f"{self._URI_BASE}/delivery_failure_orders"
        self.TICKET_EXPIRATIONS = f"{self._URI_BASE}/ticket_expirations"


class SalesCloud(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        if is_masked == True:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/salesforce/sales_cloud/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/salesforce/sales_cloud/delta"
        self.ACCOUNT = f"{self._URI_BASE}/account"
        self.ACCOUNT_HISTORY = f"{self._URI_BASE}/account_history"
        self.ACCOUNT_CONTACT_RELATION = f"{self._URI_BASE}/account_contact_relation"
        self.CASE = f"{self._URI_BASE}/case"
        self.CASE_COMMENT = f"{self._URI_BASE}/case_comment"
        self.CASE_HISTORY = f"{self._URI_BASE}/case_history"
        self.CASE_ASSIGNEE_MAPPING_C = f"{self._URI_BASE}/case_assignee_mapping_c"
        self.CASE_TEAM_MEMBER = f"{self._URI_BASE}/case_team_member"
        self.CONTACT = f"{self._URI_BASE}/contact"
        self.CONTENT_VERSION = f"{self._URI_BASE}/content_version"
        self.EMAIL_MESSAGE = f"{self._URI_BASE}/email_message"
        self.EVENT = f"{self._URI_BASE}/event"
        self.FEED_ITEM = f"{self._URI_BASE}/feed_item"
        self.GROUP = f"{self._URI_BASE}/group"
        self.ISSUE_TYPE_MAPPING_C = f"{self._URI_BASE}/issue_type_mapping_c"
        self.LEAD = f"{self._URI_BASE}/lead"
        self.MARKETING_C = f"{self._URI_BASE}/marketing_c"
        self.OPPORTUNITY = f"{self._URI_BASE}/opportunity"
        self.RECORD_TYPE = f"{self._URI_BASE}/record_type"
        self.TASK = f"{self._URI_BASE}/task"
        self.USER = f"{self._URI_BASE}/user"
        self.USER_ROLE = f"{self._URI_BASE}/user_role"


class ShipperProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/shipper_prod_gl"
        self.ACCOUNT_TYPES = f"{self._URI_BASE}/account_types"
        self.CORPORATE_BRANCHES = f"{self._URI_BASE}/corporate_branches"
        self.DISTRIBUTION_CHANNELS = f"{self._URI_BASE}/distribution_channels"
        self.MARKETPLACE_SELLERS = f"{self._URI_BASE}/marketplace_sellers"
        self.SHIPPER_ADDRESSES = f"{self._URI_BASE}/shipper_addresses"
        self.SHIPPER_ADDRESS_MILKRUN_SETTINGS = f"{self._URI_BASE}/shipper_address_milkrun_settings"
        self.SHIPPER_METADATA = f"{self._URI_BASE}/shipper_metadata"
        self.SHIPPER_PREFIXES = f"{self._URI_BASE}/shipper_prefixes"
        self.SHIPPER_SETTINGS = f"{self._URI_BASE}/shipper_settings"
        self.SHIPPERS = f"{self._URI_BASE}/shippers"

class SnsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/sns_prod_gl"
        self.CHAT_TICKETS = f"{self._URI_BASE}/chat_tickets"
        self.CS_FEEDBACK_RATINGS = f"{self._URI_BASE}/cs_feedback_ratings"
        self.EXTERNAL_PLATFORM_REFERENCES = f"{self._URI_BASE}/external_platform_references"
        self.IDENTITIES = f"{self._URI_BASE}/identities"


class SnsPlatformsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/sns_platforms_prod_gl"
        self.MESSAGE_LOGS = f"{self._URI_BASE}/message_logs"


class SortProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/sort_prod_gl"
        self.DUMMY_HUB_MAP = f"{self._URI_BASE}/dummy_hub_map"
        self.HUB_COORDS_CHANGE_EVENTS = f"{self._URI_BASE}/hub_coords_change_events"
        self.HUBS = f"{self._URI_BASE}/hubs"
        self.REGIONS = f"{self._URI_BASE}/regions"
        self.SCAN_RESULT = f"{self._URI_BASE}/scan_result"
        self.SORT_MISTAKES = f"{self._URI_BASE}/sort_mistakes"
        self.SORTING_NODES = f"{self._URI_BASE}/sorting_nodes"


class SortMistakeProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/sort_mistake_prod_gl"
        self.INTRA_HUB_NODES = f"{self._URI_BASE}/intra_hub_nodes"
        self.SCAN_RESULTS = f"{self._URI_BASE}/scan_results"
        self.SORT_NODES = f"{self._URI_BASE}/sort_nodes"


class SortVendorProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/sort_vendor_prod_gl"
        self.PARCEL_IMAGES_V2 = f"{self._URI_BASE}/parcel_images_v2"
        self.PARCEL_MEASUREMENT_SCAN = f"{self._URI_BASE}/parcel_measurement_scan"


class StationProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/station_prod_gl"
        self.RDO_SCANS = f"{self._URI_BASE}/rdo_scans"


class TicketingProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/ticketing_prod_gl"
        self.CUSTOM_FIELDS = f"{self._URI_BASE}/custom_fields"
        self.GROUPS = f"{self._URI_BASE}/groups"
        self.STATUSES = f"{self._URI_BASE}/statuses"
        self.TICKET_CUSTOM_FIELDS = f"{self._URI_BASE}/ticket_custom_fields"
        self.TICKET_LOGS = f"{self._URI_BASE}/ticket_logs"
        self.TICKET_SUBTYPES = f"{self._URI_BASE}/ticket_subtypes"
        self.TICKET_TYPES = f"{self._URI_BASE}/ticket_types"
        self.TICKETS = f"{self._URI_BASE}/tickets"
        self.USERS = f"{self._URI_BASE}/users"


class URLShortenerGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/url_shortener_gl"
        self.LINK_OPENS = f"{self._URI_BASE}/link_opens"


class WebhookHistoryProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/webhook_history_prod_gl"
        self.MMCC_WEBHOOK_HISTORY = f"{self._URI_BASE}/mmcc_webhook_history"


class WmsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/wms_prod_gl"
        self.PARCELS = f"{self._URI_BASE}/parcels"


class XbOpsProdGL(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        self._URI_BASE = f"{self._bucket_uri}/xb_operations_prod_gl"
        self.EXPORT_JOB_ITEMS = f"{self._URI_BASE}/export_job_items"
        self.EXPORT_JOBS = f"{self._URI_BASE}/export_jobs"
        self.INVOICES = f"{self._URI_BASE}/invoices"
        self.INVOICE_SHIPMENTS = f"{self._URI_BASE}/invoice_shipments"
        self.INVOICE_PARCELS = f"{self._URI_BASE}/invoice_parcels"
        self.INVOICE_BAGS = f"{self._URI_BASE}/invoice_bags"
        self.INVOICE_PERIODS = f"{self._URI_BASE}/invoice_periods"
        self.INVOICE_ALLOCATED_COSTS = f"{self._URI_BASE}/invoice_allocated_costs"
        self.RECEIVING_JOBS = f"{self._URI_BASE}/receiving_jobs"
        self.RECEIVING_TASK_ITEMS = f"{self._URI_BASE}/receiving_task_items"
        self.TRANSITS_BAGS_TAB = f"{self._URI_BASE}/transits_bags_tab"
        self.TRANSITS_TAB = f"{self._URI_BASE}/transits_tab"
        self.WAREHOUSES_TAB = f"{self._URI_BASE}/warehouses_tab"


class Zendesk(DbDataSource):
    def __init__(self, env="prod", is_masked=False):
        super().__init__(env, is_masked)
        if is_masked == True:
            self._URI_BASE = f"{MASKED_DATALAKE_BASE_URI.format(env)}/zendesk/delta"
        else:
            self._URI_BASE = f"{DATALAKE_BASE_URI.format(env)}/zendesk/delta"
        self.BRANDS = f"{self._URI_BASE}/brands"
        self.GROUPS = f"{self._URI_BASE}/groups"
        self.ORGANIZATIONS = f"{self._URI_BASE}/organizations"
        self.TICKET_FIELDS = f"{self._URI_BASE}/ticket_fields"
        self.TICKET_FORMS = f"{self._URI_BASE}/ticket_forms"
        self.TICKET_METRIC_EVENTS = f"{self._URI_BASE}/ticket_metric_events"
        self.TICKETS = f"{self._URI_BASE}/tickets"
        self.USERS = f"{self._URI_BASE}/users"