GOOGLE_ADS_CONFIG = {
    "ad": {
        "template": {
            "standard_query": """
                SELECT
                    customer.id,
                    customer.descriptive_name,
                    customer.currency_code,
                    segments.date,
                    campaign.start_date,
                    campaign.end_date,
                    segments.ad_network_type,
                    campaign.id,
                    campaign.name,
                    campaign.bidding_strategy_type,
                    campaign.advertising_channel_type,
                    ad_group.id,
                    ad_group.name,
                    ad_group_ad.ad.id,
                    ad_group_ad.ad.name,
                    ad_group_ad.ad.type,
                    ad_group_ad.action_items,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.video_views,
                    metrics.video_quartile_p25_rate,
                    metrics.video_quartile_p50_rate,
                    metrics.video_quartile_p75_rate,
                    metrics.video_quartile_p100_rate,
                    metrics.conversions,
                    metrics.all_conversions
                FROM ad_group_ad
                WHERE
                    metrics.cost_micros > 0
                    AND segments.date BETWEEN "{start_date}" AND "{end_date}"
            """,
            "performance_query": """
                SELECT
                    customer.id,
                    customer.descriptive_name,
                    customer.currency_code,
                    segments.date,
                    campaign.start_date,
                    campaign.end_date,
                    segments.ad_network_type,
                    campaign.id,
                    campaign.name,
                    campaign.bidding_strategy_type,
                    campaign.advertising_channel_type,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.video_views,
                    metrics.video_quartile_p25_rate,
                    metrics.video_quartile_p50_rate,
                    metrics.video_quartile_p75_rate,
                    metrics.video_quartile_p100_rate,
                    metrics.conversions,
                    metrics.all_conversions
                FROM campaign
                WHERE metrics.cost_micros > 0
                    AND campaign.advertising_channel_type = "PERFORMANCE_MAX"
                    AND segments.date BETWEEN "{start_date}" AND "{end_date}"
            """,
        },
        "schema": {
            "customer_resource_name": "str",
            "customer_id": "str",
            "customer_descriptive_name": "str",
            "customer_currency_code": "str",
            "campaign_resource_name": "str",
            "campaign_advertising_channel_type": "str",
            "campaign_bidding_strategy_type": "str",
            "campaign_name": "str",
            "campaign_id": "str",
            "campaign_start_date": "datetime64",
            "campaign_end_date": "datetime64",
            "ad_group_resource_name": "str",
            "ad_group_id": "str",
            "ad_group_name": "str",
            "metrics_clicks": "int64",
            "metrics_video_views": "int64",
            "metrics_conversions": "float64",
            "metrics_cost_micros": "float64",
            "metrics_all_conversions": "float64",
            "metrics_impressions": "int64",
            "ad_group_ad_resource_name": "str",
            "ad_group_ad_ad_type": "str",
            "ad_group_ad_ad_resource_name": "str",
            "ad_group_ad_ad_id": "str",
            "segments_ad_network_type": "str",
            "segments_date": "datetime64",
            "ad_group_ad_action_items": "str",
            "metrics_video_quartile_p100_rate": "str",
            "metrics_video_quartile_p25_rate": "str",
            "metrics_video_quartile_p50_rate": "str",
            "metrics_video_quartile_p75_rate": "str",
            "ad_group_ad_ad_name": "str",
        },
        "primary_columns": [
            "customer_id",
            "campaign_id",
            "ad_group_id",
            "ad_group_ad_ad_id",
            "segments_ad_network_type",
            "segments_date",
        ],
    },
}
ACCOUNT_ID_MAPPING = {
    "**********": "sg",
    "**********": "my",
    "**********": "id",
    "**********": "id",
    "**********": "th",
    "**********": "vn",
    "**********": "ph"
}
