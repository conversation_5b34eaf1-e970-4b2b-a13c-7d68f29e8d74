DAG_PREFIX = "data_science"


class AddressVerificationAccuracyMYDAG:
    DAG_ID = f"{DAG_PREFIX}_address_verification_accuracy_my"

    class AccuracyNotificationTask:
        ADDRSS_VERIFICATION_MY_ACCURACY = "notify_accuracy"


class AddressVerificationMYDAG:
    DAG_ID = f"{DAG_PREFIX}_address_verification_my"

    class UpdateTask:
        ADDRESS_VERIFICATION_MY_UPDATE = "model_update"


class FMSimilarAddressGroupingDAG:
    DAG_ID = f"{DAG_PREFIX}_fm_similar_address_grouping"

    class GroupTask:
        FM_SIMILAR_ADDRESS_GROUPING_GROUP = "fm_similar_address_grouping_group"

    class IngestTask:
        FM_SIMILAR_ADDRESS_GROUPING_INGEST = "ingest_shipper_addresses"


class PODAutoValidationDAG:
    DAG_ID = f"{DAG_PREFIX}_pod_auto_validation"

    class IngestTask:
        POD_AUTO_VALIDATION_INGEST = "pod_auto_validation_ingest"

    class PredictTask:
        POD_AUTO_VALIDATION_PREDICT = "pod_auto_validation_predict"


MYSQL_TABLES = {
    "dev": {},
    "prod": {
        "core_prod_my": ["orders"],
        "core_prod_ph": ["orders"],
        "shipper_prod_gl": ["shipper_addresses"],
        "pod_validation_prod_gl": [
            "assignments",
            "parcels",
            "photos",
            "tasks",
        ],
    },
}
