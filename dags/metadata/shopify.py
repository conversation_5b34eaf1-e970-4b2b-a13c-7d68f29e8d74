SHOPIFY_API_VERSION = "2022-01"
SHOPIFY_MAX_RESULTS_LIMIT = 250
SHOPIFY_CONFIG = {
    "customers": {
        "system_ids": ("sg", "my"),
        "path": "/{version}/customers.json?status=any".format(version=SHOPIFY_API_VERSION),
        "schema": {
            "accepts_marketing": "bool",
            "accepts_marketing_updated_at": "datetime64[ns]",
            "addresses": "object",
            "admin_graphql_api_id": "unicode",
            "created_at": "datetime64[ns]",
            "currency": "unicode",
            "default_address": "object",
            "email": "unicode",
            "email_marketing_consent": "unicode",
            "first_name": "unicode",
            "id": "Int64",
            "last_name": "unicode",
            "last_order_id": "float64",
            "last_order_name": "unicode",
            "marketing_opt_in_level": "object",
            "multipass_identifier": "object",
            "note": "unicode",
            "orders_count": "Int64",
            "phone": "unicode",
            "sms_marketing_consent": "object",
            "state": "object",
            "tags": "object",
            "tax_exempt": "bool",
            "tax_exemptions": "object",
            "total_spent": "float64",
            "updated_at": "datetime64[ns]",
            "verified_email": "bool",
        },
        "nested_columns": {
            "addresses",
            "default_address",
            "sms_marketing_consent",
            "tax_exemptions",
            "email_marketing_consent",
        },
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "incremental_range_fields": ("updated_at_min", "updated_at_max"),
        "time_fields": ("accepts_marketing_updated_at", "created_at", "updated_at"),
    },
    "orders": {
        "system_ids": ("sg", "my"),
        "path": "/{version}/orders.json?status=any".format(version=SHOPIFY_API_VERSION),
        "schema": {
            "admin_graphql_api_id": "unicode",
            "app_id": "Int64",
            "billing_address": "object",
            "browser_ip": "unicode",
            "buyer_accepts_marketing": "bool",
            "cancel_reason": "unicode",
            "cancelled_at": "datetime64[ns]",
            "cart_token": "unicode",
            "checkout_id": "Int64",
            "checkout_token": "unicode",
            "client_details": "object",
            "closed_at": "datetime64[ns]",
            "confirmation_number": "string",
            "confirmed": "bool",
            "contact_email": "object",
            "created_at": "datetime64[ns]",
            "currency": "unicode",
            "current_subtotal_price": "object",
            "current_subtotal_price_set": "object",
            "current_total_discounts": "object",
            "current_total_discounts_set": "object",
            "current_total_duties_set": "object",
            "current_total_price": "object",
            "current_total_price_set": "object",
            "current_total_tax": "object",
            "current_total_tax_set": "object",
            "customer": "object",
            "customer_locale": "unicode",
            "device_id": "object",
            "discount_applications": "object",
            "discount_codes": "object",
            "email": "object",
            "estimated_taxes": "bool",
            "financial_status": "object",
            "fulfillment_status": "object",
            "fulfillments": "object",
            "gateway": "object",
            "id": "Int64",
            "landing_site": "object",
            "landing_site_ref": "object",
            "line_items": "object",
            "location_id": "object",
            "name": "object",
            "note": "object",
            "note_attributes": "object",
            "number": "Int64",
            "order_number": "Int64",
            "order_status_url": "object",
            "original_total_duties_set": "object",
            "payment_details": "object",
            "payment_gateway_names": "object",
            "payment_terms": "object",
            "phone": "object",
            "presentment_currency": "object",
            "processed_at": "datetime64[ns]",
            "processing_method": "object",
            "reference": "object",
            "referring_site": "object",
            "refunds": "object",
            "shipping_address": "object",
            "shipping_lines": "object",
            "source_identifier": "object",
            "source_name": "object",
            "source_url": "object",
            "subtotal_price": "object",
            "subtotal_price_set": "object",
            "tags": "object",
            "tax_lines": "object",
            "tax_exempt": "bool",
            "taxes_included": "bool",
            "test": "bool",
            "token": "object",
            "total_discounts": "object",
            "total_discounts_set": "object",
            "total_line_items_price": "object",
            "total_line_items_price_set": "object",
            "total_outstanding": "object",
            "total_price": "object",
            "total_price_set": "object",
            "total_price_usd": "object",
            "total_shipping_price_set": "object",
            "total_tax": "object",
            "total_tax_set": "object",
            "total_tip_received": "object",
            "total_weight": "Int64",
            "updated_at": "datetime64[ns]",
            "user_id": "object",
        },
        "nested_columns": {
            "billing_address",
            "client_details",
            "current_subtotal_price_set",
            "current_total_discounts_set",
            "current_total_duties_set",
            "current_total_price_set",
            "current_total_tax_set",
            "customer",
            "discount_applications",
            "discount_codes",
            "fulfillments",
            "line_items",
            "note_attributes",
            "original_total_duties_set",
            "payment_details",
            "payment_gateway_names",
            "payment_terms",
            "refunds",
            "shipping_address",
            "shipping_lines",
            "subtotal_price_set",
            "tax_lines",
            "total_discounts_set",
            "total_line_items_price_set",
            "total_price_set",
            "total_shipping_price_set",
            "total_tax_set",
        },
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "incremental_range_fields": ("updated_at_min", "updated_at_max"),
        "time_fields": ("cancelled_at", "closed_at", "created_at", "processed_at", "updated_at"),
    },
    "products": {
        "system_ids": ("sg", "my"),
        "path": "/{version}/products.json".format(version=SHOPIFY_API_VERSION),
        "schema": {
            "admin_graphql_api_id": "unicode",
            "body_html": "object",
            "created_at": "datetime64[ns]",
            "handle": "object",
            "id": "Int64",
            "image": "object",
            "images": "object",
            "options": "object",
            "product_type": "unicode",
            "published_at": "datetime64[ns]",
            "published_scope": "unicode",
            "status": "unicode",
            "tags": "unicode",
            "template_suffix": "unicode",
            "title": "unicode",
            "updated_at": "datetime64[ns]",
            "variants": "object",
            "vendor": "unicode",
        },
        "nested_columns": {"image", "images", "options", "variants"},
        "created_at_field": "created_at",
        "updated_at_field": "updated_at",
        "incremental_range_fields": ("updated_at_min", "updated_at_max"),
        "time_fields": ("created_at", "published_at", "updated_at"),
    },
}
