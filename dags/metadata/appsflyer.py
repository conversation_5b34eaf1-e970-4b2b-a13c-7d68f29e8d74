APPSFLYER_API_VERSION = "v5"
MAXIMUM_ROWS = 1000000
SOURCE_APP = {
    "iOS": "id1575177079",
    "Android": "co.ninjavan.dash_mobile",
}
APPSFLYER_CONFIG = {
    "raw_data_reports": {
        "app_ids": {
            "installs_report": {
                "path": "installs_report/{version}".format(version=APPSFLYER_API_VERSION),
                "reattr": ("false", "true"),
                "additional_fields": [
                    "device_model",
                    "keyword_id",
                    "store_reinstall",
                    "deeplink_url",
                    "oaid",
                    "install_app_store",
                    "contributor1_match_type",
                    "contributor2_match_type",
                    "contributor3_match_type",
                    "match_type",
                    "device_category",
                    "gp_referrer",
                    "gp_click_time",
                    "gp_install_begin",
                    "amazon_aid",
                    "keyword_match_type",
                    "att",
                    "conversion_type",
                    "campaign_type",
                    "is_lat",
                ],
            },
            "in_app_events_report": {
                "path": "in_app_events_report/{version}".format(version=APPSFLYER_API_VERSION),
                "reattr": ("false", "true"),
                "additional_fields": [
                    "device_model",
                    "keyword_id",
                    "store_reinstall",
                    "deeplink_url",
                    "oaid",
                    "install_app_store",
                    "contributor1_match_type",
                    "contributor2_match_type",
                    "contributor3_match_type",
                    "match_type",
                    "device_category",
                    "gp_referrer",
                    "gp_click_time",
                    "gp_install_begin",
                    "amazon_aid",
                    "keyword_match_type",
                    "att",
                    "conversion_type",
                    "campaign_type",
                    "is_lat",
                ],
            },
            "organic_installs_report": {
                "path": "organic_installs_report/{version}".format(version=APPSFLYER_API_VERSION),
                "additional_fields": [
                    "device_model",
                    "keyword_id",
                    "store_reinstall",
                    "deeplink_url",
                    "oaid",
                    "install_app_store",
                    "gp_referrer",
                    "gp_click_time",
                    "gp_install_begin",
                    "amazon_aid",
                    "keyword_match_type",
                    "att",
                    "conversion_type",
                    "campaign_type",
                    "is_lat",
                ],
            },
            "organic_in_app_events_report": {
                "path": "organic_in_app_events_report/{version}".format(version=APPSFLYER_API_VERSION),
                "additional_fields": [
                    "device_model",
                    "keyword_id",
                    "store_reinstall",
                    "deeplink_url",
                    "oaid",
                    "amazon_aid",
                    "keyword_match_type",
                    "att",
                    "conversion_type",
                    "campaign_type",
                ],
            },
        },
        "src_partition_column": "nv_updated_date",
        "event_timestamp": "event_time",
        "schema": {
            "Attributed Touch Type": "str",
            "Attributed Touch Time": "datetime64",
            "Install Time": "datetime64",
            "Event Time": "datetime64",
            "Event Name": "str",
            "Event Value": "str",
            "Event Revenue": "str",
            "Event Revenue Currency": "str",
            "Event Revenue USD": "str",
            "Event Source": "str",
            "Is Receipt Validated": "str",
            "Partner": "str",
            "Media Source": "str",
            "Channel": "str",
            "Keywords": "str",
            "Campaign": "str",
            "Campaign ID": "str",
            "Adset": "str",
            "Adset ID": "str",
            "Ad": "str",
            "Ad ID": "str",
            "Ad Type": "str",
            "Site ID": "str",
            "Sub Site ID": "str",
            "Sub Param 1": "str",
            "Sub Param 2": "str",
            "Sub Param 3": "str",
            "Sub Param 4": "str",
            "Sub Param 5": "str",
            "Cost Model": "str",
            "Cost Value": "str",
            "Cost Currency": "str",
            "Contributor 1 Partner": "str",
            "Contributor 1 Media Source": "str",
            "Contributor 1 Campaign": "str",
            "Contributor 1 Touch Type": "str",
            "Contributor 1 Touch Time": "str",
            "Contributor 2 Partner": "str",
            "Contributor 2 Media Source": "str",
            "Contributor 2 Campaign": "str",
            "Contributor 2 Touch Type": "str",
            "Contributor 2 Touch Time": "str",
            "Contributor 3 Partner": "str",
            "Contributor 3 Media Source": "str",
            "Contributor 3 Campaign": "str",
            "Contributor 3 Touch Type": "str",
            "Contributor 3 Touch Time": "str",
            "Region": "str",
            "Country Code": "str",
            "State": "str",
            "City": "str",
            "Postal Code": "str",
            "DMA": "str",
            "IP": "str",
            "WIFI": "bool",
            "Operator": "str",
            "Carrier": "str",
            "Language": "str",
            "AppsFlyer ID": "str",
            "Advertising ID": "str",
            "IDFA": "str",
            "Android ID": "str",
            "Customer User ID": "str",
            "IMEI": "str",
            "IDFV": "str",
            "Platform": "str",
            "Device Type": "str",
            "OS Version": "str",
            "App Version": "str",
            "SDK Version": "str",
            "App ID": "str",
            "App Name": "str",
            "Bundle ID": "str",
            "Is Retargeting": "bool",
            "Retargeting Conversion Type": "str",
            "Attribution Lookback": "str",
            "Reengagement Window": "str",
            "Is Primary Attribution": "str",
            "User Agent": "str",
            "HTTP Referrer": "str",
            "Original URL": "str",
            "Device Model": "str",
            "Keyword ID": "str",
            "Store Reinstall": "bool",
            "Deeplink URL": "str",
            "OAID": "str",
            "Install App Store": "str",
            "Google Play Referrer": "str",
            "Google Play Click Time": "datetime64",
            "Google Play Install Begin Time": "datetime64",
            "Amazon Fire ID": "str",
            "Keyword Match Type": "str",
            "ATT": "str",
            "Conversion Type": "str",
            "Campaign Type": "str",
            "Is LAT": "bool",
            "source_app": "str",
        },
        "primary_keys": ['apps_flyer_id', 'event_name', 'event_time', 'event_value', 'postal_code']
    },
    "aggregated_report": {
        "src_partition_column": "created_date",
        "event_timestamp": "date",
        "app_ids": {
            "skan_aggregated_performance_report": {
                "path": "skadnetworks/v2/data/app/{ios_version}".format(ios_version=SOURCE_APP["iOS"]),
                "schema": {
                    "date": {"original_column": "Date", "dbtype": "str"},
                    "media_source": {"original_column": "Media Source (pid)", "dbtype": "str"},
                    "campaign_name": {"original_column": "Campaign (c)", "dbtype": "str"},
                    "campaign_id": {"original_column": "Campaign ID", "dbtype": "str"},
                    "site_id": {"original_column": "Site ID", "dbtype": "str"},
                    "adset": {"original_column": "Adset", "dbtype": "str"},
                    "adset_id": {"original_column": "Adset ID", "dbtype": "str"},
                    "ad": {"original_column": "Ad", "dbtype": "str"},
                    "ad_id": {"original_column": "Ad ID", "dbtype": "str"},
                    "country": {"original_column": "Country", "dbtype": "str"},
                    "af_attribution_flag": {"original_column": "AF Attribution Flag", "dbtype": "bool"},
                    "impressions": {"original_column": "Impressions", "dbtype": "int"},
                    "clicks": {"original_column": "Clicks", "dbtype": "int"},
                    "ctr": {"original_column": "CTR", "dbtype": "float"},
                    "installs": {"original_column": "Installs", "dbtype": "int"},
                    "click_through_installs": {"original_column": "Click Through Installs", "dbtype": "float"},
                    "view_through_installs": {"original_column": "View Through Installs", "dbtype": "float"},
                    "null_conversion_value_rate": {"original_column": "Null Conversion Value Rate", "dbtype": "float"},
                    "conversion_rate": {"original_column": "Conversion Rate", "dbtype": "float"},
                    "converted_users": {"original_column": "Converted Users", "dbtype": "int"},
                    "converted_users_or_installs": {"original_column": "Converted Users/Installs", "dbtype": "float"},
                    "total_revenue": {"original_column": "Total Revenue", "dbtype": "float"},
                    "total_cost": {"original_column": "Total Cost", "dbtype": "float"},
                    "roi": {"original_column": "ROI", "dbtype": "float"},
                    "arpu": {"original_column": "ARPU", "dbtype": "float"},
                    "average_eCPI": {"original_column": "Average eCPI", "dbtype": "float"},
                    "account_created_unique": {"original_column": "account_created (Unique users)", "dbtype": "int"},
                    "account_created_total": {"original_column": "account_created (Event counter)", "dbtype": "int"},
                    "af_app_opened_unique": {"original_column": "af_app_opened (Unique users)", "dbtype": "float"},
                    "af_app_opened_total": {"original_column": "af_app_opened (Event counter)", "dbtype": "float"},
                    "order_create_start_unique": {"original_column": "order_create (Unique users)", "dbtype": "int"},
                    "order_create_start_total": {"original_column": "order_create (Event counter)", "dbtype": "int"},
                    "order_created_unique": {"original_column": "order_created (Unique users)", "dbtype": "int"},
                    "order_created_total": {"original_column": "order_created (Event counter)", "dbtype": "int"},
                    "sign_up_start_unique": {"original_column": "sign_up_start (Unique users)", "dbtype": "int"},
                    "sign_up_start_total": {"original_column": "sign_up_start (Event counter)", "dbtype": "int"},
                },
                "not_null_columns": ["campaign_id"],
                "replace_with_0_columns": [
                    "order_create_start_unique",
                    "order_create_start_total",
                    "order_created_unique",
                    "order_created_total",
                    "sign_up_start_unique",
                    "sign_up_start_total",
                    "account_created_unique",
                    "account_created_total",
                ],
            },
        },
    },
}
