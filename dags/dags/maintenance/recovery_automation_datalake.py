from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

from airflow.models import Variable
from airflow_client.client.model.clear_dag_run import ClearDagRun
from airflow_client.client.model.clear_task_instances import ClearTaskInstances
from airflow_client.client import Configuration, ApiClient, ApiException
from airflow_client.client.api import dag_api, dag_run_api

from datetime import datetime, timedelta, timezone
import codecs
import logging

from metadata.cdc_data_integrity import DB_MW_REPLICATION
from metadata.constants import Timeout

log = logging.getLogger(__name__)

env = Variable.get("env")
airflow_pass = codecs.decode(Variable.get("monitoring_pass_secret"), 'rot-13')
required_keys_in_airflow_var = {
    "task": {"dag_id", "task_id", "start_date_time", "end_date_time"},
    "maxwell": {"maxwell_id", "start_date_time", "end_date_time"},
    "dag": {"dag_id", "start_date_time", "end_date_time"},
}
host = "https://airflow.ninjavan.co/api/v1" if env == 'prod' else "https://airflow-dev.ninjavan.co/api/v1"
api_configuration = Configuration(
    host=host,
    username='monitoring_user',
    password=airflow_pass
)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2024, 10, 14),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}


def clear_dag_run(dag_id, execution_date):
    with ApiClient(api_configuration) as api_client:
        # Create an instance of the API class
        api_instance = dag_run_api.DAGRunApi(api_client)
        dag_id = dag_id  # str | The DAG ID.
        try:
            # clear a new DAG run.
            api_response = api_instance.clear_dag_run(dag_id, f"scheduled__{execution_date}+00:00",
                                                      ClearDagRun(dry_run=False))
            log.info(api_response)
        except ApiException as e:
            log.info("Exception when calling DAGRunApi->clear_dag_run: %s\n" % e)
            raise
    return execution_date


def generate_hours_between(start_date, end_date):
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

    hours = []
    current_datetime = start_datetime
    while current_datetime <= end_datetime:
        hours.append(current_datetime.strftime('%Y-%m-%d %H:%M:%S'))
        current_datetime += timedelta(hours=1)

    return hours


def has_required_keys(data, required_keys):
    """Checks if the dictionary has all required keys.

    Args:
      data: The dictionary to check.
      required_keys: A collection of required keys (e.g., a set or list).

    Returns:
      True if all required keys are present, False otherwise.
    """
    return required_keys.issubset(data)


def clear_airflow_task(**kwargs):
    execution_date = kwargs['execution_date']
    dag_id = kwargs['dag_id']
    task_id = kwargs['task_id']

    with ApiClient(api_configuration) as api_client:
        dag_api_instance = dag_api.DAGApi(api_client)
        log.info(f"clearing for task {task_id} in {dag_id}")
        start_time = datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S')
        end_time = start_time + timedelta(hours=0.5)
        clear_task_instances = ClearTaskInstances(
            dry_run=False,
            task_ids=[task_id, ],
            start_date=start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
            end_date=end_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
            only_failed=False,
            reset_dag_runs=True,
        )
        api_response = dag_api_instance.post_clear_task_instances(dag_id, clear_task_instances)
        log.info(api_response)
    return execution_date


# Define the DAG
with DAG(
        dag_id='clear_tasks_and_monitor',
        default_args=default_args,
        description='Dynamically clear and monitor tasks/DAGs based on JSON input',
        schedule_interval=None,
) as dag:
    tasks_to_be_cleared = Variable.get('tasks_to_be_cleared', deserialize_json=True)
for task in tasks_to_be_cleared:
    if task['type'] == 'task':
        if not has_required_keys(task, required_keys_in_airflow_var.get('task')):
            raise KeyError(f"one of the required keys {required_keys_in_airflow_var} not present for {task}")
        hours = generate_hours_between(task['start_date_time'], task['end_date_time'])
        for execution_date in hours:
            str_exec_date = str(datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').strftime('%Y%m%d%H%M%S'))
            clear_task = PythonOperator(
                task_id="run_{dag_id}_{task_id}_{execution_date}".format(dag_id=task['dag_id'], task_id=task['task_id'],
                                                                         execution_date=str_exec_date),
                python_callable=clear_airflow_task,
                op_kwargs={"execution_date": execution_date, "task_id": task.get('task_id'),
                           "dag_id": task.get('dag_id')},
                provide_context=True,
                dag=dag,
            )
            monitor_task = ExternalTaskSensor(
                task_id=f"monitor_{task['task_id']}_{str_exec_date}",
                external_dag_id=task['dag_id'],
                external_task_id=task['task_id'],
                allowed_states=['success', 'failed', 'skipped'],
                mode='reschedule',
                execution_timeout=Timeout.TWELVE_HOURS,
                execution_date_fn=lambda _: datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').replace(minute=0,
                                                                                                           second=0,
                                                                                                           microsecond=0).astimezone(
                    tz=timezone.utc),
                check_existence=True,
                poke_interval=180,
                dag=dag,
            )
            clear_task >> monitor_task
    elif task['type'] == 'dag':
        if not has_required_keys(task, required_keys_in_airflow_var.get('dag')):
            raise KeyError(f"one of the required keys {required_keys_in_airflow_var.get('dag')} not present for {task}")
        hours = generate_hours_between(task['start_date_time'], task['end_date_time'])
        for execution_date in hours:
            str_exec_date = str(datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').strftime('%Y%m%d%H%M%S'))
            clear_dag = PythonOperator(
                task_id="clear_dag_{dag_id}_{execution_date}".format(dag_id=task['dag_id'],
                                                                     execution_date=str_exec_date),
                python_callable=clear_dag_run,
                op_kwargs={"execution_date": execution_date, "dag_id": task.get('dag_id')},
                provide_context=True,
                dag=dag,
            )
            monitor_dag = ExternalTaskSensor(
                task_id=f"monitor_{task['dag_id']}_{str_exec_date}",
                external_dag_id=task['dag_id'],
                external_task_id=None,
                allowed_states=['success',],
                failed_states=['failed',],
                mode='reschedule',
                execution_timeout=Timeout.TWELVE_HOURS,
                execution_date_fn=lambda _: datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').replace(
                    minute=0,
                    second=0,
                    microsecond=0).astimezone(
                    tz=timezone.utc),
                check_existence=True,
                poke_interval=180,
                dag=dag,
            )
            clear_dag >> monitor_dag
    elif task['type'] == 'maxwell':
        if not has_required_keys(task, required_keys_in_airflow_var.get('maxwell')):
            raise KeyError(
                f"one of the required keys {required_keys_in_airflow_var.get('maxwell')} not present for {task}")
        schemas = DB_MW_REPLICATION.get(task['maxwell_id'])
        hours = generate_hours_between(task['start_date_time'], task['end_date_time'])
        if schemas is not None:
            for schema in schemas:
                dag_id = f"datalake_cdc_{schema}"
                for execution_date in hours:
                    str_exec_date = str(datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').strftime('%Y%m%d%H%M%S'))
                    clear_dag = PythonOperator(
                        task_id=f"clear_dag_{dag_id}_{str_exec_date}",
                        python_callable=clear_dag_run,
                        op_kwargs={"execution_date": execution_date, "dag_id": dag_id},
                        provide_context=True,
                        dag=dag,
                    )
                    # external_dag_id = f"datalake_cdc_{schema}",
                    # external_task_id = None,
                    monitor_dag = ExternalTaskSensor(
                        task_id=f"monitor_{dag_id}_{str_exec_date}",
                        external_dag_id=dag_id,
                        external_task_id=None,
                        allowed_states=['success',],
                        failed_states=['failed',],
                        mode='reschedule',
                        execution_timeout=Timeout.TWELVE_HOURS,
                        execution_date_fn=lambda _: datetime.strptime(execution_date, '%Y-%m-%d %H:%M:%S').replace(
                            minute=0,
                            second=0,
                            microsecond=0).astimezone(
                            tz=timezone.utc),
                        check_existence=True,
                        poke_interval=180,
                        dag=dag,
                    )
                    clear_dag >> monitor_dag
    else:
        log.warning("undefined type {type}".format(type=task['type']))
