from datetime import datetime, <PERSON><PERSON><PERSON>
from importlib import import_module
from pathlib import Path
import os

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import notifications as notif
from data_warehouse.tasks.main.base import HiveMetastoreTaskConfig
from data_warehouse.utils import airflow
from metadata import constants, data_warehouse, spark_conf

DAG_ID = data_warehouse.CalendarMaskedDAG.DAG_ID
TASKS = (
    data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED,
    data_warehouse.CalendarMaskedDAG.Task.CALENDAR_RECOVERY_MASKED,
    data_warehouse.CalendarMaskedDAG.Task.CALENDAR_SHIPPER_MASKED,
    data_warehouse.CalendarMaskedDAG.Task.CALENDAR_SORT_MASKED,
    data_warehouse.CalendarMaskedDAG.Task.CALENDAR_SPEED_MASKED,
)
SYSTEM_IDS = (
    constants.SystemID.ID,
    constants.SystemID.MM,
    constants.SystemID.MY,
    constants.SystemID.PH,
    constants.SystemID.SG,
    constants.SystemID.TH,
    constants.SystemID.VN,
    constants.SystemID.IN,
)
HOLIDAYS_GDRIVE = ["holidays", "holidays_recovery", "holidays_shipper", "holidays_sort"]

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks/calendar_tasks")
hms_task_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
cz_tasks_path = Variable.get("SPARK_APPLICATION_PATH", default_var="obs://nv-data-prod-data-warehouse/clickzetta/spark")
USE_LH_SPARK = Variable.get("USE_LH_SPARK", default_var=os.environ.get("USE_LH_SPARK", "False")).lower() == "true"
hms_config = HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",))

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2020, 6, 22),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": constants.Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

gdrive_sensors = {}

with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    schedule_interval="0 18 * * *",
    catchup=False,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["data_warehouse"],
) as dag:
    for task in TASKS:
        end_task = DummyOperator(task_id=f"{task}_end")

        python_file_name = "calendar_main_masked" if task == "calendar_masked" else task
        python_module = import_module(f"data_warehouse.tasks.calendar_tasks.{python_file_name}")
        for system_id in SYSTEM_IDS:
            application_path = f"{cz_tasks_path}/airflow_tasks/calendar_tasks/{python_file_name}.py" if USE_LH_SPARK else f"{tasks_path}/{python_file_name}.py"
            python_task = SparkSubmitOperator(
                task_id=f"{task}_{system_id}",
                application=application_path,
                application_args=[env, system_id],
                conn_id="spark_default",
                conf={"spark.executor.instances": "2", "spark.executor.memory": "5g", "spark.driver.memory": "5g"},
                trigger_rule="one_success",
                execution_timeout=constants.Timeout.ONE_HOUR,
            )
            for holiday in HOLIDAYS_GDRIVE:
                task_id = f"wait_for_gdrive_{holiday}"
                if task_id not in gdrive_sensors:
                    gdrive_sensors[task_id] = ExternalTaskSensor(
                        task_id=task_id,
                        external_dag_id="datalake_gdrive",
                        external_task_id=f"merge_delta_{holiday}",
                        execution_delta=timedelta(hours=2),
                        allowed_states=["success", "skipped"],
                        soft_fail=True,
                        check_existence=True,
                        mode="reschedule",
                        poke_interval=120,
                    )
                gdrive_sensors[task_id] >> python_task >> end_task
        hms_tasks_path = cz_tasks_path if USE_LH_SPARK else hms_task_path
        update_sing_hms_task = airflow.get_update_sing_hms_task(env, hms_tasks_path, task, hms_config)
        end_task >> update_sing_hms_task
