from datetime import timedel<PERSON>
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.operators.python import PythonOperator

from common.airflow import notifications as notif
from common.date import get_schema_day
from common.stringcase import kebab_case
from data_warehouse.tasks.main.config import dag_configs
from metadata.constants import Timeout

ALERT_CHANNEL = "chat_dwh_alert"

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
default_args = {
    "owner": "airflow",
    "start_date": "2023-03-01",
    "catchup": False,
    "retries": 0,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert,
    "concurrency": 5,
}

def end_task_function():
    pass

def create_dag(dag_id, schedule_interval, default_args):
    dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval=schedule_interval,
        tags=["data_warehouse", "dwh_data_integrity"],
        catchup=False
    )
    globals()[dag_id] = dag
    return dag

for dag_name, dag_config in dag_configs.items():

    dag_id = f"data_integrity_{dag_name}"
    schema_day = get_schema_day(dag_name)
    schema_interval = f"0 13 * * *"
    dwh_di_dag = create_dag(dag_id=dag_id, schedule_interval=schema_interval, default_args=default_args)
    with dwh_di_dag as dag:
        end_task = PythonOperator(
                    task_id='end_task',
                    python_callable=end_task_function,
                    dag=dwh_di_dag,
                )

        for config in dag_config["task_configs"]:
                task_name = config.task_name
                task_id = f"ge_validation_{task_name}"
                validation_task = SparkSubmitOperator(
                    task_id=task_id,
                    name=kebab_case(task_id),
                    application=f"{tasks_path}/run_ge_checkpoint.py",
                    application_args=[
                        env,
                        task_name,
                        "{{ task_instance_key_str }}",
                        "{{ data_interval_end }}",
                    ],
                    conn_id="spark_default",
                    conf={
                        **spark_conf[tables_to_size.get(task_id, "small")],
                        "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                        "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                    },
                    execution_timeout=Timeout.ONE_HOUR,
                    params={"alert_channel": ALERT_CHANNEL},
                )
                validation_task >> end_task