from dataclasses import dataclass

import pendulum
from pyspark.sql import Data<PERSON>rame
from pyspark.sql.types import StringType, StructField, StructType

from common import date
from data_warehouse.utils import logger

logger = logger.get_logger(__file__)


@dataclass
class EtlTaskLogger:
    """
    A data class to store ETL task data for tracking purpose
    """

    task_name: str = None
    system_ids: str = None
    task_start_datetime: str = None
    load_finish_datetime: str = None
    task_finish_datetime: str = None

    SCHEMA = StructType(
        [
            StructField("task_name", StringType(), True),
            StructField("system_ids", StringType(), True),
            StructField("task_start_datetime", StringType(), True),
            StructField("load_finish_datetime", StringType(), True),
            <PERSON>ructField("task_finish_datetime", StringType(), True),
            StructField("created_at", StringType(), True),
        ]
    )

    DEST_URI = "obs://nv-data-{env}-data-warehouse/etl_tasks_log_temp/{task_id}"
    SYSTEM_ID = "gl"

    def get_system_ids(self, input_config) -> str:
        """
        Extract system_id from input config by identifying whether system_id is specified for each type of input table
        """

        input_parquet_tables = input_config.parquet_tables
        input_versioned_parquet_tables = input_config.versioned_parquet_tables
        input_delta_tables = input_config.delta_tables

        if input_parquet_tables:
            system_id = input_parquet_tables[0].system_id
            if system_id:
                return system_id
        if input_versioned_parquet_tables:
            system_id = input_versioned_parquet_tables[0].system_id
            if system_id:
                return system_id
        if input_delta_tables:
            system_id = input_delta_tables[0].system_id
            path = input_delta_tables[0].path
            if system_id:
                return system_id
            if "core_prod" in path:
                return path.split("core_prod_")[1][:2]

        return self.SYSTEM_ID

    def get_dest_path(self, output_config) -> str:
        """
        Take task output_config to extract runtime environment and construct log destination path
        """

        output_base_path = output_config.base_path
        env = output_base_path.split("-")[2]
        task_id = f"{self.task_name}_{self.system_ids}"
        res = self.DEST_URI.format(env=env, task_id=task_id)

        logger.info(f"ETL task log destination path is {res}")

        return res

    def _get_log_df(self, spark) -> DataFrame:
        """
        Transform instance attributes into a single row spark df
        """

        logger.info("Transforming ETL Task Log attributes into spark dataframe")

        created_at = pendulum.now(tz=date.Timezone.SG).strftime("%Y-%m-%dT%H:%M:%S")
        data = [
            (
                self.task_name,
                self.system_ids,
                self.task_start_datetime,
                self.load_finish_datetime,
                self.task_finish_datetime,
                created_at,
            )
        ]

        logger.info(f"Data in task logging dataframe is {data}")

        return spark.createDataFrame(data=data, schema=self.SCHEMA)

    def insert_log(self, spark, dest_path) -> None:
        """
        Process attributes to transform into spark dataframe and insert into destination delta table
        """

        df = self._get_log_df(spark)

        (df.write.format("parquet").mode("append").save(dest_path))

        logger.info(f"ETL task log data inserted into {dest_path}")
