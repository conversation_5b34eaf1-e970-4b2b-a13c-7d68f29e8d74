import pandas as pd
import json
from pyspark.sql.types import StructType
from pyspark.sql.functions import lit

from common.spark.util import cast_df
from data_warehouse.utils import obs, logger

logger = logger.get_logger(__file__)


def _get_filter(partitions):
    """
    Gets Spark filter for relevant created_month and system_id partitions within a measurement_datetime partition.

    :param partitions:       List of relevant partitions, e.g.
                             [
                                {'created_month': '2020-08', 'system_id': 'id'},
                                {'created_month': '2020-09', 'system_id': 'id'}
                             ]
    """
    partition_filters = []
    for partition in partitions:
        sub_filters = []
        for partition_column, value in partition.items():
            sub_filter_str = f"{partition_column} = '{value}'"
            if value == "__HIVE_DEFAULT_PARTITION__":
                sub_filter_str = f"{partition_column} is null"
            sub_filters.append(sub_filter_str)
        partition_filters.append(f"({' and '.join(sub_filters)})")
    return " or ".join(partition_filters)


def get_relevant_partitions(partition_combination_df, version_datetime):
    """
    Finds the partition with the latest measurement_datetime before version_datetime for each (system_id, created_month)
    combination. Returns a dict that groups partitions with the same measurement_datetime together, e.g.
    {
        "2020-08-26 18-00-00": [
            {"created_month": "2020-06", "system_id": "id"},
            {"created_month": "2020-07", "system_id": "id"},
        ],
        "2020-08-31 18-00-00": [{"created_month": "2020-08", "system_id": "id"}],
    }
    """
    relevant_partitions = partition_combination_df.query(f"measurement_datetime <= '{version_datetime}'")
    if relevant_partitions.empty:
        raise ValueError(f"No table versions before {version_datetime}.")
    relevant_partitions = relevant_partitions.groupby(["created_month", "system_id"], as_index=False).max()
    return (
        relevant_partitions.groupby("measurement_datetime")
        .apply(lambda x: x.set_index("measurement_datetime").to_dict("records"))
        .to_dict()
    )


def get_latest_version(partition_combination_df):
    """Gets the latest measurement_datetime in the partition_combination_df. Excludes measurement_datetime=latest."""
    logger.info("Getting the latest version")
    logger.info(f"Partition combination df: {partition_combination_df}")
    if partition_combination_df.empty:
        logger.info("No partitions found")
        return None

    # Check if measurement_datetime column exists
    if 'measurement_datetime' not in partition_combination_df.columns:
        logger.info("measurement_datetime column not found in partition_combination_df")
        return None

    # Filter out 'latest' partition
    filtered_df = partition_combination_df[partition_combination_df['measurement_datetime'] != 'latest']
    if filtered_df.empty:
        return None

    return filtered_df["measurement_datetime"].max()


def read(spark, path, version_datetime=None, system_id=None, created_month_range=None):
    """
    Reads a versioned parquet table.
    - version_datetime can be specified to time travel to a older version of the table
    - If partitions have different schema, the schema follows that of the latest version
    - Assumes that the table has measurement_datetime, system_id and created_month partitions
    - Returns None if the table does not exist (first run case)
    """
    logger.info(f"Loading versioned parquet table: {path}...")
    try:
        partition_combination_df = obs.get_partition_combination_df(
            path, ["measurement_datetime", "system_id", "created_month"]
        )
        logger.info(f"Partition combination df: {partition_combination_df}")

        bucket, _ = obs.get_uri_bucket_and_directory(path)
        mock_path = f"obs://{bucket}/clickzetta/spark/empty_df_path"
        identifier = path.split("/")[-1]
        default_schema_json_path = f"obs://{bucket}/A_default_latest_schemas/{identifier}.json"

        if partition_combination_df.empty:
            logger.info(f"No partitions found in {path}, likely first run or empty table")
            logger.info(f"No partitions found in {path}, try using exported default schema")

            _, json_path = obs.get_uri_bucket_and_directory(default_schema_json_path)
            if obs.is_path_exist(bucket, json_path):
                response = obs.client.getObject(bucket, json_path, loadStreamInMemory=True)
                if response.status == 200:
                    loaded_schema = StructType.fromJson(json.loads(response.body.buffer))
                    logger.info(
                        f"create empty DF for {path} with {default_schema_json_path} with mock_path {mock_path}")
                    return spark.read.schema(loaded_schema).parquet(mock_path)
                else:
                    logger.error(f"failed to read content from {default_schema_json_path}")
                    return None
            else:
                logger.error(f"json_path {default_schema_json_path} does not exist")
                return None

        latest_version = get_latest_version(partition_combination_df)
        if not latest_version:
            logger.info(f"No valid versions found in {path}, likely first run or empty table")
            return None

        if not version_datetime:
            version_datetime = latest_version
        logger.info(f"Version: {version_datetime}")

        logger.info(f"partition_combination_df for {path} before filter size {partition_combination_df.size}")
        if system_id:
            logger.info(f"Relevant partitions for {path} apply filter system_id {system_id}")
            partition_combination_df = partition_combination_df.query(f"system_id == '{system_id}'")

        if created_month_range:
            logger.info(f"Relevant partitions for {path} apply filter created_month "
                        f"{created_month_range.min} - {created_month_range.max}")
            partition_combination_df = partition_combination_df \
                .query(f"created_month >= '{created_month_range.min}' and created_month <= '{created_month_range.max}'")
        logger.info(f"partition_combination_df for {path} after filter size {partition_combination_df.size}")

        try:
            relevant_partitions = get_relevant_partitions(partition_combination_df, version_datetime)
            logger.info(f"Relevant partitions: {relevant_partitions}")
        except ValueError:
            relevant_partitions = {}
            logger.info("Empty Relevant partitions")

        # Try to read latest schema, return None if path doesn't exist
        try:
            latest_df = spark.read.parquet(f"{path}/measurement_datetime={latest_version}")
            latest_schema = latest_df.dtypes
            default_struct_type = latest_df.schema
            logger.info(f"Latest schema: {latest_schema}")
        except Exception as e:
            logger.info(f"Could not read latest schema, likely first run: {str(e)}")
            return None

        full_df = None
        for version, partitions in relevant_partitions.items():
            df = spark.read.parquet(f"{path}/measurement_datetime={version}")
            filter_condition = _get_filter(partitions)
            df = df.filter(filter_condition)

            schema_mismatch = df.dtypes != latest_schema
            if schema_mismatch:
                df = cast_df(df, latest_schema, True)

            df = df.withColumn("measurement_datetime", lit(version).cast("string"))

            full_df = full_df.unionByName(df) if full_df else df

        if full_df:
            return full_df
        else:
            logger.info(f"create empty DF for {path} with schema {latest_schema} with mock_path {mock_path}")
            return spark.read.schema(default_struct_type).parquet(mock_path)
    except Exception as e:
        if "path does not exist" in str(e).lower():
            logger.info(f"Path {path} does not exist, likely first run")
            return None
        raise e
