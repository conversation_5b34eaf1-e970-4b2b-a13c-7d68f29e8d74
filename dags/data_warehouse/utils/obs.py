import pandas as pd
from data_warehouse.utils import logger
from common.utils.nv_obs import strip_uri,get_obs_client

logger = logger.get_logger(__file__)
client = get_obs_client()

class InvalidDirectoryException(Exception):
    """Raised when OBS directory is not found."""
    pass


def _append_slash(string):
    if string and not string.endswith("/"):
        string = string + "/"
    return string


def get_directory_blobs(bucket_name, directory, delimiter=None):
    index = 1
    marker=None
    max_keys=1000
    blobs = []
    directory = _append_slash(directory)
    try:
        while True:
            resp = client.listObjects(
                bucket_name,
                prefix=directory,
                marker=marker, 
                max_keys=max_keys, 
                delimiter=delimiter
            )
            if resp.status < 300:
                for content in resp.body.contents:
                    blobs.append(content)
                    index += 1

                # Check if the result is truncated, if so, continue with the next marker
                if resp.body.is_truncated:
                    marker = resp.body.next_marker
                else:
                    break
            else:
                raise Exception(f"Error: {resp.errorCode} - {resp.errorMessage}")
        logger.info(f"Found {len(blobs)} blobs in directory - bucket: {bucket_name}, directory: {directory}")
        return blobs
    except Exception as e:
        raise Exception(f"Error: {str(e)}")


def get_blob_size(bucket_name, blob_name):    
    blob = {}
    try:
        resp = client.listObjects(
            bucket_name,
            prefix=blob_name
        )
        
        if resp.status < 300:
            if resp.body.contents:
                blob = resp.body.contents[0]  # Get the first object
            else:
                raise Exception("No objects found in the bucket.")
        else:
            raise Exception(f"Error: {resp.errorCode} - {resp.errorMessage}")

    except Exception as e:
        raise Exception(f"Error: {str(e)}")

    """Gets blob size in MB."""
    size_byte = blob.size
    size_megabyte = size_byte / 1024 / 1024
    return size_megabyte


def get_directory_size(bucket_name, directory):
    """Gets directory size in MB."""
    directory_size = 0
    try:
       directory_blobs =  get_directory_blobs(bucket_name, directory, delimiter=None)
       for blob in directory_blobs:
            size_megabyte = blob.size / 1024 / 1024
            directory_size+=size_megabyte    
    except Exception as e:
         raise Exception(f"Error: {str(e)}")
    return directory_size


def calc_num_parquet_files(directory_size, mb_per_file=100):
    if mb_per_file <= 0:
        raise ValueError("mb_per_file must be greater than zero.")

    logger.info("Calculating repartition number...")
    num_files = round(directory_size / mb_per_file)
    num_files = max(num_files, 1)
    logger.info(f"{num_files} parquet file(s) required at {mb_per_file}MB per file.")
    return num_files


def delete_blob(bucket_name, object_key, version_id=None):
    try:
        resp = client.deleteObject(bucket_name, object_key, version_id)
        if resp.status < 300:
           logger.info(f"Delete Object Succeeded | Request ID: {resp.requestId} | Delete Marker: {resp.body.deleteMarker} | Version ID: {resp.body.versionId}")
        else:
          raise Exception(f"Delete Object Failed | Request ID: {resp.requestId} | Error Code: {resp.errorCode} | Error Message: {resp.errorMessage}")
     
    except Exception as e:
        raise Exception(f"Delete Object Failed | Error: {str(e)}")
  
   
def delete_directory(bucket_name, directory):
    try:
        directory_blobs =  get_directory_blobs(bucket_name, directory, delimiter=None)
        dir_objects = get_dir_objects(directory_blobs, chunk_size=1000)
        
        for objects in dir_objects:
          resp = client.deleteObjects(
                bucket_name, 
                deleteObjectsRequest= {"quiet":True,"objects":objects}
            )
          if resp.status < 300:
            logger.info(f"Delete Objects Succeeded | Request ID:', {resp.requestId} | obs_url_path: obs://{bucket_name}/{directory}")        
          else:
            raise Exception(f"Delete Objects Failed | Request ID: {resp.requestId} | Error Code: {resp.errorCode} | "
                    f"Error Message: {resp.errorMessage}")
    except Exception as e:
        raise Exception(f"Delete Objects Failed | Error: {str(e)}")

def copy_blob(bucket_name, source_blob_name, destination_blob_name):
    try:
        resp = client.copyObject(
            bucket_name, 
            source_blob_name, 
            bucket_name, 
            destination_blob_name
        )
        
        if resp.status < 300:
             logger.info(f"Copy Object Succeeded | Request ID: {resp.requestId} | "
                  f"ETag: {resp.body.etag} | Last Modified: {resp.body.lastModified} | "
                  f"Version ID: {resp.body.versionId} | Copy Source Version ID: {resp.body.copySourceVersionId}")
        else:
             raise Exception(f"Copy Object Failed | Request ID: {resp.requestId} | "
                  f"Error Code: {resp.errorCode} | Error Message: {resp.errorMessage}")
    except Exception as e:
        raise Exception(f"Copy Object Failed | Error: {str(e)}")
        
def copy_directory(bucket_name, source_directory, destination_directory):
    source_directory = _append_slash(source_directory)
    destination_directory = _append_slash(destination_directory)
    try:
        directory_blobs =  get_directory_blobs(bucket_name, source_directory, delimiter=None)
        for blob in directory_blobs:
            source_blob_name = blob.key
            destination_blob_name = source_blob_name.replace(source_directory, destination_directory)
            copy_blob(bucket_name, source_blob_name, destination_blob_name)
            logger.info(f"obs://{bucket_name}/{source_directory} copied to obs://{bucket_name}/{destination_directory}.")
    except Exception as e:
            raise Exception(f"Error: {str(e)}")


def move_blob(bucket_name, destination_bucket_name, source_blob_name, destination_blob_name):
    """Copies blob with a new name and deletes the old blob."""
    try:
        resp = client.copyObject(
            bucket_name, 
            source_blob_name, 
            destination_bucket_name, 
            destination_blob_name
        )
        
        if resp.status < 300:
             logger.info(f"Copy Object Succeeded | Request ID: {resp.requestId} | "
                  f"ETag: {resp.body.etag} | Last Modified: {resp.body.lastModified} | "
                  f"Version ID: {resp.body.versionId} | Copy Source Version ID: {resp.body.copySourceVersionId}")

             logger.info(f"Deleting Source Directory | obs://{bucket_name}/{source_blob_name}.")
             delete_blob(bucket_name,source_blob_name)         
        else:
             raise Exception(f"Copy Object Failed | Request ID: {resp.requestId} | "
                  f"Error Code: {resp.errorCode} | Error Message: {resp.errorMessage}")
    except Exception as e:
        raise Exception(f"Copy Object Failed | Error: {str(e)}")
    

def move_directory(bucket_name, destination_bucket_name, source_directory, destination_directory):
    source_directory = _append_slash(source_directory)
    destination_directory = _append_slash(destination_directory)
    try:
        
        directory_blobs =  get_directory_blobs(bucket_name, source_directory, delimiter=None)
        for blob in directory_blobs:
            source_blob_name = blob.key
            destination_blob_name = source_blob_name.replace(source_directory, destination_directory)
            move_blob(bucket_name, destination_bucket_name, source_blob_name, destination_blob_name)
            logger.info(f"obs://{bucket_name}/{source_directory} moved to obs://{destination_bucket_name}/{destination_directory}.")            
    except Exception as e:
            raise Exception(f"Error: {str(e)}")

def get_uri_bucket_and_directory(obs_uri):
    """
    Gets the bucket and directory from a OBS URI. Assumes that the first part after obs:// is the bucket, and the rest
    is the directory.
    """
    bucket, directory = strip_uri(obs_uri).split("/", 1)
    return bucket, directory


def _get_partition_to_value(path, partition_cols):
    """Returns a dict of partition column name to value for partitions that are listed in partition_cols."""
    parts = strip_uri(path).split("/")
    part_to_val = {}
    for part in parts:
        col_name, val = None, None
        if "=" in part:
            col_name, val = part.split("=")[:2]
        if col_name in partition_cols:
            part_to_val[col_name] = val
    return part_to_val


def _get_subdirectories(obs_uri):
    """Gets names of all blobs that does not contain 'part-'."""
    logger.info(f"fetching the sub directories for uri - {obs_uri}")
    bucket, directory = get_uri_bucket_and_directory(obs_uri)
    logger.info(f"bucket - {bucket}, directory - {directory}")
    blobs = get_directory_blobs(bucket, directory, delimiter="part-")
    logger.info(f"blobs - {blobs}")
    return [blob.key for blob in blobs]


def get_partition_combination_df(table_path, partitions):
    """
    Returns Pandas df containing all existing partition column name to value combinations for given partitions.

    :param table_path:        OBS URI containing parquet table.
    :type table_path:         string
    :param partitions:        Parquet table's partition columns. Can be a subset of all available partition columns.
    :type partitions:         list
    """
    logger.info(f"fetching the partition combinations for path - {table_path}")
    subdirectories = _get_subdirectories(table_path)
    logger.info(f"subdirectories - {subdirectories}")
    complete_directories = []
    logger.info("finding the partitions in the fetched sub directories")
    for subdirectory in subdirectories:
        logger.info(f"subdirectory - {subdirectory}")
        is_complete = all([partition in subdirectory for partition in partitions])
        logger.info(f"is_complete - {is_complete}")
        if is_complete:
            complete_directories.append(subdirectory)

    partition_combinations = [_get_partition_to_value(directory, partitions) for directory in complete_directories]
    logger.info(f"partition_combinations - {partition_combinations}")
    
    # Create DataFrame with the required columns even if empty
    if not partition_combinations:
        return pd.DataFrame(columns=partitions)
    
    return pd.DataFrame(partition_combinations).drop_duplicates()


def is_path_exist(bucket, blob_path):
    logger.debug(f"Checking if path exists - bucket: {bucket}, path: {blob_path}")
    try:
        resp = client.listObjects(bucket,prefix=blob_path)
        if resp.status < 300:
            return len(resp.body.contents) > 0
        else:
            raise Exception(f"Error: {resp.errorCode} - {resp.errorMessage}")
    except Exception as e:
        raise Exception(f"Error: {str(e)}")


def get_dir_objects(directory_blobs, chunk_size=1000):
    for i in range(0, len(directory_blobs), chunk_size):
        yield directory_blobs[i:i + chunk_size]
        
def upload(bucket_name, object_key, file_path):
    """
    Upload a file to Huawei Cloud OBS.

    :param bucket_name: Name of the OBS bucket
    :param object_key: Key (path) of the object in the bucket
    :param file_path: Local path of the file to upload
    :return: Response from the upload operation
    """
    try:
        # Upload the file
        resp = client.putFile(bucket_name, object_key, file_path)

        # Check if upload was successful
        if resp.status >= 300:
            logger.error(f"Failed to upload file to {bucket_name}/{object_key}. Error: {resp.errorMessage}")
            raise Exception(f"Upload failed: {resp.errorMessage}")

        logger.info(f"Successfully uploaded file to {bucket_name}/{object_key}")
        return resp

    except Exception as e:
        logger.error(f"Error uploading file to OBS: {str(e)}")
        raise
    
def streaming_upload(bucket_name, object_key, file) -> None:
    """
    Upload an readable object to HWC OBS
    https://support.huaweicloud.com/intl/en-us/sdk-python-devg-obs/obs_22_0902.html

    :param bucket_name: Name of the OBS bucket
    :param object_key: Key (path) of the object in the bucket
    :param file_path: Local path of the file to upload
    :return: Response from the upload operation
    """
    try:
        resp = client.putContent(bucket_name, object_key, file)

        # Check if upload was a success
        if resp.status >= 300:
            logger.error(f"Failed to upload file to {bucket_name}/{object_key}. Error: {resp.errorMessage}")
            raise Exception(f"Upload failed: {resp.errorMessage}")

        logger.info(f"Successfully uploaded file to {bucket_name}/{object_key}")
        return resp

    except Exception as e:
        logger.error(f"Error uploading file to OBS: {str(e)}")
        raise
