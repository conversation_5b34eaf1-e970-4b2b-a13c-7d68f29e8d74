import json
import os
from datetime import timedelta

from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.stringcase import kebab_case
from metadata.constants import Timeout


def get_dwh_external_task_sensor(dag_id, task_id, current_dag_id=None, execution_delta=None):
    ext_task_id = f"{task_id}_end"
    ext_sensor_task_id = f"wait_for_{dag_id}_{ext_task_id}"
    params = {
        "task_id": ext_sensor_task_id,
        "external_dag_id": dag_id,
        "external_task_id": ext_task_id,
        "allowed_states": ["success", "skipped"],
        "execution_timeout": Timeout.TWELVE_HOURS,
        "check_existence": True,
        "mode": "reschedule",
        "poke_interval": 120,
    }
    if current_dag_id == "data_warehouse_gamification" and dag_id.startswith("data_warehouse"):
        params["execution_date_fn"] = lambda dt: dt.replace(hour=18, minute=0, second=0, microsecond=0) - timedelta(
            days=2
        )
    elif current_dag_id == "data_warehouse_webhook_snapshot" and dag_id.startswith("data_warehouse")\
            and "partnership_webhook" not in task_id:
        params["execution_date_fn"] = lambda dt: dt.replace(hour=18, minute=0, second=0, microsecond=0) - timedelta(
            days=1
        )
    elif current_dag_id == "data_warehouse_webhook_snapshot" and dag_id.startswith("data_warehouse")\
            and "partnership_webhook" in task_id:
        params["execution_date_fn"] = lambda dt: dt.replace(hour=20, minute=0, second=0, microsecond=0)
    elif current_dag_id == "misc_dwh_to_gcs_export" and dag_id.startswith("data_warehouse") \
            and "partnership_webhook" in task_id:
        params["execution_date_fn"] = lambda dt: dt.replace(hour=2, minute=0, second=0, microsecond=0) + timedelta(
            days=1
        )
    elif current_dag_id == "data_warehouse_gamification" and dag_id.startswith("datalake"):
        params["external_task_id"] = task_id
        params["execution_delta"] = execution_delta
    elif current_dag_id == "data_warehouse_gamification_penalty_export" and dag_id.startswith("data_warehouse_gamification"):
        params["execution_delta"] = execution_delta
    else:
        params["execution_delta"] = execution_delta
    return ExternalTaskSensor(**params)

def get_update_sing_hms_task(env, tasks_path, table_name, config, conn_id=None):
    # sing metastore updates...
    # hard coded it for POC purposes.
    # TODO: to remove when it is finalised and move it to config.

    # Use provided conn_id or get from Airflow variables, then environment variable with fallback to spark_default
    from airflow.models import Variable
    spark_conn_id = conn_id or Variable.get("spark_conn_id", default_var=os.environ.get("SPARK_CONN_ID", "spark_default"))

    task_id = f"{table_name}_update_sing_hms_{config.hive_schema}"
    return SparkSubmitOperator(
        task_id=task_id,
        name=kebab_case(task_id),
        application=f"{tasks_path}/airflow_tasks/update_hive_metastore.py",
        application_args=[env, config.hive_schema, json.dumps(config.partition_columns), table_name],
        conn_id=spark_conn_id,
        properties_file="/opt/lh-spark/conf/spark-defaults.conf",
        conf={
            "spark.executor.instances": "1",
            "spark.executor.memory": "1g",
            "spark.driver.memory": "1g",
        },
        execution_timeout=Timeout.ONE_HOUR,
    )