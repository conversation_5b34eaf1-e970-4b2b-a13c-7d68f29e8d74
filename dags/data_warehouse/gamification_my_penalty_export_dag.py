import json
from datetime import timed<PERSON><PERSON>
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.providers.http.operators.http import SimpleHttpOperator

from common.airflow import notifications as notif
from data_warehouse.utils import airflow
from metadata import data_warehouse

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
aaa_client_secret = Variable.get("aaa_client_secret")

default_args = {
    "owner": "airflow",
    "start_date": "2024-08-20",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

TASK_CONFIG = {
    "penalty-my": {
        "dwh_dag_id": data_warehouse.GamificationDAG.DAG_ID,
        "dwh_task_name": data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_MASKED,
        "dwh_task_id": "gamification_my_penalty_report_masked",
    }
}

with DAG(
    dag_id="data_warehouse_gamification_penalty_export",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # every day at 12:00
    concurrency=3,
    catchup=False,
    max_active_runs=1,
    tags=["data_lake"],
) as dag:
    aaa_auth_token_task_id = "get_aaa_auth_token"
    get_aaa_auth_token_task = SimpleHttpOperator(
        task_id=aaa_auth_token_task_id,
        method="POST",
        http_conn_id="ninjavan-api",
        endpoint="global/aaa/login?grant_type=CLIENT_CREDENTIALS",
        data=json.dumps({"clientId": "AIRFLOW", "clientSecret": aaa_client_secret}),
        response_filter=lambda response: response.json()["accessToken"],
        response_check=lambda response: response.status_code == 200,
        do_xcom_push=True,
    )

    obs_bucket = f"nv-data-{env}-data-lake"
    ext_sensors, export_kafka, wait_tasks = {}, {}, {}
    for endpoint, config in TASK_CONFIG.items():
        ext_sensors[endpoint] = airflow.get_dwh_external_task_sensor(
            config["dwh_dag_id"],
            config["dwh_task_name"],
            config["dwh_task_id"],
            execution_delta=timedelta(hours=2),
        )

        wait_tasks[endpoint] = BashOperator(
            task_id=f"{endpoint}_wait",
            bash_command="sleep 180",
        )

        system_id = endpoint.split("-")[-1]
        if "daily" in endpoint:
            start_date = "{{ (data_interval_end - macros.timedelta(hours=2)).format('YYYY-MM-DDTHH:mm:SS') }}"
        elif "monthly" in endpoint:
            start_date = "{{ (data_interval_end - macros.timedelta(days=1)).format('YYYY-MM-DDTHH:mm:SS') }}"
        else:
            start_date = "{{ (data_interval_end - macros.timedelta(days=1)).format('YYYY-MM-DDTHH:mm:SS') }}"

        export_kafka[endpoint] = SimpleHttpOperator(
            task_id=f"publish_kafka_messages_{endpoint}",
            method="POST",
            http_conn_id="ninjavan-api",
            endpoint=f"{system_id}/dwh/1.0/reports/{endpoint}",
            headers={
                "Authorization": "Bearer " + f"{{{{ ti.xcom_pull(task_ids='get_aaa_auth_token') }}}}",
                "Content-Type": "application/json",
            },
            data=json.dumps(
                {"start_date": start_date, "end_date": "{{ data_interval_end.format('YYYY-MM-DDTHH:mm:SS') }}"}
            ),
            response_check=lambda response: response.status_code == 200,
        )
        [get_aaa_auth_token_task, ext_sensors[endpoint]] >> wait_tasks[endpoint] >> export_kafka[endpoint]