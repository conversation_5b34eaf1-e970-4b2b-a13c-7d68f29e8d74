import logging
from datetime import timed<PERSON><PERSON>

from airflow import DAG
from airflow.decorators import task
from airflow.exceptions import AirflowSkipException
from airflow.models import Variable

from data_warehouse.utils import obs, versioned_parquet
from metadata.constants import NV_DATA_ARCHIVE
from metadata.long_retention_tables import LONG_RETENTION_DWH_TABLES
from metadata.versioned_parquet_tables_masked import DataWarehouse

DAG_ID = "data_warehouse_vacuum"
MEASUREMENT_DATETIME = "measurement_datetime"
VACUUM_EXCLUSION = (
    "FS_SEGMENTATION_DAILY",
    "SHIPPER_SEGMENTATION_DAILY",
    "SHIPPERS_ENRICHED",
    "SS_SEGMENTATION_DAILY",
)
VERSION_RETENTION_MONTHS = 3

# Daily at 2PM SGT
schedule = "0 6 * * *"

default_args = {
    "owner": "airflow",
    "start_date": "2022-09-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}


def get_unneeded_partitions(table, cutoff_date):
    relevant_partitions = []
    available_partitions = []
    default_partitions = ["measurement_datetime", "system_id", "created_month"]

    partition_combination_df = obs.get_partition_combination_df(table, default_partitions)
    if not partition_combination_df.empty:
        logging.info("Table is saved as versioned_parquet. Finding relevant partitions now.")
        try:
            relevant_partitions = list(
                versioned_parquet.get_relevant_partitions(partition_combination_df, cutoff_date).keys()
            )
        except ValueError:
            logging.info(f"No table versions before {cutoff_date}.")
            return relevant_partitions
        logging.info(f"Relevant partitions: {relevant_partitions}")
        available_partitions = list(partition_combination_df[MEASUREMENT_DATETIME].unique())
    else:
        partitions_df = obs.get_partition_combination_df(table, [MEASUREMENT_DATETIME])
        if not partitions_df.empty:
            available_partitions = list(partitions_df[MEASUREMENT_DATETIME])

    unneeded_partitions = [
        partition
        for partition in available_partitions
        if partition < cutoff_date.strftime("%Y-%m-%d %H-%M-%S")
        and partition != "latest"
        and partition not in relevant_partitions
    ]
    return unneeded_partitions


@task
def get_vacuum_list():
    env = Variable.get("env")
    dwh = DataWarehouse(env)
    vacuum_list = [
        table_path
        for table_name, table_path in dwh.__dict__.items()
        if not table_name.startswith("_") and table_name not in VACUUM_EXCLUSION
    ]
    return vacuum_list


@task
def run_vacuum(table, data_interval_end=None):
    cutoff_date = data_interval_end.subtract(
        months=VERSION_RETENTION_MONTHS if table.split("/")[-1] not in LONG_RETENTION_DWH_TABLES else 13
    )
    logging.info(f"Running vacuum on {table} for versions before {cutoff_date}.")
    bucket, directory = obs.get_uri_bucket_and_directory(table)
    logging.info(f"obs bucket is {bucket}.")
    partitions = get_unneeded_partitions(table, cutoff_date)

    if not partitions:
        raise AirflowSkipException("No partitions to archive.")
    logging.info(f"Archiving {len(partitions)} partitions: {partitions}")
    for partition in partitions:
        path = f"{directory}/{MEASUREMENT_DATETIME}={partition}"
        obs.move_directory(bucket, NV_DATA_ARCHIVE, path, f"data_warehouse/{path}")


with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    schedule_interval=schedule,
    max_active_runs=1,
    catchup=False,
) as dag:
    run_vacuum.expand(table=get_vacuum_list())
