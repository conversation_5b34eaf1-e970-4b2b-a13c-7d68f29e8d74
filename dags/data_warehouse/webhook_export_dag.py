from datetime import datetime, timedelta
from importlib import import_module

from airflow import DAG
from airflow.models import Variable

from huawei.sensors.obs_objects_with_prefix_existence_sensor import HuaweiOBSObjectsWithPrefixExistenceSensor
from common.airflow import notifications as notif
from common.utils.nv_obs import strip_uri
from data_warehouse.utils import airflow
from metadata import data_warehouse
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI, Timeout
from custom_operators.dwh_to_obs_operator import DWHToOBSOperator

DAG_ID = data_warehouse.WebhookExportDAG.DAG_ID
TASKS = (data_warehouse.WebhookExportDAG.Task.PARTNERSHIP_WEBHOOK_LATENCY_EXPORT_MASKED,)
env = Variable.get("env")
obs_bucket = strip_uri(MASKED_DATA_WAREHOUSE_BASE_URI.format(env))


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2024, 5, 21),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


external_sensors = {}
obs_sensors = {}
with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    schedule_interval="0 2/6 * * *",
    dagrun_timeout=Timeout.FOUR_HOURS,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
) as dag:
    for task in TASKS:
        task_config = import_module(f"data_warehouse.tasks.dwh_export_tasks.{task}").get_task_config(env)

        dwh_to_obs_task = DWHToOBSOperator(
            task_id=task_config.task_id,
            name=task_config.task_name,
            input_path=task_config.input_path,
            conf=task_config.conf if task_config.conf else None,
            sql=task_config.sql,
            output_path=task_config.output_path,
        )

        for ext_dep in task_config.depends_on_external:
            ext_dag_task_id = f"{ext_dep.dag_id}_{ext_dep.task_id}"
            if ext_dag_task_id not in external_sensors:
                external_sensors[ext_dag_task_id] = airflow.get_dwh_external_task_sensor(
                    ext_dep.dag_id, ext_dep.task_id, DAG_ID, ext_dep.execution_delta
                )
            obs_sensor_id = f"check_obs_bucket_existence_{ext_dep.task_id}"
            if obs_sensor_id not in obs_sensors:
                obs_sensors[obs_sensor_id] = HuaweiOBSObjectsWithPrefixExistenceSensor(
                    task_id=obs_sensor_id,
                    bucket_name=obs_bucket,
                    prefix=task_config.obs_check_prefix,
                    params={"alert_channel": task_config.alert_channel},
                    conn_id="hwc",
                    execution_timeout=Timeout.ONE_MINUTE,
                    retries=1,
                )
            external_sensors[ext_dag_task_id] >> obs_sensors[obs_sensor_id] >> dwh_to_obs_task
