import re
from collections import namedtuple
from tempfile import NamedTemporaryFile
from typing import Tuple, Type, Union

import numpy as np
import pandas as pd
import pendulum
from huawei.hooks.obs_hook import <PERSON>aweiOBSHook
from lifetimes import <PERSON>GammaFitter, ModifiedBetaGeoFitter
from lifetimes.utils import ConvergenceError  # noqa

from common import stringcase
from common.date import to_measurement_datetime_str
from data_warehouse.utils import logger
from data_warehouse.utils.obs import get_uri_bucket_and_directory

logger = logger.get_logger(__file__)

PICKLE_EFFECTIVE_DATE_REGEX = "20[0-9]{2}-[0-9]{2}-[0-9]{2}"
PICKLE_EFFECTIVE_DATE_FORMAT = "%Y-%m-%d"
TaskConfig = namedtuple(
    "TaskConfig",
    (
        "system_id",
        "obs_conn_id",
        "input_file_path",
        "pickle_bucket",
        "pickle_directory",
        "execution_date",
        "model_types",
        "output_file_path",
    ),
)


def run(config) -> None:
    # Convert execution_date to pendulum DateTime if it's a string
    if isinstance(config.execution_date, str):
        from pendulum import parse
        execution_date = parse(config.execution_date)
    else:
        execution_date = config.execution_date
        
    obs_hook = HuaweiOBSHook()
    df = _load_base_data(obs_hook, config.input_file_path, execution_date, config.system_id)
    if "shipper_id" in df.columns:
        df["shipper_id"].fillna(value=0, inplace=True)
        df["shipper_id"] = df["shipper_id"].astype("int64")
    pickle_path = _get_relevant_pickle_path(
        obs_hook, config.pickle_bucket, config.pickle_directory, df["report_date"][0]
    )
    logger.info(f"Pickle path: {pickle_path}.")
    df_final = _transform_data(obs_hook, pickle_path, config.system_id, df, config.model_types)

    _write_data(df_final, config.system_id, config.output_file_path, config.execution_date)
    logger.info("All done.")


def _load_base_data(
    obs_hook: HuaweiOBSHook, input_table: str, next_execution_date: pendulum.DateTime, system_id: str
) -> pd.DataFrame:
    import io
    
    # Log input parameters
    logger.info(f"[DEBUG] Starting _load_base_data with input_table={input_table}, "
                f"next_execution_date={next_execution_date}, system_id={system_id}")
    
    # Get and log the base path
    base_path = _get_dwh_table_path(input_table, next_execution_date, system_id)
    logger.info(f"[DEBUG] Base path: {base_path}")
    
    # Parse bucket and directory
    bucket, directory = get_uri_bucket_and_directory(base_path)
    logger.info(f"[DEBUG] Bucket: {bucket}, Directory: {directory}")
    
    # List all objects in OBS without extension filtering
    logger.info(f"[DEBUG] Listing all objects in OBS with prefix: {directory}")
    all_objects = obs_hook.list_objects(bucket_name=bucket, prefix=directory)
    logger.info(f"[DEBUG] Found {len(all_objects)} total objects")
    
    # Filter out any directory markers or empty objects
    file_objects = [obj for obj in all_objects if not obj.endswith('/') and obj != directory]
    logger.info(f"[DEBUG] Found {len(file_objects)} file objects after filtering")
    
    # Log first few objects for debugging
    if file_objects:
        logger.info(f"[DEBUG] First few objects: {file_objects[:3]}")
    else:
        logger.warning("[DEBUG] No file objects found in the specified location")
    
    if not file_objects:
        logger.warning(f"[DEBUG] No files found at {base_path}")
        
        # Try listing the parent directory to see what's actually there
        parent_dir = "/".join(directory.split("/")[:-1])  # Get parent directory
        logger.info(f"[DEBUG] Checking parent directory: {parent_dir}")
        
        try:
            available_objects = obs_hook.list_objects(bucket_name=bucket, prefix=parent_dir, delimiter="/")
            logger.info(f"[DEBUG] Available objects under {parent_dir}: {available_objects}")
            
            # Try to find the most recent valid directory
            date_dirs = [d for d in available_objects if d.startswith(parent_dir)]
            logger.info(f"[DEBUG] Found {len(date_dirs)} date directories")
            
            most_recent_dir = sorted(date_dirs, reverse=True)[0] if date_dirs else "No directories found"
            logger.info(f"[DEBUG] Most recent directory: {most_recent_dir}")
            
            # Check if we can list files in the most recent directory
            if most_recent_dir and most_recent_dir != "No directories found":
                logger.info(f"[DEBUG] Checking files in most recent directory: {most_recent_dir}")
                recent_files = obs_hook.list_objects(bucket_name=bucket, prefix=most_recent_dir)
                logger.info(f"[DEBUG] Found {len(recent_files)} files in most recent directory")
                if recent_files:
                    logger.info(f"[DEBUG] First few files: {recent_files[:3]}")
                
        except Exception as e:
            logger.error(f"[DEBUG] Error while checking parent directory: {str(e)}", exc_info=True)
            available_objects = []
            most_recent_dir = "Error checking parent directory"
        
        error_msg = (
            f"No files found at {base_path}. "
            f"Available objects under {parent_dir}: {available_objects}. "
            f"Most recent directory: {most_recent_dir}"
        )
        logger.error(f"[ERROR] {error_msg}")
        raise FileNotFoundError(error_msg)
    
    logger.info(f"[DEBUG] Found {len(file_objects)} files to process")
    
    # Use in-memory processing instead of downloading to disk
    dfs = []
    for i, file_path in enumerate(file_objects, 1):
        logger.info(f"[DEBUG] Processing file {i}/{len(file_objects)}: {file_path}")
        
        try:
            # Get the object data directly into memory
            logger.info(f"[DEBUG] Reading data from OBS directly: {file_path}")
            response = obs_hook.get_conn().getObject(bucket, file_path)
            logger.info(f"[DEBUG] Got response from OBS: {response['status']}")
            
            # Read from response and add detailed logging
            logger.info(f"[DEBUG] Response keys: {list(response.keys())}")
            
            data = response.get('body', None)
            logger.info(f"[DEBUG] Data type: {type(data)}, HasRead: {hasattr(data, 'read')}")
            
            if data is None:
                logger.warning(f"[DEBUG] No data returned from OBS for {file_path}")
                continue
                
            # Debug the data
            if hasattr(data, '__dict__'):
                logger.info(f"[DEBUG] Data attributes: {dir(data)}")
        except Exception as e:
            logger.error(f"[ERROR] Error getting object from OBS: {str(e)}", exc_info=True)
            continue  # Try the next file
        
        try:
            # Try a different approach for getting data from OBS
            logger.info(f"[DEBUG] Using direct download method for file: {file_path}")
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_path = temp_file.name
                logger.info(f"[DEBUG] Downloading to temp file: {temp_path}")
            
            # Use the OBS Hook's correct download method
            obs_hook.download(bucket, file_path, temp_path)
            logger.info(f"[DEBUG] Successfully downloaded to: {temp_path}")
            
            # Use the file directly
            try:
                logger.info(f"[DEBUG] Reading from temp file: {temp_path}")
                df = pd.read_parquet(temp_path)
                logger.info(f"[DEBUG] Successfully read file. Shape: {df.shape}, Columns: {list(df.columns)}")
                dfs.append(df)
                import os
                os.unlink(temp_path)  # Delete temp file
                logger.info(f"[DEBUG] Deleted temp file: {temp_path}")
                continue  # Skip the in-memory approach if we succeed with file
            except Exception as file_e:
                logger.error(f"[ERROR] Error reading from temp file: {str(file_e)}", exc_info=True)
                
            # Try the in-memory approach as a backup
            # Create bytes buffer from the response body - with better handling
            logger.info(f"[DEBUG] Falling back to in-memory approach")
            # Handle the buffer creation with proper error handling
            try:
                if hasattr(data, 'read') and callable(data.read):
                    buffer = io.BytesIO(data.read())
                    logger.info(f"[DEBUG] Created buffer from data.read()")
                elif isinstance(data, bytes):
                    buffer = io.BytesIO(data)
                    logger.info(f"[DEBUG] Created buffer directly from bytes data")
                else:
                    logger.warning(f"[DEBUG] Could not create buffer - data is type {type(data)} which is not supported")
                    # Safer approach - give up on this file
                    continue
            except Exception as buffer_e:
                logger.error(f"[ERROR] Error creating buffer: {str(buffer_e)}", exc_info=True)
                continue
            
            # Try reading as a parquet file
            try:
                logger.info(f"[DEBUG] Reading file from memory buffer")
                df = pd.read_parquet(buffer)
                logger.info(f"[DEBUG] Successfully read file. Shape: {df.shape}, Columns: {list(df.columns)}")
                if not df.empty:
                    logger.info(f"[DEBUG] First few rows: {df.head(2).to_dict('records')}")
                else:
                    logger.warning("[DEBUG] DataFrame is empty")
                dfs.append(df)
            except Exception as e:
                logger.error(f"[ERROR] Error parsing file as parquet: {str(e)}", exc_info=True)
                
                # Try detecting format and reading with fastparquet engine
                try:
                    logger.info(f"[DEBUG] Trying with fastparquet engine")
                    buffer.seek(0)  # Reset buffer
                    df = pd.read_parquet(buffer, engine='fastparquet')
                    logger.info(f"[DEBUG] Success with fastparquet. Shape: {df.shape}")
                    dfs.append(df)
                except Exception as e2:
                    logger.error(f"[ERROR] Fastparquet also failed: {str(e2)}", exc_info=True)
                    
                    # Try with different format - let's check if it's CSV
                    try:
                        logger.info(f"[DEBUG] Trying to parse as CSV")
                        buffer.seek(0)  # Reset buffer
                        df = pd.read_csv(buffer)
                        logger.info(f"[DEBUG] Success reading as CSV. Shape: {df.shape}")
                        dfs.append(df)
                    except Exception as e3:
                        logger.error(f"[ERROR] CSV parsing also failed: {str(e3)}", exc_info=True)
                        # Don't raise here - just continue to next file
        except Exception as outer_e:
            logger.error(f"[ERROR] Error processing buffer for file {file_path}: {str(outer_e)}", exc_info=True)
            continue  # Try the next file
    
    if not dfs:
        error_msg = "No valid Parquet files could be loaded"
        logger.error(f"[ERROR] {error_msg}")
        raise ValueError(error_msg)
    
    logger.info(f"[DEBUG] Successfully loaded {len(dfs)} DataFrames. Concatenating...")
    result = pd.concat(dfs, ignore_index=True)
    logger.info(f"[DEBUG] Final DataFrame shape: {result.shape}")
    return result


def _get_relevant_pickle_path(
    obs_hook: HuaweiOBSHook, pickle_bucket: str, pickle_dir: str, report_date: pendulum.DateTime
) -> str:
    pickle_version_directories = obs_hook.list_objects(bucket_name=pickle_bucket, prefix=pickle_dir, delimiter="/")
    pickle_versions = {
        re.findall(PICKLE_EFFECTIVE_DATE_REGEX, directory)[0] for directory in pickle_version_directories
    }

    pickle_version_cutoff = report_date.strftime(PICKLE_EFFECTIVE_DATE_FORMAT)
    latest_pickle_version = "0000-00-00"
    for version in pickle_versions:
        if latest_pickle_version < version <= pickle_version_cutoff:
            latest_pickle_version = version

    if latest_pickle_version == "0000-00-00":
        raise ValueError(f"No relevant pickle path for report_date={report_date}.")

    return f"obs://{pickle_bucket}/{pickle_dir}={latest_pickle_version}"


def _get_fitter(
    obs_hook: HuaweiOBSHook,
    pickle_path: str,
    system_id: str,
    model_type: str,
    fitter: Union[Type[GammaGammaFitter], Type[ModifiedBetaGeoFitter]],
) -> Union[GammaGammaFitter, ModifiedBetaGeoFitter]:
    # Import required modules
    import tempfile
    import os
    fitter_type = stringcase.snake_case(fitter.__name__)
    pickle_path = f"{pickle_path}/{fitter_type}_{model_type}"

    penalizer_coef_path = f"{pickle_path}/penalizer_coef.pkl"
    
    # Download the pickle file locally first
    penalizer_bucket, penalizer_key = get_uri_bucket_and_directory(penalizer_coef_path)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_path = tmp_file.name
        logger.info(f"Downloading penalizer coefficients from {penalizer_coef_path} to {tmp_path}")
        
    # Download the file
    obs_hook.download(bucket_name=penalizer_bucket, object_name=penalizer_key, filename=tmp_path)
    
    # Read the local file
    logger.info(f"Reading penalizer coefficients from {tmp_path}")
    penalizer_coef = pd.read_pickle(tmp_path).at[system_id, "penalizer_coef"]
    
    # Clean up
    import os
    os.unlink(tmp_path)
    fitter_obj = fitter(penalizer_coef)

    # tempfile already imported at the beginning of the function
    
    model_path = f"{pickle_path}/{system_id}_model.pkl"
    model_bucket, model_directory = get_uri_bucket_and_directory(model_path)
    
    # Download model file locally
    with tempfile.NamedTemporaryFile(delete=False) as tmp_model:
        tmp_model_path = tmp_model.name
        logger.info(f"Downloading model from {model_path} to {tmp_model_path}")
    
    # Download the file
    model_file = obs_hook.download(bucket_name=model_bucket, object_name=model_directory, filename=tmp_model_path)
    logger.info(f"Downloaded model to {tmp_model_path}")
    
    # Load model from local file
    try:
        logger.info(f"Loading model from {tmp_model_path}")
        fitter_obj.load_model(tmp_model_path)
        logger.info("Successfully loaded model")
    finally:
        # Clean up (os already imported at the beginning)
        if os.path.exists(tmp_model_path):
            os.unlink(tmp_model_path)
            logger.info(f"Deleted temporary model file {tmp_model_path}")
    return fitter_obj


def _transform_data(
    obs_hook: HuaweiOBSHook, pickle_path: str, system_id: str, df: pd.DataFrame, model_types: Tuple
) -> pd.DataFrame:
    gg_fitter = _get_fitter(obs_hook, pickle_path, system_id, "default", fitter=GammaGammaFitter)
    for model_type in model_types:
        logger.info(f"Fitting data with '{model_type}' model.")
        mbg_fitter = _get_fitter(obs_hook, pickle_path, system_id, model_type, fitter=ModifiedBetaGeoFitter)
        try:
            df["alive_probability"] = mbg_fitter.conditional_probability_alive(
                df["frequency"], df["recency"], df["age"]
            )
            df["lifetime_value"] = gg_fitter.customer_lifetime_value(
                mbg_fitter,
                df["frequency"],
                df["recency"],
                df["age"],
                df["monetary_value"],
                time=12,
                freq="D",
                discount_rate=0,
            )
        except ConvergenceError:
            logger.info("Model failed to converge. Attempting next model.")
            pass
        else:
            df["alive_probability"] = df["alive_probability"].round(2)
            df["alive_probability_model"] = model_type
            df.loc[(df["lifetime_value"] < 0) | (df["lifetime_value"] == np.inf), "lifetime_value"] = None
            df["lifetime_value"] = df["lifetime_value"].round(0).astype("Int64")
            return df
    raise ConvergenceError("All models failed to converge.")


def _write_data(df: pd.DataFrame, system_id: str, output_table: str, next_execution_date: pendulum.DateTime):
    # Import necessary modules
    import tempfile
    import io
    import os
    
    base_path = _get_dwh_table_path(output_table, next_execution_date, system_id)
    file_path = f"{base_path}/1.snappy.parquet"
    logger.info(f"Writing output to {file_path}.")
    
    # Extract the bucket and key for OBS
    bucket, key = get_uri_bucket_and_directory(file_path)
    
    # First write to a temporary file locally
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_path = tmp_file.name
        logger.info(f"Writing to temporary file: {tmp_path}")
    
    # Write the DataFrame to the temporary file
    df.to_parquet(tmp_path, compression="snappy", index=False)
    logger.info(f"Successfully wrote DataFrame to temporary file: {tmp_path}")
    
    # Upload the file to OBS
    try:
        logger.info(f"Uploading file to OBS bucket: {bucket}, key: {key}")
        # Create OBS hook
        obs_hook = HuaweiOBSHook()
        
        # Open the file and upload it
        with open(tmp_path, 'rb') as file_obj:
            obs_hook.streaming_upload(bucket_name=bucket, object_key=key, file=file_obj)
            
        logger.info(f"Successfully uploaded file to {file_path}")
    finally:
        # Clean up the temporary file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
            logger.info(f"Deleted temporary file: {tmp_path}")


def _get_dwh_table_path(base_path: str, next_execution_date, system_id: str):
    if isinstance(next_execution_date, str):
        from pendulum import parse
        next_execution_date = parse(next_execution_date)
    measurement_datetime = to_measurement_datetime_str(next_execution_date)
    return f"{base_path}/measurement_datetime={measurement_datetime}/system_id={system_id}"