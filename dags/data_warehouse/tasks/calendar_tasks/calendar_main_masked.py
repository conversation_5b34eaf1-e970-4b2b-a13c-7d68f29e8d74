import sys
from pyspark.sql import SparkSession

# Import logger first
from data_warehouse.utils import logger
logger = logger.get_logger(__file__)

# Import base module
from data_warehouse.tasks.calendar_tasks import base
from metadata import constants, delta_tables, parquet_tables_masked


CALENDAR_START_DATE = "2015-01-01"
CALENDAR_END_DATE = "2025-12-31"
DEFAULT_WEEK_MASK = "1111110"


def get_task_config(env, system_id):
    holidays_file_path = f"{delta_tables.GDrive(env).HOLIDAYS}"
    calendar_file_path = f"{parquet_tables_masked.DataWarehouse(env).CALENDAR}/calendar/system_id={system_id}/1.snappy.parquet"

    logger.info(f"Holidays File Path: {holidays_file_path}")
    logger.info(f"Calendar File Path: {calendar_file_path}")

    default_week_mask = DEFAULT_WEEK_MASK
    fri_off_regions = (("my", "Kedah"), ("my", "Terengganu"), ("my", "<PERSON><PERSON><PERSON>"))
    fri_off_week_masks = ["1111011"] * len(fri_off_regions)
    custom_week_masks = dict(zip(fri_off_regions, fri_off_week_masks))
    if system_id == constants.SystemID.TH:
        default_week_mask = "1111111"
    elif system_id == constants.SystemID.IN:
        default_week_mask = "1111100"

    return base.TaskConfig(
        system_id=system_id,
        holidays_file_path=holidays_file_path,
        default_week_mask=default_week_mask,
        custom_week_masks=custom_week_masks,
        calendar_type="Main",
        calendar_start_date=CALENDAR_START_DATE,
        calendar_end_date=CALENDAR_END_DATE,
        output_file_path=calendar_file_path,
    )


if __name__ == "__main__":
    env, system_id = sys.argv[1:]
    config = get_task_config(env, system_id)
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")

    try:
        base.get_calendar(config, spark)
    finally:
        spark.stop()
