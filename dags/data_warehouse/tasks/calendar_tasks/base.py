from collections import namedtuple

import numpy as np
import pandas as pd

from data_warehouse.utils import logger,obs

logger = logger.get_logger(__file__)

TaskConfig = namedtuple(
    "TaskConfig",
    (
        "system_id",
        "holidays_file_path",
        "default_week_mask",
        "custom_week_masks",
        "calendar_type",
        "calendar_start_date",
        "calendar_end_date",
        "output_file_path",
    ),
)


def get_week_mask(system_id, region, default_week_mask, custom_week_masks=None):
    if custom_week_masks is None:
        custom_week_masks = {}
    custom_week_mask = custom_week_masks.get((system_id, region))
    return custom_week_mask if custom_week_mask else default_week_mask


def write_data(df, file_path):
    """
    Write dataframe to specified file path using HuaweiOBSHook
    
    Args:
        df: DataFrame to upload
        file_path: File path in format 'obs://bucket_name/object_path'
    """
    logger.info(f"Writing data to {file_path}")
   
    file_path = obs.strip_uri(file_path)
    # Parse file path to get bucket and object path
    parts = file_path.split('/', 1)
    if len(parts) < 2:
        raise ValueError(f"Invalid file path format: {file_path}. Expected 'bucket_name/object_path'")
    
    bucket_name = parts[0]
    object_path = parts[1]
    
    # Convert DataFrame to parquet
    df_parq_obj = df.to_parquet(compression="snappy", engine="pyarrow", index=False)
    
    logger.info(f"Uploading to OBS bucket {bucket_name}")
    obs.streaming_upload(bucket_name, object_path, df_parq_obj)
    logger.info(f"Successfully uploaded data to {file_path}")



def _get_holidays(holidays_file_path, system_id, spark):
    df = spark.read.format("delta").load(holidays_file_path).toPandas()
    df = df.query(f"country == '{system_id}' and is_deleted == 0")
    df = df.drop(columns="is_deleted")
    return df


def _get_regional_holidays(holidays_df, region):
    regional_holidays_df = holidays_df.query("region == 'national'")
    if region != "national":
        region_specific_holidays_df = holidays_df.query(f"region == '{region}'")
        regional_holidays_df = pd.concat([regional_holidays_df, region_specific_holidays_df])
    regional_holidays_df = regional_holidays_df.drop(columns=["country", "region"])
    return regional_holidays_df


def _get_regional_calendar(
    system_id, region, regional_holidays_df, bus_day_calendar, calendar_start_date, calendar_end_date
):
    regional_calendar_df = pd.DataFrame()
    regional_calendar_df["date"] = pd.date_range(start=calendar_start_date, end=calendar_end_date)
    regional_calendar_df["country"] = system_id
    regional_calendar_df["region"] = region
    regional_calendar_df["day"] = regional_calendar_df["date"].dt.strftime("%A")
    regional_calendar_df["date"] = regional_calendar_df["date"].dt.date

    # merge to get comments column from holidays
    regional_calendar_df = pd.merge(regional_calendar_df, regional_holidays_df, how="left", on=["date"])

    regional_calendar_date_array = regional_calendar_df["date"].values.astype("datetime64[D]")
    regional_calendar_df["working_day"] = np.is_busday(regional_calendar_date_array, busdaycal=bus_day_calendar)
    regional_calendar_df["working_day"] = regional_calendar_df["working_day"].astype("int32")
    regional_calendar_df["working_day_cum"] = regional_calendar_df["working_day"].cumsum()

    # defined to be same as date, no business day rounding needed
    regional_calendar_df["next_working_day_0"] = regional_calendar_df["date"]
    for days_ahead in range(1, 91):
        regional_calendar_df[f"next_working_day_{days_ahead}"] = np.busday_offset(
            regional_calendar_date_array, days_ahead, roll="backward", busdaycal=bus_day_calendar
        )
        regional_calendar_df[f"next_working_day_{days_ahead}"] = regional_calendar_df[
            f"next_working_day_{days_ahead}"
        ].dt.date
    return regional_calendar_df


def _customize_regional_holidays(start_date, end_date, off_day_mask):
    history_dates = np.arange(np.datetime64(start_date), np.datetime64(end_date))
    history_off_days_array = history_dates[np.is_busday(history_dates, busdaycal=np.busdaycalendar(off_day_mask))]
    return history_off_days_array


def _recover_off_days(regional_holidays_array, config, region, calendar_type):
    recover_end_date = config.calendar_start_date
    off_day_mask = "1111111"
    if config.system_id == "th":
        # Before 2022, Thailand works with mask "1111110".
        # This step is to restore the Sunday off for dates before 2022-01-01.
        recover_end_date = "2022-01-01"
        off_day_mask = "0000001"  # sunday
    elif calendar_type == "Speed" and config.system_id == "ph":
        recover_end_date = "2022-02-01"
        off_day_mask = "0000001"  # sunday
    elif calendar_type == "Speed" and config.system_id == "my":
        recover_end_date = "2022-01-01"
        if region in ["Kedah", "Terengganu", "Kelantan"]:
            off_day_mask = "0000100"  # friday
        else:
            off_day_mask = "0000001"  # sunday
    elif calendar_type == "Speed" and config.system_id == "id":
        recover_end_date = "2023-07-01"
        off_day_mask = "0000001"  # sunday
    elif calendar_type == "Recovery" and config.system_id == "my":
        recover_end_date = "2022-10-01"
        off_day_mask = "0000011"  # saturday and sunday
    history_off_days_array = _customize_regional_holidays(config.calendar_start_date, recover_end_date, off_day_mask)
    regional_holidays_array = np.concatenate([regional_holidays_array, history_off_days_array])
    return regional_holidays_array


def get_calendar(config, spark):
    calendar_df = pd.DataFrame()
    holidays_df = _get_holidays(config.holidays_file_path, config.system_id, spark)

    if config.calendar_type == "Shipper":
        regions = ("national",)
    else:
        regions = set(holidays_df["region"].tolist())
    logger.info(f"Regions: {regions}")

    for region in regions:
        logger.info(f"Generating calendar for region: {region}")
        regional_holidays_df = _get_regional_holidays(holidays_df, region)
        regional_holidays_array = regional_holidays_df["date"].values.astype("datetime64[D]")
        weekmask = get_week_mask(config.system_id, region, config.default_week_mask, config.custom_week_masks)
        regional_holidays_array = _recover_off_days(regional_holidays_array, config, region, config.calendar_type)
        bus_day_calendar = np.busdaycalendar(weekmask, regional_holidays_array)

        regional_calendar_df = _get_regional_calendar(
            config.system_id,
            region,
            regional_holidays_df,
            bus_day_calendar,
            config.calendar_start_date,
            config.calendar_end_date,
        )
        calendar_df = pd.concat([regional_calendar_df, calendar_df])

    logger.info("Exporting results to GCS...")
    write_data(calendar_df, config.output_file_path)
    logger.info("All done.")
    return calendar_df
