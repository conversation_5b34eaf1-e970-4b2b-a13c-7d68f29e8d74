import sys
from pyspark.sql import SparkSession

from data_warehouse.tasks.calendar_tasks import base
from data_warehouse.utils import logger
from metadata import constants, delta_tables, parquet_tables_masked

logger = logger.get_logger(__file__)

CALENDAR_START_DATE = "2015-01-01"
CALENDAR_END_DATE = "2025-12-31"
DEFAULT_WEEK_MASK = "1111110"


def get_task_config(env, system_id):
    holidays_file_path = f"{delta_tables.GDrive('prod').HOLIDAYS_SHIPPER}"
    calendar_file_path = (
        f"{parquet_tables_masked.DataWarehouse(env).CALENDAR_SHIPPER}/system_id={system_id}/1.snappy.parquet"
    )

    default_week_mask = DEFAULT_WEEK_MASK
    if system_id == constants.SystemID.ID:
        default_week_mask = "1111100"

    return base.TaskConfig(
        system_id=system_id,
        holidays_file_path=holidays_file_path,
        default_week_mask=default_week_mask,
        custom_week_masks=None,
        calendar_type="Shipper",
        calendar_start_date=CALENDAR_START_DATE,
        calendar_end_date=CALENDAR_END_DATE,
        output_file_path=calendar_file_path,
    )


if __name__ == "__main__":
    env, system_id = sys.argv[1:]
    config = get_task_config(env, system_id)
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")

    try:
        base.get_calendar(config, spark)
    finally:
        spark.stop()
