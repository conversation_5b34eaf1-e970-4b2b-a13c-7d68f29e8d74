import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_RECORD_TYPE_ENRICHED,
                view_name="salesforce_record_type_enriched",
            ),
        ),
        delta_tables=(base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).LEAD, view_name="salesforce_lead"),),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    salesforce_lead.id
                    , salesforce_lead.name
                    , case
                        when
                            salesforce_lead.status = 'Qualified'
                            or salesforce_lead.is_converted = True
                        then 'Converted'
                        else salesforce_lead.status
                    end as status
                    , salesforce_lead.city
                    , salesforce_lead.email
                    , salesforce_lead.rating
                    , salesforce_lead.source_c as source
                    , salesforce_lead.industry_c as industry
                    , salesforce_lead.company as company_name
                    , record_type.name as record_type
                    , cast(salesforce_lead.vol_month_c as int) as expected_volume_monthly
                    , array_max(
                            transform(
                                regexp_extract_all(
                                        replace(lqo_expected_vol_mth_c, ',', '')
                                    , '([0-9]+)'),
                                    x -> cast(x as int)
                                )
                        ) as max_lqo_expected_volume_monthly
                    , salesforce_lead.owner_id as user_id
                    , salesforce_lead.created_by_id
                    , salesforce_lead.converted_account_id as account_id
                    , salesforce_lead.converted_opportunity_id as opportunity_id
                    , salesforce_lead.lead_source_c as lead_source
                    , salesforce_lead.lead_source_nd_c as lead_source_nd
                    , salesforce_lead.lead_source_details_c as lead_source_details
                    , salesforce_lead.lead_gen_channel_c as lead_gen_channel
                    , salesforce_lead.lead_gen_channel_nd_c as lead_gen_channel_nd
                    , salesforce_lead.lead_qualifier_c as lead_qualifier
                    , salesforce_lead.notes_c as notes
                    , salesforce_lead.page_url_c as page_url
                    , salesforce_lead.nv_product_line_c as nv_product_line
                    , salesforce_lead.activation_blocker_c as activation_blocker
                    , salesforce_lead.detailed_activation_blocker_c as detailed_activation_blocker
                    , salesforce_lead.extra_detail_c as extra_detail
                    , if(
                        salesforce_lead.lead_qualifier_c = 'None' or salesforce_lead.lead_qualifier_c is null
                        , salesforce_lead.owner_id
                        , salesforce_lead.lead_qualifier_c
                    ) as initial_owner_id
                    , coalesce(lead_qualifier_user.name, owner_user.name) as initial_owner
                    , coalesce(lead_qualifier_user.sales_team, owner_user.sales_team) as initial_owner_sales_team
                    , date(salesforce_lead.converted_date) as conversion_date
                    , date(salesforce_lead.prospect_date_c) as prospect_date
                    , date(salesforce_lead.prospect_qualified_date_c) as prospect_qualified_date
                    , date(salesforce_lead.qualifying_date_c) as qualifying_date
                    , date(salesforce_lead.disqualify_prospect_date_c) as disqualify_prospect_date
                    , date(salesforce_lead.disqualify_suspect_date_c) as disqualify_suspect_date
                    , date(salesforce_lead.disqualified_date_c) as disqualified_date
                    , salesforce_lead.disqualified_reason_c as disqualification_reason
                    , salesforce_lead.detailed_disqualified_reason_c as detailed_disqualification_reason
                    , date(salesforce_lead.last_transfer_date) as last_transfer_date
                    , date(salesforce_lead.last_activity_date) as last_activity_date
                    , date(salesforce_lead.suspect_future_follow_up_date_c) as suspect_future_follow_up_date
                    , date(salesforce_lead.suspect_date_c) as suspect_date
                    , from_utc_timestamp(salesforce_lead.created_date, {{ get_local_timezone }}) as creation_datetime
                    , from_utc_timestamp(
                        salesforce_lead.last_modified_date, {{ get_local_timezone }}
                    ) as last_modified_datetime
                    , salesforce_lead.competitor_used_c as competitor_used
                    , salesforce_lead.no_of_employees_c as no_of_employees
                    , salesforce_lead.buyapowa_code_c as buyapowa_code
                    , salesforce_lead.referrer_email_c as referrer_email
                    , cast(salesforce_lead.referrer_global_id_c as int) as referrer_global_id
                    , interested_services_c as interested_services
                    , record_type.system_id as country
                    , record_type.system_id
                    , date_format(
                        from_utc_timestamp(salesforce_lead.created_date, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                from salesforce_lead
                left join salesforce_user_enriched as owner_user on
                    salesforce_lead.owner_id = owner_user.id
                left join salesforce_user_enriched as lead_qualifier_user on
                    salesforce_lead.lead_qualifier_c = lead_qualifier_user.id
                left join salesforce_record_type_enriched as record_type on
                    salesforce_lead.record_type_id = record_type.id
                where
                    salesforce_lead.is_deleted = false
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("record_type.system_id"),
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_LEAD_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
