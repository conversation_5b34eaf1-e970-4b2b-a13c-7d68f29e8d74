import sys

from pyspark.sql import SparkSession
from datetime import timedelta

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.UPDATE_CONSIGNEE_EMAIL_EVENTS_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.UPDATE_CONSIGNEE_EMAIL_EVENTS_MASKED,
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                select
                    cast(order_id as long) as order_id
                    , type
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS event_timestamp
                    , get_json_object(data,'$.to_email.old_value') as original_consignee_email
                    , get_json_object(data,'$.to_email.new_value') as new_consignee_email
                    , rank() over (partition by order_id order by created_at) as change_sequence
                    , date_format(created_at, 'yyyy-MM') AS created_month
                    , '{{system_id}}' as system_id
                from order_events 
                where
                    system_id = '{{system_id}}'
                    -- filter for event = update contact information
                    and type = 12
                    -- filter for email changes only
                    and coalesce(get_json_object(data,'$.to_email.old_value'),'') != coalesce(get_json_object(data,'$.to_email.new_value'),'')

                """,
                jinja_arguments={"system_id": system_id, "get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    order_id
                    , type
                    , event_timestamp
                    , original_consignee_email
                    , split(lower(original_consignee_email),'@')[1] as original_consignee_email_domain
                    , new_consignee_email
                    , split(lower(new_consignee_email),'@')[1] as new_consignee_email_domain
                    , change_sequence
                    , created_month
                    , system_id
                from base

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).UPDATE_CONSIGNEE_EMAIL_EVENTS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()