# The output from this task is used for exporting to SFMC only. It should not be exposed to Metabase or used for
# analytics. As such, column naming in this table also need not follow DWH conventions.
import sys

from pyspark.sql import SparkSession
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.FS_SEGMENTATION_DAILY_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.FS_SEGMENTATION_DAILY_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.EberDAG.DAG_ID,
            task_id=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_LEADS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
        ),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    scvd_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 3)
    scvm_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # measurement_datetime for field_sales_short_term_retention
    measurement_datetime_partition = f"""/measurement_datetime={
        measurement_datetime.strftime('%Y-%m-01 02-%M-%S')
    }"""

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.Eber(input_env, is_masked).POINT_TRANSACTIONS,
                view_name="point_transactions",
            ),
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).USER,
                view_name="salesforce_user",
            ),

        ),
        parquet_tables=(
            base.InputTable(
                path=(versioned_parquet_tables_masked.DataWarehouse(
                    input_env).FIELD_SALES_SHORT_TERM_RETENTION + measurement_datetime_partition),
                view_name="field_sales_short_term_retention",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                input_range=scvd_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
                input_range=scvm_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_USERS,
                view_name="ninja_rewards_users",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_BUDDIES_LEADS,
                view_name="ninja_buddies_leads",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_LEAD_ENRICHED,
                view_name="salesforce_lead_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_OPPORTUNITY_ENRICHED,
                view_name="salesforce_opportunity_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CONTACT_ENRICHED,
                view_name="salesforce_contact_enriched",
            ),
        ),
        version_datetime=measurement_datetime,

    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="nr_base",
                jinja_template="""
                    select 
                        salesforce_account_enriched.id as account_id
                        , salesforce_account_enriched.sales_channel
                        , ninja_rewards_users.id as eber_id
                        , date_trunc('day', ninja_rewards_users.join_datetime) as nr_join_date
                        , date_trunc('day'
                                , ninja_rewards_users.last_redemption_datetime) as nr_last_redemption_date
                        , date_trunc('day'
                                , ninja_rewards_users.last_used_reward_datetime) as nr_last_used_reward_date
                        , ninja_rewards_users.current_member_tier
                    from salesforce_account_enriched
                    join ninja_rewards_users
                        on salesforce_account_enriched.shipper_id = ninja_rewards_users.shipper_id
                    where sales_channel = 'Field Sales'
                        and join_datetime is not null
                """,
            ),
            base.TransformView(
                view_name="nr_enriched",
                jinja_template="""
                with filtered_point_transactions as (
                    select *
                    from point_transactions
                    --where type = 'redeemed'
                        --and void = false
                )

                select 
                    account_id
                    , from_utc_timestamp(filtered_point_transactions.created_at
                            , {{ get_local_timezone }}) as nr_redemption_date
                    , count(get_json_object(coupon_issued
                            , '$.main_coupon.id')) as nr_redemption_count
                from nr_base
                join filtered_point_transactions
                    on nr_base.eber_id = filtered_point_transactions.user_id
                group by 1,2

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("filtered_point_transactions.system_id"),
                },
            ),
            base.TransformView(
                view_name="nb_enriched",
                jinja_template="""
                select
                    salesforce_account_enriched.id as account_id
                    , salesforce_account_enriched.sales_channel
                    , date_trunc('day'
                        , ninja_buddies_leads.lead_creation_datetime) as nb_referral_date
                    , count(ninja_buddies_leads.lead_id) as nb_referral_count
                from salesforce_account_enriched
                join ninja_buddies_leads
                    on salesforce_account_enriched.shipper_id 
                        = ninja_buddies_leads.buddy_shipper_id
                where sales_channel = 'Field Sales'
                group by 1,2,3
                """,
            ),
            base.TransformView(
                view_name="last_referral_tb",
                jinja_template="""
                select account_id
                    , max(nb_referral_date) nb_last_referral_date
                from nb_enriched
                where nb_referral_date <= date('{{ measurement_datetime_utc }}')
                    and nb_referral_count > 0
                group by 1
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="fs_shippers_enriched",
                jinja_template="""
                select *
                from shippers_enriched
                where sales_channel='Field Sales'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="fs_lead_enriched",
                jinja_template="""
                select *
                from salesforce_lead_enriched
                where True
                -- remove ninja direct & crossborder
                    and lower(salesforce_lead_enriched.record_type) not like '%ninja direct%'
                    and lower(salesforce_lead_enriched.record_type) not like '%crossborder%'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),

            base.TransformView(
                view_name="fs_opportunity_enriched",
                jinja_template="""
                select *
                from salesforce_opportunity_enriched
                where True
                    -- remove ninja direct & crossborder
                    and lower(salesforce_opportunity_enriched.record_type) not like '%ninja direct%'
                    and lower(salesforce_opportunity_enriched.record_type) not like '%crossborder%'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="fs_account_enriched",
                jinja_template="""
                select *
                from salesforce_account_enriched
                where sales_channel='Field Sales'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="sf_lead_interim",
                jinja_template="""

                with lead_base as (

                    select '{{ measurement_datetime_utc }}' as measurement_datetime
                        , fs_lead_enriched.id as lead_id
                        , fs_opportunity_enriched.id as opportunity_id
                        , fs_account_enriched.id as account_id
                        , fs_account_enriched.shipper_id
                        , coalesce(fs_account_enriched.name, fs_opportunity_enriched.opportunity_name, fs_lead_enriched.name) as shipper_name
                        , salesforce_user.sales_team_c as sales_team
                        , salesforce_user.name as salesperson_name
                        , salesforce_user.email as salesperson_email
                        , salesforce_user.phone as salesperson_phone
                        , coalesce(fs_opportunity_enriched.shipper_email, fs_lead_enriched.email) as email
                        , coalesce(fs_opportunity_enriched.system_id, fs_lead_enriched.system_id) as system_id
                        , 'Field Sales' as sales_channel
                        , coalesce(fs_opportunity_enriched.stage
                                , fs_lead_enriched.status) as lifecycle
                        , coalesce(fs_opportunity_enriched.expected_volume_monthly
                                , fs_lead_enriched.expected_volume_monthly) as expected_volume_monthly
                        , fs_lead_enriched.creation_datetime as lead_creation_datetime
                        , coalesce(fs_lead_enriched.disqualify_prospect_date
                                , fs_lead_enriched.disqualify_suspect_date) as disqualified_date
                        , fs_opportunity_enriched.creation_datetime as opportunity_creation_datetime
                        , fs_opportunity_enriched.onboarded_date
                        , fs_opportunity_enriched.ready_to_ship_date
                        , fs_opportunity_enriched.won_date
                        , fs_opportunity_enriched.lost_date
                        , fs_opportunity_enriched.future_opportunity_date
                        , fs_shippers_enriched.first_order_completion_date
                        , fs_shippers_enriched.first_order_placed_date
                        , fs_shippers_enriched.last_order_placed_date
                        , fs_lead_enriched.disqualification_reason
                        , fs_opportunity_enriched.loss_reason
                        , fs_lead_enriched.lead_gen_channel
                        , fs_lead_enriched.lead_source
                        , fs_lead_enriched.lead_source_details
                    from fs_lead_enriched
                    full outer join fs_opportunity_enriched
                        on fs_lead_enriched.opportunity_id = fs_opportunity_enriched.id
                    left join fs_account_enriched
                        on fs_opportunity_enriched.account_id = fs_account_enriched.id
                    left join fs_shippers_enriched
                        on fs_account_enriched.shipper_id = fs_shippers_enriched.id
                    left join salesforce_user 
                        on coalesce(fs_opportunity_enriched.user_id
                                , fs_lead_enriched.user_id) = salesforce_user.id
                    where True
                        -- keep only those 90 days from change dates
                        and (       

                                    -- keep suspects and prospects created within last 90 days
                                    (fs_lead_enriched.status in (
                                            'Suspect', 'Suspect Future Follow Up', 'Prospect Qualified')
                                    and fs_lead_enriched.creation_datetime >=
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )
                            or
                                    -- keep Max Attempt disqualified leads within last 90 days
                                    (fs_lead_enriched.status in (
                                            'Disqualify Suspect', 'Disqualified Suspect'
                                            , 'Disqualify Prospect', 'Disqualified')
                                    and disqualification_reason = 'Max no. of attempts reached'
                                    and coalesce(
                                        fs_lead_enriched.disqualify_prospect_date, 
                                        fs_lead_enriched.disqualify_suspect_date) >= 
                                                (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )
                            or      
                                    -- keep opportunities before onboarded created within last 90 days
                                    (fs_opportunity_enriched.stage in (
                                            'New', 'Proposal Submitted', 'Negotiation'
                                            , 'Contract Sent', 'Agreed to Ship')
                                    and fs_opportunity_enriched.creation_datetime >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )
                            or      
                                    -- keep opportunities onboarded within last 90 days
                                    (fs_opportunity_enriched.stage in ('Onboarding')
                                    and fs_opportunity_enriched.onboarded_date >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )
                            or
                                    -- keep ready to ship leads created within last 90 days
                                    (fs_opportunity_enriched.stage in ('Ready to Ship')
                                    and fs_opportunity_enriched.ready_to_ship_date >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )
                            or
                                    -- keep close won created within last 90 days
                                    (fs_opportunity_enriched.stage in ('Closed Won')
                                    and fs_opportunity_enriched.won_date >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )

                            or      -- keep close lost created within last 90 days
                                    (fs_opportunity_enriched.stage in ('Closed Lost')
                                    and fs_opportunity_enriched.lost_date >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )

                            or      -- keep future opportunity created within last 90 days
                                    (fs_opportunity_enriched.stage in ('Future Opportunity')
                                    and fs_opportunity_enriched.future_opportunity_date >= 
                                            (date('{{ measurement_datetime_utc }}') - interval '90' day)
                                    )

                            )
                        -- remove test accounts
                        and coalesce(fs_account_enriched.name
                                , fs_opportunity_enriched.opportunity_name
                                , fs_lead_enriched.name) not like '%Testing Soscom%'
                    )

                    select
                        measurement_datetime
                        , lead_id
                        , opportunity_id
                        , account_id
                        , shipper_id
                        , shipper_name
                        , email
                        , sales_team
                        , salesperson_name
                        , salesperson_email
                        , salesperson_phone
                        , case
                            when lifecycle in ('Suspect', 'Suspect Future Follow Up') then 'Suspect'
                            when lifecycle in ('Prospect Qualified') then 'Prospect'
                            when lifecycle in ('Disqualify Suspect', 'Disqualified Suspect'
                                    , 'Disqualify Prospect', 'Disqualified') then 'Disqualified'
                            when lifecycle in ('New') then 'New Opportunity'                            
                        else lifecycle end as lifecycle
                        , expected_volume_monthly
                        , lead_creation_datetime
                        , disqualified_date
                        , opportunity_creation_datetime
                        , onboarded_date
                        , ready_to_ship_date
                        , won_date
                        , lost_date
                        , future_opportunity_date
                        , first_order_completion_date
                        , first_order_placed_date
                        , last_order_placed_date
                        , disqualification_reason
                        , loss_reason
                        , lead_gen_channel
                        , lead_source
                        , lead_source_details
                        , system_id
                        , sales_channel
                    from lead_base
                    where True
                        -- keep only valid and clean lead & opp stages
                        and lifecycle in ('Suspect', 'Suspect Future Follow Up'
                                            , 'Disqualify Suspect', 'Disqualified Suspect'
                                            , 'Prospect Qualified', 'Disqualify Prospect'
                                            , 'Disqualified', 'New', 'Proposal Submitted'
                                            , 'Negotiation', 'Contract Sent', 'Agreed to Ship'
                                            , 'Onboarding', 'Ready to Ship', 'Closed Won'
                                            , 'Closed Lost', 'Future Opportunity')

                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),

            base.TransformView(
                view_name="sf_child_interim",
                jinja_template="""
                with 30d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_30d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '30' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , 90d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_90d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '90' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , latest_contact as (
                    select account_id
                        , max_by(id, coalesce(creation_datetime
                                , '{{ measurement_datetime_utc }}')) as contact_id
                        , max_by(phone, coalesce(creation_datetime
                                , '{{ measurement_datetime_utc }}')) as phone
                        , max_by(email, coalesce(creation_datetime
                                , '{{ measurement_datetime_utc }}')) as email
                    from salesforce_contact_enriched
                    group by 1
                )

                , sf_child_base as (
                    select '{{ measurement_datetime_utc }}' as measurement_datetime
                        , fs_account_enriched.id as account_id
                        , fs_account_enriched.parent_acc_id_coalesce
                        , fs_account_enriched.shipper_id
                        , fs_account_enriched.name as account_name
                        , fs_account_enriched.parent_acc_name_coalesce
                        , latest_contact.contact_id
                        , fs_account_enriched.sales_team
                        , fs_account_enriched.salesperson as salesperson_name
                        , salesforce_user.email as salesperson_email
                        , salesforce_user.phone as salesperson_phone
                        , coalesce(fs_account_enriched.system_id
                                , fs_shippers_enriched.system_id) as system_id
                        , latest_contact.email
                        , fs_account_enriched.sales_channel 
                        , fs_shippers_enriched.onboarded_date
                        , fs_shippers_enriched.first_order_completion_date
                        , fs_shippers_enriched.first_order_placed_date
                        , fs_shippers_enriched.last_order_placed_date
                        , fs_account_enriched.expected_monthly_vol as expected_volume_monthly
                        , 30d_tb.orders_30d
                        , 90d_tb.orders_90d
                    from fs_account_enriched
                    left join 90d_tb 
                        on fs_account_enriched.shipper_id = 90d_tb.shipper_id
                    left join 30d_tb  
                        on fs_account_enriched.shipper_id = 30d_tb.shipper_id
                    left join latest_contact
                        on fs_account_enriched.id = latest_contact.account_id
                    left join fs_shippers_enriched
                        on fs_account_enriched.shipper_id = fs_shippers_enriched.id
                    left join salesforce_user
                        on fs_account_enriched.user_id = salesforce_user.id
                    where fs_account_enriched.sales_channel = 'Field Sales'
                )

                select sf_child_base.*
                    , nr_base.nr_join_date
                    , nr_enriched.nr_redemption_count as nr_prev_day_redemption_count
                    , nr_base.nr_last_redemption_date
                    , nr_base.nr_last_used_reward_date
                    , nb_referral_count
                    , nb_last_referral_date
                from sf_child_base
                left join nr_base
                    on sf_child_base.account_id = nr_base.account_id
                left join nr_enriched
                    on date_trunc('day', sf_child_base.measurement_datetime) 
                            = nr_enriched.nr_redemption_date - interval '1' day
                    and sf_child_base.account_id = nr_enriched.account_id
                left join nb_enriched
                    on date_trunc('day', sf_child_base.measurement_datetime) 
                            = nb_enriched.nb_referral_date
                    and sf_child_base.account_id = nb_enriched.account_id
                left join last_referral_tb
                    on sf_child_base.account_id = last_referral_tb.account_id
                --left join last_redemption_tb
                    --on sf_child_base.account_id = last_redemption_tb.account_id

                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="sf_parent_interim",
                jinja_template="""
                with lifecycle_tb as (
                    select distinct sf_parent_acc_id_coalesce
                        , sf_parent_coalesce_category
                        , sf_parent_coalesce_ppd
                    from shipper_completion_vol_monthly
                    where shipper_completion_vol_monthly.completion_month 
                            = date_trunc('month', '{{ measurement_datetime_utc }}')
                        and sales_channel = 'Field Sales'
                )

                , sf_parent_attributes as (
                    select sf_child_interim.measurement_datetime
                        , sf_child_interim.parent_acc_id_coalesce
                        , sf_child_interim.parent_acc_name_coalesce
                        , min(sf_child_interim.first_order_completion_date) as sf_parent_first_order_completion_date
                        , min(sf_child_interim.first_order_placed_date) as sf_parent_first_order_placed_date
                        , max(sf_child_interim.last_order_placed_date) as sf_parent_last_order_placed_date
                        , min(sf_child_interim.onboarded_date) as sf_parent_onboarded_date
                        , sum(sf_child_interim.orders_30d) as orders_30d
                        , sum(sf_child_interim.orders_90d) as orders_90d
                        , sum(sf_child_interim.expected_volume_monthly) as expected_volume_monthly
                        , min_by(fs_lead_enriched.id, fs_lead_enriched.creation_datetime) as first_sf_lead_id
                        , min_by(fs_opportunity_enriched.id
                                , fs_opportunity_enriched.creation_datetime) as first_sf_opportunity_id
                        , min(nr_join_date) as nr_join_date
                        , sum(nr_prev_day_redemption_count) as nr_prev_day_redemption_count
                        , max(nr_last_redemption_date) as nr_last_redemption_date
                        , max(nr_last_used_reward_date) as nr_last_used_reward_date                        
                        , sum(nb_referral_count) as nb_referral_count
                        , max(nb_last_referral_date) as nb_last_referral_date

                    from sf_child_interim
                    left join fs_lead_enriched
                        on sf_child_interim.account_id = fs_lead_enriched.account_id
                    left join fs_opportunity_enriched
                        on fs_lead_enriched.opportunity_id = fs_opportunity_enriched.id
                    group by 1,2,3
                )

                select sf_parent_attributes.measurement_datetime
                    , sf_parent_attributes.first_sf_lead_id
                    , sf_parent_attributes.first_sf_opportunity_id
                    , sf_parent_attributes.parent_acc_id_coalesce
                    , sf_parent_attributes.parent_acc_name_coalesce
                    , sf_parent_attributes.sf_parent_first_order_completion_date
                    , sf_parent_attributes.sf_parent_first_order_placed_date
                    , sf_parent_attributes.sf_parent_last_order_placed_date
                    , sf_parent_attributes.sf_parent_onboarded_date
                    , sf_parent_attributes.orders_30d
                    , sf_parent_attributes.orders_90d
                    , sf_parent_attributes.expected_volume_monthly
                    , lifecycle_tb.sf_parent_coalesce_ppd
                    , field_sales_short_term_retention.churn_pred
                    , coalesce(lifecycle_tb.sf_parent_coalesce_category
                            , 'Lapsed') as sf_parent_coalesce_category
                    , sf_child_interim.shipper_id
                    , sf_child_interim.contact_id
                    , sf_child_interim.sales_team
                    , sf_child_interim.salesperson_name
                    , sf_child_interim.salesperson_email
                    , sf_child_interim.salesperson_phone
                    , fs_shippers_enriched.system_id
                    , sf_child_interim.email
                    , sf_child_interim.sales_channel
                    , fs_lead_enriched.creation_datetime as lead_creation_datetime
                    , fs_lead_enriched.disqualified_date
                    , fs_opportunity_enriched.creation_datetime as opportunity_creation_datetime
                    , fs_opportunity_enriched.ready_to_ship_date
                    , fs_opportunity_enriched.won_date
                    , fs_opportunity_enriched.lost_date
                    , fs_opportunity_enriched.future_opportunity_date
                    , fs_lead_enriched.disqualification_reason
                    , fs_opportunity_enriched.loss_reason
                    , fs_lead_enriched.lead_gen_channel
                    , fs_lead_enriched.lead_source
                    , fs_lead_enriched.lead_source_details
                    , sf_parent_attributes.nr_join_date
                    , sf_parent_attributes.nr_prev_day_redemption_count
                    , sf_parent_attributes.nr_last_redemption_date
                    , sf_parent_attributes.nr_last_used_reward_date
                    , sf_parent_attributes.nb_referral_count
                    , sf_parent_attributes.nb_last_referral_date
                from sf_parent_attributes
                left join sf_child_interim
                    -- only take child when sf_acc_id equal parent_acc_id
                    on sf_parent_attributes.parent_acc_id_coalesce = sf_child_interim.account_id
                left join field_sales_short_term_retention
                    on sf_parent_attributes.parent_acc_id_coalesce 
                            = field_sales_short_term_retention.sf_parent_acc_id_coalesce
                left join lifecycle_tb
                    on sf_parent_attributes.parent_acc_id_coalesce = lifecycle_tb.sf_parent_acc_id_coalesce
                left join fs_lead_enriched
                    on sf_parent_attributes.first_sf_lead_id = fs_lead_enriched.id
                left join fs_opportunity_enriched
                    on fs_lead_enriched.opportunity_id = fs_opportunity_enriched.id
                join fs_shippers_enriched
                    on sf_parent_attributes.parent_acc_id_coalesce = fs_shippers_enriched.sf_acc_id
                -- remove lapsed more than 3 months
                where lifecycle_tb.sf_parent_coalesce_category is not null or sf_parent_attributes.orders_90d > 0
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="union_table",
                jinja_template="""
                select 
                    measurement_datetime as snapshot_date
                    , lead_id as sf_lead_id
                    , opportunity_id as sf_opp_id
                    , account_id as sf_acc_id
                    , shipper_id
                    , null as sf_contact_id
                    , sales_team
                    , salesperson_name
                    , salesperson_email
                    , salesperson_phone
                    , shipper_name
                    , email
                    , lifecycle
                    , expected_volume_monthly
                    , lead_creation_datetime
                    , disqualified_date
                    , opportunity_creation_datetime
                    , onboarded_date
                    , ready_to_ship_date
                    , won_date
                    , lost_date
                    , future_opportunity_date
                    , first_order_completion_date
                    , first_order_placed_date
                    , last_order_placed_date
                    , 0 as orders_30d
                    , 0 as orders_90d
                    , 0 as ppd
                    , disqualification_reason
                    , loss_reason
                    , lead_gen_channel
                    , lead_source
                    , lead_source_details
                    , 0 as first_3m_churn_prediction
                    , null as nr_join_date
                    , 0 as nr_prev_day_redemption_count
                    , null as nr_last_redemption_date
                    , null as nr_last_used_reward_date
                    , 0 as nb_referral_count
                    , null as nb_last_referral_date
                    , system_id
                    , sales_channel
                from sf_lead_interim

                union

                select
                    measurement_datetime as snapshot_date
                    , first_sf_lead_id as sf_lead_id
                    , first_sf_opportunity_id as sf_opp_id
                    , parent_acc_id_coalesce as sf_acc_id
                    , shipper_id
                    , contact_id as sf_contact_id
                    , sales_team
                    , salesperson_name
                    , salesperson_email
                    , salesperson_phone
                    , parent_acc_name_coalesce as shipper_name
                    , email
                    , sf_parent_coalesce_category as lifecycle
                    , expected_volume_monthly
                    , lead_creation_datetime
                    , disqualified_date
                    , opportunity_creation_datetime
                    , sf_parent_onboarded_date as onboarded_date
                    , ready_to_ship_date
                    , won_date
                    , lost_date
                    , future_opportunity_date
                    , sf_parent_first_order_completion_date as first_order_completion_date
                    , sf_parent_first_order_placed_date as first_order_placed_date
                    , sf_parent_last_order_placed_date as last_order_placed_date
                    , orders_30d
                    , orders_90d
                    , sf_parent_coalesce_ppd as ppd
                    , disqualification_reason
                    , loss_reason
                    , lead_gen_channel
                    , lead_source
                    , lead_source_details
                    , churn_pred as first_3m_churn_prediction
                    , nr_join_date
                    , nr_prev_day_redemption_count
                    , nr_last_redemption_date
                    , nr_last_used_reward_date
                    , nb_referral_count
                    , nb_last_referral_date
                    , system_id
                    , sales_channel
                from sf_parent_interim
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="fs_segmentation_daily",
                jinja_template="""
                select 
                    date_format(
                        date_trunc('month', snapshot_date )
                        , "yyyy-MM-dd") as created_month
                    , snapshot_date
                    , sf_lead_id
                    , sf_opp_id
                    , sf_acc_id
                    , sf_contact_id
                    , shipper_id
                    , shipper_name
                    , email
                    , sales_team
                    , salesperson_name
                    , salesperson_email
                    , salesperson_phone
                    , case 
                        when lifecycle = 'Suspect' then '01. suspect'
                        when lifecycle = 'Prospect' then '02. prospect'
                        when lifecycle = 'Disqualified' then '03. disqualified'
                        when lifecycle = 'New Opportunity' then '04. new_opportunity'
                        when lifecycle = 'Proposal Submitted' then '05. proposal_submitted'
                        when lifecycle = 'Negotiation' then '06. negotiation'
                        when lifecycle = 'Agreed to Ship' then '07. agreed_to_ship'
                        when lifecycle = 'Contract Sent' then '08. contract_sent'
                        when lifecycle = 'Onboarding' then '09. onboarding'
                        when lifecycle = 'Ready to Ship' then '10. ready_to_ship'
                        when lifecycle = 'Closed Won' then '11. closed_won'
                        when lifecycle = 'Closed Lost' then '12. closed_lost'
                        when lifecycle = 'Future Opportunity' then '13. future_opportunity'
                        when lifecycle = 'New' then '14. new'
                        when lifecycle = 'Stable' then '15. stable'
                        when lifecycle = 'Uptrader' then '16. uptrader'
                        when lifecycle = 'Downtrader' then '17. downtrader'
                        when lifecycle = 'Lapsed' then '18. lapsed'
                        when lifecycle = 'Regained' then '19. regained'
                    else lifecycle end lifecycle
                    , expected_volume_monthly
                    , lead_creation_datetime
                    , disqualified_date
                    , opportunity_creation_datetime
                    , onboarded_date
                    , ready_to_ship_date
                    , won_date
                    , lost_date
                    , future_opportunity_date
                    , first_order_completion_date
                    , first_order_placed_date
                    , last_order_placed_date
                    , orders_30d
                    , orders_90d
                    , disqualification_reason
                    , loss_reason
                    , lead_gen_channel
                    , lead_source
                    , lead_source_details
                    , first_3m_churn_prediction
                    , nr_join_date
                    --, nr_prev_day_redemption_count
                    , nr_last_redemption_date
                    , nr_last_used_reward_date
                    , nb_referral_count
                    , nb_last_referral_date
                    , system_id
                    , sales_channel
                from union_table
                where sales_channel is not null
                    and system_id is not null
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FS_SEGMENTATION_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,)

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.autoBroadcastJoinThreshold", -1)
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()