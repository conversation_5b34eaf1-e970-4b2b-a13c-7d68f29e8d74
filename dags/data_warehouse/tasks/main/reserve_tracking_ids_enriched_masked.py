import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.OCreateProdGL(input_env, is_masked).RESERVE_TRACKING_IDS,
                view_name="reserve_tracking_ids",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    tracking_id
                    , request_payload
                    , system_id
                    , created_at
                    , row_number() over(partition by tracking_id, system_id order by created_at desc) as rank
                from reserve_tracking_ids
                where deleted_at is null
                and tracking_id is not null
                """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                    select
                        tracking_id
                        , if(get_json_object(
                                request_payload, '$.parcel_job.dimensions'
                            ) is not null, 1, 0) as dimensions_flag

                        {%- for column in dim_columns %}

                        , get_json_object(
                            request_payload, '$.parcel_job.dimensions.{{ column }}'
                        ) as {{ column }}

                        {%- endfor %}

                        , if(get_json_object(
                                request_payload, '$.parcel_job.is_pickup_required'
                            ) = 'true', 1, 0) as is_pickup_required
                        , date(
                            get_json_object(request_payload, '$.parcel_job.pickup_date')
                        ) as scheduled_pickup_date
                        , date(
                            get_json_object(request_payload, '$.parcel_job.delivery_start_date')
                        ) as delivery_start_date
                        , get_json_object(
                            request_payload, '$.parcel_job.delivery_timeslot.start_time'
                        ) as delivery_start_time
                        , get_json_object(
                            request_payload, '$.parcel_job.delivery_timeslot.end_time'
                        ) as delivery_end_time
                        , if(get_json_object(
                                request_payload, '$.parcel_job.allow_self_collection'
                            ) = 'true', 1, 0) as allow_self_collection
                        , lower(system_id) as system_id
                        , date_format(
                            from_utc_timestamp(created_at, {{ get_local_timezone }}), 'yyyy-MM'
                        ) as created_month
                    from base
                    where rank = 1
                    """,
                jinja_arguments={
                    "dim_columns": ("size", "weight", "length", "width", "height"),
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVE_TRACKING_IDS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=(
            "system_id",
            "created_month",
        ),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
