import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.WMS_PARCELS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.WMS_PARCELS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 12)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.WmsProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="fetch_original_tid",
                jinja_template="""
                select
                    parent_0.tid
                    , coalesce(
                    {%- for i in range(1, relabel_loop + 1)|reverse %}
                        parent_{{ i }}.tid{% if not loop.last %}, {% endif %}
                    {%- endfor %}
                    ) as original_tid
                from parcels as parent_0
                {%- for i in range(1, relabel_loop + 1) %}
                left join parcels as parent_{{ i }}
                    on parent_{{ i-1 }}.tid = parent_{{ i }}.relabel_tid
                    and parent_{{ i }}.relabel_tid is not null
                    and parent_{{ i }}.relabel_tid != ''
                {%- endfor %}
                """,
                jinja_arguments={
                    "relabel_loop":20
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    parcels.tid as parcel_tracking_id
                    , parcels.status
                    , parcels.relabel_tid
                    , parcels.relabel_attempts
                    , original.original_tid as original_tracking_id
                    , parcels.auto_dispose
                    , parcels.num_days_free_storage
                    , parcels.pick_action
                    , if(lower(parcels.system_id) in ('th', 'vn', 'id')
                        , parcels.created_at + interval '7' hour
                        , parcels.created_at + interval '8' hour
                    ) as creation_datetime
                {%- for col_name in timestamp_columns %}
                    , if(lower(parcels.system_id) in ('th', 'vn', 'id')
                        , parcels.{{ col_name }}_timestamp + interval '7' hour
                        , parcels.{{ col_name }}_timestamp + interval '8' hour
                    ) as {{ col_name }}_datetime
                {%- endfor %}
                    , if(lower(parcels.system_id) in ('th', 'vn', 'id')
                        , parcels.deleted_at + interval '7' hour
                        , parcels.deleted_at + interval '8' hour
                    ) as deletion_datetime
                    , lower(parcels.system_id) as system_id
                    , date_format(parcels.created_at, 'yyyy-MM') as created_month
                from parcels
                left join fetch_original_tid as original
                    on parcels.tid = original.tid
                """,
                jinja_arguments={
                    "timestamp_columns":(
                        "asn_uploaded",
                        "auto_dispose",
                        "putaway",
                        "picklist_uploaded",
                        "pending_pick",
                        "pick",
                        "pack",
                        "storage_end",
                    )
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).WMS_PARCELS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
