import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.SALESFORCE_SHIPPER_EXPORT_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.SALESFORCE_SHIPPER_EXPORT_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
    ),
    # DWH service queries tables in data_warehouse schema using Presto.
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=tuple()),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIELD_SALES_SHORT_TERM_RETENTION,
                view_name="field_sales_short_term_retention",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipper_ops_metrics",
                jinja_template="""
                    with
                        ordered_pets_tickets as (
                            select
                                pets.id
                                , pets.country
                                , se.id as shipper_id
                                , pets.order_id
                                , pets.type
                                , pets.outcome
                                , pets.resolution_datetime
                                , pets.days_to_resolution
                                , row_number() over (partition by pets.order_id, pets.type order by pets.id desc) as
                                ticket_order
                            from pets_tickets_enriched pets
                            join order_milestones om on
                                pets.order_id = om.order_id
                                and pets.system_id = om.system_id
                                and om.created_month >= date_format(date_trunc('month', current_date) -
                                interval 3 month,'yyyy-MM')
                            join shippers_enriched se on
                                om.shipper_id = se.id
                                and om.system_id = se.system_id
                            where
                                pets.created_month >= date_format(date_trunc('month', current_date) - interval 3 month,
                                'yyyy-MM')
                                and pets.system_id in ('sg', 'my', 'th', 'ph', 'vn', 'id')
                                and pets.type in ('DAMAGED', 'MISSING')
                                and pets.status = 'RESOLVED'
                        ),

                        damaged_missing as (

                            select
                                shipper_id
                                , date_trunc('month', resolution_datetime) as resolution_month
                                , sum(case when type = 'MISSING' then 1 else 0 end) as missing_orders
                                , sum(case when type = 'DAMAGED' then 1 else 0 end) as damaged_orders
                            from ordered_pets_tickets
                            where
                                ticket_order = 1
                            group by 1, 2

                        )

                        , sla_sc as (
                            select
                                shipper_id
                                , date_trunc('month', sla_date) as sla_month
                                , sum(if(first_valid_delivery_attempt_datetime = delivery_success_datetime, 1, 0)) as
                                 first_delivery_attempt_success
                                , sum(sc_met) as sc_met
                                , sum(sc_measured) as sc_measured

                            from transit_time_report
                            where
                                system_id in ('sg', 'my', 'th', 'ph', 'vn', 'id')
                                and created_month >= date_format(date_trunc('month', current_date) - interval 2 month,
                                'yyyy-MM')
                                and sc_measured = 1
                            group by 1, 2
                        )

                        , orders_completed_rts as (

                            select
                                completion_month
                                , sf_parent_acc_id_coalesce
                                , shipper_id
                                , sum(total_orders) as total_orders_completed
                                , sum(rts_orders) as total_rts_orders_completed

                            from shipper_completion_vol_monthly vol
                            where created_month >= date_format(date_trunc('month', current_date) -
                                interval 1 month, 'yyyy-MM')
                            group by 1,2,3
                            )

                        , pickup as (

                            select
                                se.id as shipper_id
                                , date_trunc('month', creation_datetime) as reservation_creation_month
                                , sum(case when status IN ('Success','COMPLETED')
                                    then 1 else 0 end) as successful_reservations
                                , count(*) as total_reservations
                                , sum(case when status IN ('Fail','FAILED') then 1 else 0 end) as missed_pickup
                                , sum(on_time_flag) as total_on_time_flag
                                , sum(attempted_flag) as total_attempted_flag
                            from reservations_enriched re
                            join shippers_enriched se on
                                re.shipper_id = se.id
                                and re.system_id = se.system_id
                            where
                                --- remove reservations with status = Cancel for more accurate measurement
                                re.created_month >= date_format(date_trunc('month', current_date) - interval 1 month,
                                'yyyy-MM')
                                and status <> 'Cancel'
                            group by 1, 2

                        )

                        select
                            oc. sf_parent_acc_id_coalesce

                            , sum(if(reservation_creation_month = date_trunc('month', current_date),
                                    successful_reservations,
                                    0))
                             as cm_success_pickup
                            , sum(if(reservation_creation_month = date_trunc('month', current_date) - interval 1 month,
                                    successful_reservations,
                                    0))
                             as lm_success_pickup

                            , sum(if(sla_month = date_trunc('month', current_date), sc_met, 0)) * 1.00 /
                            nullif(sum(if(sla_month = date_trunc('month', current_date), sc_measured, 0)),0)
                            as cm_sla_rate

                            , sum(if(sla_month = date_trunc('month', current_date) - interval 1 month, sc_met, 0))
                            * 1.00 / nullif(sum(if(sla_month = date_trunc('month', current_date) -
                            interval 1 month, sc_measured, 0)),0) as lm_sla_rate

                            , sum(if(completion_month = date_trunc('month', current_date),
                                    total_rts_orders_completed,
                                    0))
                                as cm_rts_orders
                            , sum(if(completion_month = date_trunc('month', current_date) - interval 1 month,
                                    total_rts_orders_completed,
                                    0))
                                    as lm_rts_orders

                            , sum(if(resolution_month = date_trunc('month', current_date), missing_orders, 0))
                            as cm_missing_orders
                            , sum(if(resolution_month = date_trunc('month', current_date) - interval 1 month,
                            missing_orders, 0)) as lm_missing_orders



                            , sum(if(resolution_month = date_trunc('month', current_date), damaged_orders, 0))
                            as cm_damaged_orders
                            , sum(if(resolution_month = date_trunc('month', current_date) - interval 1 month,
                            damaged_orders, 0)) as lm_damaged_orders

                            , sum(if(sla_month = date_trunc('month', current_date),
                            first_delivery_attempt_success, 0)) as cm_first_attempt_success
                            , sum(if(sla_month = date_trunc('month', current_date) - interval 1 month,
                            first_delivery_attempt_success, 0)) as lm_first_attempt_success

                            , sum(if(completion_month = date_trunc('month', current_date),
                            total_orders_completed, 0)) as cm_total_orders
                            , sum(if(completion_month = date_trunc('month', current_date) - interval 1 month,
                            total_orders_completed, 0)) as lm_total_orders

                        from orders_completed_rts oc
                        left join pickup p
                            on p.shipper_id = oc.shipper_id
                            and p.reservation_creation_month = oc.completion_month
                        left join damaged_missing dm
                            on dm.shipper_id  = oc.shipper_id
                            and dm.resolution_month = oc.completion_month
                        left join sla_sc sla
                            on sla.shipper_id = oc.shipper_id
                            and sla.sla_month = oc.completion_month
                        group by 1
                    """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="shippers",
                jinja_template="""
                with
                    base as (

                        select
                            sf_parent_acc_id_coalesce
                            , completion_month
                            , sf_parent_coalesce_category
                            , sf_parent_coalesce_total_orders
                            , sf_parent_coalesce_total_orders_lag_1m
                            , row_number() over (
                                partition by sf_parent_acc_id_coalesce order by completion_month desc
                            ) as order_date_num

                        from shipper_completion_vol_monthly
                        where sf_parent_acc_id_coalesce is not null

                   )
                    , cal_table as (

                        select
                            sf_parent_acc_id_coalesce
                            , max(if(order_date_num = 1, completion_month, null)) as latest_completion_month
                            , max(if(order_date_num = 2, completion_month, null)) as previous_completion_month
                            , max(
                                if(order_date_num = 2, sf_parent_coalesce_category, null)
                            ) as sf_parent_coalesce_category_lag_1m
                            , max(
                                if(order_date_num = 1, sf_parent_coalesce_total_orders, null)
                            ) as sf_parent_coalesce_total_orders
                            , max(
                                if(order_date_num = 1, sf_parent_coalesce_total_orders_lag_1m, null)
                            ) as sf_parent_coalesce_total_orders_lag_1m
                        from base
                        where order_date_num <=2
                        group by 1

                   )

                   , shipper_attributes_filtered as (

                       select
                            sf_parent_acc_id_coalesce
                            , min(onboarded_date) as onboarded_date
                        from shipper_attributes
                        where sf_parent_acc_id_coalesce is not null
                        group by 1

                   )

                   -- fs short term churn prediction
                   ,short_term_churn_base as (

                       select
                             sf_parent_acc_id_coalesce
                           , cohort_month + interval 3 month as predicted_month
                           , churn_pred
                           , row_number() over(partition by sf_parent_acc_id_coalesce
                           order by measurement_datetime desc) as measurement_datetime_rownum
                       from field_sales_short_term_retention

                   )

                   , short_term_churn as (
                       select
                             sf_parent_acc_id_coalesce
                           , predicted_month
                           , churn_pred

                       from short_term_churn_base
                       where measurement_datetime_rownum = 1
                   )

               -- 'Inactive': shippers who do not have parcel volumes for 4 straight months
               -- 'Dormant': newly onboarded shippers who still do not have any parcel volume for next 3months or more.
                select
                    base.sf_parent_acc_id_coalesce
                    , case
                        when months_between(
                            date_trunc('month', '{{ measurement_datetime }}'), latest_completion_month
                        ) >= 4 then 'Inactive'
                        when months_between(
                            date_trunc('month', '{{ measurement_datetime }}'), latest_completion_month
                        ) >= 1 then 'Lapsed'
                        when latest_completion_month is null
                            and months_between(
                                date_trunc('month', '{{ measurement_datetime }}'), onboarded_date
                            ) >=3 then 'Dormant'
                        else sf_parent_coalesce_category_lag_1m
                    end as sf_parent_coalesce_category_lag_1m
                    , if(
                        date_trunc('month', '{{ measurement_datetime }}') == latest_completion_month
                        , sf_parent_coalesce_total_orders_lag_1m
                        , 0
                    ) as sf_parent_coalesce_total_orders_lag_1m
                    , if(
                        date_trunc('month', '{{ measurement_datetime }}') == latest_completion_month
                        , sf_parent_coalesce_total_orders
                        , 0
                    ) as sf_parent_coalesce_total_orders_mtd
                    , cm_success_pickup
                    , lm_success_pickup
                    , cast(cm_sla_rate as double) as cm_sla_rate
                    , cast(lm_sla_rate as double) as lm_sla_rate
                    , cm_rts_orders / nullif(cm_total_orders, 0) as cm_rts_rate
                    , lm_rts_orders / nullif(lm_total_orders, 0) as lm_rts_rate
                    , cm_missing_orders
                    , lm_missing_orders
                    , cm_damaged_orders
                    , lm_damaged_orders
                    , cm_first_attempt_success
                    , lm_first_attempt_success
                    , cm_total_orders
                    , lm_total_orders
                    , stc.predicted_month
                    , stc.churn_pred
                from shipper_attributes_filtered as base
                left join cal_table on
                    base.sf_parent_acc_id_coalesce = cal_table.sf_parent_acc_id_coalesce
                left join short_term_churn stc
                   on stc.sf_parent_acc_id_coalesce = base.sf_parent_acc_id_coalesce
               left join shipper_ops_metrics ops
                   on ops.sf_parent_acc_id_coalesce = base.sf_parent_acc_id_coalesce
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select distinct * from shippers
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path= versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_SHIPPER_EXPORT,
        measurement_datetime=measurement_datetime,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()