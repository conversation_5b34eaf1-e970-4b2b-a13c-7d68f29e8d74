import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_FULL_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_FULL_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 4, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_MILESTONES,
                view_name="shipper_milestones",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    date_ranges as (

                        select
                            shipper_id
                            , system_id
                            , first_order_completion_date
                            , date(
                                from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }})
                            ) - interval 1 day as end_date
                        from shipper_milestones

                    )
                    , all_dates as (

                        select
                            *
                            , explode(
                                sequence(first_order_completion_date, end_date, interval 1 day)
                            ) as completion_date
                        from date_ranges
                        where
                            first_order_completion_date <= end_date

                    )
                    , final as (

                        select
                            all_dates.completion_date
                            , all_dates.shipper_id
                            , coalesce(shipper_completion_vol_daily.total_orders, 0) as total_orders
                            , coalesce(shipper_completion_vol_daily.rts_orders, 0) as rts_orders
                            , coalesce(shipper_completion_vol_daily.cod_orders, 0) as cod_orders
                            , coalesce(shipper_completion_vol_daily.non_rts_cod_orders, 0) as non_rts_cod_orders
                            , coalesce(shipper_completion_vol_daily.ninjapack_orders, 0) as ninjapack_orders
                            , coalesce(shipper_completion_vol_daily.xb_outbound_orders, 0) as xb_outbound_orders
                            , coalesce(shipper_completion_vol_daily.total_delivery_fee, 0) as total_delivery_fee
                            , coalesce(shipper_completion_vol_daily.total_cod_value, 0) as total_cod_value
                            , all_dates.system_id
                            , date_format(all_dates.completion_date, 'yyyy-MM') as created_month
                        from all_dates
                        left join shipper_completion_vol_daily on
                            all_dates.completion_date = shipper_completion_vol_daily.completion_date
                            and all_dates.shipper_id = shipper_completion_vol_daily.shipper_id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_COMPLETION_VOL_DAILY_FULL,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
