import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.CISP_REPORT_BASE_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_REPORT_BASE,
                view_name="cisp_report_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    final as (
                        select
                            base.order_id
                            , base.dest_hub_id
                            , base.dest_hub_name
                            , base.dest_hub_region
                            , base.dest_hub_facility_type
                            , base.dest_hub_datetime
                            , base.dest_zone
                            , base.last_mile_start_clock_date
                            , base.start_clock_date
                            , if(base.cod_id is null, 0, 1) as cod_flag
                            , base.working_day
                            , base.next_working_date

                            {%- for day in range(7) %}

                            , base.n{{ day }}_cutoff_date
                            , if(
                                base.last_valid_delivery_attempt_status = 'Success'
                                and date(base.last_valid_delivery_attempt_datetime) <= n{{ day }}_cutoff_date
                                , 1
                                , 0
                            ) as n{{ day }}_met_flag

                            {%- endfor %}

                            , shippers.id as shipper_id
                            , shippers.shipper_name
                            , shippers.parent_name as parent_shipper_name
                            , shippers.parent_name_coalesce as parent_shipper_name_coalesce
                            , base.last_valid_delivery_attempt_datetime
                            , base.last_valid_delivery_attempt_status
                            , base.delivery_success_datetime
                            , base.rts_trigger_datetime
                            , base.delivery_attempts
                            , base.rts_flag
                            , base.created_month
                        from cisp_report_base as base
                        left join shippers_enriched as shippers on
                            base.shipper_id = shippers.id
                    )
            select
                *
                {%- for day in range(7) %}

                , if(
                    n{{ day }}_cutoff_date <= date('{{ measurement_datetime }}')
                    or n{{ day }}_met_flag = 1
                    , 1
                    , 0
                ) as n{{ day }}_measured

                {%- endfor %}
            from final
            """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_COMPLETION_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()