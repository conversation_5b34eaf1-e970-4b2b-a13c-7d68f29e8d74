import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SHOPIFY_ORDERS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SHOPIFY_ORDERS_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LNK_ORDERS_SHIPPERS,
                view_name="lnk_orders_shippers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.EPIProdGL(input_env, is_masked).EXTERNAL_LINKED_ACCOUNTS, view_name="external_linked_accounts"),
            base.InputTable(
                path=delta_tables.EPIProdGL(input_env, is_masked).EXTERNAL_LINKED_ORDERS, view_name="external_linked_orders"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shopify_orders",
                jinja_template="""
                                with 
                
                    shopify_v1 as (
                        
                        SELECT
                            ex_acc.external_account_id
                            , shippers.id as shipper_id
                        from external_linked_accounts ex_acc
                        left join shipper_attributes shippers on
                            ex_acc.shipper_id = shippers.id
                        where 
                            external_account_type = 'SHOPIFY'
                            -- Filter out test accounts
                            and shippers.business_unit != 'Test'
                            and shippers.acquisition_endpoint != 'Test'
                            and ex_acc.shipper_id is not null
                            and ex_acc.id != 3 
                            and ex_acc.deleted_at is null
                             
                    )
                    
                    , shopify_v1_orders as (
                         
                        select
                             v1.shipper_id
                            , tracking_number as tracking_id 
                            , ex_orders.created_at as creation_datetime
                            , 'v1' as flag
                            , ex_orders.created_month
                         from shopify_v1 v1
                         join external_linked_orders ex_orders on 
                             v1.external_account_id = ex_orders.external_account_id
                         where 
                            ex_orders.deleted_at is null
                            and last_creation_status = 'SUCCESSFUL'
                        
                        )
                    
                    , shopify_v2 as (
                    
                        SELECT
                             orders_shippers.shipper_id
                            , orders.tracking_id
                            , orders.creation_datetime
                            , 'v2' as flag
                            , orders.created_month
                        from orders_enriched orders
                        left join lnk_orders_shippers orders_shippers on orders_shippers.order_id = orders.order_id
                                and orders_shippers.system_id = orders.system_id
                        where 
                            granular_status = 'Completed' 
                            and (source_id = 10 or shipper_reference_number like '%shopify%')
                    )
                    
                    SELECT 
                            * 
                        from (
                                (
                                    select 
                                        * 
                                    from shopify_v1_orders
                                )
                                union all 
                                ( 
                                    select 
                                        * 
                                    from shopify_v2
                                )
                            )
                        order by shipper_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHOPIFY_ORDERS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
