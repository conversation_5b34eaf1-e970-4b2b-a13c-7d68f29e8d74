import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCEL_ITEMS,
                view_name="parcel_items",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).AUDIT_LOGS,
                view_name="audit_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).INTERNAL_STATUSES,
                view_name="internal_statuses",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="parcel_items_aggregation",
                jinja_template="""
                select
                    parcel_id
                    , count(1) as total_parcel_items
                    , collect_list(
                        get_json_object(metadata, '$.description')
                    ) as item_description_list
                    , collect_list(
                        get_json_object(metadata, '$.native_description')
                    ) as item_native_description_list
                    , sum(cast(get_json_object(metadata, '$.quantity') as int)) as total_quantity
                    , sum(
                        cast(get_json_object(metadata, '$.quantity') as int) 
                        * cast(get_json_object(metadata, '$.unit_value') as double)
                    ) as total_value
                from parcel_items
                group by 1
                """,
            ),
            base.TransformView(
                view_name="total_mmcc_parcels",
                jinja_template="""
                select
                    source_order_id
                    , partner_id
                    , count_if(current_internal_status_id not in (29, 32, 33, 41)) as total_parcel_items
                    , count_if(current_internal_status_id in (29, 32, 33, 41)) as total_exception_parcel_items
                from parcels
                where 
                    type = 4
                    and source_order_id is not null
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="first_audit_log",
                jinja_template="""
                with get_original_value as (
            
                    select
                        id
                        , object_id
                        , cast(get_json_object(origin_state, '$.service_id') as bigint) as original_service_id
                    from audit_logs
                
                )

                select
                    object_id as tracking_id
                    , min_by(original_service_id, id) original_service_id
                from get_original_value
                where
                    original_service_id is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    parcels.id as parcel_id
                    , parcels.tracking_id
                    , parcels.source_order_id as shipper_reference_number
                    , parcels.ref_tracking_id
                    , case parcels.type
                {%- for type, name in parcel_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                {%- endfor %}
                        else 'Others'
                    end as parcel_type
                    , case parcels.source
                {%- for source, name in parcel_source_to_name.items() %}
                        when {{ source }} then '{{ name }}'
                {%- endfor %}
                        else 'Others'
                    end as parcel_creation_source
                    , parcels.product_id
                    , coalesce(first_audit_log.original_service_id, parcels.service_id) as original_service_id
                    , parcels.service_id
                    , parcels.shipper_id
                    , parcels.from_country_code as from_country
                    , parcels.to_country_code as to_country
                    , cast(
                        get_json_object(parcels.metadata, '$.manifest_weight') as double
                    ) as shipment_parcel_manifest_weight
                    , cast(
                        get_json_object(parcels.metadata, '$.shipper_submitted_weight') as double
                    ) as shipper_submitted_weight
                {%- for dimension in ('length', 'width', 'height') %}
                    , cast(
                        get_json_object(parcels.metadata, '$.shipper_submitted_dimensions.{{ dimension }}')
                        as double
                    ) as shipper_submitted_{{ dimension }}
                {%- endfor %}
                    , get_json_object(
                        parcels.metadata, '$.shipper_submitted_dimensions.unit'
                    ) as shipper_submitted_dimension_unit
                    , parcel_items.total_quantity as goods_quantity
                    , cast(case
                        when type = 3 then parcel_items.total_parcel_items
                        when type = 5 then mmcc_parcels.total_parcel_items
                    end as bigint) as total_parcel_items
                    , cast(case
                        when type = 5 then mmcc_parcels.total_exception_parcel_items
                    end as int) as total_exception_parcel_items
                    , coalesce(
                        cast(get_json_object(parcels.metadata, '$.value') as double)
                        , cast(get_json_object(parcels.metadata, '$.goods_value') as double)
                        , parcel_items.total_value
                    ) as goods_value
                    , get_json_object(parcels.metadata, '$.customs_currency') as goods_currency
                    , coalesce(
                        nullif(get_json_object(parcels.metadata, '$.customs_description'), '')
                        , concat_ws(',', parcel_items.item_description_list)
                    ) as customs_description
                    , coalesce(
                        nullif(get_json_object(parcels.metadata, '$.customs_native_description'), '')
                        , concat_ws(',', parcel_items.item_native_description_list)
                    ) as customs_native_description
                    , parcels.to_city
                    , parcels.to_postcode
                    , internal_statuses.name as current_status
                    , get_json_object(parcels.metadata, '$.warehouse_code') as warehouse_code
                    , 'gl' as system_id
                    , date_format(parcels.created_at, 'yyyy-MM') as created_month
                from parcels
                left join parcel_items_aggregation as parcel_items
                    on parcels.id = parcel_items.parcel_id
                left join total_mmcc_parcels as mmcc_parcels
                    on parcels.source_order_id = mmcc_parcels.source_order_id
                    and parcels.partner_id = mmcc_parcels.partner_id
                left join first_audit_log
                    on parcels.tracking_id = first_audit_log.tracking_id
                left join internal_statuses
                    on parcels.current_internal_status_id = internal_statuses.id
                """,
                jinja_arguments={
                    "parcel_type_to_name":{
                        1:"Parcel",
                        2:"B2B Bag",
                        3:"B2C Bag",
                        4:"MMCC Parcel",
                        5:"B2C Bag",
                    },
                    "parcel_source_to_name":{
                        0:"Unknown",
                        1:"Shipment Module",
                        2:"Core Consumer v4.1",
                        3:"Regular API",
                        4:"Custom API",
                        5:"Internal API",
                        6:"EPI Consumer",
                        7:"Dash KeyBoard",
                        8:"Dash CSV",
                        9:"EPI",
                        10:"Core Consumer v4.2",
                    },
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_PARCELS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
