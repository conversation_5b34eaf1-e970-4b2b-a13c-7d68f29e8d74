import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).CONTACT, view_name="contact"),
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).ACCOUNT_CONTACT_RELATION, view_name="account_contact_relation"
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_RECORD_TYPE_ENRICHED,
                view_name="salesforce_record_type_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="account_contact_relation_filtered",
                jinja_template="""
                    select
                        contact_id
                        , max_by(cast(is_active as int), last_modified_date) as is_active
                        , max_by(is_deleted, last_modified_date) as is_deleted
                        , max_by(is_direct, last_modified_date) as is_direct
                    from account_contact_relation
                    group by contact_id
                    """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    contact.id
                    , contact.owner_id as user_id
                    , contact.account_id
                    , contact.primary_contact_c as primary_contact
                    , contact.salutation
                    , contact.first_name
                    , contact.last_name
                    , contact.email
                    , contact.phone
                    , cast(contact.ninja_chat_subscriber_c as int) as ninja_chat_subscriber
                    , contact.subscription_link_c as subscription_link
                    , contact.subscribed_platform_c as subscribed_platform
                    , cast(contact.sns_group_id_c as int) as sns_group_id
                    , contact.mailing_street
                    , contact.mailing_city
                    , contact.mailing_state
                    , contact.mailing_postal_code
                    , contact.mailing_country
                    , account_contact_relation_filtered.is_active
                    , from_utc_timestamp(contact.created_date, {{ get_local_timezone }}) as creation_datetime
                    , contact.created_by_id
                    , record_type.system_id as country
                    , record_type.system_id
                    , date_format(
                        from_utc_timestamp(contact.created_date, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                from contact
                left join account_contact_relation_filtered on
                    contact.id = account_contact_relation_filtered.contact_id
                left join salesforce_record_type_enriched as record_type on
                    contact.record_type_id = record_type.id
                where
                    contact.is_deleted = false
                    and account_contact_relation_filtered.is_deleted = false
                    and account_contact_relation_filtered.is_direct = true
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("record_type.system_id"),
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_CONTACT_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
