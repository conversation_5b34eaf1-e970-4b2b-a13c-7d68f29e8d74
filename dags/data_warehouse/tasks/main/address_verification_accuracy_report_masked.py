import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, 
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, 
            task_id=data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

# in kilometers
EARTH_RADIUS = 6371


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS,
                view_name="update_address_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS,
                view_name="update_address_verification_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PROOFS_ENRICHED,
                view_name="proofs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="update_address_verification_events_filtered",
                jinja_template="""
                select
                    order_id
                    , user_id
                    , av_status
                    , av_source
                    , av_mode
                    , av_zone_id
                    , av_hub_id
                    , av_latitude
                    , av_longitude
                    , min(order_event_id) as order_event_id
                    , min(event_datetime) as event_datetime
                    , min(event_creation_datetime) as event_creation_datetime
                from update_address_verification_events
                where av_status = 'VERIFIED'
                group by {{ range(1, 10) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="non_rts",
                jinja_template="""
                select
                    order_milestones.order_id
                    , order_milestones.shipper_id
                    , order_milestones.to_city
                    , order_milestones.to_address1
                    , order_milestones.to_address2
                    , order_milestones.delivery_success_hub_id as delivery_hub_id
                    , order_milestones.delivery_success_datetime as transaction_datetime
                    , order_milestones.inbound_datetime as first_inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , 'non_rts' as av_case
                    , order_milestones.created_month
                    , max(update_address_events.event_datetime) as last_address_change_datetime
                from order_milestones
                left join update_address_events on
                    order_milestones.order_id = update_address_events.order_id
                    and order_milestones.inbound_datetime > update_address_events.event_datetime
                where order_milestones.status = 'Completed'
                    and order_milestones.granular_status = 'Completed'
                    and order_milestones.force_success_flag = 0
                group by {{ range(1, 12) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="rts_forward",
                jinja_template="""
                select
                    order_milestones.order_id
                    , order_milestones.shipper_id
                    , order_milestones.to_city
                    , order_milestones.to_address1
                    , order_milestones.to_address2
                    , order_milestones.last_valid_delivery_attempt_hub_id as delivery_hub_id
                    , order_milestones.last_valid_delivery_attempt_datetime as transaction_datetime
                    , order_milestones.inbound_datetime as first_inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , 'rts_forward' as av_case
                    , order_milestones.created_month
                    , max(update_address_events.event_datetime) as last_address_change_datetime
                from order_milestones
                left join update_address_events on
                    order_milestones.order_id = update_address_events.order_id
                    and order_milestones.inbound_datetime > update_address_events.event_datetime
                    and order_milestones.rts_trigger_datetime >= update_address_events.event_datetime
                where order_milestones.status = 'Completed'
                    and order_milestones.granular_status = 'Returned to Sender'
                    and order_milestones.force_success_flag = 0
                group by {{ range(1, 12) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="all_scans",
                jinja_template="""
                select
                    order_id
                    , hub_id
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as scan_time
                from warehouse_sweeps

                union all

                 select
                    order_id
                    , hub_id
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as scan_time
                from inbound_scans
                where type = 2
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="first_scans",
                jinja_template="""
                select
                    all_scans.order_id
                    , coalesce(hubs_enriched.parent_hub_id, all_scans.hub_id) as hub_id
                    , min(all_scans.scan_time) as scan_time
                from all_scans
                left join hubs_enriched on
                    all_scans.hub_id = hubs_enriched.id
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="rts_return",
                jinja_template="""
                with base as (
                    select
                        order_milestones.order_id
                        , order_milestones.shipper_id
                        , order_milestones.to_city
                        , order_milestones.to_address1
                        , order_milestones.to_address2
                        , order_milestones.delivery_success_hub_id as delivery_hub_id
                        , order_milestones.delivery_success_datetime as transaction_datetime
                        , order_milestones.rts_trigger_datetime
                        , 'rts_return' as av_case
                        , order_milestones.created_month
                        , min(first_scans.scan_time) as first_inbound_datetime
                    from order_milestones
                    left join first_scans on
                        order_milestones.order_id = first_scans.order_id
                        and order_milestones.rts_trigger_datetime < first_scans.scan_time
                    where order_milestones.status = 'Completed'
                        and order_milestones.granular_status = 'Returned to Sender'
                        and order_milestones.force_success_flag = 0
                    group by {{ range(1, 11) | join(',') }}
                )
                select
                    base.order_id
                    , base.shipper_id
                    , base.to_city
                    , base.to_address1
                    , base.to_address2
                    , base.delivery_hub_id
                    , base.transaction_datetime
                    , base.rts_trigger_datetime
                    , base.av_case
                    , base.first_inbound_datetime
                    , base.created_month
                    , max(update_address_events.event_datetime) as last_address_change_datetime
                from base
                left join update_address_events on
                    base.order_id = update_address_events.order_id
                    and base.first_inbound_datetime > update_address_events.event_datetime
                    and base.rts_trigger_datetime <= update_address_events.event_datetime
                group by {{ range(1, 12) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="order_milestones_union",
                jinja_template="""
                with
                {% for case in av_cases %}
                    {%- if not loop.first %}, {% endif %}
                    {{ case }}_av_datetime as (
                        select
                            {{ case }}.order_id
                            , {{ case }}.shipper_id
                            , {{ case }}.to_city
                            , {{ case }}.to_address1
                            , {{ case }}.to_address2
                            , {{ case }}.delivery_hub_id
                            , {{ case }}.transaction_datetime
                            , {{ case }}.first_inbound_datetime
                            , {{ case }}.rts_trigger_datetime
                            , {{ case }}.last_address_change_datetime
                            , {{ case }}.av_case
                            , {{ case }}.created_month
                            , coalesce(max(oe_1.event_datetime), min(oe_2.event_datetime)) as av_datetime
                        from {{ case }}
                        left join update_address_verification_events_filtered as oe_1 on
                            {{ case }}.order_id = oe_1.order_id
                            and coalesce(
                                {{ case }}.last_address_change_datetime
                                , from_unixtime(0)
                            ) < oe_1.event_datetime
                            and {{ case }}.first_inbound_datetime > oe_1.event_datetime
                            {%- if case == "rts_forward" %}
                                and {{ case }}.rts_trigger_datetime > oe_1.event_datetime
                            {% endif %}
                            {%- if case == "rts_return" %}
                                and ({{ case }}.rts_trigger_datetime - interval 5 minutes) <= oe_1.event_datetime
                            {% endif %}
                        left join update_address_verification_events_filtered as oe_2
                            on {{ case }}.order_id = oe_2.order_id
                            and coalesce(
                                {{ case }}.last_address_change_datetime
                                , from_unixtime(0)
                            ) <= oe_2.event_datetime
                            {%- if case == "rts_forward" %}
                                and {{ case }}.rts_trigger_datetime > oe_2.event_datetime
                            {% endif %}
                            {%- if case == "rts_return" %}
                                and ({{ case }}.rts_trigger_datetime - interval 5 minutes) <= oe_2.event_datetime
                            {% endif %}
                        group by {{ range(1, 13) | join(',') }}
                    )
                {%- endfor %}
                {% for case in av_cases %}
                    select
                        *
                    from {{ case }}_av_datetime
                    {%- if not loop.last %} union all {% endif %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "av_cases": ["non_rts", "rts_forward", "rts_return"],
                },
            ),
            base.TransformView(
                view_name="max_proof_coordinates",
                jinja_template="""
                select
                    reference_id
                    , max_by(proofs_enriched.sign_coordinates_lat, proofs_enriched.proof_id) as delivery_latitude
                    , max_by(proofs_enriched.sign_coordinates_lng, proofs_enriched.proof_id) as delivery_longitude
                from proofs_enriched
                where proofs_enriched.reference = 'Transaction'
                group by 1
                """,
            ),
            base.TransformView(
                view_name="delivery",
                jinja_template="""
                select
                    order_milestones_union.*
                    , av_events.order_event_id as av_order_event_id
                    , av_events.av_status
                    , av_events.av_source
                    , av_events.av_mode
                    , av_events.av_zone_id
                    , av_events.av_hub_id
                    , av_events.av_latitude
                    , av_events.av_longitude
                    , transactions.id as transaction_id
                    , max_proof_coordinates.delivery_latitude
                    , max_proof_coordinates.delivery_longitude
                    , waypoints.latitude as waypoint_latitude
                    , waypoints.longitude as waypoint_longitude
                    , waypoints.routing_zone_id as waypoint_zone_id
                from order_milestones_union
                left join update_address_verification_events_filtered as av_events on
                    order_milestones_union.order_id = av_events.order_id
                    and order_milestones_union.av_datetime = av_events.event_datetime
                left join transactions
                    on order_milestones_union.order_id = transactions.order_id
                    and order_milestones_union.transaction_datetime =
                        from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')
                left join waypoints on
                    transactions.waypoint_id = waypoints.legacy_id
                    and waypoints.system_id = '{{ system_id }}'                    
                left join max_proof_coordinates on
                    transactions.id = max_proof_coordinates.reference_id
                where order_milestones_union.delivery_hub_id is not null
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (
                        select
                            delivery.*
                            , orders.to_state
                            , orders.to_district
                            , delivery.delivery_longitude
                            , delivery.delivery_latitude
                            , if(delivery.delivery_hub_id = delivery.av_hub_id, 1, 0) as hub_alignment_flag
                            , if(delivery.waypoint_zone_id = delivery.av_zone_id, 1, 0) as zone_alignment_flag
                        from delivery
                        left join orders on
                            delivery.order_id = orders.id
                    )
                    , cal_rad as (
                        select
                            *
                            , radians(delivery_latitude) as delivery_lat_rad
                            , radians(delivery_longitude) as delivery_long_rad
                            , radians(av_latitude) as av_lat_rad
                            , radians(av_longitude) as av_long_rad
                            , radians(waypoint_latitude) as waypoint_lat_rad
                            , radians(waypoint_longitude) as waypoint_long_rad
                        from base
                    )
                    , final as (
                        select
                            order_id
                            , shipper_id
                            , to_state
                            , to_city
                            , to_district
                            , to_address1
                            , to_address2
                            , delivery_hub_id
                            , transaction_datetime
                            , rts_trigger_datetime
                            , transaction_id
                            , av_case
                            , waypoint_latitude
                            , waypoint_longitude
                            , waypoint_zone_id
                            , delivery_longitude
                            , delivery_latitude
                            , av_datetime
                            , av_order_event_id
                            , av_status
                            , av_source
                            , av_mode
                            , av_zone_id
                            , av_hub_id
                            , av_latitude
                            , av_longitude
                            , hub_alignment_flag
                            , zone_alignment_flag
                            , {{ earth_radius }} * acos(
                                cos(delivery_lat_rad) * cos(av_lat_rad)
                                * cos(av_long_rad - delivery_long_rad)
                                + sin(delivery_lat_rad) * sin(av_lat_rad)
                            ) as av_delivery_distance
                            , {{ earth_radius }} * acos(
                                cos(delivery_lat_rad) * cos(waypoint_lat_rad)
                                * cos(waypoint_long_rad - delivery_long_rad)
                                + sin(delivery_lat_rad) * sin(waypoint_lat_rad)
                            ) as waypoint_delivery_distance
                            , created_month
                        from cal_rad
                    )
                select
                    *
                from final
                """,
                jinja_arguments={
                    "earth_radius": EARTH_RADIUS,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ADDRESS_VERIFICATION_ACCURACY_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()