import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_BASE_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_ORDERS_ENRICHED_BASE,
                view_name="shipment_orders_enriched_base",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.system_id AS country
                       , base.order_id
                       , base.shipment_id
                       , base.add_to_shipment_datetime
                       , base.last_add_to_shipment_datetime
                       , base.order_inbound_datetime
                       , base.del_from_shipment_datetime
                       , shipments.mawb
                       , shipments.orig_hub_id
                       , shipments.orig_hub_name
                       , shipments.orig_hub_region
                       , shipments.first_transit_hub_id
                       , shipments.first_transit_hub_name
                       , shipments.first_transit_hub_region
                       , shipments.second_transit_hub_id
                       , shipments.second_transit_hub_name
                       , shipments.second_transit_hub_region
                       , shipments.dest_hub_id
                       , shipments.dest_hub_name
                       , shipments.dest_hub_region
                       , shipments.shipment_type
                       , shipments.status
                       , shipments.curr_hub_id
                       , shipments.curr_hub_name
                       , shipments.curr_hub_region
                       , shipments.shipment_sla_datetime
                       , shipments.shipment_eta_datetime
                       , shipments.shipment_comments
                       , shipments.shipment_creation_datetime
                       , shipments.orig_shipment_close_datetime
                       , shipments.orig_shipment_van_inbound_datetime
                       , shipments.first_transit_shipment_hub_inbound_datetime
                       , shipments.first_transit_shipment_van_inbound_datetime
                       , shipments.second_transit_shipment_hub_inbound_datetime
                       , shipments.second_transit_shipment_van_inbound_datetime
                       , shipments.dest_shipment_hub_inbound_datetime
                       , shipments.shipment_force_success_datetime
                       , shipments.shipment_completion_datetime
                       , shipments.last_shipment_scan_datetime
                       , shipments.last_shipment_scan_hub_id
                       , shipments.last_shipment_scan_hub_name
                       , shipments.last_shipment_scan_user_coalesce
                       , shipments.shipment_cancellation_datetime
                       , base.system_id
                       , base.created_month
                FROM shipment_orders_enriched_base AS base
                LEFT JOIN shipments_enriched AS shipments ON base.shipment_id = shipments.shipment_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
