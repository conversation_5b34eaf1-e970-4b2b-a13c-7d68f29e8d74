import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from data_warehouse.tasks.main import base
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from datetime import datetime as dt
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_STATS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_STATS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_ENRICHED_MASKED,
        data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED,
        data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED_MASKED,
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse",
                                     partition_columns=("system_id", "shipper", "delivery_success_date")),
    ),
)


def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX,
                            view_name="prefix"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Data for one completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED + latest_partition) \
        .filter(F.col("delivery_success_date") == completion_date).createOrReplaceTempView("webhook_orders_enriched")

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED + latest_partition) \
        .filter(F.col("handed_over_to_last_mile_date") == completion_date).createOrReplaceTempView(
        "webhook_intermediate_orders_enriched")

    # Read mmcc webhook order tables for orders holm-ed from the previous month
    holm_month = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m')
    holm_month_dt = dt.strptime(holm_month, '%Y-%m').date()
    start_month_dt = holm_month_dt + relativedelta(months=-1)
    start_month = start_month_dt.strftime('%Y-%m')

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).MMCC_WEBHOOK_ORDERS_ENRICHED + latest_partition) \
        .filter(F.col("holm_month") >= start_month).createOrReplaceTempView("mmcc_webhook_orders_enriched")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="daily_stats",
                jinja_template="""
                -- Measures NV webhook status SLA performance (completeness/latency) for Tiktok and Cainiao Home Delivery
                with 
                    stats as (

                        select
                            delivery_success_date
                            , cast(status_code as int) as status_code
                            , status
                            , null as tt_action_code_mapping
                            , is_tokopedia
                            , null as od_pair
                            , shipper
                            , system_id
                            , latency_sla
                            , case 
                                when completeness_flag = 0 then null 
                                else count(case 
                                        when system_id = 'sg' and status_code = 13 then null 
                                        else tracking_id
                                     end)
                            end as total_parcels
                            , sum(webhook_attempted_flag) as webhook_attempt
                            , count_if(first_webhook_sent_time is not null) as webhook_sent
                            , count_if(latency_sla_met=1) as webhook_met_latency_sla
                            , coalesce(count_if(first_webhook_sent_time is not null)/sum(webhook_attempted_flag),0) as webhook_success_rate
                            , count_if(first_webhook_sent_time is not null)/ count(case 
                                                                                     when system_id = 'sg' and status_code = 13 then null 
                                                                                     when completeness_flag = 0 then null
                                                                                     else tracking_id
                                                                                  end)
                             as event_integrity_rate
                            , case
                                when latency_sla is null then null
                                else count_if(latency_sla_met = 1)/count_if(first_webhook_sent_time is not null)
                            end as event_transmit_on_time_rate
                        from webhook_orders_enriched
                        -- Separate Calculation for AATH and AADH in PH and exclude cainiao CRP parcels
                        WHERE 
                            NOT (
                                (status IN ('Arrived At Transit Hub', 'Arrived At Destination Hub') AND system_id = 'ph' AND shipper = 'Tiktok XB')
                                OR 
                                (shipper = 'Cainiao' AND (tracking_id LIKE 'RNCNV%' OR tracking_id LIKE 'RCNSG%'))
                            )
                        group by 1,2,3,4,5,6,7,8,9, completeness_flag

                        )

                    , combined_status as (
                    
                        select 
                            delivery_success_date
                            , case
                              when status_code between 23 and 26 then 45 
                              when status_code in (13,16) then 46 
                            end as status_code
                            , case
                              when status_code between 23 and 26 then 'On Vehicle For Delivery (RTS) / RTS Triggered / 
                                                                        Delivery Exception, RTS Initiated / 
                                                                        Arrived At Transit Hub (RTS) / 
                                                                        Arrived At Destination Hub (RTS)' 
                              when status_code in (13,16) then 'In Transit To Next Sorting Hub / Added To Shipment' 
                            end as status
                            , null as tt_action_code_mapping
                            , is_tokopedia
                            , null as od_pair
                            , shipper
                            , system_id
                            , tracking_id
                            , first(case 
                                    when status_code between 23 and 26 then latency_sla
                                    when status_code in (13,16) then latency_sla
                                end) as latency_sla
                            , max(case 
                                    when status_code between 23 and 26 then completeness_flag
                                    when status_code in (13,16) then completeness_flag
                                end) as completeness_flag
                            , min(case 
                                    when status_code between 23 and 26 then first_webhook_sent_time
                                    when status_code in (13,16) then first_webhook_sent_time
                                end) as first_webhook_sent_time
                            , max(case 
                                    when status_code between 23 and 26 then latency_sla_met
                                    when status_code in (13,16) then latency_sla_met
                                end) as latency_sla_met
                            , max(case 
                                    when status_code between 23 and 26 then webhook_attempted_flag
                                    when status_code in (13,16) then webhook_attempted_flag
                                end) as webhook_attempted_flag
                        from webhook_orders_enriched
                        where status_code in (13,16,23,24,25,26)
                            and shipper in ('Tiktok XB', 'Tiktok XB LM')
                        group by 1,2,3,4,5,6,7,8,9
                        )

                    , combined_status_stats as ( 

                        select
                           delivery_success_date
                           , status_code
                           , status
                           , tt_action_code_mapping
                           , is_tokopedia
                           , od_pair
                           , shipper
                           , system_id
                           , latency_sla
                           , case 
                               when completeness_flag = 0 then null 
                               else count(tracking_id)
                           end as total_parcels
                           , sum(webhook_attempted_flag) as webhook_attempt
                           , count_if(first_webhook_sent_time is not null) as webhook_sent
                           , count_if(latency_sla_met=1) as webhook_met_latency_sla
                           , coalesce(count_if(first_webhook_sent_time is not null)/sum(webhook_attempted_flag),0) as webhook_success_rate
                           , count_if(first_webhook_sent_time is not null)/ count(case 
                                                                                       when completeness_flag = 0 then null
                                                                                       else tracking_id
                                                                                  end)
                            as event_integrity_rate
                           , case
                               when latency_sla is null then null
                               else count_if(latency_sla_met = 1)/count_if(first_webhook_sent_time is not null)
                           end as event_transmit_on_time_rate
                        from combined_status
                        group by 1,2,3,4,5,6,7,8,9, completeness_flag

                        )

                    (
                        select 
                            * 
                        from stats 
                    )

                    union all 

                    (
                        select 
                            * 
                        from combined_status_stats
                    )                  

                """
            ),
            base.TransformView(
                view_name="ph_exception",
                jinja_template="""
                   -- Measures NV webhook status SLA performance (completeness/latency) for Tiktok XB PH
                   with ph_base as (

                      select 
                          delivery_success_date
                          , 44 as status_code
                          , 'Arrived At Transit Hub / Arrived At Destination Hub' as status
                          , null as tt_action_code_mapping
                          , is_tokopedia
                          , null as od_pair
                          , shipper
                          , system_id
                          , tracking_id
                          , first(latency_sla) as latency_sla
                          , max(completeness_flag) as completeness_flag
                          , min(first_webhook_sent_time) as first_webhook_sent_time
                          , max(latency_sla_met) as latency_sla_met
                          , max(webhook_attempted_flag) as webhook_attempted_flag
                       from webhook_orders_enriched
                       -- Combining Calculation for AATH and AADH for Tiktok XB PH
                       where (status in ('Arrived At Transit Hub', 'Arrived At Destination Hub') 
                           and system_id = 'ph'
                           and shipper = 'Tiktok XB')
                       group by 1,2,3,4,5,6,7,8,9
                    )



                  select
                       delivery_success_date
                       , status_code
                       , status
                       , tt_action_code_mapping
                       , is_tokopedia
                       , od_pair
                       , shipper
                       , system_id
                       , latency_sla
                       , case 
                           when completeness_flag = 0 then null 
                           else count(tracking_id)
                       end as total_parcels
                       , sum(webhook_attempted_flag) as webhook_attempt
                       , count_if(first_webhook_sent_time is not null) as webhook_sent
                       , count_if(latency_sla_met=1) as webhook_met_latency_sla
                       , coalesce(count_if(first_webhook_sent_time is not null)/sum(webhook_attempted_flag),0) as webhook_success_rate
                       , count_if(first_webhook_sent_time is not null)/ count(case 
                                                                                   when completeness_flag = 0 then null
                                                                                   else tracking_id
                                                                              end)
                        as event_integrity_rate
                       , case
                           when latency_sla is null then null
                           else count_if(latency_sla_met = 1)/count_if(first_webhook_sent_time is not null)
                       end as event_transmit_on_time_rate
                   from ph_base
                   group by 1,2,3,4,5,6,7,8,9, completeness_flag

                   """
            ),
            base.TransformView(
                view_name="tt_action_code",
                jinja_template="""
                -- Measures Tiktok Action-Code SLA performance (completeness/latency) for Tiktok shippers
                select
                    delivery_success_date
                    , status_code
                    , status
                    , tt_action_code_mapping
                    , is_tokopedia
                    , null as od_pair
                    , shipper
                    , system_id
                    , latency_sla
                    , case 
                        when completeness_flag = 0 then null 
                        else count(case 
                                when system_id = 'sg' and status_code = 13 then null 
                                else tracking_id
                            end)
                    end as total_parcels
                    , sum(webhook_attempted_flag) as webhook_attempt
                    , count_if(first_webhook_sent_time is not null and tt_action_code is not null) as webhook_sent
                    , count_if(latency_sla_met=1) as webhook_met_latency_sla
                    , coalesce(
                        count_if(first_webhook_sent_time is not null and tt_action_code is not null) / 
                        sum(webhook_attempted_flag), 0
                    ) as webhook_success_rate
                    , count_if(first_webhook_sent_time is not null and tt_action_code is not null)/ count(case 
                                                                            when system_id = 'sg' and status_code = 13 then null 
                                                                            when completeness_flag = 0 then null
                                                                            else tracking_id
                                                                        end)
                    as event_integrity_rate
                    , case
                        when latency_sla is null then null
                        else count_if(latency_sla_met = 1)/count_if(first_webhook_sent_time is not null and tt_action_code is not null)
                    end as event_transmit_on_time_rate
                from webhook_orders_enriched
                where shipper like '%Tiktok%'
                group by 1,2,3,4,5,6,7,8,9, completeness_flag

                   """
            ),
            base.TransformView(
                view_name="mmcc_stats_action_code",
                jinja_template="""
                -- Measures Tiktok Action-Code SLA performance (completeness/latency) for Tiktok XB MMCC
                with mmcc_base as (

                select
                    shipper
                    , system_id
                    , handed_over_to_last_mile_date 
                    , status
                    , status_code
                    , tt_action_code_mapping
                    , latency_sla
                    , od_pair
                    , sum(total_parcel_items) as total_parcels
                    , sum(total_parcel_items) filter (where webhook_attempted_flag=1) as webhook_attempt
                    , sum(total_parcel_items) filter (where first_webhook_sent_time is not null and tt_action_code is not null) as webhook_sent
                    , sum(total_parcel_items) filter (where latency_sla_met=1) as webhook_met_latency_sla
                from mmcc_webhook_orders_enriched
                where shipper = 'Tiktok XB MMCC'
                group by 1,2,3,4,5,6,7,8

                )

                select
                    handed_over_to_last_mile_date as delivery_success_date
                    , status_code
                    , status
                    , tt_action_code_mapping
                    , null as is_tokopedia
                    , od_pair
                    , shipper
                    , system_id
                    , latency_sla
                    , total_parcels
                    , webhook_attempt
                    , webhook_sent
                    , webhook_met_latency_sla
                    , coalesce(webhook_sent/webhook_attempt,0) as webhook_success_rate
                    , coalesce(webhook_sent/total_parcels,0) as event_integrity_rate
                    , coalesce(webhook_met_latency_sla/webhook_sent,0) as event_transmit_on_time_rate
                from mmcc_base

                """,
            ),
            base.TransformView(
                view_name="mmcc_stats",
                jinja_template="""
                -- Measures NV webhook status SLA performance (completeness/latency) for Tiktok XB MMCC and Lazada XB MM
                with mmcc_base as (

                select
                    shipper
                    , system_id
                    , handed_over_to_last_mile_date 
                    , status
                    , status_code
                    , latency_sla
                    , od_pair
                    , sum(total_parcel_items) as total_parcels
                    , sum(total_parcel_items) filter (where webhook_attempted_flag=1) as webhook_attempt
                    , sum(total_parcel_items) filter (where first_webhook_sent_time is not null) as webhook_sent
                    , sum(total_parcel_items) filter (where latency_sla_met=1) as webhook_met_latency_sla
                from mmcc_webhook_orders_enriched
                group by 1,2,3,4,5,6,7

                )

                select
                    handed_over_to_last_mile_date as delivery_success_date
                    , status_code
                    , status
                    , null as tt_action_code_mapping
                    , null as is_tokopedia
                    , od_pair
                    , shipper
                    , system_id
                    , latency_sla
                    , total_parcels
                    , webhook_attempt
                    , webhook_sent
                    , webhook_met_latency_sla
                    , coalesce(webhook_sent/webhook_attempt,0) as webhook_success_rate
                    , coalesce(webhook_sent/total_parcels,0) as event_integrity_rate
                    , coalesce(webhook_met_latency_sla/webhook_sent,0) as event_transmit_on_time_rate
                from mmcc_base

                """,
            ),
            base.TransformView(
                view_name="holm_stats",
                jinja_template="""
                    -- Measures NV webhook status SLA performance (completeness/latency) for Tiktok XB shippers
                    -- Up until Handed Over To Last Mile status (Reactive reporting)
                    with holm_base as (

                    select
                        shipper
                        , system_id
                        , handed_over_to_last_mile_date 
                        , status
                        , status_code
                        , latency_sla
                        , count(tracking_id) as total_parcels
                        , sum(webhook_attempted_flag) as webhook_attempt
                        , count_if(first_webhook_sent_time is not null) as webhook_sent
                        , count_if(latency_sla_met=1) as webhook_met_latency_sla
                    from webhook_intermediate_orders_enriched
                    group by 1,2,3,4,5,6

                    )

                    select
                        handed_over_to_last_mile_date as delivery_success_date
                        , status_code
                        , status
                        , null as tt_action_code_mapping
                        , null as is_tokopedia
                        , null as od_pair
                        , shipper
                        , system_id
                        , latency_sla
                        , total_parcels
                        , webhook_attempt
                        , webhook_sent
                        , webhook_met_latency_sla
                        , coalesce(webhook_sent/webhook_attempt,0) as webhook_success_rate
                        , coalesce(webhook_sent/total_parcels,0) as event_integrity_rate
                        , coalesce(webhook_met_latency_sla/webhook_sent,0) as event_transmit_on_time_rate
                    from holm_base

                    """,
            ),
            base.TransformView(
                view_name="crp_cainiao_stats",
                jinja_template="""
                    -- Measures NV webhook status SLA performance (completeness/latency) for Cainiao CRP parcels
                    select
                        delivery_success_date
                        , cast(status_code as int) as status_code
                        , status
                        , null as tt_action_code_mapping
                        , is_tokopedia
                        , null as od_pair
                        , shipper
                        , system_id
                        , latency_sla
                        , case 
                            when completeness_flag = 0 then null 
                            else count(case 
                                    when system_id = 'sg' and status_code = 13 then null 
                                    else tracking_id
                                 end)
                        end as total_parcels
                        , sum(webhook_attempted_flag) as webhook_attempt
                        , count_if(first_webhook_sent_time is not null) as webhook_sent
                        , count_if(latency_sla_met=1) as webhook_met_latency_sla
                        , coalesce(count_if(first_webhook_sent_time is not null)/sum(webhook_attempted_flag),0) as webhook_success_rate
                        , count_if(first_webhook_sent_time is not null)/ count(case 
                                                                                 when system_id = 'sg' and status_code = 13 then null 
                                                                                 when completeness_flag = 0 then null
                                                                                 else tracking_id
                                                                              end)
                         as event_integrity_rate
                        , case
                            when latency_sla is null then null
                            else count_if(latency_sla_met = 1)/count_if(first_webhook_sent_time is not null)
                        end as event_transmit_on_time_rate
                    from webhook_orders_enriched
                    where shipper = 'Cainiao'
                        and (tracking_id LIKE 'RNCNV%' OR tracking_id LIKE 'RCNSG%')
                    group by 1,2,3,4,5,6,7,8,9, completeness_flag
                        
                    """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""

                   (
                       select 
                           *
                           , 'delivery_success_date' as date_type
                       from daily_stats
                   )
                   union all 
                   (
                       select
                           * 
                           , 'delivery_success_date' as date_type
                       from ph_exception
                   )
                   union all
                   (
                        select
                           *
                           , 'delivery_success_date_tt_action_code' as date_type
                       from tt_action_code
                   )
                   union all 
                   (
                       select
                           *
                           , 'delivery_success_date_crp' as date_type
                       from crp_cainiao_stats
                   )
                   union all 
                   (
                       select
                           *
                           , 'handed_over_to_last_mile_date' as date_type
                       from mmcc_stats
                   )
                   union all 
                   (
                       select
                           *
                           , 'handed_over_to_last_mile_date_tt_action_code' as date_type
                       from mmcc_stats_action_code
                   )
                   union all 
                   (
                       select
                           * 
                           , 'handed_over_to_last_mile_date' as date_type
                       from holm_stats
                   )

                   """
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_STATS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "delivery_success_date"),
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.measurement_datetime,
    )
    run(spark, task_config)
    spark.stop()