import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_PICKUPS,
                view_name="order_pickups",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PICKUP_APPOINTMENT_JOBS_ORDERS,
                view_name="pickup_appointment_jobs_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PARCEL_PICKUP_JOBS,
                view_name="parcel_pickup_jobs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with paj as (
                select
                     cast(order_id as bigint) as order_id
                    , cast(pickup_appointment_job_id as bigint) as job_id
                    , 'Pickup Appointment Job' as job_type
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as job_assigned_datetime
                    , system_id
                    , date_format(created_at, 'yyyy-MM') created_month
                from pickup_appointment_jobs_orders
                where system_id = '{{ system_id }}'
                    and pickup_appointment_job_id is not null
                ),

                reservations as (
                select
                     cast(order_id as bigint) as order_id
                    , cast(reservation_id as bigint) as job_id
                    , 'Reservation' as job_type
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as job_assigned_datetime
                    , '{{ system_id }}' as system_id
                    , date_format(created_at, 'yyyy-MM') created_month
                from order_pickups
                where reservation_id is not null
                ),

                pudo_paj as (
                select
                     cast(order_id as bigint) as order_id
                    , cast(servicing_job_id as bigint) as job_id
                    , 'Pudo Pickup Appointment Job' as job_type
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as job_assigned_datetime
                    , '{{ system_id }}' as system_id
                    , date_format(created_at, 'yyyy-MM') created_month
                from parcel_pickup_jobs
                where system_id = '{{ system_id }}'
                    and servicing_job_id is not null
                )

                select *
                from paj
                union all
                select *
                from reservations
                union all
                select *
                from pudo_paj
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
