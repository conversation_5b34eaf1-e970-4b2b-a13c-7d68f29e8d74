import sys

from pyspark.sql import SparkSession

from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from pyspark.sql.functions import current_timestamp, date_format, monotonically_increasing_id

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from pyspark.sql import functions as F
from pyspark.sql.functions import explode, col
from pyspark.sql.types import StructField, ArrayType, StringType, StructType,BooleanType


airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SHIPPER_REF_ITEMS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SHIPPER_REF_ITEMS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    addr_schema = ArrayType(
        StructType([
            StructField("items", StringType(), True)
        ])
    )

    user_schema = ArrayType(
        StructType([
            StructField("unitPrice", StringType(), True),
            StructField("quantity", StringType(), True),
            StructField("itemCategory", StringType(), True),
            StructField("name", StringType(), True),
            StructField("platformOrderNumber", StringType(), True),
            StructField("currency", StringType(), True),
            StructField("sellerShortCode", StringType(), True),
            StructField("sku", StringType(), True),
            StructField("customerOrderCreationTime", StringType(), True),
            StructField("platformItemId", StringType(), True),
            StructField("paidPrice", StringType(), True)
        ])
    )

    # we are loading input cods table for the sake of input_config use. We will remove it in future once
    # input_config is not required
    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    end_month = measurement_datetime.strftime('%Y-%m')
    dte = dt.strptime(end_month, '%Y-%m').date()
    re = dte + relativedelta(months=-6)
    start_month = re.strftime('%Y-%m')

    orders = spark.read.format("delta").load(delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS)\
        .filter(F.col("created_month").between(start_month,end_month))\
        .select("id", "shipper_id", "tracking_id", "shipper_ref_metadata", "created_month", "created_at", "global_shipper_id")

    df_new = orders.withColumn("items", F.from_json("shipper_ref_metadata", addr_schema))\
        .select("id", "shipper_id", "tracking_id", "items", "created_month", "created_at", "global_shipper_id")

    addr_df = df_new.select(explode("items").alias("itemsInfo"), "id", "shipper_id", "tracking_id", "created_month",
                             "created_at", "global_shipper_id")\
        .select("id", "shipper_id", "tracking_id", "itemsInfo.items", "created_month", "created_at", "global_shipper_id")

    shipper_new_df = addr_df.withColumn("itemInfo", F.from_json("items", user_schema))\
        .selectExpr("inline(itemInfo)", "id", "shipper_id", "tracking_id", "created_month", "created_at", "global_shipper_id")

    items_df = shipper_new_df.dropDuplicates()

    final_df = items_df.withColumn("timestamp", date_format(current_timestamp(), "yyyyMMddHHmmss")) \
        .withColumn("random_number", monotonically_increasing_id()) \
        .select(col("id").alias("order_id"), "shipper_id", "tracking_id", col("unitPrice").alias("unit_price"),
                "quantity", col("itemCategory").alias("item_category"),
                "name", col("platformOrderNumber").alias("platform_order_number"), "currency",
                col("sellerShortCode").alias("seller_short_code"), "sku",
                col("customerOrderCreationTime").alias("customer_order_creation_time"),
                col("platformItemId").alias("platform_item_id"), col("paidPrice").alias("paid_price"),
                 "created_month", "created_at", "global_shipper_id", "timestamp","random_number" )

    final_df.createOrReplaceTempView("output")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    SELECT 
                    case when '{{system_id}}' = 'id' then CONCAT(timestamp,random_number,order_id,1)
                    when '{{system_id}}' = 'mm' then CONCAT(timestamp,random_number,order_id,2)
                    when '{{system_id}}' = 'my' then CONCAT(timestamp,random_number,order_id,3)
                    when '{{system_id}}' = 'ph' then CONCAT(timestamp,random_number,order_id,4)
                    when '{{system_id}}' = 'sg' then CONCAT(timestamp,random_number,order_id,5)
                    when '{{system_id}}' = 'th' then CONCAT(timestamp,random_number,order_id,6)
                    when '{{system_id}}' = 'vn' then CONCAT(timestamp,random_number,order_id,7)
                    end as ID
                    , order_id
                    , cast(global_shipper_id as int) as shipper_id
                    , tracking_id
                    , unit_price
                    , quantity 
                    , item_category
                    , name 
                    , platform_order_number
                    , currency
                    , seller_short_code
                    , sku
                    , customer_order_creation_time
                    , platform_item_id
                    , paid_price
                    , created_month
                    , created_at
                    , '{{system_id}}' as system_id
                    FROM output
                    """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_ITEMS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config( spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )


    run(spark, task_config)
    spark.stop()