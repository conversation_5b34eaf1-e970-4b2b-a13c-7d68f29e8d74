import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.B2B_BUNDLE_ORDERS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.B2B_BUNDLE_ORDERS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_MILE_VOLUME_ORDERS,
                view_name="first_mile_volume_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.OrderProdGL(input_env, is_masked).MULTI_PIECE_SHIPMENTS,
                view_name="multi_piece_shipments",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.OrderProdGL(input_env, is_masked).MULTI_PIECE_SHIPMENT_ORDERS,
                view_name="multi_piece_shipment_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="b2b_bundle_order_pre_base",
                jinja_template="""

                select
                    multi_piece_shipment_orders.mps_id as bundle_id
                    , multi_piece_shipments.tracking_number as bundle_tracking_id
                    , from_utc_timestamp(multi_piece_shipments.created_at, '{{ local_timezone }}') as creation_datetime
                    , multi_piece_shipments.created_month as created_month
                    , multi_piece_shipments.system_id
                    , if(multi_piece_shipments.documents_required like '%RDO%', 1, 0) as rdo_required_flag
                    , if(multi_piece_shipments.documents_required like '%GRN%', 1, 0) as grn_required_flag
                    , multi_piece_shipment_orders.order_id
                from multi_piece_shipment_orders
                left join multi_piece_shipments
                    on multi_piece_shipment_orders.mps_id = multi_piece_shipments.id

                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                },
            ),
            base.TransformView(
                view_name="b2b_bundle_order_base",
                jinja_template="""

                with 
                    order_last_delivery_transaction as (

                        select 
                            order_id
                            , max_by(transaction_id, creation_datetime) as transaction_id
                            , max_by(status, creation_datetime) as transaction_status
                            , max_by(waypoint_id, creation_datetime) as waypoint_id
                        from delivery_transaction_events
                        where status = 'Success'
                        group by 1  

                    )          

                select
                    b2b_bundle_order_pre_base.*
                    , first_mile_volume_orders.waypoint_id as pickup_waypoint
                    , order_last_delivery_transaction.transaction_id as delivery_transaction_id
                    , order_last_delivery_transaction.transaction_status as delivery_transaction_status
                    , order_last_delivery_transaction.waypoint_id as delivery_waypoint
                {%- if system_id == 'id' %}
                    , if(b2b_bundle_order_pre_base.rdo_required_flag = 1, concat(b2b_bundle_order_pre_base.bundle_tracking_id, '-DO'), null) as rdo_tracking_id
                    , if(b2b_bundle_order_pre_base.grn_required_flag = 1, concat(b2b_bundle_order_pre_base.bundle_tracking_id, '-GR'), null) as grn_tracking_id
                {%- else %}
                    , cast(null as string) as rdo_tracking_id
                    , cast(null as string) as grn_tracking_id
                {%- endif %}
                from b2b_bundle_order_pre_base 
                left join first_mile_volume_orders on 
                    b2b_bundle_order_pre_base.order_id = first_mile_volume_orders.order_id
                left join order_last_delivery_transaction on
                    b2b_bundle_order_pre_base.order_id = order_last_delivery_transaction.order_id

                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""

                select
                    b2b_bundle_order_base.bundle_id
                    , b2b_bundle_order_base.bundle_tracking_id
                    , b2b_bundle_order_base.creation_datetime as bundle_creation_datetime
                    , b2b_bundle_order_base.rdo_required_flag
                    , b2b_bundle_order_base.grn_required_flag
                    , b2b_bundle_order_base.rdo_tracking_id
                    , b2b_bundle_order_base.grn_tracking_id
                    , b2b_bundle_order_base.pickup_waypoint
                    , b2b_bundle_order_base.delivery_transaction_id
                    , b2b_bundle_order_base.delivery_transaction_status
                    , b2b_bundle_order_base.delivery_waypoint
                    , cast(order_milestones.order_id as bigint) as order_id
                    , order_milestones.tracking_id
                    , order_milestones.granular_status
                    , order_milestones.shipper_id
                    , order_milestones.creation_datetime
                {%- for to_or_from in ('from', 'to') %}
                {%- for level in ('l1', 'l2', 'l3') %}
                    , order_milestones.{{ to_or_from }}_{{ level }}_id
                    , order_milestones.{{ to_or_from }}_{{ level }}_name
                {%- endfor %}
                {%- endfor %}
                    , order_milestones.pickup_datetime
                    , order_milestones.pickup_hub_id
                    , order_milestones.inbound_datetime
                    , order_milestones.inbound_hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.dest_zone
                    , order_milestones.first_valid_delivery_attempt_datetime
                    , order_milestones.second_valid_delivery_attempt_datetime
                    , order_milestones.third_valid_delivery_attempt_datetime
                    , order_milestones.rts_flag
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.first_valid_rts_attempt_datetime
                    , order_milestones.delivery_success_datetime
                    , b2b_bundle_order_base.system_id
                    , b2b_bundle_order_base.created_month
                from b2b_bundle_order_base
                left join order_milestones
                    on b2b_bundle_order_base.system_id = order_milestones.system_id
                    and b2b_bundle_order_base.order_id = order_milestones.order_id 

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).B2B_BUNDLE_ORDERS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
     )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()