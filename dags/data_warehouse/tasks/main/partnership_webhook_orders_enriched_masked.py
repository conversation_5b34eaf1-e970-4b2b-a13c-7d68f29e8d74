import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from data_warehouse.tasks.main import base
from datetime import timedelta
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED,
    depends_on=(data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_MASKED,),
    system_ids=(SystemID.GL,),
)

def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_STATUS_CONFIG,
                            view_name="webhook_status_configuration"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Order Milestones for one completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')

    spark.read.format("parquet").load(parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_SLA_CONFIG) \
        .createOrReplaceTempView("webhook_sla_configuration")

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_ORDERS+latest_partition) \
        .filter(F.col("delivery_success_date") == completion_date).createOrReplaceTempView("webhook_orders")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orphan_webhook",
                jinja_template="""

                with 

                    successful_webhook as (

                            select
                                tracking_id
                                , webhook_orders.status_code
                                , webhook_orders.status 
                                , min_by(event_time,webhook_sent_time) as event_time
                                , min_by(webhook_sent_time,webhook_sent_time) as webhook_sent_time
                            from webhook_orders
                            left join webhook_sla_configuration 
                                on webhook_orders.status_code = cast(webhook_sla_configuration.status_code as int)
                                and webhook_orders.shipper = webhook_sla_configuration.shipper
                            where 
                                cast(webhook_sla_configuration.latency_flag as int) = 1
                                and cast(webhook_sla_configuration.completeness_flag as int) = 0
                                and response_code = 200
                            group by 1,2,3

                    )

                    , webhook_attempt as (

                            select
                                tracking_id
                                , webhook_orders.shipper
                                , delivery_success_date
                                , delivery_success_datetime
                                , system_id
                                , webhook_orders.status_code
                                , webhook_orders.status
                                , webhook_orders.is_tokopedia
                                , webhook_orders.tt_action_code_mapping
                                -- to extract tiktok's action code from url-encoded format (eg. to extract delivery_start from %22action_code%22%3A%22delivery_start%)
                                , coalesce(
                                    nullif(
                                        regexp_extract(
                                            min_by(webhook_orders.webhook_generated_detail, webhook_orders.event_time), 
                                            '%22action_code%22%3A%22([^%"]+)', 
                                            1
                                        ), 
                                        ''
                                    ), 
                                    null
                                ) as tt_action_code
                                , min_by(response_code,webhook_sent_time) as response_code
                            from webhook_orders
                            left join webhook_sla_configuration 
                                on webhook_orders.status_code = cast(webhook_sla_configuration.status_code as int)
                                and webhook_orders.shipper = webhook_sla_configuration.shipper
                            where 
                                cast(webhook_sla_configuration.latency_flag as int) = 1
                                and cast(webhook_sla_configuration.completeness_flag as int) = 0
                            group by 1,2,3,4,5,6,7,8,9

                    )


                    select
                        webhook_attempt.tracking_id
                        , webhook_attempt.shipper
                        , webhook_attempt.delivery_success_date
                        , webhook_attempt.delivery_success_datetime
                        , webhook_attempt.system_id
                        , webhook_attempt.status_code as status_code
                        , webhook_attempt.status
                        , webhook_attempt.tt_action_code_mapping
                        , webhook_attempt.tt_action_code
                        , webhook_attempt.is_tokopedia
                        , cast(coalesce(webhook_sla_configuration.completeness_flag,0) as int) as completeness_flag
                        , 1 as webhook_attempted_flag
                        , webhook_attempt.response_code as first_response_code
                        , successful_webhook.event_time as first_event_time
                        , successful_webhook.webhook_sent_time as first_webhook_sent_time
                        , case
                            when webhook_attempt.status_code = 35 then 
                                (to_unix_timestamp(successful_webhook.webhook_sent_time) - 
                                    to_unix_timestamp(webhook_attempt.delivery_success_datetime))/60
                            when successful_webhook.webhook_sent_time is not null and 
                                    successful_webhook.event_time is not null then 
                                (to_unix_timestamp(successful_webhook.webhook_sent_time) - 
                                    to_unix_timestamp(successful_webhook.event_time))/60
                            else null
                        end as event_to_webhook_minutes_diff
                        , coalesce(webhook_sla_configuration.latency_sla,0) latency_sla
                        , case
                            when webhook_attempt.status_code = 35 then 
                                if(successful_webhook.webhook_sent_time <= webhook_attempt.delivery_success_datetime,1,0)
                            when successful_webhook.webhook_sent_time is not null 
                                and successful_webhook.event_time is not null then
                                if((to_unix_timestamp(successful_webhook.webhook_sent_time) -
                                    to_unix_timestamp(successful_webhook.event_time) )/60 <=
                                    cast(webhook_sla_configuration.latency_sla_in_mins as int) ,1,0)
                            else null
                        end as latency_sla_met
                    from webhook_attempt
                    left join successful_webhook
                        on webhook_attempt.tracking_id = successful_webhook.tracking_id
                        and webhook_attempt.status_code = successful_webhook.status_code
                    left join webhook_sla_configuration 
                        on webhook_attempt.status_code = cast(webhook_sla_configuration.status_code as int)
                        and webhook_attempt.shipper = webhook_sla_configuration.shipper
                """
            ),
            base.TransformView(
                view_name="status_code_map",
                jinja_template="""

                    select 
                        distinct status_code
                        , status
                    from webhook_status_configuration

                """
            ),
            base.TransformView(
                view_name="source_of_truth",
                jinja_template="""

                 {%- for condition, item in completeness_config.items() %}
                    {%- for shipper, status_code in item.items() %}
                            (
                            select 
                                orders.tracking_id
                               , orders.shipper
                               , orders.delivery_success_date
                               , orders.delivery_success_datetime
                               , orders.system_id
                               , orders.is_tokopedia
                               , completeness.status_code
                               , completeness.status
                               , completeness.completeness_flag
                               , completeness.latency_flag
                               , completeness.latency_sla
                               , completeness.latency_sla_in_mins
                            from (

                                select 
                                    distinct tracking_id
                                    , shipper
                                    , delivery_success_date
                                    , delivery_success_datetime
                                    , is_tokopedia
                                    , system_id
                                from webhook_orders
                                where
                                    {{ condition }}
                                    and webhook_orders.shipper =  '{{shipper}}'

                                ) as orders
                            cross join (

                                select 
                                    distinct webhook_sla_configuration.shipper
                                    , webhook_sla_configuration.status_code
                                    , status_code_map.status
                                    , webhook_sla_configuration.completeness_flag
                                    , webhook_sla_configuration.latency_flag
                                    , webhook_sla_configuration.latency_sla
                                    , webhook_sla_configuration.latency_sla_in_mins
                                from webhook_sla_configuration
                                left join status_code_map 
                                    on webhook_sla_configuration.status_code = status_code_map.status_code
                                    and webhook_sla_configuration.shipper = '{{shipper}}'
                                where
                                    webhook_sla_configuration.status_code IN ({{ status_code }})
                                ) as completeness
                            where orders.shipper = completeness.shipper
                            ){% if not loop.last %} union all {% endif %}
                    {%- endfor %} {% if not loop.last %} union all {% endif %}
                {%- endfor %} 

                """,
                jinja_arguments={
                    "completeness_config": spark.sql("""
                        select 
                            shipper
                            , array_join(collect_set(status_code),',') as status_code
                            , condition 
                        from webhook_sla_configuration 
                        where 
                            completeness_flag = 1
                        group by 1,3
                        order by 1
                    """).rdd.map(lambda row: (row[2], {row[0]:row[1]})).reduceByKey(lambda x, y: {**x, **y}).collectAsMap(),
                },
            ),
            base.TransformView(
                view_name="log_enriched",
                jinja_template="""

                with

                    successful_webhook as (

                        select
                            tracking_id
                            , webhook_orders.status_code
                            , min_by(event_time, webhook_sent_time) as event_time
                            , min_by(webhook_sent_time, webhook_sent_time) as webhook_sent_time
                        from webhook_orders
                        left join webhook_sla_configuration 
                            on webhook_orders.status_code = cast(webhook_sla_configuration.status_code as int)
                            and webhook_orders.shipper = webhook_sla_configuration.shipper
                        where
                            cast(webhook_sla_configuration.completeness_flag as int) = 1
                            and response_code = 200
                        group by 1,2

                    )

                    , webhook_attempt as (

                        select
                            tracking_id
                            , webhook_orders.status_code
                            -- to extract tiktok's action code from url-encoded format (eg. to extract delivery_start from %22action_code%22%3A%22delivery_start%)
                            , coalesce(
                                nullif(
                                    regexp_extract(
                                        min_by(webhook_orders.webhook_generated_detail, webhook_orders.event_time), 
                                        '%22action_code%22%3A%22([^%"]+)', 
                                        1
                                    ), 
                                    ''
                                ), 
                                null
                            ) as tt_action_code
                            , min_by(response_code, webhook_sent_time) as response_code
                        from webhook_orders
                        left join webhook_sla_configuration 
                            on webhook_orders.status_code = cast(webhook_sla_configuration.status_code as int)
                            and webhook_orders.shipper = webhook_sla_configuration.shipper
                        where
                            cast(webhook_sla_configuration.completeness_flag as int) = 1
                        group by 1,2

                    )

                    , source_of_truth as (

                        select DISTINCT
                            source_of_truth.*
                            , CASE  
                                WHEN source_of_truth.shipper = 'Tiktok Domestic' 
                                    THEN webhook_status_configuration.tt_domestic_ac
                                WHEN source_of_truth.shipper = 'Tiktok XB LM' 
                                    THEN webhook_status_configuration.tt_xb_lm_ac
                                WHEN source_of_truth.shipper = 'Tiktok XB' 
                                    THEN webhook_status_configuration.tt_xb_e2e_ac
                            END as tt_action_code_mapping
                        from source_of_truth
                        left join webhook_status_configuration 
                            on source_of_truth.status = webhook_status_configuration.status

                     )

                    select
                        source_of_truth.tracking_id
                        , source_of_truth.shipper
                        , source_of_truth.delivery_success_date
                        , source_of_truth.delivery_success_datetime
                        , source_of_truth.system_id
                        , source_of_truth.is_tokopedia
                        , source_of_truth.tt_action_code_mapping
                        , webhook_attempt.tt_action_code
                        , cast(source_of_truth.status_code as int) as status_code
                        , source_of_truth.status
                        , source_of_truth.completeness_flag
                        , if(webhook_attempt.tracking_id is not null,1,0) as webhook_attempted_flag
                        , webhook_attempt.response_code as first_response_code
                        , successful_webhook.event_time as first_event_time
                        , successful_webhook.webhook_sent_time as first_webhook_sent_time
                        , source_of_truth.latency_flag
                        , source_of_truth.latency_sla
                        , source_of_truth.latency_sla_in_mins
                        , case
                            when source_of_truth.status_code = 35 then 
                                (to_unix_timestamp(successful_webhook.webhook_sent_time) - 
                                    to_unix_timestamp(source_of_truth.delivery_success_datetime))/60
                            when successful_webhook.webhook_sent_time is not null 
                                and successful_webhook.event_time is not null then
                                (to_unix_timestamp(successful_webhook.webhook_sent_time) - 
                                    to_unix_timestamp(successful_webhook.event_time))/60
                            else null
                        end as event_to_webhook_minutes_diff
                        , case
                            when webhook_attempt.status_code = 35 then 
                                if(successful_webhook.webhook_sent_time <= source_of_truth.delivery_success_datetime,1,0)
                            when successful_webhook.webhook_sent_time is not null 
                                and successful_webhook.event_time is not null then
                                if((to_unix_timestamp(successful_webhook.webhook_sent_time) - 
                                    to_unix_timestamp(successful_webhook.event_time))/60 <=
                                    cast(source_of_truth.latency_sla_in_mins as int),1,0)
                            else null
                        end as latency_sla_met
                    from source_of_truth
                    left join successful_webhook
                        on source_of_truth.tracking_id = successful_webhook.tracking_id
                        and source_of_truth.status_code = successful_webhook.status_code
                    left join webhook_attempt
                        on source_of_truth.tracking_id = webhook_attempt.tracking_id
                        and source_of_truth.status_code = webhook_attempt.status_code
                """
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                (
                    select 
                        tracking_id
                        , shipper
                        , delivery_success_date
                        , delivery_success_datetime
                        , tt_action_code_mapping
                        , tt_action_code
                        , status_code
                        , status
                        , is_tokopedia
                        , completeness_flag
                        , webhook_attempted_flag 
                        , first_response_code
                        , first_event_time
                        , first_webhook_sent_time
                        , event_to_webhook_minutes_diff
                        , latency_sla
                        , latency_sla_met
                        , system_id
                    from log_enriched 
                )
                
                    union all 
                (   
                    select 
                        tracking_id
                        , shipper
                        , delivery_success_date
                        , delivery_success_datetime
                        , tt_action_code_mapping
                        , tt_action_code
                        , status_code
                        , status
                        , is_tokopedia
                        , completeness_flag
                        , webhook_attempted_flag 
                        , first_response_code
                        , first_event_time
                        , first_webhook_sent_time
                        , event_to_webhook_minutes_diff
                        , latency_sla
                        , latency_sla_met
                        , system_id
                    from orphan_webhook 
                )

                """
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "delivery_success_date"),
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.measurement_datetime,
    )
    run(spark, task_config)
    spark.stop()