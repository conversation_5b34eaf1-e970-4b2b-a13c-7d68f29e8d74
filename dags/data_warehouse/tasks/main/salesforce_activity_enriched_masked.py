import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACTIVITY_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACTIVITY_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).EVENT, view_name="salesforce_event"),
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).TASK,
                view_name="salesforce_task",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            id
                            , date(activity_date) as activity_date
                            , type
                            , owner_id as user_id
                            , what_id as opportunity_id
                            , account_id as account_id
                            , who_id as lead_or_contact_id
                            , status_c as status
                            , subject
                            , purpose_of_activity_c as purpose
                            , description
                            , 'event' as source
                            , null as completed_date_time
                            , created_date
                        from salesforce_event
                        where
                            is_deleted = false

                        union all

                        select
                            id
                            , date(activity_date) as activity_date
                            , type
                            , owner_id as user_id
                            , what_id as opportunity_id
                            , account_id
                            , who_id as lead_or_contact_id
                            , status
                            , subject
                            , purpose_of_activity_c as purpose
                            , description
                            , 'task' as source
                            , completed_date_time
                            , created_date
                        from salesforce_task
                        where
                            is_deleted = false

                    )
                    , final as (

                        select
                            base.id
                            , base.user_id
                            , base.opportunity_id
                            , base.account_id
                            , base.lead_or_contact_id
                            , base.type
                            , base.source
                            , base.subject
                            , base.status
                            , base.purpose
                            , base.description
                            , from_utc_timestamp(base.created_date, {{ get_local_timezone }}) as creation_datetime
                            , base.activity_date
                            , from_utc_timestamp(base.completed_date_time, {{ get_local_timezone }}) 
                            as completion_datetime
                            , salesforce_user_enriched.country
                            , salesforce_user_enriched.system_id
                            , date_format(
                                from_utc_timestamp(base.created_date, {{ get_local_timezone }}), 'yyyy-MM'
                            ) as created_month
                        from base
                        left join salesforce_user_enriched on
                            base.user_id = salesforce_user_enriched.id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("salesforce_user_enriched.system_id"),
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_ACTIVITY_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
