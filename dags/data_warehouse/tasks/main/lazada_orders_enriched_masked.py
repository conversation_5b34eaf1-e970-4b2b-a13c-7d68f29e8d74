import sys

from pyspark.sql import SparkSession
from datetime import timedelta

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.LATEST_SHIPMENT_REPORT_MASKED,
        data_warehouse.SLABreachDAG.Task.LAZADA_FRAUD_FLAGS_MASKED,
        data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_MASKED,
        data_warehouse.SLABreachDAG.Task.MITRA_ORDERS_MASKED,
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    # measurement_datetime for parquet_tables_masked
    measurement_datetime_partition = f"""/measurement_datetime={
    (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d 18-00-00')
    }"""

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(
                    env).SELLER_OPERATIONAL_METRICS + measurement_datetime_partition,
                view_name="seller_operational_metrics",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LATEST_SHIPMENT_REPORT,
                view_name="latest_shipment_report",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZ_SLA_BREACH_REPORT,
                view_name="laz_sla_breach_report",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_FRAUD_FLAGS,
                view_name="lazada_fraud_flags",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS,
                view_name="lazada_orders",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).MITRA_ORDERS,
                view_name="mitra_orders",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    -- Parcel attributes

                    lazada_orders.creation_date
                    , lazada_orders.creation_datetime
                    , lazada_orders.order_id
                    , lazada_orders.tracking_id
                    , lazada_orders.granular_status
                    , lazada_orders.lazmall_flag
                    , lazada_orders.is_tap_flag
                    , lazada_orders.cod_value
                    , lazada_orders.insurance_value
                    , coalesce(lazada_fraud_flags.price_match_results,lazada_orders.package_content) as package_content
                    , lazada_orders.original_weight
                    , lazada_fraud_flags.dws_variance_current_dws_value as latest_dws_weight
                    , lazada_orders.nv_width
                    , lazada_orders.nv_height
                    , lazada_orders.nv_length
                    , lazada_orders.nv_weight

                    -- Mitra attributes

                    , case
                        when mitra_orders.mitra_name is null then 'Core'
                        else 'Mitra'
                    end as first_pick_up
                    , mitra_orders.mitra_id
                    , mitra_orders.mitra_name
                    , mitra_orders.lodged_in_datetime
                    , mitra_orders.mitra_registration_status
                    , mitra_orders.mitra_latitude
                    , mitra_orders.mitra_longitude

                    -- Consignee attributes

                    , lazada_orders.to_name
                    , lazada_orders.to_email
                    , lazada_orders.to_email_domain
                    , lazada_orders.to_contact

                    -- Seller attributes

                    , lazada_orders.shipper_id
                    , lazada_orders.shipper_name
                    , lazada_orders.seller_id
                    , lazada_orders.seller_name
                    , lazada_fraud_flags.laz_shipper_status
                    , case
                        when seller_operational_metrics.l6m_first_order_completion_date is null
                        then 'No order completion in the L6M'
                        when seller_operational_metrics.l6m_first_order_completion_date
                            >= creation_date - interval 30 days
                        then 'New Shipper'
                        when datediff(
                            seller_operational_metrics.l6m_latest_order_completion_date
                            , creation_date
                        ) <= 30
                        then 'Active Shipper'
                        when datediff(
                            seller_operational_metrics.l6m_latest_order_completion_date
                            , creation_date
                        ) <= 60
                        then 'Last active between 31-60 days'
                        when datediff(
                            seller_operational_metrics.l6m_latest_order_completion_date
                            , creation_date
                        ) <= 90
                        then 'Last active between 61-90 days'
                        else 'Last active between 3-6 months ago'
                    end as seller_category
                    , seller_operational_metrics.l6m_first_order_completion_date
                    , seller_operational_metrics.l6m_latest_order_completion_date
                    , seller_operational_metrics.l6m_total_orders
                    , seller_operational_metrics.l6m_cancelled_orders
                    , seller_operational_metrics.l6m_completed_orders
                    , seller_operational_metrics.l6m_rts_orders
                    , seller_operational_metrics.l6m_avg_cod_value
                    , seller_operational_metrics.l6m_high_value_orders
                    , seller_operational_metrics.l6m_high_value_cancelled_orders
                    , seller_operational_metrics.l6m_high_value_completed_orders
                    , seller_operational_metrics.l6m_high_value_rts_orders

                    -- Suspicious flags

                    , lazada_fraud_flags.first_flag_timestamp
                    , date(lazada_fraud_flags.first_flag_timestamp) as first_flag_date
                    , lazada_fraud_flags.consignee_email_domain_current_flag
                    , lazada_fraud_flags.dws_variance_current_flag
                    , lazada_fraud_flags.reasonable_price_flag
                    , lazada_fraud_flags.known_fraud_shipper_flag
                    , lazada_fraud_flags.sus_consignee_flag

                    -- Parcel's current breach and shipment status

                    , laz_sla_breach_report.start_clock_date
                    , laz_sla_breach_report.end_clock_date
                    , laz_sla_breach_report.sla_days
                    , laz_sla_breach_report.sla_date
                    , laz_sla_breach_report.days_to_sla_breach
                    , latest_shipment_report.shipment_id as current_shipment_id
                    , latest_shipment_report.add_to_shipment_datetime as add_to_shipment_datetime
                    , latest_shipment_report.status as shipment_status
                    , latest_shipment_report.orig_hub_id as orig_hub_id
                    , latest_shipment_report.orig_hub_name as orig_hub_name
                    , latest_shipment_report.dest_hub_id as dest_hub_id
                    , latest_shipment_report.dest_hub_name as dest_hub_name


                    , lazada_orders.system_id
                    , lazada_orders.created_month

                from lazada_orders
                left join seller_operational_metrics on
                     lazada_orders.shipper_id = seller_operational_metrics.shipper_id
                     and lazada_orders.seller_id = seller_operational_metrics.seller_id
                     and lazada_orders.system_id = seller_operational_metrics.system_id
                left join laz_sla_breach_report
                    on lazada_orders.order_id = laz_sla_breach_report.order_id
                left join mitra_orders
                    on lazada_orders.order_id = mitra_orders.order_id
                left join latest_shipment_report
                    on lazada_orders.order_id = latest_shipment_report.order_id
                left join lazada_fraud_flags
                    on lazada_orders.order_id = lazada_fraud_flags.order_id

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()