import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.LONGTAIL_SLA_HOURS_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.LONGTAIL_SLA_HOURS_MASKED,
    system_ids=(constants.SystemID.GL,),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.GDrive(input_env).LONGTAIL_SLA_HOURS,
                view_name="longtail_sla_hours_gdrive",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    country
                    , shipper_config_type
                    , shipper_config
                    , origin_dest_config_type
                    , origin_config
                    , dest_config
                    , extra_config_type
                    , extra_config
                    , sla_type
                    , cast(sla_hour as double) as sla_hour
                    , if(calendar_config is null, 'ops_calendar', calendar_config) as calendar_config
                    , start_date
                    , end_date
                    , country as system_id
                    , date_format(start_date, 'yyyy-MM') as created_month
                from longtail_sla_hours_gdrive
                where
                    is_deleted = 0
                    or is_deleted is null
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LONGTAIL_SLA_HOURS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
