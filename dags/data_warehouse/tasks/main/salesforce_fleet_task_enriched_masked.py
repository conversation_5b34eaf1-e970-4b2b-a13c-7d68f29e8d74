import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_FLEET_TASK_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_FLEET_TASK_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched"
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).SF_TASK_RIDER_TYPE,
                view_name="sf_task_rider_type",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).TASK,
                view_name="salesforce_task",
            ),
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).CASE,
                view_name="salesforce_case"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            task.id as task_id
                            , task.type
                            , task.owner_id as user_id
                            , task.what_id as case_id
                            , case.case_number
                            , task.created_by_id
                            , user.name as created_by_name
                            , task.who_id as who_id
                            , task.driver_id_c as driver_id
                            , task.driver_name_c as driver_name
                            , task.priority
                            , task.status
                            , task.subject
                            , task.purpose_of_activity_c as purpose
                            , task.description
                            , task.task_type_l1_c as task_type_l1
                            , task.task_type_l2_c as task_type_l2
                            , task.task_type_l3_c as task_type_l3

                            -- QR type and Complaint Category based on task type L1 and L2
                            , type.rq_type
                            , type.complaint_category

                            , task.resolution_c as resolution
                            , task.resolution_reason_c as resolution_reason
                            , task.region_name_c as task_region

                            -- Resolve wrong region from manual input
                            , hubs_enriched.region as region
                            , task.station_name_c as station

                            , task.tracking_id_c as tracking_id
                            , task.call_to_rider_c as call_to_rider
                            , task.time_received_c as time_received
                            , task.created_date as created_datetime
                            , task.completed_date_time as completed_datetime
                            , task.activity_date as due_datetime
                            , user.system_id
                        from salesforce_task as task
                        left join salesforce_user_enriched as user
                            on task.created_by_id = user.id
                        left join salesforce_case as case
                            on task.what_id = case.id

                        -- Logic for RQ_type and complaint_type based on task_type
                        left join sf_task_rider_type as type
                            on task.task_type_l2_c = type.task_type_l2
                            and task.task_type_l3_c = type.task_type_l3
                            and user.system_id = type.system_id

                        left join hubs_enriched
                            on task.station_name_c = hubs_enriched.name

                        where user.system_id != 'cn'

                    )
                    , final as (

                        select
                            base.task_id
                            , base.user_id
                            , base.case_id
                            , base.who_id
                            , base.case_number
                            , base.created_by_id
                            , base.created_by_name
                            , base.driver_id
                            , base.driver_name
                            , base.priority
                            , base.type
                            , base.subject
                            , base.status
                            , base.purpose
                            , base.description as comments
                            , base.task_type_l1
                            , base.task_type_l2
                            , base.task_type_l3
                            , base.rq_type
                            , base.complaint_category
                            , base.resolution
                            , base.resolution_reason
                            , base.task_region
                            , base.region
                            , base.station
                            , base.tracking_id
                            , base.call_to_rider
                            , base.time_received
                            , from_utc_timestamp(base.created_datetime, {{ get_local_timezone }}) as creation_datetime
                            , from_utc_timestamp(base.completed_datetime, {{ get_local_timezone }})
                                as completion_datetime
                            , from_utc_timestamp(base.due_datetime, {{ get_local_timezone }}) as due_datetime
                            , base.system_id
                            , date_format(
                                from_utc_timestamp(base.created_datetime, {{ get_local_timezone }}), 'yyyy-MM'
                            ) as created_month
                        from base
                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_FLEET_TASK_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,)

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    spark.conf.set("spark.sql.autoBroadcastJoinThreshold", -1)
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()