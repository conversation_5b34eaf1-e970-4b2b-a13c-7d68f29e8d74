import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.ROUTE_CLASSIFICATION_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.ROUTE_CLASSIFICATION_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
        data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
        data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, 
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PROOFS_ENRICHED,
                view_name="proofs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_CLASSIFICATION_DRIVER_MAPPING,
                view_name="id_classification_driver_mapping",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_CLASSIFICATION_STOPS_THRESHOLD,
                view_name="id_classification_stops_threshold",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="routes_filtered",
                jinja_template="""
                select
                    fleet_performance_base_data.system_id
                    , fleet_performance_base_data.route_id
                    , fleet_performance_base_data.route_date
                    , fleet_performance_base_data.created_month
                    , id_classification_driver_mapping.scheme_type
                    , fleet_performance_base_data.depot_region as region
                    , fleet_performance_base_data.depot_id
                    , hubs_enriched.longitude as hub_longitude
                    , hubs_enriched.latitude as hub_latitude
                    , case
                        when lower(fleet_performance_base_data.depot_region) in ('papua','maluku')
                            then (fleet_performance_base_data.zone_start + interval 2 hour)
                        when lower(fleet_performance_base_data.depot_region) in ('kalimantan','sulawesi','bali','nusa','bali nusa')
                            then (fleet_performance_base_data.zone_start + interval 1 hour)
                        else (fleet_performance_base_data.zone_start)
                        end as zone_start
                    , case
                        when lower(fleet_performance_base_data.depot_region) in ('papua','maluku')
                            then (fleet_performance_base_data.zone_end + interval 2 hour)
                        when lower(fleet_performance_base_data.depot_region) in ('kalimantan','sulawesi','bali','nusa','bali nusa')
                            then (fleet_performance_base_data.zone_end + interval 1 hour)
                        else (fleet_performance_base_data.zone_end)
                        end as zone_end
                    , fleet_performance_base_data.zone_time_hrs as zone_time_hours
                from fleet_performance_base_data
                left join id_classification_driver_mapping
                    on fleet_performance_base_data.courier_type = id_classification_driver_mapping.driver_type
                left join hubs_enriched
                    on fleet_performance_base_data.depot_id = hubs_enriched.id
                where id_classification_driver_mapping.driver_type is not null
                """,
            ),
            base.TransformView(
                view_name="transactions_base",
                jinja_template="""
                with base as (
                
                    select
                        transactions.route_id
                        , transactions.id as transaction_id
                        , from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}') as transaction_datetime
                    from routes_filtered
                    left join transactions
                        on routes_filtered.route_id = transactions.route_id
                    where transactions.type = 'DD'
                        and transactions.status not in ('Pending', 'Cancelled')
                        and transactions.route_id is not null

                ),
                final as (
                
                    select
                        *
                        , date(transaction_datetime) as transaction_date
                    from base

                )
                select * from final
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="coordinates",
                jinja_template="""
                with max_proof_coordinates as (

                    select
                        reference_id
                        , max_by(proofs_enriched.sign_coordinates_lat, proofs_enriched.proof_id) as latitude
                        , max_by(proofs_enriched.sign_coordinates_lng, proofs_enriched.proof_id) as longitude
                    from proofs_enriched
                    where proofs_enriched.reference = 'Transaction'
                    group by 1

                ),
                base as (
                
                    select
                        transactions_base.route_id
                        , transactions_base.transaction_datetime
                        , transactions_base.transaction_date
                        , max_proof_coordinates.latitude
                        , max_proof_coordinates.longitude
                    from transactions_base
                    left join max_proof_coordinates
                        on transactions_base.transaction_id = max_proof_coordinates.reference_id

                ),
                processing as (
                
                    select
                        *
                        , row_number() over (partition by route_id, transaction_date order by transaction_datetime) as transaction_sequence
                    from base

                ),
                flags as (
                
                    select
                        *
                        , case when latitude = -1 and longitude = -1 then 1 else 0 end as gps_off_flag
                        , case when isnan(latitude) or isnan(longitude) then 1 else 0 end as nan_coordinate_flag
                        , case when isnull(latitude) or isnull(longitude) then 1 else 0 end as null_coordinate_flag
                    from processing

                ),
                final as (
                
                    select
                        *
                        , greatest(gps_off_flag,nan_coordinate_flag,null_coordinate_flag) as invalid_coordinate_flag
                    from flags

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="route_legs",
                jinja_template="""
                with clean_coordinates as (
                
                    select
                        *
                    from coordinates
                    where invalid_coordinate_flag = 0
                
                ),
                base as (
                
                    select
                        route_id
                        , transaction_date
                        , row_number() over (partition by (route_id, transaction_date) order by transaction_sequence) as leg_sequence
                        , transaction_datetime as origin_datetime
                        , lead(transaction_datetime, 1) over (partition by (route_id, transaction_date) order by transaction_sequence) as destination_datetime
                        , latitude as origin_latitude
                        , longitude as origin_longitude
                        , lead(latitude, 1) over (partition by (route_id, transaction_date) order by transaction_sequence) as destination_latitude
                        , lead(longitude, 1) over (partition by (route_id, transaction_date) order by transaction_sequence) as destination_longitude
                    from clean_coordinates

                ),
                calc as (
                
                    select
                        *
                        , cos(radians(origin_latitude)) * cos(radians(destination_latitude)) 
                            * cos(radians(destination_longitude) - radians(origin_longitude))
                            + sin(radians(origin_latitude)) * sin(radians(destination_latitude)
                        ) as calculated_cos
                    from base
                    where destination_datetime is not null

                ),
                distance_duration as (
                
                    select
                        *
                        , (6371 * acos(case 
                                        when calculated_cos > 1 then 1
                                        when calculated_cos < -1 then -1
                                        else calculated_cos
                                        end)
                        ) as distance_km
                        , (to_unix_timestamp(destination_datetime, 'yyyy-MM-dd HH:mm:ss') - to_unix_timestamp(origin_datetime, 'yyyy-MM-dd HH:mm:ss'))
                            / cast(60 as double
                        ) as duration_minutes
                    from calc

                ),
                speed as (
                
                    select
                        *
                        , distance_km / cast((duration_minutes/60) as double) as km_per_hour
                    from distance_duration

                ),
                pre_final as (
                
                    select
                        *
                        , case when isnan(distance_km) then 1 else 0 end nan_distance_flag
                        , case when isnull(distance_km) then 1 else 0 end null_distance_flag
                        , case when km_per_hour >= 100 then 1 else 0 end as extreme_speed_flag
                    from speed
                
                ),
                final as (
                
                    select
                        *
                        , greatest(nan_distance_flag,null_distance_flag,extreme_speed_flag) as invalid_leg_flag
                    from pre_final

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="route_stem_data",
                jinja_template="""
                with base as (
                
                    select
                        route_id
                        , transaction_date
                        , max_by(longitude, transaction_sequence) as last_longitude
                        , min_by(longitude, transaction_sequence) as first_longitude
                        , min_by(latitude, transaction_sequence) as first_latitude
                        , max_by(latitude, transaction_sequence) as last_latitude
                    from coordinates
                    where invalid_coordinate_flag = 0
                    group by {{ range(1, 3) | join(',') }}

                ),
                stem_calc as (
                
                    select
                        base.route_id
                        , base.transaction_date
                        , cos(radians(base.first_latitude)) * cos(radians(routes_filtered.hub_latitude)) 
                            * cos(radians(routes_filtered.hub_longitude) - radians(base.first_longitude))
                            + sin(radians(base.first_latitude)) * sin(radians(routes_filtered.hub_latitude)
                        ) as stem_out_cos
                        , cos(radians(base.last_latitude)) * cos(radians(routes_filtered.hub_latitude)) 
                            * cos(radians(routes_filtered.hub_longitude) - radians(base.last_longitude))
                            + sin(radians(base.last_latitude)) * sin(radians(routes_filtered.hub_latitude)
                        ) as stem_in_cos
                    from base
                    left join routes_filtered
                        on base.route_id = routes_filtered.route_id
                        and base.transaction_date = routes_filtered.route_date

                ),
                stem as (
                
                    select
                        *
                        , (6371 * acos(case 
                                        when stem_out_cos > 1 then 1
                                        when stem_out_cos < -1 then -1
                                        else stem_out_cos
                                        end)
                        ) as stem_out
                        , (6371 * acos(case 
                                        when stem_in_cos > 1 then 1
                                        when stem_in_cos < -1 then -1
                                        else stem_in_cos
                                        end)
                        ) as stem_in
                    from stem_calc

                ),
                final as (

                    select
                        *
                        , stem_out + stem_in as stem_distance
                    from stem

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="route_coordinate_agg",
                jinja_template="""
                with base as (

                    select
                        route_id
                        , transaction_date
                        , count(*) as total_coordinates
                        , sum(gps_off_flag) as total_gps_off
                        , sum(nan_coordinate_flag) as total_nan_coordinates
                        , sum(null_coordinate_flag) as total_null_coordinates
                        , sum(invalid_coordinate_flag) as total_invalid_coordinates
                    from coordinates
                    group by {{ range(1, 3) | join(',') }}
    
                ),
                final as (

                    select
                        *
                        , total_invalid_coordinates / cast(total_coordinates as double) as invalid_coordinate_percentage
                    from base

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="route_leg_agg",
                jinja_template="""
                with base as (

                    select
                        route_id
                        , transaction_date
                        , sum(distance_km) filter (where invalid_leg_flag = 0) as zone_distance
                        , count(*) as total_legs
                        , sum(nan_distance_flag) as total_nan_legs
                        , sum(null_distance_flag) as total_null_legs
                        , sum(extreme_speed_flag) as total_extreme_speed_legs
                    from route_legs
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (

                    select
                        *
                        , total_extreme_speed_legs / cast(total_legs as double) as extreme_speed_leg_percentage
                    from base

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="route_stops",
                jinja_template="""
                with base as (

                    select
                        route_legs.*
                        , routes_filtered.system_id
                        , routes_filtered.region
                        , routes_filtered.scheme_type
                    from route_legs
                    left join routes_filtered
                        on route_legs.route_id = routes_filtered.route_id
                        and route_legs.transaction_date = routes_filtered.route_date

                ), 
                potential_thresholds_base as (

                    select
                        base.route_id
                        , base.transaction_date
                        , date_format(base.transaction_date, 'yyyy-MM') as transaction_month
                        , base.region
                        , base.scheme_type
                        , id_classification_stops_threshold.threshold as potential_threshold
                        , count(base.system_id) as total_legs
                        , count_if(base.duration_minutes >= id_classification_stops_threshold.threshold) + 1 as potential_stops
                    from base
                    left join id_classification_stops_threshold
                        on base.system_id = id_classification_stops_threshold.system_id
                    group by {{ range(1, 7) | join(',') }}
                
                ),
                potential_thresholds as (

                    select
                        *
                        , case 
                            when potential_stops = 1 then 0
                            when (potential_stops - 1) = total_legs then 0
                            else 1 
                            end as good_clustering_route
                    from potential_thresholds_base

                ),
                threshold_frame as (

                    select distinct
                        region
                        , date_format(transaction_date - interval 3 months, 'yyyy-MM') as threshold_start_month
                        , transaction_month as threshold_end_month
                        , scheme_type
                        , potential_threshold
                    from potential_thresholds_base

                ),
                clustering_metric as (

                    select
                        threshold_frame.region
                        , threshold_frame.threshold_end_month as threshold_month
                        , threshold_frame.potential_threshold
                        , threshold_frame.scheme_type
                        , sum(potential_thresholds.good_clustering_route) as good_clustering_routes
                    from threshold_frame
                    left join potential_thresholds
                        on threshold_frame.region = potential_thresholds.region
                        and threshold_frame.scheme_type = potential_thresholds.scheme_type
                        and threshold_frame.threshold_start_month <= potential_thresholds.transaction_month
                        and threshold_frame.threshold_end_month >= potential_thresholds.transaction_month
                    group by {{ range(1, 5) | join(',') }}

                ),
                chosen_threshold as (

                    select
                        region
                        , threshold_month
                        , scheme_type
                        , max_by(potential_threshold, good_clustering_routes) as threshold
                    from clustering_metric
                    group by {{ range(1, 4) | join(',') }}

                ),
                final as (

                    select
                        potential_thresholds.route_id
                        , potential_thresholds.transaction_date
                        , chosen_threshold.threshold as stops_threshold
                        , potential_thresholds.potential_stops as stops
                    from chosen_threshold
                    left join potential_thresholds
                        on chosen_threshold.threshold = potential_thresholds.potential_threshold
                        and chosen_threshold.threshold_month = potential_thresholds.transaction_month
                        and chosen_threshold.scheme_type = potential_thresholds.scheme_type
                        and chosen_threshold.region = potential_thresholds.region

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="combined_base",
                jinja_template="""
                with base as (

                    select
                        routes_filtered.system_id
                        , routes_filtered.route_id
                        , routes_filtered.route_date
                        , routes_filtered.created_month
                        , routes_filtered.scheme_type
                        , routes_filtered.region
                        , routes_filtered.zone_start
                        , routes_filtered.zone_end
                        , routes_filtered.zone_time_hours
                        , route_stem_data.stem_distance
                        , route_coordinate_agg.total_coordinates
                        , route_coordinate_agg.total_gps_off
                        , route_coordinate_agg.total_nan_coordinates
                        , route_coordinate_agg.total_null_coordinates
                        , route_coordinate_agg.total_invalid_coordinates
                        , route_coordinate_agg.invalid_coordinate_percentage
                        , route_leg_agg.zone_distance
                        , route_leg_agg.total_legs
                        , route_leg_agg.total_nan_legs
                        , route_leg_agg.total_null_legs
                        , route_leg_agg.total_extreme_speed_legs
                        , route_leg_agg.extreme_speed_leg_percentage
                        , route_stops.stops
                        , route_stops.stops_threshold
                        , route_stem_data.stem_distance + route_leg_agg.zone_distance as route_travel_distance
                    from routes_filtered
                    left join route_stem_data
                        on routes_filtered.route_id = route_stem_data.route_id
                        and routes_filtered.route_date = route_stem_data.transaction_date
                    left join route_coordinate_agg
                        on routes_filtered.route_id = route_coordinate_agg.route_id
                        and routes_filtered.route_date = route_coordinate_agg.transaction_date
                    left join route_leg_agg
                        on routes_filtered.route_id = route_leg_agg.route_id
                        and routes_filtered.route_date = route_leg_agg.transaction_date
                    left join route_stops
                        on routes_filtered.route_id = route_stops.route_id
                        and routes_filtered.route_date = route_stops.transaction_date

                ),
                flags as (

                    select
                        system_id
                        , created_month
                        , route_id
                        , route_date
                        , region
                        , scheme_type
                        , zone_start as adjusted_zone_start
                        , stops_threshold
                        , stops
                        , stops / cast(zone_time_hours as double) as stops_per_on_zone_hour
                        , stem_distance
                        , zone_distance + stem_distance as route_travel_distance
                        , total_invalid_coordinates as invalid_coordinate_count
                        , case 
                            when invalid_coordinate_percentage > 0.05 
                                and total_gps_off >= 1 then 1 
                            else 0 
                            end as default_coordinate_flag
                        , case 
                            when invalid_coordinate_percentage > 0.05 
                                and (total_nan_coordinates >= 1 or total_null_coordinates >= 1) then 1 
                            else 0 
                            end as nan_null_coordinate_flag
                        , case 
                            when extreme_speed_leg_percentage > 0.05
                                and total_extreme_speed_legs >= 1 then 1 
                            else 0 
                            end as extreme_speed_leg_flag
                        , case when route_travel_distance > 500 then 1 else 0 end as extreme_distance_route_flag
                        , case when zone_time_hours < 2 then 1 else 0 end as short_zone_hours_flag
                        , case when hour(zone_start) < 7 or hour(zone_start) > 19 then 1 else 0 end as extreme_zone_start_flag
                    from base

                ),
                final as (

                    select
                        *
                        , greatest(default_coordinate_flag
                                , nan_null_coordinate_flag
                                , extreme_speed_leg_flag
                                , extreme_distance_route_flag
                                , short_zone_hours_flag
                                , extreme_zone_start_flag) as invalid_flag
                    from flags

                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with region_median as (

                    select
                        created_month
                        , scheme_type
                        , region
                        , percentile(route_travel_distance, 0.5) as route_travel_median
                        , percentile(stops_per_on_zone_hour, 0.5) as stops_per_on_zone_hour_median
                    from combined_base
                    where invalid_flag = 0
                    group by {{ range(1, 4) | join(',') }}

                ),
                flag as (

                    select
                        combined_base.*
                        , region_median.route_travel_median
                        , region_median.stops_per_on_zone_hour_median
                        , case 
                            when invalid_flag = 1 then null
                            when combined_base.route_travel_distance >= region_median.route_travel_median then 1
                            when combined_base.route_travel_distance < region_median.route_travel_median then 0
                            end as high_route_distance_flag
                        , case 
                            when invalid_flag = 1 then null
                            when combined_base.stops_per_on_zone_hour >= region_median.stops_per_on_zone_hour_median then 1
                            when combined_base.stops_per_on_zone_hour < region_median.stops_per_on_zone_hour_median then 0
                            end as high_stops_per_on_zone_hour_flag
                from combined_base
                left join region_median
                    on combined_base.created_month = region_median.created_month
                    and combined_base.scheme_type = region_median.scheme_type
                    and combined_base.region = region_median.region

                ),
                density as (
                
                    select
                        *
                        , case
                            when invalid_flag = 1 then 'invalid'
                            when high_route_distance_flag = 0 and high_stops_per_on_zone_hour_flag = 0 then 'bot_left'
                            when high_route_distance_flag = 0 and high_stops_per_on_zone_hour_flag = 1 then 'top_left'
                            when high_route_distance_flag = 1 and high_stops_per_on_zone_hour_flag = 1 then 'top_right'
                            when high_route_distance_flag = 1 and high_stops_per_on_zone_hour_flag = 0 then 'bot_right'
                            end as density
                        from flag
                ),
                final as (
                
                    select
                        density.*
                        , zones.id as zone_id
                        , zones.name as zone_name
                    from density
                    left join route_logs_enriched
                        on density.route_id = route_logs_enriched.legacy_id
                    left join zones
                        on route_logs_enriched.zone_id = zones.id

                )
                select * from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ROUTE_CLASSIFICATION,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
