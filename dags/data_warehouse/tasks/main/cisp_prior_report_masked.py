import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderTagsDAG.DAG_ID,
            task_id=data_warehouse.OrderTagsDAG.Task.ORDER_TAGS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_TAGS_ENRICHED,
                view_name="order_tags_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CASE_ENRICHED,
                view_name="salesforce_case_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(
                path=(
                    versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_CUTOFFS
                    + measurement_datetime_partition
                ),
                view_name="last_mile_push_off_cutoffs",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base_data",
                jinja_template="""
                with salesforce_cte as (
                    select
                        tracking_id
                        , creation_datetime
                        , row_number() over (partition by tracking_id order by creation_datetime asc) as rnk_sce
                    from salesforce_case_enriched
                ),

                success_hub_cte as (
                    select
                        order_id
                        , if(last_valid_delivery_attempt_status = 'Success'
                            , delivery_success_hub_id, dest_hub_id) as success_hub_id
                    from order_milestones
                )

                select
                    order_tags_enriched.order_id
                    , order_tags_enriched.tag_name as prior_tag_name
                    , order_tags_enriched.creation_datetime as prior_tag_creation_datetime
                    , success_hub_id
                    , order_movements.entry_datetime as hub_inbound_time
                    , order_movements.exit_datetime as hub_exit_time
                    , order_milestones.rts_before_first_attempt_flag
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.last_valid_delivery_attempt_datetime
                    , order_milestones.last_valid_delivery_attempt_status
                    , order_milestones.granular_status
                    , order_milestones.shipper_id
                    , if(order_milestones.cod_value > 0, 1, 0) as cod_flag
                    , order_milestones.dest_zone as dest_hub_zone
                    , order_milestones.delivery_success_hub_id
                    , pets_tickets_enriched.resolution_datetime as pets_resolution_datetime
                    , pets_tickets_enriched.creation_datetime as pets_creation_datetime
                    , pets_tickets_enriched.type
                    , pets_tickets_enriched.sub_type
                    , pets_tickets_enriched.outcome
                    , hubs_enriched.address_city
                    , hubs_enriched.region
                    , hubs_enriched.name
                    , last_mile_push_off_cutoffs.cutoff as last_mile_cutoff
                    , if((order_movements.location_id in (hubs_enriched.parent_hub_id, success_hub_cte.success_hub_id))
                        and (order_movements.location_type = 'HUB')
                        and (order_milestones.order_id is not null), 0, 1) as error_flag
                    , salesforce_cte.tracking_id as salesforce_tracking_id
                    , salesforce_cte.creation_datetime as salesforce_creation_datetime
                    , shipment_orders_enriched.shipment_completion_datetime
                    , coalesce(shipment_completion_datetime, entry_datetime) measurement_start_datetime
                    , order_tags_enriched.created_month
                from order_tags_enriched
                left join success_hub_cte
                    on success_hub_cte.order_id = order_tags_enriched.order_id
                left join order_milestones
                    on order_milestones.order_id = order_tags_enriched.order_id
                left join hubs_enriched
                    on hubs_enriched.id = success_hub_cte.success_hub_id
                left join order_movements
                    on order_movements.order_id = order_tags_enriched.order_id
                    and order_movements.hub_id = success_hub_cte.success_hub_id
                left join pets_tickets_enriched
                    on pets_tickets_enriched.order_id = order_tags_enriched.order_id
                left join last_mile_push_off_cutoffs
                    on success_hub_cte.success_hub_id = last_mile_push_off_cutoffs.hub_id
                    and date(order_movements.entry_datetime) >= last_mile_push_off_cutoffs.start_date
                    and date(order_movements.entry_datetime) <= last_mile_push_off_cutoffs.end_date
                left join salesforce_cte
                    on order_milestones.tracking_id = salesforce_cte.tracking_id
                    and salesforce_cte.rnk_sce = 1
                left join shipment_orders_enriched on
                    success_hub_cte.order_id = shipment_orders_enriched.order_id
                    and success_hub_cte.success_hub_id = shipment_orders_enriched.dest_hub_id
                where
                    order_tags_enriched.tag_name = 'PRIOR'
                """,
            ),

            base.TransformView(
                view_name="transaction_cte",
                jinja_template="""
                with transaction_base as (
                select
                    transactions.order_id
                    , from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')
                        as first_valid_delivery_attempt
                    , transactions.status
                    , cast
                        (case
                            when transactions.service_end_time is null
                            or transactions.status not in ('Success', 'Forced Success', 'Fail')
                                then null
                            when transactions.status = 'Fail'
                            and (transaction_failure_reason.failure_reason_code_id in (5, 6)
                            or (transaction_failure_reason.failure_reason_code_id = 13
                            and date(
                                from_utc_timestamp(transactions.created_at, '{{ local_timezone }}')) >= '2021-10-18')
                            )
                                then 0
                            else 1
                        end as bigint) as valid_flag
                from transactions
                left join transaction_failure_reason
                    on transactions.id = transaction_failure_reason.transaction_id
                    and transactions.type = 'DD'
                )

                select
                    *
                from transaction_base
                where valid_flag = 1
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="calendar_cte",
                jinja_template="""
                with base_rnk as (
                select
                    *
                    , row_number() over
                        (partition by order_id order by error_flag asc, measurement_start_datetime desc) as rnk_base
                from base_data
                ),
                pre_post_cte as (
                select
                    *
                    , if(prior_tag_creation_datetime > measurement_start_datetime, 'POST', 'PRE') as prior_tag_flag
                from base_rnk
                where
                    rnk_base = 1
                ),
                pets_open_cte as (
                select
                    *
                    , case
                        when prior_tag_flag = 'PRE'
                            and measurement_start_datetime > pets_creation_datetime
                            and measurement_start_datetime < pets_resolution_datetime then 1
                    when prior_tag_flag = 'POST'
                        and prior_tag_creation_datetime > pets_creation_datetime
                        and prior_tag_creation_datetime < pets_resolution_datetime then 1
                    else 0
                    end as pets_open_flag
                from pre_post_cte
                ),
                start_clock_cte as (
                select
                    *
                    , case
                        when pets_open_flag = 1 then pets_resolution_datetime
                        when prior_tag_flag = 'PRE' then measurement_start_datetime
                        when prior_tag_flag = 'POST' then prior_tag_creation_datetime
                    else null
                    end as start_clock
                from pets_open_cte
                ),
                cutoff_cte as (
                select
                    *
                    , if(date_format(start_clock, 'HHmm') >= date_format(last_mile_cutoff, 'HHmm')
                        , date(start_clock + interval '1' day)
                            , date(start_clock)) as start_clock_date
                from start_clock_cte
                )

                 select
                    cutoff_cte.*
                    , coalesce(region_calendar.working_day
                        , national_calendar.working_day) as working_day
                    , coalesce(region_calendar.next_working_day_0
                        , national_calendar.next_working_day_0) as next_working_day_0
                    , coalesce(region_calendar.next_working_day_1
                        , national_calendar.next_working_day_1) as next_working_day_1
                    , coalesce(region_calendar.next_working_day_2
                        , national_calendar.next_working_day_2) as next_working_day_2
                from cutoff_cte
                left join calendar as national_calendar
                    on cutoff_cte.start_clock_date = date(national_calendar.next_working_day_0)
                    and national_calendar.region = 'national'
                left join calendar as region_calendar
                    on cutoff_cte.start_clock_date = date(region_calendar.next_working_day_0)
                    and cutoff_cte.address_city = region_calendar.region
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with workday_cte as (
                select
                    *
                    , if(working_day = 1, next_working_day_0
                        , next_working_day_1) as working_start_clock_date
                from calendar_cte
                ),
                attempt_cte as (
                select
                    workday_cte.*
                    , if(transaction_cte.first_valid_delivery_attempt > workday_cte.rts_trigger_datetime
                        or transaction_cte.first_valid_delivery_attempt < workday_cte.prior_tag_creation_datetime
                        , null, transaction_cte.first_valid_delivery_attempt) as first_valid_delivery_attempt_datetime
                    , if(transaction_cte.first_valid_delivery_attempt > workday_cte.rts_trigger_datetime
                        or transaction_cte.first_valid_delivery_attempt < workday_cte.prior_tag_creation_datetime
                        , 0, 1) as valid_delivery_flag
                from workday_cte
                left join transaction_cte
                    on workday_cte.order_id = transaction_cte.order_id
                ),

                completion_cte as (
                select
                    workday_cte.order_id
                    , workday_cte.working_start_clock_date
                    , if(transaction_cte.first_valid_delivery_attempt > workday_cte.rts_trigger_datetime
                        or transaction_cte.first_valid_delivery_attempt < workday_cte.prior_tag_creation_datetime
                        , null, transaction_cte.first_valid_delivery_attempt) as first_valid_delivery_completion_datetime
                    , if(transaction_cte.first_valid_delivery_attempt > workday_cte.rts_trigger_datetime
                        or transaction_cte.first_valid_delivery_attempt < workday_cte.prior_tag_creation_datetime
                        , 0, 1) as valid_delivery_flag
                from workday_cte
                left join transaction_cte
                    on workday_cte.order_id = transaction_cte.order_id
                    and status in ('Success','Forced Success')
                ),

                null_cte as (
                select
                    *
                    , row_number() over
                        (partition by order_id order by valid_delivery_flag desc
                            , first_valid_delivery_attempt_datetime asc) as rnk_null
                from attempt_cte
                ),

                null_cte_completion as (
                select
                    *
                    , row_number() over
                        (partition by order_id order by valid_delivery_flag desc
                            , first_valid_delivery_completion_datetime asc) as rnk_null
                from completion_cte
                ),

                flag_cte as (
                select
                    *
                    , if(rts_trigger_datetime
                        < pets_resolution_datetime, 1, 0) as rts_before_ticket_resolution_flag
                    , if(rts_trigger_datetime
                        < measurement_start_datetime, 1, 0) as rts_before_last_mile_arrival_flag
                from null_cte
                where
                    rnk_null = 1
                ),
                measured_flag_cte as (
                select
                    flag_cte.*
                    , if(rts_before_ticket_resolution_flag = 1
                    or rts_before_last_mile_arrival_flag = 1
                    or rts_before_first_attempt_flag = 1
                    or (pets_creation_datetime IS NOT NULL AND type = 'DAMAGED' AND granular_status = 'Cancelled')
                    or (pets_creation_datetime IS NOT NULL AND type = 'MISSING' AND granular_status = 'Cancelled')
                    or (prior_tag_creation_datetime >= last_valid_delivery_attempt_datetime
                        AND granular_status = 'Completed')
                    or error_flag = 1
                    or flag_cte.working_start_clock_date >= (now() + interval '8' hour)
                    or prior_tag_creation_datetime >= rts_trigger_datetime, 0, 1) as attempt_measured_flag
                    , null_cte_completion.first_valid_delivery_completion_datetime
                from flag_cte
                left join null_cte_completion
                    on flag_cte.order_id = null_cte_completion.order_id
                    and null_cte_completion.rnk_null = 1
                ),

                pet_ticket_flag_cte as (
                select
                    *
                    , if(type = 'DAMAGED', 1, 0) as pets_damaged_flag
                    , if(type = 'PARCEL ON HOLD', 1, 0) as pets_parcel_on_hold_flag
                    , if(type = 'MISSING', 1, 0) as pets_missing_flag
                    , if(type = 'SHIPPER ISSUE', 1, 0) as pets_shipper_issue_flag
                    , if(type = 'SELF COLLECTION', 1, 0) as pets_self_collection_flag
                    , if(type = 'PARCEL EXCEPTION', 1, 0) as pets_parcel_exception_flag
                from measured_flag_cte
                )

                select
                    order_id
                    , prior_tag_name
                    , prior_tag_creation_datetime
                    , success_hub_id as dest_hub_id
                    , name as dest_hub_name
                    , region as dest_hub_region
                    , dest_hub_zone
                    , hub_inbound_time
                    , hub_exit_time
                    , shipment_completion_datetime
                    , measurement_start_datetime
                    , rts_before_first_attempt_flag
                    , rts_trigger_datetime
                    , pets_resolution_datetime
                    , pets_creation_datetime
                    , salesforce_tracking_id
                    , salesforce_creation_datetime
                    , if(salesforce_tracking_id is not null, 1, 0) as salesforce_flag
                    , sub_type
                    , outcome
                    , pets_damaged_flag
                    , pets_parcel_on_hold_flag
                    , pets_missing_flag
                    , pets_shipper_issue_flag
                    , pets_self_collection_flag
                    , pets_parcel_exception_flag
                    , prior_tag_flag prior_tagging
                    , pets_open_flag
                    , start_clock
                    , next_working_day_0
                    , next_working_day_1
                    , working_start_clock_date
                    , first_valid_delivery_attempt_datetime
                    , first_valid_delivery_completion_datetime
                    , last_valid_delivery_attempt_datetime
                    , last_valid_delivery_attempt_status
                    , granular_status
                    , shipper_id
                    , cod_flag
                    , rts_before_ticket_resolution_flag
                    , rts_before_last_mile_arrival_flag
                    , attempt_measured_flag
                    , if(attempt_measured_flag = 1
                        and working_start_clock_date >= date(first_valid_delivery_attempt_datetime)
                            , 1, 0) as attempt_met_flag
                    , if(attempt_measured_flag = 1
                        and working_start_clock_date >= date(first_valid_delivery_completion_datetime)
                            , 1, 0) as completion_met_flag
                    , created_month
                from pet_ticket_flag_cte
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_PRIOR_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()