import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_OPTIMISED_WORKING_REPORT_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_OPTIMISED_WORKING_REPORT_MASKED,
    depends_on=(
            data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FEATURES_MASKED,
            data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FRAUD_PREDICTION_MASKED,
        ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SLABreachDAG.DAG_ID,
            task_id=data_warehouse.SLABreachDAG.Task.SUSPICIOUS_LAZADA_ORDERS_REPORT_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "inbound_date")),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    latest_partition = "/measurement_datetime=latest/"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FEATURES,
                view_name="lazada_hv_order_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SUSPICIOUS_LAZADA_ORDERS_REPORT,
                view_name="suspicious_lazada_orders_report",
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Read lazada_hv_order_fraud_prediction
    spark.read.format("parquet").load(
        versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FRAUD_PREDICTION + latest_partition) \
        .createOrReplaceTempView("lazada_hv_order_fraud_prediction")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                select
                    suspicious_lazada_orders_report.order_id
                    , suspicious_lazada_orders_report.tracking_id
                    , order_milestones.granular_status
                    , date(order_milestones.inbound_datetime) as inbound_date
                    , order_milestones.inbound_datetime
                    , suspicious_lazada_orders_report.shipper_id
                    , suspicious_lazada_orders_report.shipper_name
                    , suspicious_lazada_orders_report.seller_id
                    , suspicious_lazada_orders_report.seller_name
                    , suspicious_lazada_orders_report.cod_value
                    , suspicious_lazada_orders_report.insurance_value
                    , order_milestones.items
                    , suspicious_lazada_orders_report.original_weight
                    , suspicious_lazada_orders_report.latest_dws_weight
                    , suspicious_lazada_orders_report.mitra_registration_status
                    , suspicious_lazada_orders_report.reasonable_price_flag
                    , shipper_attributes.onboarded_date
                    , suspicious_lazada_orders_report.system_id
                    , if(
                        greatest(
                            suspicious_lazada_orders_report.insurance_value
                            , suspicious_lazada_orders_report.cod_value
                        ) >= 5000000
                        , 5000000
                        , greatest(
                            suspicious_lazada_orders_report.insurance_value
                            , suspicious_lazada_orders_report.cod_value
                        )
                    ) as potential_claim_value
                    , (order_milestones.cod_value - order_milestones.insurance_value) / order_milestones.insurance_value *100
                    as cod_insurance_value_pct_gap
                    , suspicious_lazada_orders_report.l6m_high_value_completed_orders/suspicious_lazada_orders_report.l6m_high_value_orders
                    as hvi_completion_rate
                from suspicious_lazada_orders_report
                left join order_milestones on
                    suspicious_lazada_orders_report.order_id = order_milestones.order_id
                left join shipper_attributes on
                    suspicious_lazada_orders_report.shipper_id = shipper_attributes.id
                left join lazada_hv_order_fraud_prediction on
                    suspicious_lazada_orders_report.order_id = lazada_hv_order_fraud_prediction.order_id
                where
                    -- Filter for specific inbound date
                    date(order_milestones.inbound_datetime) = date('{{measurement_datetime}}')
                    -- Remove orders flagged out by ML model
                    and lazada_hv_order_fraud_prediction.prediction != 1
                    -- Remove parcels with terminal status
                    and order_milestones.granular_status not in ('Completed','Returned to Sender')

                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
            base.TransformView(
                view_name="base_with_rules",
                jinja_template="""

                select
                    *
                    , if(
                        mitra_registration_status == "Unregistered"
                        , 1
                        , 0
                    ) as mitra_registration_check
                    , if(
                        original_weight > 50 and latest_dws_weight < 1
                        , 1
                        , 0
                    ) as weight_check
                    , if(
                        cod_insurance_value_pct_gap > 100
                        , 1
                        , 0
                    ) as cod_insurance_value_pct_gap_check
                    , if(
                        hvi_completion_rate < 0.75
                        , 1
                        , 0
                    ) as hvi_completion_rate_check
                    , if(
                        reasonable_price_flag = 1
                        , 1
                        , 0
                    ) as price_check
                    , if(
                        onboarded_date >= (date_sub('{{measurement_datetime}}',1) - interval '7' day)
                        and cod_value > 2000000
                        , 1
                        , 0
                    ) as hv_cod_orders_by_new_seller_check
                from base

                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
            base.TransformView(
                view_name="base_enriched",
                jinja_template="""

                select
                    *
                    , (
                        mitra_registration_check
                        + weight_check
                        + cod_insurance_value_pct_gap_check
                        + hvi_completion_rate_check
                        + price_check
                        + hv_cod_orders_by_new_seller_check
                    ) as sum_of_rules
                from base_with_rules

                """,
            ),
            base.TransformView(
                view_name="base_ranked",
                jinja_template="""

                select
                    *
                    , rank() over (order by potential_claim_value desc) as rank_by_value
                from base_enriched
                where sum_of_rules = 0

                """,
            ),
            base.TransformView(
                view_name="base_ranked_enriched",
                jinja_template="""

                select
                    *
                    , rank_by_value/(select max(rank_by_value) from base_ranked) as pct_rank_by_value
                from base_ranked
                where sum_of_rules = 0

                """,
            ),
            base.TransformView(
                view_name="optimised_working_report",
                jinja_template="""

                (
                    select
                        order_id
                        , tracking_id
                        , granular_status
                        , inbound_date
                        , shipper_id
                        , shipper_name
                        , seller_id
                        , seller_name
                        , cod_value
                        , insurance_value
                        , items
                        , original_weight
                        , latest_dws_weight
                        , sum_of_rules
                        , mitra_registration_check
                        , weight_check
                        , cod_insurance_value_pct_gap_check
                        , price_check
                        , hvi_completion_rate_check
                        , hv_cod_orders_by_new_seller_check
                        , null as rank_by_value
                        , null as pct_rank_by_value
                        , system_id
                    from base_enriched
                    where sum_of_rules > 0
                )

                union all

                (
                    select
                        order_id
                        , tracking_id
                        , granular_status
                        , inbound_date
                        , shipper_id
                        , shipper_name
                        , seller_id
                        , seller_name
                        , cod_value
                        , insurance_value
                        , items
                        , original_weight
                        , latest_dws_weight
                        , sum_of_rules
                        , mitra_registration_check
                        , weight_check
                        , cod_insurance_value_pct_gap_check
                        , price_check
                        , hvi_completion_rate_check
                        , hv_cod_orders_by_new_seller_check
                        , rank_by_value
                        , pct_rank_by_value
                        , system_id
                    from base_ranked_enriched
                )

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                    (
                        -- Flag out high cod orders created by new shippers

                        select
                            lazada_hv_order_features.order_id
                            , lazada_hv_order_features.tracking_id
                            , lazada_hv_order_features.granular_status
                            , lazada_hv_order_features.inbound_date
                            , lazada_hv_order_features.shipper_id
                            , lazada_hv_order_features.shipper_name
                            , cast(lazada_hv_order_features.seller_id as long) as seller_id
                            , lazada_hv_order_features.seller_name
                            , lazada_hv_order_features.cod_value
                            , lazada_hv_order_features.insurance_value
                            , lazada_hv_order_features.items
                            , lazada_hv_order_features.original_weight
                            , lazada_hv_order_features.dws_weight as latest_dws_weight
                            , null as sum_of_rules
                            , null as mitra_registration_check
                            , null as weight_check
                            , null as cod_insurance_value_pct_gap_check
                            , null as price_check
                            , null as hvi_completion_rate_check
                            , 1 as hv_cod_orders_by_new_seller_check
                            , null as rank_by_value
                            , null as pct_rank_by_value
                            , lazada_hv_order_features.system_id
                        from lazada_hv_order_features
                        join shipper_attributes on
                            lazada_hv_order_features.shipper_id = shipper_attributes.id
                        where
                            -- Filter for specific inbound date
                            lazada_hv_order_features.inbound_date = date('{{measurement_datetime}}')
                            -- Filter for high cod orders
                            and lazada_hv_order_features.cod_value >= 2000000
                            -- Filter for orders created by new shippers
                            and datediff(lazada_hv_order_features.created_date, shipper_attributes.onboarded_date) <= 7
                            -- Remove orders flagged out by optimised working report
                            and lazada_hv_order_features.order_id not in (select order_id from optimised_working_report)
                    )

                    union all

                    (
                        select
                            order_id
                            , tracking_id
                            , granular_status
                            , inbound_date
                            , shipper_id
                            , shipper_name
                            , cast(seller_id as long) as seller_id
                            , seller_name
                            , cod_value
                            , insurance_value
                            , items
                            , original_weight
                            , latest_dws_weight
                            , sum_of_rules
                            , mitra_registration_check
                            , weight_check
                            , cod_insurance_value_pct_gap_check
                            , price_check
                            , hvi_completion_rate_check
                            , hv_cod_orders_by_new_seller_check
                            , rank_by_value
                            , pct_rank_by_value
                            , system_id
                        from optimised_working_report
                    )

                    """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_OPTIMISED_WORKING_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "inbound_date"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    run(spark, task_config)
    spark.stop()