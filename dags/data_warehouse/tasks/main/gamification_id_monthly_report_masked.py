import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_MONTHLY_REPORT_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_MONTHLY_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_MONTHLY_REPORT_BASE_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing",),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime.subtract(hours=18), measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    formatted_timestamp = measurement_datetime.strftime('%Y-%m-%d %H:%M:%S') + 'Z'

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_MONTHLY_REPORT_BASE,
                view_name="gamification_id_monthly_report_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_SUCCESS_RATE_GOAL,
                view_name="success_rate_goal",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_MONTHLY_BONUS,
                view_name="monthly_bonus",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_MONTHLY_DEDUCTION,
                view_name="monthly_deduction",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="deductions",
                jinja_template="""
                with final as (

                    select
                        month
                        , driver_id
                        , sum(allowance) as allowance_total
                        , sum(total_deduction) as deduction_total
                        , sum(invalid_pod_deduction) as deduction_invalid_poda
                        , sum(lost_damage_deduction) as deduction_lost_and_damage
                        , sum(total_deduction - invalid_pod_deduction - lost_damage_deduction) as deduction_other
                    from monthly_deduction
                    group by {{ range(1, 3) | join(',') }}
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="bonus",
                jinja_template="""
                with final as (

                    select
                        year_month as month
                        , driver_id
                        , lower(hub_region) as region
                        , lower(concat(hub_density_cat, hub_salary_cat)) as density
                        , sum(bonus_value) as bonus_value
                    from monthly_bonus
                    group by {{ range(1, 5) | join(',') }}
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                
                    select
                        gamification_id_monthly_report_base.*
                        , success_rate_goal.success_rate_goal
                        , coalesce(deductions.allowance_total, 0) as allowance_total
                        , coalesce(deductions.deduction_total, 0) as deduction_total
                        , coalesce(deductions.deduction_invalid_poda, 0) as deduction_invalid_poda
                        , coalesce(deductions.deduction_lost_and_damage, 0) as deduction_lost_and_damage
                        , coalesce(deductions.deduction_other, 0) as deduction_other
                        , bonus.region as region
                        , lower(concat(monthly_density_category, monthly_umk_category)) as density
                        , coalesce(bonus.bonus_value, 0) as bonus_monthly
                        , lower(gamification_id_monthly_report_base.driver_hub_region) as station_region
                        , lower(gamification_id_monthly_report_base.monthly_density_category) as station_density
                    from gamification_id_monthly_report_base
                    left join success_rate_goal
                        on gamification_id_monthly_report_base.driver_hub_region = success_rate_goal.region
                        and gamification_id_monthly_report_base.created_month >= success_rate_goal.effective_start_month
                        and gamification_id_monthly_report_base.created_month <= success_rate_goal.effective_end_month
                    left join deductions
                        on gamification_id_monthly_report_base.driver_id = deductions.driver_id
                        and gamification_id_monthly_report_base.created_month = deductions.month
                    left join bonus
                        on gamification_id_monthly_report_base.driver_id = bonus.driver_id
                        and gamification_id_monthly_report_base.created_month = bonus.month

                ),
                final as (
                
                    select
                        created_month
                        , system_id
                        , cast('"""+formatted_timestamp+"""' as timestamp) as updated_at
                        , cast(driver_id as long) as driver_id
                        , cast(left(created_month, 4) as int) as year
                        , cast(right(created_month, 2) as int) as month
                        , cast(amount as double) as amount
                        , 'IDR' as currency
                        , cast(system_id as string) as country
                        
                        , cast(region as string) as region
                        , cast(density as string) as density
                        , cast(station_region as string) as station_region
                        , cast(station_density as string) as station_density

                        , cast(planned_parcel_count as int) as planned_parcel_count
                        , cast(delivered_parcel_count as int) as delivered_parcel_count
                        , cast(failed_parcel_count as int) as failed_parcel_count     
                        , cast(success_rate_goal as decimal(18,2)) as success_rate_goal

                        , 'regular | marketplace | non-rts' as ebc_category_1
                        , cast(ebc_count_1 as int) as ebc_count_1
                        , cast(coalesce(ebc_points_1,0) as decimal(18,2)) as ebc_points_1
                        , cast(ebc_amount_1 as decimal(18,2)) as ebc_amount_1

                        , 'regular | non-marketplace | non-rts' as ebc_category_2
                        , cast(ebc_count_2 as int) as ebc_count_2
                        , cast(coalesce(ebc_points_2,0) as decimal(18,2)) as ebc_points_2
                        , cast(ebc_amount_2 as decimal(18,2)) as ebc_amount_2

                        , 'bulky | marketplace | non-rts' as ebc_category_3
                        , cast(ebc_count_3 as int) as ebc_count_3
                        , cast(coalesce(ebc_points_3,0) as decimal(18,2)) as ebc_points_3
                        , cast(ebc_amount_3 as decimal(18,2)) as ebc_amount_3

                        , 'bulky | non-marketplace | non-rts' as ebc_category_4
                        , cast(ebc_count_4 as int) as ebc_count_4
                        , cast(coalesce(ebc_points_4,0) as decimal(18,2)) as ebc_points_4
                        , cast(ebc_amount_4 as decimal(18,2)) as ebc_amount_4

                        , 'rts' as ebc_category_5
                        , cast(ebc_count_5 as int) as ebc_count_5
                        , cast(coalesce(ebc_points_5,0) as decimal(18,2)) as ebc_points_5
                        , cast(ebc_amount_5 as decimal(18,2)) as ebc_amount_5

                        , 'regular | pickup' as ebc_category_6
                        , cast(ebc_count_6 as int) as ebc_count_6
                        , cast(coalesce(ebc_points_6,0) as decimal(18,2)) as ebc_points_6
                        , cast(ebc_amount_6 as decimal(18,2)) as ebc_amount_6

                        , 'bulky | pickup' as ebc_category_7
                        , cast(ebc_count_7 as int) as ebc_count_7
                        , cast(coalesce(ebc_points_7,0) as decimal(18,2)) as ebc_points_7
                        , cast(ebc_amount_7 as decimal(18,2)) as ebc_amount_7

                        , 'superbulky | non-rts' as ebc_category_8
                        , cast(ebc_count_8 as int) as ebc_count_8
                        , cast(coalesce(ebc_points_8,0) as decimal(18,2)) as ebc_points_8
                        , cast(ebc_amount_8 as decimal(18,2)) as ebc_amount_8

                        , 'bulky-revise | non-rts' as ebc_category_9
                        , cast(ebc_count_9 as int) as ebc_count_9
                        , cast(coalesce(ebc_points_9,0) as decimal(18,2)) as ebc_points_9
                        , cast(ebc_amount_9 as decimal(18,2)) as ebc_amount_9

                        , 'Not Eligible' as tier_0_description
                        , cast(tier_0_bonus_count as int) as tier_0_bonus_count
                        , cast(tier_0_bonus_amount as decimal(18,2)) as tier_0_bonus_amount
                        , 'Tier 1' as tier_1_description
                        , cast(tier_1_bonus_count as int) as tier_1_bonus_count
                        , cast(tier_1_bonus_amount as decimal(18,2)) as tier_1_bonus_amount
                        , 'Tier 2' as tier_2_description
                        , cast(tier_2_bonus_count as int) as tier_2_bonus_count
                        , cast(tier_2_bonus_amount as decimal(18,2)) as tier_2_bonus_amount
                        , 'Tier 3' as tier_3_description
                        , cast(tier_3_bonus_count as int) as tier_3_bonus_count
                        , cast(tier_3_bonus_amount as decimal(18,2)) as tier_3_bonus_amount
                        , 'Tier 4' as tier_4_description
                        , cast(tier_4_bonus_count as int) as tier_4_bonus_count
                        , cast(tier_4_bonus_amount as decimal(18,2)) as tier_4_bonus_amount

                        , cast(bonus_daily_aggregate as decimal(18,2)) as bonus_daily_aggregate
                        , cast(bonus_monthly as decimal(18,2)) as bonus_monthly

                        , cast(allowance_total as decimal(18,2)) as allowance_total

                        , cast(deduction_total as decimal(18,2)) as deduction_total
                        , cast(deduction_invalid_poda as decimal(18,2)) as deduction_invalid_poda
                        , cast(deduction_lost_and_damage as decimal(18,2)) as deduction_lost_and_damage
                        , cast(deduction_other as decimal(18,2)) as deduction_other
                        
                        , driver_display_name
                        , driver_hub_region
                        , driver_hub_id
                        , monthly_density_category
                        , monthly_umk_category
                        , monthly_umk_value
                        , days_worked
                        , e_days_worked
                        , picked_regular
                        , picked_bulky
                        , delivered_non_rts_fs_regular
                        , delivered_non_rts_fs_bulky
                        , delivered_non_rts_cs_regular
                        , delivered_non_rts_cs_bulky
                        , delivered_non_rts_retail_regular
                        , delivered_non_rts_retail_bulky
                        , delivered_non_rts_standard_regular
                        , delivered_non_rts_standard_bulky
                        , delivered_non_rts_fs_cs_retail_regular
                        , delivered_non_rts_fs_cs_retail_bulky
                        , delivered_non_rts_superbulky
                        , delivered_non_rts_bulky_revise
                        , delivered_rts_regular
                        , delivered_rts_bulky
                        , total_ocd_points
                        , total_parcel_pay
                        , total_volume_bonus
                        , monthly_umk_payment
                        , grand_total

                    from base
                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_MONTHLY_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
