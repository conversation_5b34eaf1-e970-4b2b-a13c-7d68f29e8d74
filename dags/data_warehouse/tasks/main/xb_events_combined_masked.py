import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_COMBINED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_COMBINED_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS,
                view_name="events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS_2019,
                view_name="events_2019",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS_2020,
                view_name="events_2020",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS_2021,
                view_name="events_2021",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS_2022,
                view_name="events_2022",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS_2023,
                view_name="events_2023",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , parcel_id
                    , internal_status_id
                    , vendor_id
                    , event_time
                    , created_at
                    , updated_at
                    , 'gl' as system_id
                    , date_format(created_at, 'yyyy-MM') AS created_month
                from events
                {%- for year in ('2019', '2020', '2021', '2022', '2023') %}
                union all
                select
                    id
                    , parcel_id
                    , internal_status_id
                    , vendor_id
                    , event_time
                    , created_at
                    , updated_at
                    , 'gl' as system_id
                    , date_format(created_at, 'yyyy-MM') AS created_month
                from events_{{ year }}
                {%- endfor %}
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_EVENTS_COMBINED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
