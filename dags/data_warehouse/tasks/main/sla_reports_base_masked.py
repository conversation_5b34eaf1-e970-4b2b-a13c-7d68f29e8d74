import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SLA_REPORTS_BASE_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SLA_REPORTS_BASE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_FOURTH_PARTY_HANDOVERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_RTS_TRIGGER_LOCATIONS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.PRICING_ORDERS_HISTORY_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_PARCEL_RECEIVING_TASKS_ENRICHED_MASKED,
        ),
    ),
    post_execution_check=True,
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_RTS_TRIGGER_LOCATIONS,
                view_name="order_rts_trigger_locations",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_FOURTH_PARTY_HANDOVERS,
                view_name="order_fourth_party_handovers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PRICING_ORDERS_HISTORY_ENRICHED,
                view_name="pricing_orders_history_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_EVENTS_ENRICHED,
                view_name="xb_events_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_PARCEL_RECEIVING_TASKS_ENRICHED,
                view_name="xb_parcel_receiving_tasks_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="rts_details",
                jinja_template="""
                select
                    order_milestones.order_id
                    , min(order_movements.entry_datetime) rts_dest_hub_inbound_datetime
                from order_milestones
                left join hubs_enriched rts_dest_hub
                    on order_milestones.rts_dest_hub_id = rts_dest_hub.id
                left join order_movements
                    on order_milestones.order_id = order_movements.order_id
                    and order_milestones.rts_trigger_datetime < order_movements.exit_datetime
                    and order_movements.location_id in (rts_dest_hub.parent_hub_id, order_milestones.rts_dest_hub_id)
                    and order_movements.location_type = 'HUB'
                group by 1
                """,
            ),
            base.TransformView(
                view_name="id_missing_level_patch",
                jinja_template="""
                select
                    order_id
                    -- Filtering everything based on the existence of L1 is intended to ensure information is
                    -- fetched from one row only even when the row might have L2 or L3 missing
                {%- for level in ('l1', 'l2', 'l3') %}
                    , max_by(from_{{ level }}_id, trigger_datetime)
                        filter(where from_l1_id is not null) as from_{{ level }}_id
                    , max_by(from_{{ level }}_name, trigger_datetime)
                        filter(where from_l1_id is not null) as from_{{ level }}_name
                {%- endfor %}
                {%- for level in ('l1', 'l2', 'l3') %}
                    , max_by(to_{{ level }}_id, trigger_datetime)
                        filter(where to_l1_id is not null) as to_{{ level }}_id
                    , max_by(to_{{ level }}_name, trigger_datetime)
                        filter(where to_l1_id is not null) as to_{{ level }}_name
                {%- endfor %}
                from pricing_orders_history_enriched
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT orders.country
                       , orders.order_id
                       , orders.tracking_id
                       , orders.third_party_tracking_id
                       , shippers.id AS shipper_id
                       , shippers.shipper_name
                       , shippers.parent_id
                       , shippers.parent_name
                       , shippers.parent_id_coalesce
                       , shippers.parent_name_coalesce
                       , shippers.reporting_name
                       , shippers.business_unit
                       , shippers.acquisition_endpoint
                       , shippers.marketplace
                       , shippers.sales_channel
                {%- if shipper_order_types.get(system_id) %}
                       , CASE
                {%- for name, definition in shipper_order_types[system_id].items() %}
                             WHEN {{ definition }} THEN '{{ name }}'
                {%- endfor %}
                         END
                {%- else %}
                       , CAST(NULL AS string)
                {%- endif %}
                         AS shipper_order_type
                       , CAST(IF(orders.cod_value > 0, 1, 0) AS bigint) AS cod_flag
                       , orders.from_postcode
                       , orders.to_postcode
                       , orders.dest_postcode
                       , orders.from_billing_zone
                       , orders.to_billing_zone
                    {%- for level in ('l1', 'l2', 'l3') %}
                       {%- if system_id == 'id' %}
                       , if(orders.from_l1_id is null, patch.from_{{ level }}_id, orders.from_{{ level }}_id)
                           as from_{{ level }}_id
                       , if(orders.from_l1_id is null, patch.from_{{ level }}_name, orders.from_{{ level }}_name)
                           as from_{{ level }}_name
                       , if(orders.to_l1_id is null, patch.to_{{ level }}_id, orders.to_{{ level }}_id)
                           as to_{{ level }}_id
                       , if(orders.to_l1_id is null, patch.to_{{ level }}_name, orders.to_{{ level }}_name)
                           as to_{{ level }}_name
                       {%- else %}
                       , orders.from_{{ level }}_id
                       , orders.from_{{ level }}_name
                       , orders.to_{{ level }}_id
                       , orders.to_{{ level }}_name
                       {%- endif %}
                    {%- endfor %}
                       , orders.from_name
                       , orders.service_type
                       , orders.delivery_type
                       , orders.parcel_size
                       , orders.nv_length
                       , orders.nv_width
                       , orders.nv_height
                       , orders.nv_weight
                       , orders.weight
                       , orders.granular_status
                       , orders.creation_datetime
                       , orders.dest_hub_id
                       , dest_hub.name AS dest_hub_name
                       , dest_hub.region AS dest_hub_region
                       , dest_hub.sla_region AS dest_hub_sla_region
                       , dest_hub.address_city AS dest_hub_address_city
                       , orders.dest_zone
                       , orders.dp_dropoff_dp_id
                       , dps.name AS dp_dropoff_dp_name
                       , orders.dp_dropoff_datetime
                       , case
                           -- Only for TT Direct
                           when shippers.parent_id_coalesce in (9717974, 10416641, 10416620)
                           then coalesce(
                               xb_parcel_receiving_tasks_enriched.first_holm_received_datetime 
                               , xb_events_enriched.handed_over_to_last_mile_datetime
                           )
                           else xb_events_enriched.handed_over_to_last_mile_datetime
                       end as xb_handed_over_to_last_mile_datetime
                       , orders.pickup_hub_id
                       , pickup_hub.name as pickup_hub_name
                       , pickup_hub.region as pickup_hub_region
                       , orders.pickup_datetime
                       , orders.nv_pickup_datetime
                       , orders.inbound_hub_id
                       , inbound_hub.name as inbound_hub_name
                       , inbound_hub.region as inbound_hub_region
                       , orders.inbound_datetime
                       , orders.inbound_type
                       , orders.delivery_attempts
                       , orders.first_valid_delivery_attempt_timeslot_start
                       , orders.first_valid_delivery_attempt_timeslot_end
                       , orders.first_valid_delivery_attempt_datetime
                       , fail1.description AS first_valid_delivery_failure_reason
                       , orders.second_valid_delivery_attempt_datetime
                       , fail2.description AS second_valid_delivery_failure_reason
                       , orders.third_valid_delivery_attempt_datetime
                       , fail3.description AS third_valid_delivery_failure_reason
                       , orders.last_valid_delivery_attempt_datetime
                       , fail4.description AS last_valid_delivery_failure_reason
                       , orders.rts_flag
                       , orders.rts_reason
                       , rts_trigger_hub.id AS rts_origin_hub_id
                       , rts_trigger_hub.name AS rts_origin_hub_name
                       , rts_trigger_hub.region AS rts_origin_hub_region
                       , rts_dest_hub.id AS rts_dest_hub_id
                       , rts_dest_hub.name AS rts_dest_hub_name
                       , rts_dest_hub.region AS rts_dest_hub_region
                       , rts_dest_hub.address_city AS rts_dest_hub_address_city
                       , orders.rts_dest_zone
                       , orders.rts_trigger_datetime
                       , if(rts_details.rts_dest_hub_inbound_datetime < orders.rts_trigger_datetime
                           , orders.rts_trigger_datetime
                           , rts_details.rts_dest_hub_inbound_datetime
                       ) AS rts_dest_hub_inbound_datetime
                       , orders.first_valid_rts_attempt_datetime
                       , orders.rts_attempts
                       , orders.delivery_success_datetime
                       , orders.is_pickup_required
                       , order_fourth_party_handovers.fourth_party_partner_name
                       , order_fourth_party_handovers.fourth_party_handover_time
                       , order_fourth_party_handovers.fourth_party_handover_type
                       , orders.created_month
                FROM order_milestones AS orders
                LEFT JOIN order_rts_trigger_locations ON order_rts_trigger_locations.order_id = orders.order_id
                LEFT JOIN rts_details on orders.order_id = rts_details.order_id
                LEFT JOIN xb_events_enriched on orders.tracking_id = xb_events_enriched.tracking_id
                LEFT JOIN xb_parcel_receiving_tasks_enriched
                    on orders.tracking_id = xb_parcel_receiving_tasks_enriched.tracking_id
                LEFT JOIN order_fourth_party_handovers ON orders.order_id = order_fourth_party_handovers.order_id
            {%- if system_id == 'id' %}
                LEFT JOIN id_missing_level_patch as patch on orders.order_id = patch.order_id
            {%- endif %}
                LEFT JOIN shipper_attributes AS shippers ON shippers.id = orders.shipper_id
                LEFT JOIN dps_enriched AS dps ON dps.dpms_id = orders.dp_dropoff_dp_id
                LEFT JOIN hubs_enriched AS dest_hub ON dest_hub.id = orders.dest_hub_id
                LEFT JOIN hubs_enriched AS rts_dest_hub ON rts_dest_hub.id = orders.rts_dest_hub_id
                LEFT JOIN hubs_enriched AS pickup_hub ON pickup_hub.id = orders.pickup_hub_id
                LEFT JOIN hubs_enriched AS inbound_hub ON inbound_hub.id = orders.inbound_hub_id
                LEFT JOIN hubs_enriched AS rts_trigger_hub ON rts_trigger_hub.id = order_rts_trigger_locations.hub_id
                LEFT JOIN failure_reasons AS fail1 ON fail1.id = orders.first_valid_delivery_failure_reason_id
                    AND fail1.system_id = orders.system_id
                LEFT JOIN failure_reasons AS fail2 ON fail2.id = orders.second_valid_delivery_failure_reason_id
                    AND fail2.system_id = orders.system_id
                LEFT JOIN failure_reasons AS fail3 ON fail3.id = orders.third_valid_delivery_failure_reason_id
                    AND fail3.system_id = orders.system_id
                LEFT JOIN failure_reasons AS fail4 ON fail4.id = orders.last_valid_delivery_failure_reason_id
                    AND fail4.system_id = orders.system_id
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "shipper_order_types": {
                        "id": {
                            "Lazada Bulky": "orders.from_name = 'Lel ID - Bulky WH'",
                            "Lazada Regular": "orders.from_name = 'Lel ID - Main WH'",
                            "Lazada RTM": "orders.tracking_id like 'NLIDRM%'",
                            "Lazada RTC": "orders.tracking_id like 'NLIDRC%'",
                            "Lazada FD": "orders.tracking_id like 'NLIDFD%'",
                        },
                        "ph": {
                            "SHP": "orders.tracking_id like 'SHP%'",
                            "SOP": "orders.tracking_id like 'SOP%'",
                            "SPE": "orders.tracking_id like 'SPE%'",
                            "Lazada Marketplace": "orders.tracking_id rlike '^(NLPHSO|NLPHMP)'",
                            "Lazada Retail": "orders.tracking_id rlike '^(NLPHWM|NLPHRH)'",
                            "Lazada Crossborder": "orders.tracking_id rlike '^(NLPHCB|NLPHXB|NLPHST)'",
                            "Packs": "orders.tracking_id like 'NINPH%'",
                            "PUDO": "orders.tracking_id like 'NVPHNINJA%'",
                        },
                    },
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SLA_REPORTS_BASE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = (SparkSession.builder
        .config('spark.sql.analyzer.maxIterations', '200')
        .getOrCreate())
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
