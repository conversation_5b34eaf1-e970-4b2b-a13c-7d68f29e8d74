import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.HUB_RELATION_SCHEDULES_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.HUB_RELATION_SCHEDULES_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATION_SCHEDULES,
                view_name="hub_relation_schedules",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATIONS,
                view_name="hub_relations",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                
                    select
                        cast(relation_schedules.id as bigint) as hub_relation_schedule_id
                        , relations.id as hub_relation_id
                        , relations.origin_hub_id
                        , origin_hub.name as origin_hub_name
                        , relations.destination_hub_id
                        , dest_hub.name as destination_hub_name
                        , from_utc_timestamp(relation_schedules.created_at, {{ get_local_timezone }}) as created_at
                        , from_utc_timestamp(relation_schedules.updated_at, {{ get_local_timezone }}) as updated_at
                        , from_utc_timestamp(relation_schedules.deleted_at, {{ get_local_timezone }}) as deleted_at
                        , relation_schedules.movement_type
                        , relation_schedules.day
                        , relation_schedules.comment
                        , from_utc_timestamp(relation_schedules.start_time, {{ get_local_timezone }}) as start_time
                        , relation_schedules.duration
                        , lower(relations.origin_hub_system_id) as system_id
                    from hub_relation_schedules as relation_schedules
                    left join hub_relations as relations on
                        relation_schedules.hub_relation_id = relations.id
                    left join hubs_enriched as origin_hub on
                        relations.origin_hub_id = origin_hub.id
                        and relations.origin_hub_system_id = origin_hub.system_id
                    left join hubs_enriched as dest_hub on
                        relations.destination_hub_id = dest_hub.id
                        and relations.destination_hub_system_id = dest_hub.system_id

                ),
                wave as (
                    select
                        hub_relation_schedule_id
                        , row_number() over (
                            partition by hub_relation_id, day 
                            order by date_format(start_time,'HH:mm')
                            ) as wave
                    from base
                    where deleted_at is null

                ),
                final as (
                    select
                        base.*
                        , wave.wave
                        , date_format(base.created_at, 'yyyy-MM') as created_month
                    from base
                    left join wave
                    on base.hub_relation_schedule_id = wave.hub_relation_schedule_id

                )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("lower(relations.origin_hub_system_id)")
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HUB_RELATION_SCHEDULES_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=(
            "system_id",
            "created_month",
        ),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
