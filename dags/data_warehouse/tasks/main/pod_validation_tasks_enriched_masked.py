import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.POD_VALIDATION_TASKS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.POD_VALIDATION_TASKS_ENRICHED_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    details_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        details_lookback_ranges = base.LookBackRange(None, None)
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).ASSIGNMENTS,
                view_name="assignments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).TASKS,
                view_name="tasks",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).INVALID_POD_REASONS,
                view_name="invalid_pod_reasons",
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                            view_name="failure_reasons"),
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS,
                view_name="reservations",
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).JOB_WAYPOINTS,
                view_name="job_waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="tasks_base",
                jinja_template="""
                with job_waypoints_base as (
                -- This CTE is to standardise the values in the job_type column.
                -- The formatting for Pickup Appointment job_type is different.
                    select
                        job_id
                        , if(job_type = 'Pickup Appointment','PICKUP_APPOINTMENT', job_type) as job_type
                        , waypoint_id
                    from job_waypoints
                )

                select
                    tasks.id task_id
                    , tasks.job_id
                    , tasks.job_status
                    , tasks.job_type
                    , tasks.attempt_latitude
                    , tasks.attempt_longitude
                    , tasks.driver_id
                    , if(tasks.job_type is not null,
                        if(tasks.job_type IN ('TRANSACTION','DELIVERY')
                            and transactions.type = 'DD', 'DELIVERY',            
                                if(tasks.job_type IN ('RESERVATION','PICKUP_APPOINTMENT','PUDO_PICKUP_APPOINTMENT')
                                    and (job_waypoints_base.job_id is not null or transactions.type = 'PP' 
                                        or reservations.id is not null), 'PICKUP',
                                            if(tasks.job_type = 'RETURN_PICKUP','RETURN_PICKUP',null)))
                                                , null) transaction_type               
                    , coalesce(job_waypoints_base.waypoint_id, reservations.waypoint_id, transactions.waypoint_id) as waypoint_id
                    , cast(tasks.failure_reason_id as bigint) failure_reason_id
                    , cast(tasks.hub_id as bigint) hub_id
                    , tasks.was_reattempted
                    , tasks.system_id
                    , tasks.created_month
                    , from_utc_timestamp(tasks.attempted_at, '{{ local_timezone }}') attempted_datetime
                from tasks
                left join job_waypoints_base
                    on tasks.job_id = job_waypoints_base.job_id
                    and tasks.job_type = job_waypoints_base.job_type
                    and tasks.system_id = '{{ system_id }}'
                left join reservations
                    on tasks.job_id = reservations.id
                    and tasks.job_type = 'RESERVATION'
                left join transactions
                    on tasks.job_id = transactions.id
                    and tasks.job_type IN ('RETURN_PICKUP','DELIVERY','TRANSACTION')
                where tasks.system_id = '{{ system_id }}'
                    and (lower(received_by) != 'operations'
                        or received_by is null)
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""

                with pod_table as (
                select
                    tasks_base.task_id
                    , cast(parcels.external_id as bigint) order_id
                    , parcels.tracking_id
                    , tasks_base.job_id as transaction_id
                    , tasks_base.job_status transaction_status
                    , tasks_base.job_type
                    , tasks_base.transaction_type
                    , tasks_base.failure_reason_id
                    , failure_reasons.description as transaction_failure_reason
                    , assignments.type validation_type
                    , assignments.validity as validation_result
                    , assignments.invalid_pod_reason_id
                    , invalid_pod_reasons.en_description invalid_pod_reason
                    , assignments.user_id as validation_user_id
                    , trim(
                        concat_ws(' ', user_info.first_name, user_info.last_name)
                    ) as validation_user_name
                    , tasks_base.hub_id
                    , hubs_enriched.name as hub_name
                    , hubs_enriched.region as hub_region
                    , cast(tasks_base.driver_id as bigint) as courier_id
                    , drivers_enriched.display_name as courier_name
                    , drivers_enriched.driver_type as courier_type
                    , cast(waypoints.route_id as int) as route_id
                    , shipper_attributes.id as shipper_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.sales_channel
                    , tasks_base.attempt_latitude
                    , tasks_base.attempt_longitude
                    , tasks_base.attempted_datetime
                    , tasks_base.was_reattempted
                    , CAST(DATEDIFF(
                            tasks_base.attempted_datetime, drivers_enriched.employment_start_date)
                            /30.42 as DOUBLE) tenure_length_months
                    , from_utc_timestamp(assignments.created_at, '{{ local_timezone }}') validation_datetime
                    , tasks_base.system_id
                    , tasks_base.created_month
                from tasks_base
                left join parcels
                    on parcels.task_id = tasks_base.task_id
                    and parcels.system_id = '{{ system_id }}'
                left join assignments
                    on tasks_base.task_id = assignments.task_id
                left join waypoints
                    on tasks_base.waypoint_id = waypoints.legacy_id
                    and waypoints.system_id = '{{ system_id }}'
                left join drivers_enriched
                    on tasks_base.driver_id = drivers_enriched.id
                left join hubs_enriched
                    on hubs_enriched.id = tasks_base.hub_id
                left join failure_reasons
                    on tasks_base.failure_reason_id = failure_reasons.id
                    and failure_reasons.system_id = '{{ system_id }}'
                left join order_milestones
                    on parcels.external_id = order_milestones.order_id
                left join shipper_attributes
                    on order_milestones.shipper_id = shipper_attributes.id
                left join invalid_pod_reasons
                    on assignments.invalid_pod_reason_id = invalid_pod_reasons.id
                    and invalid_pod_reasons.system_id = '{{ system_id }}'
                left join user_info
                    on assignments.user_id = user_info.user_id
                )

                select
                    task_id
                    , order_id
                    , tracking_id
                    , transaction_id
                    , transaction_status
                    , job_type
                    , transaction_type
                    , failure_reason_id
                    , transaction_failure_reason
                    , validation_type
                    , validation_result
                    , invalid_pod_reason_id
                    , invalid_pod_reason
                    , validation_user_id
                    , validation_user_name
                    , hub_id
                    , hub_name
                    , hub_region
                    , courier_id
                    , courier_name
                    , courier_type
                    , route_id
                    , shipper_id
                    , shipper_name
                    , sales_channel
                    , attempt_latitude
                    , attempt_longitude
                    , attempted_datetime
                    , was_reattempted
                    , tenure_length_months
                    , validation_datetime
                    , system_id
                    , created_month
                from pod_table
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).POD_VALIDATION_TASKS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()