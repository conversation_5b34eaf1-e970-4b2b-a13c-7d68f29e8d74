import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.ROBOCHAT_MESSAGES_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.ROBOCHAT_MESSAGES_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.RecoveryDAG.DAG_ID,
                               task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.ShippersDAG.DAG_ID,
                               task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
                               task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED_BASE,
                view_name="pets_tickets_enriched_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.SnsPlatformsProdGL(input_env, is_masked).MESSAGE_LOGS,
                view_name="message_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.NotificationsV2ProdGL(input_env, is_masked).SCHEDULED_CHAT_MESSAGES,
                view_name="scheduled_chat_messages",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="earliest_pets",
                jinja_template="""
                    select
                        order_id
                        , min_by(id, creation_datetime) as ticket_id
                        , min(creation_datetime) as creation_datetime
                        , system_id
                    from pets_tickets_enriched_base
                    where sub_type = 'ROBO CHAT'
                    group by 1,4
                """,
            ),
            base.TransformView(
                view_name="seen_messages_cte",
                jinja_template="""
                with message_logs_base as (
                select
                    system_id
                    , get_json_object(get_json_object(request_body, '$.TemplateParams'), '$.ticket_id') as pets_ticket_id
                    , get_json_object(get_json_object(request_body, '$.TemplateParams'), '$.tracking_id') as tracking_id
                    , from_utc_timestamp(seen_at, '{{ local_timezone }}') as seen_datetime
                from message_logs
                -- identifier for robochat data: 892629075011125248, 89262972253721804, 919802515506679808
                -- new identifiers for phase 2:
                --- bad address initial msg: 955653851778727936
                --- bad address reminder msg: 955655188646006784
                --- max attempt/cancellation initial msg: 955651751527129088
                --- max attempt/cancellation reminder msg:  955652377413750784
                where outgoing_message is not null
                    and cast(get_json_object(request_body, '$.TemplateCode') as string) in (892629075011125248,89262972253721804,919802515506679808,955653851778727936,955651751527129088)
                    and system_id =  '{{ system_id }}'
                )

                select
                    pets_ticket_id
                    , tracking_id
                    , count(*) filter(where seen_datetime is not null) as message_read_count
                    , min(seen_datetime) as earliest_message_read_datetime
                from message_logs_base
                group by 1,2
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with scheduled_message_base as (
                select
                    cast(scheduled_chat_messages.id as bigint) scheduled_chat_message_id
                    , cast(scheduled_chat_messages.ticket_id as bigint) as ticket_id
                    , cast(scheduled_chat_messages.order_id as bigint) as order_id
                    , cast(scheduled_chat_messages.failure_reason_id as int) as delivery_failure_reason_id
                    , scheduled_chat_messages.status as message_status
                    , scheduled_chat_messages.outcome
                    , scheduled_chat_messages.chat_seq_number
                    , from_utc_timestamp(scheduled_chat_messages.created_at, '{{ local_timezone }}') as creation_datetime
                    , from_utc_timestamp(scheduled_chat_messages.responded_at, '{{ local_timezone }}') as responded_datetime
                    , scheduled_chat_messages.is_reminder
                    , scheduled_chat_messages.response
                    , from_utc_timestamp(scheduled_chat_messages.scheduled_at, '{{ local_timezone }}') as scheduled_datetime
                    , from_utc_timestamp(scheduled_chat_messages.updated_at, '{{ local_timezone }}') as updated_datetime
                    , seen_messages_cte.earliest_message_read_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.dest_hub_id
                    , hubs_enriched.region as dest_hub_region
                    , hubs_enriched.name as dest_hub_name
                    , earliest_pets.creation_datetime as pet_ticket_creation_datetime
                    -- Flag to identify the 1st instance of robocall ticket as the build is focused on this instead of subsequent robocall tickets
                    , if(earliest_pets.ticket_id is not null, 1,0) as earliest_robochat_pets_flag
                    , shipper_attributes.id as shipper_id
                    , shipper_attributes.parent_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.sales_channel
                    , scheduled_chat_messages.flow
                    , lower(scheduled_chat_messages.system_id) as system_id
                    , scheduled_chat_messages.created_month
                from scheduled_chat_messages          
                left join order_milestones
                    on order_milestones.order_id = scheduled_chat_messages.order_id
                    and order_milestones.system_id = lower(scheduled_chat_messages.system_id)
                left join earliest_pets
                    on earliest_pets.ticket_id = scheduled_chat_messages.ticket_id
                    and earliest_pets.order_id = scheduled_chat_messages.order_id
                left join shipper_attributes
                    on order_milestones.shipper_id = shipper_attributes.id
                    and order_milestones.system_id = shipper_attributes.system_id
                left join seen_messages_cte
                    on seen_messages_cte.pets_ticket_id = scheduled_chat_messages.ticket_id
                    and seen_messages_cte.tracking_id = order_milestones.tracking_id
                left join hubs_enriched
                    on order_milestones.dest_hub_id = hubs_enriched.id
                    and order_milestones.system_id = hubs_enriched.system_id
                where (sales_channel is null or sales_channel != 'Test') 
                -- filter out test shipper account, sales channel don't work as it is under partnerships
                    and shipper_attributes.id not in (72128) 
                    and lower(scheduled_chat_messages.system_id) =  '{{ system_id }}'
                ),

                next_delivery_attempt as (
                    select
                        scheduled_message_base.scheduled_chat_message_id
                        , scheduled_message_base.ticket_id
                        , scheduled_message_base.order_id
                        , scheduled_message_base.message_status
                        , scheduled_message_base.delivery_failure_reason_id
                        , scheduled_message_base.outcome
                        , scheduled_message_base.chat_seq_number
                        , scheduled_message_base.is_reminder
                        , scheduled_message_base.response
                        , scheduled_message_base.rts_trigger_datetime
                        , scheduled_message_base.dest_hub_id
                        , scheduled_message_base.dest_hub_region
                        , scheduled_message_base.dest_hub_name
                        , scheduled_message_base.earliest_message_read_datetime
                        , scheduled_message_base.earliest_robochat_pets_flag
                        , scheduled_message_base.scheduled_datetime
                        , scheduled_message_base.responded_datetime
                        , scheduled_message_base.shipper_id
                        , scheduled_message_base.parent_id
                        , scheduled_message_base.shipper_name
                        , scheduled_message_base.sales_channel
                        , flow
                        , scheduled_message_base.system_id
                        , scheduled_message_base.created_month
                        -- Get the next available delivery txn id after robochat as potential use case for matching to invalid POD
                        , min_by(coalesce(delivery_transaction_events.transaction_id,transactions_ba_webform.transaction_id), coalesce(delivery_transaction_events.event_datetime, transactions_ba_webform.event_datetime)) next_delivery_transaction_id


                        -- Get the next delivery datetime after robochat
                        , min(coalesce(delivery_transaction_events.event_datetime, transactions_ba_webform.event_datetime)) next_delivery_transaction_datetime

                        -- Get the next successful delivery datetime after robochat. Only fill in this column for forward delivery delivery success datetime. RTS delivery success should not be considered.
                        , min(coalesce(delivery_transaction_events.event_datetime, transactions_ba_webform.event_datetime)) filter(where coalesce(delivery_transaction_events.status, transactions_ba_webform.status) = 'Success' and rts_trigger_datetime is null) delivery_success_datetime

                        -- Get the next delivery status after robochat
                        , min_by(coalesce(delivery_transaction_events.status,transactions_ba_webform.status), coalesce(delivery_transaction_events.event_datetime, transactions_ba_webform.event_datetime)) next_delivery_transaction_status

                        -- Get the next delivery failure reason after robochat
                        , min_by(coalesce(txn_failure_reason_after_robochat.english_description, txn_failure_reason_after_robochat.description) ,coalesce(delivery_transaction_events.event_datetime, transactions_ba_webform.event_datetime)) next_delivery_transaction_failure_reason

                        -- Get last delivery failure reason before robochat
                        , max_by(failure_reasons.id, txn_before_robochat.event_datetime) last_delivery_failure_reason_id_before_robo_chat
                        , max_by(coalesce(failure_reasons.english_description,failure_reasons.description), txn_before_robochat.event_datetime) last_delivery_failure_reason_before_robo_chat
                    from scheduled_message_base
                    left join delivery_transaction_events
                        on scheduled_message_base.order_id = delivery_transaction_events.order_id
                        and delivery_transaction_events.event_datetime >= scheduled_message_base.responded_datetime
                    left join delivery_transaction_events as transactions_ba_webform
                        on scheduled_message_base.order_id = transactions_ba_webform.order_id
                        and transactions_ba_webform.event_datetime >= coalesce(scheduled_message_base.responded_datetime, scheduled_message_base.updated_datetime)
                        and flow = 'BAD_ADDRESS'
                        and scheduled_message_base.response is null
                        and scheduled_message_base.message_status = 'RESPONDED_VIA_WEBFORM'
                    left join delivery_transaction_events txn_before_robochat
                        on scheduled_message_base.order_id = txn_before_robochat.order_id
                        and (txn_before_robochat.event_datetime < scheduled_message_base.creation_datetime)
                        and txn_before_robochat.valid_flag = 1
                    left join transaction_failure_reason
                        on txn_before_robochat.transaction_id = transaction_failure_reason.transaction_id
                    left join failure_reasons
                        on transaction_failure_reason.failure_reason_id = failure_reasons.id
                        and failure_reasons.system_id = '{{ system_id }}'
                    left join transaction_failure_reason txn_failure_reason_after_robochat_mapping
                        on coalesce(delivery_transaction_events.transaction_id, transactions_ba_webform.transaction_id) = txn_failure_reason_after_robochat_mapping.transaction_id
                    left join failure_reasons txn_failure_reason_after_robochat
                        on txn_failure_reason_after_robochat_mapping.failure_reason_id = txn_failure_reason_after_robochat.id
                    group by {{range(1,25) | join(',')}}
                ),

                calendar_calc as (

                    select
                        next_delivery_attempt.*
                        , next_working_day_1 as next_working_day_n1
                        , next_working_day_2 as next_working_day_n2
                        , case when flow = 'MAX_ATTEMPT' then 1 else 0 end as max_attempt_flag
                    from next_delivery_attempt
                    left join calendar
                        on date(next_delivery_attempt.responded_datetime) = calendar.date
                        and next_delivery_attempt.system_id = calendar.system_id
                        and calendar.system_id != 'my'
                    where
                        -- Filter out Messages that were triggered during RTS leg
                        (next_delivery_attempt.scheduled_datetime < next_delivery_attempt.rts_trigger_datetime or next_delivery_attempt.rts_trigger_datetime is null)
                ),

                final_view as (
                    select
                        *
                        , case when flow = 'BAD_ADDRESS' then 1 else 0 end as bad_address_flag
                        , case when flow = 'CANCELLATION' then 1 else 0 end as cancellation_flag
                from calendar_calc

                )

                select *
                from final_view
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                }
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ROBOCHAT_MESSAGES_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()