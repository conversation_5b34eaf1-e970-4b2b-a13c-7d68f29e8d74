import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_BASE_MASKED + ".py",
    task_name=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_BASE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_SPEED_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_HUB_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_DEPARTMENT_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DP_ORDER_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DRIVER_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
        ),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_PICKUPS,
                view_name="order_pickups",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_HUB_MOVEMENTS,
                view_name="order_hub_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DEPARTMENT_MOVEMENTS,
                view_name="order_department_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_SPEED_REPORT,
                view_name="transit_time_speed_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_HUB_MILESTONES,
                view_name="shipment_hub_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_SCAN_EVENTS,
                view_name="driver_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_ORDER_EVENTS,
                view_name="dp_order_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SHIPMENT_SCANS,
                view_name="trip_shipment_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SCANS,
                view_name="hub_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="flight_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_MAWB,
                view_name="flight_mawb",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="flight_trips",
                jinja_template="""
                select 
                    flight_shipments.id
                    , flight_shipments.arrival_datetime arrival_datetime
                    , flight_info.updated_at updated_at
                    , trip_id 
                    , flight_shipments.deleted_at
                from flight_shipments
                left join flight_mawb
                    on flight_shipments.shipment_ext_awb_id = flight_mawb.mawb_id
                left join flight_info
                    on flight_mawb.flight_info_id = flight_info.id
                where trip_id is not null
                    and lower(flight_shipments.orig_hub_country) = '{{ system_id }}'
                """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),
            base.TransformView(
                view_name="ttsr_base",
                jinja_template="""
                select 
                    order_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) as dest_hub_id
                    , start_clock_datetime
                    , sla_date
                    , pickup_hub_id
                    , coalesce(rts_trigger_datetime, current_date()) as rts_leg_filter
                    , transit_time_speed_report.system_id
                    , first_valid_delivery_attempt_datetime
                    , sc_met
                    , transit_time_speed_report.created_month
                from transit_time_speed_report
                left join hubs_enriched
                    on transit_time_speed_report.dest_hub_id = hubs_enriched.id
                where sc_measured = 1 
                """,
            ),
            base.TransformView(
                view_name="dp_events",
                jinja_template="""
                with dp_events_base as (
                select 
                    dp_order_events.*
                    , dps_enriched.id as dp_id
                    , case when type_id = 19 then 'FROM_DP_TO_CUSTOMER'
                        when type_id = 20 then 'FROM_SHIPPER_TO_DP'
                        when type_id = 21 then 'FROM_DP_TO_DRIVER'
                        when type_id = 22 then 'FROM_DRIVER_TO_DP'
                    else 'REMOVE'
                    end as dp_events
                from dp_order_events 
                left join dps_enriched
                    on cast(dp_order_events.dpms_id as int) = cast(dps_enriched.dpms_id as int)
                )

                select 
                    order_id
                    , system_id
                    , dpms_id as hub_id
                    , dp_events as scan_level
                    , null as shipment_id
                    , 'dp_events' as table
                    , if(dp_events = 'FROM_DP_TO_DRIVER', 2, null) as seq_rnk
                    , min(event_datetime) as scan_datetime
                from dp_events_base
                where dp_events != 'REMOVE'
                group by 1,2,3,4,5,6,7
                """,
            ),
            base.TransformView(
                view_name="order_pickups_cte",
                jinja_template="""
                select 
                    order_pickups.order_id
                    , order_pickups.system_id
                    , coalesce(dp_events.hub_id,order_pickups.success_route_hub_id) as hub_id
                    , 'ORDER_PICKUP' as scan_level
                    , null as shipment_id
                    , 'order_pickup' as table
                    , 1 as seq_rnk
                    , coalesce(dp_events.scan_datetime,success_datetime) as scan_datetime
                from order_pickups
                left join dp_events
                    on order_pickups.order_id = dp_events.order_id
                where coalesce(dp_events.hub_id,success_route_hub_id) is not null
                """,
            ),
            base.TransformView(
                view_name="shipment_hub_scans",
                jinja_template="""
                with shipment_events_base as (
                select
                    shipment_orders_enriched.order_id
                    , shipment_orders_enriched.system_id
                    , shipment_events.hub_id
                    , event as scan_level
                    , shipment_events.shipment_id
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as scan_datetime
                from shipment_events
                left join shipment_orders_enriched
                    on shipment_events.shipment_id = shipment_orders_enriched.shipment_id
                where 
                    shipment_events.hub_system_id = '{{ system_id }}'
                    and event in ('ORDER_ADD_TO_SHIPMENT',
                                    'SHIPMENT_CLOSED', 
                                    'SHIPMENT_VAN_INBOUND',  
                                    'SHIPMENT_HUB_INBOUND', 
                                    'ADD_TO_VAN_INBOUND', 
                                    'ADD_TO_HUB_INBOUND')
                ),

                shipment_events_hub_change_cte as (
                    select 
                        *
                        , if(lag(hub_id) over (partition by order_id order by scan_datetime asc) = hub_id, 0, 1) as hub_change
                from shipment_events_base
                ),

                shipment_events_hub_info as (
                select 
                    order_id
                    , shipment_events_hub_change_cte.system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , scan_level
                    , shipment_id
                    , hub_change
                    , min(scan_datetime) scan_datetime
                from shipment_events_hub_change_cte
                left join hubs_enriched
                    on shipment_events_hub_change_cte.hub_id = hubs_enriched.id
                group by 1,2,3,4,5,6
                )

                select
                    order_id
                    , system_id
                    , hub_id
                    , scan_level
                    , shipment_id
                    , 'shipment_events' as table
                    , null as seq_rnk
                    , scan_datetime
                from shipment_events_hub_info
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="order_hub_inbounds",
                jinja_template="""
                select
                    order_id
                    , '{{ system_id }}' as system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , 'ORDER_HUB_INBOUND' as scan_level
                    , null as shipment_id
                    , 'inbound_scans' as table
                    , 3 as seq_rnk
                    , min(from_utc_timestamp(created_at, '{{ local_timezone }}')) as scan_datetime
                from inbound_scans
                left join hubs_enriched
                    on inbound_scans.hub_id = hubs_enriched.id
                where type = 2
                group by 1,2,3,4,5,6,7
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="order_scans",
                jinja_template="""
                select
                   order_id
                    , hub_country as system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , source as scan_level
                    , null as shipment_id
                    , 'hub_scans' as table
                    , null as seq_rnk
                    , min(from_utc_timestamp(created_at, '{{ local_timezone }}')) as scan_datetime
                from hub_scans
                left join hubs_enriched
                    on hub_scans.hub_id = hubs_enriched.id
                where hub_country = '{{ system_id }}'
                group by 1,2,3,4,5,6,7
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),

            base.TransformView(
                view_name="trip_scans_union",
                jinja_template="""
                with trip_scans_base as (
                {% for trip_table_name, scan_name in trip_table_config.items() %}
                select
                    shipment_id
                    , trip_id
                    , '{{ scan_name }}' scan_level
                    , lower('{{ scan_name }}') as table
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as scan_datetime
                {%- if trip_table_name == 'trip_unscanned_shipments' %}
                    , type
                {%- else %}
                    , null as type
                {%- endif %}
                from {{trip_table_name}}
                {%- if trip_table_name != 'trip_unscanned_shipments' %}
                where deleted_at is null
                {%- else %}
                where deleted_at is null
                {%- endif %}
                {% if not loop.last -%} UNION ALL {%- endif %}
                {% endfor %}
                )

                select 
                    movement_trips_enriched.origin_hub_id
                    , movement_trips_enriched.dest_hub_id
                    , trip_scans_base.shipment_id
                    , movement_trips_enriched.trip_id 
                    , movement_trips_enriched.movement_classification
                    , movement_trips_enriched.actual_start_datetime 
                    , movement_trips_enriched.actual_arrival_datetime
                    , movement_trips_enriched.expected_start_datetime
                    , movement_trips_enriched.expected_duration_min
                    , movement_trips_enriched.completion_datetime
                    , movement_trips_enriched.on_time_start_flag
                    , movement_trips_enriched.on_time_end_flag
                    , movement_trips_enriched.status
                    , coalesce(origin_hub_info.parent_hub_id, origin_hub_info.id) origin_parent_hub_coalesce_id
                    , coalesce(dest_hub_info.parent_hub_id, dest_hub_info.id) dest_parent_hub_coalesce_id
                    , trip_scans_base.type
                    , trip_scans_base.table
                    , trip_scans_base.scan_level
                    , trip_scans_base.scan_datetime
                    , movement_trips_enriched.system_id
                from trip_scans_base
                left join movement_trips_enriched
                    on movement_trips_enriched.trip_id = trip_scans_base.trip_id
                left join shipment_orders_enriched
                    on trip_scans_base.shipment_id = shipment_orders_enriched.shipment_id
                left join hubs_enriched origin_hub_info
                    on movement_trips_enriched.origin_hub_id = origin_hub_info.id
                left join hubs_enriched dest_hub_info
                    on movement_trips_enriched.dest_hub_id = dest_hub_info.id
                where shipment_orders_enriched.order_id is not null
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "trip_table_config": {
                        'trip_shipment_scans': 'MISSING_SCAN'
                        , 'trip_unscanned_shipments': 'STAYOVER'
                    }
                }
            ),

            base.TransformView(
                view_name="trip_scans_final",
                jinja_template="""
                select 
                    shipment_orders_enriched.order_id
                    , trip_scans_union.system_id
                    , if(scan_level = 'STAYOVER', origin_parent_hub_coalesce_id, dest_parent_hub_coalesce_id) as hub_id
                    , trip_scans_union.scan_level
                    , trip_scans_union.shipment_id
                    , trip_scans_union.table
                    , null as seq_rnk
                    , min(if(trip_scans_union.type is null, actual_arrival_datetime, scan_datetime)) as scan_datetime
                from trip_scans_union
                left join shipment_orders_enriched
                    on trip_scans_union.shipment_id = shipment_orders_enriched.shipment_id
                where 1=1
                    and (trip_scans_union.type = 'STAYOVER' or trip_scans_union.type is null)
                group by 1,2,3,4,5,6,7
                """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),

            base.TransformView(
                view_name="warehouse_sweep_final",
                jinja_template="""
                with warehouse_sweeps_filtered as (
                select
                    order_id
                    , '{{ system_id }}' as system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , 'WAREHOUSE_SWEEP' as scan_level
                    , null as shipmemt_id
                    , 'warehouse_sweeps' as table
                    , min(from_utc_timestamp(created_at, '{{ local_timezone }}')) as scan_datetime
                from warehouse_sweeps
                left join hubs_enriched
                    on warehouse_sweeps.hub_id = hubs_enriched.id
                group by 1,2,3,4,5,6
                ),

                driver_inbound_scan_cte as (
                    select
                        order_id
                        , driver_scan_events.system_id as system_id
                        , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                        , 'WAREHOUSE_SWEEP' as scan_level
                        , null as shipment_id
                        , 'warehouse_sweeps' as table
                        , min(event_datetime) as scan_datetime
                    from driver_scan_events
                    left join hubs_enriched
                        on driver_scan_events.route_hub_id = hubs_enriched.id
                    group by 1,2,3,4,5,6
                )

                select
                    order_milestones.order_id
                    , order_milestones.system_id
                    , coalesce(warehouse_sweeps_filtered.hub_id, driver_inbound_scan_cte.hub_id) as hub_id
                    , 'WAREHOUSE_SWEEP' as scan_level
                    , null as shipment_id
                    , 'warehouse_sweeps' as table
                    , null as seq_rnk
                    , coalesce(warehouse_sweeps_filtered.scan_datetime, driver_inbound_scan_cte.scan_datetime) as scan_datetime
                from order_milestones
                left join warehouse_sweeps_filtered
                    on order_milestones.order_id = warehouse_sweeps_filtered.order_id
                    and order_milestones.dest_hub_id = warehouse_sweeps_filtered.hub_id
                left join driver_inbound_scan_cte
                    on order_milestones.order_id = driver_inbound_scan_cte.order_id
                    and order_milestones.dest_hub_id = driver_inbound_scan_cte.hub_id
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="flight_scans_start",
                jinja_template="""
                select 
                    shipment_orders_enriched.order_id
                    , movement_trips_enriched.system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , 'AIRPORT HUB INBOUND' as scan_level
                    , flight_trips.id as shipment_id
                    , 'flight_scans' as table
                    , null as seq_rnk
                    , actual_start_datetime as scan_datetime
                from flight_trips
                left join movement_trips_enriched
                    on flight_trips.trip_id = movement_trips_enriched.trip_id
                left join shipment_orders_enriched
                    on flight_trips.id = shipment_orders_enriched.shipment_id
                left join hubs_enriched
                    on movement_trips_enriched.dest_hub_id = hubs_enriched.id
                where 
                    flight_trips.deleted_at is null 
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="flight_scans_arrival",
                jinja_template="""
                select
                    shipment_orders_enriched.order_id
                    , movement_trips_enriched.system_id
                    , coalesce(hubs_enriched.parent_hub_id, hubs_enriched.id) hub_id
                    , 'AIRPORT HUB INBOUND' as scan_level
                    , flight_trips.id as shipment_id
                    , 'flight_scans' as table
                    , null as seq_rnk
                    , actual_arrival_datetime as scan_datetime
                from flight_trips
                left join movement_trips_enriched
                    on flight_trips.trip_id = movement_trips_enriched.trip_id
                left join shipment_orders_enriched
                    on flight_trips.id = shipment_orders_enriched.shipment_id
                left join hubs_enriched
                    on movement_trips_enriched.dest_hub_id = hubs_enriched.id
                where
                    flight_trips.deleted_at is null
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="union_cte",
                jinja_template="""
                select * 
                from order_hub_inbounds
                union all
                select * 
                from order_pickups_cte
                union all
                select * 
                from shipment_hub_scans 
                union all
                select * 
                from trip_scans_final
                union all
                select * 
                from flight_scans_start
                union all
                select * 
                from flight_scans_arrival
                union all 
                select * 
                from dp_events
                union all 
                select * 
                from warehouse_sweep_final
                """,
            ),
            base.TransformView(
                view_name="sequence_cte",
                jinja_template="""
                with union_base as (
                select 
                    ttsr_base.order_id
                    , union_cte.hub_id
                    , union_cte.scan_level
                    , union_cte.shipment_id
                    , union_cte.scan_datetime
                    , union_cte.table
                    , ttsr_base.system_id
                    , start_clock_datetime
                    , sla_date
                    , dest_hub_id
                    , pickup_hub_id
                    , first_valid_delivery_attempt_datetime
                    , sc_met
                    , created_month
                    , union_cte.seq_rnk
                    , if(lag(hub_id) over (partition by ttsr_base.order_id order by scan_datetime asc, seq_rnk asc) = hub_id, 0, 1) as hub_change
                from ttsr_base
                left join union_cte
                on union_cte.order_id = ttsr_base.order_id
                    and scan_datetime < rts_leg_filter
                    and scan_datetime <= first_valid_delivery_attempt_datetime
                ),

                union_base_cumsum_cte as (
                select 
                   union_base.*
                    , SUM(hub_change) over ( PARTITION BY (order_id) ORDER BY scan_datetime, seq_rnk ROWS BETWEEN unbounded preceding AND CURRENT ROW ) cumsum 
                from union_base
                ),

                union_base_event_datetime_cte as (
                    select
                        order_id
                        , system_id
                        , hub_id
                        , start_clock_datetime
                        , sla_date
                        , dest_hub_id
                        , cumsum
                        , first_valid_delivery_attempt_datetime
                        , sc_met
                        , created_month
                        , max_by(shipment_id, cumsum) shipment_id
                        , max(if(scan_level = 'MISSING_SCAN', (scan_datetime), null)) as missing_scan
                        {% for scan_event_name, scan_event_col in events_mapping.items() %}
                        , max(if(scan_level = '{{scan_event_name}}', (scan_datetime), null)) as {{scan_event_col}}_datetime
                        {% endfor %}
                    from union_base_cumsum_cte
                    group by {{ range(1, 11) | join(',') }}
                )
                    select * 
                        , if(order_pickup_datetime is not null, hub_id, null) pickup_hub_id
                        , row_number() over (partition by order_id order by cumsum asc) as hub_seq
                        , lead(hub_id) over (partition by order_id order by cumsum asc) as next_hub_id
                        , lag(hub_id) over (partition by order_id order by cumsum asc) as previous_hub_id
                    from union_base_event_datetime_cte
                """,
                jinja_arguments={
                    'events_mapping': {
                        'FROM_DP_TO_CUSTOMER': 'FROM_DP_TO_CUSTOMER'.lower()
                        , 'FROM_SHIPPER_TO_DP': 'FROM_SHIPPER_TO_DP'.lower()
                        , 'FROM_DRIVER_TO_DP': 'FROM_DRIVER_TO_DP'.lower()
                        , 'FROM_DP_TO_DRIVER': 'FROM_DP_TO_DRIVER'.lower()
                        , 'ORDER_PICKUP': 'ORDER_PICKUP'.lower()
                        , 'ORDER_HUB_INBOUND': 'ORDER_HUB_INBOUND'.lower()
                        , 'WAREHOUSE_SWEEP': 'WAREHOUSE_SWEEP'.lower()
                        , 'ORDER_ADD_TO_SHIPMENT': 'ORDER_ADD_TO_SHIPMENT'.lower()
                        , 'SHIPMENT_CLOSED': 'SHIPMENT_CLOSED'.lower()
                        , 'ADD_TO_VAN_INBOUND': 'ADD_TO_VAN_INBOUND'.lower()
                        , 'SHIPMENT_VAN_INBOUND': 'SHIPMENT_VAN_INBOUND'.lower()
                        , 'ADD_TO_HUB_INBOUND': 'ADD_TO_HUB_INBOUND'.lower()
                        , 'SHIPMENT_HUB_INBOUND': 'SHIPMENT_HUB_INBOUND'.lower()
                        , 'STAYOVER': 'STAYOVER'.lower()
                    }
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with land_scanned_shipments as (
                    select
                        origin_hub_id
                        , shipment_id
                        , trip_id
                        , scan_datetime
                        , movement_classification
                        , actual_start_datetime 
                        , actual_arrival_datetime
                        , expected_start_datetime
                        , expected_duration_min
                        , completion_datetime
                        , on_time_start_flag
                        , on_time_end_flag
                        , dest_hub_id as trip_dest_hub_id
                    from trip_scans_union
                    where lower(status) = 'completed'
                    group by {{ range(1, 14) | join(',') }}
                ),

                flight_scanned_shipments as (
                    select movement_trips_enriched.origin_hub_id
                        , flight_trips.id as shipment_id
                        , movement_trips_enriched.trip_id 
                        , movement_trips_enriched.movement_classification
                        , movement_trips_enriched.actual_start_datetime 
                        , movement_trips_enriched.actual_arrival_datetime
                        , movement_trips_enriched.expected_start_datetime
                        , movement_trips_enriched.expected_duration_min
                        , movement_trips_enriched.completion_datetime
                        , movement_trips_enriched.on_time_start_flag
                        , movement_trips_enriched.on_time_end_flag
                        , movement_trips_enriched.dest_hub_id as trip_dest_hub_id
                    from flight_trips
                    left join movement_trips_enriched
                        on movement_trips_enriched.trip_id = flight_trips.trip_id
                    where lower(movement_trips_enriched.status) = 'completed'
                ),

                all_trips_union as (
                    select
                        origin_hub_id
                        , shipment_id
                        , trip_id
                        , movement_classification
                        , actual_start_datetime 
                        , actual_arrival_datetime
                        , expected_start_datetime
                        , expected_duration_min
                        , completion_datetime
                        , on_time_start_flag
                        , on_time_end_flag
                        , trip_dest_hub_id
                    from land_scanned_shipments
                    union all
                    select * from flight_scanned_shipments
                ),

                all_trips as (
                    select
                        origin_hub_id
                        , shipment_id
                        , trip_id
                        , movement_classification
                        , actual_start_datetime 
                        , actual_arrival_datetime
                        , expected_start_datetime
                        , expected_duration_min
                        , completion_datetime
                        , on_time_start_flag
                        , on_time_end_flag
                        , trip_dest_hub_id
                    from all_trips_union
                    group by {{ range(1, 13) | join(',') }}
                ),

                trip_orders as ( 
                select 
                    sequence_cte.*
                    , all_trips.trip_id as outbound_trip_id
                    , all_trips.movement_classification
                    , all_trips.actual_start_datetime
                    , all_trips.actual_arrival_datetime
                    , expected_duration_min
                    , expected_start_datetime
                    , completion_datetime trip_completion_datetime
                    , on_time_start_flag
                    , on_time_end_flag
                    , trip_dest_hub_id
                from sequence_cte
                left join all_trips
                    on sequence_cte.shipment_id = all_trips.shipment_id
                    and sequence_cte.hub_id = all_trips.origin_hub_id
                    and sequence_cte.next_hub_id = all_trips.trip_dest_hub_id
                ),
                last_mile_route as (
                select 
                    order_id
                    , hub_id
                    , min(entry_datetime) entry_datetime
                from order_department_movements
                where department = 'last_mile'
                    and location_type = 'ROUTE'
                    and rts_flag = 0
                    and entry_datetime is not null
                group by 1,2
                )

                select 
                    trip_orders.*
                    , entry_datetime driver_inb_datetime                                 
                from trip_orders
                left join last_mile_route
                    on trip_orders.order_id = last_mile_route.order_id
                    and trip_orders.hub_id = last_mile_route.hub_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_BASE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()