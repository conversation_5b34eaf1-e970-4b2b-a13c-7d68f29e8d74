import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_FRAUD_PERFORMANCE_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_FRAUD_PERFORMANCE_MASKED,
    depends_on=(
        data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FRAUD_PREDICTION_MASKED,
        data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_OPTIMISED_WORKING_REPORT_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.LAZADA_INTERCEPTED_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SLABreachDAG.DAG_ID,
            task_id=data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    latest_partition = "/measurement_datetime=latest/"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).FMO_DATA_CLAIM_TO_BI_CLAIM,
                view_name="claim",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_FLAGGED_ORDERS_OWR,
                view_name="flagged_by_owr",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_FLAGGED_ORDERS_ML_MODEL,
                view_name="flagged_by_ml",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_PHOTO_REQUEST,
                view_name="photo_request",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_PHOTO_RESPONSE,
                view_name="photo_response",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_INTERCEPTED_ORDERS,
                view_name="lazada_intercepted_orders",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Read all measurement datetime of lazada_orders_enriched
    spark.read.format("parquet").load(
        versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_ENRICHED) \
        .createOrReplaceTempView("lazada_orders_enriched")

    # Read lazada_optimised_working_report
    spark.read.format("parquet").load(
        versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_OPTIMISED_WORKING_REPORT + latest_partition) \
        .createOrReplaceTempView("lazada_optimised_working_report")

    # Read lazada_hv_order_fraud_prediction
    spark.read.format("parquet").load(
        versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FRAUD_PREDICTION + latest_partition) \
        .createOrReplaceTempView("lazada_hv_order_fraud_prediction")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orders_flagged_by_working_report",
                jinja_template="""

                -- Filter for orders flagged by working report
                -- Working report is no longer used by CL team anymore
                -- It is used to ensure that claims from the working report are kept to a minimum

                select
                    order_id
                    , min(first_flag_date) as first_flag_date
                from lazada_orders_enriched
                where
                    first_flag_date is not null
                group by 1

                """,
            ),
            base.TransformView(
                view_name="orders_flagged_by_ml_model",
                jinja_template="""

                -- Filter for orders flagged by ML model

                (

                -- Get orders flagged by old ML model
                -- Orders were flagged on a gsheet

                select
                    distinct order_id
                from flagged_by_ml
                where date(process_date) >= date('2024-06-18')

                )

                union all

                (

                -- Get orders by ML model in production
                -- Model has been in use since 2024-08-14

                select
                    order_id
                from lazada_hv_order_fraud_prediction
                where
                    inbound_date >= date('2024-08-14')
                    and prediction = 1

                )

                """,
            ),
            base.TransformView(
                view_name="orders_flagged_by_owr",
                jinja_template="""

                -- Filter for orders flagged by optimised working report (OWR)

                (

                -- Get orders flagged by optimised working report logic
                -- Orders were flagged on a gsheet
                select
                    order_id
                    , max_by(sum_of_rules,sum_of_rules) as sum_of_rules
                    , max_by(mitra_registration_check,sum_of_rules) as mitra_registration_check
                    , max_by(weight_check,sum_of_rules) as weight_check
                    , max_by(cod_insurance_value_pct_gap_check,sum_of_rules) as cod_insurance_value_pct_gap_check
                    , max_by(price_check,sum_of_rules) as price_check
                    , max_by(hvi_completion_rate_check,sum_of_rules) as hvi_completion_rate_check
                    , max_by(hv_cod_orders_by_new_seller_check,sum_of_rules) as hv_cod_orders_by_new_seller_check
                    , 1 as rank_by_value
                    , 0.01 as pct_rank_by_value
                    , 'id' as system_id
                from flagged_by_owr
                where date(process_date) >= date('2024-06-18')
                group by order_id, rank_by_value, pct_rank_by_value, system_id

                )

                union all

                (

                -- Get orders by optimised working report logic in production
                -- Logic has been in use since 2024-08-14

                select
                    order_id
                    , sum_of_rules
                    , mitra_registration_check
                    , weight_check
                    , cod_insurance_value_pct_gap_check
                    , price_check
                    , hvi_completion_rate_check
                    , hv_cod_orders_by_new_seller_check
                    , rank_by_value
                    , pct_rank_by_value
                    , system_id
                from lazada_optimised_working_report
                where
                    inbound_date between date('2024-08-14') and date('2024-08-19')
                    or (
                        inbound_date >= date('2024-08-20')
                        and (sum_of_rules >= 1 or pct_rank_by_value <= 0.1)                        
                    )

                )

                """,
            ),
            base.TransformView(
                view_name="photo_status",
                jinja_template="""

                -- Get orders with photo requests and photo responses

                select
                    photo_request.tracking_id
                    , max_by(
                        to_date(photo_request.date, 'M/d/yyyy')
                        , to_date(photo_request.date, 'M/d/yyyy')
                    ) as photo_request_date
                    , max_by(
                        photo_request.type
                        , to_date(photo_request.date, 'M/d/yyyy')
                    ) as photo_request_party
                    , max_by(
                        photo_request.hub
                        , to_date(photo_request.date, 'M/d/yyyy')
                    ) as photo_request_hub
                    , max_by(
                        photo_request.region
                        , to_date(photo_request.date, 'M/d/yyyy')
                    ) as photo_request_region
                    , max_by(
                        if(photo_response.tracking_id is not null,1,0)
                        , to_date(photo_request.date, 'M/d/yyyy')
                    ) as photo_received_flag
                from photo_request
                left join photo_response on
                    photo_request.tracking_id = photo_response.tracking_id
                where
                    photo_request.tracking_id is not null
                group by 1

                """,
            ),
            base.TransformView(
                view_name="claims_enriched",
                jinja_template="""
        
                select
                    tracking_id
                    , max_by(to_date(datetime, 'd-MMM-yy'),to_date(datetime, 'd-MMM-yy')) as claim_date
                    , max_by(claim_type,to_date(datetime, 'd-MMM-yy')) as claim_type
                    , max_by(claim_amount,to_date(datetime, 'd-MMM-yy')) as claim_amount
                from claim
                group by 1

        """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    order_milestones.tracking_id
                    , order_milestones.order_id
                    , order_milestones.granular_status
                    , order_milestones.creation_datetime
                    , order_milestones.platform_creation_datetime
                    , order_milestones.inbound_datetime
                    , date(order_milestones.inbound_datetime) as inbound_date
                    , order_milestones.cod_value
                    , order_milestones.insurance_value
                    , if(
                        greatest(order_milestones.insurance_value,order_milestones.cod_value) >= 500000
                        , 1
                        , 0
                    ) as hv_flag
                    , if(
                        greatest(order_milestones.insurance_value,order_milestones.cod_value) >= 5000000
                        , 5000000
                        , greatest(order_milestones.insurance_value,order_milestones.cod_value)
                    ) as potential_claim_value
                    , shipper_attributes.id as shipper_id
                    , shipper_attributes.shipper_name
                    , order_milestones.seller_id
                    , order_milestones.seller_name
                    , order_milestones.lazmall_flag
                    , if(
                        orders_flagged_by_working_report.order_id is not null
                        , 1
                        , 0
                    ) as working_report_flag
                    , orders_flagged_by_working_report.first_flag_date as working_report_first_flag_date
                    , if(
                        orders_flagged_by_ml_model.order_id is not null
                        , 1
                        , 0
                    ) as ml_flag
                    , if(
                        orders_flagged_by_owr.order_id is not null
                        , 1
                        , 0
                    ) as owr_flag
                    , orders_flagged_by_owr.sum_of_rules as sum_of_rules
                    , orders_flagged_by_owr.mitra_registration_check as mitra_registration_check
                    , orders_flagged_by_owr.weight_check as weight_check
                    , orders_flagged_by_owr.cod_insurance_value_pct_gap_check as cod_insurance_value_pct_gap_check
                    , orders_flagged_by_owr.price_check as price_check
                    , orders_flagged_by_owr.hvi_completion_rate_check as hvi_completion_rate_check
                    , orders_flagged_by_owr.hv_cod_orders_by_new_seller_check as hv_cod_orders_by_new_seller_check
                    , orders_flagged_by_owr.rank_by_value
                    , orders_flagged_by_owr.pct_rank_by_value
                    , photo_status.photo_request_date
                    , if(
                        photo_status.tracking_id is not null
                        , 1
                        , 0
                    ) as photo_requested_flag
                    , photo_status.photo_request_party
                    , photo_status.photo_request_region
                    , photo_status.photo_request_hub
                    , photo_status.photo_received_flag
                    , if(
                        lazada_intercepted_orders.interception_attempted_timestamp is not null
                        , 1
                        , 0
                    ) as interception_attempted_flag
                    , lazada_intercepted_orders.interception_attempted_timestamp
                    , if(
                        lazada_intercepted_orders.intercepted_active_seller_timestamp is not null
                            or lazada_intercepted_orders.intercepted_deactivated_seller_timestamp is not null
                        , 1
                        , 0
                    ) as intercepted_flag
                    , lazada_intercepted_orders.intercepted_active_seller_timestamp
                    , lazada_intercepted_orders.intercepted_deactivated_seller_timestamp
                    , claims_enriched.claim_date
                    , claims_enriched.claim_type
                    , claims_enriched.claim_amount
                    , order_milestones.system_id
                    , order_milestones.created_month
                from order_milestones
                join shipper_attributes on
                    order_milestones.shipper_id = shipper_attributes.id
                left join orders_flagged_by_working_report on
                    order_milestones.order_id = orders_flagged_by_working_report.order_id
                left join orders_flagged_by_ml_model on
                    order_milestones.order_id = orders_flagged_by_ml_model.order_id
                left join orders_flagged_by_owr on
                    order_milestones.order_id = orders_flagged_by_owr.order_id
                left join photo_status on
                    order_milestones.tracking_id = photo_status.tracking_id
                left join lazada_intercepted_orders on
                    order_milestones.order_id = lazada_intercepted_orders.order_id
                left join claims_enriched on
                    order_milestones.tracking_id = claims_enriched.tracking_id
                where
                    shipper_attributes.parent_id_coalesce = 341107

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_FRAUD_PERFORMANCE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    run(spark, task_config)
    spark.stop()