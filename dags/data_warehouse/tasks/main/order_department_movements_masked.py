import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_DEPARTMENT_MOVEMENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_DEPARTMENT_MOVEMENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_HUB_MILESTONES,
                view_name="shipment_hub_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipment_movements",
                jinja_template="""
                SELECT *
                FROM
                    (SELECT shipment_id
                           , hub_id
                           , origin_hub_flag
                           , destination_hub_flag
                           , transit_hub_flag
                           , explode(map('van_inbound', van_inbound_datetime, 'hub_inbound', hub_inbound_datetime))
                             AS (type, entry_datetime)
                    FROM shipment_hub_milestones AS shp_milestones
                    WHERE invalid_transit_hub_flag = 0
                {%- if system_id == 'id' %}
                          AND shipment_type <> 'OTHERS'
                {%- endif %}
                    )
                WHERE entry_datetime IS NOT NULL
                      AND ((origin_hub_flag = 1 AND type = 'van_inbound')
                           OR (destination_hub_flag = 1 AND type = 'hub_inbound')
                           OR transit_hub_flag = 1)
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                (SELECT NULL AS order_event_id
                       , order_mvt.system_id AS country
                       , order_mvt.order_id
                       , order_mvt.granular_status
                       , 'SHIPMENT' AS location_type
                       , order_mvt.location_id
                       , shp_mvt.hub_id
                       , hub.name AS hub_name
                       , hub.region AS hub_region
                       , cast(CASE
                                  WHEN orders.rts_trigger_datetime IS NULL THEN 0
                                  WHEN shp_mvt.entry_datetime < orders.rts_trigger_datetime THEN 0
                                  WHEN shp_mvt.entry_datetime >= orders.rts_trigger_datetime THEN 1
                              END AS bigint) AS rts_flag
                       , CASE
                             WHEN shp_mvt.type = 'van_inbound' THEN 'middle_mile'
                             WHEN shp_mvt.destination_hub_flag = 1 THEN 'middle_mile_staging_dest'
                             WHEN shp_mvt.transit_hub_flag = 1 THEN 'middle_mile_staging_transit'
                         END AS department
                       , shp_mvt.entry_datetime
                       , orders.created_month
                       , order_mvt.system_id
                FROM shipment_movements AS shp_mvt
                INNER JOIN order_movements AS order_mvt ON order_mvt.location_id = shp_mvt.shipment_id
                    AND order_mvt.location_type = 'SHIPMENT'
                    AND order_mvt.entry_datetime <= shp_mvt.entry_datetime
                    AND order_mvt.exit_datetime >= shp_mvt.entry_datetime
                INNER JOIN order_milestones AS orders ON orders.order_id = order_mvt.order_id
                LEFT JOIN hubs_enriched AS hub ON hub.id = shp_mvt.hub_id)
                UNION ALL
                (SELECT movements.order_event_id
                       , movements.country
                       , movements.order_id
                       , movements.granular_status
                       , movements.location_type
                       , movements.location_id
                       , movements.hub_id
                       , hub.name AS hub_name
                       , hub.region AS hub_region
                       , cast(CASE
                                  WHEN orders.rts_trigger_datetime IS NULL THEN 0
                                  WHEN movements.entry_datetime < orders.rts_trigger_datetime THEN 0
                                  WHEN movements.entry_datetime >= orders.rts_trigger_datetime THEN 1
                              END AS bigint) AS rts_flag
                {%- if system_id == 'ph' %}
                       , CASE
                             WHEN movements.location_type = 'ROUTE'
                                  AND movements.hub_id = orders.pickup_hub_id
                             THEN 'first_mile'
                             WHEN movements.location_type = 'ROUTE'
                             THEN 'last_mile'
                             WHEN movements.location_type = 'HUB'
                                  AND hub.sort_hub_flag = 1
                             THEN 'sort'
                             WHEN movements.location_type = 'HUB'
                                  AND hub.facility_type = 'RECOVERY'
                             THEN 'recovery'
                             WHEN movements.location_type = 'HUB'
                                  AND hub.facility_type = 'OTHERS'
                             THEN 'others'
                             WHEN movements.location_type = 'HUB'
                                  AND hub.name LIKE 'CONSOL-%'
                             THEN 'middle_mile_consol'
                             WHEN movements.location_type = 'HUB'
                                  AND movements.hub_id IN (orders.pickup_hub_id, orders.inbound_hub_id)
                             THEN 'first_mile'
                             WHEN movements.location_type = 'HUB'
                             THEN 'last_mile'
                             WHEN movements.location_type = 'SHIPMENT'
                             THEN 'middle_mile_staging_origin'
                             WHEN movements.location_type = 'DP'
                             THEN 'dp'
                         END
                {%- else %}
                       , CASE
                             WHEN movements.location_type = 'ROUTE'
                                  AND movements.hub_id = orders.pickup_hub_id
                             THEN 'first_mile'
                             WHEN movements.location_type = 'ROUTE'
                             THEN 'last_mile'
                             WHEN movements.location_type = 'HUB'
                                  AND hub.sort_hub_flag = 1
                             THEN 'sort'
                             WHEN movements.location_type = 'HUB'
                                  AND movements.hub_id IN (orders.pickup_hub_id, orders.inbound_hub_id)
                             THEN 'first_mile'
                             WHEN movements.location_type = 'HUB'
                             THEN 'last_mile'
                             WHEN movements.location_type = 'SHIPMENT'
                             THEN 'middle_mile_staging_origin'
                             WHEN movements.location_type = 'DP'
                             THEN 'dp'
                         END
                  {%- endif %} AS department
                       , movements.entry_datetime
                       , orders.created_month
                       , movements.system_id
                FROM order_movements AS movements
                INNER JOIN order_milestones AS orders ON orders.order_id = movements.order_id
                LEFT JOIN hubs_enriched AS hub ON hub.id = movements.hub_id)
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="base_w_window",
                jinja_template="""
                SELECT base.*
                       , LEAD(entry_datetime, 1) OVER(PARTITION BY order_id
                                                      ORDER BY entry_datetime, order_event_id) AS exit_datetime
                       , SUM(CASE
                                  WHEN LAG(department, 1) OVER(PARTITION BY order_id
                                                               ORDER BY entry_datetime, order_event_id) = department
                                       AND LAG(hub_id, 1) OVER(PARTITION BY order_id
                                                               ORDER BY entry_datetime, order_event_id) = hub_id
                                  THEN 0
                                  ELSE 1
                             END)
                             OVER(PARTITION BY order_id
                                  ORDER BY entry_datetime, order_event_id) AS department_sequence
                FROM base
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.country
                       , base.order_event_id
                       , base.order_id
                       , base.granular_status
                       , base.location_type
                       , base.location_id
                       , base.hub_id
                       , base.hub_name
                       , base.hub_region
                       , base.rts_flag
                       , base.department
                       , base.entry_datetime
                       , base.exit_datetime
                       , datediff(base.exit_datetime, base.entry_datetime) AS duration_days
                       , cast(round((to_unix_timestamp(exit_datetime,'yyyy-MM-dd HH:mm:ss')
                                    - to_unix_timestamp(entry_datetime, 'yyyy-MM-dd HH:mm:ss'))/60
                                    , 0) AS bigint) AS duration_minutes
                       , base.department_sequence
                       , base.created_month
                FROM base_w_window AS base
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DEPARTMENT_MOVEMENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
