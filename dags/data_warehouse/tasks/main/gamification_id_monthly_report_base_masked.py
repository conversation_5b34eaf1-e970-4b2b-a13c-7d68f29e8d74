import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_MONTHLY_REPORT_BASE_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_MONTHLY_REPORT_BASE_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_DAILY_REPORT_MASKED,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime.subtract(hours=18), measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_DAILY_REPORT,
                view_name="gamification_id_daily_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PARCEL_RATES,
                view_name="parcel_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_ZONE_DATA_ADJUSTED,
                view_name="id_zone_data",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                
                with base as (
                
                    select
                        system_id
                        , created_month
                        , driver_id
                        , driver_display_name
                        , driver_hub_region
                        , driver_hub_id
                        , monthly_density_category
                        , monthly_umk_category
                        , monthly_umk_value
                        , sum(case when delivered_parcel_count >= 1 then 1 else 0 end) as days_worked
                        , sum(case when density_category in ('ex','ey','ez') and delivered_parcel_count >= 1 then 1 else 0 end) as e_days_worked
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky 
                        , sum(delivered_non_rts_fs_regular) as delivered_non_rts_fs_regular
                        , sum(delivered_non_rts_fs_bulky) as delivered_non_rts_fs_bulky
                        , sum(delivered_non_rts_cs_regular) as delivered_non_rts_cs_regular
                        , sum(delivered_non_rts_cs_bulky) as delivered_non_rts_cs_bulky
                        , sum(delivered_non_rts_retail_regular) as delivered_non_rts_retail_regular
                        , sum(delivered_non_rts_retail_bulky) as delivered_non_rts_retail_bulky
                        , sum(delivered_non_rts_standard_regular) as delivered_non_rts_standard_regular
                        , sum(delivered_non_rts_standard_bulky) as delivered_non_rts_standard_bulky
                        , sum(delivered_non_rts_fs_cs_retail_regular) as delivered_non_rts_fs_cs_retail_regular
                        , sum(delivered_non_rts_fs_cs_retail_bulky) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(delivered_non_rts_superbulky) as delivered_non_rts_superbulky
                        , sum(delivered_non_rts_bulky_revise) as delivered_non_rts_bulky_revise
                        , sum(delivered_rts_regular) as delivered_rts_regular
                        , sum(delivered_rts_bulky) as delivered_rts_bulky
                        , sum(planned_parcel_count) as planned_parcel_count
                        , sum(delivered_parcel_count) as delivered_parcel_count
                        , sum(failed_parcel_count) as failed_parcel_count
                        , sum(total_ocd_points) as total_ocd_points
                        , sum(base_parcel_pay) as total_parcel_pay
                        , sum(volume_bonus) as total_volume_bonus

                        , sum(amount) as amount
                        , sum(ebc_count_1) as ebc_count_1
                        , sum(ebc_amount_1)/sum(ebc_count_1) as ebc_points_1
                        , sum(ebc_amount_1) as ebc_amount_1
                        , sum(ebc_count_2) as ebc_count_2
                        , sum(ebc_amount_2)/sum(ebc_count_2) as ebc_points_2
                        , sum(ebc_amount_2) as ebc_amount_2
                        , sum(ebc_count_3) as ebc_count_3
                        , sum(ebc_amount_3)/sum(ebc_count_3) as ebc_points_3
                        , sum(ebc_amount_3) as ebc_amount_3
                        , sum(ebc_count_4) as ebc_count_4
                        , sum(ebc_amount_4)/sum(ebc_count_4) as ebc_points_4
                        , sum(ebc_amount_4) as ebc_amount_4
                        , sum(ebc_count_5) as ebc_count_5
                        , sum(ebc_amount_5)/sum(ebc_count_5) as ebc_points_5
                        , sum(ebc_amount_5) as ebc_amount_5
                        , sum(ebc_count_6) as ebc_count_6
                        , sum(ebc_amount_6)/sum(ebc_count_6) as ebc_points_6
                        , sum(ebc_amount_6) as ebc_amount_6
                        , sum(ebc_count_7) as ebc_count_7
                        , sum(ebc_amount_7)/sum(ebc_count_7) as ebc_points_7
                        , sum(ebc_amount_7) as ebc_amount_7
                        , sum(ebc_count_8) as ebc_count_8
                        , sum(ebc_amount_8)/sum(ebc_count_8) as ebc_points_8
                        , sum(ebc_amount_8) as ebc_amount_8
                        , sum(ebc_count_9) as ebc_count_9
                        , sum(ebc_amount_9)/sum(ebc_count_9) as ebc_points_9
                        , sum(ebc_amount_9) as ebc_amount_9
                        , sum(volume_bonus) as bonus_daily_aggregate
                        , sum(case when bonus_tier = 0 then 1 else 0 end) as tier_0_bonus_count
                        , sum(case when bonus_tier = 0 then volume_bonus end) as tier_0_bonus_amount
                        , sum(case when bonus_tier = 1 then 1 else 0 end) as tier_1_bonus_count
                        , sum(case when bonus_tier = 1 then volume_bonus end) as tier_1_bonus_amount
                        , sum(case when bonus_tier = 2 then 1 else 0 end) as tier_2_bonus_count
                        , sum(case when bonus_tier = 2 then volume_bonus end) as tier_2_bonus_amount
                        , sum(case when bonus_tier = 3 then 1 else 0 end) as tier_3_bonus_count
                        , sum(case when bonus_tier = 3 then volume_bonus end) as tier_3_bonus_amount
                        , sum(case when bonus_tier = 4 then 1 else 0 end) as tier_4_bonus_count
                        , sum(case when bonus_tier = 4 then volume_bonus end) as tier_4_bonus_amount
                        from gamification_id_daily_report
                    group by {{ range(1, 10) | join(',') }}

                ),
                pre_final as (
                
                    select
                        *
                        , case
                            when monthly_density_category = 'ex' then monthly_umk_value * days_worked

                            when monthly_density_category = 'ev' and total_ocd_points < 320 then 0
                            when monthly_density_category = 'ev' and days_worked >= 15 and total_ocd_points >= 320 then (0.7 * monthly_umk_value * 26)
                            when monthly_density_category = 'ev' and days_worked < 15 and total_ocd_points >= 320 then (0.7 * monthly_umk_value * days_worked)

                            -- ey density expected to be deprecated from feb 2025 onward
                            when monthly_density_category = 'ey' and total_ocd_points < 200 then 0
                            when monthly_density_category = 'ey' and total_ocd_points >= 200 then (0.7 * monthly_umk_value * 26)

                            when monthly_density_category = 'ez' and total_ocd_points < 250 then 0
                            when monthly_density_category = 'ez' and days_worked >= 15 and total_ocd_points >= 250 then (0.7 * monthly_umk_value * 26)
                            when monthly_density_category = 'ez' and days_worked < 15 and total_ocd_points >= 250 then (0.7 * monthly_umk_value * days_worked)

                            else 0.7 * monthly_umk_value * e_days_worked
                            end as monthly_umk_payment
                    from base

                ),
                final as (
                
                        select
                            *
                            , (coalesce(total_parcel_pay,0) + coalesce(total_volume_bonus,0) + coalesce(monthly_umk_payment,0)) as grand_total
                            , created_month as route_month
                        from pre_final

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_MONTHLY_REPORT_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
