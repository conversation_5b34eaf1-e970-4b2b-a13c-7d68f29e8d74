import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_BASE_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENT_PATHS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_ORDERS_ENRICHED_BASE,
                view_name="shipment_orders_enriched_base",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_HUB_MILESTONES,
                view_name="shipment_hub_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_PATHS,
                view_name="shipment_paths",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).VENDORS,
                view_name="vendors",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SEA_VENDORS,
                view_name="sea_vendors",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_DIMENSIONS,
                view_name="shipment_dimensions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs"),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_SEAWAY_BILLS,
                view_name="shipment_seaway_bills",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SEAWAY_BILLS,
                view_name="seaway_bills",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO, view_name="user_info"),
        ),
        version_datetime=measurement_datetime,
    )

    transit_hub_columns = {"first": 1, "second": 2, "third": 3, "fourth": 4}
    order_size_columns = ("xs", "s", "m", "l", "xl", "xxl")
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="user_info_enriched",
                jinja_template="""
                select
                    user_id as id
                    , min_by(nullif(concat_ws(' ', first_name, last_name), ''), created_at) as full_name
                from user_info
                where
                    deleted_at is null
                    and user_id is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="shipment_orders_agg",
                jinja_template="""
                select
                    shipment_id
                    , count_if(add_to_shipment_datetime is not null) as add_to_shipment_orders
                    , count_if(
                        add_to_shipment_datetime is not null and order_inbound_datetime is null
                    ) as add_to_shipment_pending_orders
                    , count_if(
                        add_to_shipment_datetime is not null and order_inbound_datetime is not null
                    ) as add_to_shipment_inbound_orders
                    , count_if(
                        add_to_shipment_datetime is null and order_inbound_datetime is not null
                    ) as extra_inbound_orders

                    {%- for size in order_size_columns %}
                    , count_if(
                        parcel_size = '{{ size }}' and add_to_shipment_datetime is not null
                    ) as add_to_shipment_orders_size_{{ size }}
                    {%- endfor %}

                    , sum(weight) as total_weight
                    , sum(nv_weight) as total_nv_weight
                    , sum(estimated_weight) as total_estimated_order_weight
                    , sum(estimated_volume) as total_estimated_order_volume
                    , min(add_to_shipment_datetime) as add_to_shipment_start_datetime
                    , max(add_to_shipment_datetime) as add_to_shipment_end_datetime
                    , min(order_inbound_datetime) as break_bulk_start_datetime
                    , max(order_inbound_datetime) as break_bulk_end_datetime
                from shipment_orders_enriched_base
                group by 1
                """,
                jinja_arguments={"order_size_columns": order_size_columns},
            ),
            base.TransformView(
                view_name="hub_milestones",
                jinja_template="""
                select
                    milestones.shipment_id
                    , min(milestones.close_datetime) filter (
                        where milestones.origin_hub_flag = 1
                    ) as orig_shipment_close_datetime
                    , min(milestones.van_inbound_datetime) filter (
                        where milestones.origin_hub_flag = 1
                    ) as orig_shipment_van_inbound_datetime

                    {%- for alias, rank in transit_hub_columns.items() %}
                    , min(milestones.hub_id) filter (
                        where milestones.transit_hub_sequence = {{ rank }}
                    ) as {{ alias }}_transit_hub_id
                    , min(milestones.hub_inbound_datetime) filter (
                        where milestones.transit_hub_sequence = {{ rank }}
                    ) as {{ alias }}_transit_shipment_hub_inbound_datetime
                    , min(milestones.van_inbound_datetime) filter (
                        where milestones.transit_hub_sequence = {{ rank }}
                    ) as {{ alias }}_transit_shipment_van_inbound_datetime
                    {%- endfor %}

                    , min_by(
                        milestones.hub_id, least(milestones.van_inbound_datetime, milestones.hub_inbound_datetime)
                    ) filter (where lower(hubs_enriched.name) like 'consol-%') as first_consol_hub_id
                    , min_by(
                        milestones.hub_inbound_datetime
                        , least(milestones.van_inbound_datetime, milestones.hub_inbound_datetime)
                    ) filter (
                        where lower(hubs_enriched.name) like 'consol-%'
                    ) as first_consol_shipment_hub_inbound_datetime
                    , min_by(
                        milestones.van_inbound_datetime
                        , least(milestones.van_inbound_datetime, milestones.hub_inbound_datetime)
                    ) filter (
                        where lower(hubs_enriched.name) like 'consol-%'
                    ) as first_consol_shipment_van_inbound_datetime
                    , min(milestones.hub_inbound_datetime) filter (
                        where milestones.destination_hub_flag = 1
                    ) as dest_shipment_hub_inbound_datetime
                from shipment_hub_milestones as milestones
                left join hubs_enriched on
                    milestones.hub_id = hubs_enriched.id
                    and milestones.system_id = hubs_enriched.system_id
                group by 1
                """,
                jinja_arguments={"transit_hub_columns": transit_hub_columns},
            ),
            base.TransformView(
                view_name="shipment_milestones",
                jinja_template="""
                select
                    shipment_id
                    , max_by(event, created_at) filter (where event <> 'SHIPMENT_CANCELLED') as last_shipment_scan_type
                    , max_by(hub_id, created_at) filter (
                        where event <> 'SHIPMENT_CANCELLED'
                    ) as last_shipment_scan_hub_id
                    , max_by(oauth_client_id, created_at) filter (
                        where event <> 'SHIPMENT_CANCELLED'
                    ) as last_shipment_scan_user_id
                    , max(created_at) filter (where event <> 'SHIPMENT_CANCELLED') as last_shipment_scan_datetime_utc
                    , min(created_at) filter (
                        where event = 'SHIPMENT_FORCE_COMPLETED'
                    ) as shipment_force_success_datetime_utc
                    , max(created_at) filter (where event = 'SHIPMENT_CANCELLED') as shipment_cancellation_datetime_utc
                    , max_by(oauth_client_id, created_at) filter (
                        where event = 'SHIPMENT_CANCELLED'
                    ) as shipment_cancellation_user_id
                from shipment_events
                group by 1
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("shipments.orig_hub_country")},
            ),
            base.TransformView(
                view_name="shipment_scans",
                jinja_template="""
                with
                    scans as (
                    select distinct 
                        shipment_id
                        , trip_id
                        , trip_start_datetime
                    from middle_mile_trip_relationships
                    )

                    , trip_change as (

                        select
                            *
                            , lag(trip_id) over (partition by shipment_id order by trip_start_datetime) as prev_trip_id
                        from scans

                    )
                    , final as (

                        select
                            shipment_id
                            , trip_id
                            , trip_start_datetime
                            , count(*) as trip_used_flag
                            , row_number() over(partition by shipment_id order by trip_start_datetime asc) as rank
                            , row_number() over(partition by shipment_id order by trip_start_datetime desc) as reverse_rank
                        from trip_change
                        where prev_trip_id is null or prev_trip_id != trip_id
                        group by 1,2,3

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="total_trips_used",
                jinja_template="""
                select
                    shipment_id
                    , sum(trip_used_flag) as trips_used
                from shipment_scans
                group by 1
                """,
            ),
            base.TransformView(
                view_name="seaway_bills_cte",
                jinja_template="""
                with
                    base as (

                        select
                            shipment_seaway_bills.shipment_id
                            , max_by(seaway_bills.ref_id, shipment_seaway_bills.created_at) as swb
                            , max_by(seaway_bills.vendor_id, shipment_seaway_bills.created_at) as swb_vendor_id
                        from shipment_seaway_bills
                        left join seaway_bills
                            on shipment_seaway_bills.bill_id = seaway_bills.id
                        group by 1

                    ),
                    final as (

                        select
                            base.*
                            , sea_vendors.name as swb_vendor_name
                        from base
                        left join sea_vendors
                            on base.swb_vendor_id = sea_vendors.id

                    )

                select * from final
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    lower(shipments.orig_hub_country) as country
                    , shipments.id as shipment_id
                    , airway_bills.ref_id as mawb
                    , airway_bills.vendor_id as mawb_vendor_id
                    , vendors.name as mawb_vendor_name
                    , seaway_bills_cte.swb
                    , seaway_bills_cte.swb_vendor_id
                    , seaway_bills_cte.swb_vendor_name
                    , shipments.shipment_type
                    , shipments.status

                    {%- for column in operator_dimension_columns %}
                    , shipment_dimensions.{{ column }} as operator_{{ column }}
                    {%- endfor %}

                    , if(
                        (
                            {%- for column in operator_dimension_columns %}
                            shipment_dimensions.{{ column }} is not null {%- if not loop.last %} and {%- endif %}
                            {%- endfor %}
                        )
                        , 1
                        , 0
                    ) as complete_operator_dimensions_flag

                    , from_utc_timestamp(shipments.sla, {{ get_local_timezone }}) as shipment_sla_datetime
                    , from_utc_timestamp(shipments.arrival_datetime, {{ get_local_timezone }}) as shipment_eta_datetime
                    , first_trip.trip_id as first_trip_id
                    , second_trip.trip_id as second_trip_id
                    , last_trip.trip_id as last_trip_id
                    , total_trips_used.trips_used
                    , shipment_paths.assigned_id_path
                    , shipment_paths.assigned_string_path
                    , shipment_paths.traveled_id_path
                    , shipment_paths.traveled_string_path
                    , shipment_paths.matching_id_path_flag
                    , shipment_paths.matching_string_path_flag
                    , shipments.comments as shipment_comments
                    , from_utc_timestamp(shipments.created_at, {{ get_local_timezone }}) as shipment_creation_datetime
                    , shipments.orig_hub_id
                    , shipments.dest_hub_id
                    , shipments.curr_hub_id
                    , hub_milestones.orig_shipment_close_datetime
                    , hub_milestones.orig_shipment_van_inbound_datetime

                    {%- for alias in transit_hub_columns.keys() %}
                    , hub_milestones.{{ alias }}_transit_hub_id
                    , hub_milestones.{{ alias }}_transit_shipment_hub_inbound_datetime
                    , hub_milestones.{{ alias }}_transit_shipment_van_inbound_datetime
                    {%- endfor %}

                    , hub_milestones.first_consol_hub_id
                    , hub_milestones.first_consol_shipment_hub_inbound_datetime
                    , hub_milestones.first_consol_shipment_van_inbound_datetime
                    , hub_milestones.dest_shipment_hub_inbound_datetime
                    , from_utc_timestamp(
                        shipment_milestones.shipment_force_success_datetime_utc, {{ get_local_timezone }}
                    ) as shipment_force_success_datetime
                    , least(
                        hub_milestones.dest_shipment_hub_inbound_datetime
                        , from_utc_timestamp(
                            shipment_milestones.shipment_force_success_datetime_utc, {{ get_local_timezone }}
                        )
                    ) as shipment_completion_datetime
                    , from_utc_timestamp(
                        shipment_milestones.last_shipment_scan_datetime_utc, {{ get_local_timezone }}
                    ) as last_shipment_scan_datetime
                    , shipment_milestones.last_shipment_scan_type
                    , shipment_milestones.last_shipment_scan_hub_id
                    , shipment_milestones.last_shipment_scan_user_id
                    , from_utc_timestamp(
                        shipment_milestones.shipment_cancellation_datetime_utc, {{ get_local_timezone }}
                    ) as shipment_cancellation_datetime
                    , shipment_milestones.shipment_cancellation_user_id
                    , users.full_name as last_shipment_scan_user_name
                    , coalesce(
                        users.full_name, shipment_milestones.last_shipment_scan_user_id
                    ) as last_shipment_scan_user_coalesce
                    , shipment_orders_agg.add_to_shipment_orders

                    {%- for size in order_size_columns %}
                    , shipment_orders_agg.add_to_shipment_orders_size_{{ size }}
                    {%- endfor %}

                    , shipment_orders_agg.total_weight as total_order_weight
                    , shipment_orders_agg.total_nv_weight as total_order_nv_weight
                    , shipment_orders_agg.total_estimated_order_weight
                    , shipment_orders_agg.total_estimated_order_volume
                    , shipment_orders_agg.add_to_shipment_pending_orders
                    , shipment_orders_agg.add_to_shipment_inbound_orders
                    , shipment_orders_agg.extra_inbound_orders
                    , shipment_orders_agg.add_to_shipment_start_datetime
                    , shipment_orders_agg.add_to_shipment_end_datetime
                    , shipment_orders_agg.break_bulk_start_datetime
                    , shipment_orders_agg.break_bulk_end_datetime

                    {%- for alias, hub_col in hub_columns %}
                    , {{ alias }}_hub.name as {{ alias }}_hub_name
                    , {{ alias }}_hub.region as {{ alias }}_hub_region
                    {%- endfor %}

                    , date_format(shipments.created_at, 'yyyy-MM') as created_month
                    , lower(shipments.orig_hub_country) as system_id
                from shipments
                left join shipment_scans as first_trip
                    on shipments.id = first_trip.shipment_id
                    and first_trip.rank = 1
                left join shipment_scans as second_trip
                    on shipments.id = second_trip.shipment_id
                    and second_trip.rank = 2
                left join shipment_scans as last_trip
                    on shipments.id = last_trip.shipment_id
                    and last_trip.reverse_rank = 1
                left join total_trips_used
                    on shipments.id = total_trips_used.shipment_id
                left join shipment_orders_agg
                    on shipments.id = shipment_orders_agg.shipment_id
                left join hub_milestones
                    on shipments.id = hub_milestones.shipment_id
                left join shipment_milestones
                    on shipments.id = shipment_milestones.shipment_id
                left join shipment_ext_awbs as airway_bills
                    on shipments.shipment_ext_awb_id = airway_bills.id
                left join vendors
                    on airway_bills.vendor_id = vendors.id
                left join shipment_paths
                    on shipments.id = shipment_paths.shipment_id

                {%- for alias, hub_col in hub_columns %}
                left join hubs_enriched as {{ alias }}_hub
                    on {{ hub_col }} = {{ alias }}_hub.id
                    and lower(shipments.orig_hub_country) = {{ alias }}_hub.system_id
                {%- endfor %}

                left join user_info_enriched as users
                    on users.id = shipment_milestones.last_shipment_scan_user_id
                left join shipment_dimensions
                    on shipments.id = shipment_dimensions.shipment_id
                left join seaway_bills_cte
                    on seaway_bills_cte.shipment_id = shipments.id
                where
                    shipments.deleted_at is null
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("shipments.orig_hub_country"),
                    "transit_hub_columns": transit_hub_columns,
                    "order_size_columns": order_size_columns,
                    "hub_columns": (
                        ("orig", "shipments.orig_hub_id"),
                        ("dest", "shipments.dest_hub_id"),
                        ("curr", "shipments.curr_hub_id"),
                        ("first_consol", "first_consol_hub_id"),
                        ("last_shipment_scan", "shipment_milestones.last_shipment_scan_hub_id"),
                        *[
                            (f"{alias}_transit", f"hub_milestones.{alias}_transit_hub_id")
                            for alias in transit_hub_columns.keys()
                        ],
                    ),
                    "operator_dimension_columns": ("width", "height", "length", "weight", "volumetric_weight"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENTS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    # Disabled dynamicPartitionPruning for this task because repeated joins to hubs_enriched with it enabled was causing
    # an unexpected slow down in the query planning phase.
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
