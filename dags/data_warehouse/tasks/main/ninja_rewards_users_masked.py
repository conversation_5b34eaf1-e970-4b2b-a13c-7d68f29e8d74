import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_MASKED + ".py",
    task_name=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(
        data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_BASE_MASKED,
        data_warehouse.EberDAG.Task.NINJA_REWARDS_MONTHLY_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_USERS_BASE,
                view_name="ninja_rewards_users_base",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_MONTHLY,
                view_name="ninja_rewards_monthly",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_month_status",
                jinja_template="""
                select
                    user_id

                    {% for column in latest_month_columns %}
                    , max_by({{ column }}, month) as {{ column }}
                    {%- endfor %}

                from ninja_rewards_monthly
                group by 1
                """,
                jinja_arguments={
                    "latest_month_columns": (
                        "member_tier",
                        "point_balance",
                        "point_balance_lag_1m",
                        "total_redemptions",
                        "cumulative_points_earned",
                        "cumulative_points_redeemed",
                        "cumulative_points_expired",
                    ),
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.id
                    , base.display_name
                    , base.shipper_id
                    , base.parent_user_id_coalesce
                    , base.parent_user_shipper_id_coalesce
                    , base.join_datetime
                    , latest_month_status.total_redemptions as current_month_total_redemptions
                    , base.cumulative_total_redemptions
                    , base.last_redemption_datetime
                    , base.last_used_reward_datetime
                    , latest_month_status.point_balance
                    , latest_month_status.point_balance_lag_1m
                    , base.points_expiring_in_7d
                    , base.points_expiring_in_14d
                    , base.points_expiring_in_30d
                    , base.points_expiring_in_60d
                    , base.points_expiring_in_90d
                    , latest_month_status.member_tier as current_member_tier
                    , base.next_member_tier
                    , base.next_member_tier_required_points
                    , latest_month_status.cumulative_points_earned
                    , latest_month_status.cumulative_points_redeemed
                    , latest_month_status.cumulative_points_expired
                    , base.system_id
                    , base.created_month
                from ninja_rewards_users_base as base
                left join latest_month_status on
                    base.id = latest_month_status.user_id
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_REWARDS_USERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
