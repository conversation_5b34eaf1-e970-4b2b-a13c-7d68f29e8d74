import sys

from pyspark.sql import SparkSession
from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_LINE_ITEMS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_LINE_ITEMS_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    PROMOTION_META_JSON_STRUCT = """struct<
      cart: array<struct<
        applied_deal: array<struct<
          discount:double, 
          id:string, 
          name:string, 
          type:int
        >>,
        brand_id: string,
        category: int,
        combo_id: string,
        commercial_deal: array<struct<
          discount:double,
          id:string,
          name:string, 
          type:int
        >>,
        configuration_id: int,
        discount: double,  
        index: string,
        price: double,
        quantity: int,
        sku_code: string,
        sub_category: int
      >>,
      cart_deal: array<struct<
        discount: double,
        id: string,
        name: string,
        type: int
      >>,
      cart_discount: double, 
      coupon_deal: array<struct<
        discount:double, 
        id:string, 
        name:string, 
        type:int
      >>,
      coupon_discount: double, 
      deals: array<string>,
      order_id: string,
      total_discount: double,
      transaction_id: string
    >"""

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.MartSPMgmtProdGL(input_env, is_masked).ORDERS,
                view_name="orders",
            ),
            base.InputTable(
                path=delta_tables.MartSPMgmtProdGL(input_env, is_masked).WAREHOUSES,
                view_name="warehouses",
            ),
        ),

    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="items_exploded",
                jinja_template="""
                select 
                     id
                     , created_by
                     , created_at
                     , created_at_timestamp
                     , updated_by
                     , updated_at
                     , updated_at_timestamp
                     , source
                     , order_number
                     , deliver_meta
                     , customer_phone
                     , customer_id
                     , customer_ref_id
                     , saleperson_id
                     , saleperson_ref_id
                     , customer_name
                     , note
                     , promotion_transaction
                     , promotion_meta
                     , created_timestamp
                     , confirmed_timestamp
                     , process_timestamp
                     , delivery_timestamp
                     , completed_timestamp
                     , cancel_timestamp
                     , cancel_reason
                     , payment_timestamp
                     , payment_method_id
                     , payment_transaction_meta
                     , confirmed_by
                     , process_by
                     , deliver_by
                     , completed_by
                     , cancel_by
                     , cancel_from
                     , status
                     , tke_creation_timestamp
                     , amast_update_timestamp
                     , do_number
                     , self_pickup
                     , warehouse_id
                     , meta
                     , request
                     , logs
                     , ito_creation_timestamp
                     , ref_id
                     , deleted_at
                     , currency
                     , warehouse_ref_id
                     , gross_weight
                     , total_amount
                     , total_discount
                     , item_discount
                     , coupon_discount
                     , cart_discount
                     , tax_amount
                     , grand_total
                     , deliver_country_id
                     , deliver_province_id
                     , deliver_district_id
                     , deliver_ward_id
                     , customer_email
                     , items
                     , order_type
                     , created_month
                     , explode(split(replace(items, '}, {', 'PLACEHOLDER'), 'PLACEHOLDER')) as item_exploded_raw
                from orders

                """,
            ),
            base.TransformView(
                view_name='line_item_reformatted',
                jinja_template="""
                select 
                     id
                     , created_by
                     , created_at
                     , created_at_timestamp
                     , updated_by
                     , updated_at
                     , updated_at_timestamp
                     , source
                     , order_number
                     , deliver_meta
                     , customer_phone
                     , customer_id
                     , customer_ref_id
                     , saleperson_id
                     , saleperson_ref_id
                     , customer_name
                     , note
                     , promotion_transaction
                     , promotion_meta
                     , created_timestamp
                     , confirmed_timestamp
                     , process_timestamp
                     , delivery_timestamp
                     , completed_timestamp
                     , cancel_timestamp
                     , cancel_reason
                     , payment_timestamp
                     , payment_method_id
                     , payment_transaction_meta
                     , confirmed_by
                     , process_by
                     , deliver_by
                     , completed_by
                     , cancel_by
                     , cancel_from
                     , status
                     , tke_creation_timestamp
                     , amast_update_timestamp
                     , do_number
                     , self_pickup
                     , warehouse_id
                     , meta
                     , request
                     , logs
                     , ito_creation_timestamp
                     , ref_id
                     , deleted_at
                     , currency
                     , warehouse_ref_id
                     , gross_weight
                     , total_amount
                     , total_discount
                     , item_discount
                     , coupon_discount
                     , cart_discount
                     , tax_amount
                     , grand_total
                     , deliver_country_id
                     , deliver_province_id
                     , deliver_district_id
                     , deliver_ward_id
                     , customer_email
                     , items
                     , order_type
                     , created_month
                     , concat(concat('{', trim(replace(replace(item_exploded_raw, '[{', ''), '}]'))), '}') as to_maps
                from items_exploded 

                """
            ),
            base.TransformView(
                view_name="json_extracted",
                jinja_template="""
                select 
                    id as order_id
                    , order_number
                    , status
                    , if(source = 5, 'AMAST', null) as source_name
                    , deliver_country_id
                    , deliver_province_id
                    , deliver_district_id
                    , deliver_ward_id
                    , get_json_object(to_maps, '$.sku_id') as sku_id
                    , get_json_object(to_maps, '$.sku_name') as sku_name
                    , get_json_object(to_maps, '$.sku_code') as sku_code
                    , cast(get_json_object(to_maps, '$.total_coupon_discount') as Decimal(18, 2)) as from_item_total_coupon_discount
                    , customer_id
                    , case 
                        when customer_ref_id is null or customer_ref_id = '' then customer_id 
                        else concat('customer_ref_id:', customer_ref_id) 
                      end as customer_zalo_id 
                    , customer_name 
                    , customer_phone
                    , saleperson_ref_id as sale_person_id
                    , get_json_object(get_json_object(request, '$.salesInfo'), '$.name') as salesperson_name
                    , get_json_object(get_json_object(request, '$.salesInfo'), '$.level') as sp_level
                    , get_json_object(to_maps, '$.barcode') as barcode
                    , get_json_object(to_maps, '$.sku_configuration_name') as sku_configuration_name
                    , cast(get_json_object(to_maps, '$.gross_weight') as Decimal(18, 2)) as gross_weight
                    , get_json_object(to_maps, '$.brand_name') as brand_name
                    , get_json_object(to_maps, '$.category_name') as category_name
                    , get_json_object(to_maps, '$.sub_category_name') as sub_category_name 
                    , get_json_object(deliver_meta, '$.provinceName') as shipping_province_name
                    , get_json_object(deliver_meta, '$.address') as shipping_address
                    , warehouse_id 
                    -- local time adjustment
                    , from_utc_timestamp(created_timestamp, 'Asia/Ho_Chi_Minh') as created_at
                    , date_format(from_utc_timestamp(created_timestamp, 'Asia/Ho_Chi_Minh'), 'yyyy-MM') as created_month
                    , created_by
                    , from_utc_timestamp(confirmed_timestamp, 'Asia/Ho_Chi_Minh') as placed_at
                    , from_utc_timestamp(process_timestamp, 'Asia/Ho_Chi_Minh') as processing_at
                    , from_utc_timestamp(delivery_timestamp, 'Asia/Ho_Chi_Minh') as delivery_at
                    , from_utc_timestamp(cancel_timestamp, 'Asia/Ho_Chi_Minh') as cancelled_at
                    , if(lower(cancel_by) = 'nan', null, cancel_by) as cancelled_by
                    , from_utc_timestamp(completed_timestamp, 'Asia/Ho_Chi_Minh') as completed_at
                    , date_format(from_utc_timestamp(completed_timestamp, 'Asia/Ho_Chi_Minh'), 'yyyy-MM') as completed_month
                    , promotion_transaction as order_promotion_code
                    , cast(get_json_object(to_maps, '$.tax_rate') as Decimal(18,2)) as tax_rate 
                    , cast(get_json_object(to_maps, '$.quantity') as long) as quantity
                    , cast(get_json_object(to_maps, '$.gross_price') as Decimal(18,2)) as from_item_gross_price
                    , cast(get_json_object(to_maps, '$.item_discount') as Decimal(18,2)) as from_item_item_discount
                    , cast(get_json_object(to_maps, '$.cart_discount') as Decimal(18,2)) as from_item_cart_discount
                    , get_json_object(to_maps, '$.id') as item_id 
                    , get_json_object(to_maps, '$.base_points') as item_base_points
                    , cast(get_json_object(to_maps, '$.earned_points') as Decimal(18,2)) as item_earned_points
                    , cast(get_json_object(to_maps, '$.total_amount') as Decimal(18,2)) as from_item_total_amount
                    , cast(get_json_object(to_maps, '$.total_item_discount') as Decimal(18,2)) as from_item_total_item_discount
                    , cast(get_json_object(to_maps, '$.total_cart_discount') as Decimal(18,2)) as from_item_total_cart_discount
                    , cast(get_json_object(to_maps, '$.total_tax') as Decimal(18,2)) as from_item_total_tax
                    , cast(get_json_object(to_maps, '$.net_revenue') as Decimal(18,2)) as from_item_net_revenue
                    , get_json_object(to_maps, '$.tier') as tier
                    , note
                    , total_amount as order_total
                    , coupon_discount as order_coupon_discount
                    , cart_discount as order_cart_discount
                    , total_discount as order_discount
                    , grand_total as order_grand_total
                    , self_pickup
                    , if(cancel_reason = 'nan', null, cancel_reason) as cancel_reason
                    , get_json_object(to_maps, '$.deal_name') as sku_level_promotion_name
                from line_item_reformatted

                """

            ),
            base.TransformView(
                view_name='price_enriched',
                jinja_template="""
                select 
                    order_id
                    , order_number
                    , json_extracted.status
                    , source_name
                    , json_extracted.deliver_country_id
                    , json_extracted.deliver_province_id
                    , json_extracted.deliver_district_id
                    , json_extracted.deliver_ward_id
                    , sku_id
                    , sku_name
                    , sku_code
                    , round(from_item_total_amount / (1+tax_rate/100.0), 2) as gmv
                    , round(
                        (from_item_total_amount - from_item_item_discount - from_item_total_cart_discount) / (1 + tax_rate/100.0), 
                        2
                    ) as net_revenue
                    , round(
                        from_item_total_amount - from_item_item_discount - from_item_total_cart_discount - from_item_total_coupon_discount, 
                        2
                    ) as cash_collected
                    , from_item_total_coupon_discount
                    , customer_id
                    , customer_name
                    , customer_phone
                    , sale_person_id as salesperson_id
                    , salesperson_name
                    , sp_level as salesperson_level
                    , barcode
                    , sku_configuration_name
                    , (gross_weight * quantity) as gross_weight
                    , brand_name
                    , category_name
                    , sub_category_name
                    , shipping_province_name
                    , shipping_address
                    , warehouse_id
                    , warehouses.name as warehouse_name
                    , warehouses.amast_code as site_id
                    , json_extracted.created_at
                    , json_extracted.created_month
                    , json_extracted.created_by
                    , placed_at
                    , processing_at
                    , delivery_at
                    , cancelled_at
                    , cancelled_by
                    , completed_at
                    , completed_month
                    , order_promotion_code
                    , tax_rate
                    , quantity
                    , from_item_gross_price
                    , from_item_item_discount
                    , (from_item_gross_price - from_item_item_discount) as sku_discount_price
                    , from_item_cart_discount
                    , (from_item_gross_price - from_item_item_discount - from_item_cart_discount) as sku_grand_total
                    , item_id
                    , item_base_points
                    , item_earned_points
                    , from_item_total_amount
                    , from_item_total_item_discount
                    , (from_item_total_amount - from_item_item_discount) as item_discount_price
                    , from_item_total_cart_discount
                    , (from_item_total_amount - from_item_item_discount - from_item_total_cart_discount) as item_grand_total
                    , order_total
                    , order_coupon_discount
                    , order_cart_discount
                    , order_discount
                    , self_pickup
                    , cancel_reason
                    , sku_level_promotion_name
                    , tier
                    , from_item_total_tax
                    , from_item_net_revenue
                    , note
                    , 'vn' as system_id
                from json_extracted
                left join warehouses on 
                    json_extracted.warehouse_id = warehouses.id
                where 
                    json_extracted.deliver_country_id = 6

                """
            ),
            base.TransformView(
            view_name="line_item_movcata_discount",
            jinja_template="""
                with exploded_cart as (
                    select 
                        order_number
                        , get_json_object(to_maps, '$.id') as item_id
                        , explode(from_json(promotion_meta, '{{promotion_meta_schema}}').cart) AS cart_item
                    from line_item_reformatted
                ),

                commercial_deal_exploded as (
                    select 
                        order_number
                        , item_id
                        , explode(cart_item.commercial_deal) AS commercial_deal_item
                    from exploded_cart
                    where 
                        item_id = cart_item.index
                )

                select 
                    order_number
                    , item_id
                    , sum(commercial_deal_item.discount) as movcata_discount 
                from commercial_deal_exploded
                where 
                    commercial_deal_item.type in (18, 19)
                group by 1,2 

                """,
                jinja_arguments={"promotion_meta_schema": PROMOTION_META_JSON_STRUCT}
            ),
            base.TransformView(
                view_name='final',
                jinja_template="""
                select 
                    price_enriched.customer_id
                    , price_enriched.source_name
                    , price_enriched.customer_id as provider_customer_id 
                    , price_enriched.order_number
                    , price_enriched.salesperson_id
                    , price_enriched.customer_name
                    , price_enriched.customer_phone
                    , price_enriched.status
                    , price_enriched.created_at
                    , price_enriched.created_month
                    , price_enriched.completed_at
                    , price_enriched.completed_month
                    , price_enriched.cancelled_at
                    , price_enriched.salesperson_id as seller
                    , price_enriched.warehouse_name 
                    , price_enriched.deliver_ward_id          
                    , price_enriched.deliver_district_id   
                    , price_enriched.deliver_province_id
                    , price_enriched.deliver_province_id as payment_province_id
                    , price_enriched.shipping_province_name
                    , price_enriched.sku_name
                    , price_enriched.sku_configuration_name   
                    , price_enriched.gross_weight
                    , price_enriched.sku_code
                    , price_enriched.barcode
                    , price_enriched.tier
                    , price_enriched.quantity
                    , price_enriched.from_item_total_amount
                    , price_enriched.sku_level_promotion_name 
                    , price_enriched.from_item_total_item_discount
                    , price_enriched.from_item_total_cart_discount
                    , (price_enriched.from_item_total_coupon_discount - line_item_movcata_discount.movcata_discount) as cashback_coupon
                    , line_item_movcata_discount.movcata_discount
                    , price_enriched.self_pickup = 1 as self_pickup
                    , (price_enriched.from_item_total_amount - price_enriched.from_item_total_item_discount) as total_amount_after_item_discount
                    , (price_enriched.from_item_total_amount 
                      - price_enriched.from_item_total_item_discount 
                      - price_enriched.from_item_total_cart_discount) 
                    AS total_amount_after_item_n_cart_discount
                    , (price_enriched.from_item_total_amount 
                      - price_enriched.from_item_total_item_discount 
                      - price_enriched.from_item_total_cart_discount 
                      - price_enriched.from_item_total_coupon_discount) 
                    AS total_amount_after_item_n_cart_n_coupon_discount
                    , (price_enriched.from_item_total_amount 
                      - price_enriched.from_item_total_item_discount 
                      - price_enriched.from_item_total_cart_discount 
                      - price_enriched.from_item_total_coupon_discount) 
                    AS paid_by_customer
                    , price_enriched.from_item_total_tax
                    , price_enriched.from_item_net_revenue as net_revenue
                    , price_enriched.category_name
                    , price_enriched.sub_category_name
                    , price_enriched.note
                    , price_enriched.item_base_points
                    , price_enriched.item_base_points as amast_point
                    , price_enriched.item_earned_points
                    , price_enriched.item_id
                    , price_enriched.system_id
                from 
                    price_enriched 
                left join line_item_movcata_discount on 
                    price_enriched.order_number = line_item_movcata_discount.order_number 
                    and price_enriched.item_id = line_item_movcata_discount.item_id 
                where true  
                    and price_enriched.quantity > 0 -- omit the lines where quantity is zero
                order by 
                    price_enriched.item_id

                """
            )
        ),

    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_MART_VN_LINE_ITEMS_ENRICHED ,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
