import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.HUB_SWEEP_REPORT_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.HUB_SWEEP_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.WAREHOUSE_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID, task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAREHOUSE_SCAN_EVENTS,
                view_name="warehouse_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="missing_tickets",
                jinja_template="""
                SELECT order_id
                       , date(creation_datetime) AS creation_date
                       , date(least(resolution_datetime, first_scan_datetime)) AS found_date
                FROM
                (
                    SELECT tickets.order_id
                           , tickets.creation_datetime
                           , IF(tickets.outcome NOT LIKE 'LOST%', tickets.resolution_datetime, NULL)
                             AS resolution_datetime
                           , MIN(scans.event_datetime) AS first_scan_datetime
                    FROM pets_tickets_enriched AS tickets
                    LEFT JOIN warehouse_scan_events AS scans ON scans.order_id = tickets.order_id
                        AND scans.event_datetime > tickets.creation_datetime
                    WHERE tickets.type = 'MISSING'
                    GROUP BY 1, 2, 3
                )
                """,
            ),
            base.TransformView(
                view_name="warehouse_scans_daily",
                jinja_template="""
                SELECT date(event_datetime) AS date
                       , order_id
                       , count(1) AS total_scans
                FROM warehouse_scan_events
                GROUP BY 1, 2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT movements.country
                       , cal_nat.date
                       , movements.location_id AS hub_id
                       , hubs.name AS hub_name
                       , hubs.region AS hub_region
                       , movements.order_id
                       , orders.tracking_id
                       , IF(cal_nat.date < date(orders.rts_trigger_datetime)
                            OR orders.rts_trigger_datetime IS NULL, 0, 1) AS rts_flag
                       , coalesce(cal_reg.working_day, cal_nat.working_day) AS working_day
                       , cal_nat.day AS day_of_week
                       , IF(scans.total_scans IS NOT NULL, 1, 0) AS scanned
                       , orders.created_month
                       , max(CASE
                                 WHEN tickets.creation_date IS NULL
                                      OR cal_nat.date < tickets.creation_date THEN 0
                                 WHEN tickets.found_date IS NULL
                                      OR cal_nat.date <= tickets.found_date THEN 1
                                 ELSE 0
                             END) AS missing_flag
                FROM order_movements AS movements
                INNER JOIN order_milestones AS orders ON orders.order_id = movements.order_id
                LEFT JOIN hubs_enriched AS hubs ON
                    hubs.id = movements.location_id
                LEFT JOIN calendar AS cal_nat ON cal_nat.date > date(movements.entry_datetime)
                    AND cal_nat.date < date(movements.exit_datetime)
                    AND cal_nat.region = 'national'
                LEFT JOIN calendar AS cal_reg ON cal_reg.date = cal_nat.date
                    AND cal_reg.region = hubs.address_city
                LEFT JOIN warehouse_scans_daily AS scans ON scans.order_id = movements.order_id
                    AND scans.date = cal_nat.date
                LEFT JOIN missing_tickets AS tickets ON tickets.order_id = movements.order_id
                WHERE movements.location_type = 'HUB'
                      AND movements.duration_days >= 2
                GROUP BY {{ range(1, 13) | join(',') }}
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HUB_SWEEP_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
