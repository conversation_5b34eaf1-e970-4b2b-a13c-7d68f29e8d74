import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.RTS_SHIPPER_CONFIRMATION_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.RTS_SHIPPER_CONFIRMATION_ENRICHED_MASKED,
    depends_on=(data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED_BASE,
                view_name="pets_tickets_enriched_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.RecoveryCommsProdGL(input_env, is_masked).DELIVERY_FAILURE_ORDERS,
                view_name="delivery_failure_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RecoveryCommsProdGL(input_env, is_masked).TICKET_EXPIRATIONS,
                view_name="ticket_expirations",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with aggregated_cte as (
                    select
                        delivery_failure_orders.id
                        , delivery_failure_orders.order_id
                        , pets_tickets_enriched_base.creation_datetime as pets_ticket_creation_datetime
                        , pets_tickets_enriched_base.resolution_datetime as pets_ticket_resolution_datetime
                        , max(last_attempt_before_ticket_creation.event_datetime) as last_attempt_datetime_before_ticket_creation
                        , max_by(last_attempt_before_ticket_creation.status
                            , last_attempt_before_ticket_creation.event_datetime) as last_attempt_status_before_ticket_creation
                        , min(first_attempt_after_ticket_resolution.event_datetime) as first_attempt_datetime_after_ticket_resolution
                        , min_by(first_attempt_after_ticket_resolution.status
                            , first_attempt_after_ticket_resolution.event_datetime) as first_attempt_status_after_ticket_resolution
                        , min_by(failure_reasons.english_description
                            , first_attempt_after_ticket_resolution.event_datetime) as first_attempt_failure_reason_after_ticket_resolution
                    from delivery_failure_orders
                    left join pets_tickets_enriched_base
                        on pets_tickets_enriched_base.id = delivery_failure_orders.ticket_id
                    left join delivery_transaction_events as last_attempt_before_ticket_creation
                        on last_attempt_before_ticket_creation.order_id = pets_tickets_enriched_base.order_id
                        and last_attempt_before_ticket_creation.event_datetime < pets_tickets_enriched_base.creation_datetime
                    -- Join only on valid attempts
                        and last_attempt_before_ticket_creation.valid_flag = 1
                    left join delivery_transaction_events as first_attempt_after_ticket_resolution
                        on first_attempt_after_ticket_resolution.order_id = pets_tickets_enriched_base.order_id
                        and first_attempt_after_ticket_resolution.event_datetime > pets_tickets_enriched_base.resolution_datetime
                    -- Join only on valid attempts
                        and first_attempt_after_ticket_resolution.valid_flag = 1
                    -- Join only on orders where outcome isn't RTS
                        and delivery_failure_orders.outcome != 'RTS'
                    -- Join only on forward deliverey transactions
                        and first_attempt_after_ticket_resolution.type != 'rts'
                    left join failure_reasons
                        on failure_reasons.id = first_attempt_after_ticket_resolution.failure_reason_id
                        and failure_reasons.system_id = '{{system_id}}'
                    group by {{ range(1, 5) | join(',') }}
                )

                select 
                    delivery_failure_orders.id
                    , delivery_failure_orders.order_id
                    , delivery_failure_orders.tracking_id
                    , delivery_failure_orders.ticket_id
                    , aggregated_cte.pets_ticket_creation_datetime
                    , aggregated_cte.pets_ticket_resolution_datetime
                    , delivery_failure_orders.global_shipper_id as shipper_id
                    , delivery_failure_orders.outcome
                    , ticket_expirations.reason as rts_outcome_reason
                    , delivery_failure_orders.issue_status
                    , from_utc_timestamp(delivery_failure_orders.created_at, '{{ local_timezone }}') as reporting_datetime
                    , aggregated_cte.last_attempt_datetime_before_ticket_creation
                    , aggregated_cte.last_attempt_status_before_ticket_creation
                    , aggregated_cte.first_attempt_datetime_after_ticket_resolution
                    , aggregated_cte.first_attempt_status_after_ticket_resolution
                    , aggregated_cte.first_attempt_failure_reason_after_ticket_resolution
                    , delivery_failure_orders.system_id
                    , delivery_failure_orders.created_month
                from delivery_failure_orders
                left join ticket_expirations
                    on ticket_expirations.ticket_id = delivery_failure_orders.ticket_id
                    and ticket_expirations.system_id = '{{ system_id }}'
                left join aggregated_cte
                    on aggregated_cte.id = delivery_failure_orders.id
                where delivery_failure_orders.system_id = '{{ system_id }}'
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).RTS_SHIPPER_CONFIRMATION_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )


    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()