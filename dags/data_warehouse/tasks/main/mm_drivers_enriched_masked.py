import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.MM_DRIVERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.MM_DRIVERS_ENRICHED_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVER_TYPES_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_TYPES_ENRICHED,
                view_name="driver_types_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(path=delta_tables.HubProdGL(input_env, is_masked).MM_DRIVER_DETAILS, view_name="mm_driver_details"),
            base.InputTable(path=delta_tables.HubProdGL(input_env, is_masked).LANDHAUL_VENDORS, view_name="landhaul_vendors"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
                base.TransformView(
                    view_name="base",
                    jinja_template="""
                    select
                        drivers.system_id
                        , drivers.id as driver_id
                        , drivers.first_name
                        , drivers.last_name
                        , drivers.display_name
                        , drivers.hub_id
                        , hubs_enriched.name as hub_name
                        , hubs_enriched.region as hub_region
                        , drivers.license_number
                        , drivers.license_type
                        , drivers.employment_type
                        , mm_driver_details.vendor_id
                        , landhaul_vendors.name as vendor_name
                        , date(drivers.employment_start_date) as employment_start_date
                        , date(drivers.employment_end_date) as employment_end_date
                        , drivers.driver_type_id
                        , driver_types_enriched.driver_type as driver_type_name
                        , drivers.username
                        , drivers.comments
                        , from_utc_timestamp(drivers.created_at, {{ get_local_timezone }}) as created_at
                        , from_utc_timestamp(drivers.updated_at, {{ get_local_timezone }}) as updated_at
                        , from_utc_timestamp(drivers.deleted_at, {{ get_local_timezone }}) as deleted_at
                        , case 
                            when drivers.deleted_at is not null then 1
                            else 0
                            end as deleted_flag
                        , case 
                            when date(current_timestamp()) >= drivers.employment_start_date 
                            and (drivers.employment_end_date is null or date(current_timestamp()) <= drivers.employment_end_date) then 1
                            else 0
                            end as employment_flag
                        , case 
                            when drivers.license_number is not null
                            and (drivers.license_expiry_date is null or date(current_timestamp()) <= drivers.license_expiry_date) then 1
                            else 0
                            end as license_flag
                        , date_format(drivers.created_at, 'yyyy-MM') as created_month
                    from drivers
                    left join hubs_enriched 
                        on drivers.hub_id = hubs_enriched.id
                        and drivers.system_id = hubs_enriched.system_id
                    left join driver_types_enriched
                        on drivers.driver_type_id = driver_types_enriched.id
                    left join mm_driver_details on
                        drivers.id = mm_driver_details.driver_id
                        and mm_driver_details.deleted_at is null
                    left join landhaul_vendors on
                        mm_driver_details.vendor_id = landhaul_vendors.id
                    """,
                    jinja_arguments={"get_local_timezone": util.get_local_timezone("drivers.system_id")},
                ),
                base.TransformView(
                    view_name="final_view",
                    jinja_template="""
                    select
                        system_id
                        , created_month
                        , driver_id
                        , first_name
                        , last_name
                        , display_name
                        , hub_id
                        , hub_name
                        , hub_region
                        , license_number
                        , license_type
                        , employment_type
                        , vendor_id
                        , vendor_name
                        , employment_start_date
                        , employment_end_date
                        , driver_type_id
                        , driver_type_name
                        , username
                        , comments
                        , created_at
                        , updated_at
                        , deleted_at
                        , deleted_flag
                        , employment_flag
                        , license_flag
                        , case
                            when deleted_flag = 1 then 'inactive'
                            when employment_flag = 0 and license_flag = 0 then 'inactive'
                            when license_flag = 0 then 'inactive'
                            when employment_flag = 0 then 'inactive'
                            else 'active'
                            end as driver_status
                        , case 
                            when deleted_flag = 1 then 'deleted'
                            when employment_flag = 0 and license_flag = 0 then 'both employment and license ended'
                            when license_flag = 0 then 'license expired'
                            when employment_flag = 0 then 'employment ended'
                            else 'active_driver'
                            end as driver_status_comments
                    from base
                    where
                        (system_id = 'my' and driver_type_id = '1004503')
                        or (system_id = 'id' and driver_type_id = '1004505')
                        or (system_id = 'ph' and driver_type_id = '1004509')
                        or (system_id = 'th' and driver_type_id = '1004511')
                        or (system_id = 'vn' and driver_type_id = '1004513')
                    """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MM_DRIVERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
