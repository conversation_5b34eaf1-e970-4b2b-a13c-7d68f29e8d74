import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.ID_TIKTOK_LONGTAIL_REPORTS_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.ID_TIKTOK_LONGTAIL_REPORTS_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on=(
        data_warehouse.OrderSLADAG.Task.LONGTAIL_SLA_HOURS_MASKED,
        data_warehouse.OrderSLADAG.Task.SLA_REPORTS_BASE_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.WAREHOUSE_SPEED_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_REPORT,
                view_name="last_mile_push_off_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SLA_REPORTS_BASE,
                view_name="sla_reports_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAREHOUSE_SPEED_REPORT,
                view_name="warehouse_speed_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LONGTAIL_SLA_HOURS,
                view_name="longtail_sla_hours",
                system_id=system_id,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="order_base",
                jinja_template="""
                select
                    order_id
                    , tracking_id
                    , shipper_id
                    , reporting_name
                    , dp_dropoff_dp_name
                    , dp_dropoff_datetime
                    , pickup_hub_id
                    , pickup_hub_name
                    , pickup_datetime
                    , nv_pickup_datetime
                    , inbound_hub_id
                    , inbound_hub_name
                    , inbound_hub_region
                    , inbound_datetime
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , from_l2_name
                    , to_l2_name
                    , delivery_attempts
                    , first_valid_delivery_attempt_datetime
                    , first_valid_delivery_failure_reason
                    , delivery_success_datetime
                    , least(pickup_datetime, inbound_datetime) as start_clock_datetime
                    , case
                        when least(pickup_datetime, inbound_datetime) = pickup_datetime then pickup_hub_id
                        else inbound_hub_id
                    end as origin_hub_id
                from sla_reports_base
                where
                    reporting_name = 'tiktok'
                    and granular_status = 'Completed'
                    and rts_flag = 0
                """,
            ),
            base.TransformView(
                view_name="order_base_enriched",
                jinja_template="""
                with origin_hub_logic as (
                
                    select
                        base.*
                        , lmpor.dest_hub_datetime
                        , origin_hub.sort_hub_flag as orig_sort_hub_flag
                        , inbound_hub.sort_hub_flag as inbound_sort_hub_flag
                        , case
                            when origin_hub.sort_hub_flag = 0 and inbound_hub.sort_hub_flag = 1
                            then base.inbound_hub_region
                            else origin_hub.region
                        end as origin_hub_region
                        , case
                            when origin_hub.sort_hub_flag = 0 and inbound_hub.sort_hub_flag = 1
                            then base.inbound_hub_name
                            else origin_hub.name
                        end as origin_hub_name
                        , case
                            when origin_hub.sort_hub_flag = 0 and inbound_hub.sort_hub_flag = 0 then 1
                            when origin_hub.sort_hub_flag = 0 and inbound_hub.sort_hub_flag = 1 then 2
                            else 0
                        end as origin_pst_flag
                    from order_base as base
                    left join last_mile_push_off_report as lmpor
                        on base.order_id = lmpor.order_id
                    left join hubs_enriched as origin_hub
                        on base.origin_hub_id = origin_hub.id
                    left join hubs_enriched as inbound_hub
                        on base.inbound_hub_id = inbound_hub.id
                    
                )

                select
                    *
                    , concat(left(origin_hub_name, 3), '-', left(origin_hub_name, 3)) as origin_msh
                    , concat(left(dest_hub_name, 3), '-', left(dest_hub_name, 3)) as dest_msh
                    , case
                        when dest_hub_region = 'Greater Jakarta' then 'Jabo'
                        when dest_hub_region like '%Java' then 'In Island'
                        else 'Out Island'
                    end as dest_island
                from origin_hub_logic      
                """,
            ),
            base.TransformView(
                view_name="origin_station_timestamps",
                jinja_template="""
                select
                    base.order_id
                    , min(shipment_orders.orig_shipment_van_inbound_datetime) filter(
                        where base.pickup_hub_name = shipment_orders.orig_hub_name
                            and base.origin_msh = shipment_orders.dest_hub_name
                        ) as iv_orig_st_main
                    , min(shipment_orders.orig_shipment_van_inbound_datetime) filter(
                        where base.pickup_hub_name = shipment_orders.orig_hub_name
                        ) as iv_orig_mst_backup
                    , min(shipment_orders.orig_shipment_close_datetime) filter(
                        where base.pickup_hub_name = shipment_orders.orig_hub_name
                        ) as iv_orig_mst_backup2
                from order_base_enriched as base
                left join shipment_orders_enriched as shipment_orders
                    on base.order_id = shipment_orders.order_id
                group by 1
                """,
            ),
            base.TransformView(
                view_name="shipments_vs_schedule",
                jinja_template="""
                with schedules as (
                
                    select distinct
                        origin_hub_name
                        , dest_hub_name
                        , expected_start_datetime
                        , expected_arrival_datetime
                    from movement_trips_enriched
                    where
                        (status = 'COMPLETED' or cancellation_reason != 'Change of schedule')
                        and schedule_id is not null
                
                )
                
                , base as (

                    select
                        shipment_id
                        , first_trip_id
                        , orig_hub_name as shipment_orig_hub_name
                        , case
                            when first_transit_hub_id is null then dest_hub_name
                            when first_transit_hub_id is not null then first_transit_hub_name
                        end as shipment_next_hub_name
                        , orig_shipment_close_datetime
                        , orig_shipment_van_inbound_datetime
                        -- This 60 minutes is based on OPG's arbitrary number as trips do not depart at exact time
                        , orig_shipment_close_datetime + interval '60' minute as closest_wave_ref_datetime
                    from shipments_enriched as shipments
                
                )

                , set_earliest_expected_wave as (

                    select
                        shipment_id
                        , first_trip_id
                        , shipment_orig_hub_name
                        , shipment_next_hub_name
                        , orig_shipment_close_datetime
                        , orig_shipment_van_inbound_datetime
                        , closest_wave_ref_datetime
                        , min(schedules.expected_start_datetime) as expected_departure_datetime
                        , min(schedules.expected_arrival_datetime) as expected_arrival_datetime
                    from base
                    left join schedules
                        on base.shipment_orig_hub_name = schedules.origin_hub_name
                        and base.shipment_next_hub_name = schedules.dest_hub_name
                        and base.closest_wave_ref_datetime <= schedules.expected_start_datetime
                    group by {{ range(1, 8) | join(',') }}

                )

                select
                    *
                    -- This 30 minutes is based on OPG's arbitrary number as trips do not depart at exact time
                    , expected_departure_datetime + interval '30' minute as expected_latest_departure_datetime
                    , expected_arrival_datetime + interval '30' minute as expected_latest_arrival_datetime
                from set_earliest_expected_wave

                """,
            ),
            base.TransformView(
                view_name="warehouse_timestamps",
                jinja_template="""
                with base as (
                    
                    select
                        base.order_id
                        , base.hub_seq
                        , base.hub_name
                        , case
                            when base.inbound_start_clock_type = 'proof_of_handover_proxy'
                            then base.inbound_end_clock_datetime
                            else base.inbound_start_clock_datetime
                        end as inbound_start_clock_datetime
                        , base.inbound_end_clock_datetime
                        , base.outbound_end_clock_datetime
                        , base.outbound_shipment_id
                        , shipments.first_trip_id
                        , shipments.orig_shipment_close_datetime
                        , shipments.orig_shipment_van_inbound_datetime
                        , shipments.expected_latest_departure_datetime
                        , shipments.expected_latest_arrival_datetime
                        , trips.actual_arrival_datetime
                    from warehouse_speed_report as base
                    left join shipments_vs_schedule as shipments
                        on base.outbound_shipment_id = shipments.shipment_id
                    left join movement_trips_enriched as trips
                        on shipments.first_trip_id = trips.trip_id
                    where
                        rts_leg_flag = 0
                        and (
                            outbound_end_clock_type = 'shipment_van_inbound'
                            or outbound_end_clock_type is null
                        )
                        and (
                            inbound_start_clock_type in (
                                'proof_of_handover'
                                , 'shipment_inbound'
                                , 'proof_of_handover_proxy'
                                , 'pickup_proxy'
                            )
                            or inbound_start_clock_type is null
                        )
                
                )

                select
                    order_id
                {%- for seq, syntax in seq_to_syntax.items() %}
                {%- for col in column_list %}
                    , {{ syntax }}_by({{ col }}, hub_seq) as {{ seq }}_{{ col }}
                {%- endfor %}
                {%- endfor %}
                from base
                group by 1
                """,
                jinja_arguments={
                    "seq_to_syntax":{
                        "first":"min",
                        "last":"max",
                    },
                    "column_list":(
                        "inbound_start_clock_datetime",
                        "inbound_end_clock_datetime",
                        "outbound_end_clock_datetime",
                        "outbound_shipment_id",
                        "first_trip_id",
                        "orig_shipment_close_datetime",
                        "orig_shipment_van_inbound_datetime",
                        "expected_latest_departure_datetime",
                        "expected_latest_arrival_datetime",
                        "actual_arrival_datetime",
                    )
                },
            ),
            base.TransformView(
                view_name="combined_timestamps",
                jinja_template="""
                select
                    base.*
                    , if(base.dp_dropoff_datetime is not null, 'Mitra', 'Core') core_mitra
                    , case
                        when base.origin_pst_flag = 1 
                        then coalesce(pst.iv_orig_st_main, pst.iv_orig_mst_backup, pst.iv_orig_mst_backup2)
                        when base.origin_pst_flag = 2 then pst.iv_orig_st_main
                    end as pst_iv_datetime
                    , case
                        when base.origin_pst_flag = 0 then 'not_applicable'
                        when base.origin_pst_flag = 1 then
                            case
                                when pst.iv_orig_st_main is not null then 'iv_both_orig_st_and_msh_match'
                                when pst.iv_orig_mst_backup is not null then 'iv_orig_st_match_only'
                                when pst.iv_orig_mst_backup2 is not null then 'sc_orig_st_match_only'
                                else 'no_match'
                            end 
                        when base.origin_pst_flag = 2 then 'iv_both_orig_st_and_msh_match'
                        else 'check'
                    end as pst_iv_timestamp_source
                    , wsr.first_inbound_start_clock_datetime as o_ih_datetime
                    , wsr.first_inbound_end_clock_datetime as o_ib_datetime
                    , wsr.first_outbound_shipment_id as o_msh_shipment_id
                    , wsr.first_first_trip_id as o_iv_trip_id
                    , wsr.first_orig_shipment_close_datetime as o_cs_datetime
                    , wsr.first_orig_shipment_van_inbound_datetime as o_iv_datetime
                    , wsr.first_expected_latest_departure_datetime as o_wave_departure_datetime
                    , wsr.first_expected_latest_arrival_datetime as o_iv_expected_arrival_datetime
                    , wsr.first_actual_arrival_datetime as o_iv_actual_arrival_datetime
                    , if(base.origin_msh = base.dest_msh, null, wsr.last_inbound_start_clock_datetime) as d_ih_datetime
                    , if(base.origin_msh = base.dest_msh, null, wsr.last_inbound_end_clock_datetime) as d_ib_datetime
                    , if(base.origin_msh = base.dest_msh, null, wsr.last_outbound_shipment_id) as d_msh_shipment_id
                    , if(base.origin_msh = base.dest_msh, null, wsr.last_first_trip_id) as d_iv_trip_id
                    , if(base.origin_msh = base.dest_msh, null, wsr.last_orig_shipment_close_datetime) as d_cs_datetime
                    , if(base.origin_msh = base.dest_msh
                        , null, wsr.last_orig_shipment_van_inbound_datetime
                    ) as d_iv_datetime
                    , if(base.origin_msh = base.dest_msh
                        , null, wsr.last_expected_latest_departure_datetime
                    ) as d_wave_departure_datetime
                    , if(base.origin_msh = base.dest_msh
                        , null, wsr.last_expected_latest_arrival_datetime
                    ) as d_iv_expected_arrival_datetime
                    , if(base.origin_msh = base.dest_msh
                        , null, wsr.last_actual_arrival_datetime
                    ) as d_iv_actual_arrival_datetime
                from order_base_enriched as base
                left join warehouse_timestamps as wsr
                    on base.order_id = wsr.order_id
                left join origin_station_timestamps as pst
                    on base.order_id = pst.order_id
                    and base.origin_pst_flag in (1,2)
                """,
            ),
            base.TransformView(
                view_name="longtail_sla_conf",
                jinja_template="""
                select
                    base.*
                {%- for col in shipper_sla_hours_column_fetch %}
                    , coalesce(
                    {%- for type in shipper_sla_config_joins[system_id].keys() %}
                        sla_{{ type }}.{{ col }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                    ) AS {{ col }}
                {% endfor %}
                from combined_timestamps as base
                {%- for type, configs in shipper_sla_config_joins[system_id].items() %}
                left join longtail_sla_hours AS sla_{{ type }}
                    on sla_{{ type }}.sla_type = 'bau'
                    and date(sla_{{ type }}.start_date) <= date(base.start_clock_datetime)
                    and date(sla_{{ type }}.end_date) > date(base.start_clock_datetime)
                {%- for config, value in configs.items() %}
                {%- if value %}
                    and sla_{{ type }}.{{ config }} = {{ value }}
                {%- else %}
                    and sla_{{ type }}.{{ config }} is null
                {%- endif %}
                {%- endfor %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "shipper_sla_hours_column_fetch": (
                        "sla_hour",
                        "calendar_config",
                        "shipper_config_type",
                        "origin_dest_config_type",
                    ),
                    "shipper_sla_config_joins": {
                        "id": {
                            "reporting_name_l2_name": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name",
                                "origin_dest_config_type": "'l2_name'",
                                "origin_config": "base.from_l2_name",
                                "dest_config": "base.to_l2_name",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                    },
                },
            ),
            base.TransformView(
                view_name="other_sla_conf",
                jinja_template="""
                select
                    *
                    , case
                        when orig_sort_hub_flag = 0 and inbound_sort_hub_flag = 0
                        then if(extract(hour from start_clock_datetime) < 18, 20, 25)
                        when orig_sort_hub_flag = 0 and inbound_sort_hub_flag = 1 then 24
                    end as poh_target_sla_hour
                    , if(hour(start_clock_datetime) > 20, 24 + 21, 21) as orig_ib_target_sla_hour
                    , if(hour(start_clock_datetime) > 20, 24 + 22, 22) as orig_iv_target_sla_hour
                    , case 
                        when origin_hub_name like 'SRG%' and dest_hub_name like 'SRG%' then 37
                        when origin_hub_name like 'SOC%' and dest_hub_name like 'SOC%' then 37
                        when origin_hub_name like 'KDS%' and dest_hub_name like 'KDS%' then 37
                        when origin_hub_name like 'JOG%' and dest_hub_name like 'JOG%' then 37
                        when origin_hub_name like 'PKL%' and dest_hub_name like 'PKL%' then 37
                        when origin_hub_name like 'PWT%' and dest_hub_name like 'PWT%' then 37
                        when origin_hub_region != 'East Java' 
                            and dest_hub_name in ('SUB-BWI','SUB-BDW','SUB-GBN','SUB-TPN','SUB-PWH')
                        then 82
                        when origin_hub_region in ('West Java', 'Central Java', 'East Java', 'Greater Jakarta')
                            and dest_hub_region in ('West Java', 'Central Java', 'East Java', 'Greater Jakarta')
                            and origin_hub_region = dest_hub_region
                            and extract(hour from start_clock_datetime) < 18
                        then 37
                        else 61
                    end as station_target_sla_hour
                    , cast(date(start_clock_datetime) as timestamp) as base_start_clock_datetime
                from longtail_sla_conf
                """,
            ),
            base.TransformView(
                view_name="milestones_timestamps_calc",
                jinja_template="""
                select
                    *
                    , base_start_clock_datetime
                        + cast(poh_target_sla_hour || ' hour' as interval) as poh_target_datetime
                    , base_start_clock_datetime
                        + cast(orig_ib_target_sla_hour || ' hour' as interval) as orig_ib_target_datetime
                    , base_start_clock_datetime
                        + cast(orig_iv_target_sla_hour || ' hour' as interval) as orig_iv_target_datetime
                    , base_start_clock_datetime
                        + cast(station_target_sla_hour || ' hour' as interval) as station_target_datetime
                    , start_clock_datetime
                        + cast(cast(sla_hour * 3600 as int) || ' second' as interval) as longtail_sla_datetime
                    , date(dest_hub_datetime + interval '9' hour) as attempt_date_limit
                from other_sla_conf
                """,
            ),
            base.TransformView(
                view_name="hit_flag_and_leadtime_calc",
                jinja_template="""
                select
                    *
                {%- for lt in lead_time_calc %}
                    , (to_unix_timestamp({{ lead_time_calc[lt][1] }})
                        - to_unix_timestamp({{ lead_time_calc[lt][0] }})) / 3600
                    as {{ lt }}_lt
                {%- endfor %}
                    , case
                        when o_iv_datetime is null or o_wave_departure_datetime is null then null
                        when o_iv_datetime <= o_wave_departure_datetime then 1
                        else 0
                    end as o_closest_wave_hit_flag
                    , case
                        when o_iv_expected_arrival_datetime is null or o_iv_actual_arrival_datetime is null then null
                        when o_iv_actual_arrival_datetime <= o_iv_expected_arrival_datetime then 1
                        else 0
                    end as o_arrival_hit_flag
                    , case
                        when d_iv_datetime is null or d_wave_departure_datetime is null then null
                        when d_iv_datetime <= d_wave_departure_datetime then 1
                        else 0
                    end as d_closest_wave_hit_flag
                    , case
                        when d_iv_expected_arrival_datetime is null or d_iv_actual_arrival_datetime is null then null
                        when d_iv_actual_arrival_datetime <= d_iv_expected_arrival_datetime then 1
                        else 0
                    end as d_arrival_hit_flag
                    , case
                        when origin_pst_flag in (0,2) and o_ih_datetime <= poh_target_datetime then 0
                        when origin_pst_flag in (0,2) and o_ih_datetime > poh_target_datetime then 1
                    end as first_mile_issue_flag
                    , if(dest_hub_datetime <= station_target_datetime, 1, 0) as last_mile_issue_flag
                    , if(dest_hub_datetime > station_target_datetime, 1, 0) as network_issue_flag
                from milestones_timestamps_calc
                """,
                jinja_arguments={
                    "lead_time_calc":{
                        "fm":("start_clock_datetime", "inbound_datetime"),
                        "orig_ib_cs":("o_ib_datetime", "o_cs_datetime"),
                        "mm":("inbound_datetime", "dest_hub_datetime"),
                        "dest_ib_cs":("d_ib_datetime", "d_cs_datetime"),
                        "lm_attempt":("dest_hub_datetime", "first_valid_delivery_attempt_datetime"),
                        "lm_complete":("dest_hub_datetime", "delivery_success_datetime"),
                        "attempt":("start_clock_datetime", "first_valid_delivery_attempt_datetime"),
                        "complete":("start_clock_datetime", "delivery_success_datetime"),
                    },
                },
            ),
            base.TransformView(
                view_name="issue_attribution",
                jinja_template="""
                select
                    *
                    , case
                        when sla_hour is null then null
                        when complete_lt <= sla_hour then 0
                        else 1
                    end as longtail_complete_miss_flag
                    , case
                        when sla_hour is null then null
                        when attempt_lt <= sla_hour then 0
                        else 1
                    end as longtail_attempt_miss_flag
                    , case
                        when sla_hour is null then null
                        when date(delivery_success_datetime) <= date(longtail_sla_datetime) then 0
                        else 1
                    end as day_longtail_complete_miss_flag
                    , case
                        when sla_hour is null then null
                        when date(first_valid_delivery_attempt_datetime) <= date(longtail_sla_datetime) then 0
                        else 1
                    end as day_longtail_attempt_miss_flag
                    , case
                        when last_mile_issue_flag = 0 then null
                        when attempt_date_limit < date(first_valid_delivery_attempt_datetime) then 1
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime) then 0
                    end as lm_hygiene_flag
                    , case
                        when last_mile_issue_flag = 0 then null
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime) then 1
                        when attempt_date_limit < date(first_valid_delivery_attempt_datetime) then 0
                    end as attempted_sameday_flag
                    , case 
                        when last_mile_issue_flag = 0 then null
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime)
                            and date(first_valid_delivery_attempt_datetime) != date(delivery_success_datetime) then 1
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime)
                            and date(first_valid_delivery_attempt_datetime) = date(delivery_success_datetime) then 0
                        when attempt_date_limit < date(first_valid_delivery_attempt_datetime) then 0
                    end as attempted_success_flag
                    , case
                        when last_mile_issue_flag = 0 then null
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime)
                            and date(first_valid_delivery_attempt_datetime) = date(delivery_success_datetime) then 1
                        when attempt_date_limit >= date(first_valid_delivery_attempt_datetime)
                            and date(first_valid_delivery_attempt_datetime) != date(delivery_success_datetime) then 0
                        when attempt_date_limit < date(first_valid_delivery_attempt_datetime) then 0
                    end as completed_sameday_flag
                    , case
                        when last_mile_issue_flag = 1 then 'Last Mile'
                        when d_closest_wave_hit_flag = 0 then 'Dest LH'
                        when d_arrival_hit_flag = 0 then 'Dest LH'
                        when dest_ib_cs_lt > 1.5 and dest_msh != 'JKT-MAC' then 'Dest Sort'
                        when dest_ib_cs_lt > 2.5 and dest_msh = 'JKT-MAC' then 'Dest Sort'
                        when o_closest_wave_hit_flag = 0 then 'Orig LH'
                        when o_arrival_hit_flag = 0 then 'Orig LH'
                        when orig_ib_cs_lt > 1.5 and origin_msh != 'JKT-MAC' then 'Orig Sort'
                        when orig_ib_cs_lt > 2.5 and origin_msh = 'JKT-MAC' then 'Orig Sort'
                        when origin_pst_flag in (0,2) and first_mile_issue_flag = 1 then 'First Mile'
                        when origin_pst_flag = 1 and pst_iv_datetime > orig_iv_target_datetime then 'First Mile'
                        else 'Others'
                    end as issue_category
                from hit_flag_and_leadtime_calc
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , tracking_id
                    , shipper_id
                    , reporting_name
                    , core_mitra
                    , dp_dropoff_dp_name
                    , pickup_hub_id
                    , pickup_hub_name
                    , inbound_hub_id
                    , inbound_hub_name
                    , inbound_hub_region
                    , inbound_sort_hub_flag
                    , origin_hub_id
                    , origin_hub_name
                    , origin_pst_flag
                    , orig_sort_hub_flag
                    , origin_hub_region
                    , origin_msh
                    , dest_msh
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , dest_island
                    , from_l2_name
                    , to_l2_name

                    -- Timestamps Related
                    , dp_dropoff_datetime
                    , pickup_datetime
                    , nv_pickup_datetime
                    , inbound_datetime
                    , pst_iv_datetime
                    , pst_iv_timestamp_source
                    , o_ih_datetime
                    , o_ib_datetime
                    , o_msh_shipment_id
                    , o_iv_trip_id
                    , o_cs_datetime
                    , o_iv_datetime
                    , o_wave_departure_datetime
                    , o_closest_wave_hit_flag
                    , o_iv_expected_arrival_datetime
                    , o_iv_actual_arrival_datetime
                    , o_arrival_hit_flag
                    , d_ih_datetime
                    , d_ib_datetime
                    , d_msh_shipment_id
                    , d_iv_trip_id
                    , d_cs_datetime
                    , d_iv_datetime
                    , d_wave_departure_datetime
                    , d_closest_wave_hit_flag
                    , d_iv_expected_arrival_datetime
                    , d_iv_actual_arrival_datetime
                    , d_arrival_hit_flag
                    , dest_hub_datetime
                    , delivery_attempts
                    , first_valid_delivery_attempt_datetime
                    , first_valid_delivery_failure_reason
                    , delivery_success_datetime

                    -- SLA Related
                    , poh_target_sla_hour
                    , orig_ib_target_sla_hour
                    , orig_iv_target_sla_hour
                    , station_target_sla_hour
                    , start_clock_datetime
                    , coalesce(
                        coalesce(shipper_config_type, '')
                        , '-'
                        , coalesce(origin_dest_config_type, '')
                    ) as sla_config
                    , sla_hour as longtail_sla_hour
                    , longtail_sla_datetime

                    -- Lead Time Calculation
                    , fm_lt
                    , orig_ib_cs_lt
                    , mm_lt
                    , dest_ib_cs_lt
                    , lm_attempt_lt
                    , lm_complete_lt
                    , attempt_lt
                    , complete_lt

                    -- Issue Flag
                    , first_mile_issue_flag
                    , last_mile_issue_flag
                    , network_issue_flag
                    , longtail_complete_miss_flag
                    , day_longtail_complete_miss_flag
                    , longtail_attempt_miss_flag
                    , day_longtail_attempt_miss_flag
                    , lm_hygiene_flag
                    , attempted_sameday_flag
                    , attempted_success_flag
                    , completed_sameday_flag
                    , issue_category
                    , weekofyear(delivery_success_datetime + interval '1' day) - 1 as week_num
                    , date_format(delivery_success_datetime, 'yyyy-MM') as created_month
                from issue_attribution
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_TIKTOK_LONGTAIL_REPORTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.config("spark.sql.analyzer.maxIterations", "200").getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
