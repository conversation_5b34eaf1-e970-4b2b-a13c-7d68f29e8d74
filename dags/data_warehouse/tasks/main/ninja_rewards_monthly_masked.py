import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.EberDAG.Task.NINJA_REWARDS_MONTHLY_MASKED + ".py",
    task_name=data_warehouse.EberDAG.Task.NINJA_REWARDS_MONTHLY_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_BASE_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_USERS_BASE,
                view_name="ninja_rewards_users_base",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.Eber(input_env, is_masked).MEMBER_TRANSACTIONS, view_name="member_transactions"),
            base.InputTable(path=delta_tables.Eber(input_env, is_masked).POINT_TRANSACTIONS, view_name="point_transactions"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="member_transactions_monthly",
                jinja_template="""
                select
                    date(date_trunc('month', from_utc_timestamp(created_at, {{ get_local_timezone }}))) as month
                    , user_id
                    , replace(max_by(get_json_object(to_member_tier, '$.name'), id), ' (Old)', '') as member_tier
                from member_transactions
                group by 1, 2
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="point_transactions_enriched",
                jinja_template="""
                select
                    user_id
                    , type
                    , points
                    , cast(amount as decimal(38, 2)) as amount
                    , coupon_issued
                    -- We use user created_at to estimate created_at in the case of Point Balance Imports because the
                    -- created_at published by Eber is wrong for such cases.
                    -- We use user created_at to change created_at in the case when point transaction occur
                    -- before Eber join date
                    , from_utc_timestamp(
                        if(
                            (note = 'Point Balance Import') or (get_json_object(user, '$.created_at') > created_at)
                            , get_json_object(user, '$.created_at')
                            , created_at
                        )
                        , {{ get_local_timezone }}
                    ) as creation_datetime
                from point_transactions
                where
                    points <> 0
                    and void = false
                    and type <> 'void'
                    -- Filter out test transactions in production.
                    and (note not in ('Eber', 'Test') or note is null)
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="point_transactions_monthly",
                jinja_template="""
                select
                    date(date_trunc('month', creation_datetime)) as month
                    , user_id
                    , count_if(type = 'redeemed') as total_redemptions
                    , sum(points) as total_points
                    , sum(points) filter (where points > 0) as points_earned
                    , sum(points) filter (where type = 'redeemed') as points_redeemed
                    , sum(points) filter (where type = 'adjusted' and points < 0) as points_expired
                    , sum(amount) filter (where amount > 0) as amount_spent
                from point_transactions_enriched
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    date_ranges as (

                        select
                            id as user_id
                            , display_name as user_display_name
                            , shipper_id
                            , parent_user_id_coalesce
                            , parent_user_shipper_id_coalesce
                            , join_datetime as user_join_datetime
                            , date(date_trunc('month', join_datetime)) as user_join_month
                            , date(
                                from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }})
                            ) - interval 1 day as report_date
                            , system_id
                        from ninja_rewards_users_base

                    )
                    , users_all_months as (

                        select
                            *
                            , explode(sequence(user_join_month, report_date, interval 1 month)) as month
                        from date_ranges

                    )
                    , base as (

                        select
                            users_all_months.month
                            , users_all_months.user_id
                            , users_all_months.user_display_name
                            , users_all_months.shipper_id
                            , users_all_months.parent_user_id_coalesce
                            , users_all_months.parent_user_shipper_id_coalesce
                            , users_all_months.user_join_datetime
                            , if(
                                month(users_all_months.month) >= 7
                                , year(users_all_months.month) + 1
                                , year(users_all_months.month)
                            ) as financial_year
                            , member_transactions_monthly.member_tier

                            {%- for column in point_columns %}
                            , coalesce(point_transactions_monthly.{{ column }}, 0) as {{ column }}
                            {%- endfor %}

                            , users_all_months.system_id
                            , date_format(users_all_months.month, 'yyyy-MM') as created_month
                        from users_all_months
                        left join member_transactions_monthly on
                            users_all_months.month = member_transactions_monthly.month
                            and users_all_months.user_id = member_transactions_monthly.user_id
                        left join point_transactions_monthly on
                            users_all_months.month = point_transactions_monthly.month
                            and users_all_months.user_id = point_transactions_monthly.user_id

                    )
                    , base_w_cumulative as (

                        select
                            *
                            , last(member_tier, true) over (partition by user_id order by month) as member_tier_filled

                            {%- for column in point_columns %}
                            , sum({{ column }}) over (partition by user_id order by month) as cumulative_{{ column }}
                            {%- endfor %}

                            {%- for column in point_columns if column != 'total_points' %}
                            , sum({{ column }}) over (
                                partition by user_id, financial_year order by month
                            ) as fy_cumulative_{{ column }}
                            {%- endfor %}

                        from base

                    )
                    , base_w_lag as (

                        select
                            *

                            {%- for column in point_columns %}
                            , lag(cumulative_{{ column }}) over (
                                partition by user_id order by month
                            ) as cumulative_{{ column }}_lag_1m
                            {%- endfor %}

                        from base_w_cumulative

                    )
                    , final as (

                        select
                            month
                            , user_id
                            , user_display_name
                            , shipper_id
                            , user_join_datetime
                            , parent_user_id_coalesce
                            , parent_user_shipper_id_coalesce
                            , financial_year
                            , member_tier_filled as member_tier
                            , cumulative_total_points as point_balance
                            , cumulative_total_points_lag_1m as point_balance_lag_1m
                            , first(cumulative_total_points_lag_1m) over (
                                partition by user_id, financial_year order by month
                            ) as point_balance_from_prev_fy

                            {%- for column in point_columns if column != 'total_points' %}
                            , {{ column }}
                            , cumulative_{{ column }}
                            , cumulative_{{ column }}_lag_1m
                            , fy_cumulative_{{ column }}
                            {%- endfor %}

                            , system_id
                            , created_month
                        from base_w_lag

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "point_columns": (
                        "total_redemptions",
                        "total_points",
                        "points_earned",
                        "points_redeemed",
                        "points_expired",
                        "amount_spent",
                    ),
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_REWARDS_MONTHLY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
