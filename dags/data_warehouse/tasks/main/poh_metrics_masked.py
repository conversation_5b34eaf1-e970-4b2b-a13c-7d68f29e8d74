import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.POH_METRICS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED, data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="proof_of_handover_order_metrics",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).HUB_HANDOVERS,
                view_name="hub_handovers",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    with base as (
                        select
                            cast(id as integer) id
                            , cast(arrival_lat as double) arrival_lat
                            , cast(arrival_lng as double) arrival_lng
                            , from_utc_timestamp(arrival_time, '{{ get_local_timezone }}') as arrival_time
                            , from_utc_timestamp(created_at, '{{ get_local_timezone }}') as created_at
                            , from_utc_timestamp(deleted_at, '{{ get_local_timezone }}') as deleted_at
                            , cast(distance_from_hub as double) as distance_from_hub
                            , final_parcel_count
                            , from_utc_timestamp(handover_time, '{{ get_local_timezone }}') as handover_time
                            , cast(hub_id as integer) hub_id
                            , cast(hub_lat as double) hub_lat
                            , cast(hub_lng as double) hub_lng
                            , cast(hub_staff_id as integer) hub_staff_id
                            , hub_staff_username
                            , num_photos
                            , original_parcel_count
                            , from_utc_timestamp(updated_at, '{{ get_local_timezone }}') as updated_at
                            , verification_type
                            , created_month
                            , (
                                to_unix_timestamp(handover_time, 'yyyy-MM-dd HH:mm:ss')
                                -
                                to_unix_timestamp(arrival_time, 'yyyy-MM-dd HH:mm:ss')
                            ) / cast(60 as double) as arrival_to_handover_minutes
                            , sqrt(
                                pow(hub_lat - arrival_lat, 2) + pow(hub_lng - arrival_lng, 2)
                            )*11139 as arrival_hub_distance
                        from hub_handovers
                        where system_id=='{{ system_id }}'
                    ),

                    /* Aggregate the order level table to obtain the corresponding
                    driver information as driver/route information is not present in the handover level
                    table */
                        aggregated_orders as (

                                Select distinct
                                    hub_handover_id
                                    , route_id
                                    , driver_id
                                    , driver_name
                                    , driver_type
                                from proof_of_handover_order_metrics
                    ),

                    /* Aggregate the order level table to obtain the corresponding
                    driver information as driver/route information is not present in the handover level table */
                        merge as (

                                select
                                    base.*
                                    , aggregated_orders.route_id
                                    , aggregated_orders.driver_id
                                    , aggregated_orders.driver_name
                                    , aggregated_orders.driver_type
                                    , row_number() over(partition by id, driver_id order by id) row_num
                                from base
                                left join aggregated_orders
                                    on base.id = aggregated_orders.hub_handover_id
                    ),


                    /* Create new metric: gps compliance flag from the arrival hub distance */
                        final as (

                            select
                                merge.id
                                , merge.arrival_lat
                                , merge.arrival_lng
                                , merge.distance_from_hub
                                , merge.hub_id
                                , merge.hub_lat
                                , merge.hub_lng
                                , merge.hub_staff_id
                                , merge.hub_staff_username
                                , merge.num_photos
                                , merge.original_parcel_count
                                , merge.final_parcel_count
                                , merge.verification_type
                                , merge.arrival_to_handover_minutes
                                , merge.arrival_hub_distance
                                , merge.route_id
                                , cast(merge.driver_id as bigint) as driver_id
                                , merge.driver_name
                                , merge.driver_type
                                , if(arrival_hub_distance < 500, 1, 0) gps_compliance_flag
                                , merge.arrival_time
                                , merge.handover_time
                                , merge.created_at
                                , merge.deleted_at
                                , merge.updated_at
                                , merge.created_month
                            from merge
                            where row_num = 1
                    )
                        select *
                        from final
                """,
                jinja_arguments={
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).POH_METRICS,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
