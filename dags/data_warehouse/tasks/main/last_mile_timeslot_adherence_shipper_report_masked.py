import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_REPORT_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_TIMESLOT_ADHERENCE_REPORT,
                view_name="last_mile_timeslot_adherence_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT
                    route_date
                    , depot_id
                    , depot_name
                    , shipper_id
                    , shipper_name
                    , timeslot
                    , COUNT_IF(timeslot_status == 'EARLY') AS early_attempts
                    , COUNT_IF(timeslot_status == 'ONTIME') AS ontime_attempts
                    , COUNT_IF(timeslot_status == 'LATE') AS late_attempts
                    , COUNT_IF(status == 'Success') AS successful_attempts
                    , COUNT_IF(status == 'Fail') AS failed_attempts
                    , COUNT(1) AS total_attempts
                    , DATE_FORMAT(route_date, 'yyyy-MM') AS created_month
                FROM
                    last_mile_timeslot_adherence_report
                WHERE route_date <= DATE('{{ measurement_datetime }}')
                GROUP BY {{ range(1, 7) | join(',') }}
                """,
                jinja_arguments={"system_id": system_id, "measurement_datetime": measurement_datetime},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
