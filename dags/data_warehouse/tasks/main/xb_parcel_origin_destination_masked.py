import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_PARCEL_ORIGIN_DESTINATION_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_PARCEL_ORIGIN_DESTINATION_MASKED,
    depends_on=(data_warehouse.CrossBorderDAG.Task.XB_SHIPMENT_PARCELS_ENRICHED_MASKED,),
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).XB_SHIPMENT_PARCELS_ENRICHED,
                view_name="xb_shipment_parcels_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PRODUCTS,
                view_name="products",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (

                    select
                        parcels.id as parcel_id
                        , parcels.tracking_id
                        , if(
                            products.origin_country = 'XX'
                                , shipment_parcels.shipment_origin_country
                                , products.origin_country
                        ) as parcel_origin_country
                        , if(
                            products.destination_country = 'XX'
                                , shipment_parcels.shipment_destination_country
                                , products.destination_country
                        ) as parcel_destination_country
                        , date_format(parcels.created_at, 'yyyy-MM') as created_month
                    from parcels
                    left join products
                        on parcels.product_id = products.id
                    left join xb_shipment_parcels_enriched as shipment_parcels
                        on parcels.id = shipment_parcels.parcel_id

                )

                select
                    parcel_id
                    , tracking_id
                    , parcel_origin_country
                    , if(parcel_origin_country in ('TH', 'VN', 'ID'), 7, 8) as origin_hour_adjustment
                    , parcel_destination_country
                    , if(parcel_destination_country in ('TH', 'VN', 'ID'), 7, 8) as destination_hour_adjustment
                    , case
                        when lower(parcel_destination_country) in ('my', 'sg', 'id', 'th', 'ph', 'vn')
                        then lower(base.parcel_destination_country)
                        else 'gl'
                    end as system_id
                    , created_month
                from base
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_PARCEL_ORIGIN_DESTINATION,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
