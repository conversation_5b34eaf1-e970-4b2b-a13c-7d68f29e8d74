import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_FOURTH_PARTY_HANDOVERS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_FOURTH_PARTY_HANDOVERS_MASKED,
    system_ids=(
        constants.SystemID.TH,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.EXTERNAL_XDOCK_ORDER_MAPPINGS_ENRICHED_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_STATUS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).EXTERNAL_XDOCK_ORDER_MAPPINGS_ENRICHED,
                view_name="external_xdock_order_mappings",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DESTINATIONS,
                view_name="order_destinations",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_STATUS_EVENTS,
                view_name="update_status_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="filtered_orders",
                jinja_template="""
                select
                    base.order_id
                    , base.delivery_dest_hub_id
                    , hubs.name as delivery_dest_hub_name
                    , base.created_month
                    , {{ selected_order_flag[system_id] }} as selected_order_flag
                from order_destinations base
                left join hubs_enriched hubs
                    on base.delivery_dest_hub_id = hubs.id
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "selected_order_flag": {
                        "th": "if(hubs.name like '4PL-%' or hubs.name like '%-4PL', 1, 0)",
                    },
                },
            ),
            base.TransformView(
                view_name="first_webhook_handover",
                jinja_template="""
                select
                    order_id
                    , min(event_datetime) as status_change_datetime
                from update_status_events
                where
                    user_id = 49738714 --This is the user_id for 'Webhook Receiver INTERNAL_SERVICES'
                    and old_granular_status = 'Arrived at Sorting Hub'
                    and new_granular_status = 'En-route to Sorting Hub'
                group by 1
                """,
            ),
            base.TransformView(
                view_name="add_timestamps",
                jinja_template="""
                select
                    base.order_id
                    , base.delivery_dest_hub_id
                    , base.delivery_dest_hub_name
                    , base.selected_order_flag
                    , external.type
                    , webhook.status_change_datetime as webhook_status_change_datetime
                    , base.created_month
                    , min(shipment_orders.shipment_completion_datetime) filter(
                        where {{ selected_shipments[system_id] }}
                    ) as shipment_completion_datetime
                from filtered_orders base
                left join external_xdock_order_mappings external
                    on base.order_id = external.order_id
                left join shipment_orders
                    on base.order_id = shipment_orders.order_id
                left join first_webhook_handover webhook
                    on base.order_id = webhook.order_id
                group by {{ range(1, 8) | join(',') }}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "selected_shipments": {
                        "th": """
                            shipment_orders.dest_hub_name like '4PL-%'
                            or shipment_orders.dest_hub_name like '%-4PL'
                            """,
                    },
                },
            ),
            base.TransformView(
                view_name="adjust_timestamps",
                jinja_template="""
                select
                    order_id
                    , delivery_dest_hub_id
                    , delivery_dest_hub_name
                    , selected_order_flag
                    , webhook_status_change_datetime
                    , shipment_completion_datetime
                    , if(selected_order_flag = 1, type, null) as fourth_party_partner_name
                    , if(selected_order_flag = 1
                        , coalesce(webhook_status_change_datetime, shipment_completion_datetime)
                        , null
                    ) as fourth_party_handover_time
                    , created_month
                from add_timestamps
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , delivery_dest_hub_id
                    , delivery_dest_hub_name
                    , selected_order_flag
                    , webhook_status_change_datetime
                    , shipment_completion_datetime
                    , fourth_party_partner_name
                    , fourth_party_handover_time
                    , case
                        when fourth_party_handover_time is null then null
                        when fourth_party_handover_time = webhook_status_change_datetime then 'webhook'
                        when fourth_party_handover_time = shipment_completion_datetime then 'shipment_completion'
                    end as fourth_party_handover_type
                    , created_month                  
                from adjust_timestamps
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_FOURTH_PARTY_HANDOVERS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
