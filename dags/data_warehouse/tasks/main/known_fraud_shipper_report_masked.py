import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON><PERSON>

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.KNOWN_FRAUD_SHIPPER_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.KNOWN_FRAUD_SHIPPER_REPORT_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.PRIORITISED_LAZADA_ORDERS_MASKED,
    ),
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PRIORITISED_LAZADA_ORDERS,
                view_name="prioritised_lazada_orders",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_INTERCEPTED_ORDERS,
                view_name="lazada_intercepted_orders",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="id_fraud_shipper_blacklist",
                jinja_template="""

                (
                    select
                        shipper_id
                        , seller_id
                        , date(intercepted_active_seller_timestamp) as intercepted_date
                        , 'Still active in Lazada platform' as laz_shipper_status
                    from lazada_intercepted_orders 
                    group by 1,2,3,4
                )

                union

                (
                    select
                        shipper_id
                        , seller_id
                        , date(intercepted_deactivated_seller_timestamp) as intercepted_date
                        , 'Not active on Lazada platform' as laz_shipper_status
                    from lazada_intercepted_orders
                    group by 1,2,3,4
                )

                """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""

                -- Check if orders are made by shippers/sellers whose orders were successfully intercepted

                select
                    prioritised_lazada_orders.order_id
                    , prioritised_lazada_orders.creation_date
                    , prioritised_lazada_orders.creation_datetime
                    , prioritised_lazada_orders.shipper_id
                    , prioritised_lazada_orders.seller_id
                    , max_by(laz_shipper_status,intercepted_date) as laz_shipper_status
                    , max_by(intercepted_date,intercepted_date) as intercepted_date
                    , if(max_by(laz_shipper_status,intercepted_date) is null,0,1) as known_fraud_shipper_flag
                    , prioritised_lazada_orders.created_month
                    , prioritised_lazada_orders.system_id
                from prioritised_lazada_orders
                left join id_fraud_shipper_blacklist on
                    prioritised_lazada_orders.shipper_id = id_fraud_shipper_blacklist.shipper_id
                    and coalesce(prioritised_lazada_orders.seller_id,'misssing') =
                    coalesce(id_fraud_shipper_blacklist.seller_id,'misssing')
                    and prioritised_lazada_orders.creation_date > id_fraud_shipper_blacklist.intercepted_date
                group by 1,2,3,4,5,9,10

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).KNOWN_FRAUD_SHIPPER_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()