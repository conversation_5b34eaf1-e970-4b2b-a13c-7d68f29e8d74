import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from datetime import timedelta
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.WebhookFreqSnapshotDAG.DAG_ID,
            task_id=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RTS_TRIGGER_EVENTS_MASKED,
        ),
    ),
    system_ids=(SystemID.GL,),
)


def get_task_config(spark, env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX + latest_partition,
                            view_name="prefix"
                            ),
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_STATUS_CONFIG,
                            view_name="webhook_status_configuration"
                            ),
            base.InputTable(path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS + latest_partition,
                            view_name="delivery_transaction_events",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES + latest_partition,
                            view_name="order_milestones",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=versioned_parquet_tables_masked.DataWarehouse(input_env).RTS_TRIGGER_EVENTS + latest_partition,
                            view_name="rts_trigger_events",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_REF_DATA + latest_partition,
                            view_name="shipper_ref_data",
                            input_range=lookback_ranges.input,
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Past 1 month Webhook Data from the completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = (measurement_datetime - timedelta(days=61)).strftime("%Y-%m-%d")

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK+latest_partition) \
        .filter((F.col("created_date") <= completion_date) & (F.col("created_date") >= start_date)) \
        .createOrReplaceTempView("webhook_log")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="completed_orders",
                jinja_template="""

                with 

                    rts_events as (

                            select
                                order_id
                                 , count(*) as rts_event_triggered
                             from rts_trigger_events
                             group by 1

                    )

                    , delivery_attempts as (

                            select
                                order_id
                                , count_if(type='delivery') as delivery_attempts
                                , count_if(type='rts') as rts_attempts
                            from delivery_transaction_events
                            group by 1

                    )

                select
                     order_milestones.order_id
                    , order_milestones.tracking_id
                    , order_milestones.granular_status
                    , order_milestones.creation_datetime
                    , order_milestones.delivery_success_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.is_pickup_required
                    , shipper_ref_data.collection_point
                    , attempts.delivery_attempts
                    , attempts.rts_attempts
                    , rts_events.rts_event_triggered
                    , date(order_milestones.delivery_success_datetime) as delivery_success_date
                    , order_milestones.system_id
                from order_milestones
                left join delivery_attempts attempts
                    on order_milestones.order_id = attempts.order_id
                left join rts_events
                    on order_milestones.order_id = rts_events.order_id
                left join shipper_ref_data
                    on order_milestones.tracking_id = shipper_ref_data.tracking_id
                join prefix on 
                     order_milestones.tracking_id like prefix.prefixes || '%'
                     and order_milestones.system_id = prefix.system_id
                where  
                    -- Filter for Completed and RTS Orders only
                    order_milestones.delivery_success_flag = 1
                    and date(order_milestones.delivery_success_datetime) = date_sub('{{measurement_datetime}}',1)
                    and order_milestones.granular_status in ('Completed','Returned to Sender')
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="completed_order_webhook",
                jinja_template="""

                 select /*+ BROADCAST(webhook_status_configuration) */
                    webhook_log.*
                    , delivery_success_datetime
                    , rts_trigger_datetime
                    , delivery_attempts
                    , rts_attempts
                    , rts_event_triggered
                    , granular_status
                    , is_pickup_required
                    , collection_point
                    , cast(webhook_status_configuration.status_code as int) as status_code
                    , webhook_status_configuration.status
                    , CASE  
                        WHEN webhook_log.shipper = 'Tiktok Domestic' 
                            THEN webhook_status_configuration.tt_domestic_ac
                        WHEN webhook_log.shipper = 'Tiktok XB LM' 
                            THEN webhook_status_configuration.tt_xb_lm_ac
                        WHEN webhook_log.shipper = 'Tiktok XB' 
                            THEN webhook_status_configuration.tt_xb_e2e_ac
                    END as tt_action_code_mapping
                    , CASE 
                        WHEN INSTR(webhook_log.url, 'tokopedia.com') > 0 THEN 1
                        ELSE 0
                    END AS is_tokopedia
                    , delivery_success_date
                from webhook_log
                join completed_orders on 
                    webhook_log.tracking_id = completed_orders.tracking_id
                    and webhook_log.system_id = completed_orders.system_id 
                left join webhook_status_configuration on
                    webhook_log.webhook_event_status = webhook_status_configuration.webhook_event_status
                    and coalesce(webhook_log.webhook_event_state,'empty') = 
                        coalesce(webhook_status_configuration.webhook_event_state,'empty')
                    and cast(coalesce(webhook_log.rts_flag,0) as int) = 
                        cast(coalesce(webhook_status_configuration.rts_flag,0) as int)
                    and (
                        case
                            -- Accomodate for Pricing Updated logic difference across platforms
                            when webhook_status_configuration.url is not null then
                                if(webhook_log.webhook_event_status = 'Pricing Updated', webhook_log.url,'empty') = 
                                    coalesce(webhook_status_configuration.url,'empty')
                            else 
                                 if(webhook_log.webhook_event_status = 'Pricing Updated'
                                    , array_contains(split(replace(webhook_status_configuration.shipper,', ',','),',')
                                    , webhook_log.shipper)
                                    , true)
                        end
                        )
                """
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "delivery_success_date"),
        update_latest_with_historical=True,
		enable_compaction=False,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    run(spark, task_config)
    spark.stop()