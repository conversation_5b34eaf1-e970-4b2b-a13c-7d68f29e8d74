import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON><PERSON>

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.CONSIGNEE_EMAIL_DOMAIN_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.CONSIGNEE_EMAIL_DOMAIN_REPORT_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.PRIORITISED_LAZADA_ORDERS_MASKED,
        data_warehouse.SLABreachDAG.Task.UPDATE_CONSIGNEE_EMAIL_EVENTS_MASKED,
    ),
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_EMAIL_DOMAIN_WHITELIST,
                view_name="id_fraud_email_domain_whitelist",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PRIORITISED_LAZADA_ORDERS,
                view_name="prioritised_lazada_orders",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).UPDATE_CONSIGNEE_EMAIL_EVENTS,
                view_name="update_consignee_email_events",
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="original_consignee_email_base",
                jinja_template="""

                -- Get original consignee email in the event of email update

                select
                    order_id
                    , event_timestamp
                    , original_consignee_email as email
                    , original_consignee_email_domain as email_domain
                    , created_month
                    , system_id
                from update_consignee_email_events
                where
                    change_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="original_consignee_email",
                jinja_template="""

                -- Get original consignee email (regardless of whether there is email update)

                select
                    prioritised_lazada_orders.order_id
                    , prioritised_lazada_orders.creation_datetime as event_timestamp
                    -- If there is no email update, get original email address from orders
                    -- Else, get original email address from original_consignee_email_base
                    , coalesce(original_consignee_email_base.email, prioritised_lazada_orders.to_email) as email
                    , coalesce(original_consignee_email_base.email_domain, prioritised_lazada_orders.to_email_domain) as email_domain
                    , coalesce(original_consignee_email_base.created_month, prioritised_lazada_orders.created_month) as created_month
                    , coalesce(original_consignee_email_base.system_id, prioritised_lazada_orders.system_id) as system_id
                from prioritised_lazada_orders
                left join original_consignee_email_base
                    on prioritised_lazada_orders.order_id = original_consignee_email_base.order_id

                """,
            ),
            base.TransformView(
                view_name="new_consignee_email",
                jinja_template="""

                -- Get update email information only if it is a Lazada order created in the L30D

                select
                    update_consignee_email_events.order_id
                    , update_consignee_email_events.event_timestamp
                    , update_consignee_email_events.new_consignee_email as email
                    , update_consignee_email_events.new_consignee_email_domain as email_domain
                    , update_consignee_email_events.created_month
                    , update_consignee_email_events.system_id
                from update_consignee_email_events
                join prioritised_lazada_orders
                    on update_consignee_email_events.order_id = prioritised_lazada_orders.order_id

                """,
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""

                (
                    select
                        event_timestamp
                        , order_id
                        , email
                        , email_domain
                        , created_month
                        , system_id
                    from original_consignee_email
                )

                union

                (
                    select
                        event_timestamp
                        , order_id
                        , email
                        , email_domain
                        , created_month
                        , system_id
                    from new_consignee_email
                )

                """,
            ),
            base.TransformView(
                view_name="base_enriched",
                jinja_template="""

                select
                    base.event_timestamp
                    , base.order_id
                    , base.email
                    , base.email_domain
                    -- Consignee email domain check
                    , if(id_fraud_email_domain_whitelist.domain is null,1,0) as consignee_email_domain_flag
                    , base.created_month
                    , base.system_id
                from base
                left join id_fraud_email_domain_whitelist on
                    base.email_domain = id_fraud_email_domain_whitelist.domain
                    and date(base.event_timestamp) >= to_date(id_fraud_email_domain_whitelist.added_date,'dd/MM/yyyy')


                """,
            ),
            base.TransformView(
                view_name="first_flag",
                jinja_template="""

                select
                    order_id
                    , event_timestamp as consignee_email_domain_first_flag_timestamp
                    , email_domain as consignee_email_domain_first_value
                    , created_month
                    , system_id
                from (

                    select
                        order_id
                        , event_timestamp
                        , email_domain
                        , rank() over (partition by order_id order by event_timestamp) as event_sequence
                        , created_month
                        , system_id
                    from base_enriched
                    where consignee_email_domain_flag = 1

                )
                where event_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="current_flag",
                jinja_template="""

                select
                    order_id
                    , email_domain as consignee_email_domain_current_value
                    , consignee_email_domain_flag as consignee_email_domain_current_flag
                    , created_month
                    , system_id
                from (

                    select
                        order_id
                        , event_timestamp
                        , email_domain
                        , consignee_email_domain_flag
                        , rank() over (partition by order_id order by event_timestamp desc) as event_sequence_desc
                        , created_month
                        , system_id
                    from base_enriched

                )
                where event_sequence_desc = 1

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    coalesce(current_flag.order_id,first_flag.order_id) as order_id
                    , first_flag.consignee_email_domain_first_flag_timestamp
                    , first_flag.consignee_email_domain_first_value
                    , current_flag.consignee_email_domain_current_flag
                    , current_flag.consignee_email_domain_current_value
                    , coalesce(current_flag.created_month,first_flag.created_month) as created_month
                    , coalesce(current_flag.system_id,first_flag.system_id) as system_id
                from current_flag
                full join first_flag
                    on current_flag.order_id = first_flag.order_id

                """,
            ),

        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CONSIGNEE_EMAIL_DOMAIN_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()