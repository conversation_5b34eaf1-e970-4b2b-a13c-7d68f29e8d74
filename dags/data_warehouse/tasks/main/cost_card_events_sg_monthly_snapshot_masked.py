import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MonthlySnapshotDAG.Task.COST_CARD_EVENTS_SG_MONTHLY_SNAPSHOT_MASKED + ".py",
    task_name=data_warehouse.MonthlySnapshotDAG.Task.COST_CARD_EVENTS_SG_MONTHLY_SNAPSHOT_MASKED,
    system_ids=(constants.SystemID.SG,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("cost_segment", "event_month")),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).COST_CARD_EVENTS_SG + latest_partition,
                view_name="cost_card_events_sg",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    *
                    , current_timestamp as snapshot_datetime
                    , current_date as snapshot_date
                from cost_card_events_sg

                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_EVENTS_SG_MONTHLY_SNAPSHOT,
        measurement_datetime=measurement_datetime,
        partition_by=("cost_segment", "event_month"),
        write_mode='merge',
        primary_keys=["snapshot_date", "order_id", "event_sequence"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()