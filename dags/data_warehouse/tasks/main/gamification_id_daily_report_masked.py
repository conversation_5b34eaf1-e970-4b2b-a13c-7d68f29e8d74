import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_DAILY_REPORT_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_DAILY_REPORT_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_PLANNED_PARCELS_EVENTS_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_PICKUP_EVENTS_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(
            hive_schema="pricing",
        ),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    details_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime.subtract(hours=18), measurement_datetime, 0, 6)
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime.subtract(hours=18), measurement_datetime, 0, 1)
    if enable_full_run:
        details_lookback_ranges = base.LookBackRange(None, None)
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    formatted_timestamp = measurement_datetime.strftime("%Y-%m-%d %H:%M:%S") + "Z"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                system_id=system_id,
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAYPOINTS_ENRICHED,
                view_name="waypoints_enriched",
                system_id=system_id,
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_PLANNED_PARCELS_EVENTS,
                view_name="gamification_id_planned_parcels_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_PICKUP_EVENTS,
                view_name="gamification_id_pickup_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_EX_JABO_POINTS,
                view_name="ex_jabo_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_JABO_POINTS,
                view_name="jabo_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PARCEL_RATES,
                view_name="parcel_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_VOLUME_BONUSES,
                view_name="volume_bonus",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_HUB_DATA_ADJUSTED,
                view_name="id_hub_data",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_ZONE_DATA_ADJUSTED,
                view_name="id_zone_data",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DENSITY_PRIORITY,
                view_name="id_density_priority",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_POINTS,
                view_name="id_delivery_parcel_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PICKUP_PARCEL_POINTS,
                view_name="id_pickup_parcel_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS,
                view_name="id_delivery_parcel_exception_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS_MAP,
                view_name="id_delivery_parcel_exception_points_map",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                system_id=system_id,
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS,
                view_name="reservations",
                input_range=details_lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=details_lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=details_lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_TAGS,
                view_name="order_tags",
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_dws",
                jinja_template="""
                with final as (
                
                    select
                        order_id
                        , max_by(new_parcel_size, scan_datetime) as latest_size
                        , max_by(new_width, scan_datetime) as latest_width
                        , max_by(new_height, scan_datetime) as latest_height
                        , max_by(new_length, scan_datetime) as latest_length
                        , max_by(new_weight, scan_datetime) as latest_weight
                    from dim_weight_scans_base
                    group by 1
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="reservations_cleaned",
                jinja_template="""
                with base as (
                
                    select 
                        legacy_id as waypoint_id
                        , route_id
                        , created_at
                    from waypoints_enriched

                ),
                last_route_waypoint as (
                
                    select
                        waypoint_id
                        , max_by(route_id, created_at) as route_id
                    from base
                    group by 1

                ),
                
                final as (
                
                    select distinct
                        last_route_waypoint.route_id
                    from reservations
                    left join last_route_waypoint
                        on reservations.waypoint_id = last_route_waypoint.waypoint_id
                    where reservations.deleted_at is null
                        and last_route_waypoint.route_id is not null
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="id_zone_data_cleaned",
                jinja_template="""
                with final as (

                    select
                        *
                    from id_zone_data
                    where density_category != '#N/A'

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="route_attributes",
                jinja_template="""
                with base as (

                    select
                        route_logs.system_id
                        , route_logs.legacy_id as route_id
                        , route_logs.date as route_logs_date
                        , route_logs.driver_id
                        , drivers_enriched.display_name as driver_display_name
                        , drivers_enriched.driver_type
                        , drivers_enriched.hub_id as driver_hub_id
                        , driver_hub.region as driver_hub_region
                        , route_logs.hub_id as route_hub_id
                        , hubs_enriched.name as route_hub_name
                        , hubs_enriched.region as route_hub_region
                        , case
                            when lower(hubs_enriched.region) like '%sumatera%' then 'sumatera'
                            else lower(hubs_enriched.region)
                            end as route_hub_region_scheme
                        , route_logs.zone_id
                        , zones.name as zone_name
                        , lower(id_zone_data_cleaned.final_density) as zone_density
                        , lower(id_zone_data_cleaned.density_category) as zone_density_category
                        , lower(id_zone_data_cleaned.umk_category) as zone_umk_category
                        , lower(id_hub_data.final_density) as driver_hub_density
                        , lower(id_hub_data.density_category) as driver_hub_density_category
                        , lower(id_hub_data.umk_category) as driver_hub_umk_category
                    from route_logs
                    left join drivers_enriched
                        on route_logs.driver_id = drivers_enriched.id
                    left join hubs_enriched as driver_hub
                        on drivers_enriched.hub_id = driver_hub.id
                    left join hubs_enriched
                        on route_logs.hub_id = hubs_enriched.id
                    left join zones
                        on route_logs.zone_id = zones.legacy_zone_id
                        and zones.deleted_at is null
                    left join id_zone_data_cleaned
                        on route_logs.zone_id = id_zone_data_cleaned.zone_id
                        and date_format(route_logs.date, 'yyyy-MM') = id_zone_data_cleaned.effective_month
                    left join id_hub_data
                        on drivers_enriched.hub_id = id_hub_data.hub_id
                        and date_format(route_logs.date, 'yyyy-MM') = id_hub_data.effective_month

                ),
                final as (
                
                    select
                        *
                        , case 
                            when route_hub_region_scheme = 'greater jakarta' then 'greater jakarta'
                            when route_hub_region_scheme in ('east java', 'central java', 'west java') then 'java'
                            when route_hub_region_scheme in ('sumatera','kalimantan','sulawesi','bali', 'nusa', 'maluku', 'papua', 'bali nusa') then 'ex java'
                            end as region_group
                        , coalesce(zone_density,driver_hub_density) as density
                        , coalesce(zone_density_category,driver_hub_density_category) as density_category
                        , coalesce(zone_umk_category,driver_hub_umk_category) as umk_category
                    from base

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="planned_parcels",
                jinja_template="""
                with base as (

                    select
                        order_id
                        , route_id
                        , max_by(driver_id, created_at) filter(where driver_id is not null) as driver_id
                        , max_by(type, created_at) as type
                        , max(created_at) as created_at
                    from gamification_id_planned_parcels_events
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (

                    select
                        driver_id
                        , date(created_at) as route_date
                        , count(order_id) as planned_parcels
                    from base
                    where type = 24
                    group by {{ range(1, 3) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="route_deliveries",
                jinja_template="""
                with data as (

                    select
                        'id' as system_id
                        , transactions.order_id
                        , transactions.route_id
                        , transactions.service_end_time
                        , transactions.status
                        , lower(shipper_attributes.sales_channel) as sales_channel
                        , case
                            when orders.parcel_size_id = 0 then 's'
                            when orders.parcel_size_id = 1 then 'm'
                            when orders.parcel_size_id = 2 then 'l'
                            when orders.parcel_size_id = 3 then 'xl'
                            when orders.parcel_size_id = 4 then 'xxl'
                            when orders.parcel_size_id = 5 then 'xs'
                            end as orders_size
                        , latest_dws.latest_size
                        , cast(orders.rts as bigint) as rts_flag
                        , cast(get_json_object(orders.dimensions, '$.width') as double) as orders_width
                        , cast(get_json_object(orders.dimensions, '$.height') as double) as orders_height
                        , cast(get_json_object(orders.dimensions, '$.length') as double) as orders_length
                        , cast(get_json_object(orders.dimensions, '$.weight') as double) as orders_weight
                        , latest_dws.latest_width
                        , latest_dws.latest_height
                        , latest_dws.latest_length
                        , latest_dws.latest_weight
                        , case when order_tags.order_id is not null then 1 else 0 end as bulky_revise_flag
                    from transactions
                    left join orders
                        on transactions.order_id = orders.id
                    left join latest_dws
                        on transactions.order_id = latest_dws.order_id
                    left join shipper_attributes
                        on orders.global_shipper_id = shipper_attributes.id
                    left join order_tags
                        on transactions.order_id = order_tags.order_id
                        and order_tags.tag_id = 97
                    where transactions.deleted_at is null
                        and transactions.type = 'DD'
                        and transactions.status in ('Success','Fail')

                ),
                base as (
                
                    select
                        *
                        , coalesce(latest_size, orders_size) as size
                        , coalesce(latest_width, orders_width) as width
                        , coalesce(latest_height, orders_height) as height
                        , coalesce(latest_length, orders_length) as length
                        , coalesce(latest_weight, orders_weight) as weight
                        , date(from_utc_timestamp(service_end_time, {{ get_local_timezone }})) as route_date
                    from data

                ),
                attributes as (

                    select
                        base.*
                        , route_attributes.driver_hub_id
                        , route_attributes.region_group
                        , route_attributes.density_category
                    from base
                    left join route_attributes
                        on base.route_id = route_attributes.route_id

                ),
                category as (

                    select
                        route_id
                        , route_date
                        , date_format(route_date, 'yyyy-MM') as route_month
                        , driver_hub_id
                        , region_group
                        , density_category
                        , status
                        , case
                            when status = 'Fail' then 'failed parcel'
                            when region_group in ('greater jakarta', 'ex java', 'java') then
                                case
                                    when rts_flag = 1 and size in ('xs','s','m') then 'rts regular'
                                    when rts_flag = 1 and size in ('l','xl','xxl') then 'rts bulky'
                                    when rts_flag = 0 and (width > 50 or height > 50 or length >50 or weight >5) then 'superbulky'
                                    when rts_flag = 0 and bulky_revise_flag = 1 then 'bulky revise'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'standard regular'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'standard bulky'
                                    when rts_flag = 0 and sales_channel = 'field sales' and size in ('xs', 's', 'm') then 'fs regular'
                                    when rts_flag = 0 and sales_channel = 'field sales' and size in ('l', 'xl', 'xxl') then 'fs bulky'
                                    when rts_flag = 0 and sales_channel = 'corp sales' and size in ('xs', 's', 'm') then 'cs regular'
                                    when rts_flag = 0 and sales_channel = 'corp sales' and size in ('l', 'xl', 'xxl') then 'cs bulky'
                                    when rts_flag = 0 and sales_channel = 'retail' and size in ('xs', 's', 'm') then 'retail regular'
                                    when rts_flag = 0 and sales_channel = 'retail' and size in ('l', 'xl', 'xxl') then 'retail bulky'
                                    end
                            end as display_category
                        , case 
                            when status = 'Fail' then 'failed parcel'
                            when region_group in ('greater jakarta', 'ex java', 'java') then
                                case
                                    when rts_flag = 1 and size in ('xs','s','m') then 'rts regular'
                                    when rts_flag = 1 and size in ('l','xl','xxl') then 'rts bulky'
                                    when rts_flag = 0 and (width > 50 or height > 50 or length >50 or weight >5) then 'superbulky'
                                    when rts_flag = 0 and bulky_revise_flag = 1 then 'bulky revise'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'standard regular'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'standard bulky'
                                    when rts_flag = 0 and sales_channel in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'non standard regular'
                                    when rts_flag = 0 and sales_channel in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'non standard bulky'
                                end
                            end as join_category
                    from attributes

                ),
                join as (
                
                    select
                        category.*
                        , id_delivery_parcel_points.points
                    from category
                    left join id_delivery_parcel_points
                        on category.region_group = id_delivery_parcel_points.region_group
                        and category.density_category = id_delivery_parcel_points.density
                        and category.join_category = id_delivery_parcel_points.category
                
                ),
                exception_join as (
                
                    select
                        join.*
                        , coalesce(id_delivery_parcel_exception_points_map.points, id_delivery_parcel_exception_points.points, join.points) as adjusted_points
                    from join
                    left join id_delivery_parcel_exception_points
                        on join.route_month = id_delivery_parcel_exception_points.effective_month
                        and join.join_category = id_delivery_parcel_exception_points.category
                        and join.driver_hub_id = id_delivery_parcel_exception_points.hub_id
                    left join id_delivery_parcel_exception_points_map
                        on id_delivery_parcel_exception_points.effective_month = id_delivery_parcel_exception_points_map.effective_month
                        and id_delivery_parcel_exception_points.category = id_delivery_parcel_exception_points_map.category
                        and join.density_category = id_delivery_parcel_exception_points_map.density_category

                ),
                final as (
                
                    select  
                        route_id
                        , route_date
                        , sum(case when display_category = 'fs regular' then 1 else 0 end) as delivered_non_rts_fs_regular
                        , sum(case when display_category = 'fs bulky' then 1 else 0 end) as delivered_non_rts_fs_bulky
                        , sum(case when display_category = 'cs regular' then 1 else 0 end) as delivered_non_rts_cs_regular
                        , sum(case when display_category = 'cs bulky' then 1 else 0 end) as delivered_non_rts_cs_bulky
                        , sum(case when display_category = 'retail regular' then 1 else 0 end) as delivered_non_rts_retail_regular
                        , sum(case when display_category = 'retail bulky' then 1 else 0 end) as delivered_non_rts_retail_bulky
                        , sum(case when display_category = 'standard regular' then 1 else 0 end) as delivered_non_rts_standard_regular
                        , sum(case when display_category = 'standard bulky' then 1 else 0 end) as delivered_non_rts_standard_bulky
                        , sum(case when display_category in ('fs regular', 'cs regular', 'retail regular') then 1 else 0 end) as delivered_non_rts_fs_cs_retail_regular
                        , sum(case when display_category in ('fs bulky', 'cs bulky', 'retail bulky') then 1 else 0 end) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(case when display_category = 'rts regular' then 1 else 0 end) as delivered_rts_regular
                        , sum(case when display_category = 'rts bulky' then 1 else 0 end) as delivered_rts_bulky
                        , sum(case when display_category = 'superbulky' then 1 else 0 end) as delivered_non_rts_superbulky
                        , sum(case when display_category = 'bulky revise' then 1 else 0 end) as delivered_non_rts_bulky_revise

                        , sum(case when join_category = 'standard regular' then adjusted_points else 0 end) as delivered_non_rts_standard_regular_points
                        , sum(case when join_category = 'standard bulky' then adjusted_points else 0 end) as delivered_non_rts_standard_bulky_points
                        , sum(case when join_category = 'non standard regular' then adjusted_points else 0 end) as delivered_non_rts_fs_cs_retail_regular_points
                        , sum(case when join_category = 'non standard bulky' then adjusted_points else 0 end) as delivered_non_rts_fs_cs_retail_bulky_points
                        , sum(case when join_category = 'rts regular' then adjusted_points else 0 end) as delivered_rts_regular_points
                        , sum(case when join_category = 'rts bulky' then adjusted_points else 0 end) as delivered_rts_bulky_points
                        , sum(case when join_category = 'superbulky' then adjusted_points else 0 end) as delivered_non_rts_superbulky_points
                        , sum(case when join_category = 'bulky revise' then adjusted_points else 0 end) as delivered_non_rts_bulky_revise_points

                        , sum(case when status = 'Fail' then 1 else 0 end) as failed_parcels
                    from exception_join
                    group by {{ range(1, 3) | join(',') }}
                )

                select * from final

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="route_pickups",
                jinja_template="""
                with grouped as (

                    select
                        order_id
                        , route_id
                        , waypoint_id
                        , min(date(created_at)) as route_date
                    from gamification_id_pickup_events
                    group by {{ range(1, 4) | join(',') }}

                ),
                sizes as (

                    select
                        grouped.order_id
                        , grouped.route_id
                        , grouped.waypoint_id
                        , grouped.route_date
                        , case
                            when orders.parcel_size_id = 0 then 's'
                            when orders.parcel_size_id = 1 then 'm'
                            when orders.parcel_size_id = 2 then 'l'
                            when orders.parcel_size_id = 3 then 'xl'
                            when orders.parcel_size_id = 4 then 'xxl'
                            when orders.parcel_size_id = 5 then 'xs'
                            end as orders_size
                        , latest_dws.latest_size
                    from grouped
                    left join orders
                        on grouped.order_id = orders.id
                    left join latest_dws
                        on grouped.order_id = latest_dws.order_id

                ),
                final_size as (
                
                    select
                        *
                        , coalesce(latest_size, orders_size) as size
                    from sizes
                    
                ),
                category as (

                    select
                        route_id
                        , waypoint_id
                        , route_date
                        , least(coalesce(sum(case when size in ('xs','s','m') then 1 else 0 end), 0), 100) as picked_regular
                        , sum(case when size in ('l','xl','xxl') then 1 else 0 end) as picked_bulky
                    from final_size
                    group by {{ range(1, 4) | join(',') }}

                ),
                pre_final as (
                
                    select
                        route_id
                        , route_date
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky
                    from category
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (
                
                    select
                        *
                        , picked_regular * (select points from id_pickup_parcel_points where category = 'regular pickup') as picked_regular_points
                        , picked_bulky * (select points from id_pickup_parcel_points where category = 'bulky pickup') as picked_bulky_points
                    from pre_final
                    
                )

                select * from final

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="route_points",
                jinja_template="""
                with route_values as (
                
                    select
                        coalesce(route_deliveries.route_id, route_pickups.route_id) as route_id
                        , coalesce(route_deliveries.route_date, route_pickups.route_date) as route_date
                        , route_deliveries.delivered_non_rts_fs_regular
                        , route_deliveries.delivered_non_rts_fs_bulky
                        , route_deliveries.delivered_non_rts_cs_regular
                        , route_deliveries.delivered_non_rts_cs_bulky
                        , route_deliveries.delivered_non_rts_retail_regular
                        , route_deliveries.delivered_non_rts_retail_bulky
                        , route_deliveries.delivered_non_rts_standard_regular
                        , route_deliveries.delivered_non_rts_standard_bulky
                        , route_deliveries.delivered_non_rts_fs_cs_retail_regular
                        , route_deliveries.delivered_non_rts_fs_cs_retail_bulky
                        , route_deliveries.delivered_non_rts_superbulky
                        , route_deliveries.delivered_non_rts_bulky_revise
                        , route_deliveries.delivered_rts_regular
                        , route_deliveries.delivered_rts_bulky
                        , route_deliveries.failed_parcels
                        , route_pickups.picked_regular
                        , route_pickups.picked_bulky

                        , route_deliveries.delivered_non_rts_standard_regular_points
                        , route_deliveries.delivered_non_rts_standard_bulky_points
                        , route_deliveries.delivered_non_rts_fs_cs_retail_regular_points
                        , route_deliveries.delivered_non_rts_fs_cs_retail_bulky_points
                        , route_deliveries.delivered_non_rts_superbulky_points
                        , route_deliveries.delivered_non_rts_bulky_revise_points
                        , route_deliveries.delivered_rts_regular_points
                        , route_deliveries.delivered_rts_bulky_points
                        , route_pickups.picked_regular_points
                        , route_pickups.picked_bulky_points
                    from route_deliveries
                    full outer join route_pickups
                        on route_deliveries.route_id = route_pickups.route_id
                        and route_deliveries.route_date = route_pickups.route_date
                ),
                base as (
                
                    select
                        route_attributes.*
                        , coalesce(route_values.route_date, route_attributes.route_logs_date) as route_date
                        , coalesce(route_values.delivered_non_rts_fs_regular,0) as delivered_non_rts_fs_regular
                        , coalesce(route_values.delivered_non_rts_fs_bulky,0) as delivered_non_rts_fs_bulky
                        , coalesce(route_values.delivered_non_rts_cs_regular,0) as delivered_non_rts_cs_regular
                        , coalesce(route_values.delivered_non_rts_cs_bulky,0) as delivered_non_rts_cs_bulky
                        , coalesce(route_values.delivered_non_rts_retail_regular,0) as delivered_non_rts_retail_regular
                        , coalesce(route_values.delivered_non_rts_retail_bulky,0) as delivered_non_rts_retail_bulky
                        , coalesce(route_values.delivered_non_rts_standard_regular,0) as delivered_non_rts_standard_regular
                        , coalesce(route_values.delivered_non_rts_standard_bulky,0) as delivered_non_rts_standard_bulky
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_regular,0) as delivered_non_rts_fs_cs_retail_regular
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_bulky,0) as delivered_non_rts_fs_cs_retail_bulky
                        , coalesce(route_values.delivered_non_rts_superbulky,0) as delivered_non_rts_superbulky
                        , coalesce(route_values.delivered_non_rts_bulky_revise,0) as delivered_non_rts_bulky_revise
                        , coalesce(route_values.delivered_rts_regular,0) as delivered_rts_regular
                        , coalesce(route_values.delivered_rts_bulky,0) as delivered_rts_bulky
                        , coalesce(route_values.picked_regular,0) as picked_regular
                        , coalesce(route_values.picked_bulky,0) as picked_bulky
                        , coalesce(route_values.failed_parcels,0) as failed_parcels

                        , coalesce(route_values.delivered_non_rts_standard_regular_points,0) as delivered_non_rts_standard_regular_points
                        , coalesce(route_values.delivered_non_rts_standard_bulky_points,0) as delivered_non_rts_standard_bulky_points
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_regular_points,0) as delivered_non_rts_fs_cs_retail_regular_points
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_bulky_points,0) as delivered_non_rts_fs_cs_retail_bulky_points
                        , coalesce(route_values.delivered_non_rts_superbulky_points,0) as delivered_non_rts_superbulky_points
                        , coalesce(route_values.delivered_non_rts_bulky_revise_points,0) as delivered_non_rts_bulky_revise_points
                        , coalesce(route_values.delivered_rts_regular_points,0) as delivered_rts_regular_points
                        , coalesce(route_values.delivered_rts_bulky_points,0) as delivered_rts_bulky_points
                        , coalesce(route_values.picked_regular_points,0) as picked_regular_points
                        , coalesce(route_values.picked_bulky_points,0) as picked_bulky_points
                    from route_attributes
                    left join route_values
                        on route_attributes.route_id = route_values.route_id
                    left join reservations_cleaned
                        on route_attributes.route_id = reservations_cleaned.route_id
                    where 
                        route_values.route_id is not null
                        or reservations_cleaned.route_id is not null

                ),
                final as (
                
                    select
                        *
                        , (delivered_non_rts_standard_regular_points
                            + delivered_non_rts_standard_bulky_points
                            + delivered_non_rts_fs_cs_retail_regular_points
                            + delivered_non_rts_fs_cs_retail_bulky_points
                            + delivered_non_rts_superbulky_points
                            + delivered_non_rts_bulky_revise_points
                            + delivered_rts_regular_points
                            + delivered_rts_bulky_points
                            + picked_regular_points
                            + picked_bulky_points
                            ) as total_ocd_points
                    from base

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="daily_base",
                jinja_template="""
                with base as (

                    select
                        system_id
                        , route_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , driver_hub_id
                        , driver_hub_region
                        , driver_hub_density_category
                        , driver_hub_umk_category
                        , route_hub_region_scheme
                        , route_hub_region
                        , route_hub_id
                        , route_hub_name
                        , zone_id
                        , zone_name
                        , coalesce(zone_density,driver_hub_density) as density
                        , coalesce(zone_density_category,driver_hub_density_category) as density_category
                        , coalesce(zone_umk_category,driver_hub_umk_category) as umk_category
                        , total_ocd_points as total_points
                    from route_points

                ),
                density_ranking as (

                    select
                        base.*
                        , cast(id_density_priority.rank as int) as density_rank
                        , cast(umk_category as int) as umk_rank
                    from base
                    left join id_density_priority
                        on base.density_category = lower(id_density_priority.density_category)

                ),
                combined_rank as (

                    select
                        *
                        , row_number() over (partition by route_date, driver_id order by total_points desc, density_rank asc, umk_rank desc) as pref_rank
                    from density_ranking

                ),
                filtered as (

                    select
                        system_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , driver_hub_id
                        , driver_hub_region
                        , driver_hub_density_category
                        , driver_hub_umk_category
                        , route_hub_region_scheme
                        , route_hub_region
                        , route_hub_id
                        , route_hub_name
                        , zone_id
                        , zone_name
                        , density
                        , density_category
                        , umk_category
                    from combined_rank
                    where pref_rank = 1

                ),
                sum as (

                    select
                        route_date
                        , driver_id
                        , sum(delivered_non_rts_fs_regular) as delivered_non_rts_fs_regular
                        , sum(delivered_non_rts_fs_bulky) as delivered_non_rts_fs_bulky
                        , sum(delivered_non_rts_cs_regular) as delivered_non_rts_cs_regular
                        , sum(delivered_non_rts_cs_bulky) as delivered_non_rts_cs_bulky
                        , sum(delivered_non_rts_retail_regular) as delivered_non_rts_retail_regular
                        , sum(delivered_non_rts_retail_bulky) as delivered_non_rts_retail_bulky
                        , sum(delivered_rts_regular) as delivered_rts_regular
                        , sum(delivered_rts_bulky) as delivered_rts_bulky
                        , sum(delivered_non_rts_fs_cs_retail_regular) as delivered_non_rts_fs_cs_retail_regular
                        , sum(delivered_non_rts_fs_cs_retail_bulky) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(delivered_non_rts_standard_regular) as delivered_non_rts_standard_regular
                        , sum(delivered_non_rts_standard_bulky) as delivered_non_rts_standard_bulky
                        , sum(delivered_non_rts_superbulky) as delivered_non_rts_superbulky
                        , sum(delivered_non_rts_bulky_revise) as delivered_non_rts_bulky_revise
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky

                        , sum(delivered_non_rts_fs_cs_retail_regular_points) as delivered_non_rts_fs_cs_retail_regular_points
                        , sum(delivered_non_rts_fs_cs_retail_bulky_points) as delivered_non_rts_fs_cs_retail_bulky_points
                        , sum(delivered_non_rts_standard_regular_points) as delivered_non_rts_standard_regular_points
                        , sum(delivered_non_rts_standard_bulky_points) as delivered_non_rts_standard_bulky_points
                        , sum(delivered_non_rts_superbulky_points) as delivered_non_rts_superbulky_points
                        , sum(delivered_non_rts_bulky_revise_points) as delivered_non_rts_bulky_revise_points
                        , sum(delivered_rts_regular_points) as delivered_rts_regular_points
                        , sum(delivered_rts_bulky_points) as delivered_rts_bulky_points
                        , sum(picked_regular_points) as picked_regular_points
                        , sum(picked_bulky_points) as picked_bulky_points
                        , sum(total_ocd_points) as total_ocd_points

                        , sum(failed_parcels) as failed_parcels
                    from route_points
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (

                    select
                        filtered.system_id
                        , filtered.route_date
                        , date_format(filtered.route_date, 'yyyy-MM') as route_month
                        , filtered.driver_id
                        , filtered.driver_display_name
                        , filtered.driver_type
                        , filtered.driver_hub_id
                        , filtered.driver_hub_region
                        , filtered.driver_hub_density_category
                        , filtered.driver_hub_umk_category
                        , filtered.route_hub_region_scheme
                        , filtered.route_hub_region
                        , filtered.route_hub_id
                        , filtered.route_hub_name
                        , filtered.zone_id
                        , filtered.zone_name
                        , filtered.density
                        , filtered.density_category
                        , filtered.umk_category
                        , sum.delivered_non_rts_fs_regular
                        , sum.delivered_non_rts_fs_bulky
                        , sum.delivered_non_rts_cs_regular
                        , sum.delivered_non_rts_cs_bulky
                        , sum.delivered_non_rts_retail_regular
                        , sum.delivered_non_rts_retail_bulky
                        , sum.delivered_non_rts_standard_regular
                        , sum.delivered_non_rts_standard_bulky
                        , sum.delivered_non_rts_fs_cs_retail_regular
                        , sum.delivered_non_rts_fs_cs_retail_bulky
                        , sum.delivered_non_rts_superbulky
                        , sum.delivered_non_rts_bulky_revise
                        , sum.delivered_rts_regular
                        , sum.delivered_rts_bulky
                        , sum.picked_regular
                        , sum.picked_bulky
                        , sum.delivered_non_rts_standard_regular_points
                        , sum.delivered_non_rts_standard_bulky_points
                        , sum.delivered_non_rts_fs_cs_retail_regular_points
                        , sum.delivered_non_rts_fs_cs_retail_bulky_points
                        , sum.delivered_non_rts_superbulky_points
                        , sum.delivered_non_rts_bulky_revise_points
                        , sum.delivered_rts_regular_points
                        , sum.delivered_rts_bulky_points
                        , sum.picked_regular_points
                        , sum.picked_bulky_points
                        , sum.total_ocd_points
                        , sum.failed_parcels
                    from filtered
                    left join sum
                        on filtered.route_date = sum.route_date
                        and filtered.driver_id = sum.driver_id
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="courier_monthly_density",
                jinja_template="""
                with base as (

                    select
                        daily_base.driver_id
                        , daily_base.route_month
                        , daily_base.density_category
                        , cast(id_density_priority.rank as int) as density_rank
                        , count(daily_base.route_date) as density_frequency
                    from daily_base
                    left join id_density_priority
                        on daily_base.density_category = lower(id_density_priority.density_category)
                    group by {{ range(1, 5) | join(',') }}

                ),
                ranking as (

                    select
                        driver_id
                        , route_month
                        , density_category
                        , density_rank
                        , density_frequency
                        , row_number() over (partition by route_month, driver_id order by density_frequency desc, density_rank asc) as pref_rank
                    from base

                ),
                final as(

                    select
                        driver_id
                        , route_month
                        , density_category
                    from ranking
                    where pref_rank = 1

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="bonus_rank",
                jinja_template="""
                with final as (

                    select
                        region
                        , effective_month
                        , lower(density) as density
                        , volume_bonus.range_start as target
                        , volume_bonus.bonus as amount
                        , volume_bonus.range_start
                        , volume_bonus.range_end
                        , row_number() over (partition by region, effective_month, density order by bonus) as rank
                    from volume_bonus

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="bonus_targets",
                jinja_template="""
                with ref as (

                    select
                        region
                        , effective_month
                        , density
                        , max(case when rank = 2 then target else null end) as bonus_target_1
                        , max(case when rank = 2 then amount else null end) as bonus_amount_1
                        , max(case when rank = 3 then target else null end) as bonus_target_2
                        , max(case when rank = 3 then amount else null end) as bonus_amount_2
                        , max(case when rank = 4 then target else null end) as bonus_target_3
                        , max(case when rank = 4 then amount else null end) as bonus_amount_3
                        , max(case when rank = 5 then target else null end) as bonus_target_4
                        , max(case when rank = 5 then amount else null end) as bonus_amount_4
                    from bonus_rank
                    group by 1,2,3

                ),
                final as (

                    select
                        daily_base.driver_id
                        , daily_base.route_date
                        , ref.bonus_target_1
                        , ref.bonus_amount_1
                        , ref.bonus_target_2
                        , ref.bonus_amount_2
                        , ref.bonus_target_3
                        , ref.bonus_amount_3
                        , ref.bonus_target_4
                        , ref.bonus_amount_4
                    from daily_base
                    left join ref
                        on daily_base.route_hub_region_scheme = ref.region
                        and daily_base.density = ref.density
                        and daily_base.route_month = ref.effective_month

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="hub_umk",
                jinja_template="""
                with final as (

                    select
                        hub_id
                        , effective_month
                        , min_by(umk_category,id) as umk_category
                        , min_by(daily_umk,id) as daily_umk
                    from id_zone_data
                    group by 1,2

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                with base as (

                    select
                        daily_base.system_id
                        , daily_base.route_date
                        , daily_base.route_month
                        , courier_monthly_density.density_category as monthly_density_category
                        , hub_umk.umk_category as monthly_umk_category
                        , hub_umk.daily_umk as monthly_umk_value
                        , daily_base.driver_id
                        , daily_base.driver_display_name
                        , daily_base.driver_type
                        , daily_base.driver_hub_id
                        , daily_base.driver_hub_region
                        , daily_base.route_hub_region_scheme
                        , daily_base.route_hub_region
                        , daily_base.route_hub_id
                        , daily_base.route_hub_name
                        , daily_base.zone_id
                        , daily_base.zone_name
                        , daily_base.density
                        , daily_base.density_category
                        , daily_base.umk_category
                        , daily_base.picked_regular
                        , daily_base.picked_bulky
                        , coalesce(planned_parcels.planned_parcels,0) as planned_parcels
                        , coalesce(daily_base.failed_parcels,0) as failed_parcels
                        , daily_base.delivered_non_rts_fs_regular
                        , daily_base.delivered_non_rts_fs_bulky
                        , daily_base.delivered_non_rts_cs_regular
                        , daily_base.delivered_non_rts_cs_bulky
                        , daily_base.delivered_non_rts_retail_regular
                        , daily_base.delivered_non_rts_retail_bulky
                        , daily_base.delivered_non_rts_standard_regular
                        , daily_base.delivered_non_rts_standard_bulky
                        , daily_base.delivered_non_rts_fs_cs_retail_regular
                        , daily_base.delivered_non_rts_fs_cs_retail_bulky
                        , daily_base.delivered_non_rts_superbulky
                        , daily_base.delivered_non_rts_bulky_revise
                        , daily_base.delivered_rts_regular
                        , daily_base.delivered_rts_bulky
                        , daily_base.picked_regular_points
                        , daily_base.picked_bulky_points
                        , daily_base.delivered_non_rts_standard_regular_points
                        , daily_base.delivered_non_rts_standard_bulky_points
                        , daily_base.delivered_non_rts_fs_cs_retail_regular_points
                        , daily_base.delivered_non_rts_fs_cs_retail_bulky_points
                        , daily_base.delivered_non_rts_superbulky_points
                        , daily_base.delivered_non_rts_bulky_revise_points
                        , daily_base.delivered_rts_regular_points
                        , daily_base.delivered_rts_bulky_points
                        , case 
                            when courier_monthly_density.density_category = 'ex' then 0
                            when courier_monthly_density.density_category in ('ey','ez','ev') then monthly_rates.parcel_rate
                            else daily_rates.parcel_rate
                            end as parcel_rate
                        , daily_base.total_ocd_points
                        , coalesce(bonus_rank.amount, 0) as volume_bonus
                        , coalesce((bonus_rank.rank - 1), 0) as bonus_tier
                        , bonus_targets.bonus_target_1
                        , bonus_targets.bonus_amount_1
                        , bonus_targets.bonus_target_2
                        , bonus_targets.bonus_amount_2
                        , bonus_targets.bonus_target_3
                        , bonus_targets.bonus_amount_3
                        , bonus_targets.bonus_target_4
                        , bonus_targets.bonus_amount_4
                        from daily_base
                        left join courier_monthly_density
                            on daily_base.route_month = courier_monthly_density.route_month
                            and daily_base.driver_id = courier_monthly_density.driver_id
                        left join hub_umk
                            on daily_base.driver_hub_id = hub_umk.hub_id
                            and daily_base.route_month = hub_umk.effective_month
                        left join parcel_rates as daily_rates
                            on daily_base.umk_category = daily_rates.umk_category
                            and daily_base.route_month = daily_rates.effective_month
                            and daily_base.density_category = lower(daily_rates.density_category)
                            and daily_base.route_hub_region_scheme = daily_rates.region
                        left join parcel_rates as monthly_rates
                            on hub_umk.umk_category = monthly_rates.umk_category
                            and daily_base.route_month = monthly_rates.effective_month
                            and courier_monthly_density.density_category = lower(monthly_rates.density_category)
                            and daily_base.route_hub_region_scheme = monthly_rates.region
                        left join bonus_rank
                            on daily_base.route_hub_region_scheme = bonus_rank.region
                            and daily_base.route_month = bonus_rank.effective_month
                            and daily_base.density = bonus_rank.density
                            and daily_base.total_ocd_points >= bonus_rank.range_start
                            and daily_base.total_ocd_points < bonus_rank.range_end
                            and courier_monthly_density.density_category not in ('ex','ey','ez','ev')
                        left join planned_parcels
                            on daily_base.driver_id = planned_parcels.driver_id
                            and daily_base.route_date = planned_parcels.route_date
                        left join bonus_targets
                            on daily_base.driver_id = bonus_targets.driver_id
                            and daily_base.route_date = bonus_targets.route_date

                ),
                pay as (

                    select
                        *
                        , coalesce(total_ocd_points * parcel_rate, 0) as base_parcel_pay
                        , coalesce(picked_regular_points * parcel_rate, 0) as picked_regular_pay
                        , coalesce(picked_bulky_points * parcel_rate, 0) as picked_bulky_pay
                        , coalesce(delivered_non_rts_standard_regular_points * parcel_rate, 0) as delivered_non_rts_standard_regular_pay
                        , coalesce(delivered_non_rts_standard_bulky_points * parcel_rate, 0) as delivered_non_rts_standard_bulky_pay
                        , coalesce(delivered_non_rts_fs_cs_retail_regular_points * parcel_rate, 0) as delivered_non_rts_fs_cs_retail_regular_pay
                        , coalesce(delivered_non_rts_fs_cs_retail_bulky_points * parcel_rate, 0) as delivered_non_rts_fs_cs_retail_bulky_pay
                        , coalesce(delivered_non_rts_superbulky_points * parcel_rate, 0) as delivered_non_rts_superbulky_pay
                        , coalesce(delivered_non_rts_bulky_revise_points * parcel_rate, 0) as delivered_non_rts_bulky_revise_pay
                        , coalesce(delivered_rts_regular_points * parcel_rate, 0) as delivered_rts_regular_pay
                        , coalesce(delivered_rts_bulky_points * parcel_rate, 0) as delivered_rts_bulky_pay
                    from base
                ),
                totals as (
                
                    select
                        *
                        , (delivered_rts_regular
                            + delivered_rts_bulky
                            ) as delivered_rts
                        , (delivered_rts_regular_points
                            + delivered_rts_bulky_points
                            ) as delivered_rts_points
                        , (delivered_rts_regular_pay
                            + delivered_rts_bulky_pay
                            ) as delivered_rts_pay
                        , (delivered_non_rts_standard_regular
                            + delivered_non_rts_standard_bulky
                            + delivered_non_rts_fs_cs_retail_regular
                            + delivered_non_rts_fs_cs_retail_bulky
                            + delivered_non_rts_superbulky
                            + delivered_non_rts_bulky_revise
                            + delivered_rts_regular
                            + delivered_rts_bulky
                            ) as delivered_parcels
                        , base_parcel_pay + volume_bonus as sum_total
                        , route_month as created_month
                    from pay

                ),
                final as (

                    select
                        created_month
                        , system_id
                        , cast('""" + formatted_timestamp + """' as timestamp) as updated_at
                        , cast(volume_bonus as double) as volume_bonus
                        , cast(bonus_tier as int) as bonus_tier
                        , cast(driver_id as long) as driver_id
                        , cast(system_id as string) as country
                        , cast(route_hub_region as string) as region
                        , cast(density as string) as density
                        , cast(date(route_date) as string) as day
                        , cast(sum_total as decimal(18,2)) as amount
                        , 'IDR' as currency
                        , cast(planned_parcels as int) as planned_parcel_count
                        , cast(delivered_parcels as int) as delivered_parcel_count
                        , cast(failed_parcels as int) as failed_parcel_count

                        , cast((select name from jabo_points where id = 1) as string) as ebc_category_1
                        , cast(delivered_non_rts_standard_regular as int) as ebc_count_1
                        , cast(delivered_non_rts_standard_regular_points / delivered_non_rts_standard_regular as decimal(18,2)) as ebc_points_1
                        , cast(delivered_non_rts_standard_regular_pay as decimal(18,2)) as ebc_amount_1

                        , cast((select name from jabo_points where id = 3) as string) as ebc_category_2
                        , cast(delivered_non_rts_fs_cs_retail_regular as int) as ebc_count_2
                        , cast(delivered_non_rts_fs_cs_retail_regular_points / delivered_non_rts_fs_cs_retail_regular as decimal(18,2)) as ebc_points_2
                        , cast(delivered_non_rts_fs_cs_retail_regular_pay as decimal(18,2)) as ebc_amount_2

                        , cast((select name from jabo_points where id = 2) as string) as ebc_category_3
                        , cast(delivered_non_rts_standard_bulky as int) as ebc_count_3
                        , cast(delivered_non_rts_standard_bulky_points / delivered_non_rts_standard_bulky as decimal(18,2)) as ebc_points_3
                        , cast(delivered_non_rts_standard_bulky_pay as decimal(18,2)) as ebc_amount_3

                        , cast((select name from jabo_points where id = 4) as string) as ebc_category_4
                        , cast(delivered_non_rts_fs_cs_retail_bulky as int) as ebc_count_4
                        , cast(delivered_non_rts_fs_cs_retail_bulky_points / delivered_non_rts_fs_cs_retail_bulky as decimal(18,2)) as ebc_points_4
                        , cast(delivered_non_rts_fs_cs_retail_bulky_pay as decimal(18,2)) as ebc_amount_4

                        , cast((select name from jabo_points where id = 5) as string) as ebc_category_5
                        , cast(delivered_rts as int) as ebc_count_5
                        , cast(delivered_rts_points / delivered_rts as decimal(18,2)) as ebc_points_5
                        , cast(delivered_rts_pay as decimal(18,2)) as ebc_amount_5

                        , cast((select name from jabo_points where id = 7) as string) as ebc_category_6
                        , cast(picked_regular as int) as ebc_count_6
                        , cast(picked_regular_points / picked_regular as decimal(18,2)) as ebc_points_6
                        , cast(picked_regular_pay as decimal(18,2)) as ebc_amount_6

                        , cast((select name from jabo_points where id = 8) as string) as ebc_category_7
                        , cast(picked_bulky as int) as ebc_count_7
                        , cast(picked_bulky_points / picked_bulky as decimal(18,2)) as ebc_points_7
                        , cast(picked_bulky_pay as decimal(18,2)) as ebc_amount_7

                        , cast((select name from jabo_points where id = 9) as string) as ebc_category_8
                        , cast(delivered_non_rts_superbulky as int) as ebc_count_8
                        , cast(delivered_non_rts_superbulky_points / delivered_non_rts_superbulky as decimal(18,2)) as ebc_points_8
                        , cast(delivered_non_rts_superbulky_pay as decimal(18,2)) as ebc_amount_8

                        , cast((select name from jabo_points where id = 10) as string) as ebc_category_9
                        , cast(delivered_non_rts_bulky_revise as int) as ebc_count_9
                        , cast(delivered_non_rts_bulky_revise_points / delivered_non_rts_bulky_revise as decimal(18,2)) as ebc_points_9
                        , cast(delivered_non_rts_bulky_revise_pay as decimal(18,2)) as ebc_amount_9

                        , cast(total_ocd_points as decimal(18,2)) as db_points_earned
                        , cast(bonus_target_1 as decimal(18,2)) as db_target_1
                        , cast(bonus_amount_1 as decimal(18,2)) as db_amount_1
                        , cast(bonus_target_2 as decimal(18,2)) as db_target_2
                        , cast(bonus_amount_2 as decimal(18,2)) as db_amount_2
                        , cast(bonus_target_3 as decimal(18,2)) as db_target_3
                        , cast(bonus_amount_3 as decimal(18,2)) as db_amount_3
                        , cast(bonus_target_4 as decimal(18,2)) as db_target_4
                        , cast(bonus_amount_4 as decimal(18,2)) as db_amount_4

                        , driver_display_name
                        , driver_type
                        , driver_hub_id
                        , driver_hub_region
                        , route_hub_region
                        , route_hub_id
                        , route_hub_name
                        , zone_id
                        , zone_name
                        , monthly_density_category
                        , monthly_umk_category
                        , monthly_umk_value
                        , density_category
                        , umk_category
                        , picked_regular
                        , picked_bulky
                        , delivered_non_rts_fs_regular
                        , delivered_non_rts_fs_bulky
                        , delivered_non_rts_cs_regular
                        , delivered_non_rts_cs_bulky
                        , delivered_non_rts_retail_regular
                        , delivered_non_rts_retail_bulky
                        , delivered_non_rts_standard_regular
                        , delivered_non_rts_standard_bulky
                        , delivered_non_rts_fs_cs_retail_regular
                        , delivered_non_rts_fs_cs_retail_bulky
                        , delivered_non_rts_superbulky
                        , delivered_non_rts_bulky_revise
                        , delivered_rts_regular
                        , delivered_rts_bulky
                        , total_ocd_points
                        , base_parcel_pay

                    from totals

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_DAILY_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
