import sys

from pyspark.sql import SparkSession
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsWeeklyDAG.Task.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_WEEKLY_MASKED + ".py",
    task_name=data_warehouse.OpsWeeklyDAG.Task.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_WEEKLY_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    # This task only performs partial run
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
                view_name="orders_jobs_assignments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orders_jobs_assignments_filtered",
                jinja_template="""
                SELECT
                    order_id
                    , max_by(job_type, job_assigned_datetime) as latest_job_type
                    , max_by(job_id, job_assigned_datetime) as latest_job_id
                FROM orders_jobs_assignments_enriched
                group by 1
                """,
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                select 
                    orders.id as order_id
                    -- mapping of order to size value
                      , case
                        when orders.parcel_size_id = 0 then 's'
                        when orders.parcel_size_id = 1 then 'm'
                        when orders.parcel_size_id = 2 then 'l'
                        when orders.parcel_size_id = 3 then 'xl'
                        when orders.parcel_size_id = 4 then 'xxl'
                        when orders.parcel_size_id = 5 then 'xs'
                        end as orders_size
                    , date(reservations_enriched.ready_datetime) as rsvn_ready_date
                    , reservations_enriched.address_id
                    , reservations_enriched.reservation_id
                    , drivers_enriched.driver_type
                    , reservations_enriched.system_id
                from orders
                left join orders_jobs_assignments_filtered
                    on orders.id = orders_jobs_assignments_filtered.order_id
                left join reservations_enriched
                    on orders_jobs_assignments_filtered.latest_job_id = reservations_enriched.reservation_id
                    and orders_jobs_assignments_filtered.latest_job_type = reservations_enriched.data_source
                left join drivers_enriched 
                    on reservations_enriched.route_driver_id = drivers_enriched.id
                -- Logic updated to consume the past 7 days data instead of past 10 days
                where 1=1
                    and date(reservations_enriched.ready_datetime) between
                        date('{{ measurement_datetime }}') - interval 6 days and date('{{ measurement_datetime }}')
                    and lower(drivers_enriched.driver_type) not like '%mitra%' 
                    -- Exclude facility types that are not under FM
                    and reservations_enriched.route_hub_facility_type not in ('OTHERS', 'RECOVERY')
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "measurement_datetime": measurement_datetime,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with daily_counts as (
                    select 
                        base.address_id
                        , base.rsvn_ready_date
                        , base.system_id
                        , count(distinct base.order_id) as number_of_daily_orders
                        , count_if(base.orders_size in ('l','xl','xxl')) as number_of_bulky_orders
                        , count_if(base.orders_size in ('xs', 's', 'm')) as number_of_regular_orders
                    from base
                    group by {{range(1,4)|join(',')}}
                ),

                address_id_agg as (
                    select 
                        address_id
                        , system_id
                        , count(rsvn_ready_date) as number_of_oc_days
                        , avg(number_of_daily_orders) as avg_daily_orders
                        , count_if(number_of_bulky_orders>2) as oc_day_with_over2_bulkies
                        , avg(number_of_bulky_orders) as avg_bulky_orders
                    from daily_counts
                    group by {{range(1,3)|join(',')}}
                ),

                tag_vehicle_type as (
                    select
                        address_id_agg.address_id
                        , number_of_oc_days as number_of_oc_days_past_7_days
                        , round(avg_daily_orders,0) as avg_daily_orders_past_7_days
                        , oc_day_with_over2_bulkies as oc_day_with_over2_bulkies_past_7_days
                        -- Logic to check for vehicle to be assigned.
                        , case
                        -- Logic checks if an address has more than 50 daily orders, it'll be assigned a 4W
                        -- Logic also assigns 4W for those addresses that has < 50 daily orders but at least 1 bulky
                            when avg_daily_orders >= 50 
                                or (avg_daily_orders < 50 and avg_bulky_orders > 1 
                                and number_of_oc_days/(oc_day_with_over2_bulkies) <=3) then '4W'
                            else '2W'
                        end as vehicle_type
                        , system_id
                        , date_format('{{ measurement_datetime }}','yyyy-MM') as created_month
                    from address_id_agg
                )

                select * from tag_vehicle_type
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "measurement_datetime": measurement_datetime,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_WEEKLY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()