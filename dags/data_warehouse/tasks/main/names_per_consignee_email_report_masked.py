import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON><PERSON>

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.NAMES_PER_CONSIGNEE_EMAIL_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.NAMES_PER_CONSIGNEE_EMAIL_REPORT_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.PRIORITISED_LAZADA_ORDERS_MASKED,
        data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_MASKED,
    ),
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PRIORITISED_LAZADA_ORDERS,
                view_name="prioritised_lazada_orders",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS,
                view_name="lazada_orders",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="emails_to_flag",
                jinja_template="""

                select
                    creation_date
                    , to_email
                    , count(distinct to_name) as l3d_unique_consignee_name_count
                from lazada_orders
                where
                    creation_date between creation_date - interval '3' days and creation_date
                    and to_email_domain not in ('support.lazada.com')
                group by 1,2

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    prioritised_lazada_orders.order_id
                    , prioritised_lazada_orders.creation_datetime
                    , prioritised_lazada_orders.to_email
                    , emails_to_flag.l3d_unique_consignee_name_count
                    , if(emails_to_flag.l3d_unique_consignee_name_count>5,1,0) as sus_consignee_flag
                    , prioritised_lazada_orders.created_month
                    , prioritised_lazada_orders.system_id
                from prioritised_lazada_orders
                left join emails_to_flag
                    on prioritised_lazada_orders.to_email = emails_to_flag.to_email
                    and prioritised_lazada_orders.creation_date = emails_to_flag.creation_date

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NAMES_PER_CONSIGNEE_EMAIL_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()