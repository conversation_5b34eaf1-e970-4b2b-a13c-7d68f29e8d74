"""
Data Warehouse Main DAGs config mapping.
"""
from importlib import import_module

from metadata import data_warehouse


def get_airflow_config_list(tasks_class):
    # Convert class to list
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]

    airflow_config_list = []
    for t in tasks:
        task_module = import_module(f"data_warehouse.tasks.main.{t}")
        airflow_config_list.append(task_module.airflow_config)
    return airflow_config_list


dag_configs = {
    data_warehouse.ActiveOrdersDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.ActiveOrdersDAG.Task),
    },
    data_warehouse.CostCardDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.CostCardDAG.Task)},
    data_warehouse.CrossBorderDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.CrossBorderDAG.Task),
    },
    data_warehouse.CNTicketingToolDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.CNTicketingToolDAG.Task),
        "schedule": "0 22 * * *",
        "alert_channel": "cn_ticketing_tool",
    },
    data_warehouse.C2CDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.C2CDAG.Task),
        "schedule": "0 22 * * *",
    },
    data_warehouse.DPDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.DPDAG.Task),
    },
    data_warehouse.EberDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.EberDAG.Task),
    },
    data_warehouse.EndtoEndDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.EndtoEndDAG.Task)},
    data_warehouse.FakePhysicalParcelDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.FakePhysicalParcelDAG.Task)
    },
    data_warehouse.FleetDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.FleetDAG.Task)},
    data_warehouse.FieldSalesDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.FieldSalesDAG.Task),
        "schedule": "0 2 1 * *",
    },
    data_warehouse.GamificationDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.GamificationDAG.Task),
        "schedule": "0 */2 * * *",
        "max_active_runs": 10,
    },
    data_warehouse.GSheetsDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.GSheetsDAG.Task)},
    data_warehouse.HubsDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.HubsDAG.Task)},
    data_warehouse.JiraDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.JiraDAG.Task),
                                    "schedule": "0 22 * * *",
                                    },
    data_warehouse.JotformDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.JotformDAG.Task)},
    data_warehouse.MiddleMileDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.MiddleMileDAG.Task)},
    data_warehouse.MonitoringDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.MonitoringDAG.Task)},
    data_warehouse.MonthlySnapshotDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.MonthlySnapshotDAG.Task),
        "schedule": "0 18 1 * *",
    },
    data_warehouse.NinjaMartDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.NinjaMartDAG.Task),
        "schedule": "0 0 * * *",
    },
    data_warehouse.OpsMonthlyDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.OpsMonthlyDAG.Task),
        "schedule": "0 18 1 * *",
    },
    data_warehouse.OrderAggregatesDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.OrderAggregatesDAG.Task),
    },
    data_warehouse.OrderEventsDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.OrderEventsDAG.Task)},
    data_warehouse.OrdersDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.OrdersDAG.Task)},
    data_warehouse.OrderSLADAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.OrderSLADAG.Task)},
    data_warehouse.OrderTagsDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.OrderTagsDAG.Task)},
    data_warehouse.OpsWeeklyDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.OpsWeeklyDAG.Task),
        "schedule": "0 18 * * 0",
    },
    data_warehouse.RecoveryDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.RecoveryDAG.Task)},
    data_warehouse.SalesDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.SalesDAG.Task)},
    data_warehouse.SalesforceDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.SalesforceDAG.Task)},
    data_warehouse.SalesforceExportDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.SalesforceExportDAG.Task),
    },
    data_warehouse.ShippersDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.ShippersDAG.Task)},
    data_warehouse.SLABreachDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.SLABreachDAG.Task)
    },
    data_warehouse.SLABreachPriceMatchingDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.SLABreachPriceMatchingDAG.Task),
        "schedule": "0 */2 * * *",
    },
    data_warehouse.SNSChatDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.SNSChatDAG.Task)},
    data_warehouse.SupportDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.SupportDAG.Task)},
    data_warehouse.WebhookDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.WebhookDAG.Task),
        "schedule": "0 18 1 * *",
    },
    data_warehouse.WebhookSnapshotDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.WebhookSnapshotDAG.Task),
        "schedule": "0 2 * * *",
    },
    data_warehouse.WebhookFreqSnapshotDAG.DAG_ID: {
        "task_configs": get_airflow_config_list(data_warehouse.WebhookFreqSnapshotDAG.Task),
        "schedule": "0 2/6 * * *",
    },
    data_warehouse.ZendeskDAG.DAG_ID: {"task_configs": get_airflow_config_list(data_warehouse.ZendeskDAG.Task),
                                       "schedule": "0 22 * * *",
                                       },
}