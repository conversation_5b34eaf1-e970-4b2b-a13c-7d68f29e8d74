import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_SS_CASE_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_SS_CASE_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_RECOVERY_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_RECOVERY,
                view_name="calendar_recovery"
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CONTACT_ENRICHED,
                view_name="salesforce_contact_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).CASE, view_name="case"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).CASE_HISTORY, view_name="case_history"),
            base.InputTable(path=delta_tables.Jotform(input_env, is_masked).CUSTOMER_EFFORT_SCORE, view_name="customer_effort_score"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).GROUP, view_name="group"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).RECORD_TYPE, view_name="record_type"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="ss_case_base_table",
                jinja_template="""
                select
                    case.id
                    , case.nv_created_month as created_month
                    , case.priority
                    , case.status
                    , cast(case.case_number as string) as case_number
                    , from_utc_timestamp(case.created_date, {{ get_local_timezone }}) as creation_datetime
                    , from_utc_timestamp(case.closed_date, {{ get_local_timezone }}) as closed_datetime
                    , cast(is_closed as int) as is_closed
                    , case.closed_reason_c as closed_reason
                    , if(case.origin = 'None', 'Manual', case.origin) as origin
                    , case.case_acknowledged_user_c as case_acknowledgement_user_id
                    , case.acknowledged_case_user_department_c as case_acknowledgement_user_department
                    , from_utc_timestamp(
                        case.acknowledge_case_time_c, {{ get_local_timezone }}
                    ) as case_acknowledgement_datetime
                    , case.owner_id as case_owner_id
                    , coalesce(user.name, group.name) as case_owner_name
                    , user.fsr_sales_team as case_owner_fsr_sales_team
                    , cast(escalatable_c as int) as case_escalatable
                    , case.account_id
                    , account.name as account_name
                    , account.sales_channel as account_sales_channel
                    , account.shipper_tier 
                    , cast(case.global_id_c as bigint) as shipper_id
                    , concat(contact.first_name, ' ', contact.last_name) as contact_name
                    , case
                        when issue_type_c = 'None'
                            then 'BOI'
                        else issue_type_c
                    end as fsr_issue_type
                    , case.cs_issue_type_c as cs_issue_type
                    , case.cs_issue_sub_type_c as cs_issue_subtype
                    , case.shipper_issue_type_c as shipper_issue_type
                    , case.issue_type_l1_c as issue_type_l1
                    , case.issue_type_l2_c as issue_type_l2
                    , case.issue_type_l3_c as issue_type_l3
                    , case.subject
                    , case.reservation_id_c as reservation_id
                    , case.pickup_timeslot_today_c as pickup_timeslot_today
                    , case.pickup_timeslot_tomorrow_c as pickup_timeslot_tomorrow
                    , case.pickup_volume_c as pickup_volume
                    , case.response_c as response
                    , case.ops_response_c as ops_response
                    , ops_user.id as ops_user_id
                    , ops_user.name as ops_user_name
                    , ops_user_email_c as ops_user_email
                    , cast(case.reservation_id_ops_c as int) as reservation_id_ops
                    , case.pickup_date_c as pickup_date_ops
                    , case.pickup_timeslot_c as pickup_timeslot_ops
                    , case.pickup_volume_ops_c as pickup_volume_ops
                    , case.fsr_pickup_type_c as fsr_pickup_type
                    , case.tracking_id_c as tracking_id
                    , cast(case.order_id_c as bigint) as order_id
                    , case.eta_to_requester_c as eta_to_requester
                    , case.delivery_date_c as delivery_date
                    , case.updated_eta_date_c as updated_eta_date
                    , case.assigned_dept_c as assigned_dept
                    , cast(case.destination_hub_id_c as int) as destination_hub_id
                    , cast(case.ces_survey_sent_c as int) as ces_survey_sent
                    , from_utc_timestamp(case.survey_sent_date_time_c, {{ get_local_timezone }}) as survey_sent_datetime
                    , from_utc_timestamp(case.survey_received_date_time_c, {{ get_local_timezone }})
                    as survey_received_datetime
                    , from_utc_timestamp(case.ss_agent_frt_c, {{ get_local_timezone }}) as ss_agent_frt
                    , from_utc_timestamp(case.agent_s_frt_c, {{ get_local_timezone }}) as agent_s_frt
                    , from_utc_timestamp(case.ops_response_date_time_c, {{ get_local_timezone }})
                    as ops_response_datetime
                    , from_utc_timestamp(case.first_response_date_time_c, {{ get_local_timezone }})
                    as first_response_datetime
                    , case.case_reopened_count_c as case_reopened_count
                    , from_utc_timestamp(case.solved_date_time_c, {{ get_local_timezone }}) as solved_datetime
                    , case.rts_flag_c as rts_flag
                    , case.age_days_c as age
                    , case.historically_involved_departments_c as historically_involved_departments
                    , case.historically_involved_departments_count_c as historically_involved_departments_count
                    , case.responded_department_c as responded_department
                    , from_utc_timestamp(case.ovfd_date_time_c, {{ get_local_timezone }}) as ovfd_datetime
                    , case.latest_pickup_time_c as latest_pickup_time
                    , case.associate_case_c as associate_case
                    , case.ces_score_c
                    , case.first_response_user_c as first_response_user
                    , case.remaining_ori_sla_c as remaining_ori_sla
                    , case.remaining_reso_sla_c as remaining_reso_sla
                    , case.physically_in_hub_c as physically_in_hub
                    , case.first_response_time_business_hours_c as first_response_time_business_hours
                    , case.full_resolution_time_business_hours_c as full_resolution_time_business_hours
                    , case.paj_id_c as paj_id
                    , case.chat_csat_c as chat_csat
                    , case.ss_csat_free_text_reasons_c as ss_csat_free_text_reasons
                    , case.ss_csat_response_c as ss_csat_response
                    , case.ss_csat_reasons_c as ss_csat_reasons
                    , lower(case.country_c) as system_id
                from case
                left join salesforce_user_enriched user
                    on case.owner_id = user.id
                left join salesforce_user_enriched as ops_user
                    on case.ops_user_c = ops_user.id
                left join group
                    on case.owner_id = group.id
                left join record_type as record
                    on case.record_type_id = record.id
                left join salesforce_account_enriched as account
                    on case.account_id = account.id
                left join salesforce_contact_enriched as contact
                    on case.contact_id = contact.id
                where
                    case.is_deleted = false
                    and case.country_c != 'None'
                    and record.developer_name in ('SS', 'SS_Locked')
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(country_c)")},
            ),
            base.TransformView(
                view_name="calendar_working_days",
                jinja_template="""
                -- calendar according to recovery team working days
                with
                    country_calendar as (
                        select
                            country
                            , date
                            , day
                            , comments
                            , case
                                when country = 'th' and comments is not null
                                then 0
                                when country = 'sg' and (comments is not null or dayofweek(date) = 7
                                    or dayofweek(date) = 1)
                                then 0
                                when country in ('vn', 'my', 'id', 'ph') and (comments is not null or dayofweek(date) = 1)
                                then 0
                                else 1
                            end as is_working_day
                            , sum(if(if(dayofweek(date) = 1  or dayofweek(date) = 7 or comments is not null, 0, 1) = 1,
                                1, 0) ) over (partition by country order by date asc) as grp
                        from calendar_recovery
                        where
                            region = 'national'
                            and country != 'mm'
                    ),
                    regular_calendar as (
                        select
                             country
                            , date
                            , day
                            , comments
                            , is_working_day
                            , date_add(date, days_to_next_working_day) as next_working_day
                        from
                            (
                            select
                                *
                                , row_number() over (
                                    partition by country, grp order by date desc
                                ) as days_to_next_working_day
                            from country_calendar
                            )
                        order by country, date
                    ),
                    working_days as (

                        select
                              country
                            , date
                            , day
                        from regular_calendar
                        where is_working_day = 1

                    ),
                    final as (
                        select
                              rc.country
                            , rc.date
                            , rc.day
                            , rc.comments
                            , rc.is_working_day
                            , rc.next_working_day
                            , wd.date as next_working_dayx
                            , row_number() over(partition by rc.country, rc.date order by wd.date asc)
                                as next_working_day_order
                        from regular_calendar rc
                        left join working_days wd
                            on wd.country = rc.country
                            and wd.date > rc.date
                            and wd.date <= rc.date + interval '21' day
                    )
                    select
                        country
                        , date
                        , day
                        , comments
                        , is_working_day
                        , max(if(next_working_day_order = 1, next_working_dayx, null)) as next_working_day
                        , max(if(next_working_day_order = 2, next_working_dayx, null)) as next_working_day2
                        , max(if(next_working_day_order = 3, next_working_dayx, null)) as next_working_day3
                        , max(if(next_working_day_order = 4, next_working_dayx, null)) as next_working_day4
                        , max(if(next_working_day_order = 5, next_working_dayx, null)) as next_working_day5
                        , max(if(next_working_day_order = 6, next_working_dayx, null)) as next_working_day6
                    from final
                    where next_working_day_order < 7
                    group by 1,2,3,4,5
                """,
            ),
            base.TransformView(
                view_name="case_history_status",
                jinja_template="""
                select
                    case_history.id
                    , case_history.case_id
                    , case_history.created_date
                    , case_history.field
                    , case_history.old_value
                    , case_history.new_value
                    , case
                        when field = lead(field) over (partition by case_id order by created_date, lower(field))
                        and created_date = lead(created_date) over (partition by case_id order by created_date, lower(field))
                        and old_value = lag(new_value) over (partition by case_id order by created_date, lower(field))
                        then 0
                        else 1
                    end dup_rank
                from case_history
                where field in ('created', 'Status')
                and is_deleted = False
                """,
            ),
            base.TransformView(
                view_name="case_history_owner",
                jinja_template="""
                select
                    case_history.id
                        , case_history.case_id
                        , case_history.created_date
                        , case_history.field
                        , case_history.old_value
                        , case_history.new_value
                        , case
                            when field = lead(field) over (partition by case_id order by created_date, lower(field))
                            and created_date = lead(created_date) over (partition by case_id order by created_date, lower(field))
                            and old_value = lag(new_value) over (partition by case_id order by created_date, lower(field))
                            then 0
                            else 1
                        end dup_rank
                    from case_history
                    where (field = 'created'
                    or (field = 'Owner' and substr(old_value, 1, 2) = '00'))
                    and is_deleted = False
                """,
            ),
            base.TransformView(
                view_name="case_history_status_final",
                jinja_template="""
                select
                    *
                    , case
                        when field = 'created'
                            then lead(old_value) over (partition by case_id order by created_date, lower(field))
                        when field = 'Status'
                            then new_value
                    end as value
                from case_history_status
                """,
            ),
            base.TransformView(
                view_name="case_history_owner_final",
                jinja_template="""
                select
                    *
                    , case
                        when field = 'created'
                            then lead(old_value) over (partition by case_id order by created_date, lower(field))
                        when field = 'Owner'
                            then new_value
                    end as value
                from case_history_owner
                """,
            ),
             base.TransformView(
                view_name="case_history_first_owner_transfer",
                jinja_template="""
                 select * from (
                    select 
                        id
                        , case_id
                        , case_history.created_date AS new_owner_assigned_date
                        , field
                        , old_value as initial_owner
                        , new_value as new_owner
                        , ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY created_date) as rn
                    from case_history
                    where 
                        field = 'Owner'
                        and is_deleted = False
                        AND NOT (substr(old_value, 1, 2) = '00' OR substr(new_value, 1, 2) = '00')
                        AND new_value = 'PH CX Reso Fleet - FM' -- Target only for this shipper
                ) temp
                where rn = 1
                """,
            ),
            base.TransformView(
                view_name="case_history_base",
                jinja_template="""
                select * from case_history_status_final
                union
                select * from case_history_owner_final
                """,
            ),
            base.TransformView(
                view_name="case_history_final",
                jinja_template="""
                with base as (
                    select
                        case_history_base.*
                        , group.name as queue_name
                        , case
                            when lower(group.name) like '%recovery%'
                                then 1
                            when field = 'Owner' and lower(group.name) not like '%recovery%'
                                then 0
                            else null
                        end as case_recovery_queue
                        , case
                            when value = 'On-Hold'
                                then 1
                            when field = 'Status' and value != 'On-Hold'
                                then 0
                            else null
                        end as case_on_hold
                        , case
                            when value = 'New'
                                then 1
                            when field = 'Status' and value != 'New'
                                then 0
                            else null
                        end as case_new
                        , case
                            when value = 'Open'
                                then 1
                            when field = 'Status' and value != 'Open'
                                then 0
                            else null
                        end as case_open
                        , ss_case_base_table.system_id
                        , ss_case_base_table.creation_datetime
                        , ss_case_base_table.solved_datetime
                        , ss_case_base_table.cs_issue_type
                        , ss_case_base_table.issue_type_l2
                    from ss_case_base_table
                    left join case_history_base
                    on ss_case_base_table.id = case_history_base.case_id
                    left join (select * from group where type = 'Queue') group
                    on case_history_base.value = group.id

                    -- Only includes cases where new_value contains 00G2, but some are missing from group object
                    where
                    substr(value, 1, 4) != '0052'
                )
                select
                    *
                    , last(queue_name, true)
                        over(partition by case_id order by created_date, lower(field), dup_rank)
                    as recovery_queue_name
                    , last(case_recovery_queue, true)
                        over(partition by case_id order by created_date, lower(field), dup_rank)
                    as recovery_queue_flag
                    , last(case_on_hold, true)
                        over(partition by case_id order by created_date, lower(field), dup_rank)
                    as on_hold_flag
                    , last(case_new, true)
                        over(partition by case_id order by created_date, lower(field), dup_rank)
                    as new_flag
                    , last(case_open, true)
                        over(partition by case_id order by created_date, lower(field), dup_rank)
                    as open_flag
                from base
                order by case_id, created_date, lower(field)
                """,
            ),
            base.TransformView(
                view_name="case_first_solved_datetime",
                jinja_template="""
                select
                    case_id
                    , first_solved_datetime
                from (
                    select
                        case_id
                        , first_value(created_date) over(partition by case_id order by created_date)
                        as first_solved_datetime
                    from case_history_final
                    where value = 'Solved'
                )
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="case_first_on_hold_datetime",
                jinja_template="""
                select
                    case_id
                    , first_on_hold_datetime
                from (
                    select
                        case_id
                        , first_value(created_date) over(partition by case_id order by created_date)
                        as first_on_hold_datetime
                    from case_history_final
                    where value = 'On-Hold'
                )
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="case_time_spent_base",
                jinja_template="""
                with base as (
                    select
                        base.id
                        , base.case_id
                        , lower(substr(base.recovery_queue_name, 1, 2)) as queue_country
                        , first_solved_datetime
                        , first_on_hold_datetime
                        , case
                            when recovery_queue_flag = 1 and on_hold_flag = 1
                            then lead(created_date) over (partition by base.case_id order by created_date, lower(field))
                        end as recovery_on_hold_end
                        , case
                            when recovery_queue_flag = 1 and on_hold_flag = 1
                            then created_date
                        end as recovery_on_hold_start
                        , case
                            when on_hold_flag = 1 and created_date = first_on_hold_datetime
                            then lead(created_date) over (partition by base.case_id order by created_date, lower(field))
                        end as first_on_hold_end
                        , case
                            when on_hold_flag = 1 and created_date = first_on_hold_datetime
                            then created_date
                        end as first_on_hold_start
                        , case
                            when new_flag = 1
                            then lead(created_date) over (partition by base.case_id order by created_date, lower(field))
                        end as new_end
                        , case
                            when new_flag = 1
                            then created_date
                        end as new_start
                        , case
                            when open_flag = 1
                            then lead(created_date) over (partition by base.case_id order by created_date, lower(field))
                        end as open_end
                        , case
                            when open_flag = 1
                            then created_date
                        end as open_start
                        , case
                            when on_hold_flag = 1
                            then lead(created_date) over (partition by base.case_id order by created_date, lower(field))
                        end as on_hold_end
                        , case
                            when on_hold_flag = 1
                            then created_date
                        end as on_hold_start
                        , system_id
                        , creation_datetime
                        , solved_datetime
                        , cs_issue_type
                        , issue_type_l2
                    from case_history_final base
                    left join case_first_solved_datetime fs
                    on base.case_id = fs.case_id
                    left join case_first_on_hold_datetime fon
                    on base.case_id = fon.case_id
                )
                select
                    id
                    , case_id
                    , queue_country
                    , from_utc_timestamp(first_solved_datetime, {{ get_local_timezone }}) as first_solved_datetime
                    , from_utc_timestamp(first_on_hold_datetime, {{ get_local_timezone }}) as first_on_hold_datetime
                    , from_utc_timestamp(recovery_on_hold_end, {{ get_local_timezone }}) as recovery_on_hold_end
                    , from_utc_timestamp(recovery_on_hold_start, {{ get_local_timezone }}) as recovery_on_hold_start
                    , from_utc_timestamp(first_on_hold_end, {{ get_local_timezone }}) as first_on_hold_end
                    , from_utc_timestamp(first_on_hold_start, {{ get_local_timezone }}) as first_on_hold_start
                    , from_utc_timestamp(new_end, {{ get_local_timezone }}) as new_end
                    , from_utc_timestamp(new_start, {{ get_local_timezone }}) as new_start
                    , from_utc_timestamp(open_end, {{ get_local_timezone }}) as open_end
                    , from_utc_timestamp(open_start, {{ get_local_timezone }}) as open_start
                    , from_utc_timestamp(on_hold_end, {{ get_local_timezone }}) as on_hold_end
                    , from_utc_timestamp(on_hold_start, {{ get_local_timezone }}) as on_hold_start
                    , system_id
                    , creation_datetime
                    , solved_datetime
                    , cs_issue_type
                    , issue_type_l2
                from base
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("base.system_id")},
            ),
            base.TransformView(
                view_name="recovery_on_hold_working_days",
                jinja_template="""
                select
                    base.*
                    , case
                        when wcal1.is_working_day = 0
                        then to_timestamp(cast(wcal1.next_working_day as STRING) || ' 24', 'yyyy-MM-dd k')
                        else recovery_on_hold_end
                    end as recovery_on_hold_end_working_day
                    , case
                        when wcal2.is_working_day = 0
                        then to_timestamp(cast(wcal2.next_working_day as STRING) || ' 24', 'yyyy-MM-dd k')
                        else recovery_on_hold_start
                    end as recovery_on_hold_start_working_day
                    , case
                        when wcal3.is_working_day = 0
                        then to_timestamp(cast(wcal3.next_working_day as STRING) || ' 24', 'yyyy-MM-dd k')
                        else creation_datetime
                    end as creation_datetime_working_day
                    , case
                        when wcal4.is_working_day = 0
                        then to_timestamp(cast(wcal4.next_working_day as STRING) || ' 24', 'yyyy-MM-dd k')
                        else solved_datetime
                    end as solved_datetime_working_day
                from case_time_spent_base base
                -- Follow recovery queue team working days
                left join calendar_working_days as wcal1
                    on date(base.recovery_on_hold_end) = wcal1.date
                    and base.queue_country = wcal1.country
                -- Follow recovery queue team working days
                left join calendar_working_days as wcal2
                    on date(base.recovery_on_hold_start) = wcal2.date
                    and base.queue_country = wcal2.country
                -- For ID only, follow working days of case origination
                left join calendar_working_days as wcal3
                    on date(base.creation_datetime) = wcal3.date
                    and base.system_id = wcal3.country
                -- For ID only, follow working days of case origination
                left join calendar_working_days as wcal4
                    on date(base.solved_datetime) = wcal4.date
                    and base.system_id = wcal4.country
                """,
            ),
            base.TransformView(
                view_name="case_time_spent",
                jinja_template="""
                with non_working_days_recovery_on_hold as (
                    select
                        id
                        , case_id
                        , system_id
                        , queue_country
                        , count_if(wcal.is_working_day=0)/count(distinct id) as non_working_days_recovery_on_hold
                    from recovery_on_hold_working_days base
                    left join calendar_working_days as wcal
                    -- Follow recovery queue team working days
                    on date(base.recovery_on_hold_end_working_day) > wcal.date
                    and date(base.recovery_on_hold_start_working_day) < wcal.date
                    and base.queue_country = wcal.country
                    group by 1, 2, 3, 4
                ),
                non_working_days_creation_solved as (
                    select
                        id
                        , case_id
                        , queue_country
                        , system_id
                        , count_if(wcal.is_working_day=0)/count(distinct id) as non_working_days_creation_solved
                    from recovery_on_hold_working_days base
                    left join calendar_working_days as wcal
                    -- For ID only, follow working days of case origination 
                    on date(base.solved_datetime_working_day) > wcal.date
                    and date(base.creation_datetime_working_day) < wcal.date
                    and base.system_id = wcal.country
                    group by 1, 2, 3, 4
                ),
                time_spent_rec_on_hold_ex_id as (
                    select
                        rohwd.case_id
                        , first_solved_datetime
                        , first_on_hold_datetime
                        , rohwd.system_id
                        , creation_datetime_working_day
                        , solved_datetime_working_day
                        , cs_issue_type
                        , issue_type_l2
                        , avg(non_working_days_creation_solved) as non_working_days_creation_solved
                        , sum(case
                            when rohwd.system_id in ('sg', 'vn', 'ph', 'my', 'th')
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold
                        , sum(case
                            when rohwd.queue_country = 'sg'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_sg
                        , sum(case
                            when rohwd.queue_country = 'vn'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_vn
                        , sum(case
                            when rohwd.queue_country = 'ph'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_ph
                        , sum(case
                            when rohwd.queue_country = 'my'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_my
                        , sum(case
                            when rohwd.queue_country = 'th'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_th
                        , sum(case
                            when rohwd.queue_country = 'id'
                            then
                                (unix_timestamp(recovery_on_hold_end_working_day)
                                - unix_timestamp(recovery_on_hold_start_working_day))/60
                                - non_working_days_recovery_on_hold*24*60
                        end) as time_spent_rec_on_hold_id
                        , sum(unix_timestamp(first_on_hold_end) - unix_timestamp(first_on_hold_start))/60
                        as time_spent_first_on_hold
                        , sum(unix_timestamp(new_end) - unix_timestamp(new_start))/60 as time_spent_new
                        , sum(unix_timestamp(open_end) - unix_timestamp(open_start))/60 as time_spent_open
                        , sum(unix_timestamp(on_hold_end) - unix_timestamp(on_hold_start))/60 as time_spent_on_hold
                    from recovery_on_hold_working_days rohwd
                    left join non_working_days_recovery_on_hold nwdroh
                    on rohwd.id = nwdroh.id
                    left join non_working_days_creation_solved nwdcs
                    on rohwd.id = nwdcs.id
                    group by 1, 2, 3, 4, 5, 6, 7, 8
                )
                select
                    case_id
                    , first_solved_datetime
                    , first_on_hold_datetime
                    -- For ID, only calculate for recovery issue types
                    , case
                        when system_id = 'id'
                        and (cs_issue_type in ('Complaints: Missing', 'Complaints: Damaged', 'Complaints: Recovery-Related')
                        or issue_type_l2 in ('Damaged 0301', 'Missing/Lost Parcel 0304'))
                        then
                            (unix_timestamp(solved_datetime_working_day)
                            - unix_timestamp(creation_datetime_working_day))/60
                            - non_working_days_creation_solved*24*60
                        when system_id in ('sg', 'vn', 'ph', 'my', 'th')
                        then coalesce(time_spent_rec_on_hold_sg, 0)
                            + coalesce(time_spent_rec_on_hold_vn, 0)
                            + coalesce(time_spent_rec_on_hold_ph, 0)
                            + coalesce(time_spent_rec_on_hold_my, 0)
                            + coalesce(time_spent_rec_on_hold_th, 0)
                            + coalesce(time_spent_rec_on_hold_id, 0)
                    end as time_spent_rec_on_hold
                    , time_spent_rec_on_hold_sg
                    , time_spent_rec_on_hold_vn
                    , time_spent_rec_on_hold_ph
                    , time_spent_rec_on_hold_my
                    , time_spent_rec_on_hold_th
                    , time_spent_rec_on_hold_id
                    , time_spent_first_on_hold
                    , time_spent_new
                    , time_spent_open
                    , time_spent_on_hold
                from time_spent_rec_on_hold_ex_id
                """,
            ),
            base.TransformView(
                view_name="ces_score_auto",
                jinja_template="""
                with
                    deduped as (
                        select
                            id
                            , ip
                            , cast(created_at as timestamp) as created_at
                            , get_json_object(answers, '$.42.answer') as case_id
                            , cast(get_json_object(answers, '$.64.answer') as int) as ces_score_raw
                            , cast(get_json_object(answers, '$.64.answer') as int) * 20 as ces_score
                            , cast(get_json_object(answers, '$.69.answer') as INT) as ces_score_new_raw
                            , cast(get_json_object(answers, '$.69.answer') as INT) * 20 as ces_score_new
                            , cast(get_json_object(
                                    answers, '$.57.answer.Ninja Van made it easy to get my issue resolved'
                                ) as int)
                                as ease_of_resolution_rating
                            , cast(get_json_object(
                                    answers, '$.57.answer.I am satisfied with the solution provided') as int)
                                as satisfied_w_solution_rating
                            , get_json_object(answers, '$.59.answer') as positive_feedback_checkbox
                            , get_json_object(answers, '$.60.answer') as negative_feedback_checkbox
                            , row_number()
                                over (partition by get_json_object(answers, '$.42.answer') order by id asc)
                                as row_num
                        from customer_effort_score
                        where
                            created_at >= '2021-10-04'
                    )
                select
                    id
                    , ip
                    , created_at
                    , case_id
                    , coalesce(ces_score_raw, ces_score_new_raw) as ces_score_raw
                    , coalesce(ces_score, ces_score_new) as ces_score
                    , ease_of_resolution_rating
                    , satisfied_w_solution_rating
                    , positive_feedback_checkbox
                    , negative_feedback_checkbox
                from deduped
                where
                    row_num = 1
                """,
            ),
            base.TransformView(
                view_name="pets_tickets",
                jinja_template="""
                with
                    pets_tickets_enriched as (
                        select
                            system_id
                            , tracking_id
                            , id
                            , type
                            , status
                            , assigned_dept
                            , creation_datetime
                            , in_progress_datetime
                            , on_hold_datetime
                            , cancellation_datetime
                            , resolution_datetime
                            , row_number() over (partition by tracking_id order by id desc) as ticket_order
                        from pets_tickets_enriched
                    )
                select
                    system_id
                    , tracking_id
                    , id as last_pets_ticket_id
                    , type as last_pets_ticket_type
                    , status as last_pets_ticket_status
                    , assigned_dept as last_pets_ticket_assigned_dept
                    , creation_datetime as last_pets_ticket_creation_datetime
                    , in_progress_datetime as last_pets_ticket_in_progress_datetime
                    , on_hold_datetime as last_pets_ticket_on_hold_datetime
                    , cancellation_datetime as last_pets_ticket_cancellation_datetime
                    , resolution_datetime as last_pets_ticket_resolution_datetime
                from pets_tickets_enriched
                where ticket_order = 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.id
                    , base.priority
                    , base.status
                    , base.case_number
                    , base.creation_datetime
                    , base.closed_datetime
                    , base.is_closed
                    , base.closed_reason
                    , base.origin
                    , base.case_acknowledgement_user_id
                    , base.case_acknowledgement_user_department
                    , base.case_acknowledgement_datetime
                    , base.case_owner_id
                    , base.case_owner_name
                    , base.case_owner_fsr_sales_team
                    , base.case_escalatable
                    , base.account_id
                    , base.account_name
                    , base.shipper_tier
                    , base.account_sales_channel
                    , base.shipper_id
                    , base.contact_name
                    , base.fsr_issue_type
                    , base.cs_issue_type
                    , base.cs_issue_subtype
                    , base.shipper_issue_type
                    , base.issue_type_l1
                    , base.issue_type_l2
                    , base.issue_type_l3
                    , base.subject
                    , base.reservation_id
                    , base.pickup_timeslot_today
                    , base.pickup_timeslot_tomorrow
                    , base.pickup_volume
                    , base.response
                    , base.ops_response
                    , base.ops_user_id
                    , base.ops_user_name
                    , base.ops_user_email
                    , base.reservation_id_ops
                    , base.pickup_date_ops
                    , base.pickup_timeslot_ops
                    , base.pickup_volume_ops
                    , base.fsr_pickup_type
                    , base.tracking_id
                    , base.order_id
                    , base.eta_to_requester
                    , base.delivery_date
                    , base.updated_eta_date
                    , base.assigned_dept
                    , base.destination_hub_id
                    , base.ces_survey_sent
                    , base.survey_sent_datetime
                    , base.survey_received_datetime
                    , base.ss_agent_frt
                    , base.agent_s_frt
                    , base.ops_response_datetime
                    , base.first_response_datetime
                    , base.case_reopened_count
                    , base.solved_datetime
                    , base.rts_flag
                    , base.age
                    , base.historically_involved_departments
                    , base.historically_involved_departments_count
                    , base.responded_department
                    , base.ovfd_datetime
                    , base.latest_pickup_time
                    , base.associate_case
                    , base.first_response_user
                    , base.remaining_ori_sla
                    , base.remaining_reso_sla
                    , base.physically_in_hub
                    , base.first_response_time_business_hours
                    , base.full_resolution_time_business_hours
                    , base.paj_id
                    , base.chat_csat
                    , base.ss_csat_free_text_reasons
                    , base.ss_csat_response
                    , base.ss_csat_reasons
                    , if(auto.created_at is not null, 1, 0) as ces_survey_submitted
                    , auto.ip as ces_submission_ip_address
                    , from_utc_timestamp(auto.created_at, {{ get_local_timezone }}) as ces_survey_submission_date
                    , coalesce(auto.ces_score_raw, base.ces_score_c) as ces_score
                    , auto.ease_of_resolution_rating
                    , auto.satisfied_w_solution_rating
                    , auto.positive_feedback_checkbox
                    , auto.negative_feedback_checkbox
                    , last_pets_ticket_id
                    , last_pets_ticket_type
                    , last_pets_ticket_status
                    , last_pets_ticket_assigned_dept
                    , last_pets_ticket_creation_datetime
                    , last_pets_ticket_in_progress_datetime
                    , last_pets_ticket_on_hold_datetime
                    , last_pets_ticket_cancellation_datetime
                    , last_pets_ticket_resolution_datetime
                    , ctp.first_solved_datetime
                    , ctp.first_on_hold_datetime
                    , ctp.time_spent_rec_on_hold
                    , ctp.time_spent_rec_on_hold_sg
                    , ctp.time_spent_rec_on_hold_vn
                    , ctp.time_spent_rec_on_hold_ph
                    , ctp.time_spent_rec_on_hold_my
                    , ctp.time_spent_rec_on_hold_th
                    , ctp.time_spent_rec_on_hold_id
                    , ctp.time_spent_first_on_hold
                    , ctp.time_spent_new
                    , ctp.time_spent_open
                    , ctp.time_spent_on_hold
                    , case_history_first_owner_transfer.initial_owner
                    , case_history_first_owner_transfer.new_owner
                    , from_utc_timestamp(case_history_first_owner_transfer.new_owner_assigned_date, {{ get_local_timezone }}) as new_owner_assigned_date
                    , base.created_month
                    , base.system_id
                from ss_case_base_table as base
                left join ces_score_auto as auto
                    on substr(base.id, 1, char_length(base.id) - 3) = substr(auto.case_id, 1, 15)
                left join pets_tickets
                    on base.tracking_id = pets_tickets.tracking_id
                    and base.system_id = pets_tickets.system_id
                left join case_time_spent ctp
                    on base.id = ctp.case_id
                left join case_history_first_owner_transfer 
                    on base.id = case_history_first_owner_transfer.case_id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("base.system_id")},
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_SS_CASE_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()