import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from common.spark import util
from datetime import timedelta
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_MASKED + ".py",
    task_name=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_MASKED,
    system_ids=(SystemID.GL,),
    post_execution_check= True,
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX + latest_partition,
                view_name="prefix"
            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Webhook Data for one created_date
    if measurement_datetime.hour == 2:
        created_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')
    else:
        created_date = measurement_datetime.strftime('%Y-%m-%d')

    spark.read.format("delta").load(delta_tables.Kafka(input_env, is_masked).WEBHOOK_HISTORY) \
        .filter(F.col("created_date") == created_date).createOrReplaceTempView("webhook")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="webhook_base",
                jinja_template="""

                with preprocessed_webhook_table as (
                        SELECT 
                            standard_webhook.metadata.event_id as uuid
                            , from_unixtime(request_timestamp / 1000) AS logged_at
                            , standard_webhook.tracking_id
                            , subscription.event_status as webhook_event_status
                            , subscription.event_state as webhook_event_state
                            , null as webhook_request_url
                            , standard_webhook.shipper_id as global_shipper_id
                            , null as parent_global_shipper_id
                            , standard_webhook.legacy_shipper_id
                            , standard_webhook.parent_shipper_id as parent_legacy_shipper_id
                            , standard_webhook.metadata.system_id as system_id
                            , 'WEBHOOK_PUSH_TASK_COMPLETED' as log_type
                            , to_json(named_struct(
                                'timestamp', standard_webhook.timestamp,
                                'isParcelOnRtsLeg', standard_webhook.is_parcel_on_rts_leg
                            )) as webhook_request
                            , null as webhook_subscriptions
                            , request_body as webhook_generated_detail
                            , null as response_time
                            , response_code
                            , response_header
                            , response_body
                            , created_date
                        FROM webhook
                    ),

                    base as ( 

                        select 
                            inter.* 
                            , prefix.shipper
                        from (

                                select
                                    uuid
                                    , logged_at
                                    , tracking_id
                                    , log_type
                                    , webhook_event_status
                                    , webhook_event_state
                                    , global_shipper_id
                                    , parent_global_shipper_id
                                    , response_code
                                    , if(response_code = 200,1,0) as success_flag
                                    , get_json_object(webhook_generated_detail, '$.url') as url
                                    , get_json_object(webhook_request, '$.timestamp') as logged_event_time 
                                    , if(get_json_object(webhook_request, '$.isParcelOnRtsLeg') is true, 1, 0) as rts_flag 
                                    , webhook_request
                                    , webhook_generated_detail
                                    , case 
                                        when lower(system_id) = 'global' then 
                                            lower(substring(tracking_id, 4, 2)) 
                                        else lower(system_id) 
                                    end as system_id 
                                    , created_date
                                from preprocessed_webhook_table
                                where
                                    -- Only webhook response is required
                                    log_type = 'WEBHOOK_PUSH_TASK_COMPLETED'
                                    -- Filter out dirty data
                                    and tracking_id is not null
                                    -- Filter out custom events 
                                    and webhook_event_status != 'Custom' 

                                ) inter
                        -- Filter for platform webhooks only
                        join prefix on
                            inter.tracking_id like prefix.prefixes || '%'
                            and inter.system_id = prefix.system_id

                    )

                     , base_enriched as (

                            select 
                                uuid
                                , shipper
                                , logged_at as webhook_sent_time_utc
                                , tracking_id
                                , log_type
                                , webhook_event_status
                                , webhook_event_state
                                , global_shipper_id
                                , parent_global_shipper_id
                                , response_code
                                , success_flag
                                , from_utc_timestamp(from_unixtime(unix_timestamp(logged_event_time,
                                    "yyyy-MM-dd'T'HH:mm:ssZ")), 'UTC') as event_time_utc
                                , logged_event_time
                                , rts_flag
                                , url
                                , webhook_request
                                , webhook_generated_detail
                                , coalesce(created_date, date(logged_at)) as created_date
                                , system_id
                            from base

                    )

                select
                    uuid
                    , shipper
                    , from_utc_timestamp(webhook_sent_time_utc, {{get_local_timezone}}) as webhook_sent_time
                    , tracking_id
                    , log_type
                    , webhook_event_status
                    , webhook_event_state
                    , global_shipper_id
                    , CAST(null AS BIGINT) as parent_global_shipper_id
                    , response_code
                    , success_flag
                    , from_utc_timestamp(event_time_utc, {{get_local_timezone}}) as event_time
                    , logged_event_time
                    , rts_flag
                    , url
                    , webhook_request
                    , webhook_generated_detail
                    , created_date
                    , system_id
                from base_enriched  
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                }
            ),
        ),
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "created_date"),
        update_latest_with_historical=True,
        enable_compaction=False,
        use_native_overwrite=True
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(spark,
                                  input_args.env,
                                  input_args.measurement_datetime,
                                  )
    run(spark, task_config)
    spark.stop()