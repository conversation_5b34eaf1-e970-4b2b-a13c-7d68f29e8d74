import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.WORKDAY_SORT_PRODUCTIVITY_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.WORKDAY_SORT_PRODUCTIVITY_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).HUB_JOURNEYS,
                view_name="hub_journeys",
                input_range=lookback_ranges.input,
                system_id=system_id,
                version_datetime='latest',
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with final as (

                    select
                        system_id
                        , created_month
                        , date(event_datetime) as scan_date
                        , region as hub_region
                        , facility_type as hub_facility_type
                        , coalesce_hub_id as hub_id
                        , coalesce_hub_name as hub_name
                        ,(case 
                            when parcel_size = 'xs' then '1.XS'
                            when parcel_size = 's' then '2.S'
                            when parcel_size = 'm' then '3.M'
                            when parcel_size = 'l' then '4.L'
                            when parcel_size = 'xl' then '5.XL'
                            when parcel_size = 'xxl' then '6.XXL'
                            else '0.ERROR' end) parcel_size
                        , count(*) as parcels_processed
                    from hub_journeys
                    where parcel_size is not null
                    group by {{ range(1, 9) | join(',') }}
                    order by {{ range(1, 9) | join(',') }}

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).WORKDAY_SORT_PRODUCTIVITY,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
