import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_SCAN_EVENTS,
                view_name="pickup_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    order_id
                    , failure_reason_id
                    , reservation_id
                    , route_hub_id
                    , event_datetime
                    , status
                    , case
                        when pickup_scan_events.system_id = 'id' and drivers.driver_type = 'Mitra - Fleet' then 1
                        else 0
                    end as third_party_pickup_flag
                    , drivers.id route_driver_id
                from pickup_scan_events
                left join drivers_enriched drivers on
                    pickup_scan_events.route_driver_id = drivers.id
                """,
            ),
            base.TransformView(
                view_name="pickups",
                jinja_template="""
                select
                    order_id
                    , min_by(failure_reason_id, event_datetime) as first_attempt_failure_reason_id
                    , min_by(reservation_id, event_datetime) as first_attempt_reservation_id
                    , min_by(route_hub_id, event_datetime) as first_attempt_route_hub_id
                    , min(event_datetime) as first_attempt_datetime
                    , min_by(route_hub_id, if(status = 'Success', event_datetime, null)) as success_route_hub_id
                    , min_by(route_driver_id, if(status = 'Success', event_datetime, null)) as success_route_driver_id
                    , min(if(status = 'Success', event_datetime, null)) as success_datetime
                    , min(
                        if(status = 'Success' and third_party_pickup_flag = 0, event_datetime, null)
                    ) as nv_success_datetime
                    , count(1) as total_attempts
                from base
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    orders.id as order_id
                    , pickups.first_attempt_failure_reason_id
                    , pickups.first_attempt_reservation_id
                    , pickups.first_attempt_route_hub_id
                    , pickups.first_attempt_datetime
                    , pickups.success_route_hub_id
                    , pickups.success_route_driver_id
                    , pickups.success_datetime
                    , pickups.nv_success_datetime
                    , pickups.total_attempts
                    , date_format(orders.created_at, 'yyyy-MM') AS created_month
                from orders
                inner join pickups on
                    orders.id = pickups.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_PICKUPS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
