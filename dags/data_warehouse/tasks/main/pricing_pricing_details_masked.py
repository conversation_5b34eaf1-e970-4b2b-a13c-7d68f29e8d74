import sys

from pyspark.sql import SparkSession

from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from pyspark.sql import functions as F
from pyspark.sql.functions import explode, col
from pyspark.sql.types import StructField, ArrayType, StringType, StructType, BooleanType

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.PRICING_PRICING_DETAILS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.PRICING_PRICING_DETAILS_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", ),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    fee_schema = ArrayType(
        StructType([
            StructField("result", StringType(), True),
            StructField("info", StringType(), True)
        ])
    )

    info_schema = ArrayType(
        StructType([
            StructField("pricing_profile_id", StringType(), True),
            StructField("script_id", StringType(), True)
        ])
    )

    result_schema = ArrayType(
        StructType([
            StructField("billing_size", StringType(), True),
            StructField("billing_weight", StringType(), True),
            StructField("total_tax", StringType(), True),
            StructField("total_with_tax", StringType(), True),
            StructField("cod_fee", StringType(), True),
            StructField("delivery_fee", StringType(), True),
            StructField("flat_discount", StringType(), True),
            StructField("gross_delivery_fee", StringType(), True),
            StructField("handling_fee", StringType(), True),
            StructField("insurance_fee", StringType(), True),
            StructField("rts_fee", StringType(), True)
        ])
    )

    amt_schema = ArrayType(
        StructType([
            StructField("amount", StringType(), True),
            StructField("tax_amount", StringType(), True)
        ])
    )

    flat_schema = ArrayType(
        StructType([
            StructField("amount", StringType(), True),
            StructField("value", StringType(), True)
        ])
    )

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.PricingProdGL(input_env, is_masked).PRICING_ORDERS_HISTORY,
                view_name="pricing_orders_history",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    end_month = measurement_datetime.strftime('%Y-%m')
    dte = dt.strptime(end_month, '%Y-%m').date()
    re = dte + relativedelta(months=-6)
    start_month = re.strftime('%Y-%m')

    orders = spark.read.format("delta").load(delta_tables.PricingProdGL(input_env, is_masked).PRICING_ORDERS) \
        .filter(F.col("created_month").between(start_month, end_month)) \
        .select("id", "order_id", "status", "granular_status", "system_id", "created_date", "service_type",
                "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                "cod_value", "insured_value", "legacy_weight", "legacy_size", "tracking_id", "pricing_result",
                "shipper_id", "created_month")

    pricing_info = orders.withColumn("pricing_resul", F.from_json("pricing_result", fee_schema)) \
        .select("pricing_resul", "id", "tracking_id", "order_id", "status", "granular_status", "system_id",
                "created_date", "service_type", "service_level", "order_type", "first_mile_type", "delivery_type",
                "timeslot", "completed_date", "cod_value", "insured_value", "legacy_weight", "legacy_size",
                "shipper_id", "pricing_result", "created_month")

    pricing_info2 = pricing_info.select(explode("pricing_resul").alias("Pricinginfo"), "id", "tracking_id", "order_id",
                                        "status", "granular_status", "system_id", "created_date", "service_type",
                                        "service_level", "order_type", "first_mile_type", "delivery_type", "shipper_id",
                                        "timeslot", "completed_date", "cod_value", "insured_value", "legacy_weight",
                                        "legacy_size", "pricing_result", "created_month") \
        .select("Pricinginfo.info", "Pricinginfo.result", "id", "shipper_id",
                "tracking_id", "order_id", "status", "granular_status", "system_id", "created_date", "service_type",
                "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result", "created_month")

    priceInfo = pricing_info2.withColumn("Price_info", F.from_json("info", info_schema)) \
        .select("Price_info", "result", "id", "tracking_id", "order_id", "status", "granular_status", "system_id",
                "created_date", "service_type", "shipper_id",
                "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result", "created_month")

    priceInfo2 = priceInfo.select(explode("Price_info").alias("info"), "result", "id", "tracking_id", "shipper_id",
                                  "order_id", "status", "granular_status", "system_id", "created_date",
                                  "service_type", "service_level", "order_type", "first_mile_type", "delivery_type",
                                  "timeslot", "completed_date", "cod_value", "insured_value", "legacy_weight",
                                  "legacy_size", "pricing_result", "created_month") \
        .select("info.pricing_profile_id", "info.script_id", "result", "id", "tracking_id", "order_id",
                "status", "granular_status", "system_id", "created_date", "shipper_id",
                "service_type", "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot",
                "completed_date", "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result",
                "created_month")

    priceResult = priceInfo2.withColumn("Price_result", F.from_json("result", result_schema)) \
        .select("pricing_profile_id", "Price_result", "id", "tracking_id", "order_id", "status", "granular_status",
                "system_id", "created_date", "shipper_id", "script_id",
                "service_type", "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot",
                "completed_date", "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result",
                "created_month")

    priceResult2 = priceResult.select(explode("Price_result").alias("result"), "pricing_profile_id", "id", "script_id",
                                      "tracking_id", "order_id", "status", "granular_status", "system_id", "shipper_id",
                                      "created_date", "service_type", "service_level", "order_type", "first_mile_type",
                                      "delivery_type", "timeslot", "completed_date", "cod_value", "insured_value",
                                      "legacy_weight", "legacy_size", "pricing_result", "created_month") \
        .select("result.billing_size", "result.billing_weight", "result.total_tax", "result.total_with_tax",
                "result.cod_fee", "result.delivery_fee", "result.flat_discount", "result.gross_delivery_fee",
                "result.handling_fee", "result.insurance_fee", "result.rts_fee", "pricing_profile_id", "id",
                "tracking_id", "shipper_id", "script_id",
                "order_id", "status", "granular_status",
                "system_id", "created_date", "service_type", "service_level", "order_type", "first_mile_type",
                "delivery_type", "timeslot", "completed_date", "cod_value", "insured_value", "legacy_weight",
                "legacy_size", "pricing_result", "created_month")

    cod = priceResult2.withColumn("cod", F.from_json("cod_fee", amt_schema)).select("cod", "*")

    cod2 = cod.select(explode("cod").alias("codFee"), "*") \
        .select(col("codFee.amount").alias("cod_fee_amount"), col("codFee.tax_amount").alias("cod_fee_tax_amount"),
                "billing_size", "billing_weight", "total_tax", "total_with_tax", "cod_fee", "delivery_fee",
                "flat_discount", "shipper_id", "script_id",
                "gross_delivery_fee", "handling_fee", "insurance_fee", "rts_fee", "pricing_profile_id", "id",
                "tracking_id", "order_id", "status", "granular_status",
                "system_id", "created_date", "service_type", "service_level", "order_type", "first_mile_type",
                "delivery_type", "timeslot", "completed_date", "cod_value", "insured_value", "legacy_weight",
                "legacy_size", "pricing_result", "created_month")

    delivery = cod2.withColumn("delivery", F.from_json("delivery_fee", amt_schema)).select("delivery", "*")

    delivery2 = delivery.select(explode("delivery").alias("deliveryFee"), "*") \
        .select(col("deliveryFee.amount").alias("delivery_fee_amount"), "shipper_id",
                col("deliveryFee.tax_amount").alias("delivery_fee_tax_amount"), "cod_fee_amount", "cod_fee_tax_amount",
                "billing_size", "billing_weight", "total_tax", "total_with_tax", "flat_discount", "gross_delivery_fee",
                "handling_fee", "insurance_fee", "rts_fee", "pricing_profile_id", "id", "tracking_id", "order_id",
                "status", "granular_status", "system_id", "created_date", "script_id",
                "service_type", "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot",
                "completed_date", "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result",
                "created_month")

    grossDe = delivery2.withColumn("gross_delivery", F.from_json("gross_delivery_fee", amt_schema)) \
        .select("gross_delivery", "*")

    grossDe2 = grossDe.select(explode("gross_delivery").alias("grossDelivery"), "*") \
        .select(col("grossDelivery.amount").alias("gross_delivery_fee_amount"), "shipper_id",
                col("grossDelivery.tax_amount").alias("gross_delivery_fee_tax_amount"), "delivery_fee_amount",
                "delivery_fee_tax_amount", "cod_fee_amount", "cod_fee_tax_amount", "billing_size", "billing_weight",
                "total_tax", "total_with_tax", "flat_discount", "handling_fee", "insurance_fee", "rts_fee",
                "pricing_profile_id", "id", "tracking_id", "order_id", "status", "granular_status", "system_id",
                "created_date", "service_type", "script_id",
                "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result", "created_month")

    handling = grossDe2.withColumn("handling", F.from_json("handling_fee", amt_schema)).select("handling", "*")

    handling2 = handling.select(explode("handling").alias("handlingFee"), "*") \
        .select(col("handlingFee.amount").alias("handling_fee_amount"), "shipper_id",
                col("handlingFee.tax_amount").alias("handling_fee_tax_amount"), "gross_delivery_fee_amount",
                "gross_delivery_fee_tax_amount", "delivery_fee_amount", "delivery_fee_tax_amount", "cod_fee_amount",
                "cod_fee_tax_amount", "billing_size", "billing_weight", "total_tax", "total_with_tax", "flat_discount",
                "insurance_fee", "rts_fee", "pricing_profile_id", "id", "tracking_id", "order_id", "status",
                "granular_status", "system_id", "created_date", "service_type", "script_id",
                "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result", "created_month")

    insurance = handling2.withColumn("insurance", F.from_json("insurance_fee", amt_schema)).select("insurance", "*")

    insurance2 = insurance.select(explode("insurance").alias("insuranceFee"), "*") \
        .select(col("insuranceFee.amount").alias("insurance_fee_amount"), "shipper_id",
                col("insuranceFee.tax_amount").alias("insurance_fee_tax_amount"), "handling_fee_tax_amount",
                "handling_fee_amount", "gross_delivery_fee_amount", "gross_delivery_fee_tax_amount",
                "delivery_fee_amount", "delivery_fee_tax_amount", "cod_fee_amount", "cod_fee_tax_amount",
                "billing_size", "billing_weight", "total_tax", "total_with_tax", "flat_discount", "rts_fee",
                "pricing_profile_id", "id", "tracking_id", "order_id", "status", "script_id",
                "granular_status", "system_id", "created_date", "service_type", "service_level", "order_type",
                "first_mile_type", "delivery_type", "timeslot", "completed_date", "cod_value", "insured_value",
                "legacy_weight", "legacy_size", "pricing_result", "created_month")

    rts = insurance2.withColumn("rts", F.from_json("rts_fee", amt_schema)).select("rts", "*")

    rts2 = rts.select(explode("rts").alias("rtsFee"), "*") \
        .select(col("rtsFee.amount").alias("rts_fee_amount"), col("rtsFee.tax_amount").alias("rts_fee_tax_amount"),
                "insurance_fee_amount", "insurance_fee_tax_amount", "handling_fee_tax_amount", "handling_fee_amount",
                "gross_delivery_fee_amount", "gross_delivery_fee_tax_amount", "delivery_fee_amount",
                "delivery_fee_tax_amount", "shipper_id", "script_id",
                "cod_fee_amount", "cod_fee_tax_amount", "billing_size", "billing_weight", "total_tax", "total_with_tax",
                "flat_discount", "pricing_profile_id", "id", "tracking_id",
                "order_id", "status", "granular_status", "system_id", "created_date", "service_type", "service_level",
                "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date", "cod_value",
                "insured_value", "legacy_weight", "legacy_size", "pricing_result", "created_month")

    flat = rts2.withColumn("flat", F.from_json("flat_discount", flat_schema)).select("flat", "*")

    result_df = flat.select(explode("flat").alias("flatDiscount"), "*") \
        .select(col("flatDiscount.amount").alias("flat_discount_amount"),
                col("flatDiscount.value").alias("flat_discount_value"), "id", "rts_fee_amount",
                "rts_fee_tax_amount", "insurance_fee_amount", "insurance_fee_tax_amount", "handling_fee_tax_amount",
                "handling_fee_amount", "gross_delivery_fee_amount", "gross_delivery_fee_tax_amount",
                "delivery_fee_amount", "shipper_id", "script_id",
                "delivery_fee_tax_amount", "cod_fee_amount", "cod_fee_tax_amount", "billing_size", "billing_weight",
                "total_tax", "total_with_tax", "pricing_profile_id", "tracking_id", "order_id", "status",
                "granular_status", "system_id", "created_date",
                "service_type", "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot",
                "completed_date", "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result",
                "created_month")

    result_df.select("id", "tracking_id", "billing_size", "billing_weight", "total_tax", "total_with_tax",
                     "rts_fee_amount", "rts_fee_tax_amount", "insurance_fee_amount", "insurance_fee_tax_amount",
                     "handling_fee_tax_amount", "handling_fee_amount", "gross_delivery_fee_amount",
                     "gross_delivery_fee_tax_amount", "script_id",
                     "delivery_fee_amount", "delivery_fee_tax_amount", "cod_fee_amount", "cod_fee_tax_amount",
                     "pricing_profile_id", "shipper_id",
                     "flat_discount_amount", "flat_discount_value",
                     "order_id", "status", "granular_status", "system_id", "created_date", "service_type",
                     "service_level", "order_type", "first_mile_type", "delivery_type", "timeslot", "completed_date",
                     "cod_value", "insured_value", "legacy_weight", "legacy_size", "pricing_result",
                     "created_month").createOrReplaceTempView("output")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="pricing_orders_hist",
                jinja_template="""
                    SELECT
                    order_id
                    , max_by(coalesce(get_json_object(pricing_params,'$.from_billing_zone')
                                    , get_json_object(pricing_params,'$.from_location.billing_zone')), created_at) 
                            filter (where coalesce(get_json_object(pricing_params,'$.from_billing_zone')
                                                 , get_json_object(pricing_params,'$.from_location.billing_zone')) not like '%ERROR%'
                                    and coalesce(get_json_object(pricing_params,'$.from_billing_zone')
                                               , get_json_object(pricing_params,'$.from_location.billing_zone'))!= '') as from_billing_zone
                    , max_by(coalesce(get_json_object(pricing_params,'$.to_billing_zone')
                                    , get_json_object(pricing_params,'$.to_location.billing_zone')), created_at)
                            filter (where coalesce(get_json_object(pricing_params,'$.to_billing_zone')
                                                 , get_json_object(pricing_params,'$.to_location.billing_zone'))not like '%ERROR%'
                                    and coalesce(get_json_object(pricing_params,'$.to_billing_zone')
                                               , get_json_object(pricing_params,'$.to_location.billing_zone'))!= '') as to_billing_zone
                    FROM pricing_orders_history
                    group by order_id
                    """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                        SELECT 
                        id
                        , tracking_id
                        , shipper_id
                        , billing_size
                        , billing_weight
                        , total_tax
                        , total_with_tax
                        , rts_fee_amount
                        , rts_fee_tax_amount
                        , insurance_fee_amount
                        , insurance_fee_tax_amount
                        , handling_fee_tax_amount
                        , handling_fee_amount
                        , gross_delivery_fee_amount
                        , gross_delivery_fee_tax_amount
                        , delivery_fee_amount
                        , delivery_fee_tax_amount
                        , cod_fee_amount
                        , cod_fee_tax_amount
                        , pricing_profile_id
                        , script_id
                        , flat_discount_amount
                        , flat_discount_value
                        , output.order_id
                        , status
                        , granular_status
                        , poh.from_billing_zone
                        , poh.to_billing_zone
                        , system_id
                        , created_date
                        , service_type
                        , service_level
                        , order_type
                        , first_mile_type
                        , delivery_type
                        , timeslot
                        , completed_date
                        , cod_value
                        , insured_value
                        , legacy_weight
                        , legacy_size
                        , get_json_object(pricing_result, '$.result.campaign_discounts[0].campaign_name') as campaign_name
                        , get_json_object(pricing_result, '$.result.campaign_discounts[0].campaign_id') as campaign_id
                        , cast(get_json_object(output.pricing_result, '$.result.nett_delivery_fee.amount') as double) as nett_delivery_fee_amount
                        , created_month
                        FROM output
                        Left join pricing_orders_hist poh on output.order_id = poh.order_id
                        """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PRICING_PRICING_DETAILS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(spark,
                                  input_args.env,
                                  input_args.system_id,
                                  input_args.last_measurement_datetime,
                                  input_args.measurement_datetime,
                                  input_args.enable_full_run,
                                  )

    run(spark, task_config)
    spark.stop()