import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ShippersDAG.Task.FIELD_AND_CORP_SALES_SHIPPER_EXPORT_MASKED + ".py",
    task_name=data_warehouse.ShippersDAG.Task.FIELD_AND_CORP_SALES_SHIPPER_EXPORT_MASKED,
    depends_on=(data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shippers_with_no_sf_acc",
                jinja_template="""
                    select
                        id
                        , shipper_name
                        , sales_person_code
                        , if(parent_id is null, 0, parent_id) as parent_id
                        , if(parent_id_coalesce in ({{ enabler_shipper_ids | join(", ")}}), 1 , 0) as
                         is_parent_enabler_shipper
                        , sales_channel
                        , industry
                        , onboarded_date
                        , system_id
                        , created_month
                    from shippers_enriched
                    where
                        -- check for API shipper parents
                        parent_id_coalesce in ({{ api_shipper_ids | join(", ")}})
                        -- FS and CS shippers
                        and sales_channel in ('Field Sales', 'Corp Sales')
                        -- take shippers without SF account
                        and sf_acc_id is null
            """,
                jinja_arguments = {
                    "enabler_shipper_ids" :  (3378260,
                                                1103430,
                                                5794957,
                                                6252842,
                                                4344376,
                                                6459751,
                                                7032563,
                                                7320652,
                                                5720725,
                                                5232378,
                                                5272791,
                                                5890802,
                                                5284847,
                                                5359654,
                                                5827530,
                                                6882632,
                                                7238849,
                                                7274943),
                    "api_shipper_ids" : ( 979790,
                                            1272820,
                                            576698,
                                            1556514,
                                            3855024,
                                            4842146,
                                            42280,
                                            1359336,
                                            712279,
                                            3604424,
                                            5075449,
                                            6857048,
                                            5566978,
                                            3438880,
                                            3577942,
                                            5191056,
                                            6906986,
                                            5295168,
                                            4672438,
                                            6374344,
                                            6334755,
                                            6464788,
                                            6369252,
                                            6415701,
                                            5457759,
                                            5464184,
                                            5509666,
                                            5568655,
                                            5600785,
                                            5601987,
                                            5650737,
                                            6072714,
                                            6050650,
                                            6072758,
                                            6051617,
                                            6019400,
                                            6019416,
                                            6284551,
                                            5674444,
                                            5691448,
                                            5671729,
                                            5670925,
                                            6732252,
                                            5071136,
                                            5905211,
                                            5800268,
                                            5885951,
                                            5826828,
                                            5012630,
                                            5671322,
                                            4373292,
                                            5165096,
                                            6319468,
                                            5606281,
                                            7007333,
                                            992340,
                                            6661547,
                                            7323420,
                                            3314752,
                                            6218425,
                                            5298782,
                                            5727565,
                                            7089486,
                                            3704146,
                                            68918,
                                            4239538,
                                            74072,
                                            5814429,
                                            7547802,
                                            1194276,
                                            92002,
                                            97374,
                                            514148,
                                            602619,
                                            4257584,
                                            4440052,
                                            4679744,
                                            5074898,
                                            5272861,
                                            5324915,
                                            5617941,
                                            5683754,
                                            5698112,
                                            5826786,
                                            5933737,
                                            5973520,
                                            6163185,
                                            6267458,
                                            6375725,
                                            6409591,
                                            6438402,
                                            6633568,
                                            6700229,
                                            6862550,
                                            6867809,
                                            6917027,
                                            7016450,
                                            7145831,
                                            7234033,
                                            7303792,
                                            7303831,
                                            7303902,
                                            223477,
                                            4925837,
                                            5412013,
                                            5593945,
                                            5640941,
                                            5646409,
                                            5706083,
                                            5999089,
                                            6168359,
                                            6796565,
                                            621795,
                                            6922512,
                                            6834037,
                                            7135448,
                                            7262610,
                                            7287802,
                                            7364222,
                                            7360021,
                                            7349553,
                                            7427350,
                                            7280899,
                                            7552899,
                                            7522307,
                                            7726528,
                                            7766400,
                                            8044366,
                                            8050086,
                                            11226,
                                            902120,
                                            9454,
                                            334659,
                                            315725,
                                            4595452,
                                            4496854,
                                            144377,
                                            1556512,
                                            5373851,
                                            5514191,
                                            6283955,
                                            6224501,
                                            4885452,
                                            6677835,
                                            6822486,
                                            7264719,
                                            7535430,
                                            7953857,
                                            942098,
                                            1625904,
                                            4438534,
                                            4049486,
                                            89704,
                                            5051677,
                                            4621530,
                                            7373454,
                                            1558510,
                                            7188897,
                                            7188908,
                                            7162232,
                                            7974932,
                                            3378260,
                                            1103430,
                                            5794957,
                                            6252842,
                                            4344376,
                                            6459751,
                                            7032563,
                                            7320652,
                                            5720725,
                                            5232378,
                                            5272791,
                                            5890802,
                                            5284847,
                                            5359654,
                                            5827530,
                                            6882632,
                                            7238849,
                                            7274943)
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIELD_AND_CORP_SALES_SHIPPER_EXPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
