import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, 
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, 
            task_id=data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

# in kilometers
EARTH_RADIUS = 6371


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS,
                view_name="update_address_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS,
                view_name="update_address_verification_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PROOFS_ENRICHED,
                view_name="proofs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="av_details",
                jinja_template="""
                with base as (
                
                    select
                        order_id
                        , user_id
                        , av_status
                        , av_source
                        , av_mode
                        , av_zone_id
                        , av_hub_id
                        , av_latitude
                        , av_longitude
                        , confidence_score
                        , min(order_event_id) as av_order_event_id
                        , min(event_datetime) as av_datetime
                        , min(event_creation_datetime) as event_creation_datetime
                    from update_address_verification_events
                    group by {{ range(1, 11) | join(',') }}
                
                ),
                interim as (
                    select
                        order_id
                        , user_id
                        , av_status
                        , av_source
                        , av_mode
                        , av_zone_id
                        , cast(av_hub_id as int) as av_hub_id
                        , av_latitude
                        , av_longitude
                        , confidence_score as av_confidence_score
                        , av_order_event_id
                        , av_datetime
                        , event_creation_datetime
                    from base
                
                ),
                final as (
                    select
                        interim.*
                        , zones.short_name as av_zone_name
                        , hubs_enriched.name as av_hub_name
                    from interim
                    left join hubs_enriched
                        on interim.av_hub_id = hubs_enriched.id
                    left join zones
                        on interim.av_zone_id = zones.id
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="order_details",
                jinja_template="""
                with om_filter_base as (

                    select 
                        system_id
                        , created_month
                        , order_id
                        , shipper_id
                        , to_city
                        , to_address1
                        , to_address2
                        , to_postcode
                        , rts_flag
                        , case 
                            when rts_flag = 0 then delivery_success_datetime
                            when rts_flag = 1 then (rts_trigger_datetime - interval 5 minutes) 
                            end as forward_leg_cutoff
                        , case 
                            when rts_flag = 0 then delivery_success_datetime
                            when rts_flag = 1 then (rts_trigger_datetime + interval 5 minutes) 
                            end as return_leg_cutoff
                    from order_milestones
                    where 
                        status = 'Completed'
                        and force_success_flag = 0

                ),
                leg_filter as (
                
                    select
                        om_filter_base.order_id
                        , max(case when update_address_events.event_datetime < om_filter_base.forward_leg_cutoff then 1 else 0 end) as forward_exclusion_flag
                        , max(case when update_address_events.event_datetime > om_filter_base.return_leg_cutoff then 1 else 0 end) as return_exclusion_flag
                    from om_filter_base
                    left join update_address_events
                        on om_filter_base.order_id = update_address_events.order_id
                    group by 1
                
                ),
                om_filtered as (
                
                    select
                        om_filter_base.*
                        , leg_filter.forward_exclusion_flag
                        , leg_filter.return_exclusion_flag
                    from om_filter_base
                    left join leg_filter
                        on om_filter_base.order_id = leg_filter.order_id

                ),
                final as (
                    select
                        om_filtered.*
                        , orders.to_state
                        , orders.to_district
                        , shippers_enriched.shipper_name
                        , shippers_enriched.parent_name
                    from om_filtered
                    left join orders
                        on om_filtered.order_id = orders.id
                    left join shippers_enriched
                        on om_filtered.shipper_id = shippers_enriched.id
                    where shippers_enriched.sales_channel != 'Test'
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="transaction_details",
                jinja_template="""
                with forward_transactions_filtered as (
                
                    select
                        order_id
                        , 'forward_leg' as av_case
                        , max(transaction_id) as transaction_id
                        , max_by(waypoint_id, transaction_id) as waypoint_id
                        , max_by(waypoint_latitude, transaction_id) as waypoint_latitude
                        , max_by(waypoint_longitude, transaction_id) as waypoint_longitude
                        , max_by(legacy_zone_id, transaction_id) as waypoint_zone_id
                        , max_by(waypoint_zone_short_name, transaction_id) as waypoint_zone_short_name
                        , max_by(dest_hub_id, transaction_id) as delivery_attempt_hub_id
                    from delivery_transaction_events
                    where type = 'delivery'
                group by {{ range(1, 3) | join(',') }}

                ),
                return_transactions_filtered as (
                
                    select
                        order_id
                        , 'return_leg' as av_case
                        , max(transaction_id) as transaction_id
                        , max_by(waypoint_id, transaction_id) as waypoint_id
                        , max_by(waypoint_latitude, transaction_id) as waypoint_latitude
                        , max_by(waypoint_longitude, transaction_id) as waypoint_longitude
                        , max_by(legacy_zone_id, transaction_id) as waypoint_zone_id
                        , max_by(waypoint_zone_short_name, transaction_id) as waypoint_zone_short_name
                        , max_by(dest_hub_id, transaction_id) as delivery_attempt_hub_id
                    from delivery_transaction_events
                    where type = 'rts'
                group by {{ range(1, 3) | join(',') }}

                ),
                transactions_union as (
                
                    select * from forward_transactions_filtered
                    UNION ALL
                    select * from return_transactions_filtered

                ),
                max_proof_coordinates as (

                    select
                        reference_id
                        , max_by(proofs_enriched.sign_coordinates_lat, proofs_enriched.proof_id) as delivery_attempt_latitude
                        , max_by(proofs_enriched.sign_coordinates_lng, proofs_enriched.proof_id) as delivery_attempt_longitude
                    from proofs_enriched
                    where proofs_enriched.reference = 'Transaction'
                    group by 1

                ),
                coordinates as (

                    select
                        transactions_union.*
                        , max_proof_coordinates.delivery_attempt_latitude
                        , max_proof_coordinates.delivery_attempt_longitude
                    from transactions_union
                    left join max_proof_coordinates
                        on transactions_union.transaction_id = max_proof_coordinates.reference_id

                ),
                final as (

                    select
                        coordinates.*
                        , hubs_enriched.name as delivery_attempt_hub_name
                    from coordinates
                    left join hubs_enriched
                        on coordinates.delivery_attempt_hub_id = hubs_enriched.id
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                    select
                        order_details.system_id
                        , order_details.created_month
                        , order_details.order_id
                        , order_details.shipper_id
                        , order_details.shipper_name
                        , order_details.parent_name
                        , order_details.to_state
                        , order_details.to_city
                        , order_details.to_district
                        , order_details.to_address1
                        , order_details.to_address2
                        , order_details.to_postcode
                        , order_details.rts_flag
                        , order_details.forward_exclusion_flag
                        , order_details.return_exclusion_flag
                        , case when av_details.av_datetime > order_details.forward_leg_cutoff then 'return_leg'
                            else 'forward_leg'
                            end as av_case
                        , av_details.av_datetime
                        , row_number() over (partition by order_details.order_id order by av_details.av_datetime asc) as av_sequence
                        , av_details.av_order_event_id
                        , av_details.av_status
                        , av_details.av_source
                        , av_details.av_mode
                        , av_details.av_confidence_score
                        , av_details.av_zone_id
                        , av_details.av_hub_id
                        , av_details.av_latitude
                        , av_details.av_longitude
                        , av_details.av_zone_name
                        , av_details.av_hub_name
                    from order_details
                    left join av_details
                        on order_details.order_id = av_details.order_id
                    where av_details.order_id is not null

                ),
                leg_filtered as (

                    select
                        *
                        , row_number() over (partition by order_id, av_case order by av_datetime asc) as av_leg_sequence
                    from base
                    where 
                        (forward_exclusion_flag = 0 and av_case = 'forward_leg')
                        or (return_exclusion_flag = 0 and av_case = 'return_leg')

                ),
                transactions_joined as (
                
                    select
                        leg_filtered.*
                        , transaction_details.transaction_id
                        , transaction_details.waypoint_latitude
                        , transaction_details.waypoint_longitude
                        , transaction_details.waypoint_zone_id
                        , transaction_details.waypoint_zone_short_name
                        , transaction_details.delivery_attempt_latitude
                        , transaction_details.delivery_attempt_longitude
                        , transaction_details.delivery_attempt_hub_id
                        , transaction_details.delivery_attempt_hub_name
                    from leg_filtered
                    left join transaction_details
                        on leg_filtered.order_id = transaction_details.order_id
                        and leg_filtered.av_case = transaction_details.av_case

                ),
                rad as (

                    select
                        *
                        , radians(delivery_attempt_latitude) as delivery_attempt_lat_rad
                        , radians(delivery_attempt_longitude) as delivery_attempt_long_rad
                        , radians(av_latitude) as av_lat_rad
                        , radians(av_longitude) as av_long_rad
                        , radians(waypoint_latitude) as waypoint_lat_rad
                        , radians(waypoint_longitude) as waypoint_long_rad
                    from transactions_joined

                ),
                final as (

                    select 
                        order_id
                        , created_month
                        , shipper_id
                        , shipper_name
                        , parent_name
                        , to_state
                        , to_city
                        , to_district
                        , to_address1
                        , to_address2
                        , to_postcode
                        , rts_flag
                        , delivery_attempt_hub_id
                        , delivery_attempt_hub_name
                        , transaction_id
                        , waypoint_latitude
                        , waypoint_longitude
                        , cast(waypoint_zone_id as bigint) as waypoint_zone_id
                        , waypoint_zone_short_name
                        , concat(delivery_attempt_longitude, ',', delivery_attempt_latitude) as delivery_attempt_longlat
                        , delivery_attempt_longitude
                        , delivery_attempt_latitude
                        , av_case
                        , av_datetime
                        , av_order_event_id
                        , av_leg_sequence
                        , av_sequence
                        , av_status
                        , av_source
                        , av_mode
                        , av_confidence_score
                        , av_zone_id
                        , av_hub_id
                        , av_latitude
                        , av_longitude
                        , av_zone_name
                        , av_hub_name
                        , if(delivery_attempt_hub_id = av_hub_id,1,0) as hub_alignment_flag
                        , if(waypoint_zone_id = av_zone_id,1,0) as zone_alignment_flag
                        , {{ earth_radius }} * acos(
                            cos(delivery_attempt_lat_rad) * cos(av_lat_rad)
                            * cos(av_long_rad - delivery_attempt_long_rad)
                            + sin(delivery_attempt_lat_rad) * sin(av_lat_rad)
                        ) as av_delivery_attempt_distance
                        , {{ earth_radius }} * acos(
                            cos(delivery_attempt_lat_rad) * cos(waypoint_lat_rad)
                            * cos(waypoint_long_rad - delivery_attempt_long_rad)
                            + sin(delivery_attempt_lat_rad) * sin(waypoint_lat_rad)
                        ) as waypoint_delivery_attempt_distance
                        , system_id
                    from rad

                    )

                select * from final

                """,
                jinja_arguments={
                    "earth_radius": EARTH_RADIUS,
                    "system_id": system_id,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()