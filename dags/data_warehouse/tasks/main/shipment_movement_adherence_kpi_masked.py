import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENT_MOVEMENT_ADHERENCE_KPI_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENT_MOVEMENT_ADHERENCE_KPI_MASKED,
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,

    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SHIPMENT_SCANS,
                view_name="trip_shipment_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_MAWB,
                view_name="flight_mawb",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipment_reverted_events",
                jinja_template="""
                /* Logic to get earliest shipment reversion event*/
                select 
                    distinct shipment_id
                    , min(hub_id) as shipment_reverted_hub_id
                from shipment_events
                where event = 'SHIPMENT_STATUS_REVERTED'
                group by 1
                """,
            ),

            base.TransformView(
                view_name="ah_trip_tagging",
                jinja_template="""
                /* CTE to create movement records for Airport with trip level start or end as a proxy for scan datetime*/
                select
                    shipments_enriched.shipment_id
                    , movement_trips_enriched.trip_id
                    , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.expected_start_datetime
                        , movement_trips_enriched.actual_arrival_datetime, movement_trips_enriched.expected_arrival_datetime) as created_at
                    , 'Airport Trips' as scan_type
                    , null as type
                    , shipments_enriched.created_month
                from shipments_enriched
                left join shipment_ext_awbs
                    on shipment_ext_awbs.ref_id = shipments_enriched.mawb
                left join flight_mawb
                    on flight_mawb.mawb_id = shipment_ext_awbs.id
                left join flight_info
                    on flight_info.id = flight_mawb.flight_info_id
                left join movement_trips_enriched
                    on flight_info.trip_id = movement_trips_enriched.trip_id
                where flight_mawb.deleted_at is null
                """,
            ),

            base.TransformView(
                view_name="shipment_events_base",
                jinja_template="""
                with base as (
                    select
                        shipment_id
                        , cast(get_json_object(get_json_object(ext_data, '$.trip_data'),'$.trip_id') as bigint) as trip_id
                        , created_at
                        , event scan_type
                        , get_json_object(ext_data, '$.source') source
                        , created_month
                    from shipment_events
                    where event like '%SHIPMENT%INBOUND%'
                    and shipment_events.deleted_at is null

                )

                   select
                       shipment_id
                       , trip_id
                       , created_at
                       , scan_type
                       , if(source like '%MMDA%', 'PHYSICAL SCAN - MMDA', 'PHYSICAL SCAN - OPV2') type
                       , created_month
                    from base
                """,
            ),

            base.TransformView(
                view_name="shipment_adherence_base",
                jinja_template="""
                  with unscanned_shipments as (
                    select
                        shipment_id
                        , trip_id
                        , created_at
                        , scan_type
                        , type
                        , created_month
                    from trip_unscanned_shipments
                    where deleted_at is null
                    ),

                    tss_base as (
                        select shipment_events_base.shipment_id
                            , shipment_events_base.trip_id
                            , shipment_events_base.created_at
                            , shipment_events_base.scan_type
                            , shipment_events_base.type
                            , shipment_events_base.created_month
                        from shipment_events_base
                        union all
                        select *
                        from unscanned_shipments
                        union all
                        select *
                        from ah_trip_tagging
                    ),

                    stayover_cte as (
                    /* Identify stayover shipments and get the trip id to connect missing scans */
                        select
                            shipment_id
                            , trip_unscanned_shipments.trip_id
                            , movement_trips_enriched.origin_hub_id
                            , movement_trips_enriched.origin_hub_name
                            , movement_trips_enriched.dest_hub_id
                            , movement_trips_enriched.dest_hub_name
                        from trip_unscanned_shipments
                        left join movement_trips_enriched
                            on trip_unscanned_shipments.trip_id = movement_trips_enriched.trip_id
                        where scan_type = 'SHIPMENT_VAN_INBOUND' and type  = 'STAYOVER' and deleted_at is null
                    ),

                    scans_base as ( 
                        select
                            trip_shipment_scans.shipment_id
                            , shipments_enriched.orig_hub_name as shipment_origin_hub_name
                            , shipments_enriched.orig_hub_region as shipment_origin_hub_region
                            , origin_hub.facility_type as origin_facility_type
                            , shipments_enriched.dest_hub_name as shipment_dest_hub_name
                            , shipments_enriched.dest_hub_region as shipment_dest_hub_region
                            , dest_hub.facility_type as dest_hub_facility_type
                            , trip_shipment_scans.trip_id
                            , shipment_type
                            , trip_shipment_scans.scan_type
                            , shipments_enriched.status
                            , case when trip_shipment_scans.type = 'MISSING' THEN 'MISSING SCAN WITH TRIP' ELSE trip_shipment_scans.type end as type
                            , movement_trips_enriched.primary_driver_id
                            , drivers_enriched.display_name primary_driver_display_name
                            , movement_trips_enriched.origin_hub_name as trip_origin_hub_name
                            , movement_trips_enriched.origin_hub_region as trip_origin_hub_region
                            , movement_trips_enriched.dest_hub_name as trip_dest_hub_name
                            , movement_trips_enriched.dest_hub_region as trip_dest_hub_region
                            , movement_trips_enriched.movement_classification
                            , movement_trips_enriched.status as trip_status
                            , case when scan_type != 'Airport Trips' THEN from_utc_timestamp(trip_shipment_scans.created_at, '{{ local_timezone }}') 
                                   when scan_type = 'Airport Trips' THEN created_at END as scan_datetime
                            , if(trip_shipment_scans.scan_type in ("SHIPMENT_HUB_INBOUND",'Airport Trips'), movement_trips_enriched.dest_hub_id, movement_trips_enriched.origin_hub_id) scan_hub_id
                            , if(trip_shipment_scans.scan_type in ("SHIPMENT_HUB_INBOUND",'Airport Trips'), movement_trips_enriched.dest_hub_name, movement_trips_enriched.origin_hub_name) scan_hub_name
                            , trip_shipment_scans.created_month
                            , shipments_enriched.shipment_creation_datetime
                            , if(movement_trips_enriched.dest_hub_id = shipments_enriched.dest_hub_id,1,0) shipment_reach_destination_flag
                            , if(stayover_cte.trip_id is null, 0,1) stayover_flag
                            , matching_id_path_flag follow_assign_path_flag
                            , if(shipment_reverted_events.shipment_id is null, 0, 1) shipment_reverted_flag
                            , shipment_reverted_events.shipment_reverted_hub_id
                            , shipments_enriched.system_id
                        from tss_base trip_shipment_scans
                        left join movement_trips_enriched
                            on trip_shipment_scans.trip_id = movement_trips_enriched.trip_id
                        left join shipments_enriched
                            on trip_shipment_scans.shipment_id = shipments_enriched.shipment_id
                        /* Join to identify those missing scans which were a result of stayover */
                        left join stayover_cte
                            on stayover_cte.shipment_id = trip_shipment_scans.shipment_id
                            and stayover_cte.origin_hub_id = movement_trips_enriched.dest_hub_id
                            and trip_shipment_scans.type = 'MISSING'
                            and trip_shipment_scans.scan_type = 'SHIPMENT_HUB_INBOUND'
                        left join drivers_enriched
                            on movement_trips_enriched.primary_driver_id = drivers_enriched.id
                      left join shipment_reverted_events
                            on trip_shipment_scans.shipment_id = shipment_reverted_events.shipment_id
                        left join hubs_enriched origin_hub
                            on shipments_enriched.orig_hub_id = origin_hub.id
                        left join hubs_enriched dest_hub
                            on shipments_enriched.dest_hub_id = dest_hub.id
                        where 1=1
                            and shipments_enriched.shipment_type in ('LAND_HAUL','AIR_HAUL')
                            and shipments_enriched.status != 'Cancelled'
                            and (
                                lower(origin_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                                and
                                lower(dest_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                                )
                            and movement_trips_enriched.status != 'CANCELLED'
                        )

                       select * from scans_base
                       where stayover_flag != 1
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                /* Get the first instance where the shipment reaches the dest hub */
                with dest_datetime as (
                    select
                        shipment_id
                        , min(scan_datetime) filter(where scan_type = 'SHIPMENT_HUB_INBOUND' and type != 'MISSING') earliest_hub_dest_datetime
                        , min(scan_datetime) filter(where scan_type = 'SHIPMENT_HUB_INBOUND' and type = 'MISSING') earliest_hub_missing_dest_datetime
                        , min(scan_datetime) filter(where scan_type = 'SHIPMENT_VAN_INBOUND') earliest_van_dest_datetime
                    from shipment_adherence_base
                    where shipment_reach_destination_flag = 1 
                    group by 1
                ),

                dest_hub_datetime_coalesce as (
                    select
                        shipment_id
                        , coalesce(earliest_hub_dest_datetime, earliest_van_dest_datetime) earliest_dest_datetime
                        , earliest_hub_missing_dest_datetime
                    from dest_datetime
                ),

                lag_cte as (
                    select
                        scans_base.*
                        , hubs_enriched.region scan_hub_region
                        , lag(scans_base.scan_hub_name) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) previous_hub_name
                        , lag(scans_base.trip_id) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) previous_trip_id
                        , lag(scans_base.primary_driver_id) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) previous_driver_id
                        , lead(scans_base.primary_driver_id) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) next_driver_id
                        , lag(scans_base.scan_type) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) previous_scan_type
                        , lag(scans_base.type) over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) previous_type
                        , row_number() over(partition by scans_base.shipment_id order by scans_base.scan_datetime asc) sequence
                        , earliest_dest_datetime
                        , case when earliest_hub_missing_dest_datetime is null and earliest_dest_datetime < scan_datetime THEN 1
                                when earliest_hub_missing_dest_datetime is not null and earliest_hub_missing_dest_datetime <= scan_datetime THEN 1
                                ELSE 0 END AS scan_after_completed_flag
                    from shipment_adherence_base as scans_base
                    left join hubs_enriched
                        on scans_base.scan_hub_id = hubs_enriched.id
                    left join dest_hub_datetime_coalesce
                        on scans_base.shipment_id = dest_hub_datetime_coalesce.shipment_id
                    where hubs_enriched.facility_type NOT IN ('OTHERS')
                ),

                driver_cut as (
                    select
                        *
                        , if(next_driver_id != primary_driver_id,1,0) switch_driver_flag
                    from lag_cte
                ),

                type_revamp_cte as (
                    select 
                        *
                    /* Logic update to revamp the "Type column". This is because shipments which do not follow the assign path were found to have Missing hub inbound events when it should not be the case */
                        , case when 
                            follow_assign_path_flag = 0 
                            and previous_driver_id = primary_driver_id 
                            and scan_type = 'SHIPMENT_HUB_INBOUND'
                            and switch_driver_flag = 0
                            and type = 'MISSING SCAN WITH TRIP' 
                            and shipment_reach_destination_flag = 0   -- Extra logic: remove scenarios where missing scans at the dest hub gets converted to stayover
                            THEN 'STAYOVER'
                                ELSE type END as type_revamp
                    from driver_cut 
                ),

                previous_type_revamp_cte as (
                    select 
                        *
                        , lag(type_revamp_cte.type_revamp) over(partition by type_revamp_cte.shipment_id order by type_revamp_cte.scan_datetime asc) previous_type_revamp
                    from type_revamp_cte
                ),

                next_physical_scan_required_cte as (
                select
                    *
                    , case when scan_type = 'Airport Trips' THEN 'SHIPMENT_VAN_INBOUND'
                        when shipment_reach_destination_flag = 1 and scan_type like '%HUB%' THEN 'NO MORE SCANS REQUIRED - SHIPMENT COMPLETED'
                        when type_revamp = 'STAYOVER' THEN 'SHIPMENT_HUB_INBOUND'
                        when scan_type like '%VAN%' and scan_after_completed_flag = 0 THEN 'SHIPMENT_HUB_INBOUND'
                           when scan_type like '%HUB%' and scan_after_completed_flag = 0 THEN 'SHIPMENT_VAN_INBOUND'
                        ELSE NULL END AS next_physical_scan_required
                    /* Creating a fake scan_datetime col that return nulls for Airport Trips so that the lag() function will put them at the end */
                    , if(scan_type = 'Airport Trips', null, scan_datetime) as scan_datetime_backend
                from previous_type_revamp_cte
                ),

                get_previous_next_physical_scan_required as (
                    select
                        *
                        , lag(next_physical_scan_required) over(partition by shipment_id order by scan_datetime_backend asc) as previous_next_physical_scan_required
                        , lag(scan_hub_name) over(partition by shipment_id order by scan_datetime_backend asc) as previous_scanned_hub_name
                        , row_number() over(partition by shipment_id, trip_id, scan_type, type_revamp, scan_type, primary_driver_id order by scan_datetime) rnk
                    from next_physical_scan_required_cte
                ),

                origin_sequence_base as (
                    select *
                    from get_previous_next_physical_scan_required
                    where rnk = 1
                ),

                flight_trips_cte as (
                    select 
                        *
                    from origin_sequence_base
                    where scan_type = 'Airport Trips'
                ),

                missing_flight_cte as (
                    select
                        shipment_id
                        , 1 as missing_flight_info_flag
                    from origin_sequence_base
                    where shipment_id not in (
                        select distinct shipment_id
                        from flight_trips_cte
                    )
                    group by 1,2
                ),

                flights_later_than_dest_scan as (
                select
                    shipment_id
                    , 1 as flight_trip_out_of_sequence_flag
                from flight_trips_cte
                where previous_hub_name != trip_origin_hub_name
                group by 1,2
                ),

                add_flags_cte as (
                    select 
                        origin_sequence_base.* 
                        , coalesce(missing_flight_cte.missing_flight_info_flag, 0) as missing_flight_info_flag
                        , coalesce(flights_later_than_dest_scan.flight_trip_out_of_sequence_flag, 0) as flight_trip_out_of_sequence_flag
                    from origin_sequence_base
                    left join missing_flight_cte
                        on missing_flight_cte.shipment_id = origin_sequence_base.shipment_id
                    left join flights_later_than_dest_scan
                        on flights_later_than_dest_scan.shipment_id = origin_sequence_base.shipment_id
                )

                select 
                        shipment_id
                        , shipment_origin_hub_name
                        , shipment_origin_hub_region
                        , origin_facility_type
                        , shipment_dest_hub_name
                        , shipment_dest_hub_region
                        , dest_hub_facility_type
                        , trip_id
                        , shipment_type
                        , scan_type
                        , status
                        , type
                        , primary_driver_id
                        , primary_driver_display_name
                        , trip_origin_hub_name
                        , trip_origin_hub_region
                        , trip_dest_hub_name
                        , trip_dest_hub_region
                        , trip_status
                        , movement_classification
                        , scan_datetime
                        , scan_hub_id
                        , scan_hub_region
                        , scan_hub_name
                        , created_month
                        , shipment_creation_datetime
                        , shipment_reach_destination_flag
                        , stayover_flag
                        , follow_assign_path_flag
                        , shipment_reverted_flag
                        , shipment_reverted_hub_id
                        , system_id
                        , previous_hub_name
                        , previous_scanned_hub_name
                        , previous_trip_id
                        , previous_driver_id
                        , next_driver_id
                        , previous_scan_type
                        , previous_type
                        , sequence
                        , earliest_dest_datetime
                        , scan_after_completed_flag
                        , switch_driver_flag
                        , type_revamp
                        , previous_type_revamp
                        , next_physical_scan_required
                        , scan_datetime_backend
                        , if(sequence = 1 or scan_type = 'Airport Trips', null, previous_next_physical_scan_required) as previous_next_physical_scan_required
                        , missing_flight_info_flag
                        , if(missing_flight_info_flag = 1, 1, flight_trip_out_of_sequence_flag) as flight_trip_out_of_sequence_flag
                from add_flags_cte
                """,
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_MOVEMENT_ADHERENCE_KPI,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()