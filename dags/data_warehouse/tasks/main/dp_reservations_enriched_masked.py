import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.DPProdGL(input_env, is_masked).DP_RESERVATIONS,
                view_name="dp_reservations",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , barcode
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) as created_at
                    , deadline
                    , deadline_action
                    , dp_id
                    , from_utc_timestamp(dropped_off_at, {{ get_local_timezone }}) as dropped_off_at
                    , drop_off_on
                    , email_notification_status
                    , from_utc_timestamp(freed_at, {{ get_local_timezone }}) as freed_at
                    , global_shipper_id as shipper_id
                    , order_id
                    , pickup_date
                    , from_utc_timestamp(received_at, {{ get_local_timezone }}) as received_at
                    , received_from
                    , from_utc_timestamp(released_at, {{ get_local_timezone }}) as released_at
                    , released_to
                    , from_utc_timestamp(reserved_at, {{ get_local_timezone }}) as reserved_at
                    , service_type
                    , sms_notification_status
                    , source
                    , status
                    , from_utc_timestamp(updated_at, {{ get_local_timezone }}) as updated_at
                    , from_utc_timestamp(waiting_since, {{ get_local_timezone }}) as waiting_since
                    , lower(system_id) as system_id
                    , date_format(created_at, 'yyyy-MM') as created_month
                from dp_reservations
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DP_RESERVATIONS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
