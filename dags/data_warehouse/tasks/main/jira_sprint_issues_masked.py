import sys

from pyspark.sql import SparkSession

from common.date import Timezone
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.JiraDAG.Task.JIRA_SPRINT_ISSUES_MASKED + ".py",
    task_name=data_warehouse.JiraDAG.Task.JIRA_SPRINT_ISSUES_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(
        data_warehouse.JiraDAG.Task.JIRA_ISSUE_EVENTS_MASKED,
        data_warehouse.JiraDAG.Task.JIRA_ISSUES_ENRICHED_MASKED,
        data_warehouse.JiraDAG.Task.JIRA_SPRINTS_MASKED,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            # read full table
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_ISSUE_EVENTS,
                view_name="issue_events",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_ISSUES_ENRICHED,
                view_name="issues_enriched",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=(
                    f"{versioned_parquet_tables_masked.DataWarehouse(env).JIRA_SPRINTS}"
                    f"/measurement_datetime={measurement_datetime:%Y-%m-%d %H-%M-%S}"
                ),
                view_name="sprints",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="missing_sprint_events",
                jinja_template="""
                        -- a sprint event is not created if the issue is assigned a sprint when the issue is created.
                        -- this step creates these missing sprint events for downstream steps.

                        with
                            issue_first_sprint_event as (

                                select
                                    issue_id
                                    , coalesce(min_by(item_from, created_datetime), '') as sprint_id
                                from issue_events
                                where
                                    item_field = 'Sprint'
                                group by 1

                            ), final as (

                                -- create missing sprint events
                                -- issues with missing sprint events either have a sprint assigned but no sprint events,
                                -- or a non-empty item_from value in the first sprint event
                                select
                                    issues_enriched.id as issue_id
                                    , issues_enriched.key as issue_key
                                    , if(issue_first_sprint_event.sprint_id != '',
                                        issue_first_sprint_event.sprint_id,
                                        issues_enriched.sprint_id
                                    ) as item_to
                                    , created_datetime
                                from issues_enriched
                                left join issue_first_sprint_event on
                                    issues_enriched.id = issue_first_sprint_event.issue_id
                                where
                                    issue_first_sprint_event.sprint_id != ''
                                    or (issue_first_sprint_event.sprint_id is null
                                    and issues_enriched.sprint_id is not null)

                            )

                        select
                            *
                        from final
                        """,
            ),
            base.TransformView(
                view_name="issue_sprint_events",
                jinja_template="""
                    with
                        issue_sprint_events_combined as (

                            (select
                                issue_id
                                , issue_key
                                , item_to
                                , created_datetime
                            from issue_events
                            where
                                item_field = 'Sprint')
                            union
                            (select
                                issue_id
                                , issue_key
                                , item_to
                                , created_datetime
                            from missing_sprint_events)

                        ),
                        issue_sprint_events_base as (

                            select
                                issue_id
                                , issue_key
                                , item_to
                                , created_datetime
                                , lead(created_datetime, 1,
                                    from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}')
                                    ) over (partition by issue_id order by created_datetime)
                                as next_created_datetime
                            from issue_sprint_events_combined

                        ), issue_sprint_events_parsed as (
                            select
                                issue_id
                                , issue_key
                                , explode(split(item_to, ',')) as sprint_id
                                , created_datetime
                                , next_created_datetime
                            from
                                issue_sprint_events_base

                        ), final as (

                            select
                                issue_id
                                , issue_key
                                , trim(sprint_id) as sprint_id
                                , created_datetime
                                , next_created_datetime
                            from
                                issue_sprint_events_parsed

                        )

                    select
                        *
                    from final
                    """,
                jinja_arguments={
                    "local_timezone": Timezone.SG,
                    "measurement_datetime": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="sprint_issues",
                jinja_template="""
                    with sprint_issues_base as (

                        -- join sprints with issue events and check if
                        -- sprint start and end falls in event start/end range
                        select
                            *
                            , events.created_datetime <= sprints.start_datetime
                            and events.next_created_datetime >= sprints.start_datetime as start_flag
                            , events.created_datetime <= sprints.end_datetime
                            and events.next_created_datetime >= sprints.end_datetime as end_flag
                        from sprints
                        inner join issue_sprint_events events on
                            sprints.id = events.sprint_id

                    ), final as (

                        -- filter issues that are in a sprint either at the start or end (or both)
                        select
                            cast(id as bigint) as sprint_id
                            , name as sprint_name
                            , cast(issue_id as bigint) as issue_id
                            , issue_key
                            , date_format(start_datetime, 'yyyy-MM') as created_month
                            , cast(any(start_flag) as bigint) as start_flag
                            , cast(any(end_flag) as bigint) as end_flag
                        from sprint_issues_base
                        group by 1, 2, 3, 4, 5
                        having
                            start_flag = true
                            or end_flag = true
                    )

                select
                    *
                from final
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_SPRINT_ISSUES,
        measurement_datetime=measurement_datetime,
        system_id=constants.SystemID.SG,
        partition_by=("created_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
