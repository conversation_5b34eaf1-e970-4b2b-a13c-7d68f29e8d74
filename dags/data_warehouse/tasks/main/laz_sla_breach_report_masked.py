import sys

from pyspark.sql import SparkSession

from common.date import to_measurement_datetime_str
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.LAZ_SLA_BREACH_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.LAZ_SLA_BREACH_REPORT_MASKED,
    depends_on=(
        data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_DELIVERIES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID, task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED
        ),
    ),
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.PricingProdGL(input_env, is_masked).PRICING_ORDERS_HISTORY,
                view_name="pricing_orders_history",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DELIVERIES,
                view_name="order_deliveries",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_SLA_DAYS,
                view_name="shipper_sla_days"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                input_range=lookback_ranges.input,
            ),

        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="order_milestones_base",
                jinja_template="""

                select
                    order_id
                    , coalesce(rts_trigger_datetime, last_valid_delivery_attempt_datetime) as cutoff_datetime
                    , '{{ system_id }}' as system_id
                from order_milestones
                where
                    system_id = '{{ system_id }}'

                    -- Identify rts orders & orders without to l3 data
                    and (
                            rts_flag = 1
                            or to_l3_id is null
                        )

                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="pricing_orders_history_base",
                jinja_template="""

                select
                    order_id
                    , from_utc_timestamp(trigger_time, '{{ local_timezone }}') as trigger_datetime
                    , get_json_object(pricing_params, '$.to_l3') to_l3_id
                    , '{{ system_id }}' as system_id
                from pricing_orders_history
                where
                    lower(system_id) = '{{ system_id }}'
                    and get_json_object(pricing_params, '$.to_l3') is not null
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="revised_destination",
                jinja_template="""
                select
                    order_milestones_base.order_id
                    , order_milestones_base.cutoff_datetime
                    , max(pricing_orders_history_base.trigger_datetime) as trigger_datetime
                    , max_by(
                        pricing_orders_history_base.to_l3_id
                        , pricing_orders_history_base.trigger_datetime
                    ) as to_l3_id
                    , order_milestones_base.system_id
                from order_milestones_base
                left join pricing_orders_history_base
                    on order_milestones_base.system_id = pricing_orders_history_base.system_id 
                    and order_milestones_base.order_id = pricing_orders_history_base.order_id
                    and pricing_orders_history_base.trigger_datetime < order_milestones_base.cutoff_datetime
                group by 1,2,5
                """,
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    transit_time_report.order_id
                    , transit_time_report.reporting_name
                    , transit_time_report.creation_datetime
                    , transit_time_report.granular_status
                    , transit_time_report.from_l1_id as origin_id
                    , case
                        when transit_time_report.rts_flag = 1 and transit_time_report.delivery_success_datetime is null
                        then revised_destination.to_l3_id
                        else transit_time_report.to_l3_id
                    end as destination_id
                    , transit_time_report.start_clock_date
                    , date(
                        least(
                            transit_time_report.delivery_success_datetime
                            , order_deliveries.second_valid_rts_attempt_datetime
                        )
                    ) as end_clock_date
                    , transit_time_report.system_id
                    , date_format(transit_time_report.creation_datetime, 'yyyy-MM') as created_month
                from transit_time_report
                left join revised_destination
                    on transit_time_report.system_id = revised_destination.system_id
                    and transit_time_report.order_id = revised_destination.order_id    
                left join order_deliveries on
                    transit_time_report.order_id = order_deliveries.order_id
                where
                    transit_time_report.system_id = '{{ system_id }}'

                    -- Lazada shippers only
                    and transit_time_report.parent_id_coalesce = 341107 
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.order_id
                    , base.creation_datetime
                    , base.granular_status
                    , base.origin_id
                    , base.destination_id
                    , base.start_clock_date
                    , base.end_clock_date
                    , shipper_sla_days.sla_days
                    , date_add(base.start_clock_date, shipper_sla_days.sla_days) as sla_date
                    , if(
                            base.end_clock_date is null
                            , datediff(
                                date_add(base.start_clock_date, shipper_sla_days.sla_days)
                                , date(from_utc_timestamp(current_date, '{{ local_timezone }}'))
                            )
                            , null
                    ) as days_to_sla_breach
                    , base.system_id
                    , base.created_month
                from base
                left join shipper_sla_days on
                    base.reporting_name = shipper_sla_days.shipper_config
                    and base.origin_id = shipper_sla_days.origin_config
                    and base.destination_id = shipper_sla_days.dest_config
                where
                    shipper_sla_days.system_id = '{{ system_id }}'
                    and shipper_config_type = 'reporting_name'
                    and shipper_sla_days.sla_type = 'breach'
                    and base.start_clock_date >= date(shipper_sla_days.start_datetime)
                    and base.start_clock_date < date(shipper_sla_days.end_datetime)
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZ_SLA_BREACH_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()