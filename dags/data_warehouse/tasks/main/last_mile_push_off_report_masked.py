import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.CANCELLED_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_THIRD_PARTY_TRANSFERS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(
                path=(
                    versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_CUTOFFS
                    + measurement_datetime_partition
                ),
                view_name="last_mile_push_off_cutoffs",
                system_id=system_id,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_THIRD_PARTY_TRANSFERS,
                view_name="order_third_party_transfers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CANCELLED_EVENTS,
                view_name="cancelled_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="push_off_base",
                jinja_template="""
                select
                    order_milestones.country
                    , order_milestones.order_id
                    , order_milestones.tracking_id
                    , if(order_milestones.third_party_tracking_id is not null, 1,0) 3rd_party_flag
                    , order_milestones.granular_status
                    , order_milestones.dest_hub_id
                    , dest_hub.name as dest_hub_name
                    , dest_hub.region as dest_hub_region
                    , dest_hub.area as dest_hub_area
                    , dest_hub.address_city as dest_hub_address_city
                    , order_milestones.dest_zone
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.first_valid_delivery_attempt_datetime
                    , order_milestones.created_month
                    , if(order_movements.order_id is null, 1,0) before_dest_hub_flag
                    , min(order_movements.entry_datetime) as min_dest_hub_inbound_datetime
                    , max(order_movements.entry_datetime) as max_dest_hub_inbound_datetime
                    , min(shipment_orders_enriched.shipment_completion_datetime) as min_shipment_completion_datetime
                    , max(shipment_orders_enriched.shipment_completion_datetime) as max_shipment_completion_datetime
                    , min(order_third_party_transfers.event_datetime) 3pl_xfer_datetime
                    , min(cancelled_events.created_at) cancelled_datetime
                from order_milestones
                left join hubs_enriched as dest_hub on
                    order_milestones.dest_hub_id = dest_hub.id
                left join order_movements on
                    order_milestones.order_id = order_movements.order_id
                    and order_movements.location_type = 'HUB'
                    and order_movements.location_id in (dest_hub.parent_hub_id, order_milestones.dest_hub_id)
                    and (
                        order_milestones.first_valid_delivery_attempt_datetime >= order_movements.entry_datetime
                        or order_milestones.first_valid_delivery_attempt_datetime is null
                    )
                left join shipment_orders_enriched on
                    order_milestones.order_id = shipment_orders_enriched.order_id
                    and order_milestones.dest_hub_id = shipment_orders_enriched.dest_hub_id
                left join cancelled_events
                    on order_milestones.order_id = cancelled_events.order_id
                    and order_milestones.system_id = cancelled_events.system_id
                left join order_third_party_transfers
                    on order_milestones.order_id = order_third_party_transfers.order_id
                    and order_milestones.system_id = order_third_party_transfers.system_id
                group by {{ range(1, 16) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="dest_time_adjust",
                jinja_template="""
                with dest_time_adjust_base as (
                select
                    *
                    , if(cancelled_datetime < coalesce(max_shipment_completion_datetime
                        , max_dest_hub_inbound_datetime)
                            , 1,0) cancelled_before_start_clock_flag
                    , if(3rd_party_flag =0 , null,
                        if(3pl_xfer_datetime > least(max_shipment_completion_datetime, max_dest_hub_inbound_datetime) 
                            and ((first_valid_delivery_attempt_datetime is null 
                                or first_valid_delivery_attempt_datetime < 3pl_xfer_datetime))
                                    , 1,0)) 3pl_nv_attempted_flag
                from push_off_base
                )
            
                select
                    *
                    , case
                        when country = 'ph' and created_month >= '2022-10' and created_month < '2023-03' then
                            least(min_dest_hub_inbound_datetime, min_shipment_completion_datetime)
                        when created_month >= '2023-01' and created_month < '2023-03' then
                            least(min_dest_hub_inbound_datetime, min_shipment_completion_datetime)
                        when created_month >= '2023-03' then
                            least(max_dest_hub_inbound_datetime,max_shipment_completion_datetime)
                        else min_dest_hub_inbound_datetime
                    end as dest_hub_datetime
                from dest_time_adjust_base
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            base.*
                            , if(
                                date_format(base.dest_hub_datetime, 'HH:mm:ss')
                                    > date_format(cutoffs.cutoff, 'HH:mm:ss')
                                , date(base.dest_hub_datetime + interval 1 day)
                                , date(base.dest_hub_datetime)
                            ) as start_clock_date
                            , date_format(cutoffs.cutoff, 'HH:mm:ss') as dest_hub_cutoff
                            , date(
                                from_utc_timestamp('{{ measurement_datetime_utc }}', '{{ local_timezone }}')
                            ) - interval 1 day as report_date
                        from dest_time_adjust as base
                        left join last_mile_push_off_cutoffs as cutoffs on
                            base.dest_hub_id = cutoffs.hub_id
                            and date(base.dest_hub_datetime) >= cutoffs.start_date
                            and date(base.dest_hub_datetime) <= cutoffs.end_date

                    )
                    , base_w_calendar as (

                        select
                            base.*
                            , coalesce(
                                start_clock_reg.working_day, start_clock_nat.working_day
                            ) as start_clock_working_day_flag
                            , case
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 1
                                    then base.start_clock_date
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 0
                                    then coalesce(
                                        start_clock_reg.next_working_day_1, start_clock_nat.next_working_day_1
                                    )
                            end as n0_cutoff_date
                            , case
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 1
                                    then coalesce(
                                        start_clock_reg.next_working_day_1, start_clock_nat.next_working_day_1
                                    )
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 0
                                    then coalesce(
                                        start_clock_reg.next_working_day_2, start_clock_nat.next_working_day_2
                                    )
                            end AS n1_cutoff_date
                            , case
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 1
                                    then coalesce(
                                        start_clock_reg.next_working_day_2, start_clock_nat.next_working_day_2
                                    )
                                when coalesce(start_clock_reg.working_day, start_clock_nat.working_day) = 0
                                    then coalesce(
                                        start_clock_reg.next_working_day_3, start_clock_nat.next_working_day_3
                                    )
                            end AS n2_cutoff_date
                        from base
                        left join calendar as start_clock_nat on
                            base.start_clock_date = start_clock_nat.date
                            and start_clock_nat.region = 'national'
                        left join calendar as start_clock_reg on
                            base.start_clock_date = start_clock_reg.date
                            and base.dest_hub_address_city = start_clock_reg.region
                            and start_clock_reg.country = 'my'

                    )
                    , final as (

                        select
                            country
                            , order_id
                            , tracking_id
                            , granular_status
                            , dest_hub_id
                            , dest_hub_name
                            , dest_hub_region
                            , dest_hub_area
                            , dest_zone
                            , dest_hub_cutoff
                            , dest_hub_datetime
                            , start_clock_date
                            , start_clock_working_day_flag
                            , first_valid_delivery_attempt_datetime

                            {%- for n in range(3) %}
                            , cast(concat(n{{ n }}_cutoff_date, ' 23:59:59') as timestamp) as n{{ n }}_cutoff_datetime
                            , n{{ n }}_cutoff_date
                            , if(n{{ n }}_cutoff_date <= report_date 
                                and (3pl_nv_attempted_flag = 1 or cancelled_before_start_clock_flag = 0)
                                    , 1, 0) as n{{ n }}_measured
                            , if(
                                n{{ n }}_cutoff_date <= report_date and (3pl_nv_attempted_flag = 1 
                                    or cancelled_before_start_clock_flag = 0)
                                    , if(date(first_valid_delivery_attempt_datetime) <= n{{ n }}_cutoff_date, 1, 0)
                                        , null) as n{{ n }}_met
                            {%- endfor %}

                            , created_month
                        from base_w_calendar

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_PUSH_OFF_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
