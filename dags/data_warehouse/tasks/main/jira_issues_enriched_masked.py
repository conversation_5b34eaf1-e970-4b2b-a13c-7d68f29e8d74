import sys

from pyspark.sql import SparkSession
from pyspark.sql import types as T

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.JiraDAG.Task.JIRA_ISSUES_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.JiraDAG.Task.JIRA_ISSUES_ENRICHED_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(data_warehouse.JiraDAG.Task.JIRA_ISSUE_EVENTS_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    is_masked = True
    input_env = "prod"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
            ),
        ),
        versioned_parquet_tables=(
            # read full table
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).JIRA_ISSUE_EVENTS,
                view_name="issue_events",
            ),
        ),
        delta_tables=(base.InputTable(path=delta_tables.Jira(input_env, is_masked).ISSUES, view_name="issues"),),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base_calendar",
                jinja_template="""
                SELECT 
                    country,
                    date,
                    day,
                    comments
                FROM
                    calendar 
                group by 1,2,3,4
                """,
            ),
            base.TransformView(
                view_name="issue_times",
                jinja_template="""
                with
                    issue_status_events as (

                        -- filter issue status events
                        select
                            issue_id
                            , item_to
                            , created_datetime
                            , lead(
                                created_datetime,
                                1,
                                from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}')
                                ) over (partition by issue_id order by created_datetime)
                            as next_created_datetime
                        from issue_events
                        where
                            item_field = 'status'

                    ), issue_status_durations as (

                        -- calculate duration in each status
                        select
                            *
                            , unix_timestamp(next_created_datetime) - unix_timestamp(created_datetime)
                            as duration_sec
                            , bus_days_between(cast(next_created_datetime as string),
                                cast(created_datetime as string))
                            as duration_bus_days
                        from issue_status_events

                    ), final as (

                        -- sum duration in dwell and pending states
                        select
                            issue_id
                            , sum(duration_sec) filter (
                                where
                                    item_to in (
                                        -- Waiting for PR
                                        11002
                                        -- Waiting for PR / Approval
                                        , 11000
                                        -- In PR
                                        , 10600
                                        -- In Review
                                        , 10800
                                        -- Development Complete
                                        , 10301
                                        -- Dev Complete
                                        , 10900
                                        -- Ready for QA
                                        , 10300
                                        -- Waiting for QA
                                        , 11001
                                    )
                            ) as dwell_time_sec
                            , sum(duration_bus_days) filter (
                                where
                                    item_to in (
                                        -- Waiting for PR
                                        11002
                                        -- Waiting for PR / Approval
                                        , 11000
                                        -- In PR
                                        , 10600
                                        -- In Review
                                        , 10800
                                        -- Development Complete
                                        , 10301
                                        -- Dev Complete
                                        , 10900
                                        -- Ready for QA
                                        , 10300
                                        -- Waiting for QA
                                        , 11001
                                    )
                            ) as dwell_time_bus_days
                            , sum(duration_sec) filter (
                                where
                                    item_to in (
                                        -- Pending Release
                                        10002
                                        -- For Release
                                        , 10003
                                    )
                            ) as pending_time_sec
                            , sum(duration_bus_days) filter (
                                where
                                    item_to in (
                                        -- Pending Release
                                        10002
                                        -- For Release
                                        , 10003
                                    )
                            ) as pending_time_bus_days
                            from issue_status_durations
                        group by 1

                    )

                select
                    *
                from final
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime, "local_timezone": date.Timezone.SG},
            ),
            base.TransformView(
                view_name="issue_times_exclude", 
                jinja_template="""
                with
                    issue_status_events as (

                        -- filter issue status events
                        select
                            issue_id
                            , issue_key
                            , SUBSTRING(issue_key, 1, POSITION('-' IN issue_key) - 1) AS project_key
                            , COALESCE(
                                CASE 
                                    WHEN SUBSTRING(issue_key, 1, POSITION('-' IN issue_key) - 1) IN ('SO', 'PI', 'COREV2', 'FM', 'LM', 'SORT', 'TICKETING') THEN 'sg'
                                    WHEN SUBSTRING(issue_key, 1, POSITION('-' IN issue_key) - 1) = 'HUB' THEN 'id'
                                    WHEN SUBSTRING(issue_key, 1, POSITION('-' IN issue_key) - 1) IN ('FPL', 'XXB', 'NJMCX', 'NJM') THEN 'vn'
                                    WHEN SUBSTRING(issue_key, 1, POSITION('-' IN issue_key) - 1) IN ('DP', 'SF', 'CTT') THEN 'in'
                                    ELSE NULL
                                END,
                                system_id
                              ) AS system_id
                            , item_to
                            , created_datetime
                            , lead(
                                created_datetime,
                                1,
                                from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}')
                                ) over (partition by issue_id order by created_datetime)
                            as next_created_datetime
                            , DATE(created_datetime) AS date
                        from issue_events
                        where
                            item_field = 'status'

                    ), issue_status_durations_exclude as (

                        -- calculate duration in each status
                        select
                            *
                            , unix_timestamp(next_created_datetime) - unix_timestamp(created_datetime) as duration_sec_exclude
                            , CONCAT(MONTH(date), '-', DAY(date)) AS month_day
                        FROM 
                            issue_status_events 
                            
                    ), holiday_weekend_exclusion AS (
                    
                        -- Calculate number of weekend days between created and next created datetime
                        SELECT 
                            isd.issue_id,
                            isd.issue_key,
                            isd.system_id,
                            isd.item_to,
                            isd.created_datetime,
                            isd.next_created_datetime,
                            isd.date,
                            isd.month_day,
                            isd.duration_sec_exclude,
                            SUM(
                                CASE 
                                    WHEN cl.day IN ('Saturday', 'Sunday') THEN LEAST(UNIX_TIMESTAMP(isd.next_created_datetime), UNIX_TIMESTAMP(cl.date + INTERVAL 1 DAY)) 
                                         - GREATEST(UNIX_TIMESTAMP(isd.created_datetime), UNIX_TIMESTAMP(cl.date))
                                    ELSE 0 
                                END
                            ) AS weekend_duration_sec,
                            SUM(
                                CASE 
                                    WHEN cl.comments IS NOT NULL AND cl.day NOT IN ('Saturday', 'Sunday') THEN LEAST(UNIX_TIMESTAMP(isd.next_created_datetime), UNIX_TIMESTAMP(cl.date + INTERVAL 1 DAY)) 
                                         - GREATEST(UNIX_TIMESTAMP(isd.created_datetime), UNIX_TIMESTAMP(cl.date))
                                    ELSE 0 
                                END
                            ) AS holiday_duration_sec
                        FROM 
                            issue_status_durations_exclude isd
                        LEFT JOIN base_calendar cl
                            ON cl.date BETWEEN DATE(isd.created_datetime) 
                            AND DATE(isd.next_created_datetime)
                            AND isd.system_id = cl.country
                        GROUP BY 
                           1,2,3,4,5,6,7,8,9
                           
                    ), issue_status_durations_enriched as (
                        SELECT 
                            t1.*
                            , cl1.comments
                            , CASE 
                                WHEN cl1.comments is not null
                                THEN 1 ELSE 0
                              END as is_holiday
                            , CASE 
                                WHEN cl1.day IN ('Saturday', 'Sunday') 
                                     AND cl2.day IN ('Saturday', 'Sunday') 
                                THEN 1 ELSE 0 
                              END AS is_weekend
                            , cl1.day as created_day
                            , cl2.day as next_created_day
                            , DATE(t1.created_datetime) AS created_date
                            , DATE(t1.next_created_datetime) AS next_created_date
                            , t1.duration_sec_exclude - t1.holiday_duration_sec - t1.weekend_duration_sec AS duration_sec_exclude_weekends_holidays
                            , CASE WHEN 
                                -- September code freeze
                                t1.month_day IN ('9-8', '9-9', '9-10', '9-11', '9-12') OR
                                -- October code freeze
                                t1.month_day IN ('10-9', '10-10', '10-11', '10-12', '10-13') OR
                                -- November code freeze
                                t1.month_day IN ('11-8', '11-9', '11-10', '11-11', '11-12', '11-13', '11-14', '11-15', '11-16') OR
                                -- December code freeze
                                t1.month_day IN ('12-9', '12-10', '12-11', '12-12', '12-13', '12-14', '12-15', '12-16', '12-17')
                              THEN 1 ELSE 0 END AS code_freeze_period
                        FROM holiday_weekend_exclusion t1
                        LEFT JOIN base_calendar cl1
                            ON DATE(t1.created_datetime) = cl1.date
                            AND t1.system_id = cl1.country
                        LEFT JOIN base_calendar cl2
                            ON DATE(t1.next_created_datetime) = cl2.date
                            AND t1.system_id = cl2.country

                    ), final as (

                        SELECT
                            issue_id,
                            SUM(
                                CASE 
                                    WHEN code_freeze_period = 0 AND is_holiday = 0 AND is_weekend = 0 AND item_to IN (
                                        -- In Development
                                        3,
                                        -- Development Complete
                                        10301,
                                        -- In PR
                                        10600,
                                        -- Ready For QA 
                                        10300,
                                        -- In QA
                                        10101,
                                        -- Waiting for Dependency 
                                        11700,
                                        -- Pending Release
                                        10002,
                                        -- For Release
                                        10003
                                    ) THEN duration_sec_exclude_weekends_holidays
                                    WHEN code_freeze_period != 0 AND is_holiday = 0 AND is_weekend = 0 AND item_to IN (
                                        -- In Development
                                        3,
                                        -- Development Complete
                                        10301,
                                        -- In PR
                                        10600,
                                        -- Ready For QA 
                                        10300,
                                        -- In QA
                                        10101,
                                        -- Waiting for Dependency 
                                        11700
                                    ) THEN duration_sec_exclude_weekends_holidays
                                    ELSE 0
                                END
                            ) AS cycle_time_sec,
                            SUM(
                                CASE 
                                    WHEN code_freeze_period = 0 AND is_holiday = 0 AND is_weekend = 0 AND item_to IN (
                                        -- In Development
                                        3,
                                        -- Development Complete
                                        10301,
                                        -- In PR
                                        10600,
                                        -- Ready For QA 
                                        10300,
                                        -- In QA
                                        10101,
                                        -- Waiting for Dependency 
                                        11700,
                                        -- Pending Release
                                        10002,
                                        -- For Release
                                        10003
                                    ) THEN duration_sec_exclude_weekends_holidays / 86400.0
                                    WHEN code_freeze_period != 0 AND is_holiday = 0 AND is_weekend = 0 AND item_to IN (
                                        -- In Development
                                        3,
                                        -- Development Complete
                                        10301,
                                        -- In PR
                                        10600,
                                        -- Ready For QA 
                                        10300,
                                        -- In QA
                                        10101,
                                        -- Waiting for Dependency 
                                        11700
                                    ) THEN duration_sec_exclude_weekends_holidays / 86400.0
                                    ELSE 0
                                END
                            ) AS cycle_time_days
                        FROM 
                            issue_status_durations_enriched
                        GROUP BY 
                            issue_id
                     )
                     
                select
                    *
                from final
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime, "local_timezone": date.Timezone.SG},
            ),
            base.TransformView(
                view_name="issue_start_end_events",
                jinja_template="""
                select
                    issue_id
                    , min(created_datetime) filter (where item_field = 'Start date') as start_datetime
                    , max(created_datetime) filter (where item_field = 'End date') as end_datetime
                from issue_events
                group by 1
                """,
            ),
            base.TransformView(
                view_name="issues_enriched",
                jinja_template="""
                with
                    issue_sprints_base as (

                        -- parse first sprint for each issue (if any)
                        select
                            id as issue_id
                            , get_json_object(fields, '$.customfield_10004[0]') as sprint_string
                        from issues

                    ), issue_sprints as (

                        -- parse sprint details
                        select
                            issue_id
                            , regexp_extract(sprint_string, '(?<=(id=))(.*)(?=(,rapidViewId=))', 2) as sprint_id
                            , regexp_extract(sprint_string, '(?<=(name=))(.*)(?=(,startDate=))', 2) as sprint_name
                            , regexp_extract(sprint_string, '(?<=(state=))(.*)(?=(,name=))', 2) as sprint_state
                            , regexp_extract(
                                sprint_string, '(?<=(startDate=))(.*)(?=(,endDate=))', 2
                            ) as sprint_start_datetime_utc
                            , regexp_extract(
                                sprint_string, '(?<=(endDate=))(.*)(?=(,completeDate=))', 2
                            ) as sprint_end_datetime_utc
                            , regexp_extract(
                                sprint_string, '(?<=(completeDate=))(.*?)(?=(Z,))', 2
                            ) as sprint_complete_datetime_utc
                        from issue_sprints_base
                        where
                            sprint_string is not null

                    ),
                    issues_parsed as (

                        -- parse JSON fields
                        select
                            cast(id as bigint) as id
                            , key
                            , cast(get_json_object(fields, '$.project.id') as bigint) as project_id
                            , get_json_object(fields, '$.project.key') as project_key
                            , get_json_object(fields, '$.project.name') as project_name
                            , cast(get_json_object(fields, '$.parent.id') as bigint) as parent_id
                            , get_json_object(fields, '$.parent.key') as parent_key
                            , concat_ws(', ',
                                transform(
                                    from_json(get_json_object(fields, '$.subtasks'), 'array<string>'),
                                    x -> get_json_object(x, '$.key')
                                )
                            ) as subtask_keys
                            , get_json_object(fields, '$.customfield_13801.value') as program
                            , get_json_object(fields, '$.customfield_10006') as epic_key
                            , concat_ws(', ',
                                transform(
                                    from_json(get_json_object(fields, '$.issuelinks'), 'array<string>'),
                                    x -> get_json_object(x, '$.outwardIssue.key')
                                )
                            ) as linked_issue_keys
                            , get_json_object(fields, '$.issuetype.name') as issue_type
                            , get_json_object(fields, '$.reporter.emailAddress') as reporter
                            , get_json_object(fields, '$.assignee.emailAddress') as assignee
                            , get_json_object(fields, '$.customfield_10500.emailAddress') as qa_assignee
                            , get_json_object(fields, '$.summary') as summary
                            , get_json_object(fields, '$.priority.name') as priority
                            , get_json_object(fields, '$.status.name') as status
                            , get_json_object(fields, '$.resolution.name') as resolution
                            , cast(get_json_object(fields, '$.customfield_10002') as int) as story_points
                            , cast(get_json_object(fields, '$.customfield_12005') as int) as qa_story_points
                            , cast(get_json_object(fields, '$.resolutiondate') as timestamp) as resolved_datetime_utc
                            , get_json_object(fields, '$.duedate') as due_date
                            , concat_ws(', ', from_json(get_json_object(fields, '$.labels'), 'array<string>')) as labels
                            , concat_ws(', ',
                                transform(
                                    from_json(get_json_object(fields, '$.fixVersions'), 'array<string>'),
                                    x -> get_json_object(x, '$.name')
                                )
                            ) as fix_versions
                            , concat_ws(', ',
                                transform(
                                    from_json(get_json_object(fields, '$.components'), 'array<string>'),
                                    x -> get_json_object(x, '$.name')
                                )
                            ) as components
                            , get_json_object(fields, '$.description') as description
                            , cast(created as timestamp) as created_datetime_utc
                            , cast(updated as timestamp) as updated_datetime_utc
                        from issues

                    ), final as (

                        select
                            issues_parsed.id
                            , issues_parsed.key
                            , issues_parsed.project_id
                            , issues_parsed.project_key
                            , issues_parsed.project_name
                            , issues_parsed.parent_id
                            , issues_parsed.parent_key
                            , issues_parsed.subtask_keys
                            , issues_parsed.program
                            , issues_parsed.epic_key
                            , issues_parsed.linked_issue_keys
                            , issues_parsed.issue_type
                            , issues_parsed.reporter
                            , issues_parsed.assignee
                            , issues_parsed.qa_assignee
                            , issues_parsed.summary
                            , issues_parsed.priority
                            , issues_parsed.status
                            , issues_parsed.resolution
                            , issues_parsed.story_points
                            , issues_parsed.qa_story_points
                            , cast(issue_sprints.sprint_id as bigint) as sprint_id
                            , issue_sprints.sprint_name
                            , issue_sprints.sprint_state
                            , from_utc_timestamp(
                                issue_sprints.sprint_start_datetime_utc, '{{ local_timezone }}'
                            ) as sprint_start_datetime
                            , from_utc_timestamp(
                                issue_sprints.sprint_end_datetime_utc, '{{ local_timezone }}'
                            ) as sprint_end_datetime
                            , from_utc_timestamp(
                                issue_sprints.sprint_complete_datetime_utc, '{{ local_timezone }}'
                            ) as sprint_complete_datetime
                            , issue_start_end_events.start_datetime
                            , issue_start_end_events.end_datetime
                            , from_utc_timestamp(
                                issues_parsed.resolved_datetime_utc, '{{ local_timezone }}'
                            ) as resolved_datetime
                            , issues_parsed.due_date
                            , issues_parsed.labels
                            , issues_parsed.fix_versions
                            , issues_parsed.components
                            , issues_parsed.description
                            , issue_times.dwell_time_sec
                            , issue_times.dwell_time_bus_days
                            , issue_times.pending_time_sec
                            , issue_times.pending_time_bus_days
                            , issue_times_exclude.cycle_time_sec
                            , issue_times_exclude.cycle_time_days
                            , from_utc_timestamp(
                                issues_parsed.created_datetime_utc, '{{ local_timezone }}'
                            ) as created_datetime
                            , from_utc_timestamp(
                                issues_parsed.updated_datetime_utc, '{{ local_timezone }}'
                            ) as updated_datetime
                            , date_format(
                                from_utc_timestamp(issues_parsed.created_datetime_utc, '{{ local_timezone }}'),
                                'yyyy-MM'
                            ) as created_month
                        from issues_parsed
                        left join issue_sprints on
                            issues_parsed.id = issue_sprints.issue_id
                        left join issue_times on
                            issues_parsed.id = issue_times.issue_id
                        left join issue_times_exclude on
                            issues_parsed.id = issue_times_exclude.issue_id
                        left join issue_start_end_events on
                            issues_parsed.id = issue_start_end_events.issue_id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={"local_timezone": date.Timezone.SG},
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_ISSUES_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=constants.SystemID.SG,
        partition_by=("created_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def bus_days_between(end_dt, start_dt):
    # note that the order of dates is flipped.
    # we define the function in this manner to be consistent with other Spark SQL functions
    return float(date.num_bus_days(start_dt, end_dt, "1111100", None))


def run(spark, config):
    spark.udf.register("bus_days_between", bus_days_between, T.DoubleType())
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    # with base.get_spark(add_dags_zip=True) as spark:
    run(spark, task_config)