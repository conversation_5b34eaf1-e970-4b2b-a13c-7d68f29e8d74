import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON><PERSON>

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_MASKED,
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env


    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_DETAILS,
                view_name="order_details",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                system_id=system_id,
                view_name="shippers_enriched"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    from_utc_timestamp(orders.created_at, '{{ local_timezone }}') AS creation_datetime
                    , date(from_utc_timestamp(orders.created_at, '{{ local_timezone }}')) AS creation_date
                    , orders.id AS order_id
                    , orders.tracking_id
                    , CASE
                            WHEN get_json_object(orders.shipper_ref_metadata, '$.platformInfo.platformTags')
                                like '%lazmall%' then 1
                            ELSE 0
                        END AS lazmall_flag
                    , case when orders.shipper_order_ref_no like 'NV%' then 1 else 0 end as is_tap_flag
                    , orders.granular_status
                    , cast(cods.goods_amount as double) as cod_value
                    , cast(orders.insurance as double) as insurance_value
                    , orders.to_name
                    , orders.to_email
                    , orders.to_contact
                    , orders.to_address1
                    , orders.to_address2
                    , replace(
                        replace(
                            regexp_replace(order_details.package_content, '[{}"]','')
                            ,'[',''
                        )
                        ,']',''
                    ) as package_content
                    , split(lower(orders.to_email),'@')[1] as to_email_domain
                    , cast(get_json_object(orders.data, '$.originalWeight') as double) as original_weight
                    , cast(get_json_object(dimensions, '$.width') as double) as nv_width
                    , cast(get_json_object(dimensions, '$.height') as double) as nv_height
                    , cast(get_json_object(dimensions, '$.length') as double) as nv_length
                    , cast(get_json_object(dimensions, '$.weight') as double) as nv_weight
                    , shippers_enriched.id as shipper_id
                    , shippers_enriched.shipper_name
                    , cast(get_json_object(orders.shipper_ref_metadata, '$.shipper.sellerId') as long) as seller_id
                    , get_json_object(orders.shipper_ref_metadata, '$.shipper.sellerName') as seller_name
                    , date_format(orders.created_at, 'yyyy-MM') AS created_month
                    , '{{ system_id }}' as system_id
                from orders
                left join order_details on
                    orders.id = order_details.order_id
                left join cods on
                    orders.cod_id = cods.id
                join shippers_enriched
                    on orders.global_shipper_id = shippers_enriched.id
                where
                    -- Lazada only
                    shippers_enriched.parent_id_coalesce = 341107

                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()