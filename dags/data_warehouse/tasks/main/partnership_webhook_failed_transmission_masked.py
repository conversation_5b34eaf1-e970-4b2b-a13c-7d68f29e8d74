import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from data_warehouse.tasks.main import base
from datetime import timedelta
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_FAILED_TRANSMISSION_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_FAILED_TRANSMISSION_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_MASKED,
        data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED,
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id","shipper","delivery_success_date")),
    ),
)


def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX,
                            view_name="prefix"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Data for one completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_ORDERS+latest_partition) \
        .filter(F.col("delivery_success_date") == completion_date).createOrReplaceTempView("webhook_orders")

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED+latest_partition) \
        .filter(F.col("delivery_success_date") == completion_date).createOrReplaceTempView("webhook_orders_enriched")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="failed_transmission",
                jinja_template="""

                select
                    webhook_orders_enriched.tracking_id
                    , webhook_orders_enriched.shipper
                    , webhook_orders_enriched.delivery_success_date
                    , webhook_orders_enriched.delivery_success_datetime
                    , webhook_orders_enriched.status_code
                    , webhook_orders_enriched.status
                    , webhook_orders_enriched.webhook_attempted_flag
                    , webhook_orders.uuid
                    , webhook_orders.response_code
                    , webhook_orders.webhook_sent_time
                    , webhook_orders.event_time
                    , webhook_orders.webhook_generated_detail
                    , webhook_orders_enriched.system_id
                from webhook_orders_enriched 
                left join webhook_orders
                    on webhook_orders.tracking_id = webhook_orders_enriched.tracking_id
                    and coalesce(webhook_orders.status_code,999) = coalesce(webhook_orders_enriched.status_code,999)
                    and (
                        webhook_orders.status_code is not null
                        or (
                            case when webhook_orders.status_code is null then
                                webhook_orders_enriched.status =
                                if(webhook_orders.webhook_event_state is null
                                , webhook_orders.webhook_event_status
                                , concat(
                                    concat(webhook_orders.webhook_event_status,', '), webhook_orders.webhook_event_state
                                    )
                                )
                            end
                            )
                        )
                where 
                    -- Filter webhooks with no successfully sent (response_code = 200) webhooks
                    webhook_orders_enriched.first_webhook_sent_time is null
                """
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_FAILED_TRANSMISSION,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "delivery_success_date"),
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.measurement_datetime,
    )
    run(spark, task_config)
    spark.stop()