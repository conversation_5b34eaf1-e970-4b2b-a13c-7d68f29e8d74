import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.COMBINED_SHIPMENT_COMPLIANCE_KPI_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.COMBINED_SHIPMENT_COMPLIANCE_KPI_MASKED,
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

sea_haul_kpi_data_start_date = "'2023-03-01'"

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SHIPMENT_SCANS,
                view_name="trip_shipment_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_MAWB,
                view_name="flight_mawb",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIP_DRIVERS,
                view_name="movement_trip_drivers",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="mmda_lh",
                jinja_template="""
                  with table as (
                        select 
                            shipment_events.shipment_id
                            , shipment_events.hub_system_id
                            , shipment_events.hub_id
                            , shipments_enriched.orig_hub_id
                            , shipments_enriched.orig_hub_name
                            , shipments_enriched.orig_hub_region
                            , shipments_enriched.dest_hub_id
                            , shipments_enriched.dest_hub_name
                            , shipments_enriched.dest_hub_region
                            , shipment_events.event
                            , shipment_events.status
                            , case
                                when lower(shipment_events.hub_system_id) IN ('my','sg','ph')
                                THEN shipment_events.created_at + interval '8' hour
                                ELSE shipment_events.created_at + interval '7' hour
                            END as created_at
                            , shipment_events.user_id
                            , get_json_object(shipment_events.ext_data, '$.source') as scan
                            , shipment_events.created_month
                            , shipments_enriched.system_id
                        from shipment_events
                        left join shipments_enriched
                            on shipment_events.shipment_id = shipments_enriched.shipment_id
                        where 1=1 
                            and lower(shipments_enriched.shipment_type) in ('land_haul')
                            ),

                     scanned as (       
                            select 
                                shipment_id
                                , system_id
                                , count(*) filter(where lower(scan) like '%mmda%)') mmda_count
                                , count(*) total_count
                            from table
                            where 1=1 and lower(scan) like '%shipment%inbound%'
                            group by 1,2
                            ),

                        checking_adoption_cte as (  

                        select 
                            scanned.*
                            , case when total_count is null THEN null
                                   when mmda_count = total_count THEN 1
                                   ELSE 0 end as mmda_adoption_flag
                        from scanned
                        order by shipment_id
                        )

                        select * from checking_adoption_cte
            """,
            ),
            base.TransformView(
                view_name="ah_trip_tagging",
                jinja_template="""
                with shipment_join_ah as (
                    select
                        ah_shipments.shipment_id
                         , ah_shipments.system_id
                         , flight_info.trip_id
                         , flight_info.flight_no
                    from shipments_enriched as ah_shipments
                    left join shipment_ext_awbs
                        on ah_shipments.mawb = shipment_ext_awbs.ref_id
                    left join flight_mawb
                        on shipment_ext_awbs.id = flight_mawb.mawb_id
                    left join flight_info
                        on flight_info.id = flight_mawb.flight_info_id
                    left join movement_trips_enriched
                         on movement_trips_enriched.trip_id = flight_info.trip_id 
                    where 1=1
                        and ah_shipments.shipment_type = 'AIR_HAUL'
                        and flight_info.deleted_at is null
                        and flight_mawb.deleted_at is null
                ),

                aggregate as (

                    select
                        shipment_id
                        , system_id
                        , count(shipment_id) filter(where flight_no is not null) / count(shipment_id) ah_trip_percent
                    from shipment_join_ah
                    group by 1,2
                ),

                final as (

                select
                    shipment_id
                    , system_id
                    , max(if(flight_no is null,0,1)) as ah_trip_tagging_flag
                from shipment_join_ah
                group by 1,2
                )

                select * from final 
                """,
            ),
            base.TransformView(
                view_name="movement_trip_drivers_cte",
                jinja_template="""
                select 
                    trip_id
                    , max_by(driver_id, id) filter (where is_primary = 1) as primary_driver_id
                from movement_trip_drivers
                group by 1
                """,
            ),
            base.TransformView(
                view_name="shipments_enriched_cte",
                jinja_template="""
                select 
                    se.shipment_id
                    , se.system_id
                    , se.first_trip_id
                    , se.shipment_sla_datetime
                    , se.shipment_creation_datetime
                    , se.shipment_type
                    , se.status
                    , se.orig_shipment_van_inbound_datetime
                    , se.dest_shipment_hub_inbound_datetime
                    , se.orig_hub_name 
                    , se.orig_hub_region  
                    , se.dest_hub_name        
                    , se.dest_hub_region        
                    , mtd.primary_driver_id
                    , se.mawb
                    , se.swb
                    , se.operator_weight
                      , case when mmda_lh.mmda_count is null and mmda_lh.total_count is null THEN null
                           when mmda_lh.mmda_count is null and mmda_lh.total_count is not null THEN 0
                           else mmda_lh.mmda_count END AS mmda_numerator_count       
                    , if(mmda_lh.total_count is null, 0, mmda_lh.total_count) mmda_denomenator_count
                    , mmda_lh.mmda_adoption_flag
                    , se.complete_operator_dimensions_flag
                    , ah_trip_tagging.ah_trip_tagging_flag
                    , se.created_month
                from  shipments_enriched as se
                left join movement_trip_drivers_cte as mtd
                    on se.first_trip_id = mtd.trip_id
                left join hubs_enriched origin_hub
                    on se.orig_hub_id = origin_hub.id
                    and se.system_id = origin_hub.system_id
                left join hubs_enriched dest_hub
                    on se.dest_hub_id = dest_hub.id
                    and se.system_id = dest_hub.system_id
                left join mmda_lh
                    on mmda_lh.shipment_id = se.shipment_id
                left join ah_trip_tagging
                    on ah_trip_tagging.shipment_id = se.shipment_id
                where 
                    se.shipment_type in ('AIR_HAUL', 'SEA_HAUL', 'LAND_HAUL')
                    and lower(se.status) <> ('cancelled')
                    and (
                        lower(origin_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                        and
                        lower(dest_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                        )
                """,
            ),
            base.TransformView(
                view_name="combined_shipment_compliance",
                jinja_template="""
                with air_flag_table as (

                    select 
                        system_id
                        , shipment_id
                        , shipment_creation_datetime
                        , status
                        , shipment_type
                        , orig_hub_name 
                        , orig_hub_region  
                        , dest_hub_name        
                        , dest_hub_region 
                        , mmda_denomenator_count
                        , mmda_numerator_count
                        , mmda_adoption_flag
                        , ah_trip_tagging_flag
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as scan_compliant
                        , case 
                                when 
                                    mawb is not null
                                    and operator_weight is not null
                                    and complete_operator_dimensions_flag = 1
                                    and ah_trip_tagging_flag = 1
                                    and system_id in ('id','ph')
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                when 
                                    mawb is not null
                                    and operator_weight is not null
                                    and ah_trip_tagging_flag = 1
                                    and system_id in ('my')
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as data_compliant     
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as van_scanned
                        , case
                                when 
                                    dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as hub_scanned
                        , case
                                when 
                                    mawb is not null
                                    and system_id in ('id', 'ph', 'my')
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as mawb_details
                        , case
                                when 
                                    swb is not null
                                    and system_id in ('id', 'ph', 'my')
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as swb_details
                        , case when ah_trip_tagging_flag = 1 and shipment_type = 'AIR_HAUL' THEN 1 else 0 end as ah_trip_details
                        , case
                                when 
                                    operator_weight is not null
                                then 1
                                else 0
                            end as weight_details
                        , case
                                when 
                                    complete_operator_dimensions_flag = 1
                                    and shipment_type in ('AIR_HAUL')
                                then 1
                                else 0
                            end as dim_details             
                        , case
                                when
                                    shipment_type in ('AIR_HAUL')
                                then 0 
                            end as sla_assigned
                        , case when shipment_type = 'AIR_HAUL' THEN 0 end as mmda_details
                        , created_month


                    from shipments_enriched_cte
                    where shipment_type in ('AIR_HAUL')
                    ),


                    sea_flag_table as (
                    select 
                        system_id
                        , shipment_id
                        , shipment_creation_datetime
                        , status
                        , shipment_type
                        , orig_hub_name 
                        , orig_hub_region  
                        , dest_hub_name        
                        , dest_hub_region
                        , mmda_denomenator_count
                        , mmda_numerator_count
                        , mmda_adoption_flag
                        , ah_trip_tagging_flag
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as scan_compliant
                        , case 
                                when 
                                    swb is not null
                                    and operator_weight is not null
                                    and complete_operator_dimensions_flag = 1
                                    and shipment_creation_datetime >= date """+sea_haul_kpi_data_start_date+"""
                                    and system_id in ('id')
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                when 
                                    swb is not null
                                    and shipment_creation_datetime >= date """+sea_haul_kpi_data_start_date+"""
                                    and system_id in ('my')
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                when 
                                    swb is not null
                                    and system_id in ('ph')
                                    and shipment_creation_datetime >= date """+sea_haul_kpi_data_start_date+"""
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as data_compliant         
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as van_scanned
                        , case
                                when 
                                    dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as hub_scanned
                        ,  case
                                when 
                                    mawb is not null
                                    and system_id in ('id', 'ph', 'my')
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as mawb_details
                        , case
                                when 
                                    swb is not null
                                    and system_id in ('id', 'ph', 'my')
                                    and shipment_creation_datetime >= date """+sea_haul_kpi_data_start_date+"""
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                when 
                                    system_id in ('id', 'ph', 'my')
                                    and shipment_type in ('SEA_HAUL')
                                    and shipment_creation_datetime < date """+sea_haul_kpi_data_start_date+"""
                                then 1
                                else 0
                            end as swb_details
                        , 0 as ah_trip_details
                        , case
                                when 
                                    operator_weight is not null
                                    and system_id in ('id') 
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as weight_details
                        , case
                                when 
                                    complete_operator_dimensions_flag = 1
                                    and system_id != 'my'
                                    and shipment_type in ('SEA_HAUL')
                                then 1
                                else 0
                            end as dim_details             
                        , case
                                when
                                    shipment_type in ('SEA_HAUL')
                                then 0 
                            end as sla_assigned
                        , case when shipment_type = 'SEA_HAUL' THEN 0 end as mmda_details
                        , created_month
                    from shipments_enriched_cte
                    where shipment_type in ('SEA_HAUL')
                    ),

                land_flag_table as (

                    select 
                        system_id
                        , shipment_id
                        , shipment_creation_datetime
                        , status
                        , shipment_type
                        , orig_hub_name 
                        , orig_hub_region  
                        , dest_hub_name        
                        , dest_hub_region
                        , mmda_denomenator_count
                        , mmda_numerator_count
                        , mmda_adoption_flag
                        , ah_trip_tagging_flag
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('LAND_HAUL')
                                then 1
                                else 0
                            end as scan_compliant
                        , case 
                                when 
                                    mmda_adoption_flag = 1
                                    and shipment_sla_datetime is not null
                                    and shipment_type in ('LAND_HAUL')
                                then 1
                                else 0
                            end as data_compliant         
                        , case
                                when 
                                    orig_shipment_van_inbound_datetime is not null
                                    and shipment_type in ('LAND_HAUL')
                                then 1
                                else 0
                            end as van_scanned
                        , case
                                when 
                                    dest_shipment_hub_inbound_datetime is not null
                                    and shipment_type in ('LAND_HAUL')
                                then 1
                                else 0
                            end as hub_scanned
                        , case 
                                when
                                    shipment_type in ('LAND_HAUL')
                                then 0
                            end as mawb_details
                        , case 
                                when
                                    shipment_type in ('LAND_HAUL')
                                then 0
                            end as swb_details
                        , 0 as ah_trip_details
                        , case 
                                when
                                    shipment_type in ('LAND_HAUL')
                                then 0
                            end as weight_details
                        , case 
                                when
                                    shipment_type in ('LAND_HAUL')
                                then 0
                            end as dim_details 
                        , case
                                when 
                                    shipment_sla_datetime is not null
                                    and shipment_type in ('LAND_HAUL')
                                then 1
                                else 0
                            end as sla_assigned
                        , case when mmda_adoption_flag = 1 and shipment_type = 'LAND_HAUL' THEN 1 ELSE 0
                        END AS mmda_details
                        , created_month

                    from shipments_enriched_cte
                    where shipment_type in ('LAND_HAUL')
                    )

                    select 
                        * 
                    from air_flag_table

                    UNION ALL

                    select 
                        *
                    from land_flag_table

                    UNION ALL

                    select 
                        * 
                    from sea_flag_table
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select 
                    system_id
                    , shipment_id
                    , status
                    , shipment_type
                    , shipment_creation_datetime
                    , case
                            when 
                                scan_compliant = 0 
                                and data_compliant = 1
                            then 'Not Scan Compliant'
                            when 
                                data_compliant = 0
                                and scan_compliant = 1
                            then 'Not Data Compliant'
                            when data_compliant = 1
                                and scan_compliant = 1
                            then 'Fully Compliant'
                            else 'Fully Incompliant'
                        end as compliant_indicator    
                    , case 
                        when 
                            (mawb_details = 0 or swb_details = 0)
                            and weight_details = 0
                            and dim_details = 0
                            and system_id in ('id')
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No MAWB/SWB, weight and DIM details'
                        when 
                            mawb_details = 0
                            and weight_details = 0
                            and dim_details = 0
                            and system_id in ('my')
                            and ah_trip_details = 0
                            and shipment_type in ('AIR_HAUL')
                        then 'No MAWB, weight and trip details'
                        when 
                            mawb_details = 0
                            and weight_details = 0
                            and dim_details = 0
                            and ah_trip_details = 0
                            and system_id in ('ph')
                            and shipment_type in ('AIR_HAUL')
                        then 'No MAWB and trip details'
                        when 
                            (mawb_details = 1 or swb_details = 1)
                            and weight_details = 0
                            and dim_details = 0
                            and system_id in ('id')
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No weight and DIM details'   
                        when 
                            mawb_details = 1
                            and weight_details = 0
                            and dim_details = 0
                            and system_id in ('my')
                            and shipment_type in ('AIR_HAUL')
                        then 'No weight details'  
                        when 
                            (mawb_details = 1 or swb_details=1)
                            and weight_details = 0
                            and dim_details = 0
                            and system_id in ('ph')
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'MAWB/SWB details are available'  
                        when 
                            (mawb_details = 0 or swb_details =0)
                            and weight_details = 1
                            and dim_details = 0
                            and system_id in ('id')
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No MAWB/SWB details and DIM details'  
                        when 
                            mawb_details = 0
                            and weight_details = 1
                            and dim_details = 0
                            and system_id in ('my')
                            and shipment_type in ('AIR_HAUL')
                        then 'No MAWB details'                 
                        when 
                            (mawb_details = 1 or swb_details = 1)
                            and weight_details = 1
                            and dim_details = 0
                            and system_id in ('id')
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No DIM details'
                        when 
                            mawb_details = 1
                            and weight_details = 1
                            and dim_details = 0
                            and system_id in ('my')
                            and shipment_type in ('AIR_HAUL')
                        then 'MAWB and weight details are available'
                        when 
                            (mawb_details = 0 or swb_details=0)
                            and weight_details = 1
                            and dim_details = 1
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No MAWB/SWB details'  
                        when 
                            (mawb_details = 1 or swb_details = 1)
                            and weight_details = 0
                            and dim_details = 1
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'No weight details'  
                        when
                            (mawb_details = 1 or swb_details = 1)
                            and weight_details = 1
                            and dim_details = 1
                            and shipment_type in ('AIR_HAUL', 'SEA_HAUL')
                        then 'MAWB/SWB, weight and DIM details are available'
                        when 
                            sla_assigned = 0
                            and shipment_type in ('LAND_HAUL')
                        then 'No SLA assigned'  
                        when 
                            sla_assigned = 1
                            and shipment_type in ('LAND_HAUL')
                        then 'SLA assigned'
                    ELSE null end as data_compliance
                    , case when shipment_type != 'LAND_HAUL' THEN 'Not measured'
                        when mmda_adoption_flag = 0 then 
                        concat('MMDA not compliant: ', mmda_numerator_count, ' MMDA scans ouf of ', mmda_denomenator_count, ' total scans')
                        ELSE 'MMDA fully compliant'
                        END AS mmda_compliant_details
                    , case 
                            when 
                                van_scanned = 0
                                and hub_scanned = 1
                            then 'Origin hub incompliant'
                            when 
                                van_scanned = 1
                                and hub_scanned = 0
                            then 'Dest hub incompliant'
                            when 
                                van_scanned = 0 
                                and hub_scanned = 0
                            then 'Both hubs incompliant'
                            else 'Both hubs compliant'
                            end as incompliant_hub
                    , orig_hub_name 
                    , orig_hub_region  
                    , dest_hub_name        
                    , dest_hub_region  
                    , case when data_compliant = 1 and scan_compliant = 1 THEN 1 ELSE 0 END AS shipment_compliant
                    , scan_compliant
                    , data_compliant
                    , ah_trip_details
                    , van_scanned
                    , hub_scanned
                    , mawb_details
                    , swb_details
                    , weight_details
                    , dim_details
                    , sla_assigned
                    , mmda_details
                    , created_month
                from combined_shipment_compliance
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COMBINED_SHIPMENT_COMPLIANCE_KPI,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)

def run(spark, config):
    df = base.run(spark, config)
    return


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()