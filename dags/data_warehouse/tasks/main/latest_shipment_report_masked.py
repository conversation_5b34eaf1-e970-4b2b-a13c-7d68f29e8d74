import sys

from pyspark.sql import SparkSession

from common.date import to_measurement_datetime_str
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.LATEST_SHIPMENT_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.LATEST_SHIPMENT_REPORT_MASKED,
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SCANS,
                view_name="scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_ORDERS,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="add_to_shipment",
                jinja_template="""
                -- Get first add to shipment date for each order & shipment

                select
                    shipment_id
                    , order_id
                    , add_to_shipment_datetime
                    , system_id
                from
                    (
                        select
                            shipment_id
                            , order_id
                            , from_utc_timestamp(created_at, '{{ local_timezone }}') as add_to_shipment_datetime
                            , lower(order_country) AS system_id
                            , row_number() over(partition by shipment_id, order_id order by created_at) as rank
                        from shipment_orders
                    where
                        deleted_at is null
                        and lower(order_country) = '{{ system_id }}'
                    )
                where rank = 1
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="inbound",
                jinja_template="""
                -- Get first order inbound date for each order & shipment

                select
                    shipment_id
                    , order_id
                    , order_inbound_datetime
                    , system_id
                from
                    (
                        select
                            shipment_id
                            , order_id
                            , from_utc_timestamp(created_at, '{{ local_timezone }}') AS order_inbound_datetime
                            , lower(hub_country) AS system_id
                            , row_number() over(partition by shipment_id, order_id order by created_at) AS rank
                       from scans
                       where
                           deleted_at is null
                           and source = 'ORDER_INBOUND'
                           and lower(hub_country) = '{{ system_id }}'
                    )
                where rank = 1
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="order_shipment_details_base",
                jinja_template="""
                select
                    coalesce(add_to_shipment.shipment_id, inbound.shipment_id) AS shipment_id
                    , coalesce(add_to_shipment.order_id, inbound.order_id) AS order_id
                    , add_to_shipment.add_to_shipment_datetime
                    , inbound.order_inbound_datetime
                    , coalesce(add_to_shipment.system_id, inbound.system_id) AS system_id
                from add_to_shipment
                full join inbound on
                    add_to_shipment.system_id = inbound.system_id
                    and add_to_shipment.shipment_id = inbound.shipment_id
                    and add_to_shipment.order_id = inbound.order_id
                """,
            ),
            base.TransformView(
                view_name="order_shipment_details",
                jinja_template="""
                select
                    order_shipment_details_base.order_id
                    , order_shipment_details_base.shipment_id
                    , order_shipment_details_base.add_to_shipment_datetime
                    , order_shipment_details_base.order_inbound_datetime
                    , shipments.status
                    , shipments.orig_hub_id
                    , orig_hub.name as orig_hub_name
                    , shipments.dest_hub_id
                    , dest_hub.name as dest_hub_name
                    , shipments.curr_hub_id
                    , curr_hub.name as curr_hub_name
                    , shipments.created_at
                    , order_shipment_details_base.system_id
                    , date_format(add_to_shipment_datetime, 'yyyy-MM') as created_month
                from order_shipment_details_base
                left join shipments
                    on order_shipment_details_base.shipment_id = shipments.id
                left join hubs_enriched orig_hub
                    on shipments.orig_hub_id = orig_hub.id
                    and shipments.orig_hub_country = orig_hub.system_id
                left join hubs_enriched dest_hub
                    on shipments.dest_hub_id = dest_hub.id
                    and shipments.orig_hub_country = dest_hub.system_id
                left join hubs_enriched curr_hub
                    on shipments.curr_hub_id = curr_hub.id
                    and shipments.orig_hub_country = curr_hub.system_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , max_by(shipment_id,add_to_shipment_datetime) as shipment_id
                    , max_by(add_to_shipment_datetime,add_to_shipment_datetime) as add_to_shipment_datetime
                    , max_by(status,add_to_shipment_datetime) as status
                    , max_by(orig_hub_id,add_to_shipment_datetime) as orig_hub_id
                    , max_by(orig_hub_name,add_to_shipment_datetime) as orig_hub_name
                    , max_by(dest_hub_id,add_to_shipment_datetime) as dest_hub_id
                    , max_by(dest_hub_name,add_to_shipment_datetime) as dest_hub_name
                    , max_by(curr_hub_id,add_to_shipment_datetime) as curr_hub_id
                    , max_by(curr_hub_name,add_to_shipment_datetime) as curr_hub_name
                    , system_id
                    , max_by(created_month,add_to_shipment_datetime) as created_month
                from order_shipment_details
                group by 1,11

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LATEST_SHIPMENT_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()