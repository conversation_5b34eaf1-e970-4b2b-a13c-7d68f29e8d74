import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_MASKED + ".py",
    task_name=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
    ),
    depends_on=(data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_INTERMEDIATE_MASKED,
                data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_LATEST_TRIPS_MASKED,
                ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_INTERMEDIATE,
                view_name="order_hub_history_intermediate",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_LATEST_TRIPS,
                view_name="order_hub_history_latest_trip",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with latest_trip_info_combined as (
                select
                    order_hub_history_intermediate.*
                    , order_hub_history_latest_trip.trip_id
                    -- For MY add hub name condition
                    , coalesce(if((hub_flag_wrt_first_inb < 0) or
                            (next_hub_name like '%MM%' and hub_name like '%MM%' and order_hub_history_intermediate.system_id = 'my')
                                ,0,missing_trip_departure_cutoff)
                        ,0) as missing_cutoff_trip_flag
                    , if(next_hub_name like "MM%" and hub_name like "MM%" , 0,missing_outbound_trip_flag) as missing_outbound_trip_flag_cleaned
                    , order_hub_history_latest_trip.trip_start_datetime as order_hub_departure_cutoff
                    , order_hub_history_latest_trip.expected_duration_min_trip_id
                    , if(next_hub_name is null, 1, 0) next_hub_is_pudo_flag
                from order_hub_history_intermediate
                left join order_hub_history_latest_trip
                    on order_hub_history_intermediate.order_id = order_hub_history_latest_trip.order_id
                    and order_hub_history_intermediate.hub_id = order_hub_history_latest_trip.hub_id
                    and order_hub_history_intermediate.hub_seq = order_hub_history_latest_trip.hub_seq
                    and order_hub_history_intermediate.next_hub_id = order_hub_history_latest_trip.next_hub_id
                ),

                max_flag_cte as (
                select 
                    order_id
                    , max(missing_outbound_trip_flag_cleaned) as missing_trip_order
                    , max(missing_cutoff_trip_flag) trip_id_max
                    , max(hub_seq) as max_hub_seq
                    , max_by(hub_id, hub_seq) as last_hub_in_seq
                from latest_trip_info_combined
                group by 1 
                ),

                no_trip_order_flag_cte as (
                select 
                    latest_trip_info_combined.*
                    , if(max_flag_cte.missing_trip_order = 1, 1,0) no_trip_order_flag
                    , max_flag_cte.trip_id_max
                    , max_flag_cte.max_hub_seq
                    , max_flag_cte.last_hub_in_seq
                from latest_trip_info_combined
                left join max_flag_cte
                    on latest_trip_info_combined.order_id = max_flag_cte.order_id
                ),

               max_trip_comparison_cte as (
                    select 
                        *
                        , if( trip_id_max = 1 , 1,0) missing_hub_departure_cutoff_flag

                        , if(trip_id_max = 1, null, order_hub_departure_cutoff) order_hub_departure_cutoff1

                        , if(trip_id_max = 1, null, trip_id) trip_id1
                        , max_hub_seq
                        , last_hub_in_seq
                        , lead(coalesce(outbound_trip_expected_start_datetime, date(driver_inb_datetime))) over (partition by order_id order by hub_seq asc) as next_hub_outbound_trip_expected_start_datetime
                        , lead(if(trip_id_max = 1, null, coalesce(order_hub_departure_cutoff, date(last_mile_hub_arrival_cutoff)))) over (partition by order_id order by hub_seq asc) as next_hub_order_hub_departure_cutoff_01
                        , lead(date(driver_inb_datetime)) over (partition by order_id order by hub_seq asc) as next_driver_inbound_date
                    from no_trip_order_flag_cte
                ),

                condition_cte as (
                select 
                    order_id
                    ,
                     -- Condition 1
                        min(case 
                            when order_hub_departure_cutoff1 is not null and max_hub_seq != 1
                                and greatest(shipment_closed_datetime, order_hub_inbound_datetime, poh_datetime, from_shipper_to_dp_datetime, order_pickup_datetime) >= (least(order_hub_departure_cutoff1, last_mile_hub_arrival_cutoff) + interval 15 minutes)
                                and outbound_trip_actual_start_datetime is null
                                and (
                                    (next_hub_outbound_trip_expected_start_datetime > (next_hub_order_hub_departure_cutoff_01 + interval 15 minutes))
                                    or
                                    (next_driver_inbound_date > date(last_mile_hub_arrival_cutoff))
                                    )
                            then hub_seq

                            -- Condition 2
                            when (order_hub_departure_cutoff1 is not null and max_hub_seq != 1
                                and (outbound_trip_actual_start_datetime) > ( order_hub_departure_cutoff1 + interval 15 minutes))
                                and (
                                    (
                                    ((outbound_trip_actual_start_datetime + CAST( cast(outbound_trip_actual_duration_min as int)||" minute" AS Interval)) >  (order_hub_departure_cutoff1 + CAST(cast(expected_duration_min_trip_id as int)||" minute" AS Interval) + interval 15 minutes))
                                    and 
                                    (next_hub_id != last_hub_in_seq)
                                    )
                                or (
                                    ((outbound_trip_actual_start_datetime + CAST( cast(outbound_trip_actual_duration_min as int)||" minute" AS Interval)) >  (last_mile_hub_arrival_cutoff + interval 15 minutes))
                                    and 
                                    (next_hub_id = last_hub_in_seq)
                                    )
                                    )
                                and (
                                    (next_hub_outbound_trip_expected_start_datetime > next_hub_order_hub_departure_cutoff_01)
                                    or  
                                    (next_driver_inbound_date > date(last_mile_hub_arrival_cutoff))
                                    )
                            then hub_seq

                                -- Condition 3
                                when order_hub_departure_cutoff1 is null
                                and ((hub_seq = max_hub_seq) or max_hub_seq = 1)
                                and greatest(
                                    order_pickup_datetime
                                    , from_shipper_to_dp_datetime
                                    , from_dp_to_driver_datetime
                                    , poh_datetime
                                    , order_hub_inbound_datetime
                                    , order_add_to_shipment_datetime
                                    , shipment_closed_datetime
                                    , shipment_van_inbound_datetime
                                    , outbound_trip_actual_start_datetime
                                    , outbound_trip_expected_start_datetime
                                    , trip_actual_arrival_datetime
                                    , shipment_hub_inbound_datetime
                                    , stayover_datetime
                                    , driver_inb_datetime
                                    , warehouse_sweep_datetime
                                    , first_valid_delivery_attempt_datetime
                                    , from_driver_to_dp_datetime
                                    , from_dp_to_customer_datetime
                                    )
                                    >date(last_mile_hub_arrival_cutoff)
                                    then hub_seq
                            else null
                        end) as departure_exceeds_cutoff_hub_seq
                from max_trip_comparison_cte
                /*
                left join hubs_enriched as next_hub_info
                    on next_hub_id = next_hub_info.id
                */
                group by 1
                ),

                final_cte as (
                    select 
                        max_trip_comparison_cte.*
                        , if(outbound_trip_id is null and order_hub_departure_cutoff1 is not null and order_hub_departure_cutoff1 > start_clock_datetime, 1, 0) missing_outbound_trip_flag1
                        , departure_exceeds_cutoff_hub_seq
                        , if(hub_seq = max_hub_seq, 1,0) as is_last_hub_seq_flag
                    from max_trip_comparison_cte
                    left join condition_cte
                    on max_trip_comparison_cte.order_id = condition_cte.order_id
                    )

                    select 
                        order_id
                        , hub_id
                        , hub_seq
                        , is_first_order_hub_inb_hub
                        , hub_flag_wrt_first_inb
                        , is_pickup_hub
                        , is_dest_hub
                        , int(coalesce(is_sort_hub,0)) is_sort_hub
                        , previous_hub_id
                        , next_hub_id
                        , order_pickup_datetime
                        , poh_datetime
                        , start_clock_datetime
                        , trip_actual_arrival_datetime
                        , from_dp_to_customer_datetime
                        , from_shipper_to_dp_datetime
                        , from_driver_to_dp_datetime
                        , from_dp_to_driver_datetime
                        , order_hub_inbound_datetime
                        , warehouse_sweep_datetime
                        , order_add_to_shipment_datetime
                        , shipment_closed_datetime
                        , shipment_hub_inbound_datetime
                        , shipment_van_inbound_datetime
                        , stayover_datetime
                        , outbound_shipment_id
                        , driver_inb_datetime
                        , outbound_trip_id
                        , outbound_trip_actual_start_datetime
                        , outbound_trip_expected_start_datetime
                        , outbound_trip_expected_duration_min
                        , outbound_trip_actual_duration_min
                        , outbound_trip_on_time_start_flag
                        , outbound_trip_on_time_end_flag
                        , missing_cutoff_trip_flag missing_cutoff_trip_flag
                        , missing_outbound_trip_flag1 missing_outbound_trip_flag
                        , last_mile_hub_arrival_cutoff
                        , trip_id1 as latest_possible_outbound_trip_id
                        , expected_duration_min_trip_id as latest_possible_outbound_trip_expected_duration_min
                        , missing_hub_departure_cutoff_flag order_missing_complete_cutoffs
                        , order_hub_departure_cutoff1 as order_hub_departure_cutoff
                        , if(hub_seq = departure_exceeds_cutoff_hub_seq,1,0) is_first_departure_exceeds_cutoff_hub
                        , first_valid_delivery_attempt_datetime
                        , sc_met
                        , is_last_hub_seq_flag
                        , full_path
                        , short_path
                        , system_id
                        , created_month
                    from final_cte
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()