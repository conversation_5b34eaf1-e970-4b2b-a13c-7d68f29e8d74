import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_INTERNAL_TICKET_DATA_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_INTERNAL_TICKET_DATA_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.CNTicketingToolDAG.Task.TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE_MASKED,),
    hive_metastore_config=(
    base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("created_month",)),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    # input_env = "prod"
    is_masked = True
    input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE + latest_partition,
                view_name="tiktok_internal_ticket_data",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with 
                    base as (
                            Select 
                                 t.ticket_id            
                                , t.company_name
                                , t.country
                                , t.ticket_type
                                , t.status
                                , t.tracking_id
                                , t.create_time
                                , t.response_number
                                , t.correspondence_number
                                , t.owner
                                , t.last_reponse_time
                                , t.first_reponse_time
                                , t.last_close_time
                                , t.expiration_date
                                , t.due
                                , t.close_by
                                , t.satisfication
                                , t.feedback
                                , t.created_date
                                , count(*) as cnt
                            from tiktok_internal_ticket_data t 
                            inner join 
                                (SELECT ticket_id,MAX(created_date) as max_created_date
                                    FROM tiktok_internal_ticket_data
                                    GROUP BY ticket_id) a
                            on a.ticket_id = t.ticket_id and a.max_created_date = created_date
                            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19
                            )
                    select 
                    ticket_id            
                    , company_name
                    , country
                    , ticket_type
                    , status
                    , tracking_id
                    , create_time
                    , response_number
                    , correspondence_number
                    , owner
                    , last_reponse_time
                    , first_reponse_time
                    , last_close_time
                    , expiration_date
                    , due
                    , close_by
                    , satisfication
                    , feedback
                    , created_date
                    , date_format(created_date, 'yyyy-MM') as created_month
                    from base

                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_INTERNAL_TICKET_DATA,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
        # write_mode='merge',
        # primary_keys=["ticket_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()