import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).OPPORTUNITY, view_name="salesforce_opportunity"),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_RECORD_TYPE_ENRICHED,
                view_name="salesforce_record_type_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            salesforce_opportunity.*
                            , record_type.name as record_type
                            , case
                                when remote_area_surcharge_c = 'Yes'
                                then 1
                                when remote_area_surcharge_c = 'No'
                                then 0
                                else null
                                end as remote_area_surcharge_flag
                            , from_utc_timestamp(
                                last_status_change_date_c, {{ get_local_timezone }}
                            ) as status_changed_datetime
                            , from_utc_timestamp(created_date, {{ get_local_timezone }}) as creation_datetime
                            , from_utc_timestamp(last_modified_date, {{ get_local_timezone }}) as last_modified_datetime
                            , from_utc_timestamp(
                                '{{ measurement_datetime_utc }}', {{ get_local_timezone }}
                            ) as measurement_datetime
                            , record_type.system_id
                            , date_format(
                                from_utc_timestamp(created_date, {{ get_local_timezone }}), 'yyyy-MM'
                            ) as created_month
                        from salesforce_opportunity
                        left join salesforce_record_type_enriched as record_type on
                            salesforce_opportunity.record_type_id = record_type.id
                        where
                            salesforce_opportunity.is_deleted = false

                    )
                    , final as (

                        select
                            id
                            , description
                            , name as opportunity_name
                            , type
                            , owner_id as user_id
                            , account_id
                            , cast(global_id_c as bigint) as shipper_id
                            , shipper_email_c as shipper_email
                            , shipper_profile_c as shipper_profile
                            , bulky_potential_c as bulky_potential
                            , bulky_weight_range_c as bulky_weight_range
                            , cast(shipper_tax_id_c as bigint) as shipper_tax_id
                            , billing_person_c as billing_person
                            , actual_gmv_c as actual_gmv
                            , expected_gmv_c as expected_gmv
                            , total_potential_revenue_mth_c as total_potential_revenue_mth
                            , committed_revenue_mth_c as committed_revenue_mth
                            , reservation_person_c as reservation_person
                            , liaison_contact_c as liaison_contact
                            , record_type_id
                            , record_type
                            , remote_area_surcharge_flag
                            , loss_reason_c as loss_reason
                            , detailed_loss_reasons_c as detailed_loss_reason
                            , service_level_c as service_level
                            , cast(discount_c as decimal(38, 1)) as discount
                            , shipper_discount_c as shipper_discount
                            , cast(shipper_flat_discount_c as decimal(38, 2)) as flat_discount
                            , revenue_discount_c as revenue_discount
                            , volume_discount_c as volume_discount_name
                            , rack_rate_card_c as rack_rate_card
                            , international_rate_card_c as international_rate_card
                            , intracity_flat_rate_c as intracity_flat_rate
                            , cast(credit_terms_days_c as int) as credit_terms_days

                            , case
                                when cash_on_delivery_cod_c = 'Yes' then 1
                                when cash_on_delivery_cod_c = 'No' then 0
                                else null
                            end as cod_flag

                            , cod_fee_c as cod_fee
                            , cod_minimum_fee_c as cod_minimum_fee
                            , cod_remittance_frequency_c as cod_remittance_frequency
                            , insurance_fee_c as insurance_fee
                            , insurance_c as insurance
                            , insurance_minimum_fee_c as insurance_minimum_fee
                            , max_insurance_amount_c as max_insurance_amount
                            , rts_delivery_c as rts_delivery_percent
                            , cast(custom_pricing_script_id_c as int) as custom_pricing_script_id
                            , cast(customisation_id_c as int) as customisation_id
                            , bank_name_c as bank_name
                            , bank_account_owner_c as bank_account_owner
                            , rekening_rekonsiliasi_cod_c as rekening_rekonsiliasi_cod
                            , email_rekonsiliasi_cod_c as email_rekonsiliasi_cod
                            , cast(re_contract_c as int) as is_recontracted
                            , cast(migrated_c as int) as is_migrated
                            , industry_c as industry
                            , grouping_c as grouping
                            , business_type_c as business_type
                            , cast(vol_month_c as int) as expected_volume_monthly
                            , notes_c as notes
                            , promo_code_c as promo_code
                            , cod_volume_c as cod_volume
                            , cod_offset_type_c as cod_offset_type
                            , average_goods_price_c as average_goods_price
                            , asp_c as asp
                            , blended_asp_c as blended_asp
                            , date(requirements_gathering_date_c) as requirements_gathering_date
                            , date(quotation_sent_date_c) as quotation_sent_date
                            , date(revising_quotation_date_c) as revising_quotation_date
                            , date(contract_sent_date_c) as contract_sent_date
                            , date(onboarding_date_c) as onboarded_date
                            , date(closed_lost_date_c) as lost_date
                            , date(closed_won_date_c) as won_date
                            , date(close_date) as expected_closed_date
                            , date(new_date_c) as new_date
                            , date(agreed_to_ship_date_c) as agreed_to_ship_date
                            , date(future_opportunity_date_c) as future_opportunity_date
                            , date(negotiation_date_c) as negotiation_date
                            , date(pitching_date_c) as pitching_date
                            , date(proposal_submitted_date_c) as proposal_submitted_date
                            , date(ready_to_ship_date_c) as ready_to_ship_date
                            , date(coalesce(closed_lost_date_c, closed_won_date_c)) as completion_date
                            , date(last_activity_date_kpi_c) as last_activity_date
                            , date(future_oppty_next_follow_up_call_date_c) as future_oppty_next_follow_up_call_date
                            , status_changed_datetime
                            , order_placed_pending_procurement_date_c as order_placed_pending_procurement_date
                            , stage_name as stage
                            , coalesce(
                                datediff(measurement_datetime, status_changed_datetime)
                                , datediff(measurement_datetime, requirements_gathering_date_c)
                                , datediff(measurement_datetime, new_date_c)
                            ) as stage_duration
                            , case
                                when stage_name = 'Closed Won'
                                    then datediff(closed_won_date_c, creation_datetime)
                                when stage_name = 'Closed Lost'
                                    then datediff(closed_lost_date_c, creation_datetime)
                                else datediff(measurement_datetime, creation_datetime)
                            end as age
                            , datediff(closed_won_date_c, requirements_gathering_date_c) as lifecycle
                            , contract_type_c as contract_type
                            , freight_mode_c as freight_mode
                            , shipping_requirements_c as shipping_requirements
                            , freight_forwarding_services_c as freight_forwarding_services
                            , po_number_c as po_number
                            -- Added a case when statement to convert the 'None' to 'none' because the nullified function converts all 'None' strings into null
                            , CASE 
                                WHEN competitor_c = 'None' THEN 'none'
                                ELSE competitor_c
                              END as competitor
                            , latest_agreed_to_ship_c as latest_agreed_to_ship
                            , interested_services_c as interested_services
                            , billing_weight_logic_c as billing_weight_logic
                            , rts_delivery_percentage_c as rts_delivery_percentage
                            , tax_offset_cod_fee_c as tax_offset_cod_fee
                            , tax_offset_cod_minimum_fee_c as tax_offset_cod_minimum_fee
                            , tax_offset_insurance_fee_c as tax_offset_insurance_fee
                            , tax_offset_insurance_minimum_fee_c as tax_offset_insurance_minimum_fee
                            , myph_international_rate_card_c as myph_international_rate_card
                            , myph_international_cpsid_c as myph_international_cpsid
                            , mysg_international_rate_card_c as mysg_international_rate_card
                            , mysg_international_cpsid_c as mysg_international_cpsid
                            , creation_datetime
                            , last_modified_datetime
                            , system_id as country
                            , system_id
                            , created_month
                        from base

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("record_type.system_id"),
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_OPPORTUNITY_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
