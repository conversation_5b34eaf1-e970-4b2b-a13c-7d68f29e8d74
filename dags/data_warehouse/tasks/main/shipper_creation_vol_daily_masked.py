import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_CREATION_VOL_DAILY_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_CREATION_VOL_DAILY_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT orders.system_id AS country
                       , date(orders.creation_datetime) AS creation_date
                       , shippers.id AS shipper_id
                       , shippers.shipper_name
                       , shippers.parent_id
                       , shippers.parent_name
                       , shippers.parent_id_coalesce
                       , shippers.parent_name_coalesce
                       , orders.system_id
                       , DATE_FORMAT(orders.creation_datetime, 'yyyy-MM') AS created_month
                       , count(1) AS total_orders
                {%- for status in statuses %}
                       , count_if(orders.status_enriched = '{{ status }}') AS {{ status }}_orders
                {%- endfor %}
                       , count_if(orders.ninjapack_flag = 1) AS ninjapack_orders
                       , count_if(orders.is_pickup_required = 1) AS pickup_required_orders
                       , count_if(orders.cod_value is not null) AS cod_orders
                FROM order_milestones AS orders
                LEFT JOIN shipper_attributes AS shippers ON shippers.id = orders.shipper_id
                AND shippers.system_id = orders.system_id
                WHERE date(orders.creation_datetime)
                      < date(from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }}))
                GROUP BY {{ range(1, 11) | join(',') }}
                """,
                jinja_arguments={
                    "statuses": (
                        "pending",
                        "returned_to_sender",
                        "cancelled",
                        "on_hold",
                        "transit",
                        "completed",
                        "exception",
                    ),
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("orders.system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_CREATION_VOL_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
