import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SORT_COMPLIANCE_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SORT_COMPLIANCE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, 
            task_id=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, 
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, 
            task_id=data_warehouse.HubsDAG.Task.PARCEL_MEASUREMENT_SCAN_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED,
        ),
        base.DependsOnExternal( 
            dag_id=data_warehouse.OrdersDAG.DAG_ID, 
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),

    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS,
                view_name="dim_weight_scans",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS,
                view_name="update_address_verification_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARCEL_MEASUREMENT_SCAN_ENRICHED,
                view_name="parcel_measurement_scan_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with pre_base as (
                
                        (select order_id from inbound_scans where type = 2)
                        union distinct
                        (select order_id from warehouse_sweeps)

                    ) 
                    , base as (

                        select
                            order_milestones.system_id
                            , order_milestones.order_id
                            , order_milestones.tracking_id
                            , order_milestones.creation_datetime as order_creation_datetime
                            , order_milestones.dest_hub_id
                            , destination_hub.name as dest_hub_name
                            , order_milestones.inbound_hub_id
                            , inbound_hub.name as inbound_hub_name
                            , inbound_hub.sort_hub_flag as inbound_sort_hub_flag
                            , order_milestones.rts_flag
                            , order_milestones.inbound_datetime
                        from pre_base
                        left join order_milestones 
                            on pre_base.order_id = order_milestones.order_id
                        left join shippers_enriched
                            on order_milestones.shipper_id = shippers_enriched.id
                        left join hubs_enriched as inbound_hub
                            on order_milestones.inbound_hub_id = inbound_hub.id
                        left join hubs_enriched as destination_hub
                            on order_milestones.dest_hub_id = destination_hub.id
                        where shippers_enriched.sales_channel != 'Test'
                            and order_milestones.inbound_datetime >= date('2022-02-01')
                            and (order_milestones.inbound_datetime < cast('2022-03-30 12:00:00' as timestamp)
                                or order_milestones.inbound_datetime > cast('2022-04-01 02:30:00' as timestamp))

                    )
                    , joined as (

                        select
                            base.system_id
                            , base.order_id
                            , base.tracking_id
                            , base.order_creation_datetime
                            , base.dest_hub_id
                            , base.dest_hub_name
                            , base.inbound_hub_id
                            , base.inbound_hub_name
                            , base.inbound_sort_hub_flag
                            , base.rts_flag
                            , base.inbound_datetime
                            , min(update_address_verification_events.event_datetime) as av_datetime
                            , min_by(update_address_verification_events.av_status,update_address_verification_events.event_datetime) as av_status
                            , min_by(update_address_verification_events.av_source,update_address_verification_events.event_datetime) as av_source
                            , min_by(update_address_verification_events.av_mode,update_address_verification_events.event_datetime) as av_mode
                        from base
                        left join update_address_verification_events
                            on base.order_id = update_address_verification_events.order_id
                            and update_address_verification_events.av_status = 'VERIFIED'
                        group by {{ range(1, 12) | join(',') }}

                    )
                    , dim_weight_scans_distinct as (
                    
                        select distinct
                            order_id
                        from dim_weight_scans
                        where 
                            dim_weight_scans.user_id != 1577702
                            and dim_weight_scans.device_type = 'DWS'
                    
                    )
                    , manual_parcel_images_distinct as (
                    
                        select distinct
                            tracking_id
                        from parcel_measurement_scan_enriched
                        where 
                            parcel_measurement_scan_enriched.height_image_link_flag = 1
                            or parcel_measurement_scan_enriched.width_image_link_flag = 1 
                            or parcel_measurement_scan_enriched.length_image_link_flag = 1 
                            or parcel_measurement_scan_enriched.weight_image_link_flag = 1
                    
                    )
                    , prefinal as (

                        select
                            joined.system_id
                            , joined.order_id
                            , joined.tracking_id
                            , joined.order_creation_datetime
                            , joined.rts_flag
                            , joined.dest_hub_id
                            , joined.dest_hub_name
                            , joined.inbound_hub_id
                            , joined.inbound_hub_name
                            , joined.inbound_sort_hub_flag
                            , joined.inbound_datetime
                            , joined.av_datetime
                            , joined.av_status
                            , joined.av_source
                            , joined.av_mode
                            , if(joined.inbound_datetime >= joined.av_datetime,1,0) as av_before_inbound_flag
                            , if(dim_weight_scans_distinct.order_id is null, 0,1) as parcel_dws_flag
                            , if(manual_parcel_images_distinct.tracking_id is null, 0,1) as parcel_manual_photo_flag
                            , (to_unix_timestamp(joined.av_datetime, 'yyyy-MM-dd hh:mm:ss')-to_unix_timestamp(joined.order_creation_datetime, 'yyyy-MM-dd hh:mm:ss')) as oc_to_av_sec
                            , date_format(order_creation_datetime, 'yyyy-MM') AS created_month
                        from joined
                        left join dim_weight_scans_distinct
                            on joined.order_id = dim_weight_scans_distinct.order_id
                        left join manual_parcel_images_distinct
                            on joined.tracking_id = manual_parcel_images_distinct.tracking_id

                    )
                    , final as (

                        select
                            *
                            , oc_to_av_sec/cast(60 as double) as oc_to_av_min
                            , oc_to_av_sec/cast(3600 as double) as oc_to_av_hrs
                            , oc_to_av_sec/cast(86400 as double) as oc_to_av_days
                        from prefinal

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                    "measurement_datetime": measurement_datetime,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SORT_COMPLIANCE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
