import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ID_MANUAL_AV_LEADTIME_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ID_MANUAL_AV_LEADTIME_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS,
                view_name="update_address_verification_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with av_base as (

                    select
                        order_id
                        , av_source
                        , user_name as av_user_name
                        , user_email as av_user_email
                        , case
                            when av_source in ('BULK_AV', 'MANUAL_AV') then 'manual'
                            when av_source in ('AUTO_AV', 'FROM_SHIPPER') then 'verified'
                        end as av_type
                        , event_datetime as av_datetime
                    from update_address_verification_events
                    where
                        av_source in ('BULK_AV', 'MANUAL_AV')
                        or (av_source in ('AUTO_AV', 'FROM_SHIPPER') and av_status = 'VERIFIED')

                )

                , add_av_time as (
                    
                    select
                        base.created_month
                        , base.order_id
                        , base.tracking_id
                        , base.creation_datetime
                        , base.rts_trigger_datetime
                        , min(av_base.av_datetime) filter(where
                            av_type = 'verified'
                            and av_base.av_datetime >= base.creation_datetime
                            and av_base.av_datetime < coalesce(
                                base.rts_trigger_datetime
                                , from_utc_timestamp('{{ measurement_datetime }}', '{{ get_local_timezone }}')
                            )
                        ) as creation_auto_success_datetime
                    {%- for col in columns_to_fetch %}
                        , min_by(av_base.{{ col }}, av_base.av_datetime) filter(where
                            av_type = 'manual'
                            and av_base.av_datetime > base.creation_datetime
                            and av_base.av_datetime < coalesce(
                                base.rts_trigger_datetime
                                , from_utc_timestamp('{{ measurement_datetime }}', '{{ get_local_timezone }}')
                            )
                        ) as creation_manual_{{ col }}
                    {%- endfor %}
                        , min(av_base.av_datetime) filter(where 
                            av_type = 'verified' and av_base.av_datetime >= base.rts_trigger_datetime
                        ) as rts_auto_success_datetime
                    {%- for col in columns_to_fetch %}
                        , min_by(av_base.{{ col }}, av_base.av_datetime) filter(where
                            av_type = 'manual'
                            and av_base.av_datetime > base.rts_trigger_datetime
                        ) as rts_manual_{{ col }}
                    {%- endfor %}
                    from order_milestones as base
                    left join av_base
                        on base.order_id = av_base.order_id
                    group by {{ range(1, 6) | join(',') }}

                )

                , filter_success as (
                
                    select
                        created_month
                        , order_id
                        , tracking_id
                        , creation_datetime
                        , rts_trigger_datetime
                    {%- for type in ('creation', 'rts') %}
                        {%- for col in columns_to_fetch %}
                        , if({{ type }}_manual_av_datetime < {{ type }}_auto_success_datetime 
                            or {{ type }}_auto_success_datetime is null
                            , {{ type }}_manual_{{ col }}
                            , null
                        ) as {{ type }}_manual_{{ col }}
                        {%- endfor %}
                    {%- endfor %}
                    from add_av_time

                )

                , hour_difference as (
                
                    select
                        *
                        , (to_unix_timestamp(creation_manual_av_datetime)
                            - to_unix_timestamp(creation_datetime))/3600 as creation_av_hour_diff
                        , (to_unix_timestamp(rts_manual_av_datetime)
                            - to_unix_timestamp(rts_trigger_datetime))/3600 as rts_av_hour_diff
                    from filter_success

                )

                select
                    created_month
                    , order_id
                    , tracking_id
                    , creation_datetime
                    {%- for col in columns_to_fetch %}
                    , creation_manual_{{ col }}
                    {%- endfor %}
                    , creation_av_hour_diff
                    , rts_trigger_datetime
                    {%- for col in columns_to_fetch %}
                    , rts_manual_{{ col }}
                    {%- endfor %}
                    , rts_av_hour_diff
                from hour_difference
                where
                    creation_manual_av_datetime is not null
                    or rts_manual_av_datetime is not null

                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                    "columns_to_fetch":(
                        "av_source",
                        "av_user_name",
                        "av_user_email",
                        "av_datetime",
                    ),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_MANUAL_AV_LEADTIME,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
