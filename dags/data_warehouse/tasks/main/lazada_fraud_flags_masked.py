import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON>ta

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.LAZADA_FRAUD_FLAGS_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.LAZADA_FRAUD_FLAGS_MASKED,
    system_ids=(SystemID.ID,),
    depends_on=(
        data_warehouse.SLABreachDAG.Task.CONSIGNEE_EMAIL_DOMAIN_REPORT_MASKED,
        data_warehouse.SLABreachDAG.Task.DWS_VARIANCE_REPORT_MASKED,
        data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_MASKED,
        data_warehouse.SLABreachDAG.Task.KNOWN_FRAUD_SHIPPER_REPORT_MASKED,
        data_warehouse.SLABreachDAG.Task.NAMES_PER_CONSIGNEE_EMAIL_REPORT_MASKED
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).CONSIGNEE_EMAIL_DOMAIN_REPORT,
                system_id=system_id,
                view_name="consignee_email_domain_report"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DWS_VARIANCE_REPORT,
                system_id=system_id,
                view_name="dws_variance_report"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HIGH_COD_PRICE_SCRAPPING_REPORT,
                system_id=system_id,
                view_name="high_cod_price_scrapping_report",
                version_datetime="latest",

            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).KNOWN_FRAUD_SHIPPER_REPORT,
                system_id=system_id,
                view_name="known_fraud_shipper_report"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS,
                system_id=system_id,
                view_name="lazada_orders"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).NAMES_PER_CONSIGNEE_EMAIL_REPORT,
                system_id=system_id,
                view_name="names_per_consignee_email_report"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="high_cod_price_scrapping_report",
                jinja_template="""

                -- Flag parcels with suspicious prices from 12th June

                select
                    order_id
                    , created_at
                    , suspicious_score
                    , if(suspicious_score = -1,null,price_match_results) as price_match_results
                    , case
                        when suspicious_score >= 0.9 then 1
                        when suspicious_score >= 0 and suspicious_score < 0.9 then 0
                        else null
                    end as reasonable_price_flag
                from high_cod_price_scrapping_report
                where date(created_at) >= date('2023-06-12')

                """,
            ),
            base.TransformView(
                view_name="flags_combined",
                jinja_template="""

                -- Combine all fraud flags into 1 table

                (
                    select
                        order_id
                        , consignee_email_domain_first_flag_timestamp as flag_timestamp
                    from consignee_email_domain_report
                    where consignee_email_domain_first_flag_timestamp is not null
                )

                union

                (
                    select
                        order_id
                        , dws_variance_first_flag_timestamp as flag_timestamp
                    from dws_variance_report
                    where dws_variance_first_flag_timestamp is not null
                )
                union

                (
                    select
                        order_id
                        , creation_datetime as flag_timestamp
                    from known_fraud_shipper_report
                    where known_fraud_shipper_flag = 1
                )

                union

                (
                    select
                        order_id
                        , created_at as flag_timestamp
                    from high_cod_price_scrapping_report
                    where reasonable_price_flag = 1
                )

                union

                (
                    select
                        order_id
                        , creation_datetime as flag_timestamp
                    from names_per_consignee_email_report
                    where sus_consignee_flag = 1
                )

                """,
            ),
            base.TransformView(
                view_name="first_flag",
                jinja_template="""

                -- Get first flag timestamp of each order

                select
                    order_id
                    , min(flag_timestamp) as first_flag_timestamp
                from flags_combined
                group by 1

                """,
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""

                select
                    lazada_orders.order_id
                    , lazada_orders.lazmall_flag
                    , lazada_orders.cod_value
                    , first_flag.first_flag_timestamp
                    , consignee_email_domain_report.consignee_email_domain_first_flag_timestamp
                    , consignee_email_domain_report.consignee_email_domain_first_value
                    , consignee_email_domain_report.consignee_email_domain_current_flag
                    , consignee_email_domain_report.consignee_email_domain_current_value
                    , dws_variance_report.dws_variance_first_flag_timestamp
                    , dws_variance_report.dws_variance_first_dws_value
                    , dws_variance_report.dws_variance_current_flag
                    , dws_variance_report.dws_variance_current_dws_value
                    , known_fraud_shipper_report.laz_shipper_status
                    , high_cod_price_scrapping_report.price_match_results
                    , high_cod_price_scrapping_report.suspicious_score
                    , high_cod_price_scrapping_report.reasonable_price_flag
                    , known_fraud_shipper_report.known_fraud_shipper_flag
                    , names_per_consignee_email_report.sus_consignee_flag
                    , lazada_orders.created_month
                    , lazada_orders.system_id
                from lazada_orders
                left join first_flag
                    on lazada_orders.order_id = first_flag.order_id
                left join consignee_email_domain_report
                    on lazada_orders.order_id = consignee_email_domain_report.order_id
                left join dws_variance_report
                    on lazada_orders.order_id = dws_variance_report.order_id
                left join high_cod_price_scrapping_report
                    on lazada_orders.order_id = high_cod_price_scrapping_report.order_id
                left join known_fraud_shipper_report
                    on lazada_orders.order_id = known_fraud_shipper_report.order_id
                left join names_per_consignee_email_report
                    on lazada_orders.order_id = names_per_consignee_email_report.order_id

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_FRAUD_FLAGS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()