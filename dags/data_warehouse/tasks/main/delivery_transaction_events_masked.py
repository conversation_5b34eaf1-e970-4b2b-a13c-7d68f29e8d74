import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrderEventsDAG.Task.DELIVERY_FAILURE_EVENTS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_RTS_TRIGGERS_MASKED
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_RTS_TRIGGERS,
                view_name="order_rts_triggers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_FAILURE_EVENTS,
                view_name="delivery_failure_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.OrderProdGL(input_env, is_masked).MULTI_PIECE_SHIPMENTS,
                view_name="multi_piece_shipments",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.OrderProdGL(input_env, is_masked).MULTI_PIECE_SHIPMENT_ORDERS,
                view_name="multi_piece_shipment_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="last_failure_reason",
                jinja_template="""
                select
                    transaction_id
                    , failure_reason_id
                    , failure_reason_code_id
                from (
                    select
                        transaction_id
                        , failure_reason_id
                        , failure_reason_code_id
                        , row_number() over (
                            partition by transaction_id
                            order by created_at
                        ) as rank
                    from transaction_failure_reason
                    where deleted_at is null
                )
                where rank = 1
                """,
            ),
            base.TransformView(
                view_name="delivery_failure_events_ranked",
                jinja_template="""
                select
                    delivery_failure_events.order_id
                    , delivery_failure_events.event_datetime
                    , delivery_failure_events.failure_reason_id
                    , failure_reasons.failure_reason_code_id
                    , row_number() over(
                        partition by delivery_failure_events.order_id 
                        order by delivery_failure_events.event_datetime
                    ) as delivery_failure_rank
                from delivery_failure_events
                left join failure_reasons
                    on delivery_failure_events.failure_reason_id = failure_reasons.id
                """,
            ),
            base.TransformView(
                view_name="adjusted_transactions",
                jinja_template="""
                with dd_transaction_filtered as (

                    select
                        transactions.id
                        , transactions.order_id
                        , transactions.status
                        , transactions.postcode
                        , transactions.waypoint_id
                        , transactions.route_id
                        , transactions.transit
                        , transactions.distribution_point_id
                        , transactions.sla_date
                        , transactions.start_time
                        , transactions.end_time
                        , transactions.service_end_time
                        , transactions.created_at
                        , transactions.updated_at
                        , row_number() over(
                            partition by transactions.order_id, transactions.status
                            order by transactions.service_end_time
                        ) as transaction_status_rank
                    from transactions
                    where
                        transactions.type = 'DD'
                        and transactions.deleted_at is null

                )

                select
                    base.*
                {%- if system_id == 'th' %}
                    , coalesce(
                        last_fr.failure_reason_id, failure_events.failure_reason_id
                    ) as failure_reason_id
                    , coalesce(
                        last_fr.failure_reason_code_id, failure_events.failure_reason_code_id
                    ) as failure_reason_code_id
                {%- else %}
                    , last_fr.failure_reason_id
                    , last_fr.failure_reason_code_id
                {%- endif %}
                from dd_transaction_filtered as base
                left join last_failure_reason as last_fr
                    on base.id = last_fr.transaction_id
                {%- if system_id == 'th' %}
                left join delivery_failure_events_ranked as failure_events
                    on base.order_id = failure_events.order_id
                    and base.transaction_status_rank = failure_events.delivery_failure_rank
                    and base.status = 'Fail'
                {%- endif %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="b2b_bundle_order_base",
                jinja_template="""
                select
                    multi_piece_shipment_orders.mps_id as bundle_id
                    , multi_piece_shipments.tracking_number as bundle_tracking_id
                    , multi_piece_shipment_orders.order_id
                from multi_piece_shipment_orders
                left join multi_piece_shipments
                    on multi_piece_shipment_orders.mps_id = multi_piece_shipments.id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    transactions.id as transaction_id
                    , transactions.order_id
                    , transactions.status
                    , transactions.postcode
                    , cast(transactions.transit as bigint) as transit_flag
                    , transactions.failure_reason_id
                    , from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}') as event_datetime
                    , cast(
                        case
                            when transactions.service_end_time is null
                            or transactions.status not in ('Success', 'Forced Success', 'Fail')
                                then null
                            when transactions.status = 'Fail'
                            and (transactions.failure_reason_code_id in (5, 6)
                            or (transactions.failure_reason_code_id = 13
                            and date(
                                from_utc_timestamp(transactions.created_at, '{{ local_timezone }}')) >= '2021-10-18')
                            )
                                then 0
                            else 1
                        end as bigint) as valid_flag
                    , case
                        when transactions.service_end_time is null then
                            case
                                when transactions.status in ('Pending', 'Cancelled')
                                and from_utc_timestamp(coalesce(transactions.updated_at,
                                    transactions.created_at), '{{ local_timezone }}')
                                >= rts.event_datetime - interval 5 minute
                                    then 'rts'
                                when from_utc_timestamp(transactions.created_at, '{{ local_timezone }}')
                                >= rts.event_datetime - interval 5 minute
                                    then 'rts'
                                else 'delivery'
                            end
                        -- TODO: review the need to + interval 5 minute to rts.event_datetime. Consider removal of the buffer. 
                        -- Causes issues when SG RTS and force success immediately for cross border parcels
                        when transactions.service_end_time is not null then
                            case
                                when from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')
                                >= rts.event_datetime + interval 5 minute
                                    then 'rts'
                                else 'delivery'
                            end
                    end as type
                    , date_format(from_utc_timestamp(transactions.start_time, '{{ local_timezone }}'), 'HH:mm:ss')
                        as timeslot_start
                    , date_format(from_utc_timestamp(transactions.end_time, '{{ local_timezone }}'), 'HH:mm:ss')
                        as timeslot_end
                    , from_utc_timestamp(transactions.sla_date, '{{ local_timezone }}') as sla_datetime
                    , transactions.route_id
                    , cast(route.hub_id as bigint) as route_hub_id
                    , cast(route.driver_id as bigint) as route_driver_id
                    , route_zone.name as route_zone
                    , route_zone.short_name as route_zone_short_name
                    , cast(transactions.waypoint_id as bigint) as waypoint_id
                    , waypoints.timewindow_id as waypoint_timewindow_id
                    , waypoints.latitude as waypoint_latitude
                    , waypoints.longitude as waypoint_longitude
                    , waypoint_zone.legacy_zone_id
                    , waypoint_zone.name as waypoint_zone
                    , waypoint_zone.short_name as waypoint_zone_short_name
                    , waypoint_zone.hub_id as waypoint_zone_hub_id
                    , cast({{ dest_hub_id[system_id] }} as bigint) as dest_hub_id
                    , {{ dest_zone[system_id] }} as dest_zone
                    , transactions.distribution_point_id as dpms_id
                    , b2b_bundle_order_base.bundle_tracking_id
                    , from_utc_timestamp(transactions.created_at, '{{ local_timezone }}') as creation_datetime
                    , date_format(transactions.created_at, 'yyyy-MM') as created_month
                from adjusted_transactions as transactions
                left join order_rts_triggers as rts on
                    rts.order_id = transactions.order_id
                left join route_logs as route on
                    route.legacy_id = transactions.route_id
                    and route.system_id = '{{ system_id }}'
                left join waypoints on
                    waypoints.legacy_id = transactions.waypoint_id
                    and waypoints.system_id = '{{ system_id }}'
                left join zones as waypoint_zone on
                    waypoints.routing_zone_id = waypoint_zone.legacy_zone_id
                    and lower(waypoint_zone.system_id) = '{{ system_id }}'
                left join zones as route_zone on
                    route.zone_id = route_zone.legacy_zone_id
                    and lower(route_zone.system_id) = '{{ system_id }}'
                left join b2b_bundle_order_base
                    on transactions.order_id = b2b_bundle_order_base.order_id
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                    "dest_zone": {
                        constants.SystemID.ID: "coalesce(route_zone.name, waypoint_zone.name)",
                        constants.SystemID.MM: "coalesce(route_zone.name, waypoint_zone.name)",
                        constants.SystemID.MY: "coalesce(route_zone.name, waypoint_zone.name)",
                        constants.SystemID.PH: "coalesce(route_zone.name, waypoint_zone.name)",
                        constants.SystemID.SG: "coalesce(waypoint_zone.name, route_zone.name)",
                        constants.SystemID.TH: "coalesce(route_zone.name, waypoint_zone.name)",
                        constants.SystemID.VN: "coalesce(waypoint_zone.name, route_zone.name)",
                    },
                    "dest_hub_id": {    
                        constants.SystemID.ID: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                        constants.SystemID.MM: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                        constants.SystemID.MY: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                        constants.SystemID.PH: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                        constants.SystemID.SG: "coalesce(waypoint_zone.hub_id, route.hub_id)",
                        constants.SystemID.TH: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                        constants.SystemID.VN: "coalesce(route.hub_id, waypoint_zone.hub_id)",
                    },
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DELIVERY_TRANSACTION_EVENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()