import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookDAG.Task.PARTNERSHIP_PREFIX_MASKED + ".py",
    task_name=data_warehouse.WebhookDAG.Task.PARTNERSHIP_PREFIX_MASKED,
    system_ids=(SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_PREFIXES,
                            view_name="shipper_prefixes"),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="prefix",
                jinja_template="""

                with 
                    base as (
                        select 
                            shipper_id 
                            , prefix
                            , case 
                            {%- for country, item in prefix_groupings.items() %}
                                {%- for shipper, master_acc in item.items() %}
                                    when shipper_id in ({{ master_acc | join(",") }}) then '{{country}}'
                                {%- endfor %}
                            {%- endfor %}
                            end as system_id
                            , case 
                            {%- for country, item in prefix_groupings.items() %}
                                {%- for shipper, master_acc in item.items() %}
                                    when shipper_id in ({{ master_acc | join(",") }}) then '{{shipper}}'
                                {%- endfor %}
                            {%- endfor %}
                            end as shipper
                        from shipper_prefixes 
                        where 
                            -- Filter Orders by Master Acc Shipper IDs 
                            shipper_id in (
                                    {%- for country, item in prefix_groupings.items() %}
                                        {%- for shipper, master_acc in item.items() %}
                                           {{ master_acc | join(",") }}{% if not loop.last %},{% endif %}
                                        {%- endfor %}{% if not loop.last %},{% endif %}
                                    {%- endfor %}
                                    )
                )
                
                    select 
                        prefix as prefixes
                        , shipper
                        , system_id
                    from base
                    where 
                        -- Filter out empty values
                        prefix is not null 
                        and prefix != ''
                """,
                jinja_arguments={
                    "prefix_groupings": {
                        "sg": {
                            "Tiktok Domestic": (10853366,),
                            "Tiktok XB": (7314835,),
                            "Tiktok XB LM" : (10761085,),
                            "Lazada Domestic": (109059, 291101, 283987),
                            "Cainiao" : (9416905,),
                        },
                        "id": {
                            "Tiktok Domestic": (7474545,),
                            "Lazada Domestic": (341107, 341121),
                        },
                        "my": {
                            "Tiktok Domestic": (7717788,),
                            "Tiktok XB": (7314875,),
                            "Lazada Domestic": (332853, 473668, 324763, 329449),
                            "Tiktok XB LM" : (9717974,),
                            "Cainiao" : (9880902,9893295),
                        },
                        "ph": {
                            "Tiktok Domestic": (7823651,),
                            "Tiktok XB": (7314943,),
                            "Lazada Domestic": (79238, 341153, 341159, 931874, 9973670),
                            "Tiktok XB LM" : (10416620,),
                        },
                        "vn": {
                            "Tiktok Domestic": (9090233,),
                            "Tiktok XB": (7314925,),
                            "Tiktok XB LM" : (10416641,),
                        }
                    },
                },
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_PREFIX,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()