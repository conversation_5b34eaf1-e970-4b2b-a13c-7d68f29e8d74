import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_DAILY_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_DAILY_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_MASKED,),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CISP_PRIOR_REPORT,
                view_name="cisp_prior_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    working_start_clock_date as report_date
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , dest_hub_zone
                    , prior_tagging
                    , pets_open_flag
                    , salesforce_flag
                    , SUM(attempt_measured_flag) as attempt_measured
                    , SUM(attempt_met_flag) as attempt_met
                    , SUM(pets_damaged_flag) as damaged_parcels_count
                    , SUM(pets_parcel_on_hold_flag) as on_hold_parcels_count
                    , SUM(pets_missing_flag) as missing_parcels_count
                    , SUM(pets_shipper_issue_flag) as shipper_issue_parcels_count
                    , SUM(pets_self_collection_flag) as self_collection_parcels_count
                    , SUM(pets_parcel_exception_flag) as parcel_exception_parcels_count
                    , system_id
                    , created_month
                from cisp_prior_report
                GROUP BY 1,2,3,4,5,6,7,8,17,18
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_PRIOR_REPORT_DAILY,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
