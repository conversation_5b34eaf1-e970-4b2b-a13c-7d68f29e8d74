import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_ORDERS_BASE_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_ORDERS_BASE_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.LAZADA_INTERCEPTED_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORIGINAL_CONSIGNEE_INFORMATION_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_SLA_DAYS,
                view_name="shipper_sla_days",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORIGINAL_CONSIGNEE_INFORMATION,
                view_name="original_consignee_information",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_INTERCEPTED_ORDERS,
                view_name="lazada_intercepted_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HIGH_COD_PRICE_SCRAPPING_REPORT,
                view_name="high_cod_price_scrapping_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="cache_price_scrapping",
                jinja_template="""

                cache table high_cod_price_scrapping_report

                """,
            ),
            base.TransformView(
                view_name="orders_claimed",
                jinja_template="""

                select
                    order_id
                    , min(resolution_datetime) as resolution_datetime
                from pets_tickets_enriched
                where
                    status = 'RESOLVED'
                    and type = 'SLA BREACH'
                    and (outcome like '%NV LIABLE%' or outcome = 'RESUME DELIVERY')
                group by 1

                """,
            ),
            base.TransformView(
                view_name="sla",
                jinja_template="""

                select
                    origin_config
                    , dest_config
                    , min(sla_days) as sla_days
                    , min(rts_sla_days) as rts_sla_days
                from shipper_sla_days
                where
                    -- Get Lazada sla only
                    shipper_config = 'lazada'
                    and sla_type = 'service'
                    and origin_dest_config_type = 'l1l3'

                    -- Ensure the sla is still valid at the time of task run
                    and end_datetime > '{{measurement_datetime}}'
                group by 1,2

                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
            base.TransformView(
                view_name="orders_filtered",
                jinja_template="""

                select
                    oms.order_id
                    , oms.tracking_id
                    , oms.creation_datetime
                    , date(oms.creation_datetime) as created_date
                    , oms.granular_status
                    , oms.shipper_id
                    , se.shipper_name
                    , oms.seller_id
                    , oms.seller_name
                    , oms.items
                    , length(oms.items) as item_length
                    , oms.insurance_value
                    , oms.cod_value
                    , if(
                        greatest(oms.insurance_value,oms.cod_value) >= 5000000
                        , 5000000
                        , greatest(oms.insurance_value,oms.cod_value)
                    ) as potential_claim_value
                    , oms.insurance_value_sgd
                    , oms.cod_value_sgd
                    , case 
                        when oms.insurance_value_sgd is null or oms.cod_value_sgd is null then null
                        when oms.cod_value = oms.insurance_value then 0
                        when oms.cod_value_sgd > oms.insurance_value_sgd then oms.cod_value_sgd / oms.insurance_value_sgd
                        when oms.cod_value_sgd < oms.insurance_value_sgd then oms.insurance_value_sgd / oms.cod_value_sgd
                    end as cod_insurance_discrepancy_factor
                    , oms.parcel_size
                    , oms.nv_width
                    , oms.nv_height
                    , oms.nv_length
                    , oms.original_weight
                    , oms.nv_weight
                    , oms.inbound_datetime
                    , date(oms.inbound_datetime) as inbound_date
                    , oms.pickup_datetime
                    , if(oms.first_pickup_attempt_datetime is not null, 1, 0) as pu_scan_flag
                    , hour(oms.pickup_datetime) as pickup_hour
                    , (unix_timestamp(oms.pickup_datetime) - unix_timestamp(oms.creation_datetime)) / 3600 as oc_to_pu_hours
                    , (unix_timestamp(oms.inbound_datetime) - unix_timestamp(oms.creation_datetime)) / 3600 as oc_to_ib_hours

                    -- Take pickup hub or inbound hub whichever is earlier as the origin hub
                    , case
                        when oms.pickup_datetime is null and oms.inbound_datetime is null then null
                        when oms.pickup_datetime is null then oms.inbound_hub_id
                        when oms.inbound_datetime is null then oms.pickup_hub_id
                        when oms.pickup_datetime < oms.inbound_datetime then pickup_hub_id
                        else oms.inbound_hub_id
                    end as origin_hub_id
                    , oms.dest_hub_id
                    , oms.from_l1_id
                    , oms.to_l3_id
                    , 'id' as system_id
                    , oms.created_month
                from order_milestones as oms
                left join shipper_attributes as se on
                    oms.shipper_id = se.id
                where
                    -- Process Lazada non-lazmall orders only
                    se.parent_id_coalesce = 341107
                    and oms.lazmall_flag = 0
                    and lower(oms.order_type) <> 'return'

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select distinct
                    orders.order_id
                    , orders.tracking_id
                    , orders.creation_datetime
                    , orders.created_date
                    , orders.granular_status
                    , orders.shipper_id
                    , orders.shipper_name
                    , coalesce(orders.seller_id, 'missing') as seller_id
                    , coalesce(orders.seller_name, 'missing') as seller_name
                    , coalesce(consignee.original_to_address1, 'missing') as original_to_address1
                    , coalesce(consignee.original_to_address2, 'missing') as original_to_address2
                    , concat(
                        coalesce(consignee.original_to_address1, 'missing')
                        , " "
                        , coalesce(consignee.original_to_address2, 'missing')
                    ) as original_to_address
                    , coalesce(consignee.original_to_name, 'missing') as original_to_name
                    , coalesce(consignee.original_to_email, 'missing') as original_to_email
                    , coalesce(consignee.original_to_contact, 'missing') as original_to_number
                    , orders.items
                    , orders.item_length
                    , orders.insurance_value
                    , orders.cod_value
                    , orders.potential_claim_value
                    , orders.insurance_value_sgd
                    , orders.cod_value_sgd
                    , orders.cod_insurance_discrepancy_factor
                    , orders.parcel_size
                    , orders.nv_width
                    , orders.nv_height
                    , orders.nv_length
                    , orders.original_weight
                    , orders.nv_weight
                    , orders.inbound_datetime
                    , orders.inbound_date
                    , orders.pickup_datetime
                    , orders.pu_scan_flag
                    , orders.pickup_hour
                    , orders.oc_to_pu_hours
                    , orders.oc_to_ib_hours
                    , origin_hub.id as origin_hub_id
                    , origin_hub.name as origin_hub_name
                    , origin_hub.latitude as origin_hub_latitude
                    , origin_hub.longitude as origin_hub_longitude
                    , origin_hub.region as origin_hub_region
                    , dest_hub.id as dest_hub_id
                    , dest_hub.name as dest_hub_name
                    , dest_hub.latitude as dest_hub_latitude
                    , dest_hub.longitude as dest_hub_longitude
                    , dest_hub.region as dest_hub_region
                    , sla.sla_days
                    , sla.rts_sla_days
                    , lazada_intercepted_orders.known_fraud_timestamp
                    , orders_claimed.resolution_datetime
                    , if(
                        high_cod_price_scrapping_report.suspicious_score=-1
                        ,null
                        ,high_cod_price_scrapping_report.suspicious_score
                    ) as price_suspicious_score
                    , orders.system_id
                    , orders.created_month
                from orders_filtered as orders
                left join sla on
                    orders.from_l1_id = sla.origin_config
                    and orders.to_l3_id = sla.dest_config
                left join hubs_enriched as origin_hub on 
                    orders.origin_hub_id = origin_hub.id
                left join hubs_enriched as dest_hub on 
                    orders.dest_hub_id = dest_hub.id                
                left join original_consignee_information as consignee on
                    orders.order_id = consignee.order_id         
                left join lazada_intercepted_orders on 
                    orders.order_id = lazada_intercepted_orders.order_id
                left join orders_claimed on
                    orders.order_id = orders_claimed.order_id
                left join high_cod_price_scrapping_report on
                    orders.order_id = high_cod_price_scrapping_report.order_id

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()