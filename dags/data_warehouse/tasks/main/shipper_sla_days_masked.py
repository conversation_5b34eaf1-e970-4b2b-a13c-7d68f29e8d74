import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
                           base.HiveMetastoreTaskConfig(hive_schema="id_views"),
                           base.HiveMetastoreTaskConfig(hive_schema="my_views"),
                           ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.CoreProdGL("my", input_env, is_masked).ORDER_SLA, view_name="order_sla_my"),
            base.InputTable(
                path=delta_tables.GDrive(input_env).SHIPPER_SLA_DAYS, view_name="shipper_sla_days_gdrive"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    'my' as country
                    , 'default' as shipper_config_type
                    , null as shipper_config
                    , 'billing_zone' as origin_dest_config_type
                    , from_billing_zone as origin_config
                    , to_billing_zone as dest_config
                    , 'service_type' as extra_config_type
                    , service_type as extra_config
                    , 'service' as sla_type
                    , cast(service_days as int) as sla_days
                    , cast(21 as int) as rts_sla_days
                    , 'ops_calendar' as calendar_config
                    , 'order_sla' as source
                    , '2015-01-01 00:00:00' as start_datetime
                    , '2099-01-01 00:00:00' as end_datetime
                    , 'my' as system_id
                    , date_format('2015-01-01 00:00:00', 'yyyy-MM') as created_month
                from order_sla_my
                union all
                select
                    country
                    , shipper_config_type
                    , shipper_config
                    , origin_dest_config_type
                    , origin_config
                    , dest_config
                    , extra_config_type
                    , extra_config
                    , sla_type
                    , cast(sla_days as int) as sla_days
                    , cast(rts_sla_days as int) as rts_sla_days
                    , if(calendar_config is null, 'ops_calendar', calendar_config) calendar_config
                    , 'manual' as source
                    , start_datetime
                    , end_datetime
                    , country as system_id
                    , date_format(start_datetime, 'yyyy-MM') as created_month
                from shipper_sla_days_gdrive
                where
                    is_deleted = 0
                    or is_deleted is null
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_SLA_DAYS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
