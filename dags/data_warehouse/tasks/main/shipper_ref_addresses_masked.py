import sys

from pyspark.sql import SparkSession

from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from pyspark.sql.functions import current_timestamp, date_format, monotonically_increasing_id, sha2

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from pyspark.sql import functions as F
from pyspark.sql.functions import explode, col
from pyspark.sql.types import StructField, ArrayType, StringType, StructType,BooleanType


airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SHIPPER_REF_ADDRESSES_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SHIPPER_REF_ADDRESSES_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    addr_schema = ArrayType(
        StructType([
            StructField("origin", StringType(), True),
            StructField("destination", StringType(), True),
            StructField("returnInfo", StringType(), True)
        ])
    )

    org_dest_schema = ArrayType(
        StructType([
            StructField("address", StringType(), True),
            StructField("email", StringType(), True),
            StructField("name", StringType(), True),
            StructField("phone", StringType(), True),
            StructField("geocoding", StringType(), True)
        ])
    )

    address_schema = ArrayType(
        StructType([
            StructField("city", StringType(), True),
            StructField("country", StringType(), True),
            StructField("details", StringType(), True),
            StructField("id", StringType(), True),
            StructField("province", StringType(), True),
            StructField("type", StringType(), True),
            StructField("zipCode", StringType(), True)
        ])
    )

    geo_schema = ArrayType(
        StructType([
            StructField("latitude", StringType(), True),
            StructField("longitude", StringType(), True)
        ])
    )

    # we are loading input cods table for the sake of input_config use. We will remove it in future once
    # input_config is not required
    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    end_month = measurement_datetime.strftime('%Y-%m')
    dte = dt.strptime(end_month, '%Y-%m').date()
    re = dte + relativedelta(months=-6)
    start_month = re.strftime('%Y-%m')

    orders = spark.read.format("delta").load(delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS)\
        .filter(F.col("created_month").between(start_month,end_month))\
        .select("id", "shipper_id", "tracking_id", "shipper_ref_metadata", "created_month", "created_at", "global_shipper_id")

    df_new = orders.withColumn("shipper", F.from_json("shipper_ref_metadata", addr_schema)) \
        .select("shipper", "shipper_id", "tracking_id", "id", "global_shipper_id", "created_month", "created_at")

    addr_df = df_new.select(explode("shipper").alias("shipperInfo"), "shipper_id", "id", "tracking_id",
                            "global_shipper_id", "created_month", "created_at") \
        .select("shipperInfo.origin", "shipper_id", "shipperInfo.destination", "shipperInfo.returnInfo", "id",
                "tracking_id", "global_shipper_id", "created_month", "created_at")
    org_df = addr_df.withColumn("org", F.from_json("origin", org_dest_schema)) \
        .selectExpr("inline(org)", "shipper_id", "destination", "returnInfo", "id", "tracking_id", "global_shipper_id",
                    "created_month", "created_at")
    org_new_df = org_df.select(col("name").alias("origin_name"), col("geocoding").alias("origin_geocoding"),
                               col("address").alias("origin_address"),
                               col("id").alias("order_id"), "destination", "tracking_id", "shipper_id", "returnInfo",
                               "global_shipper_id", "created_month", "created_at")
    org_addr_df = org_new_df.withColumn("org_addr", F.from_json("origin_address", address_schema)) \
        .selectExpr("inline(org_addr)", "origin_name", "tracking_id", "shipper_id", "destination",
                    "returnInfo", "order_id", "origin_geocoding", "global_shipper_id", "created_month", "created_at")
    org_addr_new_df = org_addr_df.select(col("city").alias("origin_city"), col("country").alias("origin_country"),
                                         col("id").alias("origin_id"), "tracking_id",
                                         col("province").alias("origin_province"),
                                         col("zipCode").alias("origin_zipCode"), "origin_geocoding", "origin_name", "shipper_id",
                                         "destination", "returnInfo", "order_id", "global_shipper_id", "created_month",
                                         "created_at")
    dest_df = org_addr_new_df.withColumn("dest", F.from_json("destination", org_dest_schema)) \
        .selectExpr("inline(dest)", "origin_city", "origin_country", "origin_id", "origin_province",
                    "origin_zipCode", "origin_geocoding", "origin_name", "shipper_id",
                    "returnInfo", "order_id", "global_shipper_id", "created_month", "tracking_id", "created_at")
    dest_new_df = dest_df.select(col("address").alias("destination_address"), col("name").alias("destination_name"),
                                 "origin_geocoding", "tracking_id", "origin_city", "origin_country",
                                 "origin_id", "origin_province", "origin_zipCode",
                                 "origin_name", "shipper_id", "returnInfo", "order_id",
                                 "global_shipper_id", "created_month", "created_at")
    dest_addr_df = dest_new_df.withColumn("dest_addr", F.from_json("destination_address", address_schema)) \
        .selectExpr("inline(dest_addr)", "destination_name", "tracking_id",
                    "origin_geocoding", "origin_city", "origin_country", "origin_id", "origin_province",
                    "origin_zipCode", "origin_name", "shipper_id", "returnInfo", "order_id",
                    "global_shipper_id", "created_month", "created_at")
    dest_addr_new_df = dest_addr_df.select(col("city").alias("destination_city"),
                                           col("country").alias("destination_country"),
                                           col("id").alias("destination_id"),
                                           col("province").alias("destination_province"),
                                           col("type").alias("destination_type"),
                                           col("zipCode").alias("destination_zipCode")
                                           , "destination_name", "origin_name",
                                           "origin_city", "origin_country", "origin_id",
                                           "origin_province", "origin_zipCode", "tracking_id",
                                           "shipper_id", "returnInfo","origin_geocoding",
                                           "order_id", "global_shipper_id", "created_month", "created_at")
    return_df = dest_addr_new_df.withColumn("return", F.from_json("returnInfo", org_dest_schema)) \
        .selectExpr("inline(return)", "destination_city", "destination_country", "tracking_id",
                    "destination_id", "destination_province", "destination_type", "destination_zipCode",
                    "destination_name", "origin_city", "origin_country", "origin_id", "origin_name",
                    "origin_province", "origin_zipCode", "origin_geocoding", "origin_name",
                    "shipper_id", "order_id", "global_shipper_id", "created_month", "created_at")
    return_new_df = return_df.select(col("name").alias("return_name"), col("address").alias("return_address"),
                                     col("geocoding").alias("return_geocoding"),
                                     "destination_city", "destination_country",
                                     "destination_id", "destination_province", "origin_geocoding",
                                     "destination_type", "destination_zipCode", "destination_name",
                                     "origin_city", "origin_country", "origin_id",
                                     "origin_province", "origin_zipCode", "origin_name",
                                     "tracking_id", "shipper_id", "order_id", "global_shipper_id", "created_month",
                                     "created_at")
    return_addr_df = return_new_df.withColumn("return_addr", F.from_json("return_address", address_schema)) \
        .selectExpr("inline(return_addr)", "return_name", "destination_city",
                    "destination_country", "return_geocoding", "origin_geocoding",
                    "destination_id", "destination_province", "destination_type",
                    "destination_zipCode", "destination_name", "origin_city",
                    "origin_country", "origin_id", "origin_province", "tracking_id",
                    "origin_zipCode", "origin_name", "shipper_id", "order_id",
                    "global_shipper_id", "created_month", "created_at")
    return_addr_new_df = return_addr_df.select(col("city").alias("return_city"), col("country").alias("return_country"),
                                               col("id").alias("return_id"), col("province").alias("return_province"),
                                               col("zipCode").alias("return_zipCode"), "order_id", "return_geocoding",
                                               "origin_geocoding", "return_name",
                                               "destination_city", "destination_country", "tracking_id",
                                               "destination_id", "destination_province",
                                               "destination_type", "destination_zipCode", "destination_name",
                                               "origin_city", "origin_country", "origin_id", "origin_province",
                                               "origin_zipCode", "origin_name",
                                               "shipper_id", "global_shipper_id", "created_month", "created_at")
    geo_df = return_addr_new_df.withColumn("re_geo", F.from_json("return_geocoding", geo_schema)) \
        .withColumn("org_geo", F.from_json("origin_geocoding", geo_schema)) \
        .select("order_id", "shipper_id", "global_shipper_id", "origin_city", "origin_country",
                "origin_id", "origin_province", "origin_zipCode", "origin_name", "tracking_id",
                "destination_city", "destination_country", "destination_id", "destination_province", "destination_type",
                "destination_zipCode", "destination_name", "return_city",
                "return_country", "return_id", "return_province",
                "return_zipCode", "return_name", "created_month", "created_at",
                "re_geo", "org_geo")
    re_geo_df = geo_df.select(explode("org_geo").alias("geo_org"), "order_id", "shipper_id", "global_shipper_id",
                              "origin_city", "origin_country", "origin_id", "origin_province",
                              "origin_zipCode", "origin_name", "tracking_id",
                              "destination_city", "destination_country",  "destination_id", "destination_province",
                              "destination_type", "destination_zipCode", "destination_name",
                              "return_city", "return_country", "return_id", "return_province",
                              "return_zipCode", "return_name", "created_month",
                              "created_at", "re_geo") \
        .select("order_id", "shipper_id", "global_shipper_id", "origin_city", "origin_country",
                "origin_id", "origin_province", "origin_zipCode", "origin_name",
                col("geo_org.latitude").alias("origin_latitude"),
                col("geo_org.longitude").alias("origin_longitude"),
                "destination_city", "destination_country", "destination_id",
                "destination_province", "destination_type",
                "destination_zipCode", "destination_name", "return_city",
                "return_country", "return_id", "return_province", "tracking_id",
                "return_zipCode", "return_name", "created_month", "created_at",
                "re_geo") \
        .select(explode("re_geo").alias("geo_return"), "order_id", "shipper_id", "global_shipper_id", "origin_city",
                "origin_country", "origin_id", "origin_province", "origin_zipCode", "origin_name",
                "origin_latitude", "origin_longitude", "destination_city", "destination_country", "destination_id",
                "destination_province", "destination_type", "tracking_id",
                "destination_zipCode", "destination_name", "return_city",
                "return_country", "return_id", "return_province",
                "return_zipCode", "return_name", "created_month", "created_at") \
        .select("order_id", "shipper_id", "global_shipper_id", "origin_city", "origin_country",
                "origin_id", "origin_province", "origin_zipCode", "origin_name",
                "origin_latitude", "origin_longitude", "destination_city", "destination_country", "destination_id",
                "destination_province", "destination_type", "tracking_id",
                "destination_zipCode", "destination_name", "return_city",
                "return_country", "return_id", "return_province",
                "return_zipCode", "return_name",
                col("geo_return.latitude").alias("return_latitude"),
                col("geo_return.longitude").alias("return_longitude"),
                "created_month", "created_at")

    final_df = re_geo_df.withColumn("timestamp", date_format(current_timestamp(), "yyyyMMddHHmmss")) \
        .withColumn("random_number", monotonically_increasing_id())

    final_df.createOrReplaceTempView("output")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                   SELECT 
                    case when '{{system_id}}' = 'id' then CONCAT(timestamp,random_number,order_id,1)
                    when '{{system_id}}' = 'mm' then CONCAT(timestamp,random_number,order_id,2)
                    when '{{system_id}}' = 'my' then CONCAT(timestamp,random_number,order_id,3)
                    when '{{system_id}}' = 'ph' then CONCAT(timestamp,random_number,order_id,4)
                    when '{{system_id}}' = 'sg' then CONCAT(timestamp,random_number,order_id,5)
                    when '{{system_id}}' = 'th' then CONCAT(timestamp,random_number,order_id,6)
                    when '{{system_id}}' = 'vn' then CONCAT(timestamp,random_number,order_id,7)
                    end as ID
                    , order_id
                    , cast(global_shipper_id as int) as shipper_id
                    , tracking_id
                    , origin_city
                    , origin_country
                    , origin_id
                    , origin_province
                    , origin_zipCode
                    , origin_name
                    , origin_latitude
                    , origin_longitude
                    , destination_city
                    , destination_country
                    , destination_id
                    , destination_province
                    , destination_type
                    , destination_zipCode
                    , destination_name
                    , return_city
                    , return_country
                    , return_id
                    , return_province
                    , return_zipCode
                    , return_name
                    , return_latitude
                    , return_longitude
                    , created_month
                    , created_at
                    , '{{system_id}}' as system_id
                    FROM output
                    """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_ADDRESSES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config( spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )


    run(spark, task_config)
    spark.stop()