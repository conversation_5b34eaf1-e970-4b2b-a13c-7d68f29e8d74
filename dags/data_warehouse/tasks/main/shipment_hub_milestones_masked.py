import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hub_milestones",
                jinja_template="""
                SELECT events.shipment_id
                       , events.hub_id
                       , CAST(IF(events.hub_id = shipments.orig_hub_id, 1, 0) AS bigint) AS origin_hub_flag
                       , CAST(IF(events.hub_id = shipments.dest_hub_id, 1, 0) AS bigint) AS destination_hub_flag
                       , CAST(IF(events.hub_id <> shipments.orig_hub_id
                                 AND events.hub_id <> shipments.dest_hub_id, 1, 0) AS bigint) AS transit_hub_flag
                       , shipments.shipment_type
                       , lower(shipments.orig_hub_country) AS system_id
                       , date_format(shipments.created_at, 'yyyy-MM') AS created_month
                       , min(IF(event IN ('SHIPMENT_VAN_INBOUND', 'SHIPMENT_HUB_INBOUND')
                                , from_utc_timestamp(events.created_at, {{ get_local_timezone }})
                                , NULL)) AS entry_datetime
                       , max(IF(event = 'SHIPMENT_CLOSED'
                                , from_utc_timestamp(events.created_at, {{ get_local_timezone }})
                                , NULL)) AS close_datetime
                       , max(IF(event = 'SHIPMENT_VAN_INBOUND'
                               , from_utc_timestamp(events.created_at, {{ get_local_timezone }})
                               , NULL)) AS van_inbound_datetime
                       , min(IF(event = 'SHIPMENT_HUB_INBOUND'
                                , from_utc_timestamp(events.created_at, {{ get_local_timezone }})
                                , NULL)) AS hub_inbound_datetime
                FROM shipment_events AS events
                LEFT JOIN shipments ON events.shipment_id = shipments.id
                WHERE events.hub_id IS NOT NULL
                      AND events.event in ('SHIPMENT_CLOSED', 'SHIPMENT_VAN_INBOUND', 'SHIPMENT_HUB_INBOUND')
                GROUP BY {{ range(1, 9) | join(',') }}
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("shipments.orig_hub_country")},
            ),
            base.TransformView(
                view_name="valid_transit_hubs",
                jinja_template="""
                SELECT base.shipment_id
                       , base.shipment_type
                       , base.hub_id
                       , ROW_NUMBER() OVER(PARTITION BY base.shipment_id
                                           ORDER BY base.entry_datetime)
                         AS transit_hub_sequence
                FROM hub_milestones AS base
                LEFT JOIN hub_milestones AS orig ON base.shipment_id = orig.shipment_id
                    AND orig.origin_hub_flag = 1
                LEFT JOIN hub_milestones AS dest ON base.shipment_id = dest.shipment_id
                    AND dest.destination_hub_flag = 1
                WHERE (base.entry_datetime > orig.entry_datetime OR orig.entry_datetime IS NULL)
                      AND (base.entry_datetime < dest.entry_datetime OR dest.entry_datetime IS NULL)
                      AND base.transit_hub_flag = 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.shipment_id
                       , base.shipment_type
                       , base.hub_id
                       , base.close_datetime
                       , base.van_inbound_datetime
                       , base.hub_inbound_datetime
                       , base.origin_hub_flag
                       , base.destination_hub_flag
                       , base.transit_hub_flag
                       , IF(base.transit_hub_flag = 1 and transit.transit_hub_sequence IS NULL, 1, 0)
                         AS invalid_transit_hub_flag
                       , transit.transit_hub_sequence
                       , base.system_id
                       , base.created_month
                FROM hub_milestones AS base
                LEFT JOIN valid_transit_hubs AS transit on transit.shipment_id = base.shipment_id
                    AND transit.hub_id = base.hub_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_HUB_MILESTONES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
