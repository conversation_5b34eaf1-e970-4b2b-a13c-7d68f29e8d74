import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_TRACKER_DATA_REPORT_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_TRACKER_DATA_REPORT_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.CNTicketingToolDAG.Task.TIKTOK_TRACKER_DATA_REPORT_DUPLICATE_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("created_month",)),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    # input_env = "prod"
    is_masked = True
    input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).TIKTOK_TRACKER_DATA_REPORT_DUPLICATE + latest_partition,
                view_name="tiktok_tracker_data_report",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                Select 
                    ticket_type
                    , lsp_ticket_id
                    , tracking_id
                    , country
                    , level_1_tag
                    , level_2_tag
                    , tag_description
                    , ticket_description
                    , tag_vs_description_match
                    , supposed_tag_description
                    , ticket_issues
                    , creation_time
                    , assign_to
                    , first_response_time
                    , reopen_nv_cause_flag
                    , reopen_type
                    , reship_tid
                    , internal_ticket_no
                    , compensation_flag
                    , dnr_issue_type
                    , tt_info_complete_flag
                    , country_response_capability_flag
                    , no_investigation_reason
                    , remarks
                    , my_refresh_status_flag
                    , created_date
                    , date_format(created_date, 'yyyy-MM') as created_month
                from tiktok_tracker_data_report
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_TRACKER_DATA_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
        # write_mode='merge',
        # primary_keys=["lsp_ticket_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()