import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_LSP_DATA_REPORT_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_LSP_DATA_REPORT_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.CNTicketingToolDAG.Task.TIKTOK_LSP_DATA_REPORT_DUPLICATE_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("created_month",)),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    #input_env = "prod"
    is_masked = True
    input_env = env

    latest_partition="/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).TIKTOK_LSP_DATA_REPORT_DUPLICATE + latest_partition,
                view_name="tt_lsp_data",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                 with 
                    base as (
                        Select 
                            t.level_1_issue_type
                            , t.level_2_issue_type
                            , t.level_3_issue_type
                            , t.country
                            , t.shipment_type
                            , t.lsp_ticket_id
                            , t.tracking_id
                            , t.package_id
                            , t.status
                            , t.create_time
                            , t.first_response_time
                            , t.solving_time
                            , t.update_time
                            , t.requester
                            , t.operator
                            , t.ticket_type
                            , t.judge_results
                            , t.structured_reply
                            , t.description
                            , t.reopen_count
                            , t.key_account
                            , t.created_date
                            , count(*) as cnt
                        from tt_lsp_data t
                        inner join 
                            (SELECT lsp_ticket_id,MAX(created_date) as max_created_date
                            FROM tt_lsp_data
                            GROUP BY lsp_ticket_id) a
                        on a.lsp_ticket_id = t.lsp_ticket_id and a.max_created_date = created_date
                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22
                        )
                    select 
                        level_1_issue_type
                        , level_2_issue_type
                        , level_3_issue_type
                        , country
                        , shipment_type
                        , lsp_ticket_id
                        , tracking_id
                        , package_id
                        , status
                        , create_time
                        , first_response_time
                        , solving_time
                        , update_time
                        , requester
                        , operator
                        , ticket_type
                        , judge_results
                        , structured_reply
                        , description
                        , reopen_count
                        , key_account
                        , created_date
                        ,date_format(created_date, 'yyyy-MM') as created_month
                    from base
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_LSP_DATA_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
        # write_mode='merge',
        # primary_keys=["lsp_ticket_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()