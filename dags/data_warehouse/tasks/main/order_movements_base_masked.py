import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_BASE_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_BASE_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="last_valid_delivery_success",
                jinja_template="""
                -- order_events type 34=REVERT_COMPLETED, 37=DELIVERY_SUCCESS, 38=DELIVERY_FAILURE

                SELECT order_id
                       , system_id
                       , max_by(id, time) AS id
                       , max_by(type, time) AS type
                       , max(time) AS time
                FROM order_events
                WHERE type in (34, 37, 38)
                GROUP BY 1, 2
                HAVING type = 37
                """,
            ),
            base.TransformView(
                view_name="last_valid_add_to_shipment",
                jinja_template="""
                -- order_events type 8=ADDED_TO_SHIPMENT, 9=REMOVED_FROM_SHIPMENT

                SELECT order_id
                       , system_id
                       , get_json_object(data, '$.shipment_id') AS shipment_id
                       , max_by(id, time) AS id
                       , max_by(get_json_object(data, '$.curr_hub_id'), time) AS hub_id
                       , max_by(type, time) AS type
                       , max(time) AS time
                FROM order_events
                WHERE type in (8, 9)
                GROUP BY 1, 2, 3
                HAVING type = 8
                """,
            ),
            base.TransformView(
                view_name="events_base",
                jinja_template="""
                SELECT id AS order_event_id
                       , order_id
                       , system_id
                       , CASE
                       {%- for types, fields in types_to_fields.items() %}
                             WHEN type in ({{ types | join(',') }}) THEN '{{ fields['location_type'] }}'
                       {%- endfor %}
                         END AS location_type
                       , CASE
                       {%- for types, fields in types_to_fields.items() %}
                       {%- if fields.get('location_id') %}
                             WHEN type in ({{ types | join(',') }})
                             THEN get_json_object(data, '$.{{ fields['location_id'] }}')
                       {%- endif %}
                       {%- endfor %}
                         END AS location_id
                       , CASE
                       {%- for types, fields in types_to_fields.items() %}
                       {%- if fields.get('hub_id') %}
                             WHEN type in ({{ types | join(',') }})
                             THEN get_json_object(data, '$.{{ fields['hub_id'] }}')
                       {%- endif %}
                       {%- endfor %}
                         END AS hub_id
                       , from_utc_timestamp(time, {{ get_local_timezone }}) AS event_datetime
                       , date_format(time, 'yyyy-MM') AS created_month
                FROM order_events
                WHERE type in ({{ types_to_fields.keys() | sum(start = ()) | join(',') }})
                UNION ALL
                SELECT id AS order_event_id
                       , order_id
                       , system_id
                       , 'SHIPMENT' AS location_type
                       , shipment_id AS location_id
                       , hub_id
                       , from_utc_timestamp(time, {{ get_local_timezone }}) AS event_datetime
                       , date_format(time, 'yyyy-MM') AS created_month
                FROM last_valid_add_to_shipment
                UNION ALL
                SELECT id AS order_event_id
                       , order_id
                       , system_id
                       , 'OUT OF NV' AS location_type
                       , NULL AS location_id
                       , NULL AS hub_id
                       , from_utc_timestamp(time, {{ get_local_timezone }}) AS event_datetime
                       , date_format(time, 'yyyy-MM') AS created_month
                FROM last_valid_delivery_success
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "types_to_fields": {
                        # 26=HUB_INBOUND_SCAN, 27=PARCEL_ROUTING_SCAN, 28=ROUTE_INBOUND_SCAN, 31=OUTBOUND_SCAN
                        (26, 27, 28, 31): {"location_type": "HUB", "location_id": "hub_id", "hub_id": "hub_id"},
                        # 24=DRIVER_INBOUND_SCAN, 25=DRIVER_PICKUP_SCAN, 38=DELIVERY_FAILURE, 39=PICKUP_SUCCESS
                        (24, 25, 38, 39): {"location_type": "ROUTE", "location_id": "route_id"},
                        # 20=FROM_SHIPPER_TO_DP, 22=FROM_DP_TO_DRIVER
                        (20, 22): {"location_type": "DP", "location_id": "dp_id"},
                        # 3=FORCED_SUCCESS, 41=TRANSFERRED_TO_THIRD_PARTY
                        (3, 41): {"location_type": "OUT OF NV"},
                    },
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT cast(order_event_id AS bigint) AS order_event_id
                       , cast(order_id AS bigint) AS order_id
                       , system_id
                       , location_type
                       , cast(location_id AS bigint) AS location_id
                       , cast(hub_id AS bigint) AS hub_id
                       , event_datetime
                       , created_month
                FROM
                (SELECT *
                        , IF(LAG(COALESCE(location_type, 'NA'), 1) OVER(PARTITION BY order_id, system_id
                                                                        ORDER BY event_datetime, order_event_id)
                             = COALESCE(location_type, 'NA')
                             AND LAG(COALESCE(location_id, 'NA'), 1) OVER(PARTITION BY order_id, system_id
                                                                          ORDER BY event_datetime, order_event_id)
                                 = COALESCE(location_id, 'NA')
                             , 0, 1) AS location_change_flag
                 FROM events_base)
                WHERE location_change_flag = 1
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_MOVEMENTS_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
