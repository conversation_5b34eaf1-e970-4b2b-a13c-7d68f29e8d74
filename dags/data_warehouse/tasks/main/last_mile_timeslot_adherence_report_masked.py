import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_TIMESLOT_ADHERENCE_REPORT_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS, view_name="shippers"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                SELECT transaction_id
                       , DATE(event_datetime) AS route_date
                       , route_id
                       , order_id
                       , status
                       , COALESCE(waypoint_zone, route_zone) AS zone
                       , COALESCE(waypoint_zone_short_name, route_zone_short_name) AS zone_code
                       , DATE(sla_datetime) AS sla_date
                       , DATE_FORMAT(event_datetime, 'HH:mm:ss') AS transaction_time
                       , CASE
                       {%- for id, mapping in timewindow_mapping.items() %}
                             WHEN waypoint_timewindow_id = {{ id }} THEN '{{ mapping[0] }}'
                       {%- endfor %}
                         END AS timeslot_start_time
                       , CASE
                       {%- for id, mapping in timewindow_mapping.items() %}
                             WHEN waypoint_timewindow_id = {{ id }} THEN '{{ mapping[1] }}'
                       {%- endfor %}
                         END AS timeslot_end_time
                       , CASE
                       {%- for id, mapping in timewindow_mapping.items() %}
                             WHEN waypoint_timewindow_id = {{ id }} THEN '{{ mapping[2] }}'
                       {%- endfor %}
                         END AS timeslot
                       , route_hub_id
                       , route_driver_id
                       , created_month
                FROM delivery_transaction_events
                WHERE transit_flag = 0
                    AND route_id IS NOT NULL
                    AND order_id IS NOT NULL
                """,
                jinja_arguments={
                    "timewindow_mapping": {
                        -3: ("18:00:00", "22:00:00", "6pm - 10pm"),
                        -2: ("09:00:00", "18:00:00", "9am - 6pm"),
                        -1: ("09:00:00", "22:00:00", "9am - 10pm"),
                        0: ("09:00:00", "12:00:00", "9am - 12pm"),
                        1: ("12:00:00", "15:00:00", "12pm - 3pm"),
                        2: ("15:00:00", "18:00:00", "3pm - 6pm"),
                        3: ("18:00:00", "22:00:00", "6pm - 10pm"),
                    }
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.transaction_id
                       , base.route_date
                       , base.route_id
                       , base.order_id
                       , base.status
                       , base.zone
                       , base.zone_code
                       , shippers.id AS shipper_id
                       , shippers.name AS shipper_name
                       , base.sla_date
                       , base.transaction_time
                       , base.timeslot_start_time
                       , base.timeslot_end_time
                       , base.timeslot
                       , hubs.id AS depot_id
                       , hubs.name AS depot_name
                       , drivers.id AS courier_id
                       , drivers.first_name AS courier_name
                       , drivers.driver_type AS courier_type
                       , drivers.fleet_type AS courier_fleet_type
                       , CASE
                             WHEN base.transaction_time < base.timeslot_start_time THEN 'EARLY'
                             WHEN base.transaction_time <= base.timeslot_end_time THEN 'ONTIME'
                             WHEN base.transaction_time > base.timeslot_end_time THEN 'LATE'
                             ELSE 'ERROR'
                         END AS timeslot_status
                       , base.created_month
                FROM base
                LEFT JOIN orders ON orders.id = base.order_id
                LEFT JOIN drivers_enriched AS drivers ON drivers.id = base.route_driver_id
                LEFT JOIN hubs_enriched AS hubs ON hubs.id = base.route_hub_id
                LEFT JOIN shippers ON shippers.id = orders.global_shipper_id
                AND shippers.system_id = '{{ system_id }}'
                """,
                jinja_arguments={"system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_TIMESLOT_ADHERENCE_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
