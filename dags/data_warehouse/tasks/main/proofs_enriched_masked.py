import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
        constants.SystemID.TH,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_DP_MILESTONES_MASKED
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    ## Setting a lower lookback range for proofs table as these tables are heavy
    proofs_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        proofs_lookback_ranges = base.LookBackRange(None, None)
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOFS,
                view_name="proofs",
                input_range=proofs_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_JOBS,
                view_name="proof_jobs",
                input_range=proofs_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_TRANSACTIONS,
                view_name="proof_transactions",
                input_range=proofs_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_RESERVATIONS,
                view_name="proof_reservations",
                input_range=proofs_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_TRACKING_IDS,
                view_name="proof_tracking_ids",
                input_range=proofs_lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DP_MILESTONES,
                view_name="order_dp_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with proof_transactions_base as (
                    select
                        proof_transactions.proof_id
                        , proof_transactions.transaction_id
                    from proof_transactions
                    left join transactions
                        on proof_transactions.transaction_id = transactions.id
                    left join orders
                        on transactions.order_id = orders.id
                    left join order_dp_milestones
                        on order_dp_milestones.order_id = orders.id
                    where proof_transactions.system_id = '{{ system_id }}'
                        -- Keep delivery transactions
                        and (transactions.type = 'DD'
                        -- Keep RPU orders that were picked up from doorstep and not dropped of by consignee(in this case they are the shippers) at DP
                            or (orders.type = 'Return' and transactions.type = 'PP' and order_dp_milestones.shipper_to_dp_datetime is null)
                        )
                ),

                picked_up_orders_cte as (
                select
                    proof_id
                    , count(distinct tracking_id) as parcel_pickup_quantity
                from proof_tracking_ids
                group by 1
                )

                select
                    proofs.id as proof_id
                    , proofs.type
                    , proofs.status
                    , proofs.sign_coordinates_lat
                    , proofs.sign_coordinates_lng
                    , cast(get_json_object(proofs.failure_reason, '$.failure_reason_id') as int) as failure_reason_id
                    , cast(get_json_object(proofs.failure_reason, '$.failure_reason_code_id') as int) as failure_reason_code_id
                    , proofs.nonce_id
                    , CASE
                        WHEN proof_transactions_base.transaction_id is not null THEN 'Transaction'
                        WHEN proof_jobs.job_id is not null and proof_jobs.job_type = 'PICKUP_APPOINTMENT' THEN 'Pickup Appointment Job'
                        WHEN proof_jobs.job_id is not null and proof_jobs.job_type = 'PUDO_PICKUP_APPOINTMENT' THEN 'Pudo Pickup Appointment Job'
                        WHEN proof_reservations.reservation_id is not null THEN 'Reservation'
                    END AS reference
                    , coalesce(proof_transactions_base.transaction_id, proof_jobs.job_id, proof_reservations.reservation_id) as reference_id
                    {%- for column in metadata_columns_long %}
                    , cast(get_json_object(proofs.metadata, '$.{{column}}') as long) as {{column}}
                    {%- endfor %}
                    , picked_up_orders_cte.parcel_pickup_quantity
                    {%- for column in metadata_columns_int %}
                    , cast(get_json_object(proofs.metadata, '$.{{column}}') as int) as {{column}}
                    {%- endfor %}
                    , from_utc_timestamp(proofs.created_at, '{{ local_timezone }}') as creation_datetime
                    , from_utc_timestamp(proofs.updated_at, '{{ local_timezone }}') as updated_datetime
                    , from_utc_timestamp(proofs.commit_date, '{{ local_timezone }}') as commited_datetime
                    , proofs.system_id
                    , proofs.created_month
                from proofs
                left join proof_transactions_base
                    on proofs.id = proof_transactions_base.proof_id
                left join proof_jobs
                    on proofs.id = proof_jobs.proof_id
                left join proof_reservations
                    on proofs.id = proof_reservations.proof_id
                left join picked_up_orders_cte
                    on proofs.id = picked_up_orders_cte.proof_id
                where proofs.system_id = '{{ system_id }}'
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    'metadata_columns_long': [
                        'waypoint_id'
                    ],
                    'metadata_columns_int': [
                        'parcel_quantity'
                        , 'driver_id'
                        , 'dp_id'
                        , 'driver_dpms_id'
                        , 'total_proof_photos'
                    ],
                }
            ),
        ),
        output_range=proofs_lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PROOFS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=proofs_lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()