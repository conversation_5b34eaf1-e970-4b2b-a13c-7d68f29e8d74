import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.TICKET_CLOSURE_PERFORMANCE_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.TICKET_CLOSURE_PERFORMANCE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.RecoveryDAG.Task.EXCLUSION_REQUEST_DETAILS_MASKED,
        data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderTagsDAG.DAG_ID,
            task_id=data_warehouse.OrderTagsDAG.Task.ORDER_TAGS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_SHIPPER_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_RECOVERY_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_TAGS_ENRICHED,
                view_name="order_tag",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_RECOVERY,
                view_name="calendar_recovery",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_SHIPPER,
                view_name="calendar_shipper",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).EXCLUSION_REQUEST_DETAILS,
                view_name="exclusions",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    id
                    , order_id
                    , tracking_id
                    , shipper_id
                    , type
                    , sub_type
                    , status
                    , outcome
                    , assigned_dept
                    , investigating_hub_id
                    , creation_datetime
                    , date(creation_datetime) as start_clock_date
                    , date(resolution_datetime) as resolution_date
                    , date(now() - interval '1' day) as report_date
                    , system_id
                    , if(system_id = 'my', 'Kuala Lumpur', 'national') as calrec_region
                    , created_month
                from pets_tickets_enriched
                where type not in ('PARCEL ON HOLD', 'SELF COLLECTION')
                    and not (type = 'SHIPPER ISSUE' and sub_type = 'NO LABEL')
                    and status <> 'CANCELLED'
                """,
            ),
            base.TransformView(
                view_name="added_exclusion",
                jinja_template="""
                select
                    base.id
                    , base.order_id
                    , base.tracking_id
                    , base.shipper_id
                    , base.type
                    , base.sub_type
                    , base.status
                    , base.outcome
                    , base.assigned_dept
                    , base.investigating_hub_id
                    , base.creation_datetime
                    , base.start_clock_date
                    , base.resolution_date
                    , base.report_date
                    , base.system_id
                    , base.calrec_region
                    , base.created_month
                    , if(
                        base.system_id = 'vn'
                            and (base.investigating_hub_id in (135, 76, 1112) or base.investigating_hub_id is null)
                        , 1
                        , 0
                    ) as excluded_hub_flag
                    , if(base.system_id = 'id' and base.assigned_dept = 'MM', 1, 0) as excluded_dept_flag
                    , max(if(tag.tag_name = 'FORCE-M', 1, 0)) as excluded_tag_flag
                from base
                left join order_tag tag on base.order_id = tag.order_id
                group by {{ range(1, 19) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="filtered_start_clock",
                jinja_template="""
                select
                    *
                from added_exclusion
                where
                    excluded_hub_flag = 0
                    and excluded_tag_flag = 0
                    and excluded_dept_flag = 0
                """,
            ),
            base.TransformView(
                view_name="base_sla",
                jinja_template="""
                select
                    base.*
                    , case
                        when base.type = 'MISSING'
                            then if(
                                base.system_id in ('id', 'ph', 'my')
                                , cal_rec.next_working_day_6
                                , cal_rec.next_working_day_3
                            )
                        when base.type = 'DAMAGED' 
                            and shippers.id in (5071045, 5676794, 5011035)
                            and base.start_clock_date >= date('2024-07-25')
                            then base.start_clock_date + interval '30' day
                        when base.type = 'DAMAGED' then cal_rec.next_working_day_3
                        when base.sub_type = 'NO ORDER' then cal_rec.next_working_day_10
                        when base.assigned_dept = 'XB' and base.start_clock_date < date('2023-07-01')
                            then cal_ship.next_working_day_7
                        when base.sub_type = 'COMPLETED ORDER' then cal_ship.next_working_day_7
                        when base.system_id = 'my' and orders.shipper_id in (4564688, 4712190)
                            then cal_ship.next_working_day_7
                        when base.system_id = 'sg'
                            and orders.shipper_id = 3483596
                            and base.sub_type in ('INACCURATE ADDRESS', 'RESTRICTED ZONES', 'DISPUTED ORDER INFO')
                            and base.start_clock_date >= date('2023-01-01')
                            then cal_ship.next_working_day_7
                        when base.type in ('PARCEL EXCEPTION', 'SHIPPER ISSUE')
                            and base.start_clock_date >= date('2023-07-01')
                            and (shippers.sales_person_code like 'XB%' and shippers.sales_person_code not in ('XB_SBO'))
                            then cal_ship.next_working_day_7
                        when base.type in ('PARCEL EXCEPTION', 'SHIPPER ISSUE')
                            then case
                                when base.system_id in ('my') and base.start_clock_date < date('2023-01-01')
                                    then cal_ship.next_working_day_5
                                when base.system_id in ('my') then cal_ship.next_working_day_6
                                when base.system_id in ('ph', 'sg') then cal_ship.next_working_day_5
                                else cal_ship.next_working_day_3
                            end
                        else cal_rec.next_working_day_3
                    end as base_sla_date
                from filtered_start_clock base
                left join calendar_recovery cal_rec
                    on base.start_clock_date = cal_rec.date
                    and base.calrec_region = cal_rec.region
                left join calendar_shipper cal_ship
                    on base.start_clock_date = cal_ship.date
                    and cal_ship.region = 'national'
                left join orders_enriched orders
                    on base.order_id = orders.order_id
                left join shipper_attributes shippers
                    on orders.shipper_id = shippers.id 
                """,
            ),
            base.TransformView(
                view_name="sla_adjust",
                jinja_template="""
                select
                    base_sla.*
                    , if(cal_rec.working_day = 0, cal_rec.next_working_day_1, cal_rec.next_working_day_0) sla_date
                from base_sla
                left join calendar_recovery cal_rec
                    on base_sla.base_sla_date = cal_rec.date
                    and base_sla.system_id = cal_rec.system_id
                    and base_sla.calrec_region = cal_rec.region
                """,
            ),
            base.TransformView(
                view_name="closure_base",
                jinja_template="""
                select sla.id as pets_id
                    , sla.order_id
                    , sla.tracking_id
                    , sla.shipper_id
                    , sla.type
                    , sla.sub_type
                    , sla.status
                    , sla.outcome
                    , sla.assigned_dept
                    , sla.investigating_hub_id
                    , sla.creation_datetime
                    , sla.start_clock_date
                    , sla.sla_date
                    , sla.resolution_date
                    , if(resolution_date is not null, 1, 0) as sla_measured
                    , case
                        when resolution_date is null then null
                        when cal_age.working_day_cum <= cal_sla.working_day_cum then 1
                        else 0
                    end sla_met
                    , if(
                        resolution_date is not null
                        , greatest(cal_age.working_day_cum - cal_sla.working_day_cum, 1)
                        , null
                    ) as sla_weight
                    , sla.system_id
                    , sla.created_month
                from sla_adjust sla
                left join calendar_recovery as cal_sla
                    on sla.sla_date = cal_sla.date
                    and sla.calrec_region = cal_sla.region
                left join calendar_recovery as cal_age
                    on sla.resolution_date = cal_age.date
                    and sla.calrec_region = cal_age.region
                """,
            ),
            base.TransformView(
                view_name="closure_base_my",
                jinja_template="""
                    select *
                    from closure_base
                    where system_id = 'my'
                """,
            ),
            base.TransformView(
                view_name="closure_base_others",
                jinja_template="""
                    select *
                    from closure_base
                    where system_id != 'my'
                """,
            ),
            base.TransformView(
                view_name="exclusions_my",
                jinja_template="""
                    select * from exclusions where system_id = 'my'
                """,
            ),
            base.TransformView(
                view_name="exclusions_others",
                jinja_template="""
                   select * from exclusions where system_id != 'my'
               """,
            ),
            base.TransformView(
                view_name="expanded_extension",
                jinja_template="""
                select
                    exclusions.system_id
                    , exclusions.category
                    , exclusions.category_value
                    , cal_rec.next_working_day_0 as extended_date
                from exclusions_my exclusions
                left join calendar_recovery as cal_rec on
                    cal_rec.next_working_day_0 >= date(exclusions.extension_start)
                    and cal_rec.next_working_day_0 <= date(exclusions.extension_end)
                    and cal_rec.region = 'Kuala Lumpur'
                where cal_rec.working_day = 1 and exclusions.closure = 1

                union all

                select
                    exclusions.system_id
                    , exclusions.category
                    , exclusions.category_value
                    , cal_rec.next_working_day_0 as extended_date
                from exclusions_others exclusions
                left join calendar_recovery as cal_rec on
                    cal_rec.next_working_day_0 >= date(exclusions.extension_start)
                    and cal_rec.next_working_day_0 <= date(exclusions.extension_end)
                    and cal_rec.region = 'national'
                where cal_rec.working_day = 1 and exclusions.closure = 1
                """,
            ),
            base.TransformView(
                view_name="distinct_extension",
                jinja_template="""
                select distinct system_id
                    , category
                    , category_value
                    , extended_date
                from expanded_extension
                """,
            ),
            base.TransformView(
                view_name="expand_measurement_date",
                jinja_template="""
                select
                    raw.system_id
                    , raw.pets_id
                    , raw.tracking_id
                    , raw.investigating_hub_id
                    , raw.sla_date
                    , raw.resolution_date
                    , cal_rec.next_working_day_0 as measurement_date
                from closure_base_others as raw
                left join calendar_recovery as cal_rec on
                    cal_rec.region = 'national'
                    and cal_rec.next_working_day_0 > raw.sla_date
                    and cal_rec.next_working_day_0 <= raw.resolution_date
                where raw.sla_measured = 1 and raw.sla_met = 0

                union all

                select
                    raw.system_id
                    , raw.pets_id
                    , raw.tracking_id
                    , raw.investigating_hub_id
                    , raw.sla_date
                    , raw.resolution_date
                    , cal_rec.next_working_day_0 as measurement_date
                from closure_base_my as raw
                left join calendar_recovery as cal_rec on
                    cal_rec.region = 'Kuala Lumpur'
                    and cal_rec.next_working_day_0 > raw.sla_date
                    and cal_rec.next_working_day_0 <= raw.resolution_date
                where raw.sla_measured = 1 and raw.sla_met = 0
                """,
            ),
            base.TransformView(
                view_name="process_extension",
                jinja_template="""
                select
                    base.*
                    , coalesce(country_ext.extended_date, hub_ext.extended_date, tid_ext.extended_date) as extended_date
                    , if(country_ext.extended_date is not null, 1, 0) as country_flag
                    , if(hub_ext.extended_date is not null, 1, 0) as hub_flag
                    , if(tid_ext.extended_date is not null, 1, 0) as tid_flag
                from expand_measurement_date as base
                --country extension
                left join distinct_extension as country_ext
                    on country_ext.category = 'country'
                    and base.system_id = country_ext.category_value
                    and base.measurement_date = country_ext.extended_date
                --hub extension
                left join distinct_extension as hub_ext
                    on hub_ext.category = 'hub_id'
                    and base.investigating_hub_id = hub_ext.category_value
                    and base.measurement_date = hub_ext.extended_date
                --tracking id extension
                left join distinct_extension as tid_ext
                    on tid_ext.category = 'tracking_id'
                    and base.tracking_id = tid_ext.category_value
                    and base.measurement_date = tid_ext.extended_date
                """,
            ),
            base.TransformView(
                view_name="consolidate_flag",
                jinja_template="""
                select
                    system_id
                    , pets_id
                    , tracking_id
                    , investigating_hub_id
                    , sla_date
                    , resolution_date
                    , extended_date
                    , country_flag
                    , hub_flag
                    , tid_flag
                    , greatest(country_flag, hub_flag, tid_flag) as overall_flag
                from process_extension
                """,
            ),
            base.TransformView(
                view_name="extension_total",
                jinja_template="""
                select
                    system_id
                    , pets_id
                    , sum(overall_flag) as total_extension
                from consolidate_flag
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.pets_id
                    , base.order_id
                    , base.tracking_id
                    , base.shipper_id
                    , base.type
                    , base.sub_type
                    , base.status
                    , base.outcome
                    , base.assigned_dept
                    , base.investigating_hub_id
                    , base.creation_datetime
                    , base.start_clock_date
                    , base.sla_date
                    , base.resolution_date
                    , base.sla_measured
                    , base.sla_met
                    , base.sla_weight
                    , coalesce(extensions.total_extension, 0) as total_extension
                    , case
                        when base.sla_measured = 0 then null
                        when (base.sla_weight - coalesce(extensions.total_extension, 0) = 0) or base.sla_met = 1 then 1
                        else base.sla_met
                    end as adjusted_sla_met
                    , if(
                        base.sla_measured = 0
                        , null
                        , greatest((base.sla_weight - coalesce(extensions.total_extension, 0)), 1)
                    ) as adjusted_sla_weight
                    , base.system_id
                    , base.created_month
                from closure_base as base
                left join extension_total as extensions
                    on base.system_id = extensions.system_id
                    and base.pets_id = extensions.pets_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TICKET_CLOSURE_PERFORMANCE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
