import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.TICKET_CREATION_LAST_SCANS_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.TICKET_CREATION_LAST_SCANS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.VALID_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.FIRST_TERMINAL_STATUS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.ON_HOLD_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderTagsDAG.DAG_ID, task_id=data_warehouse.OrderTagsDAG.Task.ORDER_TAGS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_RECOVERY_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_TERMINAL_STATUS_EVENTS,
                view_name="terminal_status_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).VALID_SCAN_EVENTS,
                view_name="valid_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ON_HOLD_EVENTS,
                view_name="update_status_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_TAGS_ENRICHED,
                view_name="order_tags",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_RECOVERY,
                view_name="calendar_recovery",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar_ops",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="order_base",
                jinja_template="""
                    with first_terminal as (
                        select
                            order_id
                            , min(terminal_time) first_terminal
                        from terminal_status_events
                        group by 1
                    )

                    select
                        orders.order_id
                        , orders.system_id
                        , orders.tracking_id
                        , if(orders.system_id = 'my', 'Kuala Lumpur', 'national') as calrec_region
                        , date(least(orders.pickup_datetime, orders.inbound_datetime)) start_date
                        , date(coalesce(least(orders.force_success_datetime, orders.third_party_transfer_datetime,
                            orders.delivery_success_datetime,
                            terminal.first_terminal), date('{{ measurement_datetime }}')))
                            as end_date
                        , if(coalesce(orders.force_success_datetime, orders.third_party_transfer_datetime,
                        orders.delivery_success_datetime, terminal.first_terminal) is null, 1, 0) as still_active_flag
                        , orders.created_month
                    from order_milestones as orders
                    left join first_terminal as terminal
                        on orders.order_id = terminal.order_id
                    where
                        orders.granular_status != 'Returned to Warehouse'
                        and (
                            (orders.system_id = 'sg' and (orders.shipper_id != 502572
                                and orders.tracking_id not like 'SGNJVDIMWEIGHTTEST%'))
                            or (orders.system_id = 'th' and (orders.shipper_id != 770345
                                and orders.tracking_id != 'CJTH201123525186'))
                            or (orders.system_id = 'id' and (orders.shipper_id != 3702740))
                            or (orders.system_id = 'vn' and (orders.shipper_id not in (89332, 595816, 5317332)))
                            or (orders.system_id in ('my', 'ph'))
                        )
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
            base.TransformView(
                view_name="last_scan",
                jinja_template="""
                    with expand_base as (
                        select
                            base.*
                            , ops.next_working_day_0 as measurement_date
                            , if(rec.working_day = 1, 1, 0) as recovery_working_day_flag
                        from order_base as base
                        left join calendar_ops as ops
                            on ops.next_working_day_0 > base.start_date
                            and ops.next_working_day_0 < base.end_date
                            and ops.region = 'national'
                        left join calendar_recovery as rec
                            on base.calrec_region = rec.region
                            and ops.next_working_day_0 = rec.next_working_day_0
                    )

                    , valid_scan_combined as (
                        (select
                            order_id
                            , date(valid_scan_datetime) valid_scan_date
                        from valid_scan_events)
                        union all
                        (select
                            order_id
                            , date(shipment_cancellation_datetime) valid_scan_date
                        from shipment_orders)
                    )

                    , distinct_valid_scan_date as (
                        select distinct
                            order_id
                            , valid_scan_date
                        from valid_scan_combined
                    )

                    , valid_scan_flag as (
                        select
                            base.*
                            , if(scan.valid_scan_date is not null, 1, 0) valid_scan_flag
                            , row_number() over (partition by base.order_id order by base.measurement_date asc) rnk
                        from expand_base as base
                        left join distinct_valid_scan_date as scan
                            on base.order_id = scan.order_id
                            and base.measurement_date = scan.valid_scan_date
                        where (base.recovery_working_day_flag = 1 or scan.valid_scan_date is not null)
                    )

                    select
                        base.order_id
                        , base.system_id
                        , base.tracking_id
                        , base.calrec_region
                        , base.start_date
                        , base.end_date
                        , base.still_active_flag
                        , base.measurement_date
                        , base.recovery_working_day_flag
                        , base.valid_scan_flag
                        , base.rnk
                        , base.created_month
                        , max(end.valid_scan_datetime) last_valid_scan_datetime_by_date_end
                        , max(start.valid_scan_datetime) last_valid_scan_datetime_by_date_start
                    from valid_scan_flag as base
                    left join valid_scan_events as end
                        on base.order_id = end.order_id
                        and end.valid_scan_datetime < base.measurement_date + interval '24' hour
                    left join valid_scan_events as start
                        on base.order_id = start.order_id
                        and start.valid_scan_datetime < base.measurement_date
                    group by {{ range(1, 13) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="untracked_granular",
                jinja_template="""
                    with other_granular_status_events (
                        select
                            order_id
                            , status_time
                            , old_granular_status
                            , new_granular_status
                            , system_id
                            , row_number() over (partition by order_id order by status_time asc) rnk
                        from update_status_events
                        where
                            (old_granular_status is not null or new_granular_status is not null)
                    )

                    select
                        current.order_id
                        , current.new_granular_status current_status
                        , next.old_granular_status next_status
                        , current.status_time current_status_start_time
                        , next.status_time next_status_start_time
                        , current.system_id
                    from other_granular_status_events as current
                    left join other_granular_status_events as next
                        on current.order_id = next.order_id
                        and current.rnk = next.rnk - 1
                    where
                        current.new_granular_status in (
                            'Pickup fail'
                            , 'Staging'
                            , 'Pending Pickup'
                            , 'Arrived at Distribution Point'
                            , 'Pending Pickup at Distribution Point'
                            , 'Returned to Warehouse'
                        )
                    """,
            ),
            base.TransformView(
                view_name="selected_granular_log",
                jinja_template="""
                    with untracked_granular_adjustment as (
                        select
                            base.order_id
                            , base.current_status
                            , base.next_status
                            , date(base.current_status_start_time) current_status_start_date
                            , date(base.next_status_start_time) next_status_start_date
                            , base.system_id
                            , date(min(scan.valid_scan_datetime)) next_valid_scan_date
                        from untracked_granular as base
                        left join valid_scan_events as scan
                            on base.order_id = scan.order_id
                            and scan.valid_scan_datetime > base.current_status_start_time
                            and scan.valid_scan_datetime <= base.next_status_start_time
                        group by 1,2,3,4,5,6
                    )

                     select
                        order_id
                        , current_status
                        , next_status
                        , current_status_start_date status_start_date
                        , coalesce(next_valid_scan_date, next_status_start_date,
                            date(
                                from_utc_timestamp(
                                    '{{ measurement_datetime }}',
                                    {{ get_local_timezone }})
                                )
                        ) as status_end_date
                    from untracked_granular_adjustment
                    """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    with on_hold_events (
                        select
                            order_id
                            , system_id
                            , status_time
                            , old_granular_status
                            , new_granular_status
                            , row_number() over (partition by order_id order by status_time asc) rnk
                        from update_status_events
                        where
                            (old_granular_status = 'On Hold' or new_granular_status = 'On Hold')
                    ),
                    on_hold_log as (
                         select
                            current.order_id
                            , current.status_time status_start_time
                            , next.status_time status_end_time
                        from on_hold_events current
                        left join on_hold_events next
                            on current.order_id = next.order_id
                            and current.rnk = next.rnk - 1
                    ),
                    order_tag_log as (
                        select
                            system_id
                            , order_id
                            , date(creation_datetime) ot_start_date
                            , date(deletion_datetime) ot_end_date
                        from order_tags
                        where
                            (system_id in ('sg', 'th', 'my', 'vn')
                                and tag_name in ('excl-fmaj','excl-sreq','excl-bklg',
                                'FORCE-MAJR','CLAIM-LOST','VC','FORCEMJR-C'))
                            or (system_id = 'ph'
                                and tag_name in ('excl-fmaj','excl-sreq',
                                'excl-bklg','VC', 'CLAIM-LOST','FORCEMJR-C','FORCE-MAJR'))
                            or (system_id = 'id'
                            and tag_name in ('excl-fmaj','excl-sreq','excl-bklg',
                            'FORCE-M','FORCE-MAJR','CLAIM-LOST','VC','FORCEMJR-C'))
                    )

                    select
                        base.order_id
                        , base.system_id
                        , base.tracking_id
                        , base.calrec_region
                        , base.start_date
                        , base.end_date
                        , base.still_active_flag
                        , base.measurement_date
                        , base.valid_scan_flag
                        , base.last_valid_scan_datetime_by_date_end
                        , base.last_valid_scan_datetime_by_date_start
                        , base.recovery_working_day_flag
                        , base.rnk
                        , base.created_month
                        , max(if(on_hold.status_start_time is not null, 1, 0)) as on_hold_flag
                        , max(if(pets.creation_datetime is not null and pets.type = 'MISSING', 1, 0)
                            ) as missing_ticket_flag
                        , min(case when pets.type = 'MISSING' then pets.creation_datetime end
                            ) as missing_ticket_creation_datetime
                        , max(if(pets.creation_datetime is not null and pets.type != 'MISSING', 1, 0)
                            ) as non_missing_ticket_flag
                        , max(if(shipment.add_to_shipment_datetime is not null, 1, 0)) as in_shipment_flag
                        , max(if(granular.status_start_date is not null, 1, 0)) as selected_granular_flag
                        , max(if(tag.ot_start_date is not null, 1, 0)) as order_tag_exclusion_flag
                    from last_scan as base
                    left join last_scan as lag
                        on base.order_id = lag.order_id
                        and base.rnk - 1 = lag.rnk
                    left join on_hold_log as on_hold
                        on base.order_id = on_hold.order_id
                        and (
                            (base.measurement_date >= date(on_hold.status_start_time) and
                                base.measurement_date <= date(on_hold.status_end_time))
                            or (base.measurement_date >= date(on_hold.status_start_time
                            ) and on_hold.status_end_time is null)
                        )
                    left join pets_tickets as pets
                        on base.order_id = pets.order_id
                        and (
                            (pets.status = 'RESOLVED' and base.measurement_date >= date(pets.creation_datetime)
                                and base.measurement_date <= date(pets.resolution_datetime))
                            or (pets.status in ('IN PROGRESS', 'ON HOLD')
                                and base.measurement_date >= date(pets.creation_datetime)
                                and pets.resolution_datetime is null)
                            or (pets.status = 'CANCELLED' and base.measurement_date >= date(pets.creation_datetime)
                                and base.measurement_date <= date(pets.cancellation_datetime))
                        )
                    left join shipment_orders as shipment
                        on base.order_id = shipment.order_id
                        and ((shipment.status in ('Completed')
                                and (base.measurement_date >= date(shipment.add_to_shipment_datetime))
                                and (base.measurement_date <= date(least(shipment.last_shipment_scan_datetime,
                                    shipment.shipment_completion_datetime)))
                            )
                            or (shipment.status in ('Cancelled')
                                and (base.measurement_date >= date(shipment.add_to_shipment_datetime))
                                and (base.measurement_date <= date(shipment.shipment_cancellation_datetime))
                            )
                            or (shipment.status not in ('Completed', 'Cancelled')
                                and (base.measurement_date >= date(shipment.add_to_shipment_datetime))
                                and (base.measurement_date <= date(coalesce(shipment.order_inbound_datetime,
                                    date(from_utc_timestamp('{{ measurement_datetime }}',
                                    {{ get_local_timezone }})))))
                            )
                        )
                    left join selected_granular_log as granular
                        on base.order_id = granular.order_id
                        and (
                            base.measurement_date >= date(granular.status_start_date)
                            and base.measurement_date <= date(granular.status_end_date)
                        )
                    left join order_tag_log as tag
                        on base.order_id = tag.order_id
                        and (
                            (base.measurement_date >= tag.ot_start_date and base.measurement_date <= tag.ot_end_date)
                            or (base.measurement_date >= tag.ot_start_date and tag.ot_end_date is null)
                        )
                    group by {{ range(1, 15) | join(',') }}
                    """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("base.system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TICKET_CREATION_LAST_SCANS,
        measurement_datetime=measurement_datetime,
        output_range=lookback_ranges.output,
        partition_by=("created_month",),
        system_id=system_id,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
