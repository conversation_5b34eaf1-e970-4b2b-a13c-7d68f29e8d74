import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).RECORD_TYPE, view_name="record_type"),),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            *
                            , case
                                {%- for system_id, names in system_id_to_names.items() %}
                                when lower(name) in ('{{ names | join("', '") }}') then '{{ system_id }}'
                                {%- endfor %}
                            end as system_id
                        from record_type

                    )
                    , final as (

                        select
                            id
                            , name
                            , system_id
                            , date_format(
                                from_utc_timestamp(created_date, {{ get_local_timezone }}), 'yyyy-MM'
                            ) as created_month
                        from base

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "system_id_to_names": {
                        SystemID.ID: ("indonesia", "ninja direct indonesia", "ninja direct id"),
                        SystemID.MY: ("malaysia", "ninja direct malaysia", "ninja direct my"),
                        SystemID.PH: ("philippines", "ninja direct philippines", "ninja direct ph"),
                        SystemID.SG: ("singapore", "ninja direct singapore", "ninja direct sg"),
                        SystemID.TH: ("thailand", "ninja direct thailand", "ninja direct th"),
                        SystemID.VN: ("vietnam", "ninja direct vietnam", "ninja direct vn"),
                    },
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_RECORD_TYPE_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
