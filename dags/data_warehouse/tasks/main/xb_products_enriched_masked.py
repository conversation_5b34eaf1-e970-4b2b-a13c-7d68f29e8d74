import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_PRODUCTS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_PRODUCTS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PRODUCTS,
                view_name="products",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , code
                    , name
                    , origin_country
                    , destination_country
                    , case transport_type
                    {%- for transport_type, name in transport_type_to_name.items() %}
                        when {{ transport_type }} then '{{ name }}'
                    {%- endfor %}
                    end as transport_type
                    , case freight_type
                    {%- for freight_type, name in freight_type_to_name.items() %}
                        when {{ freight_type }} then '{{ name }}'
                    {%- endfor %}
                    end as freight_type
                    , 'gl' as system_id
                    , date_format(created_at, 'yyyy-MM') as created_month
                from products
                """,
                jinja_arguments={
                    "transport_type_to_name":{
                        1:"Air",
                        2:"Ocean",
                        3:"Land",
                    },
                    "freight_type_to_name":{
                        1:"Normal",
                        2:"Sensitive",
                    },
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_PRODUCTS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
