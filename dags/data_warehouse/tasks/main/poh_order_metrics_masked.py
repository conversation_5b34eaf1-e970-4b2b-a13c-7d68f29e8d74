import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED, ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_PICKUPS,
                view_name="order_pickups",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).HUB_HANDOVER_PARCELS,
                view_name="hub_handover_parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                /* Extract data from the datalake table, convert timestamp to local timezone */
                with base as (
                    select
                        cast(hub_handover_parcels.id as integer) as id
                        , from_utc_timestamp(hub_handover_parcels.created_at, '{{ get_local_timezone }}') as created_at
                        , from_utc_timestamp(hub_handover_parcels.deleted_at, '{{ get_local_timezone }}') as deleted_at
                        , cast(hub_handover_parcels.hub_handover_id as integer) as hub_handover_id
                        , cast(hub_handover_parcels.parcel_external_id as integer) as order_id
                        , cast(hub_handover_parcels.route_external_id as integer) as route_id
                        , hub_handover_parcels.scan as tracking_id
                        , from_utc_timestamp(hub_handover_parcels.updated_at, '{{ get_local_timezone }}') as updated_at
                        , hub_handover_parcels.created_month
                    from hub_handover_parcels
                    where hub_handover_parcels.system_id == '{{ system_id }}'

                ),

                /* Join on route logs to get the driver id */
                route_log_cte as (

                    select
                        base.*
                        , cast(route_logs.driver_id as bigint) as driver_id
                    from base
                    left join route_logs on
                        base.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'

                ),

                /* Join on driver_enriched to get the driver details */
                drivers_cte as (

                    select
                        route_log_cte.*
                        , drivers_enriched.driver_type as driver_type
                        , drivers_enriched.first_name as driver_name
                    from route_log_cte
                    left join drivers_enriched on
                        route_log_cte.driver_id = drivers_enriched.id
                )

                /* Final order level table */
                    select
                        drivers_cte.id
                        , drivers_cte.hub_handover_id
                        , drivers_cte.order_id
                        , drivers_cte.route_id
                        , order_pickups.success_route_hub_id as pickup_hub_id
                        , drivers_cte.driver_id
                        , drivers_cte.driver_name
                        , drivers_cte.driver_type
                        , drivers_cte.tracking_id
                        , drivers_cte.created_at
                        , drivers_cte.deleted_at
                        , drivers_cte.updated_at
                        , drivers_cte.created_month
                        from drivers_cte
                        left join order_pickups as order_pickups on
                            drivers_cte.order_id = order_pickups.order_id
                """,
                jinja_arguments={
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).POH_ORDER_METRICS,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
