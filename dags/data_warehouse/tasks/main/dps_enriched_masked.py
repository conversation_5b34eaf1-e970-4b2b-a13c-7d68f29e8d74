import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
    depends_on=(data_warehouse.DPDAG.Task.DP_PARTNERS_ENRICHED_MASKED,),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_PARTNERS_ENRICHED,
                view_name="dp_partners_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DPProdGL(input_env, is_masked).DPS, view_name="dps"),
            base.InputTable(path=delta_tables.GDrive(input_env).DPS_ENRICHED, view_name="dps_enriched_gdrive"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    dps.id
                    , dps.partner_id
                    , partners.dpms_partner_id
                    , partners.name as partner_name
                    , partners.send_notifications_to_customer
                    , partners.driver_flow
                    , partners.creation_datetime as partner_creation_datetime
                    , partners.is_deleted
                    , dps.dpms_id
                    , dps.global_shipper_id as shipper_id
                    , dps.hub_id
                    , dps.external_store_id
                    , dps.type
                    , dps.can_shipper_lodge_in
                    , dps.allow_shipper_send
                    , dps.allow_create_post
                    , dps.allow_create_pack
                    , dps.allow_customer_return
                    , dps.can_customer_collect
                    , dps.allow_bulk_process_parcel
                    , dps.allow_view_order_events_history
                    , dps.is_hyperlocal
                    , dps.allow_cod_service
                    , dps.allow_view_insights
                    , dps.driver_collection_mode
                    , dps.max_parcel_stay_duration
                    , dps.name
                    , dps.short_name
                    , dps.contact
                    , dps.floor_number
                    , dps.unit_number
                    , dps.address_1
                    , dps.address_2
                    , dps.city
                    , dps.system_id as country
                    , dps.postal_code
                    , dps.latitude
                    , dps.longitude
                    , dps.directions
                    , dps.actual_max_capacity
                    , dps.computed_max_capacity
                    , dps.is_active
                    , dps.is_public
                    , dps.packs_sold_here
                    , dps.created_at as creation_datetime
                    , get_json_object(dps.custom, '$.operatingHours') as operating_hours
                    , partners.dp_category
                    , gdrive.location_type
                    , gdrive.store_type
                    , dps.system_id
                    , date_format(dps.created_at, 'yyyy-MM') as created_month
                from dps
                left join dps_enriched_gdrive as gdrive on
                    dps.id = gdrive.id
                join dp_partners_enriched as partners on
                    dps.partner_id = partners.id
                where dps.deleted_at is null
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DPS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
