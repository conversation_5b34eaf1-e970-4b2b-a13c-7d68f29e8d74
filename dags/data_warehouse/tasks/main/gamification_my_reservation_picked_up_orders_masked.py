import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS_MASKED,
    system_ids=(constants.SystemID.MY,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_TRACKING_IDS,
                view_name="proof_tracking_ids",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_JOBS,
                view_name="proof_jobs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOF_RESERVATIONS,
                view_name="proof_reservations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PROOFS,
                view_name="proofs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with proofs_mapping as (
                    select
                        proof_id
                        , job_id as reservation_id
                        , if(job_type = 'PUDO_PICKUP_APPOINTMENT', 'Pudo Pickup Appointment Job'
                            , 'Pickup Appointment Job') as data_source
                        , created_month
                        , system_id
                    from proof_jobs
                    where system_id = '{{ system_id }}'
                    union all
                    select 
                        proof_id
                        , reservation_id
                        , 'Reservation' as data_source
                        , created_month
                        , system_id
                    from proof_reservations
                    where system_id = '{{ system_id }}'
                ),

                picked_up_orders_cte as (
                    select
                        proofs_mapping.reservation_id
                        , proofs_mapping.data_source
                        , proofs_mapping.system_id
                        , proofs_mapping.created_month
                        , count(distinct proof_tracking_ids.tracking_id) filter(where proofs.status = 'Success') as picked_up_orders
                    from proofs_mapping
                    left join proofs
                        on proofs_mapping.proof_id = proofs.id
                    left join proof_tracking_ids
                        on proofs_mapping.proof_id = proof_tracking_ids.proof_id
                    group by 1,2,3,4
                )

                select 
                    * 
                from picked_up_orders_cte
                """,
                jinja_arguments={"system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
