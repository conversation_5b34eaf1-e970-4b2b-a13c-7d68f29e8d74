import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ShippersDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES_BASE_MASKED + ".py",
    task_name=data_warehouse.ShippersDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES_BASE_MASKED,
    depends_on=(data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.GL,),
)

MIN_AGE = 0
MIN_DATE = "2018-01-01"


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            shippers.sf_parent_acc_id_coalesce as parent_id
                            , shipper_vol.system_id
                            , shipper_vol.completion_date
                            , sum(shipper_vol.total_orders) as total_orders
                        from shipper_completion_vol_daily as shipper_vol
                        left join shipper_attributes as shippers on
                            shipper_vol.shipper_id = shippers.id
                        where shippers.sf_parent_acc_id_coalesce is not null
                        group by 1, 2, 3

                    )
                    , daily_orders as (

                        select
                            parent_id
                            , system_id
                            , completion_date
                            -- Exclude first day when calculating average daily orders to estimate monetary value
                            , if(
                                completion_date = min(completion_date) over (partition by parent_id, system_id)
                                , null
                                , total_orders
                            ) as total_orders
                        from base
                        where
                            completion_date >= '{{ min_date }}'

                    )
                    , shipper_agg as (

                        select
                            parent_id
                            , system_id
                            , date(
                                from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }})
                            ) - interval 1 day as report_date
                            , min(completion_date) as first_order_completion_date
                            , max(completion_date) as last_order_completion_date
                            , count(1) as completion_days
                            , coalesce(avg(total_orders), 0) as monetary_value
                        from daily_orders
                        group by 1, 2, 3

                    )
                    , final as (

                        -- Based on https://lifetimes.readthedocs.io/en/latest/Quickstart.html#the-shape-of-your-data
                        select
                            parent_id
                            , report_date
                            , completion_days - 1 as frequency
                            , datediff(last_order_completion_date, first_order_completion_date) as recency
                            , datediff(report_date, first_order_completion_date) as age
                            , monetary_value
                            , system_id
                        from shipper_agg
                        where
                            datediff(report_date, first_order_completion_date) >= {{ min_age }}

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "measurement_datetime_utc": measurement_datetime,
                    "min_date": MIN_DATE,
                    "min_age": MIN_AGE,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARENT_SHIPPER_LIFETIME_VALUES_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
