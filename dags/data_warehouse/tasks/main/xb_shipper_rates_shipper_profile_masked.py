import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
        py_file=data_warehouse.CrossBorderDAG.Task.XB_SHIPPER_RATES_SHIPPER_PROFILE_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_SHIPPER_RATES_SHIPPER_PROFILE_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=tuple()),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).XB_SHIPPER_RATES_SHIPPER_PROFILE, view_name="shipper_profile"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipper_profile",
                jinja_template="""
                select profile_id
                    ,global_shipper_id
                    ,shipper_name
                    ,origin_country
                    ,destination_country
                    ,service_type
                    ,service_tax_inclusive
                    ,tax_rate
                from shipper_profile
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_SHIPPER_RATES_SHIPPER_PROFILE,
        measurement_datetime=measurement_datetime,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()