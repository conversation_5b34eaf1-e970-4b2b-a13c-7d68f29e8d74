import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsMonthlyDAG.Task.RTS_RATES_EXCLUDED_SHIPPERS_MASKED + ".py",
    task_name=data_warehouse.OpsMonthlyDAG.Task.RTS_RATES_EXCLUDED_SHIPPERS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.RTS_RATES_KPI_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

REPORT_START_DATE_VN = "2022-01-01"
REPORT_START_DATE_PH = "2024-01-01"
REPORT_START_DATE_MY = "2024-09-01"


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 8, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RTS_RATES_KPI,
                view_name="rts_rates_kpi",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="count_working_days",
                jinja_template="""
                    select
                        date_trunc('month', date) as month 
                        , count(*) as count_working_day
                    from calendar
                    where true 
                        and working_day = 1
                        and date >= date_format('{{ report_start_date }}', 'yyyy-MM-dd')
                        and date < date(from_utc_timestamp('{{measurement_datetime}}', {{ local_timezone }}))
                        and system_id = 'vn'
                    group by 1 
                """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE_VN,
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="count_adjusted_working_days",
                jinja_template="""
                    select
                        date_trunc('month', cal0.date) as month 
                        , cal0.date as onboarded_date
                        , cal1.working_day_cum - cal0.working_day_cum + 1 as previous_month_count_working_day
                        , cal2.working_day_cum - cal0.working_day_cum + 1 as current_month_count_working_day
                    from calendar as cal0
                    join calendar as cal1 
                        on last_day(cal0.date) = cal1.date
                    join calendar as cal2 
                        on cal2.date = date(from_utc_timestamp(
                            '{{measurement_datetime}}', {{ local_timezone }})) - interval '1' day
                    where 
                        cal0.date >= date_format('{{ report_start_date }}', 'yyyy-MM-dd')
                        and cal0.date < date(from_utc_timestamp('{{measurement_datetime}}', {{ local_timezone }}))
                        and cal0.system_id = 'vn'
                        and cal1.system_id = 'vn'
                        and cal2.system_id = 'vn'
                    """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE_VN,
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": util.get_local_timezone("cal0.system_id"),
                },
            ),
            base.TransformView(
                view_name="vn_excluded_shippers",
                jinja_template="""
                with shipper_completion_vol_daily AS (

                    select
                        date_trunc('month', completion_date) as completion_month 
                        , shipper_completion_vol_daily.shipper_id as shipper_id
                        , shipper_completion_vol_daily.shipper_name
                        , shipper_attributes.onboarded_date
                        , case 
                            when date_trunc('month', shipper_completion_vol_daily.completion_date)
                                = date_trunc('month', onboarded_date) 
                            then 1 else 0 end as newly_onboard_flag
                        , shipper_completion_vol_daily.system_id
                        , sum(shipper_completion_vol_daily.total_orders) as total_orders
                        , sum(shipper_completion_vol_daily.rts_orders) as rts_orders 
                    from shipper_completion_vol_daily 
                    join shipper_attributes
                        on shipper_completion_vol_daily.shipper_id = shipper_attributes.id
                        and shipper_attributes.system_id = 'vn'
                    where true 
                        and shipper_completion_vol_daily.created_month >= '2022-09'
                        and shipper_completion_vol_daily.created_month 
                            < date_format(date(from_utc_timestamp('{{measurement_datetime}}'
                                , {{ local_timezone }})), 'yyyy-MM') 
                        and shipper_completion_vol_daily.parent_id_coalesce not in (702985, 6778709)  
                        and shipper_completion_vol_daily.system_id = 'vn'
                    group by {{ range(1, 7) | join(',') }}           

                )

                , prep as (

                    select 
                        shipper_completion_vol_daily.* 
                        , case 
                            when shipper_completion_vol_daily.newly_onboard_flag = 1 then 
                                case 
                                    when shipper_completion_vol_daily.completion_month 
                                        = date_trunc('month', date(from_utc_timestamp(
                                        '{{measurement_datetime}}', {{ local_timezone }})
                                        ) - interval '1' day)
                                    then count_adjusted_working_days.current_month_count_working_day 
                                    else count_adjusted_working_days.previous_month_count_working_day 
                                end 
                            else count_working_days.count_working_day
                        end as working_day_count
                        , case 
                            when shipper_completion_vol_daily.newly_onboard_flag = 1 then 
                                case
                                    when shipper_completion_vol_daily.completion_month
                                        = date_trunc('month', date(from_utc_timestamp(
                                            '{{measurement_datetime}}', {{ local_timezone }})) - interval '1' day) 
                                    then shipper_completion_vol_daily.total_orders 
                                        / count_adjusted_working_days.current_month_count_working_day 
                                    else shipper_completion_vol_daily.total_orders
                                        / count_adjusted_working_days.previous_month_count_working_day 
                                end
                            else shipper_completion_vol_daily.total_orders / count_working_days.count_working_day
                        end as ppd
                        , shipper_completion_vol_daily.rts_orders 
                            / shipper_completion_vol_daily.total_orders AS rts_rate
                    from shipper_completion_vol_daily 
                    left join count_working_days 
                        on shipper_completion_vol_daily.completion_month = count_working_days.month 
                    left join count_adjusted_working_days 
                        on shipper_completion_vol_daily.onboarded_date = count_adjusted_working_days.onboarded_date
                )

                , final as (

                    select 
                        shipper_id
                        , date(completion_month) exclusion_month_start
                        , last_day(completion_month) exclusion_month_end
                        , date_format(completion_month, 'yyyy-MM') created_month
                        , system_id
                   from prep
                   where 
                       (ppd >=20 AND rts_rate>=0.6) 
                       or (ppd >= 30 AND rts_rate >= 0.3)
                       or (ppd >= 60 AND rts_rate >= 0.25)

                )

                select 
                    * 
                from final
                """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE_VN,
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": util.get_local_timezone("shipper_completion_vol_daily.system_id")
                },
            ),
            base.TransformView(
                view_name="ph_my_excluded_shippers",
                jinja_template="""
                    with base as (
                        select
                            system_id
                            , shipper_id
                            , date_trunc('month', evaluation_datetime) exclusion_month_start
                            , round(sum(rts_flag) *100/ count(*)) rts_rate
                        from rts_rates_kpi
                        where 1=1
                            and ((system_id = 'ph'
                                and date(evaluation_datetime) >= date_format('{{ report_start_date_ph }}', 'yyyy-MM-dd')
                                )
                                or
                                (system_id = 'my'
                                and date(evaluation_datetime) >= date_format('{{ report_start_date_my }}', 'yyyy-MM-dd')
                                ))
                            and date_format(evaluation_datetime,'yyyy-MM')
                                < date_format(
                                    from_utc_timestamp('{{measurement_datetime}}', {{ local_timezone }})
                                    , 'yyyy-MM'
                                )
                            and (system_id = 'ph'
                                and (
                                sales_channel = 'Field Sales'
                                or (parent_id = 341153 and shipper_id != 341153)
                                )
                                or 
                                (system_id = 'my' and sales_channel = 'Field Sales')
                                )
                        group by {{ range(1, 4) | join(',') }}  
                    )

                    select
                        shipper_id
                        , date(exclusion_month_start) as exclusion_month_start
                        , last_day(exclusion_month_start) as exclusion_month_end
                        , date_format(date(exclusion_month_start), 'yyyy-MM') as created_month
                        , system_id
                    from base
                    where (rts_rate > 20 and system_id = 'ph')
                    -- Exclude shippers with rts rate greater or equal to 20 for my
                    or (rts_rate >= 20 and system_id = 'my')
                    """,
                jinja_arguments={
                    "report_start_date_ph": REPORT_START_DATE_PH,
                    "report_start_date_my": REPORT_START_DATE_MY,
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": util.get_local_timezone("system_id")
                },
            ),
            base.TransformView(
                view_name="vn_ph_excluded_shippers",
                jinja_template="""
                select 
                    *
                from vn_excluded_shippers
                union all
                select 
                    *
                from ph_my_excluded_shippers                
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).RTS_RATES_EXCLUDED_SHIPPERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
