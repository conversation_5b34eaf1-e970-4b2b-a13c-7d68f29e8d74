import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ZendeskDAG.Task.ZENDESK_TICKETS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.ZendeskDAG.Task.ZENDESK_TICKETS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.LookBackRange(None, None)
    if not enable_full_run:
        lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
        input_range = base.CreatedMonthRange(
            min=lookback_ranges.input.min, max=lookback_ranges.input.max, field="nv_created_month"
        )
        lookback_ranges = base.LookBackRange(input=input_range, output=lookback_ranges.output)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.Zendesk(input_env, is_masked).BRANDS, view_name="brands"),
            base.InputTable(path=delta_tables.Zendesk(input_env, is_masked).GROUPS, view_name="groups"),
            base.InputTable(path=delta_tables.Zendesk(input_env, is_masked).ORGANIZATIONS, view_name="organizations"),
            base.InputTable(
                path=delta_tables.Zendesk(input_env, is_masked).TICKETS, view_name="tickets", input_range=lookback_ranges.input
            ),
            base.InputTable(path=delta_tables.Zendesk(input_env, is_masked).TICKET_FORMS, view_name="ticket_forms"),
            base.InputTable(
                path=delta_tables.Zendesk(input_env, is_masked).TICKET_METRIC_EVENTS,
                view_name="ticket_metric_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.Zendesk(input_env, is_masked).USERS, view_name="users"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="tickets_updated",
                jinja_template="""
                select
                    *
                    , map_from_entries(
                        filter(
                            from_json(fields, 'array<struct<id:bigint, value:string>>')
                            , x -> x.value is not null
                        )
                    ) as fields_formatted
                from tickets
                """,
            ),
            base.TransformView(
                view_name="metrics",
                jinja_template="""
                select
                    system_id
                    , ticket_id
                    , min(time) as first_reply_datetime_utc
                from ticket_metric_events
                where
                    metric = 'reply_time'
                    and type = 'fulfill'
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    tickets.id
                    , tickets.external_id
                    , get_json_object(tickets.via, '$.channel') as via_channel
                    , get_json_object(tickets.via, '$.source.from.address') as via_source_from_address
                    , get_json_object(tickets.via, '$.source.from.name') as via_source_from_name
                    , get_json_object(tickets.via, '$.source.to.address') as via_source_to_address
                    , get_json_object(tickets.via, '$.source.to.name') as via_source_to_name
                    , get_json_object(tickets.via, '$.source.rel') as via_source_rel
                    , from_utc_timestamp(tickets.created_at, {{ get_local_timezone }}) as created_at
                    , from_utc_timestamp(tickets.updated_at, {{ get_local_timezone }}) as updated_at
                    , tickets.type
                    , tickets.subject
                    , tickets.description
                    , tickets.priority
                    , tickets.status
                    , tickets.recipient
                    , tickets.requester_id
                    , requesters.email as requester_email
                    , tickets.submitter_id
                    , submitters.email as submitter_email
                    , submitters.role as submitter_role
                    , tickets.assignee_id
                    , assignees.email as assignee_email
                    , tickets.organization_id
                    , organizations.name as organization_name
                    , tickets.group_id
                    , groups.name as group_name
                    , tickets.collaborator_ids
                    , tickets.follower_ids
                    , tickets.email_cc_ids
                    , tickets.forum_topic_id
                    , tickets.problem_id
                    , tickets.has_incidents
                    , tickets.is_public
                    , tickets.due_at as due_at_utc
                    , get_json_object(tickets.satisfaction_rating, '$.score') as satisfaction_rating_score
                    , get_json_object(tickets.satisfaction_rating, '$.id') as satisfaction_rating_id
                    , get_json_object(tickets.satisfaction_rating, '$.comment') as satisfaction_rating_comment
                    , get_json_object(tickets.satisfaction_rating, '$.reason') as satisfaction_rating_reason
                    , get_json_object(tickets.satisfaction_rating, '$.reason_id') as satisfaction_rating_reason_id
                    , tickets.sharing_agreement_ids
                    , tickets.followup_ids
                    , tickets.ticket_form_id
                    , ticket_forms.name as ticket_form_name
                    , tickets.brand_id
                    , brands.name as brand_name
                    , tickets.allow_channelback
                    , tickets.allow_attachments
                    , tickets.fields_formatted

                    , upper(
                        case
                            when tickets.system_id = 'id' then tickets.fields_formatted[360012850692]
                            when tickets.system_id = 'my' then tickets.fields_formatted[360012107212]
                            when tickets.system_id = 'ph' then tickets.fields_formatted[360009759431]
                            when tickets.system_id = 'sg' then tickets.fields_formatted[360008431432]
                            when tickets.system_id = 'th' then tickets.fields_formatted[360015803192]
                            when tickets.system_id = 'vn' then tickets.fields_formatted[360009915772]
                        end
                    ) as fields_formatted_tracking_id

                    , case
                        when tickets.system_id = 'ph' then tickets.fields_formatted[360018344532]
                      end as fields_formatted_shipper_consignee

                    , case
                        when tickets.system_id = 'ph' then tickets.fields_formatted[360007407592]
                        when tickets.system_id = 'vn' then tickets.fields_formatted[360007432291]
                      end as fields_formatted_channel

                    , case
                        when tickets.system_id = 'ph' then tickets.fields_formatted[360034373851]
                      end as fields_formatted_new_type_of_issues

                    , case
                        when tickets.system_id = 'ph' then tickets.fields_formatted[360020203092]
                      end as fields_formatted_shipper_type

                    , case
                        when tickets.system_id = 'ph' then tickets.fields_formatted[900011565846]
                      end as fields_formatted_new_current_hub

                    , tickets.tags
                    , from_utc_timestamp(metrics.first_reply_datetime_utc, {{ get_local_timezone }})
                        as first_reply_datetime
                    , tickets.system_id
                    , date_format(
                        from_utc_timestamp(tickets.created_at, {{ get_local_timezone }}), "yyyy-MM"
                    ) as created_month
                from tickets_updated as tickets
                left join users as requesters on
                    tickets.requester_id = requesters.id
                    and tickets.system_id = requesters.system_id
                left join users as submitters on
                    tickets.submitter_id = submitters.id
                    and tickets.system_id = submitters.system_id
                left join users as assignees on
                    tickets.assignee_id = assignees.id
                    and tickets.system_id = assignees.system_id
                left join organizations on
                    tickets.organization_id = organizations.id
                    and tickets.system_id = organizations.system_id
                left join groups on
                    tickets.group_id = groups.id
                    and tickets.system_id = groups.system_id
                left join ticket_forms on
                    tickets.ticket_form_id = ticket_forms.id
                    and tickets.system_id = ticket_forms.system_id
                left join brands on
                    tickets.brand_id = brands.id
                    and tickets.system_id = brands.system_id
                left join metrics on
                    tickets.id = metrics.ticket_id
                    and tickets.system_id = metrics.system_id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("tickets.system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ZENDESK_TICKETS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    # Disabled dynamicPartitionPruning because it was causing the query optimizer to not perform skewed joins with
    # users table.
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
