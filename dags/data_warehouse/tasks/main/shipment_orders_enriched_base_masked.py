import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_BASE_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_BASE_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_DIMENSIONS_MASKED
        ),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DIMENSIONS,
                view_name="order_dimensions",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SCANS, 
                view_name="scans", 
                input_range=lookback_ranges.input
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_ORDERS,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="add_to_shipment",
                jinja_template="""
                SELECT shipment_id
                       , order_id
                       , add_to_shipment_datetime
                       , system_id
                FROM
                    (SELECT shipment_id
                       , order_id
                       , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS add_to_shipment_datetime
                       , lower(order_country) AS system_id
                       , row_number() over(PARTITION BY shipment_id, order_id
                                              ORDER BY created_at) AS rank
                    FROM shipment_orders
                    WHERE deleted_at IS NULL)
                WHERE rank = 1
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("order_country")},
            ),
            base.TransformView(
                view_name="last_add_to_shipment",
                jinja_template="""
                SELECT shipment_id
                       , order_id
                       , last_add_to_shipment_datetime
                       , system_id
                FROM
                    (SELECT shipment_id
                       , order_id
                       , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS last_add_to_shipment_datetime
                       , lower(order_country) AS system_id
                       , row_number() over(PARTITION BY shipment_id, order_id
                                              ORDER BY created_at desc) AS rank
                    FROM shipment_orders
                    WHERE deleted_at IS NULL)
                WHERE rank = 1
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("order_country")},
            ),
            base.TransformView(
                view_name="inbound",
                jinja_template="""
                SELECT shipment_id
                       , order_id
                       , order_inbound_datetime
                       , system_id
                FROM
                  (SELECT shipment_id
                          , order_id
                          , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS order_inbound_datetime
                          , lower(hub_country) AS system_id
                          , row_number() over(PARTITION BY shipment_id, order_id
                                              ORDER BY created_at) AS rank
                   FROM scans
                   WHERE deleted_at IS NULL
                     AND source = 'ORDER_INBOUND')
                WHERE rank = 1
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("hub_country")},
            ),
            base.TransformView(
                view_name="del_from_shipment",
                jinja_template="""
                SELECT shipment_id
                       , order_id
                       , del_from_shipment_datetime
                       , system_id
                FROM
                  (SELECT shipment_id
                          , order_id
                          , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS del_from_shipment_datetime
                          , lower(hub_country) AS system_id
                          , row_number() over(PARTITION BY shipment_id, order_id
                                              ORDER BY created_at desc) AS rank
                   FROM scans
                   WHERE deleted_at IS NULL
                    AND source = 'ORDER_DEL_FROM_SHIPMENT')
                WHERE rank = 1
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("hub_country")},
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                SELECT coalesce(add_to_shipment.shipment_id, inbound.shipment_id) AS shipment_id
                       , coalesce(add_to_shipment.order_id, inbound.order_id) AS order_id
                       , add_to_shipment.add_to_shipment_datetime
                       , last_add_to_shipment.last_add_to_shipment_datetime
                       , inbound.order_inbound_datetime
                       , del_from_shipment.del_from_shipment_datetime
                       , coalesce(add_to_shipment.system_id, inbound.system_id) AS system_id
                FROM add_to_shipment
                FULL JOIN inbound ON add_to_shipment.shipment_id = inbound.shipment_id
                    AND add_to_shipment.order_id = inbound.order_id
                LEFT JOIN del_from_shipment ON add_to_shipment.shipment_id = del_from_shipment.shipment_id
                    AND add_to_shipment.order_id = del_from_shipment.order_id
                LEFT JOIN last_add_to_shipment ON add_to_shipment.shipment_id = last_add_to_shipment.shipment_id
                    AND add_to_shipment.order_id = last_add_to_shipment.order_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.shipment_id
                       , base.order_id
                       , base.add_to_shipment_datetime
                       , base.last_add_to_shipment_datetime
                       , base.order_inbound_datetime
                       , base.del_from_shipment_datetime
                       , orders.parcel_size
                       , orders.weight
                       , orders.nv_weight
                       , order_dimensions.estimated_weight
                       , order_dimensions.estimated_volume
                       , base.system_id
                       , date_format(shipments.created_at, 'yyyy-MM') AS created_month
                FROM base
                LEFT JOIN orders_enriched AS orders ON orders.order_id = base.order_id
                    AND orders.system_id = base.system_id
                LEFT JOIN order_dimensions ON order_dimensions.order_id = base.order_id
                    AND order_dimensions.system_id = base.system_id
                LEFT JOIN shipments ON shipments.id = base.shipment_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_ORDERS_ENRICHED_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
