import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    # input_env = "prod"
    is_masked = True
    input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).TIKTOK_INTERNAL_TICKET_DATA,
                            view_name="tiktok_internal_ticket_data"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select 
                    cast(Ticket_No as bigint) as ticket_id            
                    , Company_Name as company_name
                    , Country as country
                    , Ticket_Type as ticket_type
                    , Status as status
                    , TID as tracking_id
                    , to_timestamp(Created_date,'MM/dd/yyyy HH:mm:ss') as create_time
                    , cast(Reopened_No as int) as response_number
                    , cast(Correspondence_No as int) as correspondence_number
                    , Owner as owner
                    , to_timestamp(Last_Response_Time,'MM/dd/yyyy HH:mm:ss') as last_reponse_time
                    , to_timestamp(First_Response_Time,'MM/dd/yyyy HH:mm:ss') as first_reponse_time
                    , to_timestamp(Last_close,'MM/dd/yyyy HH:mm:ss') as last_close_time
                    , to_timestamp(Expiration_Date,'MM/dd/yyyy HH:mm:ss') as expiration_date
                    , Due as due
                    , Close_by as close_by
                    , cast(Satisfaction as double) as satisfication
                    , Feedback as feedback
                    , to_date(Input_Date, 'MM/dd/yyyy') as created_date
                from tiktok_internal_ticket_data
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_date",),
        # write_mode='merge',
        # primary_keys=["ticket_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()