import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.LIQUIDATION_PARCELS_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.LIQUIDATION_PARCELS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.VALID_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.FIRST_TERMINAL_STATUS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_THIRD_PARTY_TRANSFERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_DP_MILESTONES_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).VALID_SCAN_EVENTS,
                view_name="valid_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_TERMINAL_STATUS_EVENTS,
                view_name="terminal_status_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_THIRD_PARTY_TRANSFERS,
                view_name="order_third_party_transfers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DP_MILESTONES,
                view_name="order_dp_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENT_TYPES,
                view_name="order_event_types",
            ),
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
        ),
        version_datetime=measurement_datetime
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="valid_scan_events_enriched",
                jinja_template="""
                select
                    scan.order_id
                    , scan.type
                    , scan.scan_hub_id
                    , scan.driver_id
                    , if(scan.type in (24,25), scan.driver_id, scan.user_id) as user_id
                    , if(scan.type in (24,25), 'driver_id', 'aaa_user_id') as user_id_type
                    , if(scan.type in (24,25)
                        , drivers.display_name
                        , concat(user_info.first_name, ' ', user_info.last_name)
                    ) as user_name
                    , scan.valid_scan_datetime
                    , lag(scan.valid_scan_datetime) over(
                        partition by order_id order by valid_scan_datetime
                    ) as previous_valid_scan_datetime
                from valid_scan_events as scan
                left join user_info as user_info
                    on scan.user_id = user_info.user_id
                left join drivers_enriched as drivers
                    on scan.driver_id = drivers.id
                where scan.type in (8, 24, 25, 26, 27, 28, 58)
                """,
            ),
            base.TransformView(
                view_name="last_delivery_success",
                jinja_template="""
                select
                    order_id
                    , max(event_datetime) as last_delivery_success_datetime
                    , max_by(type, event_datetime) as last_delivery_success_type
                from terminal_status_events
                where type in (3, 37)
                group by 1
                """,
            ),
            base.TransformView(
                view_name="adjusted_hubs_enriched",
                jinja_template="""
                with base as (
                
                    select
                        id
                        , name
                        , if(upper(name) like '%RECOVERY%'
                            or upper(name) like '%REC-%'
                            or upper(name) like '%REC -%'
                            or facility_type = 'RECOVERY'
                               , 1
                               , 0
                        ) as recovery_hub_flag
                        , case
                            when system_id = 'id' and id = 1389 then 1
                            when system_id = 'sg' and id = 105159 then 1
                            when system_id = 'my' and id in (60, 544) then 1
                            when system_id = 'ph' and id = 767 then 1
                            when system_id in ('vn', 'th') 
                                and (upper(name) like '%RECOVERY%'
                                    or upper(name) like '%REC-%'
                                    or upper(name) like '%REC -%'
                                    or facility_type = 'RECOVERY')
                            then 1
                            else 0
                        end as liquidation_hub_flag
                    from hubs_enriched
                
                )
                
                select
                    *
                    , case
                        when liquidation_hub_flag = 1 then 'liquidation_hub'
                        when recovery_hub_flag = 1 then 'recovery_hub'
                        else 'normal_hub'
                    end as hub_categorisation
                from base
                """,
            ),
            base.TransformView(
                view_name="pets_base",
                jinja_template="""
                with pets_base as (
                
                    select
                        order_id
                        , id
                        , type
                        , sub_type
                        , outcome
                        , investigating_hub_id
                        , investigating_hub_name
                        , resolution_datetime
                        , creation_datetime
                        , last_scan_before_creation_datetime
                        , case
                            when outcome in (
                                'LOST - NO RESPONSE - DECLARED'
                                , 'LOST - DECLARED'
                                , 'MISSING - NV LIABLE'
                                , 'LOST RECEIPT'
                            ) then 1
                            else 0
                        end as lost_flag
                        , case
                            when outcome in (
                                'NV LIABLE - PARCEL DISPOSED'
                                , 'NV NOT LIABLE - PARCEL DISPOSED'
                                , 'CANCEL - NINJA DID NOT RECEIVE'
                                , 'LOST - NO RESPONSE - DECLARED'
                                , 'LOST - DECLARED'
                                , 'NV NOT LIABLE - ITEM MISSING'
                                , 'CONFISCATED BY CUSTOMS'
                                , 'DAMAGED - NV LIABLE'
                                , 'MISSING - NV LIABLE'
                                , 'SLA BREACH - NV LIABLE'
                                , 'LOST RECEIPT'
                                , 'NV LIABLE - XMAS CAGE'
                                , 'NV LIABLE - XMAS CAGE (TIKTOK)'
                                , 'NV NOT LIABLE - PARCEL SCRAPPED'
                                , 'PARCEL SCRAPPED'
                            ) then 1
                            else 0
                        end as preceding_cancel_flag
                        , case
                            when outcome in ('XMAS CAGE', 'NV LIABLE - XMAS CAGE', 'NV LIABLE - XMAS CAGE (TIKTOK)') then 1
                            else 0
                        end as xmas_ticket_flag
                    from pets_tickets_enriched
                
                )
                
                , base as (

                    select
                        base.order_id
                        , orders.tracking_id
                        , orders.status
                        , orders.cod_value
                        , orders.insurance_value
                        , orders.created_month
                    {%- for col in columns_to_adjust %}
                        , max_by(base.{{ col }}, base.resolution_datetime)
                            filter(where base.lost_flag = 1) as last_order_lost_ticket_{{ col }}
                    {%- endfor %}
                    {%- for col in columns_to_adjust %}
                        , max_by(base.{{ col }}, base.resolution_datetime)
                            filter(where base.preceding_cancel_flag = 1) as last_order_cancellation_ticket_{{ col }}
                    {%- endfor %}
                        , min(base.creation_datetime)
                            filter (where base.xmas_ticket_flag = 1) as xmas_ticket_creation_datetime
                    {%- for col in columns_to_adjust %}
                        , min_by(base.{{ col }}, base.resolution_datetime)
                            filter(where base.xmas_ticket_flag = 1) as xmas_ticket_{{ col }}
                    {%- endfor %}
                    from pets_base base
                    join orders_enriched orders
                        on base.order_id = orders.order_id
                    group by {{ range(1, 7) | join(',') }}
                )
                
                select
                    *
                from base
                where
                    status = 'Cancelled'
                    or last_order_cancellation_ticket_outcome in ('NV NOT LIABLE - PARCEL SCRAPPED', 'PARCEL SCRAPPED')
                """,
                jinja_arguments={
                    "columns_to_adjust": (
                        "id",
                        "resolution_datetime",
                        "type",
                        "sub_type",
                        "outcome",
                        "investigating_hub_id",
                        "investigating_hub_name",
                        "last_scan_before_creation_datetime",
                    ),
                },
            ),
            base.TransformView(
                view_name="timestamps_adjustment",
                jinja_template="""
                with add_keep_flag as (
                
                    select
                        *
                        , case
                            when last_order_cancellation_ticket_type = 'SLA BREACH' 
                                and last_order_lost_ticket_resolution_datetime is not null
                            then 'lost_prioritised'
                            when last_order_cancellation_ticket_resolution_datetime is not null
                            then 'normal_last_cancellation'
                            when xmas_ticket_creation_datetime is not null
                            then 'xmas_ticket_cancellation'
                            else 'no_cancellation_ticket'
                        end as keep_condition
                    from pets_base

                )

                select
                    order_id
                    , tracking_id
                    , cod_value
                    , insurance_value
                    , keep_condition
                {%- for col in columns_to_adjust %}
                    , case keep_condition
                        when 'lost_prioritised' then last_order_lost_ticket_{{ col }}
                        when 'normal_last_cancellation' then last_order_cancellation_ticket_{{ col }}
                        when 'xmas_ticket_cancellation' then xmas_ticket_{{ col }}
                    end as last_order_cancellation_ticket_{{ col }} 
                {%- endfor %}
                    , xmas_ticket_resolution_datetime
                    , created_month
                from add_keep_flag
                """,
                jinja_arguments={
                    "columns_to_adjust": (
                        "id",
                        "resolution_datetime",
                        "type",
                        "sub_type",
                        "outcome",
                        "investigating_hub_id",
                        "investigating_hub_name",
                        "last_scan_before_creation_datetime",
                    ),
                },
            ),
            base.TransformView(
                view_name="add_movements",
                jinja_template="""
                select
                    base.order_id
                    , base.tracking_id
                    , base.cod_value
                    , base.insurance_value
                    , base.keep_condition
                    , base.last_order_cancellation_ticket_id
                    , base.last_order_cancellation_ticket_resolution_datetime
                    , base.last_order_cancellation_ticket_type
                    , base.last_order_cancellation_ticket_sub_type
                    , base.last_order_cancellation_ticket_outcome
                    , base.last_order_cancellation_ticket_investigating_hub_id
                    , base.last_order_cancellation_ticket_investigating_hub_name
                    , base.last_order_cancellation_ticket_last_scan_before_creation_datetime
                    , base.xmas_ticket_resolution_datetime
                    , base.created_month
                    , min(scan.valid_scan_datetime) as first_scan_datetime
                    , min_by(scan_hubs.id, scan.valid_scan_datetime) as first_scan_hub_id
                    , min_by(scan_hubs.name, scan.valid_scan_datetime) as first_scan_hub_name
                    , min_by(scan_hubs.hub_categorisation, scan.valid_scan_datetime) as first_scan_hub_category
                    , min_by(scan.previous_valid_scan_datetime, scan.valid_scan_datetime) as scan_datetime_preceding_first_scan
                    , min(scan.valid_scan_datetime) 
                        filter(where scan.type = 58 or scan_hubs.recovery_hub_flag = 1) as first_recovery_scan_datetime
                    , min_by(scan_hubs.id, scan.valid_scan_datetime)
                        filter(where scan.type = 58 or scan_hubs.recovery_hub_flag = 1) as first_recovery_scan_hub_id
                    , min_by(scan_hubs.name, scan.valid_scan_datetime)
                        filter(where scan.type = 58 or scan_hubs.recovery_hub_flag = 1) as first_recovery_scan_hub_name
                    , min(scan.valid_scan_datetime) 
                        filter(where scan_hubs.liquidation_hub_flag = 1) as liquidation_scan_datetime
                    , min_by(scan_hubs.id, scan.valid_scan_datetime)
                        filter(where scan_hubs.liquidation_hub_flag = 1) as liquidation_scan_hub_id
                    , min_by(scan_hubs.name, scan.valid_scan_datetime)
                        filter(where scan_hubs.liquidation_hub_flag = 1) as liquidation_scan_hub_name
                    , max(scan.valid_scan_datetime) as last_scan_datetime
                    , max_by(scan.user_id, scan.valid_scan_datetime) as last_scan_user_id
                    , max_by(scan.user_name, scan.valid_scan_datetime) as last_scan_username
                    , max_by(scan.user_id_type, scan.valid_scan_datetime) as last_scan_user_id_type
                    , max_by(scan.type, scan.valid_scan_datetime) as last_scan_type_id
                    , max_by(move.hub_id, move.entry_datetime) as last_hub_id
                    , max_by(move.location_type, move.entry_datetime) as last_location_type
                from timestamps_adjustment as base
                left join order_movements as move
                    on base.order_id = move.order_id
                    and move.location_type != 'OUT OF NV'
                left join valid_scan_events_enriched as scan
                    on base.order_id = scan.order_id
                    and scan.valid_scan_datetime > base.last_order_cancellation_ticket_resolution_datetime
                left join user_info as user
                    on scan.user_id = user.user_id
                left join adjusted_hubs_enriched as scan_hubs
                    on scan.scan_hub_id = scan_hubs.id
                group by {{ range(1, 16) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="set_reactivation",
                jinja_template="""
                select
                    base.*
                    , least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) as reactivation_datetime
                    , case
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) is null
                        then null
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.first_scan_datetime
                        then 'scan'
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.xmas_ticket_resolution_datetime
                        then 'xmas'
                        else 'others'
                    end as reactivation_type
                    , case
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) is null then null
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.first_scan_datetime
                            and base.first_scan_hub_category = 'liquidation_hub'
                        then 'liquidation_scan'
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.first_scan_datetime
                            and base.first_scan_datetime = base.first_recovery_scan_datetime
                        then 'recovery_scan'
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.first_scan_datetime
                        then 'ops_scan'
                        when least(base.first_scan_datetime, base.xmas_ticket_resolution_datetime) = base.xmas_ticket_resolution_datetime
                        then 'xmas'
                        else 'others'
                    end as granular_reactivation_type
                    , case
                        when base.last_order_cancellation_ticket_type = 'MISSING'
                            or base.last_order_cancellation_ticket_outcome = 'MISSING - NV LIABLE'
                        then 'lost'
                        when base.last_order_cancellation_ticket_type = 'SLA BREACH'
                        then 'sla_breach'
                        when base.last_order_cancellation_ticket_type = 'PARCEL EXCEPTION'
                            and last_order_cancellation_ticket_sub_type = 'MAXIMUM ATTEMPTS (RTS)'
                        then 'parcel_scrapped'
                        when base.last_order_cancellation_ticket_type = 'SELF COLLECTION'
                            and base.last_order_cancellation_ticket_outcome = 'PARCEL SCRAPPED'
                        then 'parcel_scrapped'
                        else 'not_in_scope'
                    end as liquidation_scope_type
                    , if(3pl_trf.event_datetime is not null, 1, 0) transferred_to_3pl_flag
                    , last_delivery_success.last_delivery_success_datetime
                    , last_delivery_success.last_delivery_success_type
                    , event_name.name as last_scan_type_name
                    , last_hubs.name as last_hub_name
                    , dp_milestones.driver_to_dp_datetime
                from add_movements as base
                left join last_delivery_success
                    on base.order_id = last_delivery_success.order_id
                left join hubs_enriched as last_hubs
                    on base.last_hub_id = last_hubs.id
                left join order_event_types as event_name
                    on base.last_scan_type_id = event_name.id
                left join order_third_party_transfers as 3pl_trf
                    on base.order_id = 3pl_trf.order_id
                left join order_dp_milestones as dp_milestones
                    on base.order_id = dp_milestones.order_id
                where
                    keep_condition != 'no_cancellation_ticket'
                """,
            ),
            base.TransformView(
                view_name="measured_flag",
                jinja_template="""
                select
                    *
                    , case
                        when transferred_to_3pl_flag = 1 then 0
                        when liquidation_scope_type = 'sla_breach' 
                            and last_delivery_success_datetime > last_order_cancellation_ticket_last_scan_before_creation_datetime 
                            and first_scan_datetime is null
                        then 0
                        when driver_to_dp_datetime > last_order_cancellation_ticket_last_scan_before_creation_datetime
                            and first_scan_datetime is null
                        then 0
                        when lower(last_order_cancellation_ticket_investigating_hub_name) like 'intl%' then 0
                        when liquidation_scope_type = 'not_in_scope' then 0    
                        when liquidation_scope_type = 'lost'
                            and first_scan_datetime is null
                        then 0
                        when driver_to_dp_datetime > first_scan_datetime then 0
                        when last_order_cancellation_ticket_last_scan_before_creation_datetime is null
                        then if(first_scan_datetime is null, 0, 1)
                        else 1
                    end as liquidation_measured
                from set_reactivation
                """,
            ),
            base.TransformView(
                view_name="met_flag",
                jinja_template="""
                select
                    *
                    , case
                        when liquidation_measured = 0 then null
                        when liquidation_scan_datetime is not null then 1
                        else 0
                    end as liquidation_met
                    , case
                        when liquidation_measured = 1 then null
                        when liquidation_scope_type = 'not_in_scope'
                        then 'out_of_scope_cancellation_ticket'
                        when liquidation_scope_type = 'sla_breach' 
                            and last_delivery_success_datetime > last_order_cancellation_ticket_last_scan_before_creation_datetime
                            and first_scan_datetime is null
                        then 'sla_breach_after_delivery'
                        when lower(last_order_cancellation_ticket_investigating_hub_name) like 'intl%' 
                        then 'intl_investigating_hub'
                        when transferred_to_3pl_flag = 1 then 'transferred_to_3pl'
                        when liquidation_scope_type = 'lost' 
                            and first_scan_datetime is null
                        then 'lost_not_reactivated'
                        when driver_to_dp_datetime > last_order_cancellation_ticket_last_scan_before_creation_datetime
                            and first_scan_datetime is null
                        then 'lost_in_dp'
                        when last_order_cancellation_ticket_last_scan_before_creation_datetime is null
                            and first_scan_datetime is null
                        then 'no_scan_before_ticket_creation'
                        else 'check'
                    end as exclusion_reason
                from measured_flag
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , tracking_id
                    , cod_value
                    , insurance_value
                    , last_order_cancellation_ticket_id
                    , last_order_cancellation_ticket_resolution_datetime
                    , last_order_cancellation_ticket_type
                    , last_order_cancellation_ticket_sub_type
                    , last_order_cancellation_ticket_outcome
                    , last_order_cancellation_ticket_investigating_hub_id
                    , last_order_cancellation_ticket_investigating_hub_name
                    , transferred_to_3pl_flag
                    , last_delivery_success_datetime
                    , last_delivery_success_type
                    , xmas_ticket_resolution_datetime
                    , keep_condition as cancellation_ticket_classification
                    , first_scan_datetime as first_scan_after_cancellation_datetime
                    , first_recovery_scan_datetime
                    , first_recovery_scan_hub_id
                    , first_recovery_scan_hub_name
                    , liquidation_scan_datetime
                    , liquidation_scan_hub_id
                    , liquidation_scan_hub_name
                    , last_scan_datetime
                    , last_scan_user_id
                    , last_scan_username
                    , last_scan_user_id_type
                    , last_scan_type_id
                    , last_scan_type_name
                    , if(last_scan_datetime is null, null, last_hub_id) as last_hub_id
                    , if(last_scan_datetime is null, null, last_hub_name) as last_hub_name
                    , if(last_scan_datetime is null, null, last_location_type) as last_location_type
                    , reactivation_datetime
                    , reactivation_type
                    , granular_reactivation_type
                    , liquidation_scope_type
                    , liquidation_measured
                    , liquidation_met
                    , exclusion_reason
                    , created_month
                from met_flag
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LIQUIDATION_PARCELS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
