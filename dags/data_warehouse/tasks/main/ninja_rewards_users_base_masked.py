import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_BASE_MASKED + ".py",
    task_name=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_BASE_MASKED,
    system_ids=(SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(base.InputTable(path=delta_tables.Eber(input_env, is_masked).USERS, view_name="users"),),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="users_clean",
                jinja_template="""
                    select
                        id
                        , display_name
                        , email
                        , cast(external_member_id as int) as shipper_id
                        , member
                        , points
                        , sub_members
                        , created_at
                        , system_id
                    from users
                    where
                        -- Remove dirty accounts that they are using for testing in production.
                        email not rlike '(@ninjavan.co|@eber.co)$'
                        or email is null
                    """,
            ),
            base.TransformView(
                view_name="parent_users",
                jinja_template="""
                select
                    id as parent_id
                    , shipper_id as parent_shipper_id
                    , explode(from_json(sub_members, 'array<struct<id: int>>').id) as user_id
                from users_clean
                where
                    sub_members <> '[]'
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    users_clean.id
                    , users_clean.display_name
                    , users_clean.email
                    , users_clean.shipper_id
                    , coalesce(parent_users.parent_id, users_clean.id) as parent_user_id_coalesce
                    , coalesce(
                        parent_users.parent_shipper_id, users_clean.shipper_id
                    ) as parent_user_shipper_id_coalesce

                    , get_json_object(member, '$.next_member_tier.name') as next_member_tier
                    , cast(
                        get_json_object(member, '$.next_member_tier_required_value') as int
                    ) as next_member_tier_required_points
                    , cast(get_json_object(member, '$.points_will_expire_in_7d') as int) as points_expiring_in_7d
                    , cast(get_json_object(member, '$.points_will_expire_in_14d') as int) as points_expiring_in_14d
                    , cast(get_json_object(member, '$.points_will_expire_in_30d') as int) as points_expiring_in_30d
                    , cast(get_json_object(member, '$.points_will_expire_in_60d') as int) as points_expiring_in_60d
                    , cast(get_json_object(member, '$.points_will_expire_in_90d') as int) as points_expiring_in_90d

                    , cast(get_json_object(points, '$[0].num_claimed_rewards') as int) as cumulative_total_redemptions
                    , cast(
                        get_json_object(points, '$[0].last_claimed_reward_at') as timestamp
                    ) as last_redemption_datetime
                    , cast(
                        get_json_object(points, '$[0].last_used_reward_at') as timestamp
                    ) as last_used_reward_datetime

                    , from_utc_timestamp(users_clean.created_at, {{ get_local_timezone }}) as join_datetime
                    , users_clean.system_id
                    , date_format(
                        from_utc_timestamp(users_clean.created_at, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                from users_clean
                left join parent_users on
                    users_clean.id = parent_users.user_id
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("users_clean.system_id"),
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_REWARDS_USERS_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
