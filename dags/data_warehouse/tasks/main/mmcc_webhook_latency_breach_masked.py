import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from datetime import timedelta
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_LATENCY_BREACH_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_LATENCY_BREACH_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_MASKED,
        data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_ENRICHED_MASKED,
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse",
                                     partition_columns=("system_id", "shipper", "holm_month")),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    latest_partition = "/measurement_datetime=latest/"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX,
                            view_name="prefix"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Read mmcc webhook order tables for orders holm-ed from the previous month
    holm_month = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m')
    holm_month_dt = dt.strptime(holm_month, '%Y-%m').date()
    start_month_dt = holm_month_dt + relativedelta(months=-1)
    start_month = start_month_dt.strftime('%Y-%m')

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK_ORDERS + latest_partition) \
        .filter(F.col("holm_month") >= start_month).createOrReplaceTempView("mmcc_webhook_orders")

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK_ORDERS_ENRICHED + latest_partition) \
        .filter(F.col("holm_month") >= start_month).createOrReplaceTempView("mmcc_webhook_orders_enriched")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="failed_transmission",
                jinja_template="""

               select
                    big_bag_no
                    , shipper
                    , total_parcel_items
                    , handed_over_to_last_mile_date
                    , holm_month
                    , status
                    , status_code
                    , first_event_time
                    , first_webhook_sent_time
                    , latency_sla
                    , latency_sla_met
                    , system_id
                from mmcc_webhook_orders_enriched
                where
                    first_webhook_sent_time is not null
                    and first_webhook_sent_time is not null
                    and latency_sla_met = 0

                """,
            ),
        ),
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK_LATENCY_BREACH,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "holm_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(input_args.env,
                                  input_args.last_measurement_datetime,
                                  input_args.measurement_datetime,
                                  input_args.enable_full_run,
                                  )
    run(spark, task_config)
    spark.stop()