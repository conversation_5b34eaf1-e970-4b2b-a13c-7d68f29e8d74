import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrderEventsDAG.Task.HUB_INBOUND_EVENTS_MASKED,
        data_warehouse.OrderEventsDAG.Task.PARCEL_SWEEPER_EVENTS_MASKED,
        data_warehouse.OrderEventsDAG.Task.RTS_TRIGGER_EVENTS_MASKED,
        data_warehouse.OrderEventsDAG.Task.ROUTE_INBOUND_EVENTS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).HUB_INBOUND_EVENTS,
                view_name="hub_inbound_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PARCEL_SWEEPER_EVENTS,
                view_name="parcel_sweeper_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).RTS_TRIGGER_EVENTS,
                view_name="rts_trigger_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ROUTE_INBOUND_EVENTS,
                view_name="route_inbound_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="event_base",
                jinja_template="""
                with filtered_route_inbounds as (
                
                    select
                        route_inbound_events.*
                    from route_inbound_events
                    left join order_milestones
                        on route_inbound_events.order_id = order_milestones.order_id
                    where
                        route_inbound_events.created_at < order_milestones.first_valid_delivery_attempt_datetime
                        or order_milestones.first_valid_delivery_attempt_datetime is null
                
                ),
                base as (

                    select system_id, order_id, hub_id, created_at as event_datetime, 'route_inbound' as type from filtered_route_inbounds
                    UNION ALL
                    select system_id, order_id, hub_id, created_at as event_datetime, 'hub_inbound' as type from hub_inbound_events
                    UNION ALL
                    select system_id, order_id, hub_id, created_at as event_datetime, 'parcel_sweeper' as type from parcel_sweeper_events
                    UNION ALL
                    select system_id, order_id, null as hub_id, event_datetime, 'rts' as type from rts_trigger_events

                ),
                hub_coalesce as (
                    select
                        child_hub.system_id
                        , child_hub.id
                        , coalesce(parent_hub.region, child_hub.region) as region
                        , coalesce(parent_hub.facility_type, child_hub.facility_type) as facility_type
                        , coalesce(parent_hub.id, child_hub.id) as coalesce_hub_id
                        , coalesce(parent_hub.name, child_hub.name) as coalesce_hub_name
                    from hubs_enriched as child_hub
                    left join hubs_enriched as parent_hub
                        on child_hub.system_id = parent_hub.system_id
                        and child_hub.parent_hub_id = parent_hub.id
                    
                ),
                final as (

                    select
                        base.*
                        , hub_coalesce.region
                        , hub_coalesce.facility_type
                        , hub_coalesce.coalesce_hub_id
                        , hub_coalesce.coalesce_hub_name
                    from base
                    left join hub_coalesce
                        on base.system_id = hub_coalesce.system_id
                        and base.hub_id = hub_coalesce.id

                )

                select * from final
                
                """,
            ),
            base.TransformView(
                view_name="logic",
                jinja_template="""
                with base as (

                    select
                        *
                        , lag(coalesce_hub_id) over (partition by order_id order by event_datetime) as prev_hub
                    from event_base
                    where facility_type != 'RECOVERY' or type = 'rts'


                ),
                final as (

                    select
                        *
                        , case
                            when (type != 'rts') and (coalesce_hub_id != prev_hub or prev_hub is null) then 1
                            else 0
                            end as keep_flag
                    from base
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with final as (

                    select
                        logic.*
                        , order_milestones.parcel_size
                        , date_format(event_datetime, 'yyyy-MM') as created_month
                    from logic
                    left join order_milestones
                        on logic.system_id = order_milestones.system_id
                        and logic.order_id = order_milestones.order_id
                    left join shipper_attributes
                        on logic.system_id = shipper_attributes.system_id
                        and order_milestones.shipper_id = shipper_attributes.id
                    where logic.keep_flag = 1
                        and shipper_attributes.sales_channel != 'Test'

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HUB_JOURNEYS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
