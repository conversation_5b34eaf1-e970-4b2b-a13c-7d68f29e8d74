import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.RESERVATION_PICKED_UP_ORDERS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(dag_id=data_warehouse.DPDAG.DAG_ID,
                               task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PROOFS_ENRICHED,
                view_name="proofs_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVATION_PICKED_UP_ORDERS,
                view_name="reservation_picked_up_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
                view_name="orders_jobs_assignments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS,
                view_name="reservations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATION_FAILURE_REASON,
                view_name="reservation_failure_reason",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ROUTE_WAYPOINT,
                view_name="route_waypoint",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES, view_name="zones"),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                            view_name="failure_reasons"),
            # NEW SET OF TABLES FOR PICKUP APPOINTMENT JOB
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PICKUP_APPOINTMENT_JOBS,
                view_name="pickup_appointment_jobs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PICKUP_APPOINTMENT_JOBS_PICKUP_TAGS,
                view_name="pickup_appointment_jobs_pickup_tags ",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PUDO_PICKUP_APPOINTMENT_JOBS,
                view_name="pudo_pickup_appointment_jobs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PICKUP_TAGS,
                view_name="pickup_tags ",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).JOB_WAYPOINTS,
                view_name="job_waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.FirstMileProdGL(input_env, is_masked).SHIPPER_FIRST_MILE_ADDRESSES,
                view_name="shipper_first_mile_addresses",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="reservation_picked_up_orders_dedupe",
                jinja_template="""
                -- CTE to dedupe reservation_picked_up_orders due to timezone issue
                select
                    reservation_id
                    , data_source
                    , sum(picked_up_orders) as picked_up_orders
                from reservation_picked_up_orders
                group by 1,2
                """,
            ),

            base.TransformView(
                view_name="failure_reason_ids",
                jinja_template="""
                select
                    reservation_id
                    , max_by(failure_reason_id, created_at) as last_failure_reason_id
                    , max_by(failure_reason_code_id,  created_at) as last_failure_reason_code_id
                from reservation_failure_reason
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="shipper_first_mile_addresses_cte",
                jinja_template="""
                select
                    shipper_address_id
                    , max_by(group_shipper_address_id, created_at) as group_shipper_address_id
                from shipper_first_mile_addresses
                where
                    lower(system_id) = '{{ system_id }}'
                group by 1
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="last_route_waypoint",
                jinja_template="""
                with
                    combined as (
                        select
                            waypoint_id
                            , route_id
                            , created_at
                        from route_waypoint

                        union all

                        select
                            legacy_id as waypoint_id
                            , route_id
                            , created_at
                        from waypoints
                        where waypoints.system_id = '{{ system_id }}'
                    )

                select
                    waypoint_id
                    , max_by(route_id, created_at) as route_id
                from combined
                group by 1
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="orders_jobs_assignments_aggregated",
                jinja_template="""
                select
                    job_id
                    , job_type
                    , count(distinct order_id) as total_orders
                from orders_jobs_assignments_enriched
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="proofs_agg",
                jinja_template="""
                select
                    reference_id as reservation_id
                    , max(commited_datetime) as service_end_timestamp
                from proofs_enriched
                where reference = 'Reservation'
                group by 1

                """,
            ),
            base.TransformView(
                view_name="order_events_agg",
                jinja_template="""
                select
                    reservation_id
                    , min(pickup_arrival_datetime) as first_pickup_arrival_datetime
                from order_events_pickup_success
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            reservations.id as reservation_id
                            , shipper_attributes.id as shipper_id
                            , shipper_attributes.parent_id_coalesce
                            , reservations.distribution_point_id as dpms_id
                            , dps_enriched.name as dp_name

                            , case

                                {%- for id, name in id_to_status %}
                                when reservations.status = {{ id }} then '{{ name }}'
                                {%- endfor %}

                            end as status

                            , case

                                {%- for id, name in id_to_type %}
                                   when reservations.reservation_type = {{ id }} then '{{ name }}'
                                {%- endfor %}

                            end as type

                            , reservations.priority_level
                            , reservations.approx_volume
                            , orders_jobs_assignments_aggregated.total_orders as tagged_orders
                            , if(reservations.status = 1, reservation_picked_up_orders_dedupe.picked_up_orders, null) as picked_up_orders
                            , reservations.comments
                            , from_utc_timestamp(reservations.created_at, '{{ local_timezone }}') as creation_datetime
                            , from_utc_timestamp(reservations.ready_datetime, '{{ local_timezone }}') as ready_datetime
                            , from_utc_timestamp(
                                reservations.latest_datetime, '{{ local_timezone }}'
                            ) as latest_datetime
                            , from_utc_timestamp(reservations.updated_at, '{{ local_timezone }}') as update_datetime
                            , coalesce(order_events_agg.first_pickup_arrival_datetime
                                , proofs_agg.service_end_timestamp) as attempt_datetime
                            , order_events_agg.first_pickup_arrival_datetime as spa_datetime
                            , proofs_agg.service_end_timestamp as service_end_datetime
                            , case
                                when order_events_agg.first_pickup_arrival_datetime is not null then 'SPA DATETIME'
                                when proofs_agg.service_end_timestamp is not null then 'PICKUP SUCCESS DATETIME'
                                else 'NULL'
                            end as attempt_datetime_source
                            , failure_reason_ids.last_failure_reason_code_id
                            , failure_reason_ids.last_failure_reason_id
                            , failure_reasons.description as last_failure_reason
                            , cast(route_logs.legacy_id as bigint) as route_id
                            , cast(route_logs.hub_id as bigint) as route_hub_id
                            , hubs_enriched.name as route_hub_name
                            , hubs_enriched.region as route_hub_region
                            , hubs_enriched.facility_type as route_hub_facility_type
                            , cast(route_logs.driver_id as bigint) as route_driver_id
                            , drivers_enriched.first_name as route_driver_first_name
                            , drivers_enriched.display_name as route_driver_display_name
                            , drivers_enriched.driver_type as route_driver_type
                            , route_logs.zone_id as route_zone_id
                            , route_zone.name as route_zone
                            , cast(reservations.waypoint_id as bigint) as waypoint_id
                            , waypoints.routing_zone_id as waypoint_zone_id
                            , if(waypoints.routing_zone_id = 1, legacy_zone.name, global_zone.name) as waypoint_zone
                            , if(waypoints.routing_zone_id = 1, legacy_zone.hub_id, global_zone.hub_id) as waypoint_zone_hub_id
                            , reservations.address_id
                            , shipper_first_mile_addresses_cte.group_shipper_address_id
                            , date_format(reservations.created_at, 'yyyy-MM') as created_month
                        from reservations
                        left join dps_enriched on
                            reservations.distribution_point_id = dps_enriched.dpms_id
                            and dps_enriched.created_month is not null
                        left join shipper_attributes on
                            reservations.global_shipper_id = shipper_attributes.id
                        left join failure_reason_ids on
                            reservations.id = failure_reason_ids.reservation_id
                        left join failure_reasons on
                            failure_reason_ids.last_failure_reason_id = failure_reasons.id
                            and failure_reasons.system_id = '{{ system_id }}'
                        left join waypoints on
                            reservations.waypoint_id = waypoints.legacy_id
                            and waypoints.system_id = '{{ system_id }}'
                        left join last_route_waypoint on
                            reservations.waypoint_id = last_route_waypoint.waypoint_id
                        left join route_logs on
                            last_route_waypoint.route_id = route_logs.legacy_id
                            and route_logs.system_id = '{{ system_id }}'
                        left join orders_jobs_assignments_aggregated on
                            reservations.id = orders_jobs_assignments_aggregated.job_id
                            and orders_jobs_assignments_aggregated.job_type = 'Reservation'
                        left join proofs_agg on
                            reservations.id = proofs_agg.reservation_id
                        left join order_events_agg on
                            reservations.id = order_events_agg.reservation_id
                        left join zones as legacy_zone
                            on waypoints.routing_zone_id = legacy_zone.legacy_zone_id
                            and lower(legacy_zone.system_id) = '{{ system_id}}'
                        left join zones as global_zone
                            on waypoints.routing_zone_id = global_zone.id
                            and lower(global_zone.system_id) = '{{ system_id}}'
                        left join zones as route_zone on
                            route_logs.zone_id = route_zone.legacy_zone_id
                            and lower(route_zone.system_id) = '{{ system_id }}'
                        left join hubs_enriched on
                            route_logs.hub_id = hubs_enriched.id
                        left join drivers_enriched on
                            route_logs.driver_id = drivers_enriched.id
                        left join shipper_first_mile_addresses_cte on
                            reservations.address_id = shipper_first_mile_addresses_cte.shipper_address_id
                        left join reservation_picked_up_orders_dedupe
                            on reservations.id = reservation_picked_up_orders_dedupe.reservation_id
                            and reservation_picked_up_orders_dedupe.data_source = 'Reservation'
                        where
                            reservations.deleted_at is null

                    )
                    , final as (

                        select
                            reservation_id
                            , shipper_id
                            , parent_id_coalesce
                            , dpms_id
                            , dp_name
                            , status
                            , type
                            , priority_level
                            , cast(null as string) as pickup_tags
                            , approx_volume
                            , tagged_orders
                            , picked_up_orders
                            , comments
                            , creation_datetime
                            , ready_datetime
                            , latest_datetime
                            , update_datetime
                            , spa_datetime
                            , service_end_datetime
                            , attempt_datetime
                            , attempt_datetime_source

                            , if(attempt_datetime is not null, 1, 0) as attempted_flag
                            , if(
                                ready_datetime <= attempt_datetime and attempt_datetime <= latest_datetime, 1, 0
                            ) as on_time_flag
                            , case
                                when ready_datetime is null or latest_datetime is null then 'pickup window not set'
                                when attempt_datetime is null then 'not attempted'
                                when attempt_datetime < ready_datetime then 'early'
                                when attempt_datetime <= latest_datetime then 'ontime'
                                when attempt_datetime > latest_datetime then 'late'
                            end as pickup_timeliness_indicator

                            , last_failure_reason_code_id
                            , last_failure_reason_id
                            , last_failure_reason
                            , route_id
                            , route_hub_id
                            , route_hub_name
                            , route_hub_region
                            , route_hub_facility_type
                            , route_driver_id
                            , route_driver_first_name
                            , route_driver_display_name
                            , route_driver_type
                            , route_zone_id
                            , route_zone
                            , waypoint_id
                            , waypoint_zone_id
                            , waypoint_zone
                            , waypoint_zone_hub_id
                            , address_id
                            , group_shipper_address_id
                            , created_month
                            , 'Reservation' as data_source
                        from base
                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                    "id_to_status": enumerate(("Pending", "Success", "Fail", "Reject", "Cancel")),
                    "id_to_type": enumerate(
                        (
                            "Regular/Standard Scheduled",
                            "On-Demand",
                            "Hyperlocal",
                            "Shipper Customer",
                            "Premium Scheduled",
                        )
                    ),
                },
            ),
            # PAJ CODE START
            base.TransformView(
                view_name="proof_job_agg",
                jinja_template="""
                    select
                        reference_id as job_id
                        , reference as job_type
                        , max(commited_datetime) as commit_date
                        , max_by(failure_reason_id, creation_datetime) last_failure_reason_id
                        , max_by(failure_reason_code_id, creation_datetime) last_failure_reason_code_id
                    from proofs_enriched
                    where reference in ('Pickup Appointment Job', 'Pudo Pickup Appointment Job')
                    group by 1,2
                    """,
            ),
            base.TransformView(
                view_name="tag_base",
                jinja_template="""
                select
                    pickup_appointment_jobs_pickup_tags.pickup_appointment_job_id
                    , collect_list(pickup_tags.name) pickup_tags
                from pickup_appointment_jobs_pickup_tags
                left join pickup_tags
                    on pickup_tags.id = pickup_appointment_jobs_pickup_tags.pickup_tag_id
                where
                    pickup_tags.system_id = '{{ system_id }}'
                group by 1
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="final_view_paj",
                jinja_template="""
                with base_paj_final as (
                    select
                        pickup_appointment_jobs.id reservation_id
                        , shipper_attributes.parent_id_coalesce
                        , shipper_attributes.id as shipper_id
                        , null as dpms_id
                        , null as dp_name
                        , pickup_appointment_jobs.status as status
                        , pickup_appointment_jobs.pickup_service_type as type
                        , cast(0 as int) as priority_level
                        , tag_base.pickup_tags
                        , pickup_appointment_jobs.pickup_approx_volume as approx_volume
                        , orders_jobs_assignments_aggregated.total_orders as tagged_orders
                        , reservation_picked_up_orders_dedupe.picked_up_orders as picked_up_orders
                        , pickup_appointment_jobs.pickup_instructions as comments
                        , from_utc_timestamp(
                            pickup_appointment_jobs.created_at, '{{ local_timezone }}'
                        ) creation_datetime
                        , from_utc_timestamp(
                            pickup_appointment_jobs.pickup_ready_datetime, '{{ local_timezone }}'
                        ) as ready_datetime
                        , from_utc_timestamp(
                            pickup_appointment_jobs.pickup_latest_datetime, '{{ local_timezone }}'
                        ) as latest_datetime
                        , from_utc_timestamp(
                            pickup_appointment_jobs.updated_at, '{{ local_timezone }}'
                        ) as update_datetime
                        , coalesce(order_events_agg.first_pickup_arrival_datetime, proof_job_agg.commit_date) attempt_datetime
                        , first_pickup_arrival_datetime as spa_datetime
                        , proof_job_agg.commit_date as service_end_datetime
                        , case
                            when order_events_agg.first_pickup_arrival_datetime is not null then 'SPA DATETIME'
                            when proof_job_agg.commit_date is not null then 'PICKUP SUCCESS DATETIME'
                            else 'NULL'
                        end as attempt_datetime_source
                        , proof_job_agg.last_failure_reason_code_id
                        , proof_job_agg.last_failure_reason_id
                        , failure_reasons.description as last_failure_reason
                        , cast(waypoints.route_id as bigint) as route_id
                        , cast(route_logs.hub_id as bigint) as route_hub_id
                        , hubs_enriched.name as route_hub_name
                        , hubs_enriched.region as route_hub_region
                        , hubs_enriched.facility_type as route_hub_facility_type
                        , cast(route_logs.driver_id as bigint) as route_driver_id
                        , drivers_enriched.first_name as route_driver_first_name
                        , drivers_enriched.display_name as route_driver_display_name
                        , drivers_enriched.driver_type as route_driver_type
                        , route_logs.zone_id as route_zone_id
                        , route_zone.name as route_zone
                        , cast(job_waypoints.waypoint_id as bigint) as waypoint_id
                        , waypoints.routing_zone_id as waypoint_zone_id
                        , if(waypoints.routing_zone_id = 1, legacy_zone.name, global_zone.name) as waypoint_zone
                        , if(waypoints.routing_zone_id = 1, legacy_zone.hub_id, global_zone.hub_id) as waypoint_zone_hub_id
                        , pickup_appointment_jobs.address_id
                        , shipper_first_mile_addresses_cte.group_shipper_address_id
                        , date_format(pickup_appointment_jobs.created_at, 'yyyy-MM') as created_month
                    from pickup_appointment_jobs
                    left join proof_job_agg on
                        pickup_appointment_jobs.id = proof_job_agg.job_id
                        and proof_job_agg.job_type = 'Pickup Appointment Job'
                    left join orders_jobs_assignments_aggregated on
                        pickup_appointment_jobs.id = orders_jobs_assignments_aggregated.job_id
                        and orders_jobs_assignments_aggregated.job_type = 'Pickup Appointment Job'
                    left join job_waypoints on
                        pickup_appointment_jobs.id = job_waypoints.job_id
                        and job_waypoints.job_type = 'Pickup Appointment'
                        and job_waypoints.system_id = '{{ system_id }}'
                    left join waypoints on
                        job_waypoints.waypoint_id = waypoints.legacy_id
                        and waypoints.system_id = '{{ system_id }}'
                    left join failure_reasons on
                        proof_job_agg.last_failure_reason_id = failure_reasons.id
                        and failure_reasons.system_id = '{{ system_id }}'
                    left join route_logs on
                        waypoints.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    left join hubs_enriched on
                        route_logs.hub_id = hubs_enriched.id
                    left join drivers_enriched on
                        route_logs.driver_id = drivers_enriched.id
                    left join zones as route_zone on
                        route_logs.zone_id = route_zone.legacy_zone_id
                        and lower(route_zone.system_id) = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    left join zones as legacy_zone
                        on waypoints.routing_zone_id = legacy_zone.legacy_zone_id
                        and lower(legacy_zone.system_id) = '{{ system_id}}'
                    left join zones as global_zone
                        on waypoints.routing_zone_id = global_zone.id
                        and lower(global_zone.system_id) = '{{ system_id}}'
                    left join order_events_agg on
                        pickup_appointment_jobs.id = order_events_agg.reservation_id
                    left join shipper_attributes on
                        pickup_appointment_jobs.shipper_id = shipper_attributes.id
                    left join tag_base on
                        tag_base.pickup_appointment_job_id = pickup_appointment_jobs.id
                    left join shipper_first_mile_addresses_cte on
                        pickup_appointment_jobs.address_id = shipper_first_mile_addresses_cte.shipper_address_id
                    left join reservation_picked_up_orders_dedupe
                        on pickup_appointment_jobs.id = reservation_picked_up_orders_dedupe.reservation_id
                        and reservation_picked_up_orders_dedupe.data_source = 'Pickup Appointment Job'
                    where
                        pickup_appointment_jobs.system_id = '{{ system_id }}'
                    ),

                    final_paj as (

                        select
                            reservation_id
                            , shipper_id
                            , parent_id_coalesce
                            , dpms_id
                            , dp_name
                            , status
                            , type
                            , priority_level
                            , cast(pickup_tags as string) as pickup_tags
                            , approx_volume
                            , tagged_orders
                            , picked_up_orders
                            , comments
                            , creation_datetime
                            , ready_datetime
                            , latest_datetime
                            , update_datetime
                            , spa_datetime
                            , service_end_datetime
                            , attempt_datetime
                            , attempt_datetime_source
                            , if(attempt_datetime is not null, 1, 0) as attempted_flag
                            , if(
                                ready_datetime <= attempt_datetime and attempt_datetime <= latest_datetime, 1, 0
                                ) as on_time_flag
                            , case
                                when ready_datetime is null or latest_datetime is null then 'pickup window not set'
                                when attempt_datetime is null then 'not attempted'
                                when attempt_datetime < ready_datetime then 'early'
                                when attempt_datetime <= latest_datetime then 'ontime'
                                when attempt_datetime > latest_datetime then 'late'
                            end as pickup_timeliness_indicator

                            , last_failure_reason_code_id
                            , last_failure_reason_id
                            , last_failure_reason
                            , route_id
                            , route_hub_id
                            , route_hub_name
                            , route_hub_region
                            , route_hub_facility_type
                            , route_driver_id
                            , route_driver_first_name
                            , route_driver_display_name
                            , route_driver_type
                            , route_zone_id
                            , route_zone
                            , waypoint_id
                            , waypoint_zone_id
                            , waypoint_zone
                            , waypoint_zone_hub_id
                            , address_id
                            , group_shipper_address_id
                            , created_month
                            , 'Pickup Appointment Job' as data_source
                        from base_paj_final
                    )

                    select *
                    from final_paj
                    """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),
            base.TransformView(
                view_name="final_view_pudo_paj",
                jinja_template="""
                with base_pudo_paj_final as (
                    select
                        cast(pudo_pickup_appointment_jobs.id as bigint) as reservation_id
                        , shipper_attributes.parent_id_coalesce
                        , shipper_attributes.id as shipper_id
                        , cast(pudo_pickup_appointment_jobs.legacy_dp_id as int) as dpms_id
                        , dps_enriched.name as dp_name
                        , pudo_pickup_appointment_jobs.status as status
                        , pudo_pickup_appointment_jobs.pickup_service_type as type
                        , cast(0 as int) as priority_level
                        , null as pickup_tags
                        , null as approx_volume
                        , orders_jobs_assignments_aggregated.total_orders as tagged_orders
                        , reservation_picked_up_orders_dedupe.picked_up_orders as picked_up_orders
                        , pudo_pickup_appointment_jobs.pickup_instructions as comments
                        , from_utc_timestamp(
                            pudo_pickup_appointment_jobs.created_at, '{{ local_timezone }}'
                        ) creation_datetime
                        , from_utc_timestamp(
                            pudo_pickup_appointment_jobs.pickup_ready_datetime, '{{ local_timezone }}'
                        ) as ready_datetime
                        , from_utc_timestamp(
                            pudo_pickup_appointment_jobs.pickup_latest_datetime, '{{ local_timezone }}'
                        ) as latest_datetime
                        , from_utc_timestamp(
                            pudo_pickup_appointment_jobs.updated_at, '{{ local_timezone }}'
                        ) as update_datetime
                        , coalesce(order_events_agg.first_pickup_arrival_datetime, proof_job_agg.commit_date) attempt_datetime
                        , order_events_agg.first_pickup_arrival_datetime as spa_datetime
                        , proof_job_agg.commit_date as service_end_datetime
                        , case
                            when order_events_agg.first_pickup_arrival_datetime is not null then 'SPA DATETIME'
                            when proof_job_agg.commit_date is not null then 'PICKUP SUCCESS DATETIME'
                            else 'NULL'
                        end as attempt_datetime_source
                        , proof_job_agg.last_failure_reason_code_id
                        , proof_job_agg.last_failure_reason_id
                        , failure_reasons.description as last_failure_reason
                        , cast(waypoints.route_id as bigint) as route_id
                        , cast(route_logs.hub_id as bigint) as route_hub_id
                        , hubs_enriched.name as route_hub_name
                        , hubs_enriched.region as route_hub_region
                        , hubs_enriched.facility_type as route_hub_facility_type
                        , cast(route_logs.driver_id as bigint) as route_driver_id
                        , drivers_enriched.first_name as route_driver_first_name
                        , drivers_enriched.display_name as route_driver_display_name
                        , drivers_enriched.driver_type as route_driver_type
                        , route_logs.zone_id as route_zone_id
                        , route_zone.name as route_zone
                        , cast(job_waypoints.waypoint_id as bigint) as waypoint_id
                        , waypoints.routing_zone_id as waypoint_zone_id
                        , if(waypoints.routing_zone_id = 1, legacy_zone.name, global_zone.name) as waypoint_zone
                        , if(waypoints.routing_zone_id = 1, legacy_zone.hub_id, global_zone.hub_id) as waypoint_zone_hub_id
                        , null as address_id
                        , null as group_shipper_address_id
                        , date_format(pudo_pickup_appointment_jobs.created_at, 'yyyy-MM') as created_month
                    from pudo_pickup_appointment_jobs
                    left join dps_enriched on
                        pudo_pickup_appointment_jobs.legacy_dp_id = dps_enriched.dpms_id
                        and dps_enriched.created_month is not null
                    left join proof_job_agg on
                        pudo_pickup_appointment_jobs.id = proof_job_agg.job_id
                        and proof_job_agg.job_type = 'Pudo Pickup Appointment Job'
                    left join orders_jobs_assignments_aggregated on
                        pudo_pickup_appointment_jobs.id = orders_jobs_assignments_aggregated.job_id
                        and orders_jobs_assignments_aggregated.job_type = 'Pudo Pickup Appointment Job'
                    left join job_waypoints on
                        pudo_pickup_appointment_jobs.id = job_waypoints.job_id
                        and job_waypoints.job_type = 'PUDO_PICKUP_APPOINTMENT'
                        and job_waypoints.system_id = '{{ system_id }}'
                    left join waypoints on
                        job_waypoints.waypoint_id = waypoints.legacy_id
                        and waypoints.system_id = '{{ system_id }}'
                    left join failure_reasons on
                        proof_job_agg.last_failure_reason_id = failure_reasons.id
                        and failure_reasons.system_id = '{{ system_id }}'
                    left join route_logs on
                        waypoints.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    left join hubs_enriched on
                        route_logs.hub_id = hubs_enriched.id
                    left join drivers_enriched on
                        route_logs.driver_id = drivers_enriched.id
                    left join zones as route_zone on
                        route_logs.zone_id = route_zone.legacy_zone_id
                        and lower(route_zone.system_id) = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    left join zones as legacy_zone
                        on waypoints.routing_zone_id = legacy_zone.legacy_zone_id
                        and lower(legacy_zone.system_id) = '{{ system_id}}'
                    left join zones as global_zone
                        on waypoints.routing_zone_id = global_zone.id
                        and lower(global_zone.system_id) = '{{ system_id}}'
                    left join order_events_agg on
                        pudo_pickup_appointment_jobs.id = order_events_agg.reservation_id
                    left join shipper_attributes on
                        dps_enriched.shipper_id = shipper_attributes.id
                    left join tag_base on
                        tag_base.pickup_appointment_job_id = pudo_pickup_appointment_jobs.id
                    left join reservation_picked_up_orders_dedupe
                        on pudo_pickup_appointment_jobs.id = reservation_picked_up_orders_dedupe.reservation_id
                        and reservation_picked_up_orders_dedupe.data_source = 'Pudo Pickup Appointment Job'
                    where
                        pudo_pickup_appointment_jobs.system_id = '{{ system_id }}'
                    ),

                    final_pudo_paj as (

                        select
                            reservation_id
                            , shipper_id
                            , parent_id_coalesce
                            , dpms_id
                            , dp_name
                            , status
                            , type
                            , priority_level
                            , cast(pickup_tags as string) as pickup_tags
                            , approx_volume
                            , tagged_orders
                            , picked_up_orders
                            , comments
                            , creation_datetime
                            , ready_datetime
                            , latest_datetime
                            , update_datetime
                            , spa_datetime
                            , service_end_datetime
                            , attempt_datetime
                            , attempt_datetime_source
                            , if(attempt_datetime is not null, 1, 0) as attempted_flag
                            , if(
                                ready_datetime <= attempt_datetime and attempt_datetime <= latest_datetime, 1, 0
                                ) as on_time_flag
                            , case
                                when ready_datetime is null or latest_datetime is null then 'pickup window not set'
                                when attempt_datetime is null then 'not attempted'
                                when attempt_datetime < ready_datetime then 'early'
                                when attempt_datetime <= latest_datetime then 'ontime'
                                when attempt_datetime > latest_datetime then 'late'
                            end as pickup_timeliness_indicator

                            , last_failure_reason_code_id
                            , last_failure_reason_id
                            , last_failure_reason
                            , route_id
                            , route_hub_id
                            , route_hub_name
                            , route_hub_region
                            , route_hub_facility_type
                            , route_driver_id
                            , route_driver_first_name
                            , route_driver_display_name
                            , route_driver_type
                            , route_zone_id
                            , route_zone
                            , waypoint_id
                            , waypoint_zone_id
                            , waypoint_zone
                            , waypoint_zone_hub_id
                            , address_id
                            , group_shipper_address_id
                            , created_month
                            , 'Pudo Pickup Appointment Job' as data_source
                        from base_pudo_paj_final
                    )

                    select *
                    from final_pudo_paj
                    """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),
            base.TransformView(
                view_name="final_view_union",
                jinja_template="""
                select *
                from final_view
                UNION ALL
                select *
                from final_view_paj
                UNION ALL
                select *
                from final_view_pudo_paj
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVATIONS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()