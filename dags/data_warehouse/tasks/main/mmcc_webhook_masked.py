import sys

from pyspark.sql import SparkSession

from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_MASKED,
    system_ids=(SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.WebhookHistoryProdGL(input_env, is_masked).MMCC_WEBHOOK_HISTORY,
                view_name="mmcc_webhook_history",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                select 
                    uuid
                    , logged_at as webhook_sent_time_utc
                    , log_type 
                    , big_bag_no
                    , master_awb
                    , webhook_status
                    , webhook_state
                    , global_shipper_id 
                    , response_code
                    , if(response_code = 200,1,0) as success_flag
                    , get_json_object(webhook_generated_detail, '$.url') as url
                    , from_utc_timestamp(from_unixtime(get_json_object(webhook_request, '$.metadata.eventTime')/1000), 'UTC') as event_time_utc
                    , if(get_json_object(webhook_request, '$.isParcelOnRtsLeg') is true, 1, 0) as rts_flag 
                    , webhook_request
                    , webhook_generated_detail
                    , system_id 
                    , created_at
                    , created_month 
                from mmcc_webhook_history
                where
                    -- Only webhook response is required
                    log_type = 'Webhook Push Task Completed'
                    -- Filter out custom events 
                    and webhook_status != 'Custom' 

                """,
            ),

            base.TransformView(
                view_name="bags_list",
                jinja_template="""

                select 
                    uuid
                    , master_awb 
                    , get_json_object(big_bag_no, '$.bigBagNo') as big_bag_no
                from (
                    select
                        distinct 
                        uuid
                        , get_json_object(webhook_request, '$.linehaul.masterAwb') as master_awb
                        , explode(from_json(get_json_object(webhook_request, '$.linehaul.bags'), 'array<string>')) AS big_bag_no
                    from base
                    where
                         big_bag_no = ''
                    ) 

                """,
            ),

            base.TransformView(
                view_name="final",
                jinja_template="""

                select 
                    base.uuid
                    , IF(base.global_shipper_id in (11018439, 11018476), 'Lazada XB MM', 'Tiktok XB MMCC') as shipper
                    , base.webhook_sent_time_utc
                    , base.log_type
                    , coalesce(if(base.big_bag_no = '', null, base.big_bag_no), bags_list.big_bag_no) as big_bag_no
                    , base.master_awb
                    , base.webhook_status
                    , base.webhook_state
                    , base.global_shipper_id
                    , base.response_code
                    , base.success_flag
                    , base.event_time_utc
                    , base.rts_flag
                    , base.webhook_request
                    , base.webhook_generated_detail
                    , base.system_id 
                    , date(base.created_at) as created_date
                    , base.created_month 
                from base
                left join bags_list on 
                    base.master_awb = bags_list.master_awb
                    and base.uuid = bags_list.uuid
                    and base.big_bag_no = ''

                """,
            ),
        ),
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "created_month"),
        update_latest_with_historical=True,
        enable_compaction=False,
        use_native_overwrite=True
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(input_args.env,
                                  input_args.last_measurement_datetime,
                                  input_args.measurement_datetime,
                                  input_args.enable_full_run,
                                  )
    run(spark, task_config)
    spark.stop()