import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_INBOUND_VOL_MONTHLY_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_INBOUND_VOL_MONTHLY_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.OrderAggregatesDAG.Task.SHIPPER_INBOUND_VOL_DAILY_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 4, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            # No input_range set here because we need to calculate shippers' first ever order inbound month.
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_INBOUND_VOL_DAILY,
                view_name="shipper_inbound_vol_daily",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                SELECT country
                       , cast(date_format(inbound_date, 'yyyy-MM-01') AS date) AS inbound_month
                       , shipper_id
                       , shipper_name
                       , parent_id
                       , parent_name
                       , parent_id_coalesce
                       , parent_name_coalesce
                       , system_id
                       , created_month
                {%- for col in order_agg_cols %}
                       , sum({{ col }}_orders) AS {{ col }}_orders
                {%- endfor %}
                FROM shipper_inbound_vol_daily
                GROUP BY {{ range(1, 11) | join(',') }}
                """,
                jinja_arguments={
                    "order_agg_cols": (
                        "total",
                        "returned_to_sender",
                        "cancelled",
                        "on_hold",
                        "transit",
                        "completed",
                        "exception",
                        "ninjapack",
                    )
                },
            ),
            base.TransformView(
                view_name="shipper_first_inbound",
                jinja_template="""
                SELECT shipper_id
                       , min(inbound_month) AS inbound_month
                FROM base
                GROUP BY 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.*
                       , CAST(months_between(base.inbound_month, shipper_first_inbound.inbound_month) AS int)
                         AS age_in_months
                       , CASE
                             WHEN base.total_orders >= 3000 THEN 'a.>=3000'
                             WHEN base.total_orders >= 1000 AND base.total_orders < 3000 THEN 'b.[1000,3000)'
                             WHEN base.total_orders >= 500  AND base.total_orders < 1000 THEN 'c.[500,1000)'
                             WHEN base.total_orders >= 250 AND base.total_orders < 500 THEN 'd.[250,500)'
                             WHEN base.total_orders < 250 THEN 'e.(0,250)'
                             WHEN base.total_orders is null THEN 'f.0'
                         END AS tier_level
                FROM base
                LEFT JOIN shipper_first_inbound ON base.shipper_id = shipper_first_inbound.shipper_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_INBOUND_VOL_MONTHLY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
