import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.SELLER_OPERATIONAL_METRICS_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.SELLER_OPERATIONAL_METRICS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
               select
                        date('{{ measurement_datetime }}') as snapshot_date
                       , shipper_id
                       , coalesce(seller_id, 'None') as seller_id
                       , min(date(delivery_success_datetime)) as l6m_first_order_completion_date
                       , max(date(delivery_success_datetime)) as l6m_latest_order_completion_date
                       , count(1) as l6m_total_orders
                       , count_if(granular_status='Cancelled') as l6m_cancelled_orders
                       , count_if(delivery_success_datetime is not null) as l6m_completed_orders
                       , count_if(rts_flag = 1) AS l6m_rts_orders
                       , avg(cod_value) as l6m_avg_cod_value
                       , count_if(cod_value >= 500000) as l6m_high_value_orders
                       , count_if(
                            granular_status='Cancelled' and cod_value >= 500000
                        ) as l6m_high_value_cancelled_orders
                       , count_if(
                            delivery_success_datetime is not null and cod_value >= 500000
                        ) as l6m_high_value_completed_orders
                       , count_if(rts_flag = 1 and cod_value >= 500000) AS l6m_high_value_rts_orders
                       , system_id
                from order_milestones
                group by 1,2,3, system_id
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=parquet_tables_masked.DataWarehouse(env).SELLER_OPERATIONAL_METRICS,
        measurement_datetime=measurement_datetime,
        partition_by=["system_id"],
        write_mode="merge",
        primary_keys=["snapshot_date", "shipper_id", "seller_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()