import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_PRESALES_MASKED + ".py",
    task_name=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_PRESALES_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True
    country = 'vn'
    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).LOVS,
                view_name="lovs",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).OUTLETS,
                view_name="outlets",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).SITES,
                view_name="sites",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).VAN_VISITS,
                view_name="van_visits",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).VANS,
                view_name="vans",
            ),
        ),

    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final",
                jinja_template="""
                with 
                    outlet_channel as (
                        select 
                            id
                            , display 
                            from lovs l 
                        where 
                            group_id = 'channels' 
                            and active = True
                    ),

                    outlet_subchannel as (
                        select 
                            id
                            , display 
                        from lovs l 
                        where group_id = 'sub_channels' 
                            and active = True
                    )

                select 
                    date(vv.date) as date
                    , vv.site_id
                    , vv.van_id
                    , vans.name van_name
                    , vv.outlet_id
                    , o.name as outlet_name
                    , o.district outlet_district
                    , o.ward outlet_ward
                    , o.state outlet_province
                    , sites.name site_name
                    , oc.display as channel
                    , os.display as sub_channel
                    , o.status
                    , case 
                        when vv.mobile_check_in_at is null and vv.mobile_check_out_at is null then cast(vv.check_in_at as timestamp) 
                        else cast(vv.mobile_check_in_at as timestamp) 
                      end check_in_at
                    , case 
                        when vv.mobile_check_in_at is null and vv.mobile_check_out_at is null then cast(vv.check_out_at as timestamp) 
                        else cast(vv.mobile_check_out_at as timestamp) 
                      end check_out_at
                    , case 
                        when vv.mobile_check_in_at is null and vv.mobile_check_out_at is null then (unix_timestamp(cast(vv.check_out_at as timestamp)) - unix_timestamp(cast(vv.check_in_at as timestamp))) / 60 
                        else (unix_timestamp(cast(vv.mobile_check_out_at as timestamp)) - unix_timestamp(cast(vv.mobile_check_in_at as timestamp))) / 60  
                      end time_spent_in_mins
                    , case visit_action
                        when 'V' then 'No'
                        when 'C' then 'No'
                        when 'S' then 'No'
                        when 'SC' then 'No'
                        when 'N' then 'No'
                        when 'O' then 'Yes'
                      else 'Undefined'
                      end order_taken
                    , case coalesce(visited, False)
                        when True 
                        then 'Yes'
                        else 'No'
                      end visited
                    , case is_planned 
                        when true then 'Planned Visit'
                        when false then 'Unplanned Visit'
                        else 'Undefined'
                      end visit_type
                    , vv.lat
                    , vv.lng
                    , 'vn' as system_id
                    , date_format(date(vv.date), 'yyyy-MM') as created_month
                from van_visits vv 
                inner join vans 
                    on vv.van_id = vans.id
                inner join outlets o 
                    on vv.outlet_id = o.id 
                inner join sites
                    on o.site_id = sites.id
                left join outlet_channel oc 
                    on o.channel = oc.id
                left join outlet_subchannel os 
                    on o.subchannel = os.id
                order by vv.site_id, vv.date
                """
            ),
        ),

    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_MART_VN_PRESALES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
