import sys

from pyspark.sql import SparkSession
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    # This task only performs partial run
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
                view_name="orders_jobs_assignments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orders_jobs_assignments_filtered",
                jinja_template="""
                SELECT
                    order_id
                    , max_by(job_type, job_assigned_datetime) as latest_job_type
                    , max_by(job_id, job_assigned_datetime) as latest_job_id
                FROM orders_jobs_assignments_enriched
                group by 1
                """,
            ),

            base.TransformView(
                view_name="last_reservation_ready_cte",
                jinja_template="""
                select
                    shipper_id
                    , max(ready_datetime) as last_reservation_ready_datetime
                from reservations_enriched
                group by 1
                """,
            ),

            base.TransformView(
                view_name="base",
                jinja_template="""
                select 
                    orders.id as order_id
                      , case
                        when orders.parcel_size_id = 0 then 's'
                        when orders.parcel_size_id = 1 then 'm'
                        when orders.parcel_size_id = 2 then 'l'
                        when orders.parcel_size_id = 3 then 'xl'
                        when orders.parcel_size_id = 4 then 'xxl'
                        when orders.parcel_size_id = 5 then 'xs'
                        end as orders_size
                    , date(reservations_enriched.creation_datetime) as rsvn_creation_date
                    , reservations_enriched.address_id
                    , reservations_enriched.reservation_id
                    , reservations_enriched.dpms_id
                    , drivers_enriched.veh_type
                    , drivers_enriched.driver_type
                    , case 
                        when lower(drivers_enriched.driver_type) like '%rider%' then '2W'
                        else '4W'
                    end as vehicle_type1
                    , case 
                        when lower(drivers_enriched.veh_type) = 'bike' then '2W'
                        when lower(drivers_enriched.veh_type) = 'van' then '4W'
                    else drivers_enriched.veh_type
                    end as vehicle_type2
                    , last_reservation_ready_cte.last_reservation_ready_datetime
                    , shipper_attributes.onboarded_date
                from orders
                left join orders_jobs_assignments_filtered
                    on orders.id = orders_jobs_assignments_filtered.order_id
                left join reservations_enriched
                    on orders_jobs_assignments_filtered.latest_job_id = reservations_enriched.reservation_id
                    and orders_jobs_assignments_filtered.latest_job_type = reservations_enriched.data_source
                left join drivers_enriched 
                    on reservations_enriched.route_driver_id = drivers_enriched.id
                left join last_reservation_ready_cte
                    on reservations_enriched.shipper_id = last_reservation_ready_cte.shipper_id
                left join shipper_attributes
                    on shipper_attributes.id = reservations_enriched.shipper_id
                    -- Logic updated to only look at D-1
                where date(reservations_enriched.ready_datetime) = date('{{ measurement_datetime }}')    
                    and lower(drivers_enriched.driver_type) not like '%mitra%'
                   and (
                    -- Filter new shippers that have reservations ready after last weekly upload date
                        (shipper_attributes.onboarded_date > date_trunc('week','{{ measurement_datetime }}'))
                    -- Filter only shippers that have been "inactive". 
                    -- Inactive are shippers which last reservation ready greater than 2 weeks.
                    or (unix_timestamp(date('{{ measurement_datetime }}'))
                        - unix_timestamp(date(last_reservation_ready_cte.last_reservation_ready_datetime))) / 86400 > 14)
                -- Exclude facility types that are not under FM
                and reservations_enriched.route_hub_facility_type not in ('OTHERS', 'RECOVERY')
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with daily_counts as (
                select 
                    base.address_id
                    , count(distinct base.order_id) as number_of_daily_orders
                    , count_if(base.orders_size in ('l','xl','xxl')) number_of_bulky_orders
                from base
                group by 1
                ),

                tag_vehicle_type as (
                    select
                        *
                        -- Set 4W for > 50 daily orders or number_of_daily_orders <+50 orders but with > 3 bulky orders
                        , if((number_of_daily_orders > 50 
                            or (number_of_daily_orders <= 50 and number_of_bulky_orders > 3)), '4W','2W') as vehicle_type
                    from daily_counts
                ),

                 daily_ready_reservations as (
                    select 
                        reservations_enriched.address_id
                        , date(reservations_enriched.ready_datetime) as date
                        , tag_vehicle_type.number_of_daily_orders as number_of_daily_orders_yesterday
                        , tag_vehicle_type.number_of_bulky_orders as number_of_bulky_orders_yesterday
                        , tag_vehicle_type.vehicle_type
                        , date_format(reservations_enriched.ready_datetime, 'yyyy-MM') as created_month
                        , reservations_enriched.system_id
                        , sum(reservations_enriched.tagged_orders) as tagged_vol_today
                    from reservations_enriched
                    left join tag_vehicle_type
                        on reservations_enriched.address_id = tag_vehicle_type.address_id
                    where date(reservations_enriched.ready_datetime) = date('{{ measurement_datetime }}') 
                        + interval '1' day
                        and reservations_enriched.tagged_orders is not null
                        and reservations_enriched.address_id is not null
                        and tag_vehicle_type.vehicle_type is not null
                    group by {{range(1,8)|join(',')}}
                    )

                    select * from daily_ready_reservations
                    order by 1
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
