import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesDAG.Task.SALESPERSONS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesDAG.Task.SALESPERSONS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.CoreProdGL(system_id, input_env, is_masked).SALESPERSONS, view_name="salespersons"),
            base.InputTable(
                path=getattr(delta_tables.GDrive(input_env), f"SALESPERSONS_ENRICHED_{system_id.upper()}"),
                view_name="salespersons_enriched_gdrive",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            id
                            , '{{ system_id }}' as country
                            , code
                            , name

                            , case

                                {%- for code_prefix, team in code_prefix_to_team.items() %}
                                when code like '{{ code_prefix }}%' then '{{ team }}'
                                {%- endfor %}

                            end as inferred_team

                            , from_utc_timestamp(created_at, '{{ local_timezone }}') as creation_datetime
                            , if(deleted_at is null, 0, 1) as is_deleted
                            , date_format(created_at, 'yyyy-MM') as created_month
                        from salespersons

                    )
                    , final as (

                        select
                          base.id
                          , base.country
                          , base.code
                          , base.name
                          , coalesce(gdrive.team, base.inferred_team) as team
                          , base.creation_datetime
                          , base.is_deleted
                          , base.created_month
                        from base
                        left join salespersons_enriched_gdrive as gdrive on
                            base.id = gdrive.id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "code_prefix_to_team": {
                        "B2B": "B2B",
                        "XB": "Cross Border",
                        "DSND": "Ninja Direct",
                        "DSHQ": "HQ",
                        "DSKV": "HQ",
                        "DSCT": "HQ",
                        "DSTS": "Territorial South",
                        "DSTN": "Territorial North",
                        "FHC": "Metro HCM",
                        "FHN": "Metro HN",
                        "FTS": "Telesales",
                        "FNO": "North",
                        "FSO": "South",
                        "FBD": "BD",
                        "FS": "FSE",
                        "TS": "TSE",
                        "BD": "BDM",
                        "DRS": "Direct Sales",
                        "KAM": "Partnerships",
                        "PSP": "Partnerships",
                        "AM": "Partnerships",
                        "NSQ": "Ninja Squad",
                        "PUD": "PUDO",
                        "NSOS": "PUDO",
                        "SOL": "Solutions",
                        "TEST-": "Test",
                        "SS_General": "Self Serve",
                    },
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESPERSONS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.system_id, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
