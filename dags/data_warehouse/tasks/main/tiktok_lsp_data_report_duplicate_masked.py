import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_LSP_DATA_REPORT_DUPLICATE_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_LSP_DATA_REPORT_DUPLICATE_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    #input_env = "prod"
    is_masked = True
    input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(env).TIKTOK_LOGISTIC_PARTNER_PLATFORM_DATA_REPORT,
                            view_name="tiktok_logistic_partner_platform_data_report"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select 
                    Level_1_Issue_Type as level_1_issue_type
                    , Level_2_Issue_Type as level_2_issue_type
                    , Level_3_Issue_Type as level_3_issue_type
                    , Region as country
                    , Shipment_Type as shipment_type
                    , cast(Ticket_No as bigint) as lsp_ticket_id
                    , Tracking_No as tracking_id
                    , cast(Package_Id as bigint) as package_id
                    , Status as status
                    , to_timestamp(Create_Time,'MM/dd/yyyy HH:mm:ss') as create_time
                    , to_timestamp(First_Response_Time,'MM/dd/yyyy HH:mm:ss') as first_response_time
                    , to_timestamp(Solving_Time,'MM/dd/yyyy HH:mm:ss') as solving_time
                    , to_timestamp(Update_Time,'MM/dd/yyyy HH:mm:ss') as update_time
                    , Requester as requester
                    , Operator as operator
                    , Ticket_Type as ticket_type
                    , Judge_Result as judge_results
                    , Structured_Reply as structured_reply
                    , Description as description
                    , cast(Reopen_Count as int) as reopen_count
                    , Key_Account as key_account
                    , to_date(Input_Date, 'MM/dd/yyyy') as created_date
                from tiktok_logistic_partner_platform_data_report
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_LSP_DATA_REPORT_DUPLICATE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_date",),
        # write_mode='merge',
        # primary_keys=["lsp_ticket_id"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()