import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.ROBOCALL_TABLE_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.ROBOCALL_TABLE_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.RecoveryDAG.DAG_ID,
                               task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED_BASE,
                view_name="pets_tickets_enriched_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.NotificationsV2ProdGL(input_env, is_masked).CALLOUT_POLICIES,
                view_name="callout_policies",
            ),
            base.InputTable(
                path=delta_tables.NotificationsV2ProdGL(input_env, is_masked).SCHEDULED_CALLOUTS,
                view_name="scheduled_callouts",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.NotificationsVoiceProdGL(input_env, is_masked).VONAGE_EVENT_WEBHOOK_LOGS,
                view_name="vonage_event_webhook_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.NotificationsVoiceProdGL(input_env, is_masked).CALLOUT_JOBS,
                view_name="callout_jobs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="earliest_pets",
                jinja_template="""
                select
                    order_id
                    , min_by(id, creation_datetime) as ticket_id
                    , min(creation_datetime) as creation_datetime
                    , system_id
                from pets_tickets_enriched_base
                where sub_type = 'ROBO CALL'
                group by 1,4
                """,
            ),

            base.TransformView(
                view_name="vonage_event_webhook_logs_cte",
                jinja_template="""
                select 
                    uuid
                    , duration
                    , price
                from vonage_event_webhook_logs
                -- filter out for logs that were completed
                where vonage_event_webhook_logs.status = 'completed'
                """,
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with callout_base as (
                select
                    cast(scheduled_callouts.id as bigint) callout_id
                    , cast(scheduled_callouts.ticket_id as bigint) as ticket_id
                    , cast(scheduled_callouts.order_id as bigint) as order_id
                    , cast(scheduled_callouts.failure_reason_id as int) as delivery_failure_reason_id
                    , scheduled_callouts.outcome
                    , scheduled_callouts.call_seq_number
                    , cast(callout_policies.max_call_count as int) max_call_count
                    , scheduled_callouts.is_last_call
                        -- Status update from ANSWERED to NO_INPUT on 25th October 2023. Logic to combine both into one
                    , case when callout_jobs.call_status in ('ANSWERED','NO_INPUT') THEN 'ANSWERED: NO INPUT' ELSE call_status END AS call_status
                    , callout_jobs.dtmf_digit
                    , callout_jobs.call_timestamp
                    , vonage_event_webhook_logs_cte.duration as call_duration
                    , vonage_event_webhook_logs_cte.price
                    , order_milestones.rts_trigger_datetime
                    , earliest_pets.creation_datetime as pet_ticket_creation_datetime
                    -- Flag to identify the 1st instance of robocall ticket as the build is focused on this instead of subsequent robocall tickets
                    , if(earliest_pets.ticket_id is not null, 1,0) as earliest_robocall_pets_flag
                    , shipper_attributes.id as shipper_id
                    , shipper_attributes.parent_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.sales_channel
                    , lower(scheduled_callouts.system_id) as system_id
                    , scheduled_callouts.created_month
                from scheduled_callouts
                left join callout_jobs
                    on scheduled_callouts.id = callout_jobs.callout_request_id
                    and lower(callout_jobs.system_id) = '{{ system_id }}'
                left join callout_policies
                    on callout_policies.id = scheduled_callouts.policy_id
                    and lower(callout_policies.system_id) = '{{ system_id }}'
                left join order_milestones
                    on order_milestones.order_id = scheduled_callouts.order_id
                    and order_milestones.system_id = lower(scheduled_callouts.system_id)
                left join earliest_pets
                    on earliest_pets.ticket_id = scheduled_callouts.ticket_id
                    and earliest_pets.order_id = scheduled_callouts.order_id
                left join shipper_attributes
                    on order_milestones.shipper_id = shipper_attributes.id
                    and order_milestones.system_id = shipper_attributes.system_id
                left join vonage_event_webhook_logs_cte
                    on vonage_event_webhook_logs_cte.uuid = callout_jobs.call_uuid
                where (sales_channel is null or sales_channel != 'Test')
                    and lower(scheduled_callouts.system_id) = '{{ system_id }}'
                ),        

                next_delivery_attempt as (

                    select
                        callout_base.callout_id
                        , callout_base.ticket_id
                        , callout_base.order_id
                        , callout_base.delivery_failure_reason_id
                        , callout_base.outcome
                        , callout_base.call_seq_number
                        , callout_base.max_call_count
                        , callout_base.is_last_call
                        , callout_base.call_status
                        , callout_base.dtmf_digit
                        , callout_base.rts_trigger_datetime
                        , callout_base.earliest_robocall_pets_flag
                        , from_utc_timestamp(callout_base.call_timestamp, '{{ local_timezone }}') as call_datetime
                        , callout_base.call_duration
                        , callout_base.price
                        , callout_base.shipper_id
                        , callout_base.parent_id
                        , callout_base.shipper_name
                        , callout_base.sales_channel
                        -- Get the next available delivery txn id after robocall as potential use case for matching to invalid POD
                        , min_by(transactions.id, from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) next_delivery_transaction_id
                        -- Get the next delivery datetime after robocall
                        , min(from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) next_delivery_transaction_datetime
                        -- Get the next successful delivery datetime after robocall. Only fill in this column for forward delivery delivery success datetime. RTS delivery success should not be considered.
                        , max(from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) filter(where transactions.status = 'Success' and rts_trigger_datetime is null) delivery_success_datetime
                        -- Get the next delivery status after robocall
                        , min_by(transactions.status, from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) next_delivery_transaction_status
                        , system_id
                        , callout_base.created_month
                    from callout_base
                    left join transactions
                        on callout_base.order_id = transactions.order_id
                        and transactions.service_end_time >= callout_base.call_timestamp
                    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,24,25
                ),


                calendar_calc as (

                    select
                        next_delivery_attempt.*
                        , next_working_day_1 as next_working_day_n1
                        , next_working_day_2 as next_working_day_n2
                    from next_delivery_attempt
                    left join calendar
                        on date(next_delivery_attempt.call_datetime) = calendar.date
                        and next_delivery_attempt.system_id = calendar.system_id
                        and calendar.system_id != 'my'
                    where
                        -- Filter out calls that were triggered during RTS leg
                        (next_delivery_attempt.call_datetime < next_delivery_attempt.rts_trigger_datetime or next_delivery_attempt.rts_trigger_datetime is null)

                )

                select * from calendar_calc
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                }
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ROBOCALL_TABLE_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
