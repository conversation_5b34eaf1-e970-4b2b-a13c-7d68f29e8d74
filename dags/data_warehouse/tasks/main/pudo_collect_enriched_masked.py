import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.PUDO_COLLECT_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.PUDO_COLLECT_ENRICHED_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DP_MILESTONES_MASKED,
        data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DP_OPERATING_HOURS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DP_RESERVATION_EVENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.WAYPOINTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).DP_EVENT_NAME_MAPPING,
                view_name="dp_event_name_mapping",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVE_TRACKING_IDS_ENRICHED,
                view_name="reserve_tracking_ids_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DESTINATIONS,
                view_name="order_destinations",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DP_MILESTONES,
                view_name="order_dp_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATION_EVENTS_ENRICHED,
                view_name="dp_reservation_events_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_OPERATING_HOURS_ENRICHED,
                view_name="dp_operating_hours_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAYPOINTS_ENRICHED,
                view_name="waypoints_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orders_base",
                jinja_template="""
                with earliest_latest_dp_reservations as (

                    select
                        order_id
                        -- First dp reservation
                        , min(id) as earliest_dp_reservation_id
                        , min_by(drop_off_on, id) as earliest_drop_off_on
                        , min_by(status, id) as earliest_dp_reservation_status

                        -- Last dp reservation
                        , max(id) as latest_dp_reservation_id
                        , max_by(drop_off_on, id) as latest_drop_off_on
                        , max_by(status, id) as latest_dp_reservation_status

                        -- First slot secured dp reservation
                        , min_by(id, concat(drop_off_on, '_', created_at)) as earliest_slot_secured_dp_reservation_id
                    from dp_reservations_enriched
                    where
                        source in (
                            'FULLY_INTEGRATED_NINJA_COLLECT',
                            'SEMI_INTEGRATED_NINJA_COLLECT',
                            'RESCHEDULE',
                            'OPERATOR'
                        )
                    group by 1
                
                )

                select
                    orders.id as order_id
                    , orders.tracking_id as tracking_id
                    , orders.granular_status
                    , coalesce(rti.allow_self_collection, 0) as dp_collect_intended_flag
                    , get_json_object(orders.shipper_ref_metadata,'$.collection_point') as dp_short_name
                    , dpr.earliest_dp_reservation_id
                    , dpr.earliest_drop_off_on
                    , dpr.earliest_dp_reservation_status
                    , dpr.latest_dp_reservation_id
                    , dpr.latest_drop_off_on
                    , dpr.latest_dp_reservation_status
                    , dpr.earliest_slot_secured_dp_reservation_id
                    , from_utc_timestamp(orders.created_at, '{{ local_timezone }}') as creation_datetime
                    , date_format(orders.created_at, 'yyyy-MM') as created_month
                from orders
                left join reserve_tracking_ids_enriched as rti
                    on orders.tracking_id = rti.tracking_id
                left join earliest_latest_dp_reservations as dpr
                    on orders.id = dpr.order_id
                where
                    rti.allow_self_collection = 1
                    or dpr.order_id is not null
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="postcode_reference",
                jinja_template="""
                select
                    waypoints.postcode
                    , max_by(zones.name, waypoints.created_at) as zones
                    , max_by(zones.hub_id, waypoints.created_at) as hub_id
                from waypoints_enriched as waypoints
                left join zones
                    on waypoints.routing_zone_id = zones.legacy_zone_id
                    and zones.system_id = 'sg'
                    and zones.deleted_at is null
                where
                    zones.id is not null
                    and waypoints.zone_type is null
                    and waypoints.routing_zone_id != 1
                group by 1
                """,
            ),
            base.TransformView(
                view_name="delivery_attempts_enriched",
                jinja_template="""
                with dp_operating_hours_deduplicate as (

                    select
                        dp_id
                        , day_of_week
                        , min(open_time) as open_time
                        , max(close_time) as close_time
                    from dp_operating_hours_enriched
                    where
                        is_active = 1
                    group by 1,2

                )
                
                , delivery_attempts_agg as (

                    select
                        order_id
                        , min(event_datetime) as event_datetime
                        , min_by(route_id, event_datetime) as route_id
                        , min_by(failure_reason_id, event_datetime) as failure_reason_id
                        , min_by(dpms_id, event_datetime) as first_delivery_attempt_dpms_id
                        , max_by(dpms_id, event_datetime) filter(
                            where status = 'Success'
                        ) as delivery_success_dpms_id
                    from delivery_transaction_events
                    group by 1

                )

                select
                    base.order_id
                    , base.event_datetime as first_delivery_attempt_datetime
                    , first_dps.id as first_delivery_attempt_dp_id
                    , base.delivery_success_dpms_id
                    , drivers.display_name as first_delivery_attempt_driver_name
                    , failure_reasons.description as first_delivery_attempt_failure_reason
                    , dp_hours.open_time as first_delivery_attempt_dp_operating_hour_start
                    , dp_hours.close_time as first_delivery_attempt_dp_operating_hour_end
                from delivery_attempts_agg as base
                left join route_logs_enriched as routes
                    on base.route_id = routes.legacy_id
                left join drivers_enriched as drivers
                    on routes.driver_id = drivers.id
                left join failure_reasons
                    on base.failure_reason_id = failure_reasons.id
                left join dps_enriched as first_dps
                    on base.first_delivery_attempt_dpms_id = first_dps.dpms_id
                left join dps_enriched as success_dps
                    on base.delivery_success_dpms_id = success_dps.dpms_id 
                left join dp_operating_hours_deduplicate as dp_hours
                    on first_dps.id = dp_hours.dp_id
                    and date_format(event_datetime, 'EEEE') = dp_hours.day_of_week
                """,
            ),
            base.TransformView(
                view_name="first_dp_events_enriched",
                jinja_template="""
                with dp_events_adjusted as (

                    select
                        id
                        , dp_reservation_id
                        , dp_id
                        , name
                        , created_at
                        , lead(name, 1) over(partition by dp_reservation_id order by id) next_name
                    from dp_reservation_events_enriched
                    
                )

                , first_dp_events_agg as (

                    select
                        dp_reservation_id
                        , min_by(dp_id, id) as first_dp_id
                        , min_by(created_at, id) as first_dp_event_datetime
                        , min_by(name, id) as first_dp_event_name
                        , min_by(created_at, id) filter(
                            where name in ('SYSTEM_CONFIRMED', 'OPERATOR_CONFIRMED', 'CUSTOMER_CONFIRMED')
                        ) as first_slot_secured_datetime
                        , min_by(dp_id, id) filter(
                            where name in ('SYSTEM_CONFIRMED', 'OPERATOR_CONFIRMED', 'CUSTOMER_CONFIRMED')
                        ) as first_slot_secured_dp_id
                    from dp_events_adjusted
                    group by 1
                    
                )

                select
                    base.dp_reservation_id
                    , base.first_dp_id
                    , base.first_dp_event_datetime
                    , base.first_dp_event_name
                    , base.first_slot_secured_datetime
                    , base.first_slot_secured_dp_id
                    , dps.name as first_slot_secured_dp_name
                    , postcode.zones as first_slot_secured_dp_zone
                    , postcode.hub_id as first_slot_secured_dp_hub_id
                    , case
                        when dps.partner_id = 313 and dps.type = 'SHOP' then '7-11'
                        when dps.partner_id = 752 then 'Pick Lockers'
                        when dps.partner_id not in (313, 752) then 'Independent Stores'
                    end as first_slot_secured_dp_type
                from first_dp_events_agg as base
                left join dps_enriched dps
                    on base.first_slot_secured_dp_id = dps.id
                left join postcode_reference as postcode
                    on dps.postal_code = postcode.postcode

                """,
            ),
            base.TransformView(
                view_name="doorstep_reason",
                jinja_template="""
                with dp_events_sequence as (

                    select
                        dpr.order_id
                        , event.name
                        , event.created_at
                        , lead(event.name, 1) over(
                            partition by dpr.order_id
                            order by event.created_at
                        ) as next_name
                    from dp_reservations_enriched as dpr
                    left join dp_reservation_events_enriched as event
                        on dpr.id = event.dp_reservation_id
                    where
                        source in (
                            'FULLY_INTEGRATED_NINJA_COLLECT',
                            'SEMI_INTEGRATED_NINJA_COLLECT',
                            'RESCHEDULE',
                            'OPERATOR'
                        )

                )

                select
                    order_id
                    , max_by(next_name, created_at) as next_event_after_confirmed
                from dp_events_sequence
                where 
                    name in (
                        'SYSTEM_CONFIRMED'
                        , 'OPERATOR_CONFIRMED'
                        , 'CUSTOMER_CONFIRMED'
                        , 'SYSTEM_CONFIRMED_RESCHEDULE_FAILED_DELIVERY'
                    )
                    and name != next_name 
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.order_id
                    , base.tracking_id
                    , base.granular_status
                    , base.dp_collect_intended_flag
                    , order_destinations.delivery_dest_zone as latest_dest_zone
                    , order_destinations.delivery_dest_hub_id as latest_dest_hub_id
                    , base.earliest_dp_reservation_id
                    , base.earliest_drop_off_on
                    , base.earliest_dp_reservation_status
                    , base.latest_dp_reservation_id
                    , base.latest_drop_off_on
                    , base.latest_dp_reservation_status
                    , dd_attempt.first_delivery_attempt_datetime
                    , dd_attempt.first_delivery_attempt_dp_id
                    , dd_attempt.first_delivery_attempt_driver_name
                    , dd_attempt.first_delivery_attempt_failure_reason
                    , dd_attempt.first_delivery_attempt_dp_operating_hour_start
                    , dd_attempt.first_delivery_attempt_dp_operating_hour_end
                    , original_dp.id as original_dp_id
                    , case
                        when original_dp.partner_id = 313 and original_dp.type = 'SHOP' then '7-11'
                        when original_dp.partner_id = 752 then 'Pick Lockers'
                        when original_dp.partner_id not in (313, 752) then 'Independent Stores'
                    end as original_dp_type
                    , original_dp.name as original_dp_name
                    , dp_postcode.zones as original_dp_zone
                    , dp_postcode.hub_id as original_dp_hub_id
                    , slot_secured_dp_events.first_slot_secured_datetime as slot_secured_datetime
                    , slot_secured_dp_events.first_slot_secured_dp_id
                    , slot_secured_dp_events.first_slot_secured_dp_name
                    , slot_secured_dp_events.first_slot_secured_dp_zone
                    , slot_secured_dp_events.first_slot_secured_dp_hub_id
                    , slot_secured_dp_events.first_slot_secured_dp_type
                    , case
                        when base.granular_status = 'Completed' 
                            and dd_attempt.delivery_success_dpms_id is not null
                        then 'Delivered to DP'
                        when base.granular_status = 'Completed'
                            and dd_attempt.delivery_success_dpms_id is null
                        then 'Delivered to Doorstep'
                        when base.granular_status = 'Returned to Sender' then 'RTS'
                        when base.granular_status = 'Cancelled' then 'Cancelled'
                        else 'In Progress'
                    end as parcel_end_dest
                    , if(slot_secured_dp_events.first_slot_secured_dp_id != original_dp.id
                        , 1, 0
                    ) as redirected_to_dp_flag
                    , first_dp_events.first_dp_event_datetime
                    , first_dp_events.first_dp_event_name
                    , first_dp_events.first_dp_id as first_dp_event_dp_id
                    , case
                        when first_dp_events.first_dp_event_name is null then null
                        when dp_event_name_mapping.event_name is null then 'Others'
                        else dp_event_name_mapping.grouping_value
                    end as parcel_movement_status
                    , case
                        when dp_event_name_mapping.grouping_value = 'Overcapacity' then
                            case
                                when slot_secured_dp_events.first_slot_secured_dp_id is null
                                then 'leaked_out'
                                when date(slot_secured_dp_events.first_slot_secured_datetime)
                                    > date(first_dp_events.first_dp_event_datetime)
                                then 'retag_on_other_days'
                                when date(first_dp_events.first_dp_event_datetime) 
                                    = date(slot_secured_dp_events.first_slot_secured_datetime)
                                    and slot_secured_dp_events.first_slot_secured_dp_id = first_dp_events.first_dp_id
                                then 'same_day_org_retag'
                                when date(first_dp_events.first_dp_event_datetime) 
                                    = date(slot_secured_dp_events.first_slot_secured_datetime)
                                    and slot_secured_dp_events.first_slot_secured_dp_id != first_dp_events.first_dp_id
                                then 'same_day_other_dp_retag' 
                                else 'check'
                            end
                    end as overcap_outcome
                    , if(base.granular_status = 'Completed' and order_dp_milestones.driver_to_dp_datetime is null
                        , coalesce(doorstep_reason.next_event_after_confirmed, first_dp_events.first_dp_event_name)
                        , null
                    ) as doorstep_reason_event
                    , base.creation_datetime
                    , 'sg' as system_id
                    , base.created_month
                from orders_base as base
                left join order_destinations
                    on base.order_id = order_destinations.order_id
                left join delivery_attempts_enriched as dd_attempt
                    on base.order_id = dd_attempt.order_id
                left join dps_enriched as original_dp
                    on base.dp_short_name = original_dp.short_name
                left join postcode_reference as dp_postcode
                    on original_dp.postal_code = dp_postcode.postcode
                left join first_dp_events_enriched as first_dp_events
                    on base.earliest_dp_reservation_id = first_dp_events.dp_reservation_id
                left join first_dp_events_enriched as slot_secured_dp_events
                    on base.earliest_slot_secured_dp_reservation_id = slot_secured_dp_events.dp_reservation_id
                left join doorstep_reason
                    on base.order_id = doorstep_reason.order_id
                left join order_dp_milestones
                    on base.order_id = order_dp_milestones.order_id
                left join dp_event_name_mapping
                    on first_dp_events.first_dp_event_name = dp_event_name_mapping.event_name
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PUDO_COLLECT_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
