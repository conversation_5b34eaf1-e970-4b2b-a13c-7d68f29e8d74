import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.DPDAG.Task.DP_PARTNERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.DPDAG.Task.DP_PARTNERS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.DPProdGL(input_env, is_masked).PARTNERS, view_name="partners"),
            base.InputTable(
                path=delta_tables.GDrive(input_env).DP_PARTNERS_ENRICHED, view_name="dp_partners_enriched_gdrive"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    partners.id
                    , partners.dpms_partner_id
                    , partners.system_id as country
                    , partners.name
                    , partners.send_notifications_to_customer
                    , partners.driver_flow
                    , gdrive.dp_category
                    , from_utc_timestamp(partners.created_at, {{ get_local_timezone }}) as creation_datetime
                    , cast(if(partners.deleted_at is not null, 1, 0) as int) as is_deleted
                    , partners.system_id
                    , date_format(partners.created_at, 'yyyy-MM') as created_month
                from partners
                left join dp_partners_enriched_gdrive as gdrive on
                    partners.id = gdrive.id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("partners.system_id")},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DP_PARTNERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
