import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RTS_TRIGGER_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 7, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS,
                view_name="update_address_verification_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RTS_TRIGGER_EVENTS,
                view_name="rts_trigger_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="last_av_before_rts",
                jinja_template="""
                WITH base as (
                
                    SELECT
                        rts.order_id
                        , MAX_BY(update_av.av_hub_id, update_av.event_datetime)
                            FILTER(WHERE update_av.av_status = 'VERIFIED') AS last_verified_dest_hub_id_before_rts
                        , MAX_BY(update_av.av_hub_id, update_av.event_datetime)
                            FILTER(WHERE update_av.av_status = 'UNVERIFIED') AS last_unverified_dest_hub_id_before_rts
                    FROM rts_trigger_events AS rts
                    LEFT JOIN update_address_verification_events AS update_av
                        ON rts.order_id = update_av.order_id
                        AND rts.event_datetime > update_av.event_datetime
                    LEFT JOIN hubs_enriched AS hubs
                        ON update_av.av_hub_id = hubs.id
                    WHERE hubs.id is not null
                    GROUP BY 1
                
                )

                SELECT
                    order_id
                    , COALESCE(
                        last_verified_dest_hub_id_before_rts
                        , last_unverified_dest_hub_id_before_rts
                    ) AS last_dest_hub_id_before_rts
                FROM base
                """,
            ),
            base.TransformView(
                view_name="ranked",
                jinja_template="""
                SELECT order_id
                       , route_hub_id
                       , waypoint_zone_hub_id
                       , dest_hub_id
                       , route_zone
                       , waypoint_zone
                       , dest_zone
                       , type
                       , ROW_NUMBER() over(PARTITION BY order_id, type
                                           ORDER BY creation_datetime desc) AS rank
                FROM delivery_transaction_events
                """,
            ),
            base.TransformView(
                view_name="delivery",
                jinja_template="""
                SELECT order_id
                       , route_hub_id
                       , waypoint_zone_hub_id
                       , dest_hub_id
                       , route_zone
                       , waypoint_zone
                       , dest_zone
                FROM ranked
                WHERE type = 'delivery' AND rank = 1
                """,
            ),
            base.TransformView(
                view_name="rts",
                jinja_template="""
                SELECT order_id
                       , route_hub_id
                       , waypoint_zone_hub_id
                       , dest_hub_id
                       , route_zone
                       , waypoint_zone
                       , dest_zone
                FROM ranked
                WHERE type = 'rts' AND rank = 1
                """,
            ),
            base.TransformView(
                view_name="pre_final",
                jinja_template="""
                SELECT orders.id as order_id
                       , orders.tracking_id
                       {%- for type in ('delivery', 'rts') %}
                       , {{ type }}.route_hub_id as {{ type }}_route_hub_id
                       , {{ type }}.waypoint_zone_hub_id as {{ type }}_waypoint_zone_hub_id
                       , {{ type }}.dest_hub_id as {{ type }}_dest_hub_id
                       , {{ type }}.route_zone as {{ type }}_route_zone
                       , {{ type }}.waypoint_zone as {{ type }}_waypoint_zone
                       , {{ type }}.dest_zone as {{ type }}_dest_zone
                       {%- endfor %}
                       , date_format(orders.created_at, 'yyyy-MM') AS created_month
                FROM delivery
                FULL JOIN rts ON delivery.order_id = rts.order_id
                INNER JOIN orders on orders.id = coalesce(delivery.order_id, rts.order_id)
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT pre_final.order_id
                       , pre_final.tracking_id
                       , pre_final.delivery_route_hub_id
                       , pre_final.delivery_waypoint_zone_hub_id
                       , CAST(coalesce(
                            pre_final.delivery_dest_hub_id, last_av.last_dest_hub_id_before_rts
                       ) AS BIGINT) AS delivery_dest_hub_id
                       , pre_final.delivery_route_zone
                       , pre_final.delivery_waypoint_zone
                       , pre_final.delivery_dest_zone
                       , pre_final.rts_route_hub_id
                       , pre_final.rts_waypoint_zone_hub_id
                       , pre_final.rts_dest_hub_id
                       , pre_final.rts_route_zone
                       , pre_final.rts_waypoint_zone
                       , pre_final.rts_dest_zone
                       , pre_final.created_month
                FROM pre_final
                LEFT JOIN last_av_before_rts AS last_av
                    ON pre_final.order_id = last_av.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DESTINATIONS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
