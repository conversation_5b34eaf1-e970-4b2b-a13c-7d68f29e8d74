import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ID_FM_PAYROLL_MONTHLY_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ID_FM_PAYROLL_MONTHLY_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ID_FM_PAYROLL_DAILY_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing",),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ID_FM_PAYROLL_DAILY,
                view_name="id_fm_payroll_daily",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with final as (
                
                    select
                        system_id
                        , created_month
                        , route_month
                        , courier_id
                        , courier_name
                        , courier_type
                        , hub_region
                        , hub_id
                        , hub_name
                        , count(route_date) as days_worked
                        , sum(distinct_waypoints) as distinct_waypoints
                        , sum(capped_pickups) as capped_pickups
                        , sum(rpu_parcels) as rpu_parcels
                        , sum(waypoint_payment) as waypoint_payment
                        , sum(parcel_payment) as parcel_payment
                        , sum(rpu_payment) as rpu_payment
                        , sum(ocp_bonus) as ocp_bonus
                        , sum(sum_total) as sum_total
                    from id_fm_payroll_daily
                    group by {{ range(1, 10) | join(',') }}
                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_FM_PAYROLL_MONTHLY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
