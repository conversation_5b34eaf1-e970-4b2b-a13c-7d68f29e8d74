import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_DAILY_PUSH_OFF_ADHERENCE_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_DAILY_PUSH_OFF_ADHERENCE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,
                data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
                data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_THIRD_PARTY_TRANSFERS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(
                path=(
                        versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_CUTOFFS
                        + measurement_datetime_partition
                ),
                view_name="last_mile_push_off_cutoffs",
                system_id=system_id,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED_BASE,
                view_name="pets_tickets_enriched_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_THIRD_PARTY_TRANSFERS,
                view_name="order_third_party_transfers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            ## Create a shipment completion table with specific columns that will be used for unioning with order movements
            base.TransformView(
                view_name="shipment_completion_cte",
                jinja_template="""
                select
                    order_id
                    , dest_hub_id as hub_id
                    , 'HUB' as location_type
                    , shipment_orders_enriched.system_id
                    , shipment_orders_enriched.created_month
                    , shipment_id
                    , max(date(shipment_completion_datetime)) as shipment_completion_date
                    , max(shipment_completion_datetime) as shipment_completion_datetime
                from shipment_orders_enriched
                group by {{ range(1, 7) | join(',') }}
                """,
            ),

            ## Get Entry inbound into sort hub to exclude all records before sort hub.
            ## This is to filter out records where the pickup was done by a station that will not push off the parcel
            base.TransformView(
                view_name="order_movement_base",
                jinja_template="""
                with sort_hub_inbound_cte as (
                    select
                        order_movements.order_id
                        , min(order_movements.entry_datetime) as sort_hub_inbound_datetime
                    from order_movements
                    left join hubs_enriched
                        on order_movements.hub_id = hubs_enriched.id
                    where 1=1
                        and hubs_enriched.sort_hub_flag = 1
                    group by 1
                ),

                earliest_poh_cte as (
                    select
                        poh_order_metrics.order_id
                        , min(poh_metrics.handover_time) as earliest_handover_datetime
                    from poh_order_metrics
                    left join poh_metrics
                        on poh_order_metrics.hub_handover_id = poh_metrics.id
                    group by 1
                ),

                first_mile_handover_to_sort_cte as (
                    select
                        coalesce(earliest_poh_cte.order_id, sort_hub_inbound_cte.order_id) as order_id
                        , coalesce(earliest_poh_cte.earliest_handover_datetime, sort_hub_inbound_cte.sort_hub_inbound_datetime) as fm_handover_to_sort_hub_datetime
                    from earliest_poh_cte
                    full outer join sort_hub_inbound_cte
                        on earliest_poh_cte.order_id = sort_hub_inbound_cte.order_id
                )

                select 
                    order_movements.*
                from order_movements
                left join first_mile_handover_to_sort_cte
                    on order_movements.order_id = first_mile_handover_to_sort_cte.order_id
                where 1=1
                    and (
                        first_mile_handover_to_sort_cte.fm_handover_to_sort_hub_datetime is null
                        or
                        first_mile_handover_to_sort_cte.fm_handover_to_sort_hub_datetime < order_movements.entry_datetime
                    )
                """,
            ),
            ## Union both tables together.
            ## For order_movements table, filter out events that occur after delivery success
            base.TransformView(
                view_name="order_movements_cte",
                jinja_template="""
                select
                    order_movement_base.order_id
                    , order_milestones.tracking_id
                    , order_movement_base.hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.shipper_id
                    , order_movement_base.location_type
                    , order_movement_base.entry_datetime
                    , order_movement_base.exit_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_movement_base.system_id
                    , order_movement_base.created_month
                from order_movement_base
                left join order_milestones
                    on order_movement_base.order_id = order_milestones.order_id
                where 1=1
                    and (order_movement_base.entry_datetime < order_milestones.delivery_success_datetime
                        or order_milestones.delivery_success_datetime is null
                        )
                    and order_milestones.order_type != 'Return'
                union all
                select
                    shipment_completion_cte.order_id
                    , order_milestones.tracking_id
                    , shipment_completion_cte.hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.shipper_id
                    , shipment_completion_cte.location_type
                    , shipment_completion_cte.shipment_completion_datetime as entry_datetime
                    , null as exit_datetime
                    , null as rts_trigger_datetime
                    , shipment_completion_cte.system_id
                    , shipment_completion_cte.created_month
                from shipment_completion_cte
                left join order_milestones
                    on shipment_completion_cte.order_id = order_milestones.order_id
                where 1=1
                    and order_milestones.order_type != 'Return'
                """,
            ),

            ## Filter for orders that have Xfer to 3PL event, use this to filter out orders that are xfer to 3PL as these are not under LM
            base.TransformView(
                view_name="order_third_party_transfers_cte",
                jinja_template="""
                select
                    distinct order_id
                from order_third_party_transfers
                """,
            ),

            ## Recreate order hub movements by grouping the hubs with the same hub sequence together
            ## Code used for hub_sequence is legacy code from order_hub_movements
            base.TransformView(
                view_name="order_hub_movements_cte",
                jinja_template="""
                with hub_seq_cte as (
                select 
                    order_movements_cte.*
                    , SUM(IF(LAG(order_movements_cte.hub_id, 1) OVER(PARTITION BY order_movements_cte.order_id
                                                 ORDER BY order_movements_cte.entry_datetime) = order_movements_cte.hub_id, 0, 1))
                          OVER(PARTITION BY order_movements_cte.order_id
                               ORDER BY order_movements_cte.entry_datetime) AS hub_sequence
                from order_movements_cte
                left join order_third_party_transfers_cte
                    on order_third_party_transfers_cte.order_id = order_movements_cte.order_id
                left join shipper_attributes
                    on order_movements_cte.shipper_id = shipper_attributes.id
                where order_third_party_transfers_cte.order_id is null
                    and lower(shipper_attributes.sales_channel) not in ('test')
                order by order_movements_cte.entry_datetime
                ),

                final as (
                select
                    order_id
                    , tracking_id
                    , hub_id
                    , dest_hub_id
                    , hub_sequence
                    , system_id
                    , rts_trigger_datetime
                    , MIN(entry_datetime) AS entry_datetime
                    , MAX(exit_datetime) AS exit_datetime
                    , MIN(created_month) AS created_month
                from hub_seq_cte
                group by {{ range(1, 8) | join(',') }}
                )
                select * from final
                """,
            ),

            ## Obtain scans that belong to LM hubs
            ## For PH, we can rely on the is_delivery_hub logic
            ## For other countries take STATION/CROSSDOCK_STATION from their facility type
            base.TransformView(
                view_name="lm_movement_base",
                jinja_template="""
                select
                    order_hub_movements_cte.order_id
                    , order_hub_movements_cte.tracking_id
                    , order_hub_movements_cte.hub_id
                    , hubs_enriched.name as hub_name
                    , hubs_enriched.region as hub_region
                    , hubs_enriched.address_city
                    , order_hub_movements_cte.entry_datetime
                    , date(order_hub_movements_cte.entry_datetime) as entry_date
                    , order_hub_movements_cte.exit_datetime
                    , date(order_hub_movements_cte.exit_datetime) as exit_date
                    , order_hub_movements_cte.rts_trigger_datetime
                    , order_hub_movements_cte.created_month
                    , order_hub_movements_cte.system_id
                from order_hub_movements_cte
                inner join hubs_enriched
                    on order_hub_movements_cte.hub_id = hubs_enriched.id
                where 1=1
                    and ((hubs_enriched.facility_type like '%STATION%' and hubs_enriched.system_id not in ('ph','id'))
                    or (hubs_enriched.system_id = 'ph' and hubs_enriched.is_delivery_hub = 1)
                    or (hubs_enriched.system_id = 'id' and hubs_enriched.facility_type = 'STATION')
                    )
                """,
            ),

            ## Explode the result and apply cutoff
            base.TransformView(
                view_name="cutoff_cte",
                jinja_template="""
                with explode_cte as (
                select
                    *
                    , explode(sequence(entry_date, exit_date)) reference_date
                from lm_movement_base
                )

                select
                    explode_cte.order_id
                    , explode_cte.tracking_id
                    , explode_cte.hub_id
                    , explode_cte.hub_name
                    , explode_cte.address_city
                    , explode_cte.hub_region
                    , explode_cte.entry_datetime
                    , explode_cte.exit_datetime
                    , explode_cte.rts_trigger_datetime
                    , last_mile_push_off_cutoffs.cutoff as lm_hub_cutoff
                    , reference_date
                    , explode_cte.created_month
                    , explode_cte.system_id
                from explode_cte
                left join last_mile_push_off_cutoffs
                    on explode_cte.hub_id = last_mile_push_off_cutoffs.hub_id
                    and date(explode_cte.entry_datetime) >= last_mile_push_off_cutoffs.start_date
                    and date(explode_cte.entry_datetime) <= last_mile_push_off_cutoffs.end_date
                """,
            ),

            ## Get attempt flag for each date
            ## For each delivery attempt date, extract latest the transaction details from delivery_transaction_events
            base.TransformView(
                view_name="delivery_attempt_cte",
                jinja_template="""
                with delivery_transactions_base as (
                    select
                        order_id
                        , date(event_datetime) as attempt_date
                        , max(event_datetime) as attempt_datetime
                        , max_by(route_hub_id, event_datetime) as route_hub_id
                        , max_by(route_id, event_datetime) as route_id
                        , max_by(route_driver_id, event_datetime) as driver_id
                        , max_by(status, event_datetime) as attempt_status
                        , max_by(dest_zone, event_datetime) as dest_zone
                    from delivery_transaction_events
                    where valid_flag = 1
                    group by 1,2
                ),

                delivery_transaction_events_pending_orders as (
                    select
                        order_id
                        , max(dest_zone) as dest_zone
                    from delivery_transaction_events
                    where event_datetime is null
                    group by 1
                ),

                transactions_data_cte as (
                select
                    cutoff_cte.order_id
                    , cutoff_cte.tracking_id
                    , cutoff_cte.hub_id
                    , cutoff_cte.hub_name
                    , cutoff_cte.hub_region
                    , cutoff_cte.address_city
                    , cutoff_cte.entry_datetime
                    , cutoff_cte.exit_datetime
                    , cutoff_cte.rts_trigger_datetime
                    , if(delivery_transactions_base.dest_zone is null, last_value(delivery_transactions_base.dest_zone, true) over(partition by cutoff_cte.order_id, cutoff_cte.hub_id), delivery_transactions_base.dest_zone) as dest_zone
                    , cutoff_cte.lm_hub_cutoff
                    -- Only applicable for the date of RTS
                    , if(cutoff_cte.rts_trigger_datetime is null, null, 
                        if(date_format(cutoff_cte.rts_trigger_datetime, 'HHmm') <= cutoff_cte.lm_hub_cutoff 
                            and date(cutoff_cte.rts_trigger_datetime) = cutoff_cte.reference_date, 1,0)) as rts_before_cutoff_flag
                    , cutoff_cte.reference_date
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, delivery_transactions_base.route_id, null) as route_id
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, delivery_transactions_base.driver_id, null) as driver_id
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, drivers_enriched.display_name, null) as driver_name
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, drivers_enriched.driver_type, null) as driver_type
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, delivery_transactions_base.attempt_status, null) as attempt_status
                    , if(cutoff_cte.hub_id = delivery_transactions_base.route_hub_id, delivery_transactions_base.attempt_datetime, null) as attempt_datetime
                    , cutoff_cte.created_month
                    , cutoff_cte.system_id
                from cutoff_cte
                left join delivery_transactions_base
                    on cutoff_cte.order_id = delivery_transactions_base.order_id
                    and cutoff_cte.reference_date = delivery_transactions_base.attempt_date
                left join drivers_enriched
                    on drivers_enriched.id = delivery_transactions_base.driver_id
                )

                select
                    transactions_data_cte.order_id
                    , transactions_data_cte.tracking_id
                    , transactions_data_cte.hub_id
                    , transactions_data_cte.hub_name
                    , transactions_data_cte.hub_region
                    , transactions_data_cte.address_city
                    , transactions_data_cte.entry_datetime
                    , transactions_data_cte.exit_datetime
                    , transactions_data_cte.rts_trigger_datetime
                    , coalesce(transactions_data_cte.dest_zone, delivery_transaction_events_pending_orders.dest_zone) as dest_zone
                    , transactions_data_cte.lm_hub_cutoff
                    , transactions_data_cte.rts_before_cutoff_flag
                    , transactions_data_cte.reference_date
                    , transactions_data_cte.route_id
                    , transactions_data_cte.driver_id
                    , transactions_data_cte.driver_name
                    , transactions_data_cte.driver_type
                    , transactions_data_cte.attempt_status
                    , transactions_data_cte.attempt_datetime
                    , transactions_data_cte.created_month
                    , transactions_data_cte.system_id
                from transactions_data_cte
                left join delivery_transaction_events_pending_orders
                    on transactions_data_cte.order_id = delivery_transaction_events_pending_orders.order_id

                """,
            ),

            ## Get PETS Ticket details and explode to get dates in recovery
            base.TransformView(
                view_name="pets_cte",
                jinja_template="""
                with pets_base as (
                select
                    id as ticket_id
                    , order_id
                    , type
                    , sub_type
                    , status
                    , resolution_datetime
                    , cancellation_datetime
                    , if(entry_source_id = 19, 1,0) as auto_flow_pets_flag
                    , coalesce(resolution_datetime, cancellation_datetime) as pets_end_datetime
                    , creation_datetime as pets_creation_datetime
                    , date(coalesce(resolution_datetime, cancellation_datetime, date('{{ measurement_datetime }}'))) as exit_date
                    , date(creation_datetime) as entry_date
                from pets_tickets_enriched_base
                group by 1,2,3,4,5,6,7,8,9,10
                ),

                explode as (
                select
                    *
                    , explode(sequence(entry_date, exit_date)) as dates_in_recovery
                from pets_base
                )

                select * from explode
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),

            ## Join pets details to main table and calculate the d0 cutoff date
            base.TransformView(
                view_name="start_clock_cte",
                jinja_template="""
                with added_pets_info_cte as (
                select
                    delivery_attempt_cte.*
                    , if(delivery_attempt_cte.attempt_datetime is null 
                            and date_format(delivery_attempt_cte.exit_datetime, 'HHmm') < delivery_attempt_cte.lm_hub_cutoff
                                and date(delivery_attempt_cte.exit_datetime) = delivery_attempt_cte.reference_date, 1,0) as exit_current_hub_before_cutoff_flag
                    , pets_cte.ticket_id
                    , pets_cte.type as ticket_type
                    , pets_cte.sub_type as ticket_sub_type
                    , pets_cte.auto_flow_pets_flag
                    , pets_cte.status as pets_ticket_status
                    , pets_cte.pets_end_datetime
                    , pets_cte.pets_creation_datetime
                    , if(pets_cte.ticket_id is null, null,
                        if(date_format(pets_cte.pets_creation_datetime, 'HHmm') < date_format(delivery_attempt_cte.lm_hub_cutoff,'HHmm') 
                            and date(pets_cte.pets_creation_datetime) = delivery_attempt_cte.reference_date, 1, 0)) as pets_created_before_cutoff_flag
                    , if(pets_cte.ticket_id is null, null,
                        if(date_format(pets_cte.pets_end_datetime, 'HHmm') < date_format(delivery_attempt_cte.lm_hub_cutoff,'HHmm') 
                            and date(pets_cte.pets_end_datetime) = delivery_attempt_cte.reference_date, 1, 0)) as pets_resolved_before_cutoff_flag
                    , if(pets_cte.ticket_id is null, null,
                        if(pets_cte.auto_flow_pets_flag = 1 and pets_cte.type = 'MISSING', 1, 0)) as pets_auto_creation_flag
                from delivery_attempt_cte
                left join pets_cte
                    on delivery_attempt_cte.order_id = pets_cte.order_id
                    and delivery_attempt_cte.reference_date = pets_cte.dates_in_recovery
                )

                select 
                    *
                    -- If there's pets created before the cutoff and not lm liable OR
                    -- If there's pets created after cutoff but order was attempted within the day, use pets_end_datetime
                    , CASE WHEN (pets_end_datetime is not null and pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 1) 
                                or (attempt_datetime is not null and pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date != date(pets_creation_datetime)) THEN pets_end_datetime
                    -- If there's no pets or pets is lm liable and order enters LM hub, use entry_datetime
                        WHEN (ticket_id is null or pets_auto_creation_flag = 1) and date(entry_datetime) = reference_date 
                                or (pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date = date(pets_creation_datetime) and date(entry_datetime) = reference_date) THEN entry_datetime
                    -- If there's no route inbound the previous day and there's no pets and order did not enter LM hub today OR
                    -- If there's no route inbound in the previous day, PETS was created after the cutoff but order was attempted within the day, use reference date
                        WHEN ((ticket_id is null or pets_auto_creation_flag = 1) and date(entry_datetime) != reference_date)
                            or (pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date = date(pets_creation_datetime) and date(entry_datetime) != reference_date) THEN reference_date
                        ELSE pets_end_datetime
                    END AS start_clock
                    , CASE WHEN (pets_end_datetime is not null and pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 1)
                                or (attempt_datetime is not null and pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date != date(pets_creation_datetime)) THEN 'PETS End'
                        WHEN (ticket_id is null or pets_auto_creation_flag = 1) and date(entry_datetime) = reference_date 
                                or (pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date = date(pets_creation_datetime) and date(entry_datetime) = reference_date) THEN 'Shipment Completion'
                        WHEN ((ticket_id is null or pets_auto_creation_flag = 1) and date(entry_datetime) != reference_date)
                            or (pets_auto_creation_flag = 0 and pets_created_before_cutoff_flag = 0 and reference_date = date(pets_creation_datetime) and date(entry_datetime) != reference_date) THEN 'Reference Date'
                        ELSE 'PETS End'
                    END AS start_clock_reference
                from added_pets_info_cte
                """,
            ),

            ## Get calendar working day logic and calculate sla met
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with working_day_cte as (
                select
                    start_clock_cte.*
                    , case when date_format(start_clock_cte.start_clock, 'HHmm') < start_clock_cte.lm_hub_cutoff and calendar.working_day = 1 THEN calendar.next_working_day_0
                    ELSE calendar.next_working_day_1
                    END AS d0_sla_date
                from start_clock_cte
                left join calendar
                    on date(start_clock_cte.start_clock) = calendar.next_working_day_0
                     and ((calendar.region = 'national' and start_clock_cte.system_id not in ('my'))
                         or (start_clock_cte.system_id = 'my' and lower(calendar.region) = lower(start_clock_cte.address_city))
                         )
                order by reference_date
                ),

                measured_cte as (
                select
                    *
                    , if(d0_sla_date = reference_date and
                            reference_date <= date('{{ measurement_datetime }}') and
                                ((rts_before_cutoff_flag = 0 and reference_date <= date(rts_trigger_datetime)) or rts_before_cutoff_flag is null)
                                    and exit_current_hub_before_cutoff_flag = 0
                        , 1,0) as d0_sla_measured
                from working_day_cte
                ),

                met_cte as (
                select
                    *
                    , if(d0_sla_measured = 1 and attempt_datetime is not null,1,0) as d0_sla_met
                from measured_cte
                )

                select
                    order_id
                    , tracking_id
                    , hub_id
                    , hub_name
                    , hub_region
                    , address_city
                    , dest_zone
                    , entry_datetime
                    , exit_datetime
                    , reference_date
                    , rts_trigger_datetime
                    , lm_hub_cutoff
                    , rts_before_cutoff_flag
                    , route_id
                    , driver_id
                    , driver_name
                    , driver_type
                    , attempt_status
                    , attempt_datetime
                    , exit_current_hub_before_cutoff_flag
                    , ticket_id
                    , ticket_type
                    , ticket_sub_type
                    , pets_auto_creation_flag
                    , pets_end_datetime
                    , pets_resolved_before_cutoff_flag
                    , start_clock
                    , start_clock_reference
                    , d0_sla_date
                    , d0_sla_met
                    , d0_sla_measured
                    , system_id
                    , created_month
                from met_cte
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_DAILY_PUSH_OFF_ADHERENCE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()