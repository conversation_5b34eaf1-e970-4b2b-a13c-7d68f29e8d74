import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.LNK_ORDERS_SHIPPERS_MASKED,
        ),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LNK_ORDERS_SHIPPERS,
                view_name="orders_shippers",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    hub_inbound_scans as (
                        select
                            cast(id as bigint) as order_event_id
                            , cast(get_json_object(data, '$.scan_uuid') as string) as scan_uuid
                            , order_id AS order_id
                            , user_id
                            , user_grant_type
                            , cast(get_json_object(data, '$.raw_weight') as double) as raw_weight
                            , cast(get_json_object(data, '$.raw_length') as double) as raw_length
                            , cast(get_json_object(data, '$.raw_height') as double) as raw_height
                            , cast(get_json_object(data, '$.raw_width') as double) as raw_width
                            , cast(get_json_object(data, '$.device_id') as string) as device
                            , cast(get_json_object(data, '$.device_type') as string) as device_type
                            , cast(get_json_object(data, '$.hub_id') as int) as scan_hub_id
                            , cast(get_json_object(data, '$.weight.old_value') as double) as old_weight
                            , cast(get_json_object(data, '$.length.old_value') as double) as old_length
                            , cast(get_json_object(data, '$.width.old_value') as double) as old_width
                            , cast(get_json_object(data, '$.height.old_value') as double) as old_height
                            , cast(get_json_object(data, '$.parcel_size_id.old_value') as int) as old_size_id
                            , cast(get_json_object(data, '$.weight.new_value') as double) as new_weight
                            , cast(get_json_object(data, '$.length.new_value') as double) as new_length
                            , cast(get_json_object(data, '$.width.new_value') as double) as new_width
                            , cast(get_json_object(data, '$.height.new_value') as double) as new_height
                            , cast(get_json_object(data, '$.parcel_size_id.new_value') as int) as new_size_id
                            , from_utc_timestamp(time, {{ get_local_timezone }}) as scan_datetime
                            , system_id
                            , date_format(time, 'yyyy-MM') as created_month
                        from order_events
                        where
                            --order_events type 26 = HUB_INBOUND_SCAN
                            type = 26
                            and system_id <> 'mm'
                            -- DWS system only implemented after this date. We only need the report after this date.
                            and created_month >= '2020-12'

                    )
                    , base as (

                        select
                            *
                            , old_width * old_length * old_height as old_vol_weight_raw
                            , new_width * new_length * new_height as new_vol_weight_raw
                            , map_from_arrays(sequence(0, 5), array('s', 'm', 'l', 'xl', 'xxl', 'xs')) as id_to_size
                            , row_number() over (partition by scan_uuid order by scan_datetime desc) as rank
                        from hub_inbound_scans

                    )
                    , final as (

                        select
                            base.order_event_id
                            , base.scan_uuid
                            , base.order_id
                            , orders_shippers.shipper_id
                            , base.user_id
                            , coalesce(
                                    trim(concat_ws(' ', user_info.first_name, user_info.last_name))
                                    , user_info.display_name
                                    ) as user_name
                            , base.user_grant_type
                            , base.device
                            , base.device_type
                            , base.scan_hub_id
                            , base.raw_weight
                            , base.raw_length
                            , base.raw_width
                            , base.raw_height

                            {%- for type in ('old', 'new') %}
                            , base.{{ type }}_weight
                            , base.{{ type }}_length
                            , base.{{ type }}_width
                            , base.{{ type }}_height
                            , id_to_size[{{ type }}_size_id] as {{ type }}_parcel_size

                            , case

                                {%- for config in vol_weight_config %}
                                when (
                                    base.system_id = '{{ config['system_id'] }}'

                                    {%- if config['extra_condition'] %}
                                    and {{ config['extra_condition'] }}
                                    {%- endif %}

                                )
                                    then round((base.{{ type }}_vol_weight_raw/{{ config['divisor'] }}), 2)
                                {%- endfor %}

                            end as {{ type }}_vol_weight

                            {%- endfor %}

                            , base.scan_datetime
                            , base.system_id
                            , base.created_month
                        from base
                        left join user_info on
                            base.user_id = user_info.user_id
                        left join orders_shippers on
                            base.order_id = orders_shippers.order_id
                            and base.system_id = orders_shippers.system_id
                        where rank = 1

                    )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "vol_weight_config": [
                        {"system_id": "id", "divisor": 6000},
                        {
                            "system_id": "my",
                            "divisor": 6000,
                            "extra_condition": "orders_shippers.shipper_id in (45012, 42206)",
                        },
                        {"system_id": "my", "divisor": 5000},
                        {"system_id": "ph", "divisor": 3500},
                        {"system_id": "sg", "divisor": 6000},
                        {"system_id": "th", "divisor": 6000},
                        {"system_id": "vn", "divisor": 6000},
                    ],
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DIM_WEIGHT_SCANS_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
