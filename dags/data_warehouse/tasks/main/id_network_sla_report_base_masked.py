import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.ID_NETWORK_SLA_REPORT_BASE_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.ID_NETWORK_SLA_REPORT_BASE_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on=(
        data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUB_RELATION_SCHEDULES_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)

BASE_SELECTED_COLUMNS = (
    "order_id",
    "origin_hub_id",
    "pickup_hub_id",
    "inbound_hub_id",
    "origin_msh_hub_id",
    "origin_msh",
    "transit1_hub",
    "transit2_hub",
    "transit3_hub",
    "transit4_hub",
    "transit5_hub",
    "dest_msh_hub_id",
    "dest_msh",
    "dest_hub_id",
    "dest_hub_name",
    "pickup_datetime",
    "expected_poh_hour_adjustment",
    "inbound_datetime",
    "start_clock_datetime",
    "start_clock_classification",
    "start_clock_granular_classification",
    "crossdock_transit_flow",
)
MAX_TRANSIT_LOOP = 5
MAX_MILKRUN_TRANSIT_LOOP = 7

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    
    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=(
                    versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_CUTOFFS
                    + measurement_datetime_partition
                ),
                view_name="last_mile_push_off_cutoffs",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_AF_SF_SCHEDULES,
                view_name="id_af_sf_schedules",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_HUB_CONNECTIONS,
                view_name="id_hub_connections",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_MILKRUN_CONNECTIONS,
                view_name="id_milkrun_connections",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_RELATION_SCHEDULES_ENRICHED,
                view_name="hub_relation_schedules_enriched",
                system_id=system_id,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hub_connections_dedup",
                jinja_template="""
                select distinct
                    origin
                    , destination
                    , hub_1
                    , hub_2
                    , hub_3
                    , hub_4
                    , hub_5
                from id_hub_connections
                """,
            ),
            base.TransformView(
                view_name="af_sf_schedule_tidied",
                jinja_template="""
                with base as (
                
                {%- for dow in range(1,day_list|length + 1) %}
                    select
                        origin_hub
                        , dest_hub
                        , {{ dow }} as day
                        , departure
                        , duration
                    from id_af_sf_schedules
                    where
                        {{ day_list[dow-1] }} = 'TRUE'
                        and (reg_bi_remark is null or reg_bi_remark != 'not needed')
                    {% if not loop.last %}union all{% endif %}
                {%- endfor %}

                )

                , duration_sanity_check as (
                
                    select
                        *
                        , case
                            when duration like '%:%:%:%' then 'error'
                            when duration like '%:%:%' then 'complete'
                            when duration like '%:%' then 'missing_second'
                            else 'error'
                        end as completeness
                    from base
                
                )

                , parse_hour_min_sec as (
                
                    select
                        *
                        , if(completeness != 'error', cast(substring_index(duration, ':', 1) as int), null) hour
                        , case
                            when completeness = 'error' then null
                            when completeness = 'complete' then cast(right(substring(duration, 1, length(duration)-3), 2) as int)
                            when completeness = 'missing_second' then cast(right(duration, 2) as int)
                        end minute
                        , case
                            when completeness = 'error' then null
                            when completeness = 'complete' then cast(right(duration, 2) as int)
                            when completeness = 'missing_second' then 0
                        end as second
                    from duration_sanity_check
                
                )

                select
                    origin.id as origin_hub_id
                    , base.origin_hub as origin_hub_name
                    , dest.id as destination_hub_id
                    , base.dest_hub as destination_hub_name
                    , base.day
                    , base.departure
                    , base.hour * 3600 + base.minute * 60 + base.second as duration_seconds
                    , concat(base.day, '_', base.departure) as seq_index
                from parse_hour_min_sec as base
                left join hubs_enriched as origin
                    on base.origin_hub = origin.name
                left join hubs_enriched as dest
                    on base.dest_hub = dest.name
                """,
                jinja_arguments={
                    "day_list":("monday","tuesday","wednesday","thursday","friday","saturday","sunday"),
                }
            ),
            base.TransformView(
                view_name="asf_flag",
                jinja_template="""
                select distinct
                    origin_hub_id
                    , origin_hub_name
                    , destination_hub_id
                    , destination_hub_name
                    , 1 as asf_flag
                from af_sf_schedule_tidied
                """,
                jinja_arguments={
                    "day_list":("monday","tuesday","wednesday","thursday","friday","saturday","sunday"),
                }
            ),
            base.TransformView(
                view_name="linehaul_schedule",
                jinja_template="""
                with base as (
                
                    select
                        origin_hub_id
                        , origin_hub_name
                        , destination_hub_id
                        , destination_hub_name
                        , day
                        , date_format(start_time, 'HH:mm:ss') as departure
                        , duration * 60 as duration_seconds
                        , concat(day, '_', date_format(start_time, 'HH:mm:ss')) as seq_index
                    from hub_relation_schedules_enriched
                    where
                        deleted_at is null

                )

                , combined_base as (
                
                    select * from base
                    union all
                    select * from af_sf_schedule_tidied
                
                )

                , ranked_schedules as (
                
                    select
                        *
                        , row_number() over(
                            partition by origin_hub_id, destination_hub_id
                            order by seq_index
                        ) as rank
                    from combined_base
                
                )

                , dummy_first_row as (
                
                    select
                        origin_hub_id
                        , origin_hub_name
                        , destination_hub_id
                        , destination_hub_name
                        , 0 as day
                        , '00:00:00' as departure
                        , 0 as duration_seconds
                        , '0_00:00:00' as seq_index
                    from ranked_schedules
                    where rank = 1
                
                )

                , dummy_last_row as (
                
                    select
                        origin_hub_id
                        , origin_hub_name
                        , destination_hub_id
                        , destination_hub_name
                        , 7 + day as day
                        , departure
                        , duration_seconds
                        , concat(day + 7, '_', departure) as seq_index
                    from ranked_schedules
                    where rank = 1
                
                )

                , combined_schedules as (
                
                    select * from dummy_first_row
                    union all
                    select * from combined_base
                    union all
                    select * from dummy_last_row
                
                )

                , lead_schedules as (
                
                    select
                        *
                    {%- for col in lead_columns_list %}
                        , lead({{ col }}) over(
                            partition by origin_hub_id, destination_hub_id
                            order by seq_index
                        ) as next_{{ col }}
                    {%- endfor %}
                    from combined_schedules
                
                )

                select
                    origin_hub_id
                    , origin_hub_name
                    , destination_hub_id
                    , destination_hub_name
                    , seq_index
                {%- for col in lead_columns_list %}
                    , next_{{ col }}
                {%- endfor %}
                from lead_schedules
                where next_seq_index is not null
                """,
                jinja_arguments={
                    "lead_columns_list":(
                        'seq_index',
                        'day',
                        'departure',
                        'duration_seconds',
                    ),
                }
            ),
            base.TransformView(
                view_name="order_base",
                jinja_template="""
                with base as (

                    select
                        order_id
                        , origin_hub_id
                        , inbound_hub_id
                        , case
                            when inbound_hub_id = 439 then 'MAC-MAC'
                            else concat(left(inbound_hub_name, 3), '-', left(inbound_hub_name, 3))
                        end as origin_msh
                        , case
                            when dest_hub_id = 439 then 'MAC-MAC'
                            else concat(left(dest_hub_name, 3), '-', left(dest_hub_name, 3))
                        end as dest_msh
                        , dest_hub_id
                        , dest_hub_name
                        , pickup_hub_id
                        , pickup_datetime
                        , hour(pickup_datetime) as pickup_hour
                        , case
                            when hour(pickup_datetime) between 0 and 17 then 20
                            when hour(pickup_datetime) between 18 and 23 then 25
                        end as expected_poh_hour_adjustment
                        , inbound_hub_id
                        , inbound_datetime
                        , start_clock_datetime
                        , date_format(delivery_success_datetime, 'yyyy-MM') as created_month
                    from transit_time_report
                    where
                        granular_status = 'Completed'
                        and delivery_success_datetime is not null

                )

                , order_shipments as (
                
                    select distinct
                        order_id
                        , orig_hub_id
                    from shipment_orders_enriched
                
                )

                select
                    base.*
                    , if(proxy.order_id is not null, 1, 0) as shipment_at_pst_flag
                    , origin_msh.id origin_msh_hub_id
                    , dest_msh.id dest_msh_hub_id
                    , cast(transit.hub_1 as string) as transit1_hub
                    , cast(transit.hub_2 as string) as transit2_hub
                    , cast(transit.hub_3 as string) as transit3_hub
                    , cast(transit.hub_4 as string) as transit4_hub
                    , cast(transit.hub_5 as string) as transit5_hub
                from base
                left join order_shipments as proxy
                    on base.order_id = proxy.order_id
                    and base.pickup_hub_id = proxy.orig_hub_id
                left join hubs_enriched as origin_msh
                    on base.origin_msh = origin_msh.name
                left join hubs_enriched as dest_msh
                    on base.dest_msh = dest_msh.name
                left join hub_connections_dedup as transit
                    on base.origin_msh = transit.origin
                    and base.dest_msh = transit.destination
                where
                    base.created_month between '{{ min_created_month }}' and '{{ max_created_month }}'
                """,
                jinja_arguments={
                    "min_created_month":lookback_ranges.output.min,
                    "max_created_month":lookback_ranges.output.max,
                }
            ),
            base.TransformView(
                view_name="category_setup",
                jinja_template="""
                select
                    created_month
                    , order_id
                    , origin_hub_id
                    , pickup_hub_id
                    , inbound_hub_id
                    , origin_msh_hub_id
                    , origin_msh
                    , transit1_hub
                    , transit2_hub
                    , transit3_hub
                    , transit4_hub
                    , transit5_hub
                    , dest_msh_hub_id
                    , dest_msh
                    , dest_hub_id
                    , dest_hub_name
                    , pickup_datetime
                    , expected_poh_hour_adjustment
                    , inbound_datetime
                    , start_clock_datetime
                    , case
                        -- Normal flow of pickup and inbounded to MSH
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id = origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                        then 'pickup_msh_inbound'

                        -- Special flow where parcels are directly added to shipment without being inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                            and shipment_at_pst_flag = 1
                        then 'pickup_station_inbound'

                        -- Scenario where parcels are picked up at station and somehow inbounded at MSH directly
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                            and shipment_at_pst_flag = 0
                        then 'pickup_msh_inbound'

                        -- Weird flow of being picked up at MSH but inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id = origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then 'pickup_station_inbound'

                        -- Normal flow of pickup and inbounded to station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then 'pickup_station_inbound'

                        -- Direct inbound to MSH
                        when start_clock_datetime = inbound_datetime
                            and inbound_hub_id = origin_msh_hub_id
                        then 'direct_msh_inbound'

                        -- Direct inbound to station
                        when start_clock_datetime = inbound_datetime
                            and (inbound_hub_id != origin_msh_hub_id or origin_msh_hub_id is null)
                        then 'direct_station_inbound'
                        else 'check'
                    end as start_clock_classification
                    , case
                        when origin_msh = dest_msh then 'same_msh'
                        when transit5_hub is not null then '5_transit'
                        when transit4_hub is not null then '4_transit'
                        when transit3_hub is not null then '3_transit'
                        when transit2_hub is not null then '2_transit'
                        when transit1_hub is not null then '1_transit'
                        when transit1_hub is null then 'direct'
                        else 'no_connection'
                    end as crossdock_transit_flow
                    , case
                        -- Normal flow of pickup and inbounded to MSH
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id = origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                        then 'normal_msh_pu_ib'

                        -- Special flow where parcels are directly added to shipment without being inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                            and shipment_at_pst_flag = 1
                        then 'st_pu_direct_ats'

                        -- Scenario where parcels are picked up at station and somehow inbounded at MSH directly
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                            and shipment_at_pst_flag = 0
                        then 'st_pu_msh_ib'

                        -- Weird flow of being picked up at MSH but inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id = origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then 'msh_pu_st_ib'

                        -- Normal flow of pickup and inbounded to station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then 'normal_st_pu_ib'

                        -- Direct inbound to MSH
                        when start_clock_datetime = inbound_datetime
                            and inbound_hub_id = origin_msh_hub_id
                        then 'direct_msh_ib'

                        -- Direct inbound to station
                        when start_clock_datetime = inbound_datetime
                            and (inbound_hub_id != origin_msh_hub_id or origin_msh_hub_id is null)
                        then 'direct_st_ib'
                        else 'unknown'
                    end as start_clock_granular_classification
                    , case
                        -- Special flow where parcels are directly added to shipment without being inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id = origin_msh_hub_id
                            and shipment_at_pst_flag = 1
                        then pickup_hub_id

                        -- Weird flow of being picked up at MSH but inbounded at station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id = origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then inbound_hub_id

                        -- Normal flow of pickup and inbounded to station
                        when start_clock_datetime = pickup_datetime
                            and pickup_hub_id != origin_msh_hub_id
                            and inbound_hub_id != origin_msh_hub_id
                        then pickup_hub_id

                        -- Direct inbound to station
                        when start_clock_datetime = inbound_datetime
                            and (inbound_hub_id != origin_msh_hub_id or origin_msh_hub_id is null)
                        then inbound_hub_id
                    end as station_origin_hub_id
                from order_base
                """,
            ),
            base.TransformView(
                view_name="station_to_origin_msh",
                jinja_template="""
                with base as (
                
                    select
                        *
                        , case
                            -- Closest linehaul : pickup + 3 hours (IB) + 30 min (CS) + 30 min (buffer) < timeslot
                            when start_clock_classification = 'pickup_station_inbound'
                            then pickup_datetime + interval '4' hour
                            -- Closest linehaul : inbound + 30 min (CS) + 30 min (buffer) < timeslot
                            when start_clock_classification = 'direct_station_inbound'
                            then inbound_datetime + interval '1' hour
                        end as cutoff
                    from category_setup
                
                )

                , set_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from base
                
                )

                , set_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                            date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                            , ' ', schedule.next_departure) as timestamp) + interval '30' minute
                        as expected_departure_datetime_to_origin_msh
                    from set_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.station_origin_hub_id = schedule.origin_hub_id
                        and base.origin_msh_hub_id = schedule.destination_hub_id
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , set_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_origin_msh + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_origin_msh
                    from set_departure
                
                )

                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                from set_arrival
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                }
            ),
            base.TransformView(
                view_name="origin_msh_to_transit1",
                jinja_template="""
                with pre_base as (
                
                    select
                        *
                        , case
                            -- Closest linehaul : pickup + expected_poh_hour + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when start_clock_classification = 'pickup_msh_inbound'
                            then cast(date(pickup_datetime) as timestamp)
                                + cast(expected_poh_hour_adjustment || ' hour' as interval) + interval '3' hour
                            -- Closest linehaul : pickup + 1.5hr (CS) + 30min (buffer) < timeslot
                            when start_clock_classification = 'direct_msh_inbound'
                            then inbound_datetime + interval '2' hour
                            -- Closest linehaul : arrival + 30min (POH/IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when start_clock_classification in ('pickup_station_inbound', 'direct_station_inbound')
                            then expected_arrival_datetime_at_origin_msh + interval '3' hour
                        end as initial_cutoff
                    from station_to_origin_msh
                
                )

                , base as (
                
                    select
                        pre_base.*
                        -- For AF and SF will need extra buffer after CS 
                        , case
                            when asf_flag.asf_flag = 1 then pre_base.initial_cutoff + interval '1' hour '30' minute
                            else pre_base.initial_cutoff
                        end as cutoff
                        , if(asf_flag.asf_flag = 1, 60, 30) as evaluation_buffer_minute
                    from pre_base
                    left join asf_flag
                        on pre_base.origin_msh = asf_flag.origin_hub_name
                        and pre_base.transit1_hub = asf_flag.destination_hub_name
                
                )

                , set_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from base
                
                )

                , set_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                                date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                                , ' ', schedule.next_departure) as timestamp) 
                            + cast(base.evaluation_buffer_minute || ' minute' as interval)
                        as expected_departure_datetime_to_transit1
                    from set_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.origin_msh = schedule.origin_hub_name
                        and base.transit1_hub = schedule.destination_hub_name
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , set_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_transit1 + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_transit1
                    from set_departure
                
                )

                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                    , expected_departure_datetime_to_transit1
                    , expected_arrival_datetime_at_transit1
                from set_arrival
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                }
            ),
            base.TransformView(
                view_name="other_transits",
                jinja_template="""
                -- This is created to enable standardisation of loop
                with transit1 as (select * from origin_msh_to_transit1)
                
                {%- for seq in range(2,max_transit+1) %}

                , transit{{ seq }}_pre_base as (
                
                    select
                        * 
                        -- Closest linehaul : arrival + 1hr (IH) + 30min (buffer) < timeslot
                        , expected_arrival_datetime_at_transit{{ seq-1 }} + interval '1' hour '30' minute
                            as initial_cutoff
                    from transit{{ seq-1 }}
                
                )

                , transit{{ seq }}_base as (
                
                    select
                        pre_base.*
                        -- For AF and SF will need extra buffer after CS
                        , case
                            when asf_flag.asf_flag = 1 then pre_base.initial_cutoff + interval '1' hour '30' minute
                            else pre_base.initial_cutoff
                        end as cutoff
                        , if(asf_flag.asf_flag = 1, 60, 30) as evaluation_buffer_minute
                    from transit{{ seq }}_pre_base as pre_base
                    left join asf_flag
                        on pre_base.transit{{ seq-1 }}_hub = asf_flag.origin_hub_name
                        and pre_base.transit{{ seq }}_hub = asf_flag.destination_hub_name
                
                )

                , transit{{ seq }}_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from transit{{ seq }}_base
                
                )

                , transit{{ seq }}_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                                date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                                , ' ', schedule.next_departure) as timestamp) 
                            + cast(base.evaluation_buffer_minute || ' minute' as interval)
                        as expected_departure_datetime_to_transit{{ seq }}
                    from transit{{ seq }}_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.transit{{ seq-1 }}_hub = schedule.origin_hub_name
                        and base.transit{{ seq }}_hub = schedule.destination_hub_name
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , transit{{ seq }}_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_transit{{ seq }} + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_transit{{ seq }}
                    from transit{{ seq }}_departure
                
                )

                , transit{{ seq }} as (

                    select
                        created_month
                    {%- for col in base_select %}
                        , {{ col }}
                    {%- endfor %}
                        , expected_departure_datetime_to_origin_msh
                        , expected_arrival_datetime_at_origin_msh
                    {%- for new_col in range(1, seq) %}
                        , expected_departure_datetime_to_transit{{ new_col }}
                        , expected_arrival_datetime_at_transit{{ new_col }}
                    {%- endfor %}
                        , expected_departure_datetime_to_transit{{ seq }}
                        , expected_arrival_datetime_at_transit{{ seq }}
                    from transit{{ seq }}_arrival
                
                )
                {%- endfor %}

                -- This is to standardise loop exit
                select * from transit{{ max_transit }}
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="others_to_dest_msh",
                jinja_template="""
                with setup as (
                
                    select
                        *
                        , case
                    {%- for transit in range(1, max_transit+1)|reverse %}
                            when transit{{ transit }}_hub is not null
                            then transit{{ transit }}_hub
                    {%- endfor %}
                            else origin_msh
                        end as preceding_dest_msh_hub
                        , case
                    {%- for transit in range(1, max_transit+1)|reverse %}
                            when transit{{ transit }}_hub is not null
                            then expected_arrival_datetime_at_transit{{ transit }}
                    {%- endfor %}
                        end as expected_last_transit_arrival_datetime
                    from other_transits
                
                )

                , add_hub_id as (
                
                    select
                        setup.*
                        , hubs.id as preceding_dest_msh_hub_id
                    from setup
                    left join hubs_enriched as hubs
                        on setup.preceding_dest_msh_hub = hubs.name

                )

                , pre_base as (
                
                    select
                        *
                        , case
                            -- Closest linehaul : pickup + expected_poh_hour + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'direct' and start_clock_classification = 'pickup_msh_inbound'
                            then cast(date(pickup_datetime) as timestamp)
                                + cast(expected_poh_hour_adjustment || ' hour' as interval) + interval '3' hour
                            -- Closest linehaul : inbound + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'direct' and start_clock_classification = 'direct_msh_inbound'
                            then inbound_datetime + interval '2' hour
                            -- Closest linehaul : arrival + 30min (POA/IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'direct'
                                and start_clock_classification in ('pickup_station_inbound', 'direct_station_inbound')
                            then expected_arrival_datetime_at_origin_msh + interval '3' hour '30' minute
                            -- Closest linehaul : POA/IH + 1hr (IH) + 30min (buffer) < timeslot
                            -- This is for shipment that go through transit.
                            else expected_last_transit_arrival_datetime + interval '1' hour '30' minute
                        end as initial_cutoff
                    from add_hub_id
                
                )

                , base as (
                
                    select
                        pre_base.*
                        -- For AF and SF will need extra buffer after CS 
                        , case
                            when asf_flag.asf_flag = 1 then pre_base.initial_cutoff + interval '1' hour '30' minute
                            else pre_base.initial_cutoff
                        end as cutoff
                        , if(asf_flag.asf_flag = 1, 60, 30) as evaluation_buffer_minute
                    from pre_base
                    left join asf_flag
                        on pre_base.preceding_dest_msh_hub_id = asf_flag.origin_hub_id
                        and pre_base.dest_msh_hub_id = asf_flag.destination_hub_id
                
                )

                , set_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from base
                
                )

                , set_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                                date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                                , ' ', schedule.next_departure) as timestamp) 
                            + cast(base.evaluation_buffer_minute || ' minute' as interval)
                        as expected_departure_datetime_to_dest_msh
                    from set_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.preceding_dest_msh_hub_id = schedule.origin_hub_id
                        and base.dest_msh_hub_id = schedule.destination_hub_id
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , set_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_dest_msh + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_dest_msh
                    from set_departure
                
                )

                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                {%- for transit in range(1, max_transit+1) %}
                    , expected_departure_datetime_to_transit{{ transit }}
                    , expected_arrival_datetime_at_transit{{ transit }}
                {%- endfor %}
                    , expected_departure_datetime_to_dest_msh
                    , expected_arrival_datetime_at_dest_msh
                from set_arrival
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="dest_msh_to_milkrun1",
                jinja_template="""
                with classify_milkrun_transit as (
                
                    select
                        *
                    {% for seq in range(1, max_milkrun_transit+1) %}
                        , milkrun_config.hub_{{ seq }} as milkrun{{ seq }}_hub
                    {%- endfor %}
                        , case
                        {% for seq in range(1, max_milkrun_transit+1)|reverse %}
                            when milkrun_config.hub_{{ seq }} is not null then '{{ seq }}_transit'
                        {%- endfor %}
                            else 'direct'
                        end as milkrun_transit_flow
                    from others_to_dest_msh as base
                    left join id_milkrun_connections as milkrun_config
                        on base.dest_hub_name = milkrun_config.destination
                
                )

                , base as (
                
                    select
                        *
                        , case
                            -- Closest linehaul : pickup + expected_poh_hour + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'same_msh' and start_clock_classification = 'pickup_msh_inbound'
                            then cast(date(pickup_datetime) as timestamp)
                                + cast(expected_poh_hour_adjustment || ' hour' as interval) + interval '3' hour
                            -- Closest linehaul : inbound + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'same_msh' and start_clock_classification = 'direct_msh_inbound'
                            then inbound_datetime + interval '2' hour
                            -- Closest linehaul : arrival + 30min (POH/IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot 
                            when crossdock_transit_flow = 'same_msh'
                                and start_clock_classification in ('pickup_station_inbound', 'direct_station_inbound')
                            then expected_arrival_datetime_at_origin_msh + interval '3' hour '30' minute
                            -- Closest linehaul : Arrival + 30min (IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            -- This is for shipments that arrived from another crossdock
                            else expected_arrival_datetime_at_dest_msh + interval '3' hour '30' minute
                        end as cutoff
                    from classify_milkrun_transit
                
                )

                , set_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from base
                
                )

                , set_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                            date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                            , ' ', schedule.next_departure) as timestamp) + interval '30' minute
                        as expected_departure_datetime_to_milkrun1
                    from set_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.dest_msh_hub_id = schedule.origin_hub_id
                        and base.milkrun1_hub = schedule.destination_hub_name
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , set_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_milkrun1 + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_milkrun1
                    from set_departure
                
                )

                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                {%- for transit in range(1, max_milkrun_transit+1) %}
                    , milkrun{{ transit }}_hub
                {%- endfor %}
                    , milkrun_transit_flow
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                {%- for transit in range(1, max_transit+1) %}
                    , expected_departure_datetime_to_transit{{ transit }}
                    , expected_arrival_datetime_at_transit{{ transit }}
                {%- endfor %}
                    , expected_departure_datetime_to_dest_msh
                    , expected_arrival_datetime_at_dest_msh
                    , expected_departure_datetime_to_milkrun1
                    , expected_arrival_datetime_at_milkrun1 - interval '30' minute as expected_arrival_datetime_at_milkrun1
                from set_arrival
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                    "max_milkrun_transit":MAX_MILKRUN_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="other_milkrun_transits",
                jinja_template="""
                -- This is created to enable standardisation of loop
                with milkrun1 as (select * from dest_msh_to_milkrun1)
                
                {%- for seq in range(2,max_milkrun_transit+1) %}

                , milkrun{{ seq }}_base as (
                
                    select
                        *
                        -- Closest linehaul : arrival (no buffer given)
                        , expected_arrival_datetime_at_milkrun{{ seq-1 }} as cutoff
                    from milkrun{{ seq-1 }}
                
                )

                , milkrun{{ seq }}_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from milkrun{{ seq }}_base
                
                )

                , milkrun{{ seq }}_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                            date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                            , ' ', schedule.next_departure) as timestamp)
                        as expected_departure_datetime_to_milkrun{{ seq }}
                    from milkrun{{ seq }}_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.milkrun{{ seq-1 }}_hub = schedule.origin_hub_name
                        and base.milkrun{{ seq }}_hub = schedule.destination_hub_name
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , milkrun{{ seq }}_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_milkrun{{ seq }} + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_milkrun{{ seq }}
                    from milkrun{{ seq }}_departure
                
                )

                , milkrun{{ seq }} as (

                    select
                        created_month
                    {%- for col in base_select %}
                        , {{ col }}
                    {%- endfor %}
                    {%- for transit in range(1, max_milkrun_transit+1) %}
                        , milkrun{{ transit }}_hub
                    {%- endfor %}
                        , milkrun_transit_flow
                        , expected_departure_datetime_to_origin_msh
                        , expected_arrival_datetime_at_origin_msh
                    {%- for transit in range(1, max_transit+1) %}
                        , expected_departure_datetime_to_transit{{ transit }}
                        , expected_arrival_datetime_at_transit{{ transit }}
                    {%- endfor %}
                        , expected_departure_datetime_to_dest_msh
                        , expected_arrival_datetime_at_dest_msh
                    {%- for new_col in range(1, seq) %}
                        , expected_departure_datetime_to_milkrun{{ new_col }}
                        , expected_arrival_datetime_at_milkrun{{ new_col }}
                    {%- endfor %}
                        , expected_departure_datetime_to_milkrun{{ seq }}
                        , expected_arrival_datetime_at_milkrun{{ seq }}
                    from milkrun{{ seq }}_arrival
                )
                {%- endfor %}

                -- This is to standardise loop exit
                select * from milkrun{{ max_milkrun_transit }}
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                    "max_milkrun_transit":MAX_MILKRUN_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="linehaul_to_station",
                jinja_template="""
                with setup as (
                
                    select
                        *
                        , case
                    {%- for transit in range(1, max_milkrun_transit+1)|reverse %}
                            when milkrun{{ transit }}_hub is not null
                            then milkrun{{ transit }}_hub
                    {%- endfor %}
                            else dest_msh
                        end as last_hub_before_station
                        , case
                    {%- for transit in range(1, max_transit+1)|reverse %}
                            when milkrun{{ transit }}_hub is not null
                            then expected_arrival_datetime_at_milkrun{{ transit }}
                    {%- endfor %}
                        end as expected_last_milkrun_arrival_datetime
                    from other_milkrun_transits
                
                )

                , base as (
                
                    select
                        *
                        , case
                            -- Closest linehaul : pickup + expected_poh_hour + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'same_msh'
                                and start_clock_classification = 'pickup_msh_inbound'
                                and milkrun_transit_flow = 'direct'
                            then cast(date(pickup_datetime) as timestamp)
                                    + cast(expected_poh_hour_adjustment || ' hour' as interval) + interval '3' hour
                            -- Closest linehaul : inbound + 1.5hr (CS) + 30min (buffer) < timeslot
                            when crossdock_transit_flow = 'same_msh'
                                and start_clock_classification = 'direct_msh_inbound'
                                and milkrun_transit_flow = 'direct'
                            then inbound_datetime + interval '2' hour
                            -- Closest linehaul : arrival + 30min (POH/IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot 
                            when crossdock_transit_flow = 'same_msh'
                                and start_clock_classification in ('pickup_station_inbound', 'direct_station_inbound')
                                and milkrun_transit_flow = 'direct'
                            then expected_arrival_datetime_at_origin_msh + interval '3' hour '30' minute
                            -- Closest linehaul : Arrival + 30min (IH) + 1hr (IB) + 1.5hr (CS) + 30min (buffer) < timeslot
                            -- This is for shipments that arrived from another crossdock
                            when milkrun_transit_flow = 'direct'
                            then expected_arrival_datetime_at_dest_msh + interval '3' hour '30' minute
                            else expected_last_milkrun_arrival_datetime
                        end as cutoff
                    from setup
                
                )

                , set_cutoff_index as (
                
                    select
                        *
                        , weekday(cutoff) + 1 as cutoff_day
                        , concat(weekday(cutoff) + 1, '_', date_format(cutoff , 'HH:mm:ss')) as cutoff_index
                    from base
                
                )

                , set_departure as (
                
                    select
                        base.*
                        , schedule.next_duration_seconds
                        , cast(concat(
                            date(base.cutoff + cast(schedule.next_day - base.cutoff_day || ' day' as interval))
                            , ' ', schedule.next_departure) as timestamp) + interval '30' minute
                        as expected_departure_datetime_to_dest_hub
                    from set_cutoff_index as base
                    left join linehaul_schedule as schedule
                        on base.last_hub_before_station = schedule.origin_hub_name
                        and base.dest_hub_id = schedule.destination_hub_id
                        and base.cutoff_index > schedule.seq_index
                        and base.cutoff_index <= schedule.next_seq_index

                )

                , set_arrival as (
                
                    select
                        *
                        , expected_departure_datetime_to_dest_hub + cast(next_duration_seconds || ' second' as interval) 
                            as expected_arrival_datetime_at_dest_hub
                    from set_departure
                
                )

                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                {%- for transit in range(1, max_milkrun_transit+1) %}
                    , milkrun{{ transit }}_hub
                {%- endfor %}
                    , milkrun_transit_flow
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                {%- for transit in range(1, max_transit+1) %}
                    , expected_departure_datetime_to_transit{{ transit }}
                    , expected_arrival_datetime_at_transit{{ transit }}
                {%- endfor %}
                    , expected_departure_datetime_to_dest_msh
                    , expected_arrival_datetime_at_dest_msh
                {%- for seq in range(1, max_milkrun_transit+1) %}
                    , expected_departure_datetime_to_milkrun{{ seq }}
                    , expected_arrival_datetime_at_milkrun{{ seq }}
                {%- endfor %}
                    , expected_departure_datetime_to_dest_hub
                    , expected_arrival_datetime_at_dest_hub
                from set_arrival
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                    "max_milkrun_transit":MAX_MILKRUN_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="set_attempt_target",
                jinja_template="""
                select
                    base.*
                    , case
                        when base.expected_arrival_datetime_at_dest_hub is null then null
                        when date_format(base.expected_arrival_datetime_at_dest_hub, 'HH:mm:ss') <= cutoffs.cutoff
                        then cast(date(base.expected_arrival_datetime_at_dest_hub) as timestamp)
                            + interval '23' hour '59' minute '59' second
                        else cast(date(base.expected_arrival_datetime_at_dest_hub) as timestamp)
                            + interval '1' day + interval '23' hour '59' minute '59' second
                    end as expected_first_delivery_attempt_datetime
                from linehaul_to_station as base
                left join last_mile_push_off_cutoffs as cutoffs
                    on base.dest_hub_id = cutoffs.hub_id
                    and date(base.expected_arrival_datetime_at_dest_hub) >= cutoffs.start_date
                    and date(base.expected_arrival_datetime_at_dest_hub) <= cutoffs.end_date
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="data_hygiene_check",
                jinja_template="""
                select
                    *
                    , case
                        when expected_arrival_datetime_at_dest_hub is not null then 'no_issue'
                        when crossdock_transit_flow = 'same_msh' 
                            and start_clock_classification in ('pickup_station_inbound', 'direct_station_inbound')
                            and expected_arrival_datetime_at_origin_msh is null
                        then 'FM STCD Issue'
                        when crossdock_transit_flow != 'same_msh' 
                            and expected_arrival_datetime_at_dest_msh is null
                        then 'CDCD issue'
                        when milkrun_transit_flow = 'direct' then concat(dest_msh, ' > ', dest_hub_name)
                    {%- for mr in range (1, max_milkrun_transit+1) %}
                        when milkrun_transit_flow = '{{ mr }}_transit' then case
                            when expected_departure_datetime_to_milkrun1 is null
                            then concat(dest_msh, ' > ', milkrun1_hub)
                        {%- for seq in range(1,mr) %}
                            when expected_departure_datetime_to_milkrun{{ seq+1 }} is null
                            then concat(milkrun{{ seq }}_hub, ' > ', milkrun{{ seq+1 }}_hub)
                        {%- endfor %}
                            when expected_departure_datetime_to_dest_hub is null
                            then concat(milkrun{{ mr }}_hub, ' > ', dest_hub_name)
                            end 
                    {%- endfor %}
                        else 'check'
                    end as cdst_data_issue
                from set_attempt_target
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                    "max_milkrun_transit":MAX_MILKRUN_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    created_month
                {%- for col in base_select %}
                    , {{ col }}
                {%- endfor %}
                    , milkrun_transit_flow
                    , cdst_data_issue
                    , expected_departure_datetime_to_origin_msh
                    , expected_arrival_datetime_at_origin_msh
                {%- for transit in range(1, max_transit+1) %}
                    , expected_departure_datetime_to_transit{{ transit }}
                    , expected_arrival_datetime_at_transit{{ transit }}
                {%- endfor %}
                    , expected_departure_datetime_to_dest_msh
                    , expected_arrival_datetime_at_dest_msh
                    , if(milkrun_transit_flow = 'direct'
                        , expected_departure_datetime_to_dest_hub, expected_departure_datetime_to_milkrun1
                    ) as expected_departure_datetime_to_dest_hub
                    , expected_arrival_datetime_at_dest_hub
                    , expected_first_delivery_attempt_datetime
                from data_hygiene_check
                """,
                jinja_arguments={
                    "base_select":BASE_SELECTED_COLUMNS,
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_NETWORK_SLA_REPORT_BASE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.config("spark.sql.analyzer.maxIterations", "200").getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
