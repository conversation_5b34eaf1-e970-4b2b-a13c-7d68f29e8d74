import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_INTERMEDIATE_MASKED + ".py",
    task_name=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_INTERMEDIATE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
    ),
    depends_on=(data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_BASE_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_BASE,
                view_name="order_hub_history_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_PICKUPS,
                view_name="order_pickups",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=(
                        versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_CUTOFFS
                        + measurement_datetime_partition
                ),
                view_name="last_mile_push_off_cutoffs",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="first_poh_scan",
                jinja_template="""
                with poh_metrics_raw as (
                    select 
                        poh_order_metrics.order_id
                        , coalesce(hubs_enriched.parent_hub_id, poh_metrics.hub_id) hub_id
                        , poh_metrics.handover_time
                    from poh_order_metrics
                    left join poh_metrics
                        on poh_order_metrics.hub_handover_id = poh_metrics.id
                    left join hubs_enriched
                        on poh_metrics.hub_id = hubs_enriched.id
                    where poh_order_metrics.hub_handover_id is not null
                )

                    select
                        order_pickups.order_id
                        , order_pickups.success_datetime as om_pickup_datetime
                        , min(poh_metrics_raw.handover_time) as poh_datetime
                        , min_by(poh_metrics_raw.hub_id, poh_metrics_raw.handover_time) hub_id
                    from poh_metrics_raw
                    left join order_pickups
                        on poh_metrics_raw.order_id = order_pickups.order_id
                    group by 1,2
                """,
            ),

            base.TransformView(
                view_name="last_mile_push_off_cutoffs_cte",
                jinja_template="""
                select 
                    max_by(cutoff, end_date) as cutoff
                    , substring_index(max_by(cutoff, end_date), ':',1)  as hour
                    , substring_index(max_by(cutoff, end_date), ':',-1)  as minute
                from last_mile_push_off_cutoffs
                """,
            ),

            base.TransformView(
                view_name="sequence_before_dest_hub_cte",
                jinja_template="""
                with hub_tagging as (
                    select 
                        order_hub_history_base.*
                        , first_poh_scan.poh_datetime
                        , first_poh_scan.om_pickup_datetime
                        , if(coalesce(dps_enriched.dpms_id,order_hub_history_base.pickup_hub_id) = order_hub_history_base.hub_id and order_hub_history_base.hub_seq in (1,2), 1,0) is_pickup_hub
                        , if(order_hub_history_base.dest_hub_id = order_hub_history_base.hub_id, 1, 0) is_dest_hub
                        , cast(sum(order_hub_history_base.expected_duration_min) 
                              over (partition by (order_hub_history_base.order_id) order by order_hub_history_base.hub_seq desc rows between unbounded preceding and current row) as int) cumsum1
                        , to_timestamp(sla_date) + cast(last_mile_push_off_cutoffs_cte.hour || ' hour' as interval) + cast(last_mile_push_off_cutoffs_cte.minute || ' minute' as interval) as last_mile_hub_arrival_cutoff
                    from order_hub_history_base
                    left join first_poh_scan
                        on order_hub_history_base.order_id = first_poh_scan.order_id
                        and order_hub_history_base.hub_id = first_poh_scan.hub_id
                    left join dps_enriched
                        on order_hub_history_base.hub_id = dps_enriched.dpms_id
                    left join last_mile_push_off_cutoffs_cte
                ),

                dest_trip_cte as (
                    select 
                        order_id
                        , max(hub_seq) dest_hub_seq
                    from hub_tagging
                    where is_dest_hub = 1
                    group by 1
                )

                select 
                    hub_tagging.order_id
                    , hub_tagging.hub_id
                    , current_hub.name as hub_name
                    , hub_tagging.hub_seq
                    , hub_tagging.is_pickup_hub
                    , hub_tagging.is_dest_hub
                    , hub_tagging.sla_date
                    , hub_tagging.previous_hub_id
                    , previous_hub.name as previous_hub_name
                    , hub_tagging.next_hub_id
                    , next_hub.name as next_hub_name
                    , hub_tagging.system_id
                    , hub_tagging.poh_datetime
                    , hub_tagging.start_clock_datetime
                    , hub_tagging.pickup_hub_id
                    , if(hub_tagging.is_pickup_hub =1, hub_tagging.order_pickup_datetime, null) order_pickup_datetime
                    , dest_trip_cte.dest_hub_seq
                    , hub_tagging.sc_met
                    , hub_tagging.created_month
                    , hub_tagging.from_dp_to_customer_datetime
                    , hub_tagging.from_shipper_to_dp_datetime
                    , hub_tagging.from_driver_to_dp_datetime
                    , hub_tagging.from_dp_to_driver_datetime
                    , if(hub_tagging.hub_seq = dest_trip_cte.dest_hub_seq, hub_tagging.first_valid_delivery_attempt_datetime, null) first_valid_delivery_attempt_datetime
                    , lag(hub_tagging.actual_arrival_datetime) over (partition by hub_tagging.order_id order by hub_tagging.hub_seq asc) as trip_actual_arrival_datetime
                    , hub_tagging.order_hub_inbound_datetime
                    , hub_tagging.warehouse_sweep_datetime
                    , hub_tagging.order_add_to_shipment_datetime
                    , hub_tagging.shipment_closed_datetime
                    , hub_tagging.shipment_hub_inbound_datetime
                    , hub_tagging.shipment_van_inbound_datetime
                    , if(hub_tagging.is_dest_hub = 1 or hub_tagging.is_pickup_hub = 1,null,hub_tagging.stayover_datetime) stayover_datetime
                    , if(hub_tagging.is_dest_hub = 1, null, hub_tagging.shipment_id) outbound_shipment_id
                    , hub_tagging.outbound_trip_id
                    , hub_tagging.actual_start_datetime outbound_trip_actual_start_datetime
                    , hub_tagging.expected_start_datetime outbound_trip_expected_start_datetime
                    , hub_tagging.expected_duration_min outbound_trip_expected_duration_min
                    , hub_tagging.actual_arrival_datetime
                    , hub_tagging.on_time_start_flag outbound_trip_on_time_start_flag
                    , hub_tagging.on_time_end_flag outbound_trip_on_time_end_flag
                    , hub_tagging.trip_completion_datetime as outbound_trip_completion_datetime
                    , hub_tagging.driver_inb_datetime
                    , hub_tagging.last_mile_hub_arrival_cutoff
                    , hub_tagging.last_mile_hub_arrival_cutoff - CAST(hub_tagging.cumsum1||" minute" AS Interval) trip_start_cutoff
                    , ((bigint(to_timestamp(hub_tagging.actual_arrival_datetime))) - (bigint(to_timestamp(hub_tagging.actual_start_datetime))))/60 as outbound_trip_actual_duration_min
                from hub_tagging
                left join dest_trip_cte
                    on dest_trip_cte.order_id = hub_tagging.order_id
               left join hubs_enriched current_hub
                   on hub_tagging.hub_id = current_hub.id
               left join hubs_enriched next_hub
                   on hub_tagging.next_hub_id = next_hub.id
               left join hubs_enriched previous_hub
                   on hub_tagging.previous_hub_id = previous_hub.id
                where hub_tagging.hub_seq <= dest_trip_cte.dest_hub_seq
                """,
            ),

            base.TransformView(
                view_name="path_assignment_cte",
                jinja_template="""
                with sort_hub_flag_cte as (
                    select 
                        sequence_before_dest_hub_cte.* 
                        , sort_hub_flag is_sort_hub
                    from sequence_before_dest_hub_cte
                    left join hubs_enriched
                        on hubs_enriched.id = sequence_before_dest_hub_cte.hub_id
                ),

                sort_hub_seq as (
                    select 
                        order_id
                         , min(hub_seq) sort_hub_seq
                     from sort_hub_flag_cte
                     where is_sort_hub = 1
                     group by 1
                ),

                sort_hub_lag as (
                select 
                    sort_hub_flag_cte.order_id
                    , hub_id
                    , hub_seq
                    , is_pickup_hub
                    , is_dest_hub
                    , stayover_datetime
                    , sort_hub_seq
                    , lag(hub_id) over (partition by sort_hub_flag_cte.order_id order by sort_hub_flag_cte.hub_seq) lag_hub_id
                 from sort_hub_flag_cte
                 left join sort_hub_seq
                    on sort_hub_flag_cte.order_id = sort_hub_seq.order_id
                ),

                hub_path as (
                     select 
                         order_id
                         , collect_list(case when hub_id = lag_hub_id then null else hub_id end) as full_path
                     from sort_hub_lag
                    group by order_id   
                ),

                short_path as (
                    select 
                        order_id
                        , collect_list(case when hub_id = lag_hub_id then null else hub_id end) as short_path
                    from sort_hub_lag
                    where is_pickup_hub = 0 and is_dest_hub = 0
                    and hub_seq >= sort_hub_seq
                    and stayover_datetime is null
                    group by order_id 
                )

                select 
                    sort_hub_flag_cte.*
                    , row_number() over (partition by sort_hub_flag_cte.order_id order by sort_hub_flag_cte.hub_seq asc) as hub_seq1
                    , cast(hub_path.full_path as string) full_path
                    , cast(short_path.short_path as string) short_path
                from sort_hub_flag_cte
                left join hub_path
                        on hub_path.order_id = sort_hub_flag_cte.order_id
                left join short_path
                        on short_path.order_id = sort_hub_flag_cte.order_id
                """,
            ),
            base.TransformView(
                view_name="order_shipment_movements",
                jinja_template="""
                with order_hub_inbound_seq_cte as (
                select 
                    path_assignment_cte.*
                    , if(order_hub_inbound_datetime is null, null, row_number() over (partition by order_id order by order_hub_inbound_datetime asc nulls last)) as order_hub_inb_hub_seq1
                from path_assignment_cte
                ),

                limit_cte as (
                select *
                    , if(order_hub_inb_hub_seq1 = 1,1,0) is_first_order_hub_inb_hub
                    , if(hub_seq = dest_hub_seq, 1,0) final_destination
                    , if(hub_seq = dest_hub_seq, null,next_hub_id) next_hub_id1
                    , if(outbound_shipment_id is not null and outbound_trip_id is null and is_dest_hub = 0,1,0) missing_outbound_trip_flag
                 from order_hub_inbound_seq_cte
                where dest_hub_seq < 12
                ),

                filter_hub_seq_cte as (
                    select 
                        order_id
                        , hub_seq as order_hub_inb_hub_seq
                    from limit_cte
                    where order_hub_inb_hub_seq1 = 1
                )

                select 
                    limit_cte.*
                    , if(limit_cte.hub_seq < filter_hub_seq_cte.order_hub_inb_hub_seq, -1, if(limit_cte.hub_seq = filter_hub_seq_cte.order_hub_inb_hub_seq,0,1)) hub_flag_wrt_first_inb
                from limit_cte
                left join filter_hub_seq_cte 
                    on limit_cte.order_id = filter_hub_seq_cte.order_id   
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_INTERMEDIATE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()