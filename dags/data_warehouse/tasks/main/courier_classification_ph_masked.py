import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.COURIER_CLASSIFICATION_PH_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.COURIER_CLASSIFICATION_PH_MASKED,
    system_ids=(
        constants.SystemID.PH,
    ),
    depends_on=(
        data_warehouse.FleetDAG.Task.ROUTE_CLASSIFICATION_PH_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_CLASSIFICATION_PH,
                view_name="route_classification_ph",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="route_classification_ph_filtered",
                jinja_template="""
                select
                    *
                    , case
                        when courier_type in (
                            'Rider - Monthly Pay'
                            , 'Rider - Seasonal'
                            , 'Rider - Reliever'
                            , 'Rider - Per Parcel Rate'
                            , 'LM - Walker/Biker'
                            , 'LM-Rider-3W'
                            , 'Driver Aide - Last-Mile'
                            , 'LM-Rider-Monthly Pay'
                            , 'LM-Driver Aide'
                            , 'LM-Rider-Seasonal'
                            , 'LM-Rider-Reliever'
                            , 'LM-Rider-Per Parcel Rate'
                            , 'LM-Walker/Biker'
                        ) then 1
                    end included_in_classification
                from route_classification_ph
                """,
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                with rt_range_new as (
                    select
                        hub_region
                        , route_travel_range
                    from (values
                        ('Mindanao', 334),
                        ('Visayas', 243),
                        ('Western Visayas', 243),
                        ('Central Visayas', 243),
                        ('North Luzon', 216),
                        ('GMA', 127),
                        ('Metro Manila', 70),
                        ('Eastern Visayas', 204),
                        ('South Luzon', 493),
                        ('Palawan', 493)
                    ) as t(hub_region, route_travel_range)
                  
                )

                select
                    route_classification_ph_filtered.country
                    , route_classification_ph_filtered.courier_id
                    , route_classification_ph_filtered.courier_type
                    , route_classification_ph_filtered.hub_region
                    , route_classification_ph_filtered.route_id
                    , route_classification_ph_filtered.5m_stop/cast(route_classification_ph_filtered.on_zone_hours as double) as spozh
                    , route_classification_ph_filtered.route_travel_distance
                    , route_classification_ph_filtered.route_date
                    , case
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Saturday' then date(route_classification_ph_filtered.route_date)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Sunday' then date(route_classification_ph_filtered.route_date - interval '1' day)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Monday' then date(route_classification_ph_filtered.route_date - interval '2' day)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Tuesday' then date(route_classification_ph_filtered.route_date - interval '3' day)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Wednesday' then date(route_classification_ph_filtered.route_date - interval '4' day)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Thursday' then date(route_classification_ph_filtered.route_date - interval '5' day)
                        when date_format(route_classification_ph_filtered.route_date, 'EEEE') = 'Friday' then date(route_classification_ph_filtered.route_date - interval '6' day)
                        else null
                    end as week_start_date
                    , route_classification_ph_filtered.parcels_delivered_size_xs
                    , route_classification_ph_filtered.parcels_delivered_size_s
                    , route_classification_ph_filtered.parcels_delivered_size_m
                    , route_classification_ph_filtered.parcels_delivered_size_l
                    , route_classification_ph_filtered.parcels_delivered_size_xl
                    , route_classification_ph_filtered.parcels_delivered_size_xxl
                    , if(route_classification_ph_filtered.invalid_coordinates_count > 0, 1, 0) as invalid_coordinates
                    , if(route_classification_ph_filtered.driver_exceeded_100kmph > 0, 1, 0) as driver_exceeded_100kmph
                    , if(route_classification_ph_filtered.p2p_distance_isnan > 0 or route_classification_ph_filtered.p2p_distance_isnull > 0 or route_classification_ph_filtered.route_travel_distance > rt_range_new.route_travel_range, 1, 0) 
                        as invalid_distance_travelled
                    , if(route_classification_ph_filtered.on_zone_hours < 5, 1, 0) as invalid_on_zone_hours
                    , case
                        when route_classification_ph_filtered.invalid_coordinates_count > 0 then 1
                        when route_classification_ph_filtered.driver_exceeded_100kmph > 0 then 1
                        when route_classification_ph_filtered.p2p_distance_isnan > 0 then 1
                        when route_classification_ph_filtered.p2p_distance_isnull > 0 then 1
                        when route_classification_ph_filtered.route_travel_distance > rt_range_new.route_travel_range then 1
                        when route_classification_ph_filtered.on_zone_hours < 5 then 1
                        else 0
                    end as excluded_route_flag
                from route_classification_ph_filtered 
                left join rt_range_new 
                    on route_classification_ph_filtered.hub_region = rt_range_new.hub_region
                where 
                    hub_is_delivery_hub = 1
                    and included_in_classification = 1
                """,
            ),
            base.TransformView(
                view_name="reference",
                jinja_template="""
                with distinct_date as (
                    select distinct
                        week_start_date classification_start
                        , date(week_start_date + interval '6' days) classification_end
                        --, date(week_start_date - interval '4' week) start_week
                    from base 
                ),

                --This is done due to PH pushing forward one fo the classification period as there was long holiday

                hardcoded_date_adjustment (
                    select
                        case
                            when classification_start = date('2022-10-29') then date('2022-10-28')
                            when classification_start = date('2023-06-24') then date('2023-06-23')
                            when classification_start = date('2024-04-06') then date('2024-04-04')
                            else classification_start
                        end classification_start
                        , case
                            when classification_end = date('2022-10-28') then date('2022-10-27')
                            when classification_end = date('2023-06-23') then date('2023-06-22')
                            when classification_end = date('2024-04-05') then date('2024-04-03')
                            else classification_end
                        end classification_end
                    from distinct_date
                ),

                classification_base as (
                    select
                        base.hub_region
                        , base.route_id
                        , base.spozh
                        , base.route_travel_distance
                        , base.route_date
                        , hardcoded_date_adjustment.classification_start
                        , hardcoded_date_adjustment.classification_end
                        , base.excluded_route_flag
                    from hardcoded_date_adjustment
                    left join base
                        on base.route_date >= hardcoded_date_adjustment.classification_start
                        and base.route_date <= hardcoded_date_adjustment.classification_end
                    where excluded_route_flag = 0
                )

                select
                    hub_region
                    , classification_start classification_start_date
                    , classification_end classification_end_date
                    , percentile(route_travel_distance, 0.5) route_travel_median
                    , percentile(spozh, 0.5) spozh_median
                    , count(*) measured_routes
                from classification_base
                group by 1,2,3
                """,
            ),
            base.TransformView(
                view_name="joined",
                jinja_template="""
                select
                    base.country
                    , base.courier_id
                    , base.courier_type
                    , base.route_id
                    , reference.hub_region
                    , reference.classification_start_date
                    , reference.classification_end_date
                    , reference.route_travel_median
                    , reference.spozh_median
                    , base.parcels_delivered_size_xs
                    , base.parcels_delivered_size_s
                    , base.parcels_delivered_size_m
                    , base.parcels_delivered_size_l
                    , base.parcels_delivered_size_xl
                    , base.parcels_delivered_size_xxl
                    , base.spozh spozh_route
                    , base.route_travel_distance as route_travel_route
                    , base.invalid_coordinates
                    , base.driver_exceeded_100kmph
                    , base.invalid_distance_travelled
                    , base.invalid_on_zone_hours
                    , base.excluded_route_flag
                    , if(base.excluded_route_flag = 1, null, base.route_travel_distance) as route_travel_distance
                    , case
                        when base.excluded_route_flag = 1 then null
                        else if(base.spozh >= reference.spozh_median, 1, 0)
                    end as high_sp_flag
                    , case 
                        when base.excluded_route_flag = 1 then null
                        else if(base.route_travel_distance >= reference.route_travel_median, 1, 0)
                    end as high_rt_flag
                from base
                left join reference
                    on base.hub_region = reference.hub_region
                    and base.route_date >= reference.classification_start_date
                    and base.route_date <= reference.classification_end_date
                """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                with aggregated as (
                    select
                        country
                        , courier_id
                        , courier_type
                        , hub_region
                        , classification_start_date
                        , classification_end_date
                        , route_travel_median
                        , spozh_median
                        , sum(parcels_delivered_size_xs) as parcels_delivered_size_xs
                        , sum(parcels_delivered_size_s) as parcels_delivered_size_s
                        , sum(parcels_delivered_size_m) as parcels_delivered_size_m
                        , sum(parcels_delivered_size_l) as parcels_delivered_size_l
                        , sum(parcels_delivered_size_xl) as parcels_delivered_size_xl
                        , sum(parcels_delivered_size_xxl) as parcels_delivered_size_xxl
                        , count(*) as total_route_counts
                        , count_if(excluded_route_flag = 0) as total_measured_routes
                        , sum(route_travel_distance) as total_route_travel_distance
                        , sum(excluded_route_flag) as total_invalid_routes
                        , sum(invalid_coordinates) as total_invalid_coordinate_routes
                        , sum(driver_exceeded_100kmph) as total_driver_exceeded_100kmph_routes
                        , sum(invalid_distance_travelled) as total_invalid_distance_travelled_routes
                        , sum(invalid_on_zone_hours) as total_invalid_on_zone_hours_routes
                        , avg(spozh_route) filter(where excluded_route_flag = 0) as average_spozh
                        , count_if(high_sp_flag = 0 and high_rt_flag = 0) as urban_bot_count
                        , count_if(high_sp_flag = 1 and high_rt_flag = 0) as urban_top_count
                        , count_if(high_rt_flag = 0) as urban_count
                        , (count_if(high_rt_flag = 0) + sum(excluded_route_flag)) as adjusted_urban_count
                        , count_if(high_sp_flag = 1 and high_rt_flag = 1) as suburban_count
                        , count_if(high_sp_flag = 0 and high_rt_flag = 1) as rural_count
                        
                    from joined
                    group by 1,2,3,4,5,6,7,8
                )

                select
                    country as system_id
                    , courier_id
                    , courier_type
                    , hub_region
                    , date_format(classification_start_date, 'yyyy-MM') as created_month
                    , classification_start_date
                    , classification_end_date
                    , route_travel_median
                    , spozh_median
                    , parcels_delivered_size_xs
                    , parcels_delivered_size_s
                    , parcels_delivered_size_m
                    , parcels_delivered_size_l
                    , parcels_delivered_size_xl
                    , parcels_delivered_size_xxl
                    , total_route_counts
                    , total_measured_routes
                    , total_route_travel_distance
                    , total_invalid_routes
                    , total_invalid_coordinate_routes
                    , total_driver_exceeded_100kmph_routes
                    , total_invalid_distance_travelled_routes
                    , total_invalid_on_zone_hours_routes
                    , average_spozh
                    , urban_bot_count
                    , urban_top_count
                    , urban_count
                    , adjusted_urban_count
                    , suburban_count
                    , rural_count
    
                    -- new logic
                    , greatest(adjusted_urban_count, suburban_count, rural_count) greatest_count
                    , case
                        when greatest(urban_count, suburban_count, rural_count) = 0 then 'DEFAULTED HIGH DENSE'
                        when greatest(adjusted_urban_count, suburban_count, rural_count) = adjusted_urban_count and greatest(adjusted_urban_count, suburban_count, rural_count) = rural_count then 'MID DENSE'
                        when greatest(adjusted_urban_count, suburban_count, rural_count) = adjusted_urban_count then 'HIGH DENSE'
                        when greatest(adjusted_urban_count, suburban_count, rural_count) = suburban_count then 'MID DENSE'
                        when greatest(adjusted_urban_count, suburban_count, rural_count) = rural_count then 'LOW DENSE'
                        else 'ERROR'
                    end as courier_class
                from aggregated
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COURIER_CLASSIFICATION_PH,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()