import sys

from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, <PERSON>ructField, MapType, StringType, IntegerType, FloatType, LongType
from pyspark.sql.functions import udf, size, col

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID
import h3

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FEATURES_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FEATURES_MASKED,
    depends_on=(
        data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_ORDERS_BASE_MASKED,
        data_warehouse.FakePhysicalParcelDAG.Task.SELLER_ORDER_FEATURES_MASKED,
        data_warehouse.FakePhysicalParcelDAG.Task.CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES_MASKED,
        data_warehouse.FakePhysicalParcelDAG.Task.CONSIGNEE_EMAIL_ORDER_FEATURES_MASKED,
        data_warehouse.FakePhysicalParcelDAG.Task.PARCEL_SCAN_FEATURES_MASKED,
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_BASE,
                view_name="lazada_orders_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SELLER_ORDER_FEATURES,
                view_name="seller_order_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES,
                view_name="consignee_phone_number_order_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).CONSIGNEE_EMAIL_ORDER_FEATURES,
                view_name="consignee_email_order_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PARCEL_SCAN_FEATURES,
                view_name="parcel_scan_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hv_order_features",
                jinja_template="""

                select
                    base.order_id
                    , base.tracking_id
                    , base.creation_datetime
                    , base.created_date
                    , base.granular_status
                    , base.shipper_id
                    , base.shipper_name
                    , base.seller_id
                    , base.seller_name
                    , base.original_to_address1
                    , base.original_to_address2
                    , base.original_to_address
                    , base.original_to_name
                    , base.original_to_email
                    , split(base.original_to_email, '@')[1] as original_to_email_domain
                    , if(
                        split(base.original_to_email, '@')[1] in {{ reputable_email_domain_list }}, 1, 0
                    ) as reputable_consignee_email_domain_flag
                    , base.original_to_number
                    , base.items
                    , base.item_length
                    , base.insurance_value
                    , base.cod_value
                    , base.potential_claim_value
                    , base.insurance_value_sgd
                    , base.cod_value_sgd
                    , base.cod_insurance_discrepancy_factor
                    , base.parcel_size
                    , base.nv_width
                    , base.nv_height
                    , base.nv_length
                    , base.original_weight
                    , base.nv_weight
                    , base.inbound_datetime
                    , base.inbound_date
                    , base.pickup_datetime
                    , base.pu_scan_flag
                    , base.pickup_hour
                    , base.oc_to_pu_hours
                    , base.oc_to_ib_hours
                    , base.origin_hub_id
                    , base.origin_hub_name
                    , base.origin_hub_latitude
                    , base.origin_hub_longitude
                    , base.origin_hub_region
                    , base.dest_hub_id
                    , base.dest_hub_name
                    , base.dest_hub_latitude
                    , base.dest_hub_longitude
                    , base.dest_hub_region
                    , base.sla_days
                    , base.rts_sla_days
                    , base.known_fraud_timestamp
                    , base.resolution_datetime
                    , base.price_suspicious_score

                    -- Seller features
                    , seller_features.daily_order_count as seller_daily_order_count
                    , seller_features.days_from_onboarded as seller_days_from_onboarded
                    , seller_features.days_from_first_order_creation as seller_days_from_first_order_creation
                    , seller_features.new_seller_with_hv_order_flag
                    , seller_features.l30d_avg_daily_count as seller_l30d_avg_daily_count
                    , seller_features.l30d_standard_deviation as seller_l30d_standard_deviation
                    , seller_features.z_score as seller_z_score
                    , seller_features.l30d_orders_created as seller_l30d_orders_created
                    , seller_features.l30d_orders_completed as seller_l30d_orders_completed
                    , seller_features.l30d_order_completion_rate as seller_l30d_orders_completion_rate
                    , seller_features.l30d_hv_orders_created as seller_l30d_hv_orders_created
                    , seller_features.l30d_hv_orders_completed as seller_l30d_hv_orders_completed
                    , seller_features.l30d_hv_order_completion_rate as seller_l30d_hv_orders_completion_rate
                    , seller_features.l90d_orders_intercepted as seller_l90d_orders_intercepted
                    , seller_features.l90d_orders_claimed as seller_l90d_orders_claimed
                    , seller_features.l90d_intercepted_seller_flag as seller_l90d_intercepted_seller_flag
                    , seller_features.l90d_claimed_seller_flag as seller_l90d_claimed_seller_flag

                    -- Consignee email features
                    , email_features.email_daily_order_count as consignee_email_daily_order_count
                    , email_features.address_daily_order_count as consignee_address_daily_order_count
                    , email_features.unique_name_count as consignee_daily_unique_name_count
                    , email_features.l30d_orders_created as consignee_email_l30d_orders_created
                    , email_features.l30d_orders_completed as consignee_email_l30d_orders_completed
                    , email_features.l30d_orders_completion_rate as consignee_email_l30d_orders_completion_rate
                    , email_features.l90d_orders_intercepted as consignee_email_l90d_orders_intercepted
                    , email_features.l90d_orders_claimed as consignee_email_l90d_orders_claimed

                    -- Consingee phone number features
                    , number_features.daily_order_count as consignee_number_daily_order_count
                    , number_features.l30d_orders_created as consignee_number_l30d_orders_created
                    , number_features.l30d_orders_completed as consignee_number_l30d_orders_completed
                    , number_features.l30d_orders_completion_rate as consignee_number_l30d_orders_completion_rate
                    , number_features.l90d_orders_intercepted as consignee_number_l90d_orders_intercepted
                    , number_features.l90d_orders_claimed as consignee_numberl90d_orders_claimed

                    -- Parcel scan features
                    , parcel_features.dws_weight
                    , parcel_features.pct_weight_variance
                    , parcel_features.abs_weight_variance
                    , parcel_features.suspicious_weight_variance_flag
                    , parcel_features.pct_weight_variance_band
                    , parcel_features.abs_weight_variance_band
                    , parcel_features.weight_variance_crossed
                    , parcel_features.dws_scan_cnt
                    , parcel_features.manual_scan_cnt
                    , parcel_features.inbound_image_cnt
                    , parcel_features.dws_scan_flag
                    , parcel_features.manual_scan_flag
                    , parcel_features.inbound_image_flag

                    -- Partition columns
                    , base.system_id
                    , base.created_month
                from lazada_orders_base as base
                left join seller_order_features as seller_features on
                    base.order_id = seller_features.order_id
                left join consignee_phone_number_order_features as number_features on
                    base.order_id = number_features.order_id
                left join consignee_email_order_features as email_features on
                    base.order_id = email_features.order_id
                left join parcel_scan_features as parcel_features on
                    base.order_id = parcel_features.order_id
                where
                    -- Filter for only high value inbounded orders only
                    base.potential_claim_value >= 500000
                    and base.inbound_datetime is not null

                """,
                jinja_arguments={
                    "reputable_email_domain_list": (
                        'support.lazada.com',
                        'gmail.com',
                        'yahoo.com',
                        'yahoo.co.id',
                        'hotmail.com',
                        'icloud.com',
                        'outlook.com',
                    ),
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FEATURES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


@udf(MapType(StringType(), IntegerType()))
def convert_package_content(package_content):
    """
    Inputs example:
    [{"item_description":"A":1}]
    [{"item_description":"B":1},{"item_description":"B","quantity":1}]
    [{"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1}]

    Outputs example:
    {"A": 1}
    {"B": 2}
    {"C": 6}
    """
    package_content = eval(str(package_content))
    if type(package_content) == list:
        result_dictionary = {}
        for dictionary in package_content:
            item = dictionary["item_description"]
            quantity = dictionary["quantity"]
            cumulative_quantity = result_dictionary.get(item)
            if cumulative_quantity is not None:
                result_dictionary[item] = quantity + cumulative_quantity
            else:
                result_dictionary[item] = quantity

        return result_dictionary
    else:
        print("Converted package content is not a list")
        return package_content


@udf(IntegerType())
def count_items(map_column):
    """ Count unique items in the order """
    if map_column:
        return sum(map_column.values())
    return 0


@udf(FloatType())
def avg_description_length(map_column):
    """ Calculate average item description lengh of the order """
    if map_column:
        return float(sum(len(key) for key in map_column.keys())) / len(map_column)
    return 0.0


def get_hub_h3(spark, df):
    """
    Use python h3 package to hex encode orders origin and destination hub location
    """
    cols = ['order_id', 'origin_hub_latitude', 'origin_hub_longitude', 'dest_hub_latitude', 'dest_hub_longitude']
    pandas_df = df.select(cols).toPandas()
    pandas_df['origin_hub_h3_id'] = pandas_df.apply(
        lambda x: h3.geo_to_h3(x['origin_hub_latitude'], x['origin_hub_longitude'], resolution=8), axis=1)
    pandas_df['dest_hub_h3_id'] = pandas_df.apply(
        lambda x: h3.geo_to_h3(x['dest_hub_latitude'], x['dest_hub_longitude'], resolution=8), axis=1)

    schema = StructType([
        StructField("order_id", LongType(), True),
        StructField("origin_hub_latitude", FloatType(), True),
        StructField("origin_hub_longitude", FloatType(), True),
        StructField("dest_hub_latitude", FloatType(), True),
        StructField("dest_hub_longitude", FloatType(), True),
        StructField("origin_hub_h3_id", StringType(), True),
        StructField("dest_hub_h3_id", StringType(), True),
    ])
    spark_df = spark.createDataFrame(pandas_df, schema=schema)

    return spark_df


def run(spark, config):
    base.load_data(spark, config.input)

    # Get base transformed table
    df = base.transform_data(spark, config.transform)

    # Enrich with order items related features
    df_enriched = (
        df
        .withColumn("transformed_items", convert_package_content(col("items")))
        .withColumn("unique_item_count", size(col("transformed_items")))
        .withColumn("items_count", count_items(col("transformed_items")))
        .withColumn("avg_description_length", avg_description_length(col("transformed_items")))
    )

    # Get order origin/dest hub location hex id
    df_h3 = get_hub_h3(spark, df_enriched)

    # Combine all features
    df_final = (
        df_enriched
        .join(df_h3, df_enriched.order_id == df_h3.order_id, 'left')
        .select(df_enriched['*'], df_h3['origin_hub_h3_id'], df_h3['dest_hub_h3_id'])
    )

    base.write_data(df_final, config.output, spark)

    return df_final


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()