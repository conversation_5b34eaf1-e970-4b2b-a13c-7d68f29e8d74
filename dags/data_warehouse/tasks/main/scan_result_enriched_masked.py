import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.SCAN_RESULT_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.SCAN_RESULT_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    lookback_ranges_order_milestones = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.SortMistakeProdGL(input_env, is_masked).SCAN_RESULTS,
                view_name="scan_results",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.SortMistakeProdGL(input_env, is_masked).SORT_NODES,
                view_name="sort_nodes",
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.SortMistakeProdGL(input_env, is_masked).INTRA_HUB_NODES,
                view_name="intra_hub_nodes",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    scan_results.id
                    , scan_results.system_id
                    , scan_results.order_id
                    , scan_results.tracking_id
                    , scan_results.hub_id as hub_id
                    , hub.name as hub_name
                    , hub.facility_type as hub_facility_type
                    , hub.region as hub_region
                    , coalesce(hub.parent_hub_id, hub.id) as coalesce_parent_hub_id
                    , coalesce(hub.parent_hub_name, hub.name) as coalesce_parent_hub_name
                    , scan_results.device_name
                    , scan_results.scan_flow
                    , scan_results.scan_type
                    , scan_results.scan_originator
                    , scan_results.scanned_by_user_id as user_id
                    , trim(
                        concat_ws(' ', scanner.first_name, scanner.last_name)
                    ) as user_name
                    , scan_results.scanned_by_client_id as client_id
                    , scan_results.current_node_id
                    , coalesce(current_node.name, current_intra_node.name, current_migrated_intra_node.name) as current_node_name
                    , scan_results.next_node_id
                    , coalesce(next_node.name, next_intra_node.name, next_migrated_intra_node.name) as next_node_name
                    , scan_results.zone_node_id as destination_node_id
                    , coalesce(zone_node.name, zone_intra_node.name, zone_migrated_intra_node.name) as destination_node_name
                    , scan_results.parcel_destination_zone_id
                    , zones.short_name as parcel_destination_zone_name
                    , zones.type as parcel_destination_zone_type
                    , scan_results.parcel_destination_hub_id
                    , parcel_destination_hub.name as parcel_destination_hub_name
                    , scan_results.parcel_destination_latitude
                    , scan_results.parcel_destination_longitude
                    , scan_results.shipment_destination_hub_id
                    , shipment_destination_hub.name as shipment_destination_hub_name
                    , scan_results.mistake_type
                    , scan_results.mistake_by_user_id as mistake_user_id
                    , trim(
                        concat_ws(' ', error_scanner.first_name, error_scanner.last_name)
                    ) as mistake_user_name
                    , scan_results.mistake_by_client_id as mistake_client_id
                    , scan_results.mistake_details
                    , parent_hub.sort_hub_flag
                    , case 
                        when scan_results.mistake_type is null then 0
                        when lower(scan_results.mistake_type) in ('mistake_type_destination_coordinates_changing','mistake_type_destination_zone_polygon_changing','mistake_type_shipment') then 0 
                        else 1 
                    end as actual_error_flag
                    , from_utc_timestamp(scan_results.created_at, {{ get_local_timezone }}) as scan_datetime
                    , date_format(
                        from_utc_timestamp(scan_results.created_at, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                from scan_results
                left join hubs_enriched as hub
                    on scan_results.hub_id = hub.id
                left join hubs_enriched as parent_hub
                    on coalesce(hub.parent_hub_id, hub.id) = parent_hub.id
                left join hubs_enriched as shipment_destination_hub
                    on scan_results.shipment_destination_hub_id = shipment_destination_hub.id
                left join hubs_enriched as parcel_destination_hub
                    on scan_results.parcel_destination_hub_id = parcel_destination_hub.id
                -- Old logic for getting node details, effective until scan_results.created_month <= '2024-10'.
                -- This will be preserved in case of historical reruns.
                left join sort_nodes as current_node
                    on scan_results.current_node_id = current_node.id
                    and scan_results.extra_info != 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                left join sort_nodes as next_node
                    on scan_results.next_node_id = next_node.id
                    and scan_results.extra_info != 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                left join sort_nodes as zone_node
                    on scan_results.zone_node_id = zone_node.id
                    and scan_results.extra_info != 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                left join intra_hub_nodes as current_intra_node
                    on scan_results.current_node_id = current_intra_node.id
                    and scan_results.extra_info = 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                left join intra_hub_nodes as next_intra_node
                    on scan_results.next_node_id = next_intra_node.id
                    and scan_results.extra_info = 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                left join intra_hub_nodes as zone_intra_node
                    on scan_results.zone_node_id = zone_intra_node.id
                    and scan_results.extra_info = 'INTRA_NODE'
                    and scan_results.created_month <= '2024-10'
                -- From scan_results.created_month >= '2024-11', filter is no longer required as all records are `INTRA_NODE`
                left join intra_hub_nodes as current_migrated_intra_node
                    on scan_results.current_node_id = current_migrated_intra_node.id
                    and scan_results.created_month >= '2024-11'
                left join intra_hub_nodes as next_migrated_intra_node
                    on scan_results.next_node_id = next_migrated_intra_node.id
                    and scan_results.created_month >= '2024-11'
                left join intra_hub_nodes as zone_migrated_intra_node
                    on scan_results.zone_node_id = zone_migrated_intra_node.id
                    and scan_results.created_month >= '2024-11'
                left join zones
                    on scan_results.parcel_destination_zone_id = zones.id
                left join user_info as scanner
                    on scan_results.scanned_by_user_id = scanner.user_id
                left join user_info as error_scanner
                    on scan_results.mistake_by_user_id = error_scanner.user_id
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("scan_results.system_id"),
                },
            ),
            base.TransformView(
                view_name="prev_scan",
                jinja_template="""
                with scans as (
                    select
                        order_id
                        , id
                        , scan_datetime
                        , hub_facility_type
                        , coalesce_parent_hub_id
                        , coalesce_parent_hub_name
                        , scan_flow
                        , scan_type
                        , current_node_id
                        , current_node_name
                        , next_node_id
                        , next_node_name
                    from base
                ),
                prev_scans as(
                    select
                        id
                        , lag(id,1) over (partition by order_id order by scan_datetime) as prev_scan_id
                        , lag(scan_datetime,1) over (partition by order_id order by scan_datetime) as prev_scan_datetime
                        , lag(hub_facility_type,1) over (partition by order_id order by scan_datetime) as prev_hub_facility_type
                        , lag(coalesce_parent_hub_id,1) over (partition by order_id order by scan_datetime) as prev_coalesce_parent_hub_id
                        , lag(coalesce_parent_hub_name,1) over (partition by order_id order by scan_datetime) as prev_coalesce_parent_hub_name
                        , lag(scan_flow,1) over (partition by order_id order by scan_datetime) as prev_scan_flow
                        , lag(scan_type,1) over (partition by order_id order by scan_datetime) as prev_scan_type
                        , lag(current_node_id,1) over (partition by order_id order by scan_datetime) as prev_current_node_id
                        , lag(current_node_name,1) over (partition by order_id order by scan_datetime) as prev_current_node_name
                        , lag(next_node_id,1) over (partition by order_id order by scan_datetime) as prev_next_node_id
                        , lag(next_node_name,1) over (partition by order_id order by scan_datetime) as prev_next_node_name
                    from scans
                ),
                final as(
                    select
                        id
                        , prev_scan_id
                        , prev_scan_datetime
                        , prev_hub_facility_type
                        , prev_coalesce_parent_hub_id
                        , prev_coalesce_parent_hub_name
                        , prev_scan_flow
                        , prev_scan_type
                        , prev_current_node_id
                        , prev_current_node_name
                        , prev_next_node_id
                        , prev_next_node_name
                    from prev_scans
                )
                select * from final
                """,
            ),
            base.TransformView(
                view_name="recovery_exclusions",
                jinja_template="""
                with exclusions as (

                    select
                        order_id
                        , creation_datetime
                        , least(resolution_datetime, cancellation_datetime, now()) as resolution_datetime
                    from pets_tickets_enriched
                    where type = 'MISSING'

                ), 
                scans as (

                    select
                        base.order_id
                        , base.scan_datetime
                        , base.id
                    from base

                ),
                final as  (

                    select
                        scans.order_id
                        , scans.id
                        , max(case when exclusions.order_id is not null then 1 else 0 end) as recovery_exclusion_flag
                    from scans
                    left join exclusions
                        on scans.order_id = exclusions.order_id
                        and scans.scan_datetime >= exclusions.creation_datetime
                        and scans.scan_datetime <= exclusions.resolution_datetime
                    group by {{ range(1, 3) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with final_base as (
                    select
                        base.id
                        , prev_scan.prev_scan_id
                        , base.system_id
                        , base.order_id
                        , base.tracking_id
                        , base.scan_datetime
                        , base.hub_id as hub_id
                        , base.hub_name
                        , base.hub_facility_type
                        , base.hub_region
                        , base.coalesce_parent_hub_id
                        , base.coalesce_parent_hub_name
                        , base.device_name
                        , base.scan_flow
                        , base.scan_type
                        , base.scan_originator
                        , base.user_id
                        , base.user_name
                        , base.client_id
                        , base.current_node_id
                        , base.current_node_name
                        , base.next_node_id
                        , base.next_node_name
                        , base.destination_node_id
                        , base.destination_node_name
                        , base.parcel_destination_zone_id
                        , base.parcel_destination_zone_name
                        , base.parcel_destination_zone_type
                        , base.parcel_destination_hub_id
                        , base.parcel_destination_hub_name
                        , base.parcel_destination_latitude
                        , base.parcel_destination_longitude
                        , base.shipment_destination_hub_id
                        , base.shipment_destination_hub_name
                        , base.mistake_type
                        , base.mistake_user_id
                        , base.mistake_user_name
                        , base.mistake_client_id
                        , base.mistake_details
                        , base.sort_hub_flag
                        , previous_hub.sort_hub_flag as prev_sort_hub_flag
                        , base.actual_error_flag
                        , case
                            when prev_scan.prev_scan_type = 'SCAN_TYPE_GLOBAL_INBOUND' and prev_scan.prev_hub_facility_type not in ('CROSSDOCK', 'STATION_CROSSDOCK') then 1
                            when prev_scan.prev_scan_type = 'SCAN_TYPE_PARCEL_SWEEPER' and base.parcel_destination_zone_type = 'RTS' then 1 
                            else 0
                            end as missort_exclusions_flag
                        , case when base.coalesce_parent_hub_id = prev_scan.prev_coalesce_parent_hub_id then 0 else 1 end as hub_change_flag
                        , prev_scan.prev_scan_datetime
                        , prev_scan.prev_hub_facility_type
                        , prev_scan.prev_coalesce_parent_hub_id
                        , prev_scan.prev_coalesce_parent_hub_name
                        , prev_scan.prev_scan_flow
                        , prev_scan.prev_scan_type
                        , prev_scan.prev_current_node_id
                        , prev_scan.prev_current_node_name
                        , prev_scan.prev_next_node_id
                        , prev_scan.prev_next_node_name
                        , base.created_month
                        , recovery_exclusions.recovery_exclusion_flag
                        from base
                        left join prev_scan
                            on base.id = prev_scan.id
                        left join hubs_enriched as previous_hub
                            on prev_scan.prev_coalesce_parent_hub_id = previous_hub.id
                        left join recovery_exclusions
                            on base.id = recovery_exclusions.id
                ),
                pre_final as (
                    select
                        final_base.*
                        , case when shippers_enriched.sales_channel != 'Test' then 1 else 0 end as actual_order_flag
                    from final_base
                    left join orders_enriched 
                        on final_base.order_id = orders_enriched.order_id
                    left join shippers_enriched
                        on orders_enriched.shipper_id = shippers_enriched.id
                ),
                final as (
                    select
                        *
                        , case 
                            when prev_sort_hub_flag = 1 
                            and actual_error_flag = 1 
                            and hub_change_flag = 1
                            and missort_exclusions_flag = 0
                            and recovery_exclusion_flag = 0
                            and actual_order_flag = 1
                            and prev_scan_datetime is not null 
                            then 1 
                            else 0 
                            end as kpi_missort_flag
                    from pre_final
                )
                select * from final
                where created_month >= '2023-07'
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SCAN_RESULT_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()