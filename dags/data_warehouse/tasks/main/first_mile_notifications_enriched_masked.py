import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.FIRST_MILE_NOTIFICATIONS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.FIRST_MILE_NOTIFICATIONS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.SnsPlatformsProdGL(input_env, is_masked).MESSAGE_LOGS,
                view_name="message_logs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- TemplateCode obtained from product team. 
                -- If product stop using the existing templatecode, need to get new code from them else no output

                select
                    id
                    , cast(get_json_object(request_body, '$.TemplateParams.PickupJobId') as long) AS pickup_job_id
                    , cast(get_json_object(request_body, '$.TemplateParams.PickupJobType') as string) AS pickup_job_type
                    , failure_reason
                    , from_utc_timestamp(seen_at, '{{ local_timezone }}') as seen_datetime
                    , from_utc_timestamp(delivered_at, '{{ local_timezone }}') as delivered_datetime
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') as creation_datetime
                    , created_month
                    , system_id
                from message_logs
                where 1=1
                    and platform = 'WHATSAPP'
                    and route = 'WHATSAPP'
                    and get_json_object(request_body, '$.TemplateCode') = '897667370392662016'
                    and system_id =  '{{ system_id }}'
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                }
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIRST_MILE_NOTIFICATIONS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
