import random
import re
import statistics
import sys
import time
from collections import Counter
from itertools import takewhile

import bs4
import requests
from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from data_warehouse.utils import logger
from metadata import constants, data_warehouse, versioned_parquet_tables_masked
from pyspark.sql.types import *

logger = logger.get_logger(__file__)
EXCHANGE_RATE_USD_TO_IDR = 15182
GOOGLE_SEARCH_SITE = "https://google.co.id/search?q="
FIRST_VALUE_FAR_OFF_COEFFICIENT = 0.3
FAR_OFF_COEFFICIENT = 0.3
AVG_FAR_OFF_COEFFICIENT = 0.2
MEDIAN_FAR_OFF_COEFFICIENT = 0.2
FIRST_VALUE_SUM_COEFFICIENT = 0.5
MEDIAN_SUM_COEFFICIENT = 0.5
FAR_OFF_THRESHOLD = 0.2
MEDIAN_FAR_OFF_THRESHOLD = 0.1
AVG_FAR_OFF_THRESHOLD = 0.1
PRICE_NA_FLAG = -1.0
FIRST_VALUE_FAR_OFF_THRESHOLD = 0
SUS_SCORE_THRESHOLD = 0.9

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachPriceMatchingDAG.Task.HIGH_COD_PRICE_SCRAPPING_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachPriceMatchingDAG.Task.HIGH_COD_PRICE_SCRAPPING_REPORT_MASKED,
    depends_on=(data_warehouse.SLABreachPriceMatchingDAG.Task.COD_ORDERS_TO_EXAMINE_MASKED,),
    system_ids=(constants.SystemID.ID,),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime):
    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=(
                    versioned_parquet_tables_masked.DataWarehouse(env).COD_ORDERS_TO_EXAMINE + measurement_datetime_partition
                ),
                view_name="cod_orders_to_examine",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , shipper_id
                    , created_at
                    , updated_at
                    , cod_id
                    , granular_status
                    , cod_value
                    , cast(package_content as STRING) as package_content
                    , '{{ system_id }}' as system_id
                    , created_month
                from cod_orders_to_examine
                where created_at >= from_utc_timestamp('{{ last_measurement_datetime }}', '{{ local_timezone }}') - interval '1' hour
                    and created_at < from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}') - interval '1' hour
                """,
                jinja_arguments={
                    "last_measurement_datetime": last_measurement_datetime,
                    "measurement_datetime": measurement_datetime,
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HIGH_COD_PRICE_SCRAPPING_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        write_mode="merge",
        primary_keys=["order_id",],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    logger.info('Level 0. start loading data')
    base.load_data(spark, config.input)
    logger.info('Level 0. finished loading data. start transforming data')
    df = base.transform_data(spark, config.transform).toPandas()
    logger.info('Level 0. finished transforming data. start processing data')
    results = process_dataframe(df)
    logger.info('Level 0. finished processing data. start writing data')
    # schema specified to prevent the following Error:
    # ValueError: Some of types cannot be determined after inferring
    schema = StructType([
        StructField("order_id", LongType(), True),
        StructField("shipper_id", IntegerType(), True),
        StructField("created_at", TimestampType(), True),
        StructField("updated_at", TimestampType(), True),
        StructField("cod_id", LongType(), True),
        StructField("granular_status", StringType(), True),
        StructField("cod_value", DecimalType(38, 18), True),
        StructField("system_id", StringType(), True),
        StructField("created_month", StringType(), True),
        StructField("list_of_item_description", ArrayType(StringType()), True),
        StructField("list_of_quantities", ArrayType(LongType()), True),
        StructField("list_of_price_lists", ArrayType(ArrayType(LongType())), True),
        StructField("common_prices_found_with_google_search", ArrayType(ArrayType(LongType())), True),
        StructField("suspicious_score", DoubleType(), True),
        StructField("price_match_results", StringType(), True),
        StructField("suspicious_flag", IntegerType(), True)

    ])
    spark_df = spark.createDataFrame(results, schema=schema)
    base.write_data(spark_df, config.output, spark)
    logger.info('Level 0. finished writing data')
    return spark_df


def process_dataframe(df):
    """
    Input:
        pandas dataframe that contains columns named "package_content" and "cod_value", and "order_id"
    Output:
        pandas dataframe with additional columns named
            "column_list_of_item_description"
            "column_list_of_quantities"
            "column_list_of_price_lists"
    """
    logger.info("Level 1. process_dataframe started")
    package_content = df["package_content"]
    item_maps = [convert_package_content(item) for item in package_content]
    cod_values = df['cod_value']
    logger.info("Level 1. package content converted")
    # placeholder for the additional columns
    column_list_of_item_description = []
    column_list_of_quantities = []
    column_list_of_price_lists = []
    for order_map, cod_value in zip(item_maps, cod_values):
        keys = []
        quantities = []
        price_lists = []
        if order_map:
            total_quantities = sum(order_map.values())
            unit_cod = 0 if total_quantities == 0 else float(cod_value) / float(total_quantities)
            for key in order_map:
                prices = []  # placeholder for prices found
                keys.append(key)  # item_description
                quantities.append(order_map[key])  # respective quantity

                # additional business logic
                # if the unit_cod is less than 500,000, there is no need to search from Google
                if unit_cod < 500000:
                    price_lists.append(prices)
                    continue
                # each key is an item_description
                # not to do scrapping too fast in order to avoid captcha prompts
                time.sleep(4 + 4 * random.random())
                try:
                    # calling another function defined in this script
                    prices = get_prices_from_google(key)
                    if len(prices) == 0:
                        time.sleep(4 + 4 * random.random())
                except TypeError:
                    time.sleep(4 + 4 * random.random())
                price_lists.append(prices)
        column_list_of_item_description.append(keys)
        column_list_of_quantities.append(quantities)
        column_list_of_price_lists.append(price_lists)

    logger.info("Level 1. price scraping finished")

    # add to additional columns
    logger.info('Level 1. creating item description list')
    df["list_of_item_description"] = column_list_of_item_description
    logger.info('Level 1. creating quantity list')
    df["list_of_quantities"] = column_list_of_quantities
    logger.info('Level 1. creating price list')
    df["list_of_price_lists"] = column_list_of_price_lists
    logger.info('Level 1. creating common prices list')
    df["common_prices_found_with_google_search"] = df.list_of_price_lists.apply(
        lambda x: find_common_prices_all_items(x)
    )
    logger.info('Level 1. computing sus score')
    df["suspicious_score"] = df.apply(conditional_func, axis=1, result_type='reduce')
    logger.info("Level 1. additional columns added")

    # the column package_content is redundant and causing problems when querying
    df = df.drop('package_content', axis=1)
    df = add_display_columns(df)
    df = df.drop_duplicates(subset=['order_id'])
    return df


def add_display_columns(df):
    """
    Consolidate the results, using 4 original columns to produce 2 new ones:

        list_of_item_description
        list_of_quantities          to          price_match_results
        list_of_price_lists

        suspicious_score            to          suspicious_flag
    """
    display_col = []
    suspicious_flags = []
    for index, row in df.iterrows():
        single_order = ''
        for i in range(len(row['list_of_quantities'])):
            # Format results for one item (there could be multiple items per order)
            single_item = f'Item No.{i + 1}: ' + row['list_of_item_description'][i] + '\n' + \
                          f'Quantity No.{i + 1}: ' + str(row['list_of_quantities'][i]) + '\n' + \
                          f'Possible Prices No.{i + 1}: ' + str(row['list_of_price_lists'][i]) + '\n' + '\n'
            # Combine results for all items of an order into 1
            single_order = single_order + single_item
        display_col.append(single_order)
        suspicious_flags.append(1 if row['suspicious_score'] >= SUS_SCORE_THRESHOLD else 0)
    df['price_match_results'] = display_col
    df['suspicious_flag'] = suspicious_flags
    return df

def get_prices_from_google(item_desc):
    """
    Get the text by feeding item_desc to Google indo,
    then parse the scraped text for prices

    Input:
            item_desc (str): search term to feed google.co.id
    Output:
            list of int prices
    """
    # Using beautiful soup to get prices from Google Indonesia
    try:
        url = GOOGLE_SEARCH_SITE + item_desc + " price"
        request_result = requests.get(url)
        soup = bs4.BeautifulSoup(request_result.text, "html.parser")
        web_text = soup.get_text()
        # calling another function defined in this script
        return get_prices_from_text(web_text)
    except ConnectionError:
        return []


def get_prices_from_text(web_text):
    """
    Get the text by feeding item_desc to Google indo,
    then parse the scraped text for prices
    The most atomic function in the script (no calling of other functions)

    Input:
            web_text (str): search term to feed google.co.id
    Output:
            list of int prices
    """
    # Price found on Google often starts with either IDR or Rp
    idr_pattern = r"(?<=IDR).[\d]+,[\d]+[\,]?[\d]+|Rp\d{1,3}(?:\.\d{3})*(?:,\d+)?"
    # Match prices with prefixes like IDR or Rp
    output = re.findall(idr_pattern, web_text)
    # avoid situations when tuples(of strings) are returned instead of strings
    output = ["".join(x) for x in output]
    # remove the spaces and the currency signs
    output = [price.replace("\xa0", "").replace("Rp", "") for price in output]
    # remove the separators
    output = [int(price.replace(",", "").replace(".", "")) for price in output if len(price) > 4]

    # if no outputs from above, try matching result in USD
    if len(output) == 0:
        usd_pattern = r"US\$(\d{1,3}(,\d{3})*|\d+)"
        output = re.findall(usd_pattern, web_text)

        # avoid situations when tuples(of strings) are returned instead of strings
        output = ["".join(x) for x in output]
        # remove the currency prefixes
        output = [price.replace("US$", "") for price in output]
        # remove the separators
        output = [int(price.replace(",", "")) for price in output]
        # convert the prices into IDR
        output = [price * EXCHANGE_RATE_USD_TO_IDR for price in output]
    return output


def convert_package_content(package_content):
    """
    Inputs are like:
    [{"item_description":"A":1}]
    [{"item_description":"B":1},{"item_description":"B","quantity":1}]
    [{"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1},
    {"item_description":"C","quantity":1}]

    Outputs to be like:
    {"A": 1}
    {"B": 2}
    {"C": 6}
    """
    package_content = eval(str(package_content))
    if type(package_content) == list:
        result_dictionary = {}
        for dictionary in package_content:
            item = dictionary["item_description"]
            quantity = dictionary["quantity"]
            cumulative_quantity = result_dictionary.get(item)
            if cumulative_quantity is not None:
                result_dictionary[item] = quantity + cumulative_quantity
            else:
                result_dictionary[item] = quantity

        return result_dictionary
    else:
        logger.info("level 3: converted package content is not a list")
        return package_content


def find_common_prices_all_items(list_of_price_lists):
    """
    For example, the list_of_price_lists may look like this
    l = [[1500000, 1200000, 1200000, 1200000, 1200000, 90000]]
    Roughly speaking, this function reduces the non-frequent prices*
    and l" would become
    l = [[1500000, 1200000, 1200000, 1200000, 1200000]]
    """
    return [find_common_prices(price_list) for price_list in list_of_price_lists]


def find_common_prices(price_list):
    """
    Purpose of this function is to find common prices among a list of prices scraped from website
    Ideally, it just collects the mode. In case of multiple modes, return all of them (if the counts are greater than 1)

    Realistically, the prices can be quite close
    Hence the strategy is to first make a copy of rounded values to the nearest 100,000
    Then we continue to find the mode(s) among the rounded values
    Then we take values from the original lists (before rounding) that was close to the rounded modes
    """
    if len(price_list) != 0:
        rounded_prices = [round(p, -5) for p in price_list]
        common = get_common(rounded_prices)
        result = [p for p in price_list if round(p, -5) in common]
        result = result if len(result) > 0 else price_list
        return price_list[:1] + result
    return []


def get_common(list_of_values):
    common = []
    counter = Counter(list_of_values)
    if counter:
        most_common = counter.most_common()
        max_count = most_common[0][1]
        if max_count >= 2:
            common = [t[0] for t in takewhile(lambda x: x[1] >= 2, most_common)]
    return common


# based on how many item_desc, use corresponding sus_score
def conditional_func(row):
    if len(row["list_of_quantities"]) > 1:
        return get_multiple_items_sus_score(
            row["common_prices_found_with_google_search"], row["cod_value"], row["list_of_quantities"]
        )
    elif len(row["list_of_quantities"]) == 1:
        return get_single_item_sus_score(
            row["common_prices_found_with_google_search"][0], row["cod_value"], row["list_of_quantities"][0]
        )
    else:
        return PRICE_NA_FLAG


# cod value is at least 500,000
# list_of_price_list are lists of list of positive integers
# list_of_quantity are list positive integers
def get_multiple_items_sus_score(list_of_price_list, cod_value, list_of_quantity):
    for price_list in list_of_price_list:
        if len(price_list) == 0:
            return PRICE_NA_FLAG
    first_values, medians = [], []
    for i in range(len(list_of_price_list)):
        first_values.append(list_of_price_list[i][0] * list_of_quantity[i])
        medians.append(statistics.median(list_of_price_list[i]) * list_of_quantity[i])

    first_value_sum, median_sum = sum(first_values), sum(medians)
    return FIRST_VALUE_SUM_COEFFICIENT * my_func(
        percentage_difference(low=first_value_sum, high=cod_value, threshold=0)
    ) + MEDIAN_SUM_COEFFICIENT * my_func(percentage_difference(low=median_sum, high=cod_value, threshold=0.15))


# cod value is at least 500,000
# price_list are list of positive integers
# quantity are positive integers
def get_single_item_sus_score(price_list, cod_value, quantity):
    # case when price_list is empty
    if len(price_list) == 0:
        return PRICE_NA_FLAG
    # corrected price list
    price_list = [p * quantity for p in price_list]
    # final computations
    # VERY LENIENT: if cod is less than first value, the sus_score is 0
    first_price = price_list[0]
    if cod_value <= first_price:
        return 0.0
    # 4 components included as of now
    return (
        far_off_from_first_value_score(cod_value, first_price)
        + far_off_from_other_prices_score(cod_value, price_list)
        + far_off_from_price_avg_score(cod_value, price_list) * confidence_coefficient(price_list)
        + far_off_from_price_median_score(cod_value, price_list)
    )


# component of get_single_item_sus_score
def far_off_from_other_prices_score(cod_value, price_list):
    count = 0
    for p in price_list:
        if percentage_difference(low=p, high=cod_value, threshold=AVG_FAR_OFF_THRESHOLD) > 0:
            count = count + 1
    return FAR_OFF_COEFFICIENT * count / len(price_list)


# component of get_single_item_sus_score
def far_off_from_price_avg_score(cod_value, price_list):
    avg = statistics.mean(price_list)
    pct_df = percentage_difference(low=avg, high=cod_value, threshold=AVG_FAR_OFF_THRESHOLD)
    return AVG_FAR_OFF_COEFFICIENT * my_func(pct_df)


# component of get_single_item_sus_score
def far_off_from_price_median_score(cod_value, price_list):
    median = statistics.median(price_list)
    pct_df = percentage_difference(low=median, high=cod_value, threshold=MEDIAN_FAR_OFF_THRESHOLD)
    return MEDIAN_FAR_OFF_COEFFICIENT * my_func(pct_df)


# auxiliary function
def percentage_difference(low=0, high=1, threshold: float = 0):
    # to prevent float division by 0
    if abs(high) < 1e-6:
        logger.info('Level 2: percentage difference denominator is 0')
        return 0
    high, low = float(high), float(low)
    val = (high - low) / high
    if val < threshold:
        return 0
    return val


# pct is from [0,1)
# output is [0, 1]
# increasing at a decreasing speed
def my_func(pct, k=0.21242):
    return float(pct) ** float(k)


# component of get_single_item_sus_score
def far_off_from_first_value_score(cod_value, first_value):
    pct_df = percentage_difference(low=first_value, high=cod_value)
    return FIRST_VALUE_FAR_OFF_COEFFICIENT * my_func(pct_df)


# computes the confidence applied to prices_far_off_avg_score
def confidence_coefficient(price_list):
    min_p, max_p = min(price_list), max(price_list)
    return 1 - 0.5 * percentage_difference(low=min_p, high=max_p)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()