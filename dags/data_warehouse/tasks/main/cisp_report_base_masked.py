import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_REPORT_BASE_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_REPORT_BASE_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_REPORT,
                view_name="last_mile_push_off_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="pets_ticket_cte",
                jinja_template="""
                select
                    order_id
                    , id
                    , type
                    , sub_type
                    , creation_datetime
                    , resolution_datetime
                from pets_tickets_enriched
                where 
                    (lower(sub_type) not IN ('completed order','cancelled order','max attempts (delivery)','robo chat','robo call','max attempts (rts)') 
                    and lower(type) not in ('missing','sla breach')
                    and lower(outcome) != 'rts')
                """,
            ),
            base.TransformView(
                view_name="ats_cte",
                jinja_template="""
                select
                    order_milestones.order_id
                    , min(add_to_shipment_datetime) earliest_add_to_shipment_datetime_after_rts
                from order_milestones
                left join shipment_orders_enriched
                    on shipment_orders_enriched.order_id = order_milestones.order_id
                    and shipment_orders_enriched.orig_hub_id = order_milestones.dest_hub_id
                    and shipment_orders_enriched.add_to_shipment_datetime > order_milestones.rts_trigger_datetime
                group by 1
                """,
            ),
            base.TransformView(
                view_name="pets",
                jinja_template="""
                with base as (
                    select
                        last_mile.order_id
                        , last_mile.dest_hub_id
                        , last_mile.dest_hub_name
                        , last_mile.dest_hub_region
                        , last_mile.dest_zone
                        , last_mile.dest_hub_datetime
                        , last_mile.start_clock_date as last_mile_start_clock_date
                        , max(pets.resolution_datetime) latest_lm_pets_resolution_datetime
                        , last_mile.created_month
                    from last_mile_push_off_report as last_mile
                    left join ats_cte
                        on last_mile.order_id = ats_cte.order_id
                    left join pets_ticket_cte as pets on
                        last_mile.order_id = pets.order_id
                        and (pets.creation_datetime < ats_cte.earliest_add_to_shipment_datetime_after_rts
                            or ats_cte.earliest_add_to_shipment_datetime_after_rts is null)
                    group by 1,2,3,4,5,6,7,9
                )

                select
                    base.order_id
                    , base.dest_hub_id
                    , base.dest_hub_name
                    , base.dest_hub_region
                    , base.dest_zone
                    , base.dest_hub_datetime
                    , base.last_mile_start_clock_date
                    , date(greatest(coalesce(base.latest_lm_pets_resolution_datetime,base.last_mile_start_clock_date), base.last_mile_start_clock_date)) as start_clock_date
                    , base.created_month
                from base
                """,
            ),
            base.TransformView(
                view_name="cutoff",
                jinja_template="""
                select
                    pets.order_id
                    , pets.dest_hub_id
                    , pets.dest_hub_name
                    , pets.dest_hub_region
                    , pets.dest_zone
                    , hubs.facility_type as dest_hub_facility_type
                    , pets.dest_hub_datetime
                    , pets.last_mile_start_clock_date
                    , pets.start_clock_date
                    , coalesce(region.working_day, nation.working_day) as working_day
                    , coalesce(region.next_working_day_1, nation.next_working_day_1) as next_working_date
                    , if(
                        coalesce(region.working_day, nation.working_day) = 0,
                        coalesce(region.next_working_day_1, nation.next_working_day_1),
                        pets.start_clock_date
                    ) as n0_cutoff_date

                    {%- for day in range(1, 10) %}

                    , if(
                        coalesce(region.working_day, nation.working_day) = 0,
                        coalesce(region.next_working_day_{{ day+1 }}, nation.next_working_day_{{ day+1 }}),
                        coalesce(region.next_working_day_{{ day }}, nation.next_working_day_{{ day }})
                    ) as n{{ day }}_cutoff_date

                    {%- endfor %}

                    , pets.created_month
                from pets
                left join hubs_enriched as hubs on
                    pets.dest_hub_id = hubs.id
                left join calendar as region on
                    hubs.address_city = region.region
                    and pets.start_clock_date = region.next_working_day_0
                left join calendar as nation on
                    nation.region = 'national'
                    and pets.start_clock_date  = nation.next_working_day_0
                """,
            ),
            base.TransformView(
                view_name="order",
                jinja_template="""
                select
                    cutoff.order_id
                    , cutoff.dest_hub_id
                    , cutoff.dest_hub_name
                    , cutoff.dest_hub_region
                    , cutoff.dest_zone
                    , cutoff.dest_hub_facility_type
                    , cutoff.dest_hub_datetime
                    , cutoff.last_mile_start_clock_date
                    , cutoff.start_clock_date
                    , cutoff.working_day
                    , cutoff.next_working_date

                    {%- for day in range(10) %}

                    , cutoff.n{{ day }}_cutoff_date

                    {%- endfor %}

                    , order.shipper_id
                    , order.last_valid_delivery_attempt_datetime
                    , order.last_valid_delivery_attempt_status
                    , order.rts_trigger_datetime
                    , order.delivery_success_datetime
                    , order.delivery_attempts
                    , order.cod_id
                    , order.rts_flag
                    , cutoff.created_month
                from cutoff
                left join order_milestones as order on
                    cutoff.order_id = order.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_REPORT_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()