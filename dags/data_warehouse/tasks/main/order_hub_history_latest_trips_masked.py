import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_LATEST_TRIPS_MASKED + ".py",
    task_name=data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_LATEST_TRIPS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
    ),
    depends_on=(data_warehouse.OpsWeeklyDAG.Task.ORDER_HUB_HISTORY_INTERMEDIATE_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_INTERMEDIATE,
                view_name="order_hub_history_intermediate",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="all_trips",
                jinja_template="""
                with trip_base as (
                 select 
                    trip_id
                    , coalesce(expected_arrival_datetime,actual_arrival_datetime) as trip_arrival_datetime
                    , coalesce(expected_start_datetime,actual_start_datetime) as trip_start_datetime
                    , expected_duration_min as expected_duration_min_trip_id
                    /*
                    , concat(coalesce(expected_arrival_datetime,actual_arrival_datetime), 
                        '-', coalesce(expected_start_datetime,actual_start_datetime)) as trip_reference_concat
                    */
                    , origin_hub_id
                    , dest_hub_id
                from movement_trips_enriched
                where lower(status) = 'completed'
                )

                /* Dedupe those trips that have the same arrival */
                select

                     origin_hub_id
                    , dest_hub_id
                    , trip_arrival_datetime
                    , max_by(trip_id, trip_start_datetime) as trip_id
                    , max(trip_start_datetime) as trip_start_datetime
                    , max_by(expected_duration_min_trip_id, trip_start_datetime) as expected_duration_min_trip_id
                from trip_base
                group by {{ range(1, 4) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="sequence_cutoff",
                jinja_template="""
                with intermediate_sequence_base_1 as (
                    select order_id
                        , hub_id
                        , hub_seq
                        , next_hub_id
                        , final_destination
                        , last_mile_hub_arrival_cutoff
                        , Null as trip_id
                        , Null as trip_arrival_datetime
                        , Null as trip_start_datetime
                        , Null as expected_duration_min_trip_id
                        , 0 as missing_trip_departure_cutoff
                        , (order_hub_history_intermediate.last_mile_hub_arrival_cutoff) as cutoff
                        , system_id
                        , created_month
                    from order_hub_history_intermediate
                    where hub_seq = 11 and final_destination = 1
                ),

                {% for i in range(2,12) %}
                intermediate_sequence_base_{{i}} as (
                    select
                        order_id
                        , hub_id
                        , hub_seq
                        , next_hub_id
                        , final_destination
                        , last_mile_hub_arrival_cutoff
                        , trip_id
                        , trip_arrival_datetime
                        , trip_start_datetime
                        , expected_duration_min_trip_id
                        , if(final_destination != 1 and trip_id is null and cutoff is not null, 1,0) missing_trip_departure_cutoff
                        , case 
                            when final_destination = 1
                            then last_mile_hub_arrival_cutoff 
                            else (trip_start_datetime) 
                            end as cutoff
                        , system_id
                        , created_month
                    from (
                    select 
                        order_hub_history_intermediate.order_id
                        , order_hub_history_intermediate.hub_id
                        , order_hub_history_intermediate.hub_seq
                        , order_hub_history_intermediate.next_hub_id
                        , order_hub_history_intermediate.final_destination
                        , order_hub_history_intermediate.last_mile_hub_arrival_cutoff
                        , intermediate_sequence_base_{{i-1}}.cutoff
                        , order_hub_history_intermediate.system_id
                        , order_hub_history_intermediate.created_month
                        , max_by(all_trips.trip_id, all_trips.trip_arrival_datetime) as trip_id
                        , max_by(all_trips.expected_duration_min_trip_id, all_trips.trip_arrival_datetime) as expected_duration_min_trip_id
                        , max_by(all_trips.trip_start_datetime, all_trips.trip_arrival_datetime) as trip_start_datetime
                        , max(all_trips.trip_arrival_datetime) as trip_arrival_datetime
                    from order_hub_history_intermediate
                    left join intermediate_sequence_base_{{i-1}}
                        on order_hub_history_intermediate.order_id = intermediate_sequence_base_{{i-1}}.order_id
                    left join all_trips
                        on origin_hub_id = order_hub_history_intermediate.hub_id
                        and dest_hub_id = order_hub_history_intermediate.next_hub_id
                        and (all_trips.trip_arrival_datetime) <=  cutoff
                        and (all_trips.trip_arrival_datetime) <= order_hub_history_intermediate.last_mile_hub_arrival_cutoff
                    where order_hub_history_intermediate.hub_seq = 12 - {{i}}
                    group by {{ range(1, 10) | join(',') }}
                    )
                ),
                {% endfor %}

                union_cte as (
                {%- for i in range(1,12) %}
                    select *
                    from intermediate_sequence_base_{{i}}
                {% if not loop.last -%} UNION ALL {%- endif %}
                {% endfor %}
                )

                select 
                    order_id
                    , hub_id
                    , hub_seq
                    , next_hub_id
                    , trip_id
                    , trip_start_datetime
                    , cutoff
                    , expected_duration_min_trip_id
                    , missing_trip_departure_cutoff
                    , system_id
                    , created_month
                from union_cte
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_HISTORY_LATEST_TRIPS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()