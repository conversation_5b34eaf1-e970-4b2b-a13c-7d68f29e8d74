import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.SHIPMENT_HUB_MILESTONES_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_HUB_MILESTONES,
                view_name="shipment_hub_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).TRIP_COST_TIERS,
                view_name="trip_cost_tiers",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATIONS,
                view_name="hub_relations",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIPS,
                view_name="movement_trips",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_ORDERS,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_MAWB,
                view_name="flight_mawb",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipment_completion_cte",
                jinja_template="""
                with shipment_hub_milestones as (
                    select
                        shipment_id
                        , min(hub_inbound_datetime) filter (
                            where destination_hub_flag = 1
                        ) as dest_shipment_hub_inbound_datetime
                    from shipment_hub_milestones
                    group by 1
                ),

                shipment_milestones as (
                    select
                        shipment_id
                        , min(from_utc_timestamp(created_at, {{ get_local_timezone }})) filter (
                            where event = 'SHIPMENT_FORCE_COMPLETED'
                        ) as shipment_force_success_datetime
                    from shipment_events
                    group by 1
                ),

                final as (
                    select
                        shipments.id as shipment_id
                        , least(shipment_hub_milestones.dest_shipment_hub_inbound_datetime,
                            shipment_milestones.shipment_force_success_datetime
                        ) as shipment_completion_datetime
                    from shipments
                    left join shipment_hub_milestones
                        on shipments.id = shipment_hub_milestones.shipment_id
                    left join shipment_milestones
                        on shipments.id = shipment_milestones.shipment_id
                )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("shipment_events.hub_system_id").lower()},
            ),
            base.TransformView(
                view_name="scans_base",
                jinja_template="""
                with scanned_base as (

                    select 
                        coalesce(get_json_object(ext_data, '$.trip_data.trip_id'), get_json_object(ext_data, '$.possible_trip')) as trip_id
                        , shipment_id
                        , created_at
                        , event as scan_type
                        , 'scanned' as type 
                    from shipment_events 
                    where event in ('SHIPMENT_VAN_INBOUND','SHIPMENT_HUB_INBOUND')
                        and deleted_at is null
                ), 
                scanned as (
                
                    select
                        *
                    from scanned_base
                    where trip_id is not null

                ),
                unscanned as (

                    select 
                        trip_id
                        , shipment_id
                        , created_at
                        , scan_type
                        , 'unscanned' as type 
                    from trip_unscanned_shipments 
                    where type = 'STAYOVER' 
                        and deleted_at is null

                ),
                joined as (

                    select * from scanned
                    union
                    select * from unscanned

                ),
                pre_final as (

                    select
                        lower(hub_relations.origin_hub_system_id) as system_id
                        , joined.trip_id
                        , joined.shipment_id
                        , joined.created_at
                        , joined.scan_type
                        , joined.type
                        , shipments.status as shipment_status
                        , shipment_completion_cte.shipment_completion_datetime
                        , shipments.dest_hub_id as shipment_destination_hub_id
                        , hubs_enriched.short_name as shipment_destination_hub_short_name
                        , hub_relations.origin_hub_id as trip_origin_hub_id
                        , hub_relations.destination_hub_id as trip_destination_hub_id
                        , movement_trips.completion_time
                        , movement_trips.actual_end_time
                    from joined
                    left join movement_trips
                        on joined.trip_id = movement_trips.id
                    left join hub_relations
                        on movement_trips.hub_relation_id = hub_relations.id
                    left join shipments
                        on joined.shipment_id = shipments.id
                    left join hubs_enriched
                        on shipments.dest_hub_id = hubs_enriched.id
                        and hub_relations.origin_hub_system_id = hubs_enriched.system_id
                    left join shipment_completion_cte
                        on joined.shipment_id = shipment_completion_cte.shipment_id

                ),
                convert_timezone_cte as (

                    select
                        system_id
                        , trip_id
                        , shipment_id
                        , from_utc_timestamp(created_at, {{ get_local_timezone }}) as created_at
                        , from_utc_timestamp(actual_end_time, {{ get_local_timezone }}) as trip_actual_arrival_time
                        , from_utc_timestamp(completion_time, {{ get_local_timezone }}) as trip_completion_time
                        , scan_type
                        , type
                        , shipment_status
                        , shipment_completion_datetime
                        , shipment_destination_hub_id
                        , shipment_destination_hub_short_name
                        , trip_origin_hub_id
                        , trip_destination_hub_id
                    from pre_final

                ),
                cutoff_time as (

                    select
                        * 
                        , coalesce(trip_actual_arrival_time, trip_completion_time, created_at) as cutoff_time
                    from convert_timezone_cte

                ),
                final as (
                
                    select distinct
                        system_id
                        , trip_id
                        , shipment_id
                        , scan_type
                        , type
                        , shipment_destination_hub_id
                        , shipment_destination_hub_short_name
                        , trip_origin_hub_id
                        , trip_destination_hub_id
                        , cutoff_time
                    from cutoff_time
                        
                )
                select * from final

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="standard_trip_shipments",
                jinja_template="""
                with restock_shipment_trips as (
                
                    select distinct
                        trip_id
                        , shipment_id
                    from scans_base
                    where lower(scans_base.shipment_destination_hub_short_name) like '%restock%'
                    
                ),
                non_restock_shipment_trips as (
                
                    select
                        *
                    from scans_base
                    where lower(scans_base.shipment_destination_hub_short_name) not like '%restock%'
                
                ),
                last_real_scan as(
                
                    select
                        shipment_id
                        , max_by(trip_id, cutoff_time) as last_real_scan_trip_id
                        , max_by(scan_type, cutoff_time) as last_real_scan_scan_type
                        , max(cutoff_time) as last_real_scan_cutoff_time
                    from non_restock_shipment_trips
                    where type = 'scanned'
                    group by 1
                
                ),
                hub_inbound_shipment_trips as (
                
                    select distinct
                        non_restock_shipment_trips.trip_id
                        , non_restock_shipment_trips.shipment_id
                    from non_restock_shipment_trips
                    left join last_real_scan
                        on non_restock_shipment_trips.shipment_id = last_real_scan.shipment_id
                    where last_real_scan.last_real_scan_scan_type = 'SHIPMENT_HUB_INBOUND'
                        and non_restock_shipment_trips.cutoff_time <= last_real_scan.last_real_scan_cutoff_time

                ),
                van_inbound_shipment_trips_base as (
                
                    select
                        non_restock_shipment_trips.*
                        , last_real_scan.last_real_scan_cutoff_time
                    from non_restock_shipment_trips
                    left join last_real_scan
                        on non_restock_shipment_trips.shipment_id = last_real_scan.shipment_id
                    where last_real_scan.last_real_scan_scan_type = 'SHIPMENT_VAN_INBOUND'
                
                ),
                recursive_filter_base as (
                
                    select
                        *
                    from van_inbound_shipment_trips_base
                    where cutoff_time > last_real_scan_cutoff_time

                ),
                recursive_filter as (
                
                    select
                        recursive_filter_base.shipment_id
                        , min(recursive_filter_base.cutoff_time) as cutoff_time
                    from recursive_filter_base
                    join recursive_filter_base as prev_scans
                        on recursive_filter_base.shipment_id = prev_scans.shipment_id
                        and recursive_filter_base.trip_destination_hub_id = prev_scans.trip_origin_hub_id
                    group by 1
                    order by 1

                ),
                recursive_filter_applied as (
                
                    select
                        van_inbound_shipment_trips_base.*
                    from van_inbound_shipment_trips_base
                    left join recursive_filter
                        on van_inbound_shipment_trips_base.shipment_id = recursive_filter.shipment_id
                    where recursive_filter.shipment_id is null
                        or van_inbound_shipment_trips_base.cutoff_time < recursive_filter.cutoff_time
                ),
                van_inbound_shipment_trips as (
                
                    select distinct
                        trip_id
                        , shipment_id
                    from recursive_filter_applied

                ),
                final as (
                
                    select trip_id, shipment_id from restock_shipment_trips
                    UNION ALL
                    select trip_id, shipment_id from hub_inbound_shipment_trips
                    UNION ALL
                    select trip_id, shipment_id from van_inbound_shipment_trips
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with mawb_trip_shipments as (

                    select distinct
                        movement_trips.id
                        , shipments.id as shipment_id
                    from movement_trips
                    left join flight_info
                        on movement_trips.id = flight_info.trip_id
                    left join flight_mawb
                        on flight_info.id = flight_mawb.flight_info_id
                    left join shipment_ext_awbs
                        on flight_mawb.mawb_id = shipment_ext_awbs.id
                    left join shipments
                        on shipment_ext_awbs.id = shipments.shipment_ext_awb_id
                    where shipments.id is not null

                ),
                union_trip_shipments as (

                    select * from standard_trip_shipments
                    union
                    select * from mawb_trip_shipments

                ),
                pre_final as (

                    select distinct
                        lower(hub_relations.origin_hub_system_id) as system_id
                        , trip_cost_tiers.route as route_id
                        , movement_trips.id as trip_id
                        , movement_trips.actual_start_time
                        , union_trip_shipments.shipment_id
                        , shipment_orders.order_id
                        , movement_trips.created_at
                    from movement_trips
                    left join hub_relations
                        on movement_trips.hub_relation_id = hub_relations.id
                    left join trip_cost_tiers
                        on movement_trips.id = trip_cost_tiers.trip_id
                    left join union_trip_shipments
                        on movement_trips.id = union_trip_shipments.trip_id
                    left join shipment_orders
                        on union_trip_shipments.shipment_id = shipment_orders.shipment_id

                ),
                test_order_removal as (

                    select
                        pre_final.*
                    from pre_final
                    left join orders_enriched
                        on pre_final.system_id = orders_enriched.system_id
                        and pre_final.order_id = orders_enriched.order_id
                    left join shipper_attributes
                        on pre_final.system_id = shipper_attributes.system_id
                        and orders_enriched.shipper_id = shipper_attributes.id
                    where shipper_attributes.sales_channel != 'Test'

                ),
                final as (

                    select
                        system_id
                        , route_id
                        , trip_id
                        , from_utc_timestamp(
                            actual_start_time, {{ get_local_timezone }}
                        ) as trip_start_datetime
                        , shipment_id
                        , order_id
                        , date_format(
                            from_utc_timestamp(created_at, {{ get_local_timezone }}), 'yyyy-MM'
                        ) as created_month
                    from test_order_removal

                )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()