import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.RTS_RATES_KPI_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.RTS_RATES_KPI_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED,
                data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
        ),
)

REPORT_START_DATE = "2022-01-01"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_REPORT,
                view_name="last_mile_push_off_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_pets",
                jinja_template="""
                select
                    order_id
                    , max_by(investigating_hub_id, creation_datetime) as investigating_hub_id
                    , max(creation_datetime) as creation_datetime
                    , system_id
                from pets_tickets_enriched
                where 1=1
                    and lower(outcome) not like '%found%'
                    and lower(outcome) not like '%received%'
                    and lower(pets_tickets_enriched.type) like '%missing%'
                    and pets_tickets_enriched.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                group by 1,4
                """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE,
                }
            ),

            base.TransformView(
                view_name="base_data",
                jinja_template="""
                with 
                -- For RTS Parcels
                    latest_transaction as (
                    select
                        transactions.order_id
                        , route_logs.hub_id
                        , cast(max_by(transactions.route_id, transactions.created_at) as bigint) as route_id
                        , max(from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) as transaction_end_time
                    from transactions
                    left join route_logs
                        on transactions.route_id = route_logs.legacy_id
                    where 1=1
                        and lower(transactions.status) = 'fail'
                        and transactions.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                    group by 1,2
                ),

                -- FOR NON RTS PARCELS
                success_transactions as (
                    select
                        transactions.order_id
                        , route_logs.hub_id
                        , cast(max_by(transactions.route_id, transactions.created_at) as bigint) as route_id
                        , max(from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}')) as transaction_end_time
                    from transactions
                    left join route_logs
                        on transactions.route_id = route_logs.legacy_id
                    where 1=1
                        and lower(transactions.status) = 'success'
                        and transactions.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                    group by 1,2
                ),

                last_failure_reason as (
                     select
                        transactions.order_id
                        , max_by(failure_reasons.english_description, transactions.service_end_time) as delivery_failure_reason
                    from transactions
                    left join transaction_failure_reason
                        on transactions.id = transaction_failure_reason.transaction_id
                    left join failure_reasons
                        on transaction_failure_reason.failure_reason_id = failure_reasons.id
                        and failure_reasons.system_id = '{{ system_id }}'
                        and transactions.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                    where transactions.status = 'Fail'
                    group by 1
                ),

                  final as (
                        select
                            order_milestones.order_id
                            , order_milestones.dest_hub_id
                            , hubs_enriched.name as dest_hub_name
                            , hubs_enriched.region as dest_hub_region
                            , order_milestones.creation_datetime as order_creation_datetime
                            , order_milestones.rts_flag
                            , order_milestones.rts_before_first_attempt_flag
                            , order_milestones.rts_trigger_datetime
                            , upper(if(lower(order_milestones.rts_reason) not like '%max delivery attempt%'
                                , order_milestones.rts_reason, last_failure_reason.delivery_failure_reason)) as rts_reason_2
                            , order_milestones.first_valid_delivery_attempt_datetime
                            , pets.creation_datetime pets_ticket_creation_datetime
                            , order_milestones.delivery_success_datetime
                            , case when order_milestones.rts_flag = 1 THEN order_milestones.rts_trigger_datetime
                                   when order_milestones.delivery_success_datetime is not null then order_milestones.delivery_success_datetime 
                                   when pets.order_id is not null THEN pets.creation_datetime 
                                   ELSE order_milestones.force_success_datetime END AS measurement_datetime       
                            , order_milestones.granular_status
                            , order_milestones.parcel_size
                            , lmpr.n0_met as fifo_n0_met
                            , if(order_milestones.cod_value is not null and order_milestones.cod_value != 0, 'cod', 'non cod') as cod_flag
                            , cast(fleet_performance_base_data.courier_id as bigint) as courier_id
                            , fleet_performance_base_data.courier_name
                            , drivers_enriched.display_name as courier_display_name
                            , fleet_performance_base_data.courier_type
                            , coalesce(latest_transaction.route_id,success_transactions.route_id) as latest_delivery_route_id
                            , CAST(DATEDIFF(fleet_performance_base_data.route_date,drivers_enriched.employment_start_date)/30.42 as DOUBLE) as tenure_length_months
                            , shipper_attributes.id as shipper_id
                            , shipper_attributes.parent_id
                            , shipper_attributes.shipper_name as shipper_name
                            , shipper_attributes.parent_name
                            , shipper_attributes.sales_channel
                            , if(pets.order_id is null, 0, 1) as order_lost_flag
                            , transit_time_report.days_to_first_valid_delivery_attempt
                            , order_milestones.system_id
                            , order_milestones.created_month
                            , row_number() over(partition by order_milestones.order_id, order_milestones.system_id order by order_milestones.order_id desc) as rnk
                        from order_milestones
                        left join shipper_attributes
                            on order_milestones.shipper_id = shipper_attributes.id
                        left join hubs_enriched
                            on order_milestones.dest_hub_id = hubs_enriched.id
                        left join latest_pets as pets
                            on pets.order_id = order_milestones.order_id
                            and pets.investigating_hub_id = order_milestones.dest_hub_id
                        left join latest_transaction
                            on order_milestones.order_id = latest_transaction.order_id
                            and order_milestones.dest_hub_id = latest_transaction.hub_id
                            and order_milestones.rts_flag = 1
                        left join success_transactions
                            on order_milestones.order_id = success_transactions.order_id
                            and order_milestones.dest_hub_id = success_transactions.hub_id
                            and order_milestones.rts_flag = 0
                        left join fleet_performance_base_data
                            on coalesce(latest_transaction.route_id, success_transactions.route_id) = fleet_performance_base_data.route_id
                            and coalesce(date(latest_transaction.transaction_end_time), date(success_transactions.transaction_end_time)) = date(fleet_performance_base_data.route_date)
                        left join drivers_enriched
                            on fleet_performance_base_data.courier_id = drivers_enriched.id
                        left join last_mile_push_off_report as lmpr
                            on order_milestones.order_id = lmpr.order_id
                        left join transit_time_report
                            on order_milestones.order_id = transit_time_report.order_id
                        left join last_failure_reason
                            on order_milestones.order_id = last_failure_reason.order_id
                        where 1=1
                            and lower(hubs_enriched.facility_type) like ('%station%')
                            and lower(shipper_attributes.sales_channel) not in ('test')
                            and order_milestones.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                )

                select * from final
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                    "report_start_date": REPORT_START_DATE,
                }
            ),
            base.TransformView(
                view_name="rts_reason_others",
                jinja_template="""
                 select 
                    order_id
                   , case 
                       when rts_flag = 0 THEN null
                        -- shipper issues
                        when rts_reason_2 LIKE '%BAD%' or rts_reason_2 LIKE '%FRAUD%' then 'FRAUD SHIPPER'
                        when rts_reason_2 LIKE '%SHIPPER%REQ%' 
                            or rts_reason_2 LIKE '%REQ%SHIPPER%' 
                            or rts_reason_2 LIKE '%BỊ HỦY BỞI NGƯỜI BÁN' then 'SHIPPER REQUESTED'
                        when rts_reason_2 LIKE '%SHIPPER%UPDATE%' or rts_reason_2 LIKE '%UPDATE%SHIPPER%' then 'NO UPDATE FROM SHIPPER'

                        -- customer issues
                        when rts_reason_2 LIKE '%CUSTOMER%DELAYED%BEYOND%' 
                            or rts_reason_2 LIKE '%KHÁCH HÀNG TRÌ HOÃN THỜI GIAN GIAO HÀNG' then 'CUSTOMER DELAYED BEYOND DELIVERY PERIOD'
                        when rts_reason_2 LIKE '%CUSTOMER%NOT%REACHABLE%' 
                            or rts_reason_2 LIKE '%UNCONTACT%' 
                            or rts_reason_2 like '%NO%ONE%RECEIVE%' 
                            or rts_reason_2 like '%CLOSED%'
                            or rts_reason_2 like '%NOT%AT%LOCATION%'
                            then 'UNABLE TO CONTACT CUSTOMER'
                        when rts_reason_2 like '%REFUSED%TO%PAY%'
                            or rts_reason_2 like '%PAY%'
                            then 'CUSTOMER REFUSED TO PAY COD'
                        when rts_reason_2 LIKE '%CANCEL%' 
                            or rts_reason_2 LIKE '%CUSTOMER%REJECT%' 
                            or rts_reason_2 LIKE '%REFUSE%'                    
                            or (rts_reason_2 LIKE '%CUSTOMER%' AND rts_reason_2 NOT LIKE '%MOVED%OUT%')
                            or rts_reason_2 LIKE '%ITEM%REFUSED%AT%DOORSTEP%' 
                            or rts_reason_2 LIKE '%TỪ%CHỐI%' then 'CUSTOMER REJECT'
                        when rts_reason_2 LIKE '%DIDN%T%ORDER%' 
                            or rts_reason_2 LIKE '%NO%ORDER%'then 'CUSTOMER DIDNT ORDER'
                        when rts_reason_2 LIKE '%OMPLAIN%' then 'CUSTOMER COMPLAINT'

                        -- information issues 
                        when rts_reason_2 LIKE '%ADDRESS%' 
                            or rts_reason_2 LIKE '%LOCATION%' 
                            or rts_reason_2 LIKE '%POST%CODE%'
                            or rts_reason_2 LIKE '%ZONE%'
                            or rts_reason_2 LIKE '%MOVED%OUT%'
                            or rts_reason_2 LIKE '%KHÔNG THỂ TÌM THẤY ĐỊA CHỈ' then 'WRONG ADDRESS'
                        when rts_reason_2 LIKE '%OUT%OF%COVERAGE%' 
                            or rts_reason_2 LIKE '%NGOÀI VÙNG BAO PHỦ' then 'OUT OF COVERAGE'
                        when rts_reason_2 LIKE '%D%BLE%ID%' 
                            or rts_reason_2 LIKE '%DOUBLE%' then 'DOUBLE TRACKING ID' 
                        when rts_reason_2 LIKE '%DISPUTED%ORDER%INFO%' then 'DISPUTED ORDER INFO'

                        -- goods issues 
                        when rts_reason_2 LIKE '%DANGEROUS%GOOD%' 
                            or rts_reason_2 LIKE '%SUSPICIOUS%' 
                            or rts_reason_2 LIKE '%MẶT HÀNG NGUY HIỂM' then 'DANGEROUS GOODS'     
                        when rts_reason_2 LIKE '%FO%MAJ%RE%'
                            or rts_reason_2 LIKE '%ĐƠN HÀNG BỊ HỦY BỞI TÌNH HUỐNG BẤT KHẢ KHÁNG' then 'FORCE MAJEURE'
                        when rts_reason_2 LIKE '%DAMAGE%' 
                            or rts_reason_2 LIKE '%DMG%' 
                            or rts_reason_2 LIKE '%DEFECT%' 
                            or rts_reason_2 LIKE '%BROKEN%' then 'DAMAGED ITEM'
                        when rts_reason_2 LIKE '%OVERSIZED%PARCEL%' then 'OVERSIZED PARCEL'
                        when rts_reason_2 LIKE '%DUPLICATE%' then 'DUPLICATE PARCEL'
                        when rts_reason_2 LIKE '%WRONG I%' then 'WRONG ITEM'
                        when rts_reason_2 LIKE '%POOR%PA%CK%' 
                            or rts_reason_2 LIKE '%PACKAG%'then 'POOR PACKAGING'

                        -- other issues
                        when rts_reason_2 LIKE '%OTHER%REASON%' 
                            or rts_reason_2 LIKE '%LÝ DO KHÁC%' then 'OTHER REASON'
                        --no other info
                        when rts_reason_2 LIKE '%MAX%DELIVERY%ATTEMPT%' or rts_reason_2 LIKE '%MAX%ATTEM%' 
                            or rts_reason_2 LIKE '%KHÔNG%CÓ%NGƯỜI%NHẬN%HÀNG%TẠI%ĐỊA%CHỈ%' then 'MAX DELIVERY ATTEMPT'
                        else 'EVERYTHING ELSE'
                    end as rts_reason_new
                    , system_id
                from base_data
                where system_id != 'sg' and rnk = 1             
                """,
            ),
            base.TransformView(
                view_name="rts_reason_sg",
                jinja_template="""
             -- STEP 1: EXTRACT THE TICKET NUMBER 
                with 
                     sg_ticket ( 
                         select 
                            order_id 
                            , rts_reason_2
                            , case when 
                                rts_reason_2 LIKE '%TICKET_________' then substring(rts_reason_2, -15) 
                                else rts_reason_2 
                            end as rts_ticket
                            , system_id
                        from base_data   
                        where system_id = 'sg' and rnk = 1
                    ),

             -- STEP 2 : MAP THE TICKET TO PETS ENRICHED
                    ticket_type as (
                        select 
                            sg_ticket.system_id
                            , sg_ticket.order_id
                            , sg_ticket.rts_ticket
                            , sg_ticket.rts_reason_2
                            , pets_tickets_enriched.type
                            , pets_tickets_enriched.sub_type
                            from sg_ticket
                            left join pets_tickets_enriched
                                on sg_ticket.order_id = pets_tickets_enriched.order_id
                                and sg_ticket.rts_ticket = upper(pets_tickets_enriched.ticket_notes)          
                    )
             -- STEP 2 : PERFORM THE MAPPING
                        select
                            order_id
                            , case 
                                -- shipper issues
                                when rts_ticket LIKE '%BAD%' or rts_ticket LIKE '%FRAUD%' then 'FRAUD SHIPPER'
                                when rts_ticket LIKE '%SHIPPER%REQ%' 
                                    or rts_ticket LIKE '%REQ%SHIPPER%' then 'SHIPPER REQUESTED'
                                when rts_ticket LIKE '%SHIPPER%UPDATE%' or rts_ticket LIKE '%UPDATE%SHIPPER%' then 'NO UPDATE FROM SHIPPER'
                                when rts_ticket LIKE '%SHIPPER%' then 'SHIPPER ISSUES'

                                -- customer issues
                                when rts_ticket LIKE '%CUSTOMER%DELAYED%BEYOND%' then 'CUSTOMER DELAYED BEYOND DELIVERY PERIOD'
                                when rts_ticket LIKE '%CUSTOMER%NOT%REACHABLE%' 
                                    or rts_ticket LIKE '%UNCONTACT%' 
                                    or rts_ticket like '%NO%ONE%RECEIVE%' 
                                    or rts_ticket like '%CLOSED%'
                                    or rts_ticket like '%NOT%AT%LOCATION%'
                                    then 'UNABLE TO CONTACT CUSTOMER'                
                                when rts_ticket like '%REFUSED%TO%PAY%'
                                    or rts_ticket like '%PAY%'
                                    then 'CUSTOMER REFUSED TO PAY COD'                
                                when rts_ticket LIKE '%CANCEL%' 
                                    or rts_ticket LIKE '%CUSTOMER%REJECT%' 
                                    or rts_ticket LIKE '%REFUSE%'                    
                                    or rts_ticket LIKE '%CUSTOMER%REQUEST%' 
                                    or rts_ticket LIKE '%ITEM%REFUSED%AT%DOORSTEP%' then 'CUSTOMER REJECT'
                                when rts_ticket LIKE '%DIDN%T%ORDER%'
                                    or rts_ticket LIKE '%NO%ORDER%'then 'CUSTOMER DIDNT ORDER'
                                when rts_ticket LIKE '%OMPLAIN%' then 'CUSTOMER COMPLAINT'

                                -- information issues 
                                when rts_ticket LIKE '%ADDRESS%' 
                                    or rts_ticket LIKE '%LOCATION%' 
                                    or rts_ticket LIKE '%POST%CODE%'
                                    or (rts_ticket LIKE '%CUSTOMER%' AND rts_reason_2 NOT LIKE '%MOVED%OUT%')
                                    or rts_ticket LIKE '%ZONE%' then 'WRONG ADDRESS'
                                when rts_ticket LIKE '%OUT%OF%COVERAGE%' then 'OUT OF COVERAGE'
                                when rts_ticket LIKE '%D%BLE%ID%' 
                                    or rts_ticket LIKE '%DOUBLE%' then 'DOUBLE TRACKING ID' 
                                when rts_ticket LIKE '%DISPUTED%ORDER%INFO%' then 'DISPUTED ORDER INFO'

                                -- goods issues 
                                when rts_ticket LIKE '%DANGEROUS%GOOD%' 
                                    or rts_ticket LIKE '%SUSPICIOUS%' then 'DANGEROUS GOODS'     
                                when rts_ticket LIKE '%FO%MAJ%RE%' then 'FORCE MAJEURE'
                                when rts_ticket LIKE '%DAMAGE%' 
                                    or rts_ticket LIKE '%DMG%' 
                                    or rts_ticket LIKE '%DEFECT%' 
                                    or rts_ticket LIKE '%BROKEN%' then 'DAMAGED ITEM'
                                when rts_ticket LIKE '%OVERSIZED%PARCEL%' then 'OVERSIZED PARCEL'
                                when rts_ticket LIKE '%DUPLICATE%' then 'DUPLICATE PARCEL'
                                when rts_ticket LIKE '%WRONG I%' then 'WRONG ITEM'
                                when rts_ticket LIKE '%POOR%PA%CK%' 
                                    or rts_ticket LIKE '%PACKAG%'then 'POOR PACKAGING'

                                -- other issues
                                when rts_ticket LIKE '%OTHER%REASON%' then 'OTHER REASON'
                                when rts_ticket LIKE '%TICKET%' then (case when sub_type is null then type else sub_type end)

                                --no other info
                                when rts_ticket LIKE '%MAX%DELIVERY%ATTEMPT%' 
                                    or rts_ticket LIKE '%MAX%ATTEM%' then 'MAX DELIVERY ATTEMPT'
                                else 'EVERYTHING ELSE'
                            end as rts_reason_new
                            , system_id
                        from ticket_type       
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with 
                    rts_reasons_cte as (
                        select
                            *
                        from rts_reason_others
                        UNION ALL
                        select * 
                        from rts_reason_sg
                    ),

                   map_rts_reason as (

                    select 
                        base_data.order_id
                        , base_data.dest_hub_id
                        , base_data.dest_hub_name
                        , base_data.dest_hub_region
                        , base_data.order_creation_datetime
                        , base_data.rts_flag
                        , base_data.rts_before_first_attempt_flag
                        , base_data.rts_trigger_datetime
                        , rts_reasons_cte.rts_reason_new
                        , base_data.first_valid_delivery_attempt_datetime
                        , base_data.pets_ticket_creation_datetime
                        , base_data.delivery_success_datetime
                        , base_data.measurement_datetime
                        , base_data.granular_status
                        , base_data.parcel_size
                        , base_data.fifo_n0_met
                        , base_data.cod_flag
                        , base_data.courier_id
                        , base_data.courier_name
                        , base_data.courier_display_name
                        , base_data.courier_type
                        , base_data.latest_delivery_route_id
                        , base_data.tenure_length_months
                        , base_data.shipper_id
                        , base_data.parent_id
                        , base_data.shipper_name
                        , base_data.parent_name as parent_shipper_name
                        , base_data.sales_channel
                        , base_data.order_lost_flag
                        , base_data.days_to_first_valid_delivery_attempt
                        , base_data.system_id
                        , base_data.created_month
                        , row_number() over(partition by base_data.order_id, base_data.system_id order by base_data.order_id) as rnk
                    from base_data
                    left join rts_reasons_cte
                        on base_data.order_id = rts_reasons_cte.order_id
                )
                   select 
                    order_id
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , order_creation_datetime
                    , rts_flag
                    , rts_before_first_attempt_flag
                    , rts_trigger_datetime
                    , rts_reason_new
                    , first_valid_delivery_attempt_datetime
                    , pets_ticket_creation_datetime
                    , delivery_success_datetime
                    , measurement_datetime
                    , measurement_datetime as evaluation_datetime
                    , granular_status
                    , fifo_n0_met
                    , parcel_size
                    , cod_flag
                    , courier_id
                    , courier_name
                    , courier_display_name
                    , courier_type
                    , latest_delivery_route_id
                    , tenure_length_months
                    , shipper_id
                    , parent_id
                    , shipper_name
                    , parent_shipper_name
                    , sales_channel
                    , order_lost_flag
                    , days_to_first_valid_delivery_attempt
                    , system_id
                    , created_month
                from map_rts_reason
                where rnk = 1

            """
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).RTS_RATES_KPI,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()