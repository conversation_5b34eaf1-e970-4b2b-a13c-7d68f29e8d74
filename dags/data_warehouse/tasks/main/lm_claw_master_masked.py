import sys

from pyspark.sql import SparkSession
from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SupportDAG.Task.LM_CLAW_MASTER_MASKED + ".py",
    task_name=data_warehouse.SupportDAG.Task.LM_CLAW_MASTER_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.HUB_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID, task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.CISP_PRIOR_REPORT_DAILY_MASKED
        ),        
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.RTS_RATES_KPI_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.POD_VALIDATION_TASKS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_SPEED_MASKED
        ),
    ),
    system_ids=(constants.SystemID.MY,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="my_views", partition_columns=("system_id",)),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    # This task only performs partial run
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, 
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_COMPLETION_VOL_DAILY,
                view_name="hub_completion_vol_daily",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),

            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CISP_PRIOR_REPORT_DAILY,
                view_name="cisp_prior_report_daily",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CISP_COMPLETION_REPORT_DAILY,
                view_name="cisp_completion_report_daily",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RTS_RATES_KPI,
                view_name="rts_rates_kpi",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_REPORT,
                view_name="last_mile_push_off_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POD_VALIDATION_TASKS_ENRICHED,
                view_name="pod_validation_tasks_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR, 
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_SPEED,
                view_name="calendar_speed",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hubs_cal",
                jinja_template="""
                SELECT
                      h.id
                    , facility_type
                    , h.region
                    , c.working_day
                    , date
                    , system_id
                    , h.created_month
                FROM hubs_enriched h
                JOIN (
                    SELECT
                        date(date) as date 
                        , working_day 
                        , region 
                    FROM calendar
                    WHERE date >= date_trunc('month', add_months(date_add(current_date, -1), -1))
                        and date < date_trunc('day', current_date)
                        and system_id='my'
                ) c on c.region = h.address_city
                WHERE facility_type in ('STATION', 'CROSSDOCK_STATION')
                    and h.id != 520
                    and h.system_id='my'
                """,
            ),
            base.TransformView(
                view_name="monthly_vols",
                jinja_template="""
                SELECT
                    case when completion_hub_id = 530 then 330 
                        else completion_hub_id
                    end as completion_hub_id
                    , cast(date_trunc('month', completion_date) as date) as completion_mth
                    , sum(total_orders) as total_orders
                FROM hub_completion_vol_daily
                WHERE date_trunc('month', completion_date) >= date_trunc('month', add_months(date_add(current_date, -1), -5))
                    and completion_hub_id in (select id from hubs_enriched where facility_type in ('STATION', 'CROSSDOCK_STATION') and system_id='my')
                    and system_id='my'
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="hybrid",
                jinja_template="""
                SELECT
                    case
                        when depot_id = 530 then 330
                        else depot_id
                    end as depot_id,
                    to_date(route_date) as route_date,
                    sum(parcels_delivered) as Total_Success
                FROM 
                    fleet_performance_base_data
                WHERE
                    date_trunc('day', route_date) >= date_trunc('month', add_months(date_add(current_date, -1), -1))
                    and date_trunc('day', route_date)< date_trunc('day', current_date)
                    and system_id='my'
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="prior",
                jinja_template="""
                SELECT   
                    case
                        when dest_hub_id = 530 then 330
                        else dest_hub_id
                    end as dest_hub_id,
                    report_date,
                    sum(attempt_measured) as Prior_Measured,
                    sum(attempt_met) as Prior_Met,
                    cast(sum(attempt_met) as double) / if(sum(attempt_measured) = 0, null, sum(attempt_measured)) as Prior_Rate
                FROM 
                    cisp_prior_report_daily
                WHERE
                    date_trunc('day', report_date) >= date_trunc('month', add_months(date_add(current_date, -1), -1))
                    and date_trunc('day', report_date) < date_trunc('day', current_date)
                    and system_id='my'
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="cisp",
                jinja_template="""
                SELECT
                    case
                        when dest_hub_id = 530 then 330
                        else dest_hub_id
                    end as dest_hub_id,
                    report_date,
                    sum(case when dest_hub_region = 'East Malaysia' then n2_measured_count else n0_measured_count end) as D0D2_Measured,
                    sum(case when dest_hub_region = 'East Malaysia' then n2_met_count else n0_met_count end) as D0D2_Met,
                    sum(n3_measured_count) as D3_Measured,
                    sum(n3_met_count) as D3_Met,
                    cast(sum(case when dest_hub_region = 'East Malaysia' then n2_met_count else n0_met_count end) as double) / if(sum(case when dest_hub_region = 'East Malaysia' then n2_measured_count else n0_measured_count end) = 0, null, sum(case when dest_hub_region = 'East Malaysia' then n2_measured_count else n0_measured_count end)) as D0D2_Rate,
                    cast(sum(n3_met_count) as double) / if(sum(n3_measured_count) = 0, null, sum(n3_measured_count)) as D3_Rate
                FROM 
                    cisp_completion_report_daily
                WHERE
                    date_trunc('day', report_date)>= date_trunc('month', add_months(date_add(current_date, -1), -1))
                    and date_trunc('day', report_date)< date_trunc('day', current_date)
                    and created_month >= date_format(add_months(current_date, -4), 'yyyy-MM')
                    and system_id = 'my'
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="lost",
                jinja_template="""
                SELECT
                    case
                        when investigating_hub_id = 530 then 330
                        else investigating_hub_id
                    end as hub_id,
                    date(resolution_datetime) as resolved_date,
                    count(*) as resolved_damaged_tickets
                FROM
                    pets_tickets_enriched
                WHERE 
                    cast(resolution_datetime as date) >= cast(date_trunc('month', add_months(date_add(current_date, -1), -1)) as DATE)
                    and cast(resolution_datetime as date) < cast(date_trunc('day', current_date) as date)
                    and status = 'RESOLVED'
                    and type = 'MISSING'
                    and outcome like 'LOST%'
                    and system_id = 'my'
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="rts",
                jinja_template="""
                SELECT
                    case
                        when dest_hub_id = 530 then 330
                        else dest_hub_id
                    end as dest_hub_id
                    , date(delivery_success_datetime) as completion_date 
                    , sum(rts_flag) as RTS_Orders
                    , cast(sum(rts_flag) as double) / count(*) as RTS_Rate
                    , sum(case when rts_flag = 1 and cod_flag = 'cod' then 1 else 0 end) as COD_RTS_Orders
                    , cast(sum(case when rts_flag = 1 and cod_flag = 'cod' then 1 else 0 end) as double) / if(count_if(cod_flag = 'cod') = 0, 1,count_if(cod_flag = 'cod')) as COD_RTS_Rate
                FROM (
                    SELECT
                        order_id 
                        , rts_flag
                        , cod_flag 
                        , dest_hub_id
                        , delivery_success_datetime
                    FROM rts_rates_kpi
                    WHERE date_trunc('day', delivery_success_datetime) >= date_trunc('month', add_months(date_add(current_date, -1), -1)) 
                        and date_trunc('day', delivery_success_datetime) < date_trunc('day', current_date)
                        and created_month >= date_format(add_months(current_date, - 4), 'yyyy-MM')
                        and system_id = 'my'
                )
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="epd",
                jinja_template="""
                    select 
                        case when dest_hub_id = 530 then 330 
                            else dest_hub_id
                        end as dest_hub_id
                        , date(dest_hub_datetime) as shipment_date 
                        , cast(count_if(date(first_valid_delivery_attempt_datetime) <= n0_cutoff_date and (first_valid_delivery_failure_reason_id is null or first_valid_delivery_failure_reason_id not in (158, 159, 160, 161))) as double) / count(*) as first_day_attempt_rate
                    from (
                        select 
                            a.*
                            , case 
                                when working_day = 1 then start_clock_date
                                else next_working_day_1
                            end as n0_cutoff_date
                        from (
                            select 
                                lmp.dest_hub_id 
                                , dest_hub_address_city
                                , dest_hub_datetime
                                , case 
                                    when hour(dest_hub_datetime) < 14 and lmp.dest_hub_region = 'East Malaysia' then date(dest_hub_datetime)
                                    when hour(dest_hub_datetime) < 18 and lmp.dest_hub_region != 'East Malaysia' then date(dest_hub_datetime)
                                    else date(dest_hub_datetime) + interval '1' day
                                end as start_clock_date
                                , lmp.first_valid_delivery_attempt_datetime
                                , first_valid_delivery_failure_reason_id
                            from (
                                select 
                                    order_id
                                    , dest_hub_id
                                    , dest_hub_datetime
                                    , first_valid_delivery_attempt_datetime
                                    , dest_hub_region
                                    , he.address_city as dest_hub_address_city
                                from last_mile_push_off_report lmp
                                join hubs_enriched he on he.id = lmp.dest_hub_id
                                    and he.facility_type in ('STATION', 'CROSSDOCK_STATION')
                                    and lmp.system_id = he.system_id
                                where cast(date_trunc('day', dest_hub_datetime) as date) >= cast(date_trunc('month', add_months(date_add(current_date, -1), -1)) as date)
                                    and cast(date_trunc('day', dest_hub_datetime) as date) < cast(date_trunc('day', current_date) as date)
                                    and first_valid_delivery_attempt_datetime is not null
                                    and lmp.created_month >= date_format(add_months(current_date, -4),'yyyy-MM')
                                    and lmp.system_id = 'my'
                            ) lmp
                            join ( 
                                select
                                    order_id 
                                    , first_valid_delivery_failure_reason_id
                                    , third_party_tracking_id
                                from order_milestones 
                                where created_month >= date_format(add_months(current_date, -4), 'yyyy-MM')
                                    and country = 'my'
                                ) om on om.order_id = lmp.order_id
                                    and third_party_tracking_id is null
                            left join pets_tickets_enriched pets on pets.order_id = lmp.order_id 
                                and pets.creation_datetime > lmp.dest_hub_datetime
                                and pets.creation_datetime < lmp.first_valid_delivery_attempt_datetime
                                and pets.system_id = 'my'
                            where pets.id is null
                        ) a
                        
                        left join (
                            select 
                                date 
                                , region 
                                , working_day
                                , next_working_day_1
                            from calendar_speed where country = 'my'
                            ) c on c.date = start_clock_date 
                                and c.region = a.dest_hub_address_city
                    )
                    
                    group by 1,2

                """,
            ),
            base.TransformView(
                view_name="pod",
                jinja_template="""
                select 
                    hub_id
                    , cast(date_trunc('day', attempted_datetime) as date) as attempted_date
                    , count_if(validation_result = 'FAILURE') as invalid_pod
                    , count(*) as ttl_validated
                    , count_if(validation_result = 'FAILURE') *1.0000 / count(*) as invalid_pod_rate
                from pod_validation_tasks_enriched
                where transaction_status = 'FAIL'
                    and transaction_type = 'DELIVERY'
                    and date_trunc('day', attempted_datetime) >= date_trunc('month', add_months(date_add(current_date, -1), -1))
                    and date_trunc('day', attempted_datetime) < date_trunc('day', current_date)
                    and validation_result is not null
                    and shipper_id in (7885898, 4610784, 4582102, 5191329, 346771, 5554395, 6716867, 7314875, 7323822, 8305796, 3291110, 5038227, 8730981, 8731392, 8233752)
                    and created_month >= date_format(add_months(current_date, -4), 'yyyy-MM')
                    and system_id = 'my'
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="open",
                jinja_template="""
                with total_orders_last_mth_avg_vol as (
                    select
                        completion_hub_id,
                        avg(total_orders) as last_mth_avg_vol
                    from monthly_vols m
                    where completion_mth = cast(date_trunc('month', add_months(date_add(current_date, -1),-2)) as date)
                    group by 1
                )
                , total_orders_curr_mth_avg_vol as (
                        select
                        completion_hub_id,
                        avg(total_orders) as curr_mth_avg_vol
                    from monthly_vols m
                    where completion_mth = cast(date_trunc('month', add_months(date_add(current_date, -1),-1)) as date)
                    group by 1
                )
                
                    select
                        vols.completion_hub_id as completion_hub_id,
                        last.last_mth_avg_vol as last_mth_avg_vol,
                        curr.curr_mth_avg_vol as curr_mth_avg_vol,
                        count(*)
                    from monthly_vols vols
                    join total_orders_last_mth_avg_vol last on vols.completion_hub_id = last.completion_hub_id
                    join total_orders_curr_mth_avg_vol curr on vols.completion_hub_id = curr.completion_hub_id
                    group by 1, 2,3
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT
                    current_date as currentDate,
                    date as calendar_date,
                    route_date as RouteDate,
                    id as HubID,
                    Total_Success TotalSuccess,
                    case when working_day = 0 then 1 
                        else if(Prior_Rate is null, 1, Prior_Rate) 
                    end as PriorRate,
                    case when working_day = 0 then 1 
                        else if(D0D2_Rate is null, 1, D0D2_Rate)
                    end as D0CompletionRate,
                    case when working_day = 0 then 1 
                        else if(D3_Rate is null, 1, D3_Rate) 
                    end as D3Rate,
                    case when working_day = 0 then 0 
                        else if((cast(resolved_damaged_tickets as double) / if(Total_Success = 0, 1, Total_Success)) is null, 0,cast(resolved_damaged_tickets as double) / if(Total_Success = 0, 1, Total_Success))
                    end as LostRate,
                    case when working_day = 0 then 0
                        else if(RTS_Rate is null, 0, RTS_Rate) 
                    end as RTSRate,
                    case 
                        when working_day = 0 then 0 
                        else if(COD_RTS_Rate is null, 0, COD_RTS_Rate)
                    end as CODRTSRate,
                    region as HubRegion,
                    case when working_day = 0 then 1
                        else if(epd.first_day_attempt_rate is null, 1, epd.first_day_attempt_rate)
                    end as FIFOD0Rate,
                    month(date) as Month,
                    case when month(date) = month(current_date - interval '1' day) then curr_mth_avg_vol
                        else last_mth_avg_vol
                    end as VolLM,
                    working_day as WorkingDay,
                    case when working_day = 0 then 0
                        else if(invalid_pod_rate is null, 0, invalid_pod_rate)
                    end as InvalidPODRate,
                    h.system_id,
                    CAST(date_format(current_date, 'yyyy-MM') as date) as created_month
                FROM hubs_cal h
                LEFT JOIN hybrid ON h.id = hybrid.depot_id and h.date = route_date
                LEFT JOIN prior ON prior.dest_hub_id = h.id and prior.report_date = h.date
                LEFT JOIN cisp ON cisp.dest_hub_id = h.id and cisp.report_date = h.date
                LEFT JOIN lost ON lost.hub_id = h.id and resolved_date = h.date
                LEFT JOIN rts ON rts.completion_date = h.date and rts.dest_hub_id = h.id
                LEFT JOIN epd ON epd.shipment_date = h.date and epd.dest_hub_id = h.id
                LEFT JOIN pod ON pod.attempted_date = h.date and pod.hub_id = h.id
                LEFT JOIN open ON open.completion_hub_id = h.id
                WHERE facility_type in ('STATION', 'CROSSDOCK_STATION')
                    and h.id not in (520,530)
                    order by 4, date
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LM_CLAW_MASTER,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.system_id, input_args.last_measurement_datetime, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()