import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.MILKRUN_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.MILKRUN_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="id_views"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_ADDRESSES,
                view_name="shipper_addresses",
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_ADDRESS_MILKRUN_SETTINGS,
                view_name="shipper_address_milkrun_settings",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).MILKRUN_GROUPS,
                view_name="milkrun_groups",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).MILKRUN_SAMS,
                view_name="milkrun_sams",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                      select
                        shipper_addresses.id address_id
                        , shipper_addresses.name address_name
                        , concat_ws(' ', address1, address2) shipper_address
                        , shipper_addresses.shipper_id
                        , shipper_attributes.shipper_name as shipper_name
                        , is_milk_run
                        , milkrun_groups.id milkrun_group_id
                        , milkrun_groups.name milkrun_group_name
                        , milkrun_groups.driver_id courier_id
                        , milkrun_groups.hub_id
                        , lower(shipper_addresses.country) system_id
                        , shipper_addresses.created_month
                        , row_number() over(partition by shipper_addresses.id, milkrun_groups.name
                            order by shipper_address_milkrun_settings.created_at desc) as rnk
                    from shipper_addresses
                    left join shipper_address_milkrun_settings
                        on shipper_addresses.id = shipper_address_milkrun_settings.address_id
                    left join milkrun_sams
                        on shipper_address_milkrun_settings.id = milkrun_sams.sams_id
                    left join milkrun_groups
                        on milkrun_sams.milkrun_group_id  = milkrun_groups.id 
                    left join shipper_attributes
                        on shipper_addresses.shipper_id = shipper_attributes.id
                    where lower(shipper_addresses.country) = '{{ system_id }}'
                )
                
                select
                    address_id
                    , address_name
                    , shipper_address
                    , shipper_id
                    , shipper_name
                    , is_milk_run
                    , milkrun_group_id
                    , milkrun_group_name
                    , courier_id
                    , hub_id
                    , system_id
                    , created_month
                from base
                where rnk = 1
                """,
                jinja_arguments={'system_id': system_id}
            ),
        ),
        output_range=lookback_ranges.output,
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MILKRUN_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()