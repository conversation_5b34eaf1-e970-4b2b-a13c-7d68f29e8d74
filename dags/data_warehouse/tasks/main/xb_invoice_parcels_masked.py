import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_INVOICE_PARCELS_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_INVOICE_PARCELS_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="pricing"),),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).INVOICE_PARCELS,
                view_name="invoice_parcels",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    invoice_id
                    , created_at
                    , lane
                    , granular_nature
                    , billing_unit_quantity
                    , billing_unit
                    , currency
                    , amount
                    , vat
                    , tracking_id
                    , 'gl' as system_id
                    , created_month
                from invoice_parcels
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_INVOICE_PARCELS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
