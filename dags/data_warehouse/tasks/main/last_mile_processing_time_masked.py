import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_PROCESSING_TIME_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_PROCESSING_TIME_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_REPORT_MASKED,)
    ,
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
                               task_id=data_warehouse.OrderEventsDAG.Task.DRIVER_START_ROUTE_EVENTS_MASKED),
        base.DependsOnExternal(dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
                               task_id=data_warehouse.OrderEventsDAG.Task.ORDER_HUB_MOVEMENTS_MASKED),
        base.DependsOnExternal(dag_id=data_warehouse.RecoveryDAG.DAG_ID,
                               task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LAST_MILE_PUSH_OFF_REPORT,
                view_name="last_mile_push_off_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_HUB_MOVEMENTS,
                view_name="order_hub_movements",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVER_START_ROUTE_EVENTS,
                view_name="driver_start_route_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                with inbound_scans_conversion as (
                select
                    order_id
                    , hub_id
                    , from_utc_timestamp(created_at, '{{ local_timezone }}') created_at
                from inbound_scans
                where type = 4
                    and result = 'S3'
                )

                select
                    last_mile_push_off_report.order_id
                    , dest_hub_id
                    , dest_hub_region
                    , dest_hub_datetime
                    , last_mile_push_off_report.system_id
                    , last_mile_push_off_report.created_month
                    , min(created_at) first_driver_inbound_datetime
                    , max(created_at) last_driver_inbound_datetime
                    , min(event_datetime) first_driver_start_route_datetime
                from last_mile_push_off_report
                left join inbound_scans_conversion
                    on last_mile_push_off_report.order_id = inbound_scans_conversion.order_id
                    and last_mile_push_off_report.dest_hub_id = inbound_scans_conversion.hub_id
                    and dest_hub_datetime <= inbound_scans_conversion.created_at
                left join driver_start_route_events
                    on driver_start_route_events.order_id = last_mile_push_off_report.order_id
                where 1=1
                    group by {{ range(1, 7) | join(',') }}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                }
            ),
            base.TransformView(
                view_name="pets_base",
                jinja_template="""
                select
                    pets.order_id
                    , pets.creation_datetime
                    , pets.resolution_datetime
                    , order_hub_movements.hub_id
                from pets_tickets_enriched pets
                left join order_hub_movements
                    on pets.order_id = order_hub_movements.order_id
                    and pets.creation_datetime >= order_hub_movements.entry_datetime
                    and pets.creation_datetime <= order_hub_movements.exit_datetime
                """,
            ),
            base.TransformView(
                view_name="pets_base",
                jinja_template="""
                with merge_pets as (
                    select
                        base.order_id
                        , dest_hub_id
                        , hubs_enriched.address_city
                        , dest_hub_region
                        , dest_hub_datetime
                        , first_driver_inbound_datetime
                        , last_driver_inbound_datetime
                        , first_driver_start_route_datetime
                        , base.system_id
                        , base.created_month 
                        , min(pets_base.creation_datetime) earliest_pets_creation_datetime
                        , max(resolution_datetime) latest_pets_resolution_datetime
                    from base
                    left join pets_base
                        on base.order_id = pets_base.order_id
                        and base.dest_hub_id = pets_base.hub_id
                        and base.dest_hub_datetime <= pets_base.creation_datetime
                    left join hubs_enriched
                        on hubs_enriched.id = base.dest_hub_id
                    group by {{ range(1,11) | join (',')}}
                ),

                calendar_merge as (
                select
                    merge_pets.*
                    , datediff(date(latest_pets_resolution_datetime), date(earliest_pets_creation_datetime)) pets_resolution_datediff
                    , datediff(date(first_driver_inbound_datetime), date(dest_hub_datetime)) first_driver_inbound_datediff
                    , datediff(date(last_driver_inbound_datetime), date(dest_hub_datetime)) last_driver_inbound_datediff
                    , datediff(date(first_driver_start_route_datetime), date(dest_hub_datetime)) first_driver_start_route_datediff
                    {% for event_name, timestamp_name  in timestamp_config.items() %}
                    , cal_{{event_name}}.working_day_cum cal_{{event_name}}_working_day_cum
                    {% endfor %}
                from merge_pets
                {% for event_name, timestamp_name in timestamp_config.items() %}
                left join calendar cal_{{event_name}}
                    on date({{timestamp_name}}) = cal_{{event_name}}.next_working_day_0
                    and ((cal_{{event_name}}.region = 'national' and merge_pets.system_id != 'my') or (merge_pets.system_id = 'my' and lower(cal_{{event_name}}.region) = lower(merge_pets.address_city)))
                {% endfor %}
                ),

                pets_resolution_time as (
                select
                    *
                    , ((to_unix_timestamp(latest_pets_resolution_datetime) - to_unix_timestamp(earliest_pets_creation_datetime)) / 3600) - ((pets_resolution_datediff - (cal_latest_pets_resolution_working_day_cum - cal_earliest_pets_creation_working_day_cum))*24) resolution_time_hours
                    , ((to_unix_timestamp(first_driver_inbound_datetime) - to_unix_timestamp(dest_hub_datetime)) / 3600) - ((first_driver_inbound_datediff - (cal_first_driver_inbound_working_day_cum - cal_shipment_completion_working_day_cum))* 24) time_to_first_driver_inbound_hours
                    , (to_unix_timestamp(last_driver_inbound_datetime) - to_unix_timestamp(dest_hub_datetime)) / 3600 - ((last_driver_inbound_datediff - (cal_last_driver_inbound_working_day_cum - cal_shipment_completion_working_day_cum))* 24) time_to_last_driver_inbound_hours
                    , (to_unix_timestamp(first_driver_start_route_datetime) - to_unix_timestamp(dest_hub_datetime)) / 3600 - ((first_driver_start_route_datediff - (cal_driver_start_route_working_day_cum - cal_shipment_completion_working_day_cum))* 24) time_to_first_driver_start_route_hours
                from calendar_merge
                ),

                final as (
                select
                    order_id
                    , dest_hub_id
                    , dest_hub_region
                    , dest_hub_datetime
                    , first_driver_inbound_datetime
                    , last_driver_inbound_datetime
                    , earliest_pets_creation_datetime
                    , latest_pets_resolution_datetime
                    , first_driver_start_route_datetime
                    , resolution_time_hours
                    , last_driver_inbound_datediff
                    , cal_last_driver_inbound_working_day_cum
                    , cal_shipment_completion_working_day_cum
                    , (to_unix_timestamp(first_driver_inbound_datetime) - to_unix_timestamp(dest_hub_datetime))/3600 first_inbound_diff_raw
                    , if(resolution_time_hours is not null and date(earliest_pets_creation_datetime) < date(last_driver_inbound_datetime) and date(earliest_pets_creation_datetime) < date(first_driver_inbound_datetime),
                         time_to_first_driver_inbound_hours -resolution_time_hours, time_to_first_driver_inbound_hours) time_to_first_driver_inbound_hours
                    , if(resolution_time_hours is not null and date(earliest_pets_creation_datetime) < date(last_driver_inbound_datetime) and date(latest_pets_resolution_datetime) < date(last_driver_inbound_datetime),
                         time_to_last_driver_inbound_hours -resolution_time_hours, time_to_last_driver_inbound_hours) time_to_last_driver_inbound_hours
                    , if(resolution_time_hours is not null and date(earliest_pets_creation_datetime) < date(first_driver_start_route_datetime) and date(latest_pets_resolution_datetime) < date(first_driver_start_route_datetime),
                         time_to_first_driver_start_route_hours -resolution_time_hours, time_to_first_driver_start_route_hours) time_to_first_driver_start_route_hours
                    , system_id
                    , created_month
                from pets_resolution_time
                )

                select
                    order_id
                    , dest_hub_id
                    , dest_hub_region
                    , dest_hub_datetime
                    , first_driver_inbound_datetime
                    , last_driver_inbound_datetime
                    , earliest_pets_creation_datetime
                    , latest_pets_resolution_datetime
                    , first_driver_start_route_datetime
                    {% for column_name in columns_to_round %}
                    , round({{column_name}}, 2) as {{column_name}}
                    {% endfor %}
                    , system_id
                    , created_month
                from final
                """,
                jinja_arguments={
                    "timestamp_config": {
                        'shipment_completion': 'dest_hub_datetime'
                        , 'earliest_pets_creation': 'earliest_pets_creation_datetime'
                        , 'latest_pets_resolution': 'latest_pets_resolution_datetime'
                        , 'last_driver_inbound': 'last_driver_inbound_datetime'
                        , 'first_driver_inbound': 'first_driver_inbound_datetime'
                        , 'driver_start_route': 'first_driver_start_route_datetime'
                    },

                    'columns_to_round': [
                        'resolution_time_hours'
                        , 'first_inbound_diff_raw'
                        , 'time_to_first_driver_inbound_hours'
                        , 'time_to_last_driver_inbound_hours'
                        , 'time_to_first_driver_start_route_hours'
                    ]
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_PROCESSING_TIME,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
