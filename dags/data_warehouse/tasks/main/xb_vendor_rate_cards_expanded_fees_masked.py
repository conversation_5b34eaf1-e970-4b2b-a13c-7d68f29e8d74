import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_VENDOR_RATE_CARDS_EXPANDED_FEES_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_VENDOR_RATE_CARDS_EXPANDED_FEES_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).VENDOR_RATE_CARDS,
                view_name="vendor_rate_cards",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- The different type of costs are stored in an array of JSON with the Ranges Fees stored in another array of JSON
                -- Therefore to properly restructure them as rows, 2 explodes are needed
                with base as (

                    select
                        id
                        , created_at
                        , explode(from_json(get_json_object(rates, '$.fees'), 'array<string>')) as fee_group
                    from vendor_rate_cards

                )

                , exploded_fee_group as (

                    select
                        id
                        , created_at
                        , get_json_object(fee_group, '$.type') as type
                        , get_json_object(fee_group, '$.group') as group
                        , get_json_object(fee_group, '$.currency') as currency
                        , get_json_object(fee_group, '$.unit') as unit
                        , cast(get_json_object(fee_group, '$.value') as double) as value
                        , date(get_json_object(fee_group, '$.effective_date')) as effective_date_start
                        , from_json(get_json_object(fee_group, '$.ranges'), 'array<string>') as ranges_list
                    from base

                )

                -- This is to explode the Ranges Fees JSON
                , ranges_fee_base as (

                    select
                        id
                        , explode(ranges_list) as range
                    from exploded_fee_group
                    where type = 'RangesFees'

                )

                , exploded_ranges_fees as (

                    select
                        id
                        , cast(get_json_object(range, '$.from') as double) as ranges_fee_from
                        , cast(get_json_object(range, '$.to') as double) as ranges_fee_to
                        , cast(get_json_object(range, '$.value') as double) as ranges_fee_value
                    from ranges_fee_base

                )

                -- Merge the Ranges Fees back to the main table and create separate column for them 
                , combined as (

                    select
                        'gl' as system_id
                        , base.id as vendor_rate_card_id
                        , base.type
                        , base.group
                        , base.currency
                        , base.unit
                        , base.value
                        , base.effective_date_start
                        , ranges.ranges_fee_from
                        , ranges.ranges_fee_to
                        , ranges.ranges_fee_value
                        , date_format(base.created_at, 'yyyy-MM') as created_month
                    from exploded_fee_group as base
                    left join exploded_ranges_fees as ranges
                        on base.id = ranges.id
                        and base.type = 'RangesFees'

                )

                select * from combined
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_VENDOR_RATE_CARDS_EXPANDED_FEES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
