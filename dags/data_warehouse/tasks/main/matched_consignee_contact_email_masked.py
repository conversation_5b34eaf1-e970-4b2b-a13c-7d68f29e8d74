import sys

from pyspark.sql import SparkSession

from common.spark import util
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from datetime import datetime as dt
from pyspark.sql import functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import explode, col, lit, collect_set, array
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID
import pandas as pd

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FieldSalesDAG.Task.MATCHED_CONSIGNEE_CONTACT_EMAIL_MASKED + ".py",
    task_name=data_warehouse.FieldSalesDAG.Task.MATCHED_CONSIGNEE_CONTACT_EMAIL_MASKED,
    system_ids=(
        SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORIGINAL_CONSIGNEE_INFORMATION_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)
        
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORIGINAL_CONSIGNEE_INFORMATION,
                view_name="original_consignee_information",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    
    end_month = (measurement_datetime + relativedelta(months=-1)).strftime('%Y-%m')
    start_month = (measurement_datetime + relativedelta(months=-4)).strftime('%Y-%m')
    
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="matched_consignee_contact_email",
                jinja_template="""
                    select 
                        sa.id as shipper_id
                        , sa.sales_channel
                        , om.order_id
                        , om.tracking_id
                        , oci.original_to_email as buyers_email 
                        , oci.original_to_contact as buyers_contact
                        , oci.original_to_name as buyers_name
                        , om.rts_flag
                        , om.granular_status
                        , om.delivery_success_datetime
                        , date(date_trunc('month', om.delivery_success_datetime)) as delivery_success_month
                        , om.creation_datetime
                        , om.created_month
                        , '{{ system_id }}' as system_id
                    from order_milestones om
                    join shipper_attributes sa
                        on om.shipper_id = sa.id 
                    left join original_consignee_information oci
                        on om.order_id = oci.order_id
                    where 
                        om.granular_status in ('Completed', 'Returned to Sender')
                        and sa.sales_channel in ('Field Sales', 'Corp Sales')
                        and om.created_month >= '{{ start_month }}'
                        and om.created_month <= '{{ end_month }}'
                        and date(date_trunc('month', om.delivery_success_datetime)) between date('{{ start_month }}') + interval 1 month and date('{{ end_month }}')  
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "start_month": start_month,
                    "end_month": end_month,
                    "to_exclude": "'fake-email-provider.com','ninjavan.co','yopmail.com','mailnesia.com','pretreer.com','shopee.com','support.lazada.com'," \
                                  "'shein.com','emailexam.com','supperexpress.com','diginey.com','shopee68.tk','exelica.com','1secmail.com','triots.com'," \
                                  "'emailboxa.online','tiki.vn','yopmail.fr','mailna.co','yopmail.net','bbktorreurizar.com','zoodoo.site'"
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MATCHED_CONSIGNEE_CONTACT_EMAIL,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)
        
def null_email_dataframe(spark_df):
    """
    Get the distinct buyers_contact that have NULL buyers_email column 

    Input:
            spark dataframe that contains consignee details at an order level for the last 3 months
    Output:
            spark dataframe with buyers_contact column 
    """
    null_email = spark_df.filter((spark_df.buyers_email.isNull()) &
                                 (spark_df.buyers_contact.isNotNull())) \
                         .select('buyers_contact') \
                         .distinct()
    return null_email
    
def null_contact_dataframe(spark_df):
    """
    Get the distinct buyers_email that have NULL buyers_contact column

    Input:
            spark dataframe that contains consignee details at an order level for the last 3 months
    Output:
            spark dataframe with buyers_email column 
    """
    null_contact = spark_df.filter((spark_df.buyers_contact.isNull()) &
                                   (spark_df.buyers_email.isNotNull())) \
                           .select('buyers_email') \
                           .distinct()
    return null_contact

def process_spark_dataframe(spark_df):
    """
    Create a pandas dataframe that contains a set contact numbers that are associated with a unique consignee email

    Input:
            spark dataframe that contains consignee details at an order level for the last 3 months 
    Output:
            pandas dataframe with the following columns:
                "email"
                "system_id"
                "sales_channel"
                "contact"
    """
    spark_df_filtered = spark_df.withColumnRenamed("buyers_email", "email") \
                                .withColumnRenamed("buyers_contact", "contact") \
                                .withColumnRenamed("system_id", "system_id") \
                                .withColumnRenamed("sales_channel", "sales_channel")
    contact_to_emails = spark_df_filtered.groupBy("contact", "system_id", "sales_channel").agg(collect_set("email").alias("emails"))
    email_to_contacts = spark_df_filtered.groupBy("email", "system_id", "sales_channel").agg(collect_set("contact").alias("contacts"))
    exploded_contacts = contact_to_emails.withColumn("email", explode("emails"))
    exploded_emails = email_to_contacts.withColumn("dummy_contact", explode("contacts"))
    spark_joined_df = exploded_contacts.join(exploded_emails, "email").select(exploded_contacts.contact.alias("contact"), 
                                                                              exploded_contacts.email.alias("email"), 
                                                                              exploded_contacts.system_id.alias("system_id"), 
                                                                              exploded_contacts.sales_channel.alias("sales_channel"))
    contact_email_pairs = spark_joined_df.groupBy("contact", "system_id", "sales_channel").agg(collect_set("email").alias("emails"))
    email_contact_pairs = spark_joined_df.groupBy("email", "system_id", "sales_channel").agg(collect_set("contact").alias("contacts"))
    pandas_df = email_contact_pairs.toPandas()
    return pandas_df

def define_system_id(pandas_df):
    """
    Get system_id of consignees  

    Input:
            pandas dataframe that contains a system_id column
    Output:
            system_id (str)
    """
    array_system_id = pandas_df["system_id"].unique()
    system_id = array_system_id[0]
    return system_id

def update_dict(existing_dict, new_key, new_value):
    """
    Get all the linked emails and contact numbers of a consignee 

    Input:
            dictionary with distinct emails and associated contact number of a consignee
    Output:
            dictionary with all the linked emails and contact numbers of a consignee
    """
    # Convert the dictionary keys to tuples if they are not already tuples
    new_dict = dict()
    for key, value in existing_dict.items():
        if not isinstance(key, tuple):
            key = (key,)
        new_dict[key] = value
    
    for keys, values in new_dict.items():
        # Check for intersection between the 2 sets
        if new_value & values: 
            # If the value exists, append the new key to the existing keys
            updated_keys = keys + (new_key,)
            # If the value exists, merge the sets
            new_dict[updated_keys] = new_value | values
            # Remove the old key from the dictionary
            del new_dict[keys]
            break
    else:
        # If the value does not exist, add the new key-value pair
        new_dict[(new_key,)] = new_value
    
    return new_dict

def get_null_email_rows(spark_df, spark_df_merged):
    """
    Get the new rows of consignees whose buyers_contact do not exist in the linked emails and contact numbers dataframe

    Input:
            spark dataframe with distinct buyers_contact with NULL buyers_email
            spark dataframe that contains linked emails and contact numbers of a consignee
    Output:
            spark dataframe with NULL in the emails column and contact values that do not exist in the linked emails and
            contact numbers dataframe
    """
    null_email = null_email_dataframe(spark_df)
    exploded_contact_final = spark_df_merged.withColumn('exploded_contacts', explode(col('contacts')))
    new_contacts = null_email.join(exploded_contact_final, null_email.buyers_contact == exploded_contact_final.exploded_contacts, 'left_anti')
    new_contact_rows = new_contacts.withColumn('emails', lit(None)) \
                                   .withColumn('contacts', array(col('buyers_contact')))
    new_contact_rows = new_contact_rows.select('emails', 'contacts')
    return new_contact_rows
    
def get_null_contact_rows(spark_df, spark_df_merged):
    """
    Get the new rows of consignees whose buyers_email do not exist in the linked emails and contact numbers dataframe

    Input:
            spark dataframe with distinct buyers_email with NULL buyers_contact
            spark dataframe that contains linked emails and contact numbers of a consignee
    Output:
            spark dataframe with NULL in the contact column and email values that do not exist in the linked emails and
            contact numbers dataframe 
    """
    null_contact = null_contact_dataframe(spark_df)
    exploded_email_final = spark_df_merged.withColumn('exploded_emails', explode(col('emails')))
    new_emails = null_contact.join(exploded_email_final, null_contact.buyers_email == exploded_email_final.exploded_emails, 'left_anti')
    new_email_rows = new_emails.withColumn('emails', array(col('buyers_email'))) \
                               .withColumn('contacts', lit(None))
    new_email_rows = new_email_rows.select('emails', 'contacts')
    return new_email_rows

def add_buyer_id_and_system_id(pandas_df, spark_df):
    """
    Input:
            pandas dataframe to define system_id and spark dataframe to add the new columns into 
    Output:
            spark dataframe with additional columns named
                "buyer_id"
                "system_id"
    """
    system_id = define_system_id(pandas_df)
    spark_df = spark_df.withColumn('buyer_id', F.row_number().over(Window.orderBy(F.monotonically_increasing_id())))
    spark_df = spark_df.withColumn('system_id', lit(system_id))
    return spark_df
    
def run(spark, config):
    base.load_data(spark, config.input)
    spark_df = base.transform_data(spark, config.transform)
    pandas_df = process_spark_dataframe(spark_df)
    pandas_df["contacts"] = pandas_df["contacts"].apply(set)
    email_contact_dict = pandas_df.set_index('email')['contacts'].to_dict()
    existing_dict = dict()
    for email, contact in email_contact_dict.items():
        existing_dict = update_dict(existing_dict, email, contact)
    dict_with_lists = {key: list(value) for key, value in existing_dict.items()}
    final_pandas_df = pd.DataFrame(dict_with_lists.items(), columns=['emails', 'contacts'])
    final_pandas_df['emails'] = final_pandas_df['emails'].apply(list)
    spark_df_merged = spark.createDataFrame(final_pandas_df)
    new_contact_rows = get_null_email_rows(spark_df, spark_df_merged)
    new_email_rows = get_null_contact_rows(spark_df, spark_df_merged)
    final_spark_df = spark_df_merged.union(new_contact_rows).union(new_email_rows)
    final_spark_df = add_buyer_id_and_system_id(pandas_df, final_spark_df)
    base.write_data(final_spark_df, config.output, spark)
    return final_spark_df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()