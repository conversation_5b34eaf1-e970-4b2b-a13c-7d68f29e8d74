import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import delta_tables, data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.PARCEL_SCAN_FEATURES_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.PARCEL_SCAN_FEATURES_MASKED,
    depends_on=(data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_ORDERS_BASE_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.SortVendorProdGL(input_env, is_masked).PARCEL_IMAGES_V2,
                view_name="parcel_images_v2",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_BASE,
                view_name="lazada_orders_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="dws_scan",
                jinja_template="""

                select
                    lazada_orders_base.order_id
                    , max_by(
                        dim_weight_scans_base.new_weight, dim_weight_scans_base.scan_datetime
                    ) filter (where dim_weight_scans_base.device_type = 'DWS') as dws_weight
                    , count_if(dim_weight_scans_base.device_type = 'DWS') as dws_scan_cnt
                    , count_if(dim_weight_scans_base.device_type = 'MANUAL') as manual_scan_cnt
                from lazada_orders_base
                left join dim_weight_scans_base on
                    lazada_orders_base.order_id = dim_weight_scans_base.order_id
                group by 1

                """,
            ),
            base.TransformView(
                view_name="images",
                jinja_template="""

                select
                    lazada_orders_base.order_id
                    , lazada_orders_base.tracking_id
                    , count(parcel_images_v2.image_name) as inbound_image_cnt
                from lazada_orders_base
                left join parcel_images_v2 on
                    lazada_orders_base.tracking_id = parcel_images_v2.tracking_id
                group by 1,2
                
                """,
            ),
            base.TransformView(
                view_name="pre_final",
                jinja_template="""

                select
                    lazada_orders_base.order_id
                    , lazada_orders_base.tracking_id
                    , lazada_orders_base.creation_datetime
                    , lazada_orders_base.granular_status
                    , lazada_orders_base.original_weight
                    , dws_scan.dws_weight
                    , (
                        (dws_scan.dws_weight - lazada_orders_base.original_weight) / lazada_orders_base.original_weight
                    ) as pct_weight_variance
                    , dws_scan.dws_weight - lazada_orders_base.original_weight as abs_weight_variance 
                    , dws_scan.dws_scan_cnt
                    , dws_scan.manual_scan_cnt
                    , images.inbound_image_cnt
                    , if(dws_scan.dws_scan_cnt > 0, 1, 0) as dws_scan_flag
                    , if(dws_scan.manual_scan_cnt > 0, 1, 0) as manual_scan_flag
                    , if(images.inbound_image_cnt > 0, 1, 0) as inbound_image_flag
                    , lazada_orders_base.system_id
                    , lazada_orders_base.created_month
                from lazada_orders_base
                left join dws_scan on 
                    lazada_orders_base.order_id = dws_scan.order_id
                left join images on 
                    lazada_orders_base.order_id = images.order_id

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with 
                    weight_variance as (

                        select
                            order_id
                            , tracking_id
                            , creation_datetime
                            , granular_status
                            , original_weight
                            , dws_weight
                            , pct_weight_variance
                            , abs_weight_variance 
                            , if(
                                original_weight - dws_weight >= 0.5 and pct_weight_variance <= -0.9, 1, 0
                            ) as suspicious_weight_variance_flag
                            , case 
                                when pct_weight_variance <= -0.75 then "pct weight variance <= -0.75, "
                                when pct_weight_variance > -0.75 and pct_weight_variance < 0 then "pct weight variance between 0 to 0.75, "
                                else "pct weight variance >= 0, "
                            end as pct_weight_variance_band
                            , case 
                                when abs_weight_variance >= 0 then "abs weight variance >= 0"
                                when abs_weight_variance < 0 and abs_weight_variance > -1 then "abs weight variance between -1 to 0 (non inclusive)"
                                else "abs weight variance < -1"
                            end as abs_weight_variance_band
                            , dws_scan_cnt
                            , manual_scan_cnt
                            , inbound_image_cnt
                            , dws_scan_flag
                            , manual_scan_flag
                            , inbound_image_flag
                            , system_id
                            , created_month
                        from pre_final

                        )

                select 
                    order_id
                    , tracking_id
                    , creation_datetime
                    , granular_status
                    , original_weight
                    , dws_weight
                    , pct_weight_variance
                    , abs_weight_variance 
                    , suspicious_weight_variance_flag
                    , pct_weight_variance_band
                    , abs_weight_variance_band
                    , concat(pct_weight_variance_band, abs_weight_variance_band) as weight_variance_crossed
                    , dws_scan_cnt
                    , manual_scan_cnt
                    , inbound_image_cnt
                    , dws_scan_flag
                    , manual_scan_flag
                    , inbound_image_flag
                    , system_id
                    , created_month
                from weight_variance

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARCEL_SCAN_FEATURES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()