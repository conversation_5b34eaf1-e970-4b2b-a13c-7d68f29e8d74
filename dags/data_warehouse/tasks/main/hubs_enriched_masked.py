import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
                           base.HiveMetastoreTaskConfig(hive_schema="pricing"),),

)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).DUMMY_HUB_MAP, view_name="dummy_hub_map"),
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).HUBS, view_name="hubs"),
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).REGIONS, view_name="regions"),
            base.InputTable(
                path=delta_tables.GDrive(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched_gdrive",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    cast(hubs.hub_id as bigint) as id
                    , hubs.system_id as country
                    , hubs.name
                    , hubs.short_name
                    , hubs.country as address_country
                    , hubs.city as address_city
                    , hubs.latitude
                    , hubs.longitude
                    , hubs.facility_type
                    , regions.name as region
                    , hubs.area
                    , cast(coalesce(hubs.sort_hub, 0) as bigint) as sort_hub_flag
                    , cast(if(virtual.dummy_hub_id is not null, 1, 0) as bigint) as virtual_hub_flag
                    , cast(parent_hub.hub_id as bigint) as parent_hub_id
                    , parent_hub.name as parent_hub_name

                    -- manual update columns
                    , cast(gdrive.is_delivery_hub as bigint) as is_delivery_hub
                    , cast(gdrive.is_sea_haul_hub as bigint) as is_sea_haul_hub
                    , cast(gdrive.consol_hub as bigint) as consol_hub
                    -- manual update columns

                    -- auto update columns

                    , case
                        when (
                            hubs.system_id = 'vn'
                            and hubs.short_name in ('{{ vn_central_region_provinces | join("','") }}')
                        )
                            then 'Central'
                        else regions.name
                    end as sla_region
                    -- auto update columns

                    , from_utc_timestamp(hubs.created_at, {{ get_local_timezone }}) as creation_datetime
                    , from_utc_timestamp(hubs.deleted_at, {{ get_local_timezone }}) as deletion_datetime
                    , cast(if(hubs.deleted_at is null, 0, 1) as bigint) as is_deleted
                    , hubs.system_id
                    , date_format(hubs.created_at, 'yyyy-MM') as created_month
                from hubs
                left join hubs_enriched_gdrive as gdrive on
                    hubs.hub_id = gdrive.id
                    and hubs.system_id = gdrive.system_id
                left join dummy_hub_map as virtual on
                    hubs.hub_id = virtual.dummy_hub_id
                    and hubs.system_id = virtual.system_id
                left join hubs as parent_hub on
                    virtual.real_hub_id = parent_hub.hub_id
                    and virtual.system_id = parent_hub.system_id
                left join regions on
                    hubs.region_id = regions.id
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("hubs.system_id"),
                    "vn_central_region_provinces": {
                        "Đà Nẵng",
                        "Quảng Nam",
                        "Thừa Thiên Huế",
                        "Quảng Ngãi",
                        "Quảng Bình",
                        "Quảng Trị",
                    },
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HUBS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=(
            "system_id",
            "created_month",
        ),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
