import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.EXCLUSION_REQUESTS_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.EXCLUSION_REQUESTS_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.GDrive(input_env).EXCLUSION_REQUESTS, view_name="exclusion_requests_gdrive"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    request_id
                    , creation_datetime
                    , type
                    , cast(request_reference as bigint) as request_reference
                    , category
                    , effective_start
                    , effective_end
                    , impact_creation_flag
                    , impact_closure_flag
                    , reason
                    , evidence
                    , requestor
                    , country_approval_date
                    , region_approval_date
                    , completion_date
                    , status
                    , system_id
                    , date_format(creation_datetime, 'yyyy-MM') as created_month
                from exclusion_requests_gdrive
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).EXCLUSION_REQUESTS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
