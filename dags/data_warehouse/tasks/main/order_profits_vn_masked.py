import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.ORDER_PROFITS_VN_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.ORDER_PROFITS_VN_MASKED,
    system_ids=(SystemID.VN,),
    depends_on=(
        data_warehouse.CostCardDAG.Task.COST_CARD_INTERMEDIATE_MASKED,
        data_warehouse.CostCardDAG.Task.PRICED_ORDERS_INTERMEDIATE_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).COST_CARD_INTERMEDIATE,
                view_name="cost_card_intermediate",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PRICED_ORDERS_INTERMEDIATE,
                view_name="priced_orders_intermediate",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COST_CARD_SHOPEE_DISCOUNT_CONFIG_VN,
                view_name="cost_card_shopee_discount_config_vn",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                -- Consolidate columns from cost_card_intermediate, priced_orders_intermediate and ttr tables

                select
                    cc.order_id
                    , cc.tracking_id
                    , cc.creation_datetime
                    , cc.created_month
                    , cc.terminal_datetime
                    , date_trunc('month', cc.terminal_datetime) as terminal_month
                    , cc.terminal_datetime_type
                    , cc.system_id
                    , cc.granular_status

                    -- Shipper information
                    , po.shipper_id
                    , po.shipper_name
                    , po.sales_channel
                    , po.shipper_parent_id_coalesce
                    , po.shipper_parent_name_coalesce
                    , po.sf_acc_id
                    , po.sf_parent_acc_id_coalesce
                    , po.sf_parent_acc_name_coalesce
                    , po.sf_parent_acc_shipper_id_coalesce
                    , po.sf_sales_territory
                    , po.sf_sales_team
                    , po.sf_nv_product_line

                    , cc.delivery_type
                    , cc.om_size
                    , cc.om_width
                    , cc.om_height
                    , cc.om_length
                    , cc.om_cubic_measure
                    , cc.om_weight
                    , cc.om_delivery_fee
                    , cc.third_party_flag
                    , cc.force_success_flag
                    , cc.airhaul_flag
                    , cc.seahaul_flag
                    , cc.direct_dispatch_flag
                    , po.rts_flag
                    , po.first_mile_type
                    , po.service_level
                    , po.service_type

                    -- Timestamp information
                    , po.pickup_datetime
                    , po.nv_pickup_datetime
                    , po.inbound_datetime
                    , po.delivery_success_datetime

                    -- Cost information
                    , cc.fm_cost
                    , cc.sort_cost
                    , cc.mm_cost
                    , cc.lm_cost
                    , cc.hub_cost
                    , cc.total_ops_cost
                    , cc.total_raw_events
                    , cc.fm_non_null_count
                    , cc.sort_non_null_count
                    , cc.mm_non_null_count
                    , cc.lm_non_null_count
                    , cc.hub_non_null_count
                    , cc.total_non_null_count
                    , cc.fm_null_count
                    , cc.sort_null_count
                    , cc.mm_null_count
                    , cc.lm_null_count
                    , cc.hub_null_count
                    , cc.null_cost_count

                    , cc.mm_intersort_cost
                    , cc.mm_pri_land_cost
                    , cc.mm_pri_air_cost
                    , cc.mm_pri_sea_cost
                    , cc.mm_sec_cost
                    , cc.mm_dd_cost
                    , cc.mm_intersort_non_null_count
                    , cc.mm_pri_land_non_null_count
                    , cc.mm_pri_air_non_null_count
                    , cc.mm_pri_sea_non_null_count
                    , cc.mm_sec_non_null_count
                    , cc.mm_dd_non_null_count
                    , cc.mm_intersort_null_count
                    , cc.mm_pri_land_null_count
                    , cc.mm_pri_air_null_count
                    , cc.mm_pri_sea_null_count
                    , cc.mm_sec_null_count
                    , cc.mm_dd_null_count

                    -- Pricing information
                    , po.billing_delivery_fee
                    , po.cod_value
                    , po.cod_collected
                    , po.script_id
                    , po.pricing_breakdown
                    , po.pricer_rate_card_id
                    , po.pricing_profile_id
                    , po.billing_size
                    , po.billing_weight
                    , po.billing_weight_source
                    , po.pricing_billable_weight
                    , po.pricing_from_billing_zone
                    , po.pricing_from_region
                    , po.pricing_from_city
                    , po.pricing_to_billing_zone
                    , po.pricing_to_region
                    , po.pricing_to_city
                    , po.cod_fee
                    , po.rts_fee
                    , po.flat_discount
                    , po.insurance_fee
                    , po.handling_fee
                    , po.pricing_created_at                        

                    -- OD hub information from TTR
                    , ttr.origin_hub_id
                    , ttr.origin_hub_address_city as origin_hub_city
                    , ttr.origin_hub_region
                    , ttr.dest_hub_id
                    , ttr.dest_hub_address_city as dest_hub_city
                    , ttr.dest_hub_region

                    -- FM information
                    , cc.fm_waypoint_id
                    , cc.fm_pudo_id

                    -- Utilization
                    , cc.mm_sum_utilization_numerator
                    , cc.mm_sum_utilization_denominator
                    , cc.mm_avg_utilization_rate

                    -- Country specific columns
                    , case
                        -- Shopee Shipper
                        when 
                            po.shipper_parent_id_coalesce in (702985, 6778709)
                            and po.pricing_from_region = po.pricing_to_region
                            and po.pricing_from_city = po.pricing_to_city
                        then 'Intra Province'

                        when 
                            po.shipper_parent_id_coalesce in (702985, 6778709)
                            and po.pricing_from_region = po.pricing_to_region
                            and po.pricing_from_city != po.pricing_to_city
                        then 'Same Region'

                        when 
                            po.shipper_parent_id_coalesce in (702985, 6778709)
                            and po.pricing_from_city in ('HCM', 'HN', 'DNG')
                            and po.pricing_to_city in ('HCM', 'HN', 'DNG')
                            and po.pricing_from_city != po.pricing_to_city
                        then 'Special Cross Region'

                        when 
                            po.shipper_parent_id_coalesce in (702985, 6778709)
                            and po.pricing_from_region != po.pricing_to_region
                            and not (
                                po.pricing_from_city in ('HCM', 'HN', 'DNG')
                                and po.pricing_to_city in ('HCM', 'HN', 'DNG')
                                and po.pricing_from_city != po.pricing_to_city
                            )
                        then 'Cross Region'

                        -- Special logic for big cities (HCM and HN only)
                        when 
                            po.pricing_from_region = 'S'
                            and po.pricing_from_city = 'HCM'
                            and po.pricing_to_region = 'N'
                            and po.pricing_to_city = 'HN'
                        then 'Intercity'

                        when 
                            po.pricing_from_region = 'N'
                            and po.pricing_from_city = 'HN'
                            and po.pricing_to_region = 'S'
                            and po.pricing_to_city = 'HCM'
                        then 'Intercity'

                        -- All other cases
                        when 
                            po.pricing_from_region != po.pricing_to_region
                        then 'Interzone'

                        when 
                            po.pricing_from_region = po.pricing_to_region
                            and po.pricing_from_city = po.pricing_to_city
                        then 'Intracity'

                        when 
                            po.pricing_from_region = po.pricing_to_region
                            and po.pricing_from_city != po.pricing_to_city
                        then 'Intrazone'

                        else null
                    end as pricing_route
                    , count(*)

                from cost_card_intermediate as cc
                left join priced_orders_intermediate as po 
                    on cc.order_id = po.order_id
                left join transit_time_report as ttr 
                    on cc.order_id = ttr.order_id
                group by {{ range(1, 115) | join(',') }}
                """,
            ),

            base.TransformView(
                view_name="shipper_volume_discounting",
                jinja_template="""
                -- Calculate Shopee volume and apply discount accordingly
                -- Shipper ID = 702985: Shopee Vietnam, Shipper ID = 6778709: Shopee Return Vietnam

                with 
                    shipper_volumes as (

                        select
                            terminal_month
                            , shipper_parent_id_coalesce
                            , count(distinct order_id) as monthly_volume
                        from base
                        where 
                            terminal_datetime is not null
                            and granular_status != 'Cancelled'
                        group by {{ range(1, 3) | join(',') }}

                )

                select
                    base.*
                    , base.billing_delivery_fee * cost_card_shopee_discount_config_vn.discount as volume_discount
                from base
                left join shipper_volumes 
                    on base.terminal_month = shipper_volumes.terminal_month
                    and base.shipper_parent_id_coalesce = shipper_volumes.shipper_parent_id_coalesce
                left join cost_card_shopee_discount_config_vn
                    on base.shipper_parent_id_coalesce in (702985, 6778709)
                    and date(base.terminal_datetime) >= date(cost_card_shopee_discount_config_vn.start_date)
                    and date(base.terminal_datetime) <= date(cost_card_shopee_discount_config_vn.end_date)
                    and base.pricing_route = cost_card_shopee_discount_config_vn.route
                    and shipper_volumes.monthly_volume >= cast(cost_card_shopee_discount_config_vn.min_volume_threshold as float)
                    and shipper_volumes.monthly_volume <= cast(cost_card_shopee_discount_config_vn.max_volume_threshold as float)

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- Cleanup and column mapping for consistent display across countries 
                with 
                    dashboard_base as (

                        select
                            *
                            , case
                                when shipper_parent_id_coalesce in (702985, 6778709)
                                then billing_delivery_fee - volume_discount
                                else billing_delivery_fee
                            end as dashboard_fee
                            , origin_hub_region as dashboard_origin_region
                            , dest_hub_region as dashboard_dest_region
                        from shipper_volume_discounting

                    )
                    , dashboard_flag as (

                        select
                            *
                            , case 
                                when 
                                    om_weight <= 200 
                                    and total_ops_cost is not null 
                                    and billing_delivery_fee is not null
                                    and dashboard_fee <= 1000000
                                then 1
                                else 0 
                            end as dashboard_flag
                        from dashboard_base

                    )
                    , final as (

                        select distinct
                            order_id
                            , tracking_id
                            , creation_datetime
                            , created_month
                            , terminal_datetime
                            , terminal_month
                            , terminal_datetime_type
                            , system_id
                            , granular_status

                            -- Shipper information
                            , shipper_id
                            , shipper_name
                            , sales_channel
                            , shipper_parent_id_coalesce
                            , shipper_parent_name_coalesce
                            , sf_acc_id
                            , sf_parent_acc_id_coalesce
                            , sf_parent_acc_name_coalesce
                            , sf_parent_acc_shipper_id_coalesce
                            , sf_sales_territory
                            , sf_sales_team
                            , sf_nv_product_line

                            , delivery_type
                            , om_size
                            , om_width
                            , om_height
                            , om_length
                            , om_cubic_measure
                            , om_weight
                            , om_delivery_fee
                            , third_party_flag
                            , force_success_flag
                            , airhaul_flag
                            , seahaul_flag
                            , direct_dispatch_flag
                            , rts_flag
                            , first_mile_type
                            , service_level
                            , service_type

                            -- Timestamp information
                            , pickup_datetime
                            , nv_pickup_datetime
                            , inbound_datetime
                            , delivery_success_datetime

                            -- Cost information
                            , fm_cost
                            , sort_cost
                            , mm_cost
                            , lm_cost
                            , hub_cost
                            , total_ops_cost
                            , total_raw_events
                            , fm_non_null_count
                            , sort_non_null_count
                            , mm_non_null_count
                            , lm_non_null_count
                            , hub_non_null_count
                            , total_non_null_count
                            , fm_null_count
                            , sort_null_count
                            , mm_null_count
                            , lm_null_count
                            , hub_null_count
                            , null_cost_count
                            , mm_intersort_cost
                            , mm_pri_land_cost
                            , mm_pri_air_cost
                            , mm_pri_sea_cost
                            , mm_sec_cost
                            , mm_dd_cost
                            , mm_intersort_non_null_count
                            , mm_pri_land_non_null_count
                            , mm_pri_air_non_null_count
                            , mm_pri_sea_non_null_count
                            , mm_sec_non_null_count
                            , mm_dd_non_null_count
                            , mm_intersort_null_count
                            , mm_pri_land_null_count
                            , mm_pri_air_null_count
                            , mm_pri_sea_null_count
                            , mm_sec_null_count
                            , mm_dd_null_count

                            -- Pricing information
                            , billing_delivery_fee
                            , cod_value
                            , cod_collected
                            , script_id
                            , pricing_breakdown
                            , pricer_rate_card_id
                            , pricing_profile_id
                            , billing_size
                            , billing_weight
                            , billing_weight_source
                            , pricing_billable_weight
                            , pricing_from_billing_zone
                            , pricing_from_region
                            , pricing_from_city
                            , pricing_to_billing_zone
                            , pricing_to_region
                            , pricing_to_city
                            , cod_fee
                            , rts_fee
                            , flat_discount
                            , insurance_fee
                            , handling_fee
                            , pricing_created_at  

                            -- Calculated revenue information
                            , 'not_applicable' as calculated_delivery_fee_with_tax
                            , 'not_applicable' as calculated_delivery_fee_without_tax
                            , volume_discount                      

                            -- OD hub information from TTR
                            , origin_hub_id
                            , origin_hub_city
                            , origin_hub_region
                            , dest_hub_id
                            , dest_hub_city
                            , dest_hub_region

                            -- FM information
                            , fm_waypoint_id
                            , fm_pudo_id

                            -- Utilization
                            , mm_sum_utilization_numerator
                            , mm_sum_utilization_denominator
                            , mm_avg_utilization_rate

                            -- Standardized display columns
                            , dashboard_fee
                            , dashboard_origin_region
                            , dashboard_dest_region
                            , dashboard_flag

                            -- Country specific columns
                            , pricing_route
                        from dashboard_flag   

                    )

                    select * from final
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_PROFITS_VN,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()