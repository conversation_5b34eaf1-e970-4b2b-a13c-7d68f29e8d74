import sys

from pyspark.sql import SparkSession

from common.date import Timezone
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.JiraDAG.Task.JIRA_SPRINTS_MASKED + ".py",
    task_name=data_warehouse.JiraDAG.Task.JIRA_SPRINTS_MASKED,
    system_ids=(constants.SystemID.SG,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    is_masked = True
    input_env = "prod"

    input_config = base.InputConfig(
        delta_tables=(base.InputTable(path=delta_tables.Jira(input_env, is_masked).ISSUES, view_name="issues"),),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="sprints",
                jinja_template="""
                    with
                        sprints_base as (

                            -- unpack sprints JSON array
                            select
                                updated as updated_datetime_utc
                                , explode(
                                    from_json(get_json_object(fields, '$.customfield_10004'), 'array<string>')
                                ) as sprint_string
                            from issues

                        ), sprints_parsed as (

                            -- extract sprint details with regex
                            select
                                updated_datetime_utc
                                , regexp_extract(sprint_string, '(?<=(id=))(.*)(?=(,rapidViewId=))', 2) as id
                                , regexp_extract(sprint_string, '(?<=(name=))(.*)(?=(,startDate=))', 2) as name
                                , regexp_extract(sprint_string, '(?<=(state=))(.*)(?=(,name=))', 2) as state
                                , regexp_extract(
                                    sprint_string, '(?<=(startDate=))(.*)(?=(,endDate=))', 2
                                ) as start_datetime_utc
                                , regexp_extract(
                                    sprint_string, '(?<=(endDate=))(.*)(?=(,completeDate=))', 2
                                ) as end_datetime_utc
                                , regexp_extract(
                                    sprint_string, '(?<=(completeDate=))(.*?)(?=(Z,))', 2
                                ) as complete_datetime_utc
                                , regexp_extract(
                                    sprint_string, '(?<=(activatedDate=))(.*)(?=(,sequence=))', 2
                                ) as activated_datetime_utc
                                -- TODO: add project
                                , regexp_extract(
                                    sprint_string, '(?<=(goal=))((.|\n)*?)(?=((,autoStartStop=)|(\\])))', 2
                                ) as goal
                            from sprints_base

                        ), final as (

                            -- get latest sprint details by issue updated datetime
                            select
                                cast(id as bigint) as id
                                , max_by(name, updated_datetime_utc) as name
                                , max_by(state, updated_datetime_utc) as state
                                , from_utc_timestamp(
                                    max_by(cast(start_datetime_utc as timestamp), updated_datetime_utc),
                                    '{{ local_timezone }}'
                                ) as start_datetime
                                , from_utc_timestamp(
                                    max_by(cast(end_datetime_utc as timestamp), updated_datetime_utc),
                                    '{{ local_timezone }}'
                                ) as end_datetime
                                , from_utc_timestamp(
                                    max_by(cast(complete_datetime_utc as timestamp), updated_datetime_utc),
                                    '{{ local_timezone }}'
                                ) as complete_datetime
                                , from_utc_timestamp(
                                    max_by(cast(activated_datetime_utc as timestamp), updated_datetime_utc),
                                    '{{ local_timezone }}'
                                ) as activated_datetime
                                , max_by(goal, updated_datetime_utc) as goal
                            from sprints_parsed
                            group by 1

                        )

                    select
                        *
                    from final
                """,
                jinja_arguments={
                    "local_timezone": Timezone.SG,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_SPRINTS,
        measurement_datetime=measurement_datetime,
        system_id=constants.SystemID.SG,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.cz.accelerate.mode.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()