import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_LEADS_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_LEADS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_LEAD_ENRICHED,
                view_name="salesforce_lead_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_OPPORTUNITY_ENRICHED,
                view_name="salesforce_opportunity_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).NINJA_BUDDIES_SALES_TERRITORY_GROUPING,
                view_name="ninja_buddies_sales_territory_grouping",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).NB_LEADS_NOT_IN_SF,
                view_name="leads_not_in_sf",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="buddies_leads",
                jinja_template="""
                (
                    SELECT
                        lead.id
                        , lead.name
                        , lead.status
                        , lead.user_id
                        , lead.opportunity_id
                        , acc.id as account_id
                        , acc.name as account_name
                        , lead.lead_source
                        , lead.lead_source_details
                        , lead.lead_gen_channel
                        , lead.conversion_date
                        , lead.creation_datetime
                        , lead.status
                        , lead.prospect_qualified_date
                        , lead.disqualification_reason
                        , lead.country
                        , lead.system_id
                        , lead.record_type
                        , lead.created_month
                        , coalesce(acc.referrer_global_id, lead.referrer_global_id) as buddy_shipper_id
                        , acc.shipper_id
                        , acc.sales_territory
                        , lead.buyapowa_code
                        , lead.created_by_id
                    FROM salesforce_lead_enriched lead
                    left join salesforce_opportunity_enriched opp
                        on lead.opportunity_id = opp.id
                    left join salesforce_account_enriched acc
                        on opp.account_id = acc.id
                    WHERE
                        -- Filter for Ninja Buddies leads
                        lead_gen_channel = 'Shipper Referral'
                        and lead_source = 'Ninja Buddies'
                        and coalesce(acc.referrer_global_id, lead.referrer_global_id) is not null
                )

                UNION ALL

                -- Include leads outside Salesforce
                (
                    SELECT
                        leads_not_in_sf.id
                        , leads_not_in_sf.name
                        , leads_not_in_sf.status
                        , leads_not_in_sf.user_id
                        , leads_not_in_sf.opportunity_id
                        , acc.id as account_id
                        , acc.name as account_name
                        , lead_source
                        , lead_source_details
                        , lead_gen_channel
                        , cast(conversion_date as date) as conversion_date
                        , cast(leads_not_in_sf.creation_datetime as date) as creation_datetime
                        , case when leads_not_in_sf.status = 'Qualified' then 'Converted' else status end as status
                        , cast(prospect_qualified_date as date) as prospect_qualified_date
                        , disqualification_reason
                        , leads_not_in_sf.country
                        , leads_not_in_sf.system_id
                        , case when leads_not_in_sf.system_id = 'my' then 'Malaysia' else null end as record_type
                        , leads_not_in_sf.created_month
                        , buddy_shipper_id
                        , acc.shipper_id
                        , acc.sales_territory
                        , null as buyapowa_code
                        , null as created_by_id
                    FROM leads_not_in_sf
                    left join salesforce_opportunity_enriched opp
                        on leads_not_in_sf.opportunity_id = opp.id
                    left join salesforce_account_enriched acc
                        on opp.account_id = acc.id
                )
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="friend_m0_m3_volume",
                jinja_template="""
                    with shipper_completion_vol_monthly_enhanced as (

                        select distinct
                            shipper_id
                            , completion_month
                            , first_order_completion_date
                            --
                            , case
                                when (
                                        (
                                            system_id != 'vn'
                                            and first_order_completion_month >= date('2022-07-01')
                                            and day(first_order_completion_date) = 1
                                        )

                                        or

                                        (
                                            system_id = 'vn'
                                            and first_order_completion_month >= date('2022-05-17')
                                            and day(first_order_completion_date) = 1
                                        )

                                    )
                                then months_between(completion_month,first_order_completion_month) + 1
                                else months_between(completion_month,first_order_completion_month)
                            end as month
                            , total_orders
                            , shipper_first_30d_ppd
                        from shipper_completion_vol_monthly

                    )

                select
                    shipper_id
                    , first_order_completion_date
                    , shipper_first_30d_ppd
                    , sum(total_orders) filter (where month = 0) as m0_orders
                    , sum(total_orders) filter (where month = 1) as m1_orders
                    , sum(total_orders) filter (where month = 2) as m2_orders
                    , sum(total_orders) filter (where month = 3) as m3_orders
                    , sum(total_orders) as lifetime_orders
                from shipper_completion_vol_monthly_enhanced
                group by 1,2,3
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="friend_60d_volume",
                jinja_template="""
                select distinct
                    se.id as shipper_id
                    , se.system_id
                    , sum(vol.total_orders) as first_60d_orders
                from shippers_enriched se
                left join shipper_completion_vol_daily vol
                    on se.id = vol.shipper_id
                where
                    completion_date between se.first_order_completion_date
                    and (se.first_order_completion_date + interval '60' day)
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="final_table",
                jinja_template="""
                select distinct
                    -- Buddy's profile
                    buddies_se.id as buddy_shipper_id
                    , buddies_se.sf_acc_id as buddy_sf_acc_id
                    , buddies_se.shipper_name as buddy_shipper_name
                    , buddies_se.sf_sales_territory as buddy_sales_territory
                    , ifnull(buddy_st.grouping, buddies_se.sf_sales_territory) as buddy_sales_territory_grouped
                    , buddies_se.sales_person_code as buddy_sp_code
                    , min(
                        date(date_trunc('month',leads.creation_datetime))
                            ) over (partition by buddy_shipper_id, if(month(leads.creation_datetime) <= 6
                                    , year(leads.creation_datetime), year(leads.creation_datetime) + 1) 
                                    order by leads.creation_datetime asc) as first_referral_month

                    -- Lead information
                    , if(
                        month(leads.creation_datetime) <= 6
                        , year(leads.creation_datetime)
                        , year(leads.creation_datetime) + 1
                    ) as financial_year
                    , leads.id as lead_id
                    , leads.name as lead_name
                    , leads.creation_datetime as lead_creation_datetime
                    , leads.lead_gen_channel
                    , leads.lead_source
                    , leads.lead_source_details
                    , leads.status as lead_status
                    , leads.disqualification_reason
                    , leads.prospect_qualified_date
                    , case when
                        leads.prospect_qualified_date is null
                            and status in ('Prospect Qualified','Qualified','Converted')
                        then leads.conversion_date
                        else leads.prospect_qualified_date
                    end as prospect_qualified_date_coalesce
                    , leads.opportunity_id
                    , leads.conversion_date
                    , leads.buyapowa_code
                    , leads.created_by_id as created_by_id
                    , friend_created_by.name as created_by_name

                    -- Friend's profile
                    , leads.shipper_id as friend_shipper_id
                    , leads.account_id as friend_sf_acc_id
                    , friends_se.sf_parent_acc_id_coalesce as friend_sf_parent_acc_id_coalesce
                    , leads.account_name as friend_shipper_name
                    , leads.sales_territory as friend_sales_territory
                    , ifnull(friend_st.grouping, leads.sales_territory) as friend_sales_territory_grouped
                    , case when leads.system_id = 'ph' and leads.sales_territory is not null
                        then case
                            when leads.sales_territory in ('MM','GMA')
                                then 'METRO MANILA/GMA'
                            when leads.sales_territory in ('North Luzon','Central Luzon','South Luzon')
                                then 'PROVINCIAL'
                            else null end
                        else null
                    end as friend_sales_area
                    , friend_sf_user_enriched.alias as friend_sp_code
                    , friend_sf_user_enriched.sales_team as friend_sp_sales_team
                    , friends_se.prepaid_account
                    , friends_se.sales_channel as friend_sf_sales_channel
                    , friends_se.onboarded_date
                    , friends_se.first_order_completion_date
                    , datediff(
                        coalesce(prospect_qualified_date,conversion_date,NULL),leads.creation_datetime
                    ) as lead_to_qlead_days
                    , datediff(
                        leads.conversion_date,coalesce(prospect_qualified_date,conversion_date,NULL)
                    ) as qlead_to_opp_days
                    , datediff(friends_se.onboarded_date,leads.conversion_date) as opp_to_onboarded_days
                    , datediff(
                        friends_se.first_order_completion_date,friends_se.onboarded_date
                    ) as onboarded_to_activated_days
                    , datediff(
                        friends_se.first_order_completion_date,leads.creation_datetime
                    ) as first_contact_to_activated_days
                    , shipper_first_30d_ppd as friend_first_30d_ppd

                    -- Friend's volume
                    , friend_m0_m3_volume.m0_orders
                    , friend_m0_m3_volume.m1_orders
                    , friend_m0_m3_volume.m2_orders
                    , friend_m0_m3_volume.m3_orders
                    , friend_60d_volume.first_60d_orders
                    , friend_m0_m3_volume.lifetime_orders

                    -- Get first 2mos/3mos total or avg orders (calculation differ across countries and time period)

                        -- For shippers from VN
                            -- First completed from 17 May 2022 + occurs on the first day of the month,
                            -- sum up orders from M1 to M3.
                            -- Otherwise, take the sum of orders from M0 to M3.
                        -- For shippers from other countries
                            -- First completed from FY23 onwards + occurs on the first day of the month,
                            -- sum up orders from M1 to M3.
                            -- Otherwise, take the sum of orders from M0 to M3.

                    , case when
                                (
                                    (
                                        leads.system_id != 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-07-01')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                    or

                                    (
                                        leads.system_id = 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-05-17')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                )
                        then coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) +
                                coalesce(friend_m0_m3_volume.m3_orders,0)
                        else coalesce(friend_m0_m3_volume.m0_orders,0) +
                                coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) +
                                coalesce(friend_m0_m3_volume.m3_orders,0)
                    end as first_3mos_total_orders

                    , case when
                                (
                                    (
                                        leads.system_id != 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-07-01')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                    or

                                    (
                                        leads.system_id = 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-05-17')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                )
                        then ( coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) +
                                coalesce(friend_m0_m3_volume.m3_orders,0) ) / 3
                        else ( coalesce(friend_m0_m3_volume.m0_orders,0) +
                                coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) +
                                coalesce(friend_m0_m3_volume.m3_orders,0) ) / 3
                    end as first_3mos_avg_orders

                    , case when
                                (
                                    (
                                        leads.system_id != 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-07-01')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                    or

                                    (
                                        leads.system_id = 'vn'
                                        and friends_se.first_order_completion_date >= date('2022-05-17')
                                        and day(friends_se.first_order_completion_date) = 1
                                    )

                                )
                        then ( coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) ) / 2
                        else ( coalesce(friend_m0_m3_volume.m0_orders,0) +
                                coalesce(friend_m0_m3_volume.m1_orders,0) +
                                coalesce(friend_m0_m3_volume.m2_orders,0) ) / 2
                    end as first_2mos_avg_orders

                    -- Partition
                    , leads.system_id as system_id
                    , leads.created_month as created_month

                -- lead
                from buddies_leads leads

                -- buddy's shippers enriched
                left join shippers_enriched buddies_se
                    on leads.buddy_shipper_id = buddies_se.id

                -- buddy's sales territory grouping
                left join ninja_buddies_sales_territory_grouping buddy_st
                    on buddies_se.sf_sales_territory = buddy_st.buddy_sales_territory
                    and leads.system_id = buddy_st.system_id

                -- friend's salesperson information
                left join salesforce_user_enriched friend_sf_user_enriched
                    ON leads.user_id = friend_sf_user_enriched.id

                -- friend's shippers enriched
                left join shippers_enriched friends_se
                    ON leads.shipper_id = friends_se.id

                -- friend's M0-M3 monthly order vol
                left join friend_m0_m3_volume
                    ON leads.shipper_id = friend_m0_m3_volume.shipper_id

                -- friend's first 60 days monthly order vol
                left join friend_60d_volume
                    ON leads.shipper_id = friend_60d_volume.shipper_id

                -- friend's sales territory grouping
                left join ninja_buddies_sales_territory_grouping friend_st
                    on leads.sales_territory = friend_st.buddy_sales_territory
                    and leads.system_id = friend_st.system_id

                -- friend created by name
                left join salesforce_user_enriched friend_created_by
                    on leads.created_by_id = friend_created_by.id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    select * from final_table
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_BUDDIES_LEADS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()