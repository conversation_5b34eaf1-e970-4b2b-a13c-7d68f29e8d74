import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_TERMINAL_STATUS_REPORT_DAILY_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_TERMINAL_STATUS_REPORT_DAILY_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.CISP_TERMINAL_STATUS_REPORT_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 5)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CISP_TERMINAL_STATUS_REPORT,
                view_name="cisp_terminal_status_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="cisp_report",
                jinja_template="""
                    select
                        order_id
                        , dest_hub_id
                        , dest_hub_name
                        , dest_hub_region
                        , dest_zone
                        , dest_hub_facility_type
                        , dest_hub_datetime
                        , last_mile_start_clock_date
                        , start_clock_date
                        , working_day
                        , next_working_date
                        , last_valid_delivery_attempt_datetime
                        , last_valid_delivery_attempt_status
                        , rts_trigger_datetime
                        {%- for day in range(1, 8) %}

                        , n{{ day }}_cutoff_date

                        {%- endfor %}

                        {%- for day in range(1, 8) %}

                        , max(n{{ day }}_met_flag) as n{{ day }}_met_flag

                        {%- endfor %}

                    from cisp_terminal_status_report
                    group by {{ range(1, 22) | join(',') }}

                    """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select

                            {% if system_id == 'id' %}

                            if(hubs.is_sea_haul_hub = 1, base.n7_cutoff_date, base.n6_cutoff_date) as report_date
                            , if(hubs.is_sea_haul_hub = 1, 'D7', 'D6') as cutoff_type
                            , if(hubs.is_sea_haul_hub = 1, base.n7_met_flag, base.n6_met_flag) as met_flag

                            {% elif system_id == 'my' %}

                            base.n7_cutoff_date as report_date
                            , 'D7' as cutoff_type
                            , n7_met_flag as met_flag

                            {%- else %}

                            base.n5_cutoff_date as report_date
                            , 'D5' as cutoff_type
                            , n5_met_flag as met_flag

                            {%- endif %}

                            , base.dest_hub_id
                            , dest_zone
                            , hubs.name as dest_hub_name
                            , hubs.region as dest_hub_region
                            , hubs.facility_type as dest_hub_facility_type
                        from cisp_report as base
                        left join hubs_enriched as hubs on
                            base.dest_hub_id = hubs.id

                    )
                    , final as (

                        select
                            report_date
                            , dest_hub_id
                            , dest_zone
                            , dest_hub_name
                            , dest_hub_region
                            , dest_hub_facility_type
                            , cutoff_type
                            , date_format(report_date, 'yyyy-MM') as created_month
                            , count(*) as total_count
                            , sum(met_flag) as terminal_status_met_count
                        from base
                        group by {{ range(1, 9) | join(',') }}

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_TERMINAL_STATUS_REPORT_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
