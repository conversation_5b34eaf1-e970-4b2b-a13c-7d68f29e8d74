import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SHIPPER_CLAIMS_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SHIPPER_CLAIMS_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrderSLADAG.Task.SLA_REPORTS_BASE_MASKED,
        data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED,
        data_warehouse.OrderSLADAG.Task.SLA_EXTENSIONS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SLA_REPORTS_BASE,
                view_name="sla_reports_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SLA_EXTENSIONS,
                view_name="sla_extensions",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_SLA_DAYS,
                view_name="shipper_sla_days",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="start_clock_conf",
                jinja_template="""
                SELECT base.order_id
                       , base.tracking_id
                       , base.inbound_datetime
                       , base.inbound_hub_id
                       , base.pickup_datetime
                       , base.pickup_hub_id
                       , base.xb_handed_over_to_last_mile_datetime
                       , base.rts_trigger_datetime
                       , base.delivery_success_datetime
                       , base.reporting_name
                       , base.shipper_id
                       , base.parent_id
                       , base.acquisition_endpoint
                       , base.sales_channel
                       , base.dest_hub_region
                       , base.dest_hub_address_city
                       , base.rts_dest_hub_address_city
                       , base.rts_flag
                       , base.system_id

                       , {{ start_clock_type_conf[system_id] }} AS start_clock_type
                       , {{ start_clock_cutoff_conf[system_id] }} AS start_clock_cutoff
                       , {{ rts_start_clock_type_conf[system_id] }} AS rts_start_clock_type
                FROM sla_reports_base AS base
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "start_clock_type_conf": {
                        "id": "IF(base.parent_id = 849948, 'pickup', 'pickup_inbound')",
                        "my": "'pickup_inbound'",
                        "ph": """
                                IF(base.sales_channel = 'Cross Border'
                                    , 'holm_pickup_inbound'
                                    , 'pickup_inbound'
                                )
                                """,
                        "sg": "'pickup_inbound'",
                        "th": "'pickup_inbound'",
                        "vn": "IF(base.parent_id = 3272628, 'inbound', 'pickup_inbound')",
                    },
                    "start_clock_cutoff_conf": {
                        "id": """
                            CASE
                                WHEN base.parent_id = 849948 THEN '21:15:00'
                                WHEN base.from_name IN ('LeL ID - Surabaya Bulky', 'LeL ID - Bulky WH')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '13:00:00'
                                WHEN base.from_name IN ('LeL ID - Medan'
                                                         , 'LeL ID - Medan Bulky'
                                                         , 'LeL ID - Balikpapan'
                                                         , 'LeL ID - Makassar')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '14:00:00'
                                WHEN base.from_name IN ('LeL ID - Main WH', 'LeL ID - CBD', 'LeL ID - Surabaya')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '15:00:00'
                                WHEN base.reporting_name = 'lazada' THEN '22:00:00'
                                ELSE '23:59:59'
                            END
                            """,
                        "my": "'23:59:59'",
                        "ph": "'23:59:59'",
                        "sg": "'23:59:59'",
                        "th": "'23:59:59'",
                        "vn": "IF(base.reporting_name = 'tiki', '22:00:00', '23:59:59')",
                    },
                    "rts_start_clock_type_conf": {
                        "id": "'forward_leg_start_clock'",
                        "my": "'forward_leg_start_clock'",
                        "ph": """
                                CASE
                                    WHEN base.shipper_id = 7314943 THEN 'rts_trigger'
                                    WHEN base.reporting_name = 'lazada' THEN 'forward_leg_start_clock'
                                    WHEN base.shipper_id in (6380259, 9491680) THEN 'forward_leg_start_clock'
                                    WHEN base.sales_channel = 'Cross Border' THEN 'forward_leg_start_clock'
                                    ELSE 'rts_trigger'
                                END
                                """,
                        "sg": "'forward_leg_start_clock'",
                        "th": "'forward_leg_start_clock'",
                        "vn": """
                            IF(base.reporting_name = 'sendo' OR base.acquisition_endpoint = 'Ninja Easy'
                               , 'rts_trigger'
                               , 'forward_leg_start_clock')
                            """,
                    },
                },
            ),
            base.TransformView(
                view_name="start_clock_calc_1",
                jinja_template="""
                SELECT base.*
                       , CASE
                            WHEN base.start_clock_type = 'pickup'
                            THEN IF(base.pickup_datetime > base.rts_trigger_datetime, NULL, base.pickup_datetime)
                            WHEN base.start_clock_type = 'inbound'
                            THEN IF(base.inbound_datetime > base.rts_trigger_datetime, NULL, base.inbound_datetime)
                            WHEN base.start_clock_type = 'pickup_inbound'
                            THEN
                                IF(least(base.pickup_datetime, base.inbound_datetime) > base.rts_trigger_datetime
                                   , NULL
                                   , least(base.pickup_datetime, base.inbound_datetime))
                            WHEN base.start_clock_type = 'holm_pickup_inbound'
                            THEN
                                IF(least(
                                    base.pickup_datetime
                                    , base.inbound_datetime
                                    , base.xb_handed_over_to_last_mile_datetime
                                ) > base.rts_trigger_datetime
                                   , NULL
                                   , least(
                                    base.pickup_datetime, base.inbound_datetime, base.xb_handed_over_to_last_mile_datetime
                                ))
                         END AS start_clock_datetime
                       , CASE
                            WHEN base.start_clock_type = 'pickup'
                            THEN IF(base.pickup_datetime > base.rts_trigger_datetime, NULL, base.pickup_hub_id)
                            WHEN base.start_clock_type = 'inbound'
                            THEN IF(base.inbound_datetime > base.rts_trigger_datetime, NULL, base.inbound_hub_id)
                            WHEN base.start_clock_type in ('pickup_inbound', 'holm_pickup_inbound')
                            THEN
                                CASE
                                    WHEN least(base.pickup_datetime, base.inbound_datetime) > base.rts_trigger_datetime
                                    THEN NULL
                                    WHEN base.inbound_datetime IS NULL THEN base.pickup_hub_id
                                    WHEN base.pickup_datetime IS NULL THEN base.inbound_hub_id
                                    WHEN base.pickup_datetime <= base.inbound_datetime THEN base.pickup_hub_id
                                    WHEN base.inbound_datetime < base.pickup_datetime THEN base.inbound_hub_id
                                END
                         END AS origin_hub_id
                       , base.rts_start_clock_type
                FROM start_clock_conf AS base
                """,
            ),
            base.TransformView(
                view_name="start_clock_calc_2",
                jinja_template="""
                SELECT base.*
                       , origin_hub.name AS origin_hub_name
                       , origin_hub.region AS origin_hub_region
                       , origin_hub.address_city AS origin_hub_address_city
                       , IF(date_format(base.start_clock_datetime, 'HH:mm:ss') > base.start_clock_cutoff
                            , coalesce(c_start_dt_reg.next_working_day_1, c_start_dt_nat.next_working_day_1)
                            , date(base.start_clock_datetime)) AS start_clock_date
                       , base.rts_start_clock_type
                FROM start_clock_calc_1 AS base
                LEFT JOIN hubs_enriched AS origin_hub ON base.origin_hub_id = origin_hub.id
                LEFT JOIN calendar AS c_start_dt_nat on c_start_dt_nat.date = date(base.start_clock_datetime)
                    AND c_start_dt_nat.region = 'national'
                LEFT JOIN calendar AS c_start_dt_reg on c_start_dt_reg.date = date(base.start_clock_datetime)
                    AND c_start_dt_reg.region = origin_hub.address_city
                    AND c_start_dt_reg.system_id = 'my'
                """,
            ),
            base.TransformView(
                view_name="start_clock_calc_3",
                jinja_template="""
                SELECT base.*
                       , CASE
                             WHEN base.rts_start_clock_type = 'forward_leg_start_clock' THEN base.start_clock_date
                             WHEN base.rts_start_clock_type = 'rts_trigger' THEN date(base.rts_trigger_datetime)
                         END AS rts_start_clock_date
                FROM start_clock_calc_2 AS base
                """,
            ),
            base.TransformView(
                view_name="stop_clock_calc",
                jinja_template="""
                SELECT *
                       , least(base.delivery_success_datetime, base.rts_trigger_datetime) AS nc_datetime
                       , IF(base.rts_flag = 1, base.delivery_success_datetime, NULL) AS rts_nc_datetime
                FROM start_clock_calc_3 AS base
                """,
            ),
            base.TransformView(
                view_name="sla_date_conf",
                jinja_template="""
                SELECT base.*
                       , coalesce(
                {%- for type in shipper_sla_days_joins[system_id].keys() %}
                                  sla_{{ type }}.sla_days{% if not loop.last %}, {% endif %}
                {% endfor %}
                                 ) AS sla_days
                       , coalesce(
                {%- for type in shipper_sla_days_joins[system_id].keys() %}
                                  sla_{{ type }}.rts_sla_days{% if not loop.last %}, {% endif %}
                {% endfor %}
                                 ) AS rts_sla_days
                       , coalesce(
                {%- for type in shipper_sla_days_joins[system_id].keys() %}
                                  sla_{{ type }}.calendar_config{% if not loop.last %}, {% endif %}
                {% endfor %}
                                 ) AS calendar_config
                       , coalesce(fwd_extensions.extension_days, 0) AS extension_days
                       , fwd_extensions.extension_reason
                       , coalesce(rts_extensions.extension_days, 0) AS rts_extension_days
                       , rts_extensions.extension_reason AS rts_extension_reason
                FROM stop_clock_calc AS base
                LEFT JOIN sla_extensions AS fwd_extensions ON fwd_extensions.tracking_id = base.tracking_id
                    AND fwd_extensions.delivery_leg = 'delivery'
                LEFT JOIN sla_extensions AS rts_extensions ON rts_extensions.tracking_id = base.tracking_id
                    AND rts_extensions.delivery_leg = 'rts'
                {%- for type, configs in shipper_sla_days_joins[system_id].items() %}
                LEFT JOIN shipper_sla_days AS sla_{{ type }} ON sla_{{ type }}.sla_type = 'claims'
                    AND date(sla_{{ type }}.start_datetime) <= base.start_clock_date
                    AND date(sla_{{ type }}.end_datetime) > base.start_clock_date
                {%- for config, value in configs.items() %}
                {%- if value %}
                    AND sla_{{ type }}.{{ config }} = {{ value }}
                {%- else %}
                    AND sla_{{ type }}.{{ config }} IS NULL
                {%- endif %}
                {%- endfor %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "shipper_sla_days_joins": {
                        "id": {
                            "shipper": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "parent": {
                                "shipper_config_type": "'parent_id'",
                                "shipper_config": "base.parent_id",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                        "my": {
                            "reporting_name": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            }
                        },
                        "ph": {
                            "shopee": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_region",
                                "dest_config": "base.dest_hub_region",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "shopee_express": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id AND base.shipper_id = 3585384",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_region",
                                "dest_config": "base.dest_hub_region",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "shopee_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "lazada": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_region",
                                "dest_config": "base.dest_hub_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "tiktok_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'tiktok'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "zalora_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'zalora'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "shipper_id": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "sales_channel": {
                                "shipper_config_type": "'sales_channel'",
                                "shipper_config": "base.sales_channel",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_region",
                                "dest_config": "base.dest_hub_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                        "sg": {
                            "lazada": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            }
                        },
                        "th": {
                            "shopee": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "partnerships": {
                                "shipper_config_type": "'acquisition_endpoint'",
                                "shipper_config": "base.acquisition_endpoint "
                                "AND base.acquisition_endpoint = 'Key Account Management'",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                        "vn": {
                            "reporting_name": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "ninjaeasy": {
                                "shipper_config_type": "'acquisition_endpoint'",
                                "shipper_config": "base.acquisition_endpoint "
                                "AND base.acquisition_endpoint = 'Key Account Management'",
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                    },
                },
            ),
            base.TransformView(
                view_name="sla_date_calc",
                jinja_template="""
                SELECT *
                {%- if system_id == 'id' %}
                       , date_add(base.start_clock_date, base.sla_days)
                {%- else %}
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(start_clock_date, base.sla_days)
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.sla_days = {{ day }}
                             THEN coalesce(c_start_reg.next_working_day_{{ day }}
                                           , c_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END
                {%- endif %}
                         AS base_sla_date

                {%- if system_id == 'id' %}
                       , date_add(base.start_clock_date, base.sla_days + CAST(base.extension_days AS int))
                {%- else %}
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(start_clock_date, base.sla_days + CAST(base.extension_days AS int))
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.sla_days + base.extension_days = {{ day }}
                             THEN coalesce(c_start_reg.next_working_day_{{ day }}
                                           , c_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END
                {%- endif %}
                         AS sla_date

                {%- if system_id == 'id' %}
                       , date_add(base.rts_start_clock_date, base.rts_sla_days)
                {%- else %}
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(rts_start_clock_date, base.rts_sla_days)
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.rts_sla_days = {{ day }}
                             THEN coalesce(c_rts_start_reg.next_working_day_{{ day }}
                                           , c_rts_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END
                {%- endif %}
                         AS rts_base_sla_date

                {%- if system_id == 'id' %}
                       , date_add(base.rts_start_clock_date
                                  , base.rts_sla_days + CAST(base.rts_extension_days AS int))
                {%- else %}
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(
                                rts_start_clock_date, base.rts_sla_days + CAST(base.rts_extension_days AS int)
                            )
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.rts_sla_days + base.rts_extension_days = {{ day }}
                             THEN coalesce(c_rts_start_reg.next_working_day_{{ day }}
                                           , c_rts_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END
                {%- endif %}
                         AS rts_sla_date
                FROM sla_date_conf AS base
                {%- if system_id != 'id' %}
                LEFT JOIN calendar AS c_start_nat ON c_start_nat.date = base.start_clock_date
                    AND c_start_nat.region = 'national'
                LEFT JOIN calendar AS c_start_reg ON c_start_reg.date = base.start_clock_date
                    AND c_start_reg.region = base.dest_hub_address_city
                    AND c_start_reg.system_id = 'my'
                LEFT JOIN calendar AS c_rts_start_nat ON c_rts_start_nat.date = base.rts_start_clock_date
                    AND c_rts_start_nat.region = 'national'
                LEFT JOIN calendar AS c_rts_start_reg ON c_rts_start_reg.date = base.rts_start_clock_date
                    AND c_rts_start_reg.region = base.rts_dest_hub_address_city
                    AND c_rts_start_reg.system_id = 'my'
                {%- endif %}
                """,
                jinja_arguments={"system_id": system_id, "max_sla_days": 90},
            ),
            base.TransformView(
                view_name="kpi_measured_calc",
                jinja_template="""
                SELECT *
                       , IF(base.sla_date IS NOT NULL
                            AND base.sla_date < date('{{ measurement_datetime_local }}'), 1, 0) AS nc_measured
                       , IF(base.rts_sla_date IS NOT NULL
                             AND base.rts_sla_date < date('{{ measurement_datetime_local }}')
                             AND base.rts_flag = 1, 1, 0) AS rts_nc_measured
                FROM sla_date_calc AS base
                """,
                jinja_arguments={
                    "measurement_datetime_local": measurement_datetime.in_tz(getattr(date.Timezone, system_id.upper()))
                },
            ),
            base.TransformView(
                view_name="kpi_met_calc",
                jinja_template="""
                SELECT *
                       , CASE
                             WHEN base.nc_measured = 0 THEN NULL
                             WHEN date(base.nc_datetime) <= base.sla_date THEN 1
                             ELSE 0
                         END AS nc_met
                       , CASE
                             WHEN base.rts_nc_measured = 0 THEN NULL
                             WHEN date(base.rts_nc_datetime) <= base.rts_sla_date THEN 1
                             ELSE 0
                         END AS rts_nc_met
                FROM kpi_measured_calc AS base
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.country
                       , base.order_id
                       , base.tracking_id
                       , base.third_party_tracking_id
                       , base.shipper_id
                       , base.shipper_name
                       , base.parent_id
                       , base.parent_name
                       , base.parent_id_coalesce
                       , base.parent_name_coalesce
                       , base.sales_channel
                       , base.reporting_name
                       , base.business_unit
                       , base.acquisition_endpoint
                       , base.marketplace
                       , base.shipper_order_type
                       , base.cod_flag
                       , base.from_postcode
                       , base.to_postcode
                       , base.dest_postcode
                       , base.from_billing_zone
                       , base.to_billing_zone
                       , base.from_name
                       , base.service_type
                       , base.delivery_type
                       , base.parcel_size
                       , base.nv_length
                       , base.nv_width
                       , base.nv_height
                       , base.nv_weight
                       , base.weight
                       , base.granular_status
                       , base.creation_datetime
                       , sla_calc.origin_hub_id
                       , sla_calc.origin_hub_name
                       , sla_calc.origin_hub_region
                       , sla_calc.origin_hub_address_city
                       , base.dest_hub_id
                       , base.dest_hub_name
                       , base.dest_hub_region
                       , base.dest_hub_address_city
                       , base.dest_zone
                       , base.dp_dropoff_dp_id
                       , base.dp_dropoff_dp_name
                       , base.dp_dropoff_datetime
                       , base.xb_handed_over_to_last_mile_datetime
                       , base.pickup_hub_id
                       , base.pickup_hub_name
                       , base.pickup_hub_region
                       , base.pickup_datetime
                       , base.nv_pickup_datetime
                       , base.inbound_hub_id
                       , base.inbound_hub_name
                       , base.inbound_hub_region
                       , base.inbound_datetime
                       , base.inbound_type
                       , base.delivery_attempts
                       , base.first_valid_delivery_attempt_datetime
                       , base.first_valid_delivery_failure_reason
                       , base.second_valid_delivery_attempt_datetime
                       , base.second_valid_delivery_failure_reason
                       , base.third_valid_delivery_attempt_datetime
                       , base.third_valid_delivery_failure_reason
                       , base.last_valid_delivery_attempt_datetime
                       , base.last_valid_delivery_failure_reason
                       , base.rts_flag
                       , base.rts_reason
                       , base.rts_origin_hub_id
                       , base.rts_origin_hub_name
                       , base.rts_origin_hub_region
                       , base.rts_dest_hub_id
                       , base.rts_dest_hub_name
                       , base.rts_dest_hub_region
                       , base.rts_dest_hub_address_city
                       , base.rts_dest_zone
                       , base.rts_trigger_datetime
                       , base.rts_dest_hub_inbound_datetime
                       , base.first_valid_rts_attempt_datetime
                       , base.rts_attempts
                       , base.delivery_success_datetime
                       , sla_calc.start_clock_type
                       , sla_calc.start_clock_datetime
                       , sla_calc.start_clock_cutoff
                       , sla_calc.start_clock_date
                       , sla_calc.rts_start_clock_type AS claims_rts_start_clock_type
                       , sla_calc.rts_start_clock_date AS claims_rts_start_clock_date
                       , sla_calc.sla_days
                       , sla_calc.base_sla_date
                       , sla_calc.extension_days
                       , sla_calc.extension_reason
                       , sla_calc.sla_date
                       , c_sla.day AS sla_date_dow
                       , sla_calc.nc_datetime
                       , sla_calc.nc_measured
                       , sla_calc.nc_met
                       , sla_calc.rts_sla_days
                       , sla_calc.rts_sla_date
                       , sla_calc.rts_extension_days
                       , sla_calc.rts_extension_reason
                       , sla_calc.rts_nc_datetime
                       , sla_calc.rts_base_sla_date
                       , c_rts_sla.day AS rts_sla_date_dow
                       , sla_calc.rts_nc_measured
                       , sla_calc.rts_nc_met
                       , base.is_pickup_required
                {%- for name, ref_dates in dest_hub_datediff_cols.items() %}
                       , CASE
                           WHEN {{ calendar_joins[ref_dates[0]] }} is null
                               or {{ calendar_joins[ref_dates[1]] }} is null
                           THEN null
                           ELSE
                {%- if system_id == 'my' %}
                        greatest(
                            coalesce({{ ref_dates[1] }}_reg.working_day_cum - {{ ref_dates[0] }}_reg.working_day_cum
                                    , {{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum)
                            , 0
                        )
                {%- else %}
                        greatest({{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum, 0)
                {%- endif %}
                       END AS {{ name }}
                {%- endfor %}

                {%- for name, ref_dates in rts_dest_hub_datediff_cols.items() %}
                       , CASE
                           WHEN {{ calendar_joins[ref_dates[0]] }} is null
                               or {{ calendar_joins[ref_dates[1]] }} is null
                           THEN null
                           ELSE
                {%- if system_id == 'my' %}
                        greatest(
                            coalesce({{ ref_dates[1] }}_rts_reg.working_day_cum
                                    - {{ ref_dates[0] }}_rts_reg.working_day_cum
                                    , {{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum)
                            , 0
                        )
                {%- else %}
                        greatest({{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum, 0)
                {%- endif %}
                       END AS {{ name }}
                {%- endfor %}

                       , base.created_month
                FROM sla_reports_base AS base
                LEFT JOIN kpi_met_calc AS sla_calc ON sla_calc.order_id = base.order_id
                {%- for alias, definition in calendar_joins.items() %}
                LEFT JOIN calendar AS {{ alias }} on {{ alias }}.date = {{ definition }}
                    AND {{ alias }}.region = 'national'
                {%- if system_id == 'my' %}
                {%- if alias in dest_hub_datediff_cols.values() | sum(start = ()) %}
                LEFT JOIN calendar AS {{ alias }}_reg ON {{ alias }}_reg.date = {{ definition }}
                    AND {{ alias }}_reg.region = base.dest_hub_address_city
                {%- endif %}
                {%- if alias in rts_dest_hub_datediff_cols.values() | sum(start = ()) %}
                LEFT JOIN calendar AS {{ alias }}_rts_reg ON {{ alias }}_rts_reg.date = {{ definition }}
                    AND {{ alias }}_rts_reg.region = base.rts_dest_hub_address_city
                {%- endif %}
                {%- endif %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "calendar_joins": {
                        "c_start": "sla_calc.start_clock_date",
                        "c_first_attempt": "date(base.first_valid_delivery_attempt_datetime)",
                        "c_second_attempt": "date(base.second_valid_delivery_attempt_datetime)",
                        "c_third_attempt": "date(base.third_valid_delivery_attempt_datetime)",
                        "c_last_attempt": "date(base.last_valid_delivery_attempt_datetime)",
                        "c_rts": "date(base.rts_trigger_datetime)",
                        "c_success": "date(base.delivery_success_datetime)",
                        "c_rts_attempt": "date(base.first_valid_rts_attempt_datetime)",
                        "c_rts_start": "sla_calc.rts_start_clock_date",
                        "c_nc": "date(sla_calc.nc_datetime)",
                        "c_rts_nc": "date(sla_calc.rts_nc_datetime)",
                        "c_sla": "sla_calc.sla_date",
                        "c_rts_sla": "sla_calc.rts_sla_date",
                    },
                    "dest_hub_datediff_cols": {
                        "days_to_first_valid_delivery_attempt": ("c_start", "c_first_attempt"),
                        "days_to_second_valid_delivery_attempt": ("c_start", "c_second_attempt"),
                        "days_to_third_valid_delivery_attempt": ("c_start", "c_third_attempt"),
                        "days_to_last_valid_delivery_attempt": ("c_start", "c_last_attempt"),
                        "last_attempt_to_rts_trigger": ("c_last_attempt", "c_rts"),
                        "days_to_rts_trigger": ("c_start", "c_rts"),
                        "days_to_delivery_success": ("c_start", "c_success"),
                        "days_to_nc": ("c_start", "c_nc"),
                    },
                    "rts_dest_hub_datediff_cols": {
                        "rts_trigger_to_first_rts_attempt": ("c_rts", "c_rts_attempt"),
                        "rts_trigger_to_delivery_success": ("c_rts", "c_success"),
                        "days_to_rts_nc": ("c_rts_start", "c_rts_nc"),
                    },
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_CLAIMS_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
