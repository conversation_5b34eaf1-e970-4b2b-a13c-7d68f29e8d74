import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED + ".py",
    task_name=data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED,
    depends_on=(data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_INBOUND_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_CREATION_VOL_DAILY_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_CREATION_VOL_DAILY,
                view_name="shipper_creation_vol_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_INBOUND_VOL_DAILY,
                view_name="shipper_inbound_vol_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="creation_agg",
                jinja_template="""
                select
                    shipper_id
                    , min(creation_date) filter (
                        where pending_orders + cancelled_orders <> total_orders
                    ) as first_order_placed_date
                    , max(creation_date) filter (
                        where pending_orders + cancelled_orders <> total_orders
                    ) as last_order_placed_date
                from shipper_creation_vol_daily
                group by 1
                """,
            ),
            base.TransformView(
                view_name="inbound_agg",
                jinja_template="""
                select
                    shipper_id
                    , min(inbound_date) as first_order_inbound_date
                    , max(inbound_date) as last_order_inbound_date
                from shipper_inbound_vol_daily
                group by 1
                """,
            ),
            base.TransformView(
                view_name="completion_agg",
                jinja_template="""
                with
                    base as (

                        select
                            shipper_id
                            , completion_date
                            , datediff(
                                completion_date
                                , lag(completion_date) over (partition by shipper_id order by completion_date)
                            ) as days_since_last_completion
                        from shipper_completion_vol_daily

                    )
                    , final as (

                        select
                            shipper_id
                            , min(completion_date) as first_order_completion_date
                            , max(completion_date) as last_order_completion_date
                            , count(completion_date) as total_completion_days
                            , avg(days_since_last_completion) as avg_days_btw_completion
                        from base
                        group by 1

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    shipper_attributes.id as shipper_id
                    , shipper_attributes.parent_id
                    , shipper_attributes.parent_id_coalesce
                    , creation_agg.first_order_placed_date
                    , creation_agg.last_order_placed_date
                    , inbound_agg.first_order_inbound_date
                    , inbound_agg.last_order_inbound_date
                    , completion_agg.first_order_completion_date
                    , completion_agg.last_order_completion_date
                    , completion_agg.total_completion_days
                    , completion_agg.avg_days_btw_completion
                    , case
                        when completion_agg.total_completion_days <= 4 then 'Less than 4'
                        when completion_agg.avg_days_btw_completion <= 2 then 'Daily'
                        when completion_agg.avg_days_btw_completion <= 7 then 'Weekly'
                        when completion_agg.avg_days_btw_completion <= 15 then 'Biweekly'
                        when completion_agg.avg_days_btw_completion <= 30 then 'Monthly'
                        when completion_agg.avg_days_btw_completion <= 90 then 'Quarterly'
                        when completion_agg.avg_days_btw_completion <= 180 then 'Biannually'
                        when completion_agg.avg_days_btw_completion <= 365 then 'Annually'
                        when completion_agg.avg_days_btw_completion > 365 then 'Less than Once a Year'
                    end as order_frequency
                    , shipper_attributes.system_id
                    , shipper_attributes.created_month
                from shipper_attributes
                left join creation_agg on
                    shipper_attributes.id = creation_agg.shipper_id
                left join inbound_agg on
                    shipper_attributes.id = inbound_agg.shipper_id
                left join completion_agg on
                    shipper_attributes.id = completion_agg.shipper_id
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_MILESTONES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
