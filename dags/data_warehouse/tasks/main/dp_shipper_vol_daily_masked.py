import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.DPDAG.Task.DP_SHIPPER_VOL_DAILY_MASKED + ".py",
    task_name=data_warehouse.DPDAG.Task.DP_SHIPPER_VOL_DAILY_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED, data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED, view_name="dps_enriched"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
        )
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT
                    DATE(reservations.released_at) AS released_date
                    , reservations.dp_id
                    , dps.dp_category
                    , shippers.id AS shipper_id
                    , reservations.system_id
                    , reservations.system_id AS country
                    , shippers.shipper_name
                    , shippers.parent_id
                    , shippers.parent_name
                    , dps.partner_id
                    , dps.partner_name
                    , shippers.parent_id_coalesce
                    , shippers.parent_name_coalesce
                    , reservations.source
                    , DATE_FORMAT(DATE(reservations.released_at), 'yyyy-MM') AS created_month
                    , count(*) AS total_orders
                FROM
                  dps_enriched dps
                  JOIN dp_reservations_enriched reservations ON reservations.dp_id = dps.id
                  LEFT JOIN shipper_attributes shippers ON shippers.id = reservations.shipper_id
                WHERE reservations.status = 'RELEASED' AND reservations.released_at IS NOT NULL
                GROUP BY {{ range(1, 15) | join(',') }}
                HAVING released_date <= date('{{ measurement_datetime }}')
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DP_SHIPPER_VOL_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
