import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.SG_ROUTE_ZONE_SUCCESS_RATE_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.SG_ROUTE_ZONE_SUCCESS_RATE_MASKED,
    system_ids=(
        constants.SystemID.SG,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with route_deliveries as (
                    select
                        date(event_datetime) as route_date
                        , route_id
                        , route_hub_id
                        , route_driver_id
                        , dest_zone
                        , order_id
                        , status
                        , type
                        , event_datetime
                        , waypoint_zone
                        , substring_index(waypoint_zone_short_name, '-',1) as waypoint_zone_group
                        , if(valid_flag = 0 and status = 'Fail', 1, 0) as invalid_attempt_flag
                        , system_id
                    from delivery_transaction_events
                    where
                        order_id is not null
                        and route_id is not null
                        and event_datetime is not null
                    ),

                    aggregated as (
                            select
                                route_deliveries.route_date
                                , route_deliveries.route_id
                                , route_deliveries.waypoint_zone
                                , route_deliveries.waypoint_zone_group
                                , route_deliveries.route_hub_id as depot_id
                                , hubs_enriched.name as depot_name
                                , route_deliveries.route_driver_id as courier_id
                                , drivers_enriched.display_name as courier_display_name
                                , drivers_enriched.driver_type as courier_type
                                , route_deliveries.system_id
                                , date_format(route_deliveries.route_date, 'yyyy-MM') as created_month
                                , count(distinct route_deliveries.order_id)  as parcels_on_route
                                , count(distinct route_deliveries.order_id) filter (where route_deliveries.status = 'Success') as parcels_delivered
                            from route_deliveries
                            left join hubs_enriched
                                on route_deliveries.route_hub_id = hubs_enriched.id
                            left join drivers_enriched
                                on route_deliveries.route_driver_id = drivers_enriched.id
                            group by {{ range(1, 12) | join(',') }}
                    )

                    select *
                    from aggregated
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SG_ROUTE_ZONE_SUCCESS_RATE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
