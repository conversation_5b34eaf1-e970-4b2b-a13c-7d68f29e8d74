import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.DRIVER_TYPES_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.DRIVER_TYPES_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_TYPES, view_name="driver_types"),
            base.InputTable(
                path=delta_tables.GDrive(input_env).DRIVER_TYPES_ENRICHED, view_name="driver_types_enriched_gdrive"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    driver_types.system_id as country
                    , driver_types.id
                    , driver_types.name as driver_type
                    , gdrive.hub
                    , gdrive.fleet_type
                    , gdrive.scheme_type
                    , gdrive.veh_type
                    , date(from_utc_timestamp(driver_types.created_at, {{ get_local_timezone }})) as creation_date
                    , if(driver_types.deleted_at is not null, 1, 0) as is_deleted
                    , driver_types.system_id
                    , date_format(driver_types.created_at, 'yyyy-MM') as created_month
                from driver_types
                left join driver_types_enriched_gdrive as gdrive on
                    driver_types.id = gdrive.id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("driver_types.system_id")},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVER_TYPES_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
