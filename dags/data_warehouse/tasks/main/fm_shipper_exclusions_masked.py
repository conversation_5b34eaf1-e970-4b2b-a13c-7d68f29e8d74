import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED
        ),
    ),
    system_ids=(constants.SystemID.GL,),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FM_SHIPPER_EXCLUSION_LIST,
                view_name="id_fm_shipper_exclusion_list",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).MY_FM_SHIPPER_EXCLUSION_LIST,
                view_name="my_fm_shipper_exclusion_list",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).PH_FM_SHIPPER_EXCLUSION_LIST,
                view_name="ph_fm_shipper_exclusion_list",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).SG_FM_SHIPPER_EXCLUSION_LIST,
                view_name="sg_fm_shipper_exclusion_list",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).TH_FM_SHIPPER_EXCLUSION_LIST,
                view_name="th_fm_shipper_exclusion_list",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).VN_FM_SHIPPER_EXCLUSION_LIST,
                view_name="vn_fm_shipper_exclusion_list",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_MILESTONES,
                view_name="shipper_milestones",
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with gsheet_base as (
                {% for country in country_list %}
                    select
                        '{{ country }}' as system_id
                        , cast(global_shipper_id as int) as global_shipper_id
                        , shipper_name
                        , exclusion_reason
                        , to_date(start_date, 'M/d/yyyy') as start_date
                        , to_date(end_date, 'M/d/yyyy') as end_date
                        , to_date(onboard_date, 'M/d/yyyy') as onboard_date
                    from {{ country }}_fm_shipper_exclusion_list                
                {% if not loop.last -%} UNION ALL {%- endif %}
                {% endfor %}
                ),

                gsheet_start_adjust as (
                    select
                        system_id
                        , global_shipper_id
                        , exclusion_reason
                        , if(system_id IN ('sg','ph','th'), onboard_date, start_date) as start_date
                        , end_date
                    from gsheet_base
                ),

                legacy_shippers_base as (
                    select
                        shipper_attributes.system_id
                        , shipper_attributes.id as shipper_id
                        , '' as exclusion_reason
                        , date '2014-12-01' as start_date
                        , date '2099-01-01' as end_date
                    from shipper_attributes
                    left join shipper_milestones
                        on shipper_attributes.id = shipper_milestones.shipper_id
                    where
                        last_order_placed_date > date '2021-07-01'
                        and pickup_scan_exclusion_flag = 1
                ),

                merged_data as (
                select 
                    * 
                from legacy_shippers_base
                UNION ALL
                select
                    *
                from gsheet_start_adjust
                ),

                modify_end_date_cte as (
                select
                    shipper_id
                    , exclusion_reason
                    , start_date
                    , end_date
                -- use the start date of the newer entry and subtract it by 1
                    , lag(start_date) 
                        over(partition by shipper_id, system_id order by start_date desc) - interval '1' day 
                            as end_date_lag
                    , system_id
                from merged_data

                )

                select
                    shipper_id
                    , exclusion_reason
                    , start_date
                    , coalesce(end_date_lag, end_date) as end_date
                    , system_id
                from modify_end_date_cte
                where
                    coalesce(end_date_lag, end_date) > start_date

                """,
                jinja_arguments={
                    "country_list": ('id', 'my', 'ph', 'sg', 'th', 'vn')
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FM_SHIPPER_EXCLUSIONS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
