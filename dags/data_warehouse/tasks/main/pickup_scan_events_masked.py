import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    core_system_ids = (
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    )

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
            *[
                base.InputTable(
                    path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                    view_name=f"inbound_scans_{system_id}",
                    input_range=lookback_ranges.input,
                )
                for system_id in core_system_ids
            ],
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="pickup_success_scans",
                jinja_template="""
                with
                    global_raw as (

                        {%- for system_id in system_ids %}
                        select
                            id
                            , order_id
                            , result
                            , route_id
                            , created_at
                            , '{{ system_id }}' as system_id
                        from inbound_scans_{{ system_id }}
                        where
                            deleted_at is null
                            and order_id is not null
                            and type = 1
                        {% if not loop.last %}union all{% endif %}
                        {%- endfor %}

                    )
                    , final as (

                        -- inbound_scans type 1=VAN_FROM_SHIPPER
                        select
                            id
                            , order_id
                            , 'Success' as status
                            , result as scan_result_id
                            , case
                                {%- for id, name in id_to_result.items() %}
                                when result = '{{ id }}' then '{{ name }}'
                                {%- endfor %}
                            end as scan_result
                            , route_id
                            , from_utc_timestamp(created_at, {{ get_local_timezone }}) as event_datetime
                            , system_id
                            , date_format(created_at, 'yyyy-MM') as created_month
                        from global_raw

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "system_ids": core_system_ids,
                    "get_local_timezone": util.get_local_timezone("system_id"),
                    "id_to_result": {
                        "S1": "RECEIVED_BY_DRIVER_FROM_CUSTOMER",
                        "S2": "RECEIVED_BY_SORTING_HUB",
                        "S2A": "RECEIVED_BY_SORTING_HUB_SAMEDAY",
                        "S3": "RECEIVED_BY_VAN",
                        "S4": "RECEIVED_BY_TRUCK",
                        "S5": "RECEIVED_BY_SORTING_HUB_CMI",
                        "E1": "TRACKING_ID_NOT_FOUND",
                        "E2": "INCORRECT_ROUTE",
                        "E3": "DUPLICATE_SCAN",
                        "E4": "ORDER_COMPLETED",
                        "E5": "OTHERS",
                    },
                },
            ),
            base.TransformView(
                view_name="pickup_fail_events",
                jinja_template="""
                -- order_events type 40=PICKUP_FAILURE

                select
                    cast(id as bigint) as id
                    , cast(order_id as bigint) as order_id
                    , 'Failed' as status
                    , cast(get_json_object(data, '$.failure_reason_id') as int) as failure_reason_id
                    , cast(get_json_object(data, '$.reservation_id') as int) as reservation_id
                    , cast(get_json_object(data, '$.route_id') as int) as route_id
                    , from_utc_timestamp(time, {{ get_local_timezone }}) as event_datetime
                    , system_id
                    , date_format(time, 'yyyy-MM') as created_month
                from order_events
                where
                    type = 40
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    route as (

                        select
                            legacy_id as id
                            , system_id
                            , driver_id
                            , hub_id
                        from route_logs

                    )
                    , all_pickup as (

                        select
                            id
                            , 'inbound_scans' as source_table
                            , order_id
                            , status
                            , scan_result_id
                            , scan_result
                            , null as failure_reason_id
                            , null as reservation_id
                            , route_id
                            , event_datetime
                            , system_id
                            , created_month
                        from pickup_success_scans
                        union all
                        select
                            id
                            , 'order_events' as source_table
                            , order_id
                            , status
                            , null as scan_result_id
                            , null as scan_result
                            , failure_reason_id
                            , reservation_id
                            , route_id
                            , event_datetime
                            , system_id
                            , created_month
                        from pickup_fail_events

                    )
                    , final as (

                        select
                            all_pickup.id
                            , all_pickup.source_table
                            , all_pickup.order_id
                            , all_pickup.status
                            , all_pickup.scan_result_id
                            , all_pickup.scan_result
                            , all_pickup.failure_reason_id
                            , all_pickup.reservation_id
                            , all_pickup.route_id
                            , cast(route.driver_id as bigint) as route_driver_id
                            , cast(route.hub_id as bigint) route_hub_id
                            , all_pickup.event_datetime
                            , all_pickup.system_id
                            , all_pickup.created_month
                        from all_pickup
                        left join route on
                            all_pickup.route_id = route.id
                            and all_pickup.system_id = route.system_id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={"system_ids": core_system_ids},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PICKUP_SCAN_EVENTS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
