import sys

from pyspark.sql import SparkSession
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SNSChatDAG.Task.SNS_SUBSCRIPTION_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SNSChatDAG.Task.SNS_SUBSCRIPTION_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.SnsProdGL(input_env, is_masked).EXTERNAL_PLATFORM_REFERENCES,
                view_name="external_platform_references",
            ),
            base.InputTable(
                path=delta_tables.SnsProdGL(input_env, is_masked).IDENTITIES,
                view_name="identities",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="subscribers",
                jinja_template="""
                    select
                        date('{{ measurement_datetime_utc }}') as measurement_datetime
                        , se.id as shipper_id
                        , se.shipper_name
                        , se.parent_id_coalesce
                        , se.parent_name_coalesce
                        , se.sales_channel
                        , epr.platform
                        , case
                            when se.last_order_placed_date >= now() - interval 30 day
                            then 1
                            else 0
                        end as active_flag
                        , epr.subscribed_at
                        , epr.unsubscribed_at
                        , i.phone_number
                        , se.system_id
                        , date_format(current_date(), 'yyyy-MM') as created_month
                    from shippers_enriched as se
                    left join identities as i
                        on se.id = i.shipper_id
                        and se.system_id = i.system_id
                    left join (
                        select
                            identity_id
                            , platform
                            , unsubscribed_at
                            , subscribed_at
                            , subscribed
                            , system_id
                        from external_platform_references
                        where subscribed = 1
                        ) as epr
                        on i.id = epr.identity_id
                        and se.system_id = epr.system_id
                    where 1=1
                        and epr.subscribed = 1
                        and se.sales_channel in ('Partnerships', 'Field Sales', 'Corp Sales', 'Retail', 'NBU'
                            , 'Cross Border', 'Self Serve')
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SNS_SUBSCRIPTION_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)

def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,)

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    spark.conf.set("spark.sql.autoBroadcastJoinThreshold", -1)
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()