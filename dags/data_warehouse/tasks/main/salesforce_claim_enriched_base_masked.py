import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_CLAIM_ENRICHED_BASE_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_CLAIM_ENRICHED_BASE_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing"),
    ),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        parquet_tables=(base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR, view_name="calendar"),),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).CASE, view_name="case"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).CASE_HISTORY, view_name="case_history"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).RECORD_TYPE, view_name="record_type"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="claim_base_table",
                jinja_template="""
                with
                    case_history_status as (
                        select
                            last_value(id) as id
                            , case_id
                            , last_value(created_by_id) as created_by_id
                            , max(case
                                when old_value = 'Pending Invoice'
                                then created_date
                                else null
                            end) as pending_invoice_payment_datetime
                            , max(created_date) as created_date
                        from case_history
                        where
                            field = 'Status'
                            and is_deleted = False
                            and new_value = 'Pending Payment'
                        group by 2
                    )
                select
                    case.id
                    , case.status
                    , cast(case.case_number as string) as case_number
                    , from_utc_timestamp(case.created_date, {{ get_local_timezone }}) as creation_datetime
                    , from_utc_timestamp(case.closed_date, {{ get_local_timezone }}) as closed_datetime
                    , cast(is_closed as int) as is_closed
                    , case.closed_reason_c as closed_reason
                    , case.invalid_reason_c as invalid_reason
                    , user.fsr_sales_team as case_owner_fsr_sales_team
                    , case.tracking_id_c as tracking_id
                    , case.cs_issue_type_c as cs_issue_type
                    , case.cs_issue_sub_type_c as cs_issue_subtype
                    , case.account_id
                    , account.name as account_name
                    , account.sales_channel as account_sales_channel
                    , if(case.origin = 'None', 'Manual', case.origin) as origin
                    , cast(case.global_id_c as bigint) as shipper_id
                    , case.order_outcome_c as order_outcome
                    , case.order_id_c as order_id
                    , case.is_parent_c as is_parent
                    , case.parent_id
                    , case.currency_iso_code as currency_iso_code
                    , case.invoice_expiry_date_c as invoice_expiry_date
                    , case.invoice_value_c as invoice_value
                    , case.liability_cap_c as liability_cap
                    , case.owner_id
                    , case.invoice_date_c as invoice_date
                    , case.days_before_expiry_c as days_before_expiry
                    , case.cod_amount_c as cod_amount
                    , case.insured_value_c as insured_value
                    , case.sf_claimable_amount_c as sf_claimable_amount
                    , case.final_claimable_amount_c as final_claimable_amount
                    , status.id as case_history_id
                    , status.created_by_id as case_history_created_by_id
                    , case
                        when
                            from_utc_timestamp(case.created_date, {{ get_local_timezone }}) < cast('2023-05-01' as date)
                        then
                            from_utc_timestamp(status.pending_invoice_payment_datetime, {{ get_local_timezone }})
                        else from_utc_timestamp(status.created_date, {{ get_local_timezone }})
                    end as pending_payment_datetime
                    , lower(case.country_c) as system_id
                    , case.nv_created_month as created_month
                from case
                left join salesforce_user_enriched user on
                    case.owner_id = user.id
                left join salesforce_user_enriched as ops_user on
                    case.ops_user_c = ops_user.id
                left join record_type as record on
                    case.record_type_id = record.id
                left join salesforce_account_enriched as account on
                    case.account_id = account.id
                left join case_history_status status on
                    case.id = status.case_id
                where
                    case.is_deleted = false
                    and case.country_c != 'None'
                    and record.developer_name in ('FSR_Claims', 'FSR_Claims_Locked')
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(country_c)")},
            ),
            base.TransformView(
                view_name="calendar_working_days",
                jinja_template="""
                -- regular calendar where saturday and sunday are off days
                with
                    country_calendar as (
                        select
                            country
                            , date
                            , day
                            , comments
                            , if(dayofweek(date) = 1  or dayofweek(date) = 7  or comments is not null, 0, 1)
                                as is_working_day
                            , sum(if(if(dayofweek(date) = 1  or dayofweek(date) = 7 or comments is not null, 0, 1) = 1,
                                1, 0) ) over (partition by country order by date asc) as grp
                        from calendar
                        where
                            region = 'national'
                            and country != 'mm'
                    ),
                    regular_calendar as (
                        select
                             country
                            , date
                            , day
                            , comments
                            , is_working_day
                            , date_add(date, days_to_next_working_day) as next_working_day
                        from
                            (
                            select
                                *
                                , row_number() over (
                                    partition by country, grp order by date desc
                                ) as days_to_next_working_day
                            from country_calendar
                            )
                        order by country, date
                    ),
                    working_days as (
                        select
                              country
                            , date
                            , day
                        from regular_calendar
                        where is_working_day = 1
                    ),
                    final as (
                        select
                              rc.country
                            , rc.date
                            , rc.day
                            , rc.comments
                            , rc.is_working_day
                            , rc.next_working_day
                            , wd.date as next_working_dayx
                            , row_number() over(partition by rc.country, rc.date order by wd.date asc)
                                as next_working_day_order
                        from regular_calendar rc
                        left join working_days wd
                            on wd.country = rc.country
                            and wd.date > rc.date
                            and wd.date <= rc.date + interval '21' day
                    )
                select
                    country
                    , date
                    , day
                    , comments
                    , is_working_day
                    , max(if(next_working_day_order = 1, next_working_dayx, null)) as next_working_day
                    , max(if(next_working_day_order = 2, next_working_dayx, null)) as next_working_day2
                    , max(if(next_working_day_order = 3, next_working_dayx, null)) as next_working_day3
                    , max(if(next_working_day_order = 4, next_working_dayx, null)) as next_working_day4
                    , max(if(next_working_day_order = 5, next_working_dayx, null)) as next_working_day5
                    , max(if(next_working_day_order = 6, next_working_dayx, null)) as next_working_day6
                from final
                where next_working_day_order < 7
                group by 1, 2, 3, 4, 5
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    recovery_response as (
                        select
                            claim_base_table.id
                            , sum(cal1.is_working_day) as recovery_response_business_days
                        from claim_base_table
                        left join calendar_working_days cal1
                            on cal1.date >= claim_base_table.invoice_date
                            and cal1.date <= date(claim_base_table.pending_payment_datetime)
                            and cal1.country = claim_base_table.system_id
                        group by 1
                    ),
                    claim_process_payment as (
                        select
                            claim_base_table.id
                            , sum(cal2.is_working_day) as claim_process_payment_business_days
                        from claim_base_table
                        left join calendar_working_days cal2
                            on cal2.date >= claim_base_table.invoice_date
                            and cal2.date <= date(claim_base_table.closed_datetime)
                            and cal2.country = claim_base_table.system_id
                        group by 1
                    )
                select
                    claim_base_table.*
                    , recovery_response_business_days
                    , claim_process_payment_business_days
                from claim_base_table
                left join recovery_response
                    on claim_base_table.id = recovery_response.id
                left join claim_process_payment
                    on claim_base_table.id = claim_process_payment.id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("base.system_id")},
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_CLAIM_ENRICHED_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
