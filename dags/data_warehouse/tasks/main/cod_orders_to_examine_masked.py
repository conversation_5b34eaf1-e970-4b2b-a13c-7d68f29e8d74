import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachPriceMatchingDAG.Task.COD_ORDERS_TO_EXAMINE_MASKED + ".py",
    task_name=data_warehouse.SLABreachPriceMatchingDAG.Task.COD_ORDERS_TO_EXAMINE_MASKED,
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_DETAILS,
                view_name="order_details",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select 
                    orders.id order_id
                    , shippers_enriched.id as shipper_id
                    , from_utc_timestamp(orders.created_at, '{{ local_timezone }}') AS created_at
                    , from_utc_timestamp(orders.updated_at, '{{ local_timezone }}') AS updated_at
                    , orders.cod_id
                    , orders.granular_status
                    , cods.goods_amount
                    , package_content
                    , '{{ system_id }}' as system_id
                from 
                    orders
                join shippers_enriched on
                    orders.global_shipper_id = shippers_enriched.id
                join cods on 
                    orders.cod_id = cods.id
                join order_details on
                    orders.id = order_details.order_id
                where True
                    -- Lazada only
                    and shippers_enriched.parent_id_coalesce = 341107

                    -- non-Lazmall shippers only
                    and get_json_object(orders.shipper_ref_metadata, '$.platformInfo.platformTags') not like '%lazmall%'

                    -- all COD orders
                    and cod_id is not null

                    -- filter for orders with cod over 500k   
                    and cods.goods_amount >= 500000
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , shipper_id
                    , created_at
                    , updated_at
                    , cod_id
                    , granular_status
                    , goods_amount AS cod_value
                    , package_content
                    , system_id
                    , date_format(created_at, 'yyyy-MM') AS created_month
                from base 
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COD_ORDERS_TO_EXAMINE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()