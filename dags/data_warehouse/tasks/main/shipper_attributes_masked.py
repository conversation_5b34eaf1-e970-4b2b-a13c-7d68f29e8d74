import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED + ".py",
    task_name=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesDAG.DAG_ID, task_id=data_warehouse.SalesDAG.Task.SALESPERSONS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
    ),
    system_ids=(SystemID.GL,),
)

NOTIFICATION_SETTINGS = (
    "pickup_fail_customer",
    "pickup_fail_shipper",
    "transit_customer",
    "transit_shipper",
    "delivery_fail_customer",
    "delivery_fail_shipper",
    "completed_customer",
    "completed_shipper",
)
SYSTEM_IDS = (SystemID.ID, SystemID.MY, SystemID.PH, SystemID.SG, SystemID.TH, SystemID.VN)
SALESFORCE_ACCOUNT_COLUMNS = {
    "id": "acc_id",
    "parent_acc_id_coalesce": None,
    "parent_acc_name_coalesce": None,
    "parent_acc_shipper_id_coalesce": None,
    "shipper_origin": None,
    "shipping_type": None,
    "nv_product_line": None,
    "expected_monthly_vol": None,
    "salesperson": None,
    "salesperson_alias": None,
    "salesperson_manager": None,
    "sales_territory": None,
    "sales_team": None,
    "sales_channel": None,
    "business_type": None,
    "latest_lead_gen_channel": None,
    "latest_lead_source": None,
    "latest_lead_source_details": None,
}


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_tables_industries = [
        base.InputTable(
            path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INDUSTRIES,
            view_name=f"industries_{system_id}"
        )
        for system_id in SYSTEM_IDS
    ]

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESPERSONS_ENRICHED,
                view_name="salespersons_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).PARTNERSHIP_SHIPPER_MASTER_LIST,
                view_name="partnership_shipper_master_list",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS, view_name="shippers"),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).MARKETPLACE_SELLERS,
                view_name="marketplace_sellers"
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).DISTRIBUTION_CHANNELS,
                view_name="distribution_channels"
            ),
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).ACCOUNT_TYPES,
                            view_name="account_types"),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).CORPORATE_BRANCHES, view_name="corporate_branches"
            ),
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_METADATA,
                            view_name="shipper_metadata"),
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_SETTINGS,
                            view_name="shipper_settings"),
            base.InputTable(
                path=delta_tables.GDrive(input_env).SHIPPER_ATTRIBUTES, view_name="shipper_attributes_gdrive"
            ),
            *input_tables_industries,
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                # marketplace_sellers only contains sellers within each marketplace and excludes actual marketplaces
                # this part appends actual marketplaces so that they will show up under the same parent_id
                view_name="marketplace_sellers_enriched",
                jinja_template="""
                select
                     marketplace_id
                    , seller_id
                    , external_ref
                from marketplace_sellers
                union all
                select distinct
                    marketplace_id
                    , marketplace_id as seller_id
                    , null as external_ref
                from marketplace_sellers
                """,
            ),
            base.TransformView(
                view_name="industries",
                jinja_template="""
                {%- for system_id in system_ids %}
                select
                    id
                    , name
                    , '{{ system_id }}' as system_id
                from industries_{{ system_id }}
                {% if not loop.last %}union all{% endif %}
                {%- endfor %}
                """,
                jinja_arguments={"system_ids": SYSTEM_IDS},
            ),
            base.TransformView(
                view_name="shipper_setting_agg",
                jinja_template="""
                select
                    shipper_id
                    {%- for setting in boolean_settings %}
                    , max(if(key = '{{ setting }}' and value = 'true', 1, 0)) as {{ setting }}_flag
                    {%- endfor %}
                    , max(
                        if(key in ('service_type_level', 'services') and lower(value) like '%premium%', 1, 0)
                    ) as premium_service_flag
                    , max(
                        if(key in ('account_frozen_by') and value != '[]', 1, 0)
                    ) as frozen_flag
                from shipper_settings
                group by 1
                """,
                jinja_arguments={
                    "boolean_settings": ("is_pre_paid", *NOTIFICATION_SETTINGS, "enforce_parcel_pickup_tracking")},
            ),
            base.TransformView(
                view_name="shippers_base",
                jinja_template="""
                with
                    unmasked_shipper_metadata as (

                    select 
                        shipper_id 
                        , key 
                        , case 
                            {%- for hashed_value, actual_value in masked_account_creation_source.items() %}
                                 when value = '{{hashed_value}}' then '{{actual_value}}'
                            {%- endfor %}
                            else value
                            end as value
                    from shipper_metadata

                    )

                select
                    shippers.id
                    , shippers.name as shipper_name
                    , lower(shippers.system_id) as country
                    , parents.marketplace_id as parent_id
                    , parent_shippers.name as parent_name
                    , coalesce(parents.marketplace_id, shippers.id) as parent_id_coalesce
                    , coalesce(parent_shippers.name, shippers.name) as parent_name_coalesce
                    , corporate_branches.corporate_id as parent_corporate_id
                    , corporate_branches.external_ref as branch_id
                    , corporate_shippers.name as parent_corporate_name
                    , case
                        when parents.marketplace_id is null then null
                        when parents.marketplace_id = shippers.id then 'marketplace'
                        when parents.marketplace_id <> shippers.id then 'marketplace_seller'
                    end as marketplace
                    , parents.external_ref
                    , shippers.billing_name
                    , distribution_channels.name as distribution_channel_name
                    , account_types.name as account_type_name
                    , industries.name as industry
                    , salespersons_enriched.name as sales_person
                    , salespersons_enriched.code as sales_person_code
                    , salespersons_enriched.team as sales_person_team
                    , shipper_source.value as account_creation_source
                    , if(shipper_setting_agg.is_pre_paid_flag = 1, 'Pre-paid', 'Post-paid') as prepaid_account
                    , if(shipper_setting_agg.premium_service_flag = 1, 'Premium', 'Standard') as service_level
                    , shipper_setting_agg.enforce_parcel_pickup_tracking_flag
                    , shipper_setting_agg.frozen_flag

                    {%- for setting in notification_settings %}
                    , shipper_setting_agg.{{ setting }}_flag as {{ setting }}_notification_flag
                    {%- endfor %}

                    , gdrive.business_unit as hardcoded_business_unit
                    , gdrive.acquisition_endpoint as hardcoded_acquisition_endpoint
                    , coalesce(gdrive.pickup_scan_exclusion_flag, 0) as pickup_scan_exclusion_flag
                    , gdrive.pickup_scan_exclusion_reason

                    {%- for column, new_name in salesforce_account_columns.items() %}
                    , salesforce_account_enriched.{{ column }}
                        as sf_{%- if new_name %}{{ new_name }}{% else %}{{ column }}{%- endif %}
                    {%- endfor %}

                    , case
                        when salespersons_enriched.team = 'Test'
                            then 'Test'
                        when salesforce_account_enriched.sales_channel in (
                                'Corp Sales'
                                , 'Field Sales'
                                , 'Partnerships'
                                , 'Self Serve'
                                , 'Retail'
                            )
                            then salesforce_account_enriched.sales_channel
                        when salespersons_enriched.team in ('{{ field_sales_teams | join("','") }}')
                            then 'Field Sales'
                        when salespersons_enriched.team = 'Partnerships'
                            then 'Partnerships'
                        when salespersons_enriched.team = 'B2B'
                            then 'Corp Sales'
                        when salespersons_enriched.team = 'Cross Border'
                            then 'Cross Border'
                        when salespersons_enriched.team in ('Ninja Direct', 'Solutions')
                            then 'NBU'
                        when salespersons_enriched.team = 'PUDO' or shipper_source.value = 'PUDO'
                            then 'Retail'
                        when shipper_source.value in ('DASH', 'RETAIL')
                             then 'Self Serve'
                        when (
                            shipper_source.value in ('DASH_WEB', 'DASH_MOBILE')
                            and lower(shippers.system_id) in ('sg', 'my', 'ph', 'vn', 'th', 'id')
                        )
                            then 'Self Serve'
                        when (
                            salespersons_enriched.team = "Self Serve"
                            and salespersons_enriched.system_id in ("sg", "my", "ph", "th", "vn", "id")
                        )
                            then "Self Serve"
                        else 'Unassigned'
                    end as sales_channel
                    , salesforce_account_enriched.original_sales_channel
                    , shippers.active
                    , date(from_utc_timestamp(shippers.created_at, {{ get_local_timezone }})) as onboarded_date
                    , date(from_utc_timestamp(shippers.deleted_at, {{ get_local_timezone }})) as deleted_date
                    , lower(shippers.system_id) as system_id
                    , date_format(
                        from_utc_timestamp(shippers.created_at, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                from shippers
                left join marketplace_sellers_enriched as parents on
                    shippers.id = parents.seller_id
                left join shippers as parent_shippers on
                    parents.marketplace_id = parent_shippers.id
                left join corporate_branches on
                    shippers.id = corporate_branches.branch_id
                    and corporate_branches.deleted_at is null
                left join shippers as corporate_shippers on
                    corporate_branches.corporate_id = corporate_shippers.id
                left join distribution_channels on
                    shippers.distribution_channel_id = distribution_channels.id
                left join account_types on
                    shippers.account_type_id = account_types.id
                left join unmasked_shipper_metadata  as shipper_source on
                    shippers.id = shipper_source.shipper_id
                    and shipper_source.key = 'source'
                left join shipper_setting_agg on
                    shippers.id = shipper_setting_agg.shipper_id
                left join industries on
                    shippers.industry_id = industries.id
                    and lower(shippers.system_id) = industries.system_id
                left join salespersons_enriched on
                    lower(shippers.sales_person) = lower(salespersons_enriched.code)
                    and lower(shippers.system_id) = salespersons_enriched.system_id
                left join shipper_attributes_gdrive as gdrive on
                    shippers.id = gdrive.id
                left join salesforce_account_enriched on
                    shippers.id = salesforce_account_enriched.shipper_id
                where
                    shippers.id is not null
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("shippers.system_id"),
                    "notification_settings": NOTIFICATION_SETTINGS,
                    "salesforce_account_columns": SALESFORCE_ACCOUNT_COLUMNS,
                    "field_sales_teams": {
                        "Affiliate",
                        "BD",
                        "Category",
                        "Community Hyperlocal",
                        "Direct Sales Central Java",
                        "Direct Sales East Java",
                        "Direct Sales HQ",
                        "Direct Sales South Sumatera",
                        "Direct Sales West Java",
                        "Direct Sales West Sumatera",
                        "Direct Sales",
                        "DS-HCM/HN",
                        "DSHQ-RM",
                        "DSHQ_CM",
                        "Greater Manila Area",
                        "HQ",
                        "Metro HCM",
                        "Metro HN",
                        "Mindanao",
                        "Ninja Squad",
                        "North Luzon",
                        "North",
                        "South Luzon",
                        "South",
                        "Telesales",
                        "Territorial East Coast",
                        "Territorial North",
                        "Territorial Sales",
                        "Territorial South",
                        "Territory Sales - Ninja Squad",
                        "Visayas",
                        "FSE",
                        "TSE",
                        "BDM",
                    },
                    "masked_account_creation_source": {
                        "a399f8aa80d2ab99f2efcb0b120f182acf8fd22d6a22366aaafdfaf0d64df9f8": 'DASH_MOBILE',
                        "bebd3bf2e7960f851789cf986a5d22824922c88b78b6761ade3bb17f188e7fef": 'DASH_WEB',
                        "1ce382dc148a7555d0be908bc34baf90747d5d1cded629847dcd8d8e16dcf16f": 'DASH',
                        "12551ae4163d39554d2f00c97be36c4b1296df8d1b40e36892e76fb420f24dcb": 'SALESFORCE',
                        "36509f018af56f67da2d2c06b20fdb89d0d1285d0dddeab442b8f2a33bdd8753": 'PUDO',
                        "636b2094a0dd47da04c34088e9dce0bee15d4fe20fa6a1af2b956fa627a783f2": 'RETAIL'
                    },
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            *
                            , case
                                {%- for name, parent_id_coalesce in reporting_name_to_parent_id_coalesce.items() %}
                                when parent_id_coalesce in ({{ parent_id_coalesce | join(",") }})
                                    then '{{ name }}'
                                {%- endfor %}
                            end as reporting_name

                            

                            , case
                                {%- for item in bu_ae_definitions %}
                                when {{ item['definition'] }} then '{{ item['business_unit'] }}'
                                {%- endfor %}
                                else null
                            end as inferred_business_unit

                            , case
                                {%- for item in bu_ae_definitions %}
                                when {{ item['definition'] }} then '{{ item['acquisition_endpoint'] }}'
                                {%- endfor %}
                                else null
                            end as inferred_acquisition_endpoint

                        from shippers_base

                    )
                    
                    , partnership_shippers_tag as (
                    
                    select 
                        country
                        , shipper_id
                        , parent_shipper_name as platform_name
                        , cast(is_platform as int) as is_platform
                    from partnership_shipper_master_list
                    
                    )
                    , final as (

                        select
                            base.id
                            , base.shipper_name
                            , base.country
                            , base.parent_id
                            , base.parent_name
                            , base.parent_id_coalesce
                            , base.parent_name_coalesce
                            , base.parent_corporate_id
                            , base.parent_corporate_name
                            , partnerships.platform_name
                            , partnerships.is_platform as platform_flag
                            , base.branch_id
                            , base.reporting_name
                            , base.marketplace
                            , base.billing_name
                            , base.sales_person
                            , base.sales_person_code
                            , base.sales_person_team
                            , base.industry
                            , base.distribution_channel_name
                            , base.account_type_name
                            , base.account_creation_source
                            , base.prepaid_account
                            , base.service_level
                            , base.enforce_parcel_pickup_tracking_flag
                            , base.frozen_flag


                            {%- for setting in notification_settings %}
                            , base.{{ setting }}_notification_flag
                            {%- endfor %}

                            , base.pickup_scan_exclusion_flag
                            , base.pickup_scan_exclusion_reason
                            , base.onboarded_date
                            , base.active
                            , base.deleted_date
                            , base.external_ref

                            {%- for column, new_name in salesforce_account_columns.items() %}
                            , base.sf_{%- if new_name %}{{ new_name }}{% else %}{{ column }}{%- endif %}
                            {%- endfor %}

                            , case
                                when base.sales_channel = 'Test' then 'Test'
                                when base.sf_acc_id is not null then base.sales_channel
                                else coalesce(parent.sales_channel, base.sales_channel)
                            end as sales_channel

                            , base.original_sales_channel

                            , coalesce(
                                base.hardcoded_business_unit
                                , parent.inferred_business_unit
                                , base.inferred_business_unit
                            ) as business_unit
                            , coalesce(
                                base.hardcoded_acquisition_endpoint
                                , parent.inferred_acquisition_endpoint
                                , base.inferred_acquisition_endpoint
                            ) as acquisition_endpoint
                            , base.system_id
                            , base.created_month
                        from base
                        left join base as parent on
                            base.parent_id = parent.id
                        left join partnership_shippers_tag partnerships
                            on partnerships.shipper_id = base.parent_id
                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "salesforce_account_columns": SALESFORCE_ACCOUNT_COLUMNS,
                    "bu_ae_definitions": [
                        {
                            "definition": "sales_person_team = 'Test'",
                            "acquisition_endpoint": "Test",
                            "business_unit": "Test",
                        },
                        {
                            "definition": """system_id = 'sg' and account_creation_source = 'DASH'
                            and prepaid_account = 'Post-paid'""",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "account_creation_source in ('DASH', 'RETAIL')",
                            "acquisition_endpoint": "Self-Signup",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "account_creation_source = 'PUDO'",
                            "acquisition_endpoint": "PUDO",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": """system_id = 'my' and sales_person_team in ('Category', 'HQ',
                            'Territorial East Coast', 'Territorial North', 'Territorial South')""",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": """system_id = 'ph' and sales_person_team in ('HQ','Community Hyperlocal',
                            'Greater Manila Area','South Luzon','North Luzon','Visayas','Mindanao', 'DSHQ_CM',
                            'DSHQ-RM')""",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": """system_id = 'vn' and sales_person_team in ('DS-HCM/HN', 'South', 'North',
                            'Metro HN', 'Metro HCM', 'BD')""",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "system_id = 'vn' and sales_person_team = 'Territorial Sales'",
                            "acquisition_endpoint": "Territorial Sales",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": """system_id = 'id' and sales_person_team in ('Direct Sales HQ',
                            'Direct Sales East Java', 'Direct Sales Central Java', 'Direct Sales West Java',
                            'Direct Sales West Sumatera', 'Direct Sales South Sumatera')""",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "system_id = 'sg' and sales_person_team in ('HQ')",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "system_id = 'th' and sales_person_team in ('Direct Sales')",
                            "acquisition_endpoint": "Direct Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": """system_id = 'id' and sales_person_team in
                            ('Territory Sales - Ninja Squad', 'Ninja Squad')""",
                            "acquisition_endpoint": "Ninja Squad",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "sales_person_team in ('Affiliate', 'Ninja Squad')",
                            "acquisition_endpoint": "Affiliate Selling",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "sales_person_team = 'Solutions'",
                            "acquisition_endpoint": "Solutions",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "sales_person_team = 'Ninja Direct'",
                            "acquisition_endpoint": "Ninja Direct",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "sales_person_team = 'B2B'",
                            "acquisition_endpoint": "B2B",
                            "business_unit": "B2B",
                        },
                        {
                            "definition": "sales_person_team = 'Cross Border'",
                            "acquisition_endpoint": "Cross Border",
                            "business_unit": "Cross Border",
                        },
                        {
                            "definition": "parent_id_coalesce in (850024, 849952)",
                            "acquisition_endpoint": "Key Account Management",
                            "business_unit": "Enterprise",
                        },
                        {
                            "definition": "sales_person_team = 'Partnerships'",
                            "acquisition_endpoint": "Key Account Management",
                            "business_unit": "Enterprise",
                        },
                        {
                            "definition": "sales_person_team = 'PUDO'",
                            "acquisition_endpoint": "PUDO",
                            "business_unit": "Small Medium Business",
                        },
                        {
                            "definition": "industry = 'Distribution Points' and system_id in ('sg', 'my', 'ph')",
                            "acquisition_endpoint": "Test",
                            "business_unit": "Test",
                        },
                        {
                            "definition": "industry = 'Distribution Points' and system_id in ('id', 'th')",
                            "acquisition_endpoint": "PUDO",
                            "business_unit": "Small Medium Business",
                        },
                    ],
                    "reporting_name_to_parent_id_coalesce": {
                        "btpn": (814112, 4373292, 5665767, 5665584, 5665750, 5665894),
                        "fahasa": (89812, 339591),
                        "lazada": (
                            341107,
                            341121,
                            48930,
                            324763,
                            79238,
                            341153,
                            14016,
                            109059,
                            283987,
                            291101,
                            429308,
                            837306,
                            341167,
                        ),
                        "sendo": (87922, 1016526, 1270240),
                        "shopee": (
                            160271,
                            1449130,
                            42982,
                            3960,
                            4064,
                            4218,
                            7210,
                            7590,
                            12044,
                            13280,
                            544294,
                            561868,
                            776705,
                            990076,
                            1375272,
                            92200,
                            497542,
                            700865,
                            711147,
                            711245,
                            1069100,
                            216977,
                        ),
                        "shopee_china": (36706, 19322, 776763),
                        "shopee_malaysia": (853272, 1340474),
                        "tiki": (318421, 797637),
                        "tiktok": (7823651, 7717788, 7474545,  9090233, 9078111),
                        "tokopedia": (72594,),
                        "zalora": (44090, 1133246, 3969098, 5047129),
                    },

                    "notification_settings": NOTIFICATION_SETTINGS,
                },
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_ATTRIBUTES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
