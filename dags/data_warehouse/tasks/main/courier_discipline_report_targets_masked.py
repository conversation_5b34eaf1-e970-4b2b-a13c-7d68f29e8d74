import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.COURIER_DISCIPLINE_REPORT_TARGETS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.COURIER_DISCIPLINE_REPORT_TARGETS_MASKED,
    system_ids=(
        constants.SystemID.MY,
        constants.SystemID.PH,
    ),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_TARGETS,
                view_name="targets",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_TARGETS_WEEKEND,
                view_name="targets_weekend",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).PH_LM_MANUAL_REGION_MAPPING,
                view_name="ph_region_mapping",
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                select
                    calendar.date
                    , hubs_enriched.id as hub_id
                    -- For PH, use internal region mapping for Midanao split
                    , coalesce(ph_region_mapping.region, hubs_enriched.region) as region
                    , if(calendar.working_day = 0 or calendar.day = 'Saturday' , 1, 0) as weekend_public_holiday_flag
                    , hubs_enriched.system_id
                    , hubs_enriched.created_month
                from calendar
                left join hubs_enriched
                    on calendar.system_id = hubs_enriched.system_id
                    and (calendar.region = 'national' and hubs_enriched.system_id not in ('my'))
                    or (hubs_enriched.system_id = 'my' and lower(calendar.region) = lower(hubs_enriched.address_city))
                left join ph_region_mapping
                    on ph_region_mapping.hub_id = hubs_enriched.id
                    and hubs_enriched.system_id = 'ph'
                where 1=1
                -- To populate the data for today which is + 1 day from measurement_datetime
                    and calendar.date = date('{{ measurement_datetime }}') + interval '1' day
                -- To populate data only for LM stations. For PH, only entries in Gsheet, for MY, facility type = STATION
                    and ((hubs_enriched.facility_type like '%STATION%' and calendar.system_id = 'my')
                        or (ph_region_mapping.hub_id is not null and calendar.system_id = 'ph'))
                ),

                targets_cte as (
                    select
                        base.date
                        , base.hub_id
                        , base.region
                        , cast(if(base.weekend_public_holiday_flag = 1 
                            , sr_targets_weekend.kpi_target,sr_targets.kpi_target) as double) as success_rate_target
                        , base.system_id
                        , base.created_month
                    from base
                    left join targets as sr_targets
                        on base.system_id = sr_targets.system_id
                        and sr_targets.kpi_name = 'success_rate'
                        and base.region = sr_targets.hub_region
                        and base.date >= sr_targets.start_date
                        and base.date <= sr_targets.end_date
                    left join targets_weekend as sr_targets_weekend
                        on base.system_id = sr_targets_weekend.system_id
                        and sr_targets_weekend.kpi_name = 'success_rate'
                        and base.region = sr_targets_weekend.hub_region
                        and base.date >= sr_targets_weekend.start_date
                        and base.date <= sr_targets_weekend.end_date
                )

                select *
                from targets_cte
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COURIER_DISCIPLINE_REPORT_TARGETS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()