import sys

from jinja2 import Template
from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.dwh_jotform_map import NPS

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.JotformDAG.Task.ENRICHED_NPS_DATA_MASKED + ".py",
    task_name=data_warehouse.JotformDAG.Task.ENRICHED_NPS_DATA_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID, task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID, task_id=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)

# variables to generate query with jinja templates
ORDER_NPS_SUBMISSIONS_COLUMNS = NPS["order_nps_submission_columns"]
DELIVERY_SUCCESS_FEEDBACK_COLUMNS = NPS["delivery_success_feedback_columns"]


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.LookBackRange(None, None)
    if not enable_full_run:
        lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 4)
        input_range = base.CreatedMonthRange(
            min=lookback_ranges.input.min, max=lookback_ranges.input.max, field="nv_created_month"
        )
        lookback_ranges = base.LookBackRange(input=input_range, output=lookback_ranges.output)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.Jotform(input_env, is_masked).DELIVERY_SUCCESS_FEEDBACK,
                view_name="delivery_success_feedback",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.Jotform(input_env, is_masked).ORDER_NPS_SUBMISSIONS,
                view_name="order_nps_submissions",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED, view_name="drivers_enriched"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="order_nps_submissions_enriched",
                jinja_template="""
                -- Note: This form was deprecated on 19th Aug 2020.
                SELECT id AS submission_id
                       , form_id AS jotform_id
                       , created_at AS submission_datetime
                {%- for column in answer_columns %}
                       , {{ column }}
                {%- endfor %}
                       , system_id
                FROM order_nps_submissions
                """,
                jinja_arguments={
                    "answer_columns": [_get_parse_answers_sql(column) for column in ORDER_NPS_SUBMISSIONS_COLUMNS]
                },
            ),
            base.TransformView(
                view_name="delivery_success_feedback_enriched",
                jinja_template="""
                SELECT id AS submission_id
                       , form_id AS jotform_id
                       , created_at AS submission_datetime
                {%- for column in answer_columns %}
                       , {{ column }}
                {%- endfor %}
                       , system_id
                FROM delivery_success_feedback
                """,
                jinja_arguments={
                    "answer_columns": [_get_parse_answers_sql(column) for column in DELIVERY_SUCCESS_FEEDBACK_COLUMNS]
                },
            ),
            base.TransformView(
                view_name="dp_collected",
                jinja_template="""
                SELECT barcode AS tracking_id
                       , system_id
                       , count(1) AS count
                FROM dp_reservations_enriched
                WHERE released_to = 'CUSTOMER'
                GROUP BY 1, 2
                """,
            ),
            base.TransformView(
                view_name="full_survey_data",
                jinja_template="""
                (
                SELECT nps.submission_id
                       , nps.jotform_id
                       , nps.submission_datetime
                       , nps.tracking_id
                       , NULL AS source
                       , nps.nps_score
                       , nps.nps_reason
                       , IF(dp_collected.count >= 1, 1, 0) AS dp_collected_flag
                       , nps.driver_rating
                       , nps.driver_rating_details1
                       , nps.driver_rating_details2
                       , nps.driver_rating_details3
                       , nps.driver_rating_details4
                       , nps.driver_rating_details5
                       , NULL AS delivery_excellence_details1
                       , NULL AS delivery_excellence_details2
                       , NULL AS delivery_excellence_details3
                       , NULL AS delivery_excellence_details4
                       , NULL AS delivery_excellence_details5
                       , NULL AS delivery_improvement_details1
                       , NULL AS delivery_improvement_details2
                       , NULL AS delivery_improvement_details3
                       , NULL AS delivery_improvement_details4
                       , NULL AS delivery_improvement_details5
                       , NULL AS nv_uniform_flag
                       , nps.ninja_box_rating AS ninja_box_rating
                       , nps.ninja_box_rating_details1
                       , nps.ninja_box_rating_details2
                       , nps.ninja_box_rating_details3
                       , nps.ninja_box_rating_details4
                       , nps.ninja_point_rating AS ninja_point_rating
                       , nps.ninja_point_rating_details1
                       , nps.ninja_point_rating_details2
                       , nps.ninja_point_rating_details3
                       , nps.ninja_point_rating_details4
                       , nps.ninja_point_rating_details5
                       , nps.cs_rating
                       , nps.cs_rating_details1
                       , nps.cs_rating_details2
                       , nps.cs_rating_details3
                       , nps.cs_rating_details4
                       , nps.cs_rating_details5
                       , NULL AS feedback
                       , NULL AS notification_updates_rating
                       , NULL AS notification_real_time_updates_flag
                       , NULL AS notification_info_on_point_flag
                       , NULL AS notification_customise_want_flag
                       , NULL AS notification_customise_where_flag
                       , NULL AS notification_no_multi_channels_flag
                       , NULL AS notification_more_frequent_updates_flag
                       , NULL AS notification_less_frequent_updates_flag
                       , NULL AS notification_self_help_capabilities_flag
                       , NULL AS notification_more_order_details_flag
                       , NULL AS notification_malay_language_flag
                       , NULL AS notification_other_comments
                       , nps.system_id
                FROM order_nps_submissions_enriched AS nps
                LEFT JOIN dp_collected ON nps.tracking_id = dp_collected.tracking_id
                    AND nps.system_id = dp_collected.system_id
                )
                UNION ALL
                (
                SELECT submission_id
                       , jotform_id
                       , submission_datetime
                       , tracking_id
                       , source
                       , nps_score
                       , NULL AS nps_reason
                       , dp_collected_flag
                       , driver_rating
                       , NULL AS driver_rating_details1
                       , NULL AS driver_rating_details2
                       , NULL AS driver_rating_details3
                       , NULL AS driver_rating_details4
                       , NULL AS driver_rating_details5
                       , delivery_excellence_details1
                       , delivery_excellence_details2
                       , delivery_excellence_details3
                       , delivery_excellence_details4
                       , delivery_excellence_details5
                       , delivery_improvement_details1
                       , delivery_improvement_details2
                       , delivery_improvement_details3
                       , delivery_improvement_details4
                       , delivery_improvement_details5
                       , nv_uniform_flag
                       , CASE WHEN ninja_box_flag = 1 THEN collection_rating ELSE NULL END AS ninja_box_rating
                       , ninja_box_rating_details1
                       , ninja_box_rating_details2
                       , ninja_box_rating_details3
                       , ninja_box_rating_details4
                       , CASE WHEN ninja_point_flag = 1 THEN collection_rating ELSE NULL END AS ninja_point_rating
                       , ninja_point_rating_details1
                       , ninja_point_rating_details2
                       , ninja_point_rating_details3
                       , ninja_point_rating_details4
                       , ninja_point_rating_details5
                       , NULL AS cs_rating
                       , NULL AS cs_rating_details1
                       , NULL AS cs_rating_details2
                       , NULL AS cs_rating_details3
                       , NULL AS cs_rating_details4
                       , NULL AS cs_rating_details5
                       , feedback
                       , notification_updates_rating
                       , notification_real_time_updates_flag
                       , notification_info_on_point_flag
                       , notification_customise_want_flag
                       , notification_customise_where_flag
                       , notification_no_multi_channels_flag
                       , notification_more_frequent_updates_flag
                       , notification_less_frequent_updates_flag
                       , notification_self_help_capabilities_flag
                       , notification_more_order_details_flag
                       , notification_malay_language_flag
                       , notification_other_comments
                       , system_id
                FROM delivery_success_feedback_enriched
                )
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT survey.system_id AS country
                       , orders.order_id
                       , survey.tracking_id
                       , survey.jotform_id
                       , CAST(survey.submission_id AS bigint) AS submission_id
                       , FROM_UTC_TIMESTAMP(survey.submission_datetime, {{ get_local_timezone }})
                         AS submission_datetime
                       , survey.source
                       , sla.shipper_id
                       , orders.cod_value
                       , orders.delivery_fee
                       , orders.rts_flag
                       , sla.sc_met
                       , sla.sc_measured
                       , CAST(IF(survey.nps_score >= 9, 1, 0) AS bigint) AS promoter_flag
                       , CAST(IF(survey.nps_score >= 7 AND survey.nps_score < 9, 1, 0) AS bigint) AS passive_flag
                       , CAST(IF(survey.nps_score >= 0 AND survey.nps_score < 7, 1, 0) AS bigint) AS detractor_flag
                       , survey.dp_collected_flag
                       , CAST(survey.nps_score AS bigint) AS nps_score
                       , survey.nps_reason
                       , sla.delivery_attempts
                       , drivers.driver_type
                       , orders.delivery_success_datetime
                       , orders.delivery_success_driver_id AS driver_id
                       , orders.delivery_success_hub_id AS driver_hub_id
                       , CAST(survey.driver_rating AS bigint) AS driver_rating
                       , survey.driver_rating_details1
                       , survey.driver_rating_details2
                       , survey.driver_rating_details3
                       , survey.driver_rating_details4
                       , survey.driver_rating_details5
                       , CAST(survey.delivery_excellence_details1 AS bigint) AS delivery_excellence_details1
                       , CAST(survey.delivery_excellence_details2 AS bigint) AS delivery_excellence_details2
                       , CAST(survey.delivery_excellence_details3 AS bigint) AS delivery_excellence_details3
                       , CAST(survey.delivery_excellence_details4 AS bigint) AS delivery_excellence_details4
                       , CAST(survey.delivery_excellence_details5 AS bigint) AS delivery_excellence_details5
                       , CAST(survey.delivery_improvement_details1 AS bigint) AS delivery_improvement_details1
                       , CAST(survey.delivery_improvement_details2 AS bigint) AS delivery_improvement_details2
                       , CAST(survey.delivery_improvement_details3 AS bigint) AS delivery_improvement_details3
                       , CAST(survey.delivery_improvement_details4 AS bigint) AS delivery_improvement_details4
                       , CAST(survey.delivery_improvement_details5 AS bigint) AS delivery_improvement_details5
                       , CAST(survey.nv_uniform_flag AS bigint) AS nv_uniform_flag
                       , CAST(survey.ninja_box_rating AS bigint) AS ninja_box_rating
                       , CAST(survey.ninja_box_rating_details1 AS bigint) AS ninja_box_rating_details1
                       , CAST(survey.ninja_box_rating_details2 AS bigint) AS ninja_box_rating_details2
                       , CAST(survey.ninja_box_rating_details3 AS bigint) AS ninja_box_rating_details3
                       , CAST(survey.ninja_box_rating_details4 AS bigint) AS ninja_box_rating_details4
                       , CAST(survey.ninja_point_rating AS bigint) AS ninja_point_rating
                       , CAST(survey.ninja_point_rating_details1 AS bigint) AS ninja_point_rating_details1
                       , CAST(survey.ninja_point_rating_details2 AS bigint) AS ninja_point_rating_details2
                       , CAST(survey.ninja_point_rating_details3 AS bigint) AS ninja_point_rating_details3
                       , CAST(survey.ninja_point_rating_details4 AS bigint) AS ninja_point_rating_details4
                       , CAST(survey.ninja_point_rating_details5 AS bigint) AS ninja_point_rating_details5
                       , CAST(survey.cs_rating AS bigint) AS cs_rating
                       , survey.cs_rating_details1
                       , survey.cs_rating_details2
                       , survey.cs_rating_details3
                       , survey.cs_rating_details4
                       , survey.cs_rating_details5
                       , survey.feedback
                       , cast(survey.notification_updates_rating as int) as notification_updates_rating
                       , cast(survey.notification_real_time_updates_flag as int) as notification_real_time_updates_flag
                       , cast(survey.notification_info_on_point_flag as int) as notification_info_on_point_flag
                       , cast(survey.notification_customise_want_flag as int) as notification_customise_want_flag
                       , cast(survey.notification_customise_where_flag as int) as notification_customise_where_flag
                       , cast(survey.notification_no_multi_channels_flag as int) as notification_no_multi_channels_flag
                       , cast(
                           survey.notification_more_frequent_updates_flag as int
                       ) as notification_more_frequent_updates_flag
                       , cast(
                           survey.notification_less_frequent_updates_flag as int
                       ) as notification_less_frequent_updates_flag
                       , cast(
                           survey.notification_self_help_capabilities_flag as int
                       ) as notification_self_help_capabilities_flag
                       , cast(
                           survey.notification_more_order_details_flag as int
                        ) as notification_more_order_details_flag
                       , cast(survey.notification_malay_language_flag as int) as notification_malay_language_flag
                       , survey.notification_other_comments
                       , survey.system_id
                       , DATE_FORMAT(survey.submission_datetime, "yyyy-MM") AS created_month
                FROM full_survey_data AS survey
                LEFT JOIN order_milestones AS orders ON survey.tracking_id = orders.tracking_id
                    AND survey.system_id = orders.system_id
                LEFT JOIN transit_time_report AS sla ON survey.tracking_id = sla.tracking_id
                    AND survey.system_id = sla.system_id
                LEFT JOIN drivers_enriched AS drivers ON orders.delivery_success_driver_id = drivers.id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("survey.system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ENRICHED_NPS_DATA,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def _get_parse_answers_sql(column_configs):
    return Template(
        """
        CASE
        {%- for question_id, form_ids in question_id_to_form_ids.items() %}
            WHEN form_id in ({{ form_ids | join(', ') }})
            THEN
        {%- if type == 'boolean' %}
                CASE
                    WHEN get_json_object(answers, '$.{{ question_id }}.answer') like '%{{ detail }}%'
                    THEN 1
                    WHEN get_json_object(answers, '$.{{ question_id }}.answer') IS NULL THEN NULL
                    ELSE 0
                END

        {%- elif type == 'emoji5' %}
                CASE
                {%- for id, value in emoji5_mapping.items() %}
                    WHEN get_json_object(answers, '$.{{ question_id }}.answer.{{ detail }}') = {{ id }}
                    THEN {{ value }}
                {%- endfor %}
                 END

        {%- elif type == 'subtype' %}
                get_json_object(answers, '$.{{ question_id }}.answer.{{ detail }}')

        {%- elif type == 'flat' %}
                get_json_object(answers, '$.{{ question_id }}.answer')
        {%- endif %}
        {%- endfor %}
        END AS {{ name }}
        """
    ).render({"emoji5_mapping": {1: 1, 2: 2, 4: 3, 6: 4, 7: 5}, **column_configs})


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()