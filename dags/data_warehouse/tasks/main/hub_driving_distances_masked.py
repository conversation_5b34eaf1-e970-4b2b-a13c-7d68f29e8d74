import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.HUB_DRIVING_DISTANCES_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.HUB_DRIVING_DISTANCES_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
    ),
)

def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_MY,
                view_name="hub_driving_distances_my",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_PH,
                view_name="hub_driving_distances_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_SG,
                view_name="hub_driving_distances_sg",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_TH,
                view_name="hub_driving_distances_th",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_VN,
                view_name="hub_driving_distances_vn",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_ID_1,
                view_name="hub_driving_distances_id_1",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_ID_2,
                view_name="hub_driving_distances_id_2",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).HUB_DRIVING_DISTANCES_ID_3,
                view_name="hub_driving_distances_id_3",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with my_distances as (
                
                    select
                        'my' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_my
                
                ),
                ph_distances as (
                
                    select
                        'ph' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_ph
                
                ),
                sg_distances as (
                
                    select
                        'sg' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_sg
                
                ),
                th_distances as (
                
                    select
                        'th' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_th
                
                ),
                vn_distances as (
                
                    select
                        'vn' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_vn
                
                ),
                id_distances_1 as (
                
                    select
                        'id' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_id_1
                
                ),
                id_distances_2 as (
                
                    select
                        'id' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_id_2
                
                ),
                id_distances_3 as (
                
                    select
                        'id' as system_id
                        , origin_id
                        , dest_id
                        , distance
                    from hub_driving_distances_id_3
                
                ),
                combined_distances as (
                
                    select * from my_distances
                    UNION ALL
                    select * from ph_distances
                    UNION ALL
                    select * from sg_distances
                    UNION ALL
                    select * from th_distances
                    UNION ALL
                    select * from vn_distances
                    UNION ALL
                    select * from id_distances_1
                    UNION ALL
                    select * from id_distances_2
                    UNION ALL
                    select * from id_distances_3
                
                ),
                final as (
                
                    select
                        combined_distances.system_id
                        , combined_distances.origin_id as origin_id
                        , origin_hub.name as origin_name
                        , origin_hub.latitude as origin_lat
                        , origin_hub.longitude as origin_long
                        , combined_distances.dest_id as dest_id
                        , dest_hub.name as dest_name
                        , dest_hub.latitude as dest_lat
                        , dest_hub.longitude as dest_long
                        , combined_distances.distance
                    from combined_distances
                    left join hubs_enriched as origin_hub
                        on combined_distances.system_id = origin_hub.system_id
                        and combined_distances.origin_id = origin_hub.id
                    left join hubs_enriched as dest_hub
                        on combined_distances.system_id = dest_hub.system_id
                        and combined_distances.dest_id = dest_hub.id

                )

                select * from final

                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=parquet_tables_masked.DataWarehouse(env).HUB_DRIVING_DISTANCES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()

