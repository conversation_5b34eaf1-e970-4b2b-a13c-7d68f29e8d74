import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_BASE_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS_BASE,
                view_name="order_movements_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                SELECT base.order_event_id
                       , base.system_id AS country
                       , base.order_id
                       , orders.granular_status
                       , base.location_type
                       , base.location_id
                       , cast(coalesce(base.hub_id, route.hub_id) as bigint) AS hub_id
                       , base.event_datetime AS entry_datetime
                       , LEAD(base.event_datetime
                              , 1
                              , IF(orders.granular_status not in ('{{ terminal_statuses | join("', '") }}')
                                   , from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}')
                                   , NULL)) OVER(PARTITION BY base.order_id, base.system_id
                                                 ORDER BY base.event_datetime, base.order_event_id) AS exit_datetime
                       , base.created_month
                FROM order_movements_base AS base
                LEFT JOIN orders_enriched AS orders ON orders.order_id = base.order_id
                LEFT JOIN route_logs AS route
                    ON route.legacy_id = base.location_id
                    AND base.location_type = 'ROUTE'
                    AND route.system_id = '{{ system_id }}'
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                    "terminal_statuses": {"Completed", "Returned to Sender", "Transferred to 3PL", "Cancelled"},
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    SELECT order_event_id
                           , country
                           , order_id
                           , granular_status
                           , location_type
                           , location_id
                           , hub_id
                           , entry_datetime
                           , exit_datetime
                           , cast(round((to_unix_timestamp(exit_datetime,'yyyy-MM-dd HH:mm:ss')
                                    - to_unix_timestamp(entry_datetime, 'yyyy-MM-dd HH:mm:ss'))/60
                                    , 0) AS bigint) AS duration_minutes
                           , datediff(exit_datetime, entry_datetime) AS duration_days
                           , created_month
                    FROM base
                    """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_MOVEMENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
