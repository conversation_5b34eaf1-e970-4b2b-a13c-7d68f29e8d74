import sys

from jinja2 import Template
from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SERVICE_BREACH_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SERVICE_BREACH_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.OrderSLADAG.Task.SHIPPER_CLAIMS_REPORT_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID, task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)

ID_DEFAULT_CLAIMS_AMOUNT = "coalesce(10 * orders.delivery_fee * greatest(LEAST(orders.weight, 30), 1), 0)"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_CLAIMS_REPORT,
                view_name="shipper_claims_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="recovery_tickets",
                jinja_template="""
                SELECT order_id
                       , type
                       , max_by(id, resolution_datetime) AS id
                       , max(resolution_datetime) AS resolution_datetime
                FROM pets_tickets_enriched
                WHERE type IN ('MISSING', 'DAMAGED')
                      AND outcome rlike '^(LOST|NV LIABLE)|DAMAGE'
                GROUP BY 1, 2
                """,
            ),
            base.TransformView(
                view_name="sla_breach",
                jinja_template="""
                SELECT order_id
                       , start_clock_date
                       , sla_date
                       , rts_sla_date
                       , IF(nc_met = 0, 1, 0) AS nc_breach_flag
                       , IF(rts_nc_met = 0, 1, 0) AS rts_nc_breach_flag
                FROM shipper_claims_report
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT orders.country
                       , orders.order_id
                       , orders.tracking_id
                       , shippers.id AS shipper_id
                       , shippers.shipper_name
                       , shippers.parent_id
                       , shippers.parent_name
                       , shippers.parent_id_coalesce
                       , shippers.parent_name_coalesce
                       , shippers.sales_person
                       , shippers.sales_person_team
                       , shippers.business_unit
                       , shippers.acquisition_endpoint
                       , orders.cod_value
                       , orders.insurance_value
                       , orders.gst_fee
                       , orders.delivery_fee
                       , orders.weight
                       , orders.granular_status
                       , orders.creation_datetime
                       , orders.pickup_datetime
                       , orders.inbound_datetime
                       , sla.start_clock_date
                       , sla.sla_date
                       , sla.rts_sla_date
                       , missing_tickets.resolution_datetime AS last_missing_ticket_resolution_datetime
                       , damaged_tickets.resolution_datetime AS last_damaged_ticket_resolution_datetime
                       , missing_tickets.id AS last_missing_ticket_id
                       , damaged_tickets.id AS last_damaged_ticket_id
                       , date({{ claims_date_logic }}) AS claims_date
                       , IF(missing_tickets.id IS NOT NULL, 1, 0) AS missing_flag
                       , IF(damaged_tickets.id IS NOT NULL, 1, 0) AS damaged_flag
                       , sla.nc_breach_flag
                       , sla.rts_nc_breach_flag
                       , CASE
                             WHEN {{ claims_date_logic }} = sla.sla_date THEN 'forward_sla_breach'
                             WHEN {{ claims_date_logic }} = sla.rts_sla_date THEN 'return_sla_breach'
                             WHEN {{ claims_date_logic }} = missing_tickets.resolution_datetime THEN 'missing'
                             WHEN {{ claims_date_logic }} = damaged_tickets.resolution_datetime THEN 'damaged'
                         END AS claims_type
                       , CASE
                {%- for config in claims_amount_config[system_id] %}
                             WHEN {{ config['filter'] }}
                             THEN {{ config['definition'] }}
                {%- endfor %}
                         END AS estimated_claims_amount
                       , orders.created_month
                FROM order_milestones AS orders
                LEFT JOIN shipper_attributes AS shippers ON shippers.id = orders.shipper_id
                LEFT JOIN recovery_tickets AS missing_tickets ON missing_tickets.order_id = orders.order_id
                    AND missing_tickets.type = 'MISSING'
                LEFT JOIN recovery_tickets AS damaged_tickets ON damaged_tickets.order_id = orders.order_id
                    AND damaged_tickets.type = 'DAMAGED'
                LEFT JOIN sla_breach AS sla ON sla.order_id = orders.order_id
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "claims_date_logic": """
                        CASE
                            WHEN sla.nc_breach_flag = 0
                                 AND sla.rts_nc_breach_flag = 0
                                 AND missing_tickets.id IS NULL
                                 AND damaged_tickets.id IS NULL
                            THEN NULL
                            ELSE LEAST(sla.sla_date
                                       , sla.rts_sla_date
                                       , missing_tickets.resolution_datetime
                                       , damaged_tickets.resolution_datetime)
                        END
                        """,
                    "claims_amount_config": {
                        "id": [
                            {
                                # Mitra
                                "filter": "orders.tracking_id like 'NVIDNINJA%'",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=10_000_000,
                                    uninsured_claims_cap=2_500_000,
                                ),
                            },
                            {
                                # Ninjapack
                                "filter": "orders.ninjapack_flag = 1",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                            {
                                "filter": """
                                    orders.shipper_id IN (
                                        568488, 118835, 1222616, 487072,
                                        605143, 814112, 819452, 113511, 75524)
                                    """,
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False, insurance_claims_cap=None, uninsured_claims_cap=None
                                ),
                            },
                            {
                                "filter": "orders.shipper_id IN (1107978, 857884)",
                                "definition": f"""
                                    CASE
                                        WHEN {ID_DEFAULT_CLAIMS_AMOUNT} > 0 THEN {ID_DEFAULT_CLAIMS_AMOUNT} + {50_000}
                                        ELSE 0
                                    END
                                    """,
                            },
                            {
                                "filter": "orders.shipper_id IN (703495, 1215982)",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                            {
                                "filter": "orders.shipper_id IN (72746, 75698)",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True, insurance_claims_cap=None, uninsured_claims_cap=1_000_000
                                ),
                            },
                            {
                                "filter": "orders.shipper_id = 74432",
                                "definition": f"""
                                    CASE
                                        WHEN orders.insurance_value > 0
                                        THEN LEAST({ID_DEFAULT_CLAIMS_AMOUNT}, {1_000_000}, orders.insurance_value)
                                        ELSE LEAST({ID_DEFAULT_CLAIMS_AMOUNT}, {1_000_000})
                                    END
                                    """,
                            },
                            {
                                "filter": "orders.shipper_id IN (73950, 833158)",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=5_000_000,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                            {
                                "filter": """
                                    orders.shipper_id IN (
                                        1216754, 1217420, 1217464, 1166792, 1164742, 1203028, 1152834,
                                        1042696, 1025502, 1393956, 984342, 996568, 959844, 1105206,
                                        1126852, 1067542, 1101094, 1116258, 1140420, 919274, 1230742,
                                        1244152, 1259068, 758087, 409286, 800907, 780823)
                                    """,
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False, insurance_claims_cap=None, uninsured_claims_cap=750_000
                                ),
                            },
                            {
                                "filter": "orders.shipper_id IN (341121, 341107, 357387)",
                                "definition": f"""
                                    CASE
                                        WHEN orders.cod_value > 0 THEN LEAST(orders.cod_value, {1_000_000})
                                        WHEN orders.insurance_value > 0 THEN LEAST(orders.insurance_value, {1_000_000})
                                        ELSE 0.15 * {1_000_000}
                                    END
                                    """,
                            },
                            {
                                "filter": "orders.shipper_id IN (168291, 1184280, 1105366, 1244192)",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=10_000_000,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                            {
                                "filter": "shippers.parent_id_coalesce IN (68104, 72594, 75894, 216977)",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                            {
                                "filter": "shippers.parent_id_coalesce = 546164",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True, insurance_claims_cap=None, uninsured_claims_cap=1_000_000
                                ),
                            },
                            {
                                "filter": "shippers.parent_id_coalesce = 341107",
                                "definition": f"""
                                    CASE
                                        WHEN insurance_value > 0 THEN LEAST(insurance_value, {1_000_000})
                                        WHEN cod_value > 0 THEN LEAST(cod_value, {4_350_000})
                                        ELSE {170_000}
                                    END
                                    """,
                            },
                            {
                                # Ninja Squad
                                "filter": "shippers.sales_person_team = 'Ninja Squad'",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=10_000_000,
                                    uninsured_claims_cap=2_500_000,
                                ),
                            },
                            {
                                # Default
                                "filter": "TRUE",
                                "definition": _get_id_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    uninsured_claims_cap=1_000_000,
                                ),
                            },
                        ],
                        "my": [
                            {
                                # Lazada
                                "filter": "shippers.parent_id_coalesce IN (48930, 324763)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=500,
                                ),
                            },
                            {
                                # Shopee China
                                "filter": "shippers.parent_id_coalesce IN (36706, 19322, 776763)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=200,
                                ),
                            },
                            {
                                # Shopee Malaysia
                                "filter": "shippers.parent_id_coalesce = 853272",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=200,
                                ),
                            },
                            {
                                # Zalora
                                "filter": "shippers.parent_id_coalesce IN (44090, 1133246)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=400,
                                ),
                            },
                            {
                                "filter": "orders.shipper_id IN (55798, 55368, 55292, 159449)",
                                "definition": _get_my_claims_logic(
                                    uninsured_claims_cap=200, insurance_claims_cap=4_500
                                ),
                            },
                            {
                                # Cross Border
                                "filter": "shippers.business_unit = 'Cross Border'",
                                "definition": _get_my_claims_logic(
                                    uninsured_claims_cap=200, insurance_claims_cap=2_500
                                ),
                            },
                            {
                                # Small Medium
                                "filter": "shippers.business_unit = 'Small Medium Business'",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=False,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=200,
                                ),
                            },
                            {
                                # B2B
                                "filter": "shippers.business_unit = 'B2B'",
                                "definition": _get_my_claims_logic(
                                    uninsured_claims_cap=200, insurance_claims_cap=5_000
                                ),
                            },
                        ],
                        "ph": [
                            {
                                # Lazada
                                "filter": "shippers.parent_id_coalesce = 341153",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=False,
                                    uninsured_claims_cap=None,
                                ),
                            },
                            {
                                # Default
                                "filter": "TRUE",
                                "definition": "coalesce(orders.insurance_value, orders.cod_value)",
                            },
                        ],
                        "sg": [
                            {
                                # Ninjapack
                                "filter": "orders.ninjapack_flag = 1",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=50,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=50,
                                ),
                            },
                            {
                                # Lazada
                                "filter": """
                                    shippers.parent_id_coalesce IN (14016, 109059, 283987, 291101, 429308, 837306)
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=700,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=700,
                                ),
                            },
                            {
                                # Shopee
                                "filter": """
                                    shippers.parent_id_coalesce IN (42982, 3960, 4064, 4218, 7210, 7590, 12044, 13280
                                                                    , 544294, 561868, 776705, 990076, 1375272)
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=200,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=200,
                                ),
                            },
                            {
                                # Partnership
                                "filter": """
                                    shippers.business_unit = 'Enterprise'
                                    AND shippers.acquisition_endpoint = 'Key Account Management'
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=100,
                                ),
                            },
                            {
                                # Cross Border
                                "filter": "shippers.business_unit = 'Cross Border'",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=100,
                                ),
                            },
                            {
                                # Small Medium
                                "filter": "shippers.business_unit = 'Small Medium Business'",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=100,
                                ),
                            },
                        ],
                        "th": [
                            {
                                # Shopee
                                "filter": """
                                    shippers.parent_id_coalesce IN (92200, 497542, 700865, 711147, 711245, 1069100)
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=2_000,
                                ),
                            },
                            {
                                # Lineman
                                "filter": "shippers.parent_id_coalesce = 367987",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=3_000,
                                ),
                            },
                            {
                                # Partnership
                                "filter": """
                                    shippers.business_unit = 'Enterprise'
                                    AND shippers.acquisition_endpoint = 'Key Account Management'
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=50_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=2_000,
                                ),
                            },
                            {
                                # Small Medium
                                "filter": "shippers.business_unit = 'Small Medium Business'",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=2_000,
                                ),
                            },
                            {
                                # Aggregator
                                "filter": """
                                    shippers.business_unit = 'Enterprise'
                                    AND shippers.acquisition_endpoint = 'Aggregator'
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=2_000,
                                ),
                            },
                            {
                                # Cross Border
                                "filter": "shippers.business_unit = 'Cross Border'",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=100_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=2_000,
                                ),
                            },
                        ],
                        "vn": [
                            {
                                # Lazada
                                "filter": "shippers.parent_id_coalesce IN (89648, 89658, 90154, 341167, 341175)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=None,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap=None,
                                ),
                            },
                            {
                                # Tiki
                                "filter": "shippers.parent_id_coalesce IN (318421, 323245, 354903, 797637, 2266410)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=50_000_000,
                                    has_uninsured_claims=False,
                                    uninsured_claims_cap=None,
                                ),
                            },
                            {
                                # Sendo
                                "filter": "shippers.parent_id_coalesce IN (90048, 87922, 1016526, 1270240)",
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=30_000_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap="orders.delivery_fee * 4",
                                ),
                            },
                            {
                                # Ninja Easy
                                "filter": """
                                    shippers.business_unit = 'Small Medium Business'
                                    AND shippers.acquisition_endpoint = 'Ninja Easy'
                                    """,
                                "definition": _get_default_claims_logic(
                                    has_insurance_claims=True,
                                    insurance_claims_cap=30_000_000,
                                    has_uninsured_claims=True,
                                    uninsured_claims_cap="orders.delivery_fee * 10",
                                ),
                            },
                        ],
                    },
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SERVICE_BREACH_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def _get_default_claims_logic(has_insurance_claims, insurance_claims_cap, has_uninsured_claims, uninsured_claims_cap):
    """
    Returns a SQL snippet that calculates estimated_claims_amount.

    :param has_insurance_claims:        Indicates whether the order is eligible for insurance claims.
    :param insurance_claims_cap:        Maximum amount claimable for orders eligible for insurance claims. None
                                        indicates no limit.
    :param has_uninsured_claims:        Indicates whether the order is eligible for claims if it is uninsured.
    :param uninsured_claims_cap:        Maximum amount claimable for orders with no insurance. None indicates no limit.
    """
    return Template(
        """
        CASE
        {%- if has_insurance_claims %}
            WHEN orders.insurance_value > 0
        {%- if insurance_claims_cap %}
            THEN LEAST(orders.insurance_value, {{ insurance_claims_cap }})
        {%- else %}
            THEN orders.insurance_value
        {%- endif %}
        {%- endif %}
        {%- if has_uninsured_claims %}
            WHEN orders.cod_value > 0
        {%- if uninsured_claims_cap %}
            THEN LEAST(orders.cod_value, {{ uninsured_claims_cap }})
        {%- else %}
            THEN orders.cod_value
        {%- endif %}
        {%- endif %}
        {%- if uninsured_claims_cap %}
            ELSE 0.5 * {{ uninsured_claims_cap }}
        {%- else %}
            ELSE 0
        {%- endif %}
        END
        """
    ).render(
        {
            "has_insurance_claims": has_insurance_claims,
            "insurance_claims_cap": insurance_claims_cap,
            "has_uninsured_claims": has_uninsured_claims,
            "uninsured_claims_cap": uninsured_claims_cap,
        }
    )


def _get_id_claims_logic(has_insurance_claims, insurance_claims_cap, uninsured_claims_cap):
    """
    Returns a SQL snippet that calculates estimated_claims_amount for some ID shippers.

    :param has_insurance_claims:        Indicates whether the order is eligible for insurance claims.
    :param insurance_claims_cap:        Maximum amount claimable for orders eligible for insurance claims. None
                                        indicates no limit.
    :param uninsured_claims_cap:        Maximum amount claimable for orders with no insurance. None indicates no limit.
    """
    return Template(
        """
        CASE
            WHEN FALSE THEN NULL
        {%- if has_insurance_claims %}
            WHEN orders.insurance_value > 0
        {%- if insurance_claims_cap %}
            THEN LEAST(orders.insurance_value, {{ insurance_claims_cap }})
        {%- else %}
            THEN orders.insurance_value
        {%- endif %}
        {%- endif %}
        {%- if uninsured_claims_cap %}
            ELSE LEAST({{ default_logic }}, {{ uninsured_claims_cap }})
        {%- else %}
            ELSE {{ default_logic }}
        {%- endif %}
        END
        """
    ).render(
        {
            "default_logic": ID_DEFAULT_CLAIMS_AMOUNT,
            "has_insurance_claims": has_insurance_claims,
            "insurance_claims_cap": insurance_claims_cap,
            "uninsured_claims_cap": uninsured_claims_cap,
        }
    )


def _get_my_claims_logic(insurance_claims_cap, uninsured_claims_cap):
    """
    Returns a SQL snippet that calculates estimated_claims_amount for some MY shippers.

    :param insurance_claims_cap:        Maximum amount claimable for orders eligible for insurance claims. None
                                        indicates no limit.
    :param uninsured_claims_cap:        Maximum amount claimable for orders with no insurance. None indicates no limit.
    """
    # Orders with insurance_value less than min_insured_value are considered to be uninsured. This is to filter out
    # dirty values where shippers set an insurance_value that is too low. Only applies to orders with COD.
    min_insured_value = uninsured_claims_cap
    logic = f"""
        CASE
            WHEN coalesce(orders.cod_value, 0) = 0 AND orders.insurance_value > 0
            THEN LEAST(orders.insurance_value, {insurance_claims_cap})
            WHEN orders.cod_value > 0 AND orders.insurance_value > {min_insured_value}
            THEN LEAST(orders.cod_value, orders.insurance_value, {insurance_claims_cap})
            WHEN orders.cod_value > 0
                 AND (orders.insurance_value <= {min_insured_value} OR orders.insurance_value IS NULL)
            THEN LEAST(orders.cod_value, {uninsured_claims_cap})
            ELSE 0.5 * {uninsured_claims_cap}
        END
        """
    return logic


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
