import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_DAILY_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_DAILY_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.CISP_COMPLETION_REPORT_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 5)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CISP_COMPLETION_REPORT,
                view_name="cisp_completion_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with

                    {%- for day in range(7) %}

                        {%- if not loop.first %}, {% endif %}

                        day{{ day }} as (

                            select
                                n{{ day }}_cutoff_date as report_date
                                , dest_zone
                                , dest_hub_id
                                , sum(n{{ day }}_measured) as n{{ day }}_measured_count
                                , sum(n{{ day }}_met_flag) as n{{ day }}_met_count
                            from cisp_completion_report
                            group by {{ range(1, 4) | join(',') }}

                        )

                    {%- endfor %}
                    , day_agg as (

                        select
                            coalesce(
                                day0.report_date
                                {%- for day in range(1, 7) %} ,day{{ day }}.report_date {%- endfor %}
                            ) as report_date
                            , coalesce(
                                day0.dest_zone
                                {%- for day in range(1, 7) %} ,day{{ day }}.dest_zone {%- endfor %}
                            ) as dest_zone
                            , coalesce(
                                day0.dest_hub_id
                                {%- for day in range(1, 7) %} ,day{{ day }}.dest_hub_id {%- endfor %}
                            ) as dest_hub_id
                            {%- for day in range(7) %}

                            , sum(coalesce(n{{ day }}_measured_count, 0)) as n{{ day }}_measured_count
                            , sum(coalesce(n{{ day }}_met_count, 0)) as n{{ day }}_met_count

                            {%- endfor %}

                        from day0


                        {%- for day in range(1, 7) %}

                        full outer join day{{ day }} on
                            day0.dest_hub_id = day{{ day }}.dest_hub_id
                            and day0.dest_zone = day{{ day }}.dest_zone
                            and day0.report_date = day{{ day }}.report_date

                        {%- endfor %}

                        group by {{ range(1, 4) | join(',') }}
                    )
                    , final as (
                        select
                            report_date
                            , dest_zone
                            , dest_hub_id

                            {%- for day in range(7) %}

                            , n{{ day }}_measured_count
                            , n{{ day }}_met_count

                            {%- endfor %}
                            , hubs.name as dest_hub_name
                            , hubs.region as dest_hub_region
                            , hubs.facility_type as dest_hub_facility_type
                            , date_format(report_date, 'yyyy-MM') as created_month
                        from day_agg
                        left join hubs_enriched as hubs on
                            day_agg.dest_hub_id = hubs.id
                    )

                select
                    *
                from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CISP_COMPLETION_REPORT_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
