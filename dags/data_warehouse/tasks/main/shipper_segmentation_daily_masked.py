# The output from this task is used for exporting to SFMC only. It should not be exposed to Metabase or used for
# analytics. As such, column naming in this table also need not follow DWH conventions.
import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.SHIPPER_SEGMENTATION_DAILY_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.SHIPPER_SEGMENTATION_DAILY_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.EberDAG.DAG_ID,
            task_id=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_LEADS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
        ),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)

# jinja variables
query_columns = """
    measurement_datetime
    , account_id
    , account_name
    , lead_id
    , shipper_id
    , contact_id
    , email
    , sales_channel
    , onboarded_date
    , first_order_completion_date
    , alive_probability
    , orders_30d
    , orders_90d
    , lifecycle
    , potential_ppm
    , shipper_tier
    , nb_referral_count
    , last_referral_date
    , nr_redemption_count
    , last_redemption_date
    , system_id
"""


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shipper_prod_gl",
            ),
            base.InputTable(
                path=delta_tables.Eber(input_env, is_masked).POINT_TRANSACTIONS,
                view_name="point_transactions",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_USERS,
                view_name="ninja_rewards_users",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_BUDDIES_LEADS,
                view_name="ninja_buddies_leads",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CONTACT_ENRICHED,
                view_name="salesforce_contact_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_LEAD_ENRICHED,
                view_name="salesforce_lead_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="nr_enriched",
                jinja_template="""
                select
                    from_utc_timestamp(created_at, {{ get_local_timezone }}) as redemption_date
                    , shippers_enriched.sf_acc_id as account_id
                    , case
                        when sales_channel = 'Self Serve' and lower(account_creation_source) like '%mobile%'
                            then 'DASH Mobile'
                        when sales_channel = 'Self Serve' and lower(account_creation_source) not like '%mobile%'
                            then 'DASH Lite'
                    else sales_channel end as sales_channel
                    , count(get_json_object(coupon_issued, '$.main_coupon.id')) as nr_redemption_count
                from point_transactions
                left join ninja_rewards_users
                    on point_transactions.user_id = ninja_rewards_users.id
                left join shippers_enriched
                    on ninja_rewards_users.shipper_id = shippers_enriched.id
                where type = 'redeemed'
                    and void = false
                    and user_id in (
                        select id from ninja_rewards_users where current_member_tier not in ('Sub', 'Contacts')
                    )
                group by 1,2,3
                order by 1,2,3
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("point_transactions.system_id"),
                },
            ),
            base.TransformView(
                view_name="nb_enriched",
                jinja_template="""
                select date_trunc('day', nb_lead_enriched.lead_creation_datetime) as lead_creation_date
                    , shippers_enriched.sf_acc_id as account_id
                    , case
                        when sales_channel = 'Self Serve' and lower(account_creation_source) like '%mobile%'
                            then 'DASH Mobile'
                        when sales_channel = 'Self Serve' and lower(account_creation_source) not like '%mobile%'
                            then 'DASH Lite'
                    else sales_channel end as sales_channel
                    , count(nb_lead_enriched.lead_id) as nb_referral_count
                from ninja_buddies_leads as nb_lead_enriched
                left join shippers_enriched
                    on nb_lead_enriched.system_id = shippers_enriched.system_id
                    and nb_lead_enriched.buddy_shipper_id = shippers_enriched.id
                group by 1,2,3
                order by 1,2,3
                """,
            ),
            base.TransformView(
                view_name="last_redemption_tb",
                jinja_template="""
                select account_id
                    , max(redemption_date) filter(where nr_redemption_count>0) as last_redemption_date
                from nr_enriched
                where redemption_date <= date('{{ measurement_datetime_utc }}')
                group by 1
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="last_referral_tb",
                jinja_template="""
                select account_id
                    , max(lead_creation_date) filter(where nb_referral_count>0) as last_referral_date
                from nb_enriched
                where lead_creation_date <= date('{{ measurement_datetime_utc }}')
                group by 1
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="full_base",
                jinja_template="""
                with 30d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_30d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '30' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , 90d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_90d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '90' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , latest_contact as (
                    select account_id
                        , max_by(id, creation_datetime) as contact_id
                        , max_by(phone, creation_datetime) as phone
                        , max_by(email, creation_datetime) as email
                    from salesforce_contact_enriched
                    where email != 'None' and email is not null
                    group by 1
                )

                -- deduplicate multiple lead mapping to sf_acc_id
                , first_lead as (
                    select account_id
                        , min_by(sl.id, sl.creation_datetime) as lead_id
                    from salesforce_account_enriched as sf
                    left join salesforce_lead_enriched as sl
                        on sf.id = sl.account_id
                    group by 1
                )
                , individual_shipper as (
                    select sf.id as account_id
                        , sf.name as account_name
                        , first_lead.lead_id
                        , sf.shipper_id
                        , sf.parent_acc_id_coalesce as sf_parent_acc_id_coalesce
                        , sf.parent_acc_name_coalesce as sf_parent_acc_name_coalesce
                        , latest_contact.contact_id
                        , latest_contact.phone
                        , latest_contact.email
                        , sf.system_id
                        , case
                            when se.sales_channel = 'Self Serve'
                                and lower(se.account_creation_source) not like '%mobile%'
                                then 'DASH Lite'
                            when se.sales_channel = 'Self Serve'
                                and lower(se.account_creation_source) like '%mobile%'
                                then 'DASH Mobile'
                        else se.sales_channel end as sales_channel
                        , se.account_creation_source
                        , se.onboarded_date
                        , se.first_order_completion_date
                        , se.alive_probability
                        , 30d_tb.orders_30d
                        , 90d_tb.orders_90d
                    from salesforce_account_enriched as sf
                    left join 90d_tb
                        on sf.shipper_id = 90d_tb.shipper_id
                    left join 30d_tb
                        on sf.shipper_id = 30d_tb.shipper_id
                    left join shippers_enriched as se
                        on sf.shipper_id = se.id
                    left join first_lead
                        on sf.id = first_lead.account_id
                    left join latest_contact
                        on sf.id = latest_contact.account_id

                )

                select *
                from individual_shipper
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                select date('{{ measurement_datetime_utc }}') as measurement_datetime
                    , *
                from full_base
                    where sales_channel in ('DASH Lite', 'DASH Mobile', 'Field Sales', 'Partnerships')
                       and (
                            -- filter to include dormant accounts
                            (first_order_completion_date is null
                            and onboarded_date >=date('{{ measurement_datetime_utc }}') - interval '180' day
                            and onboarded_date <=date('{{ measurement_datetime_utc }}'))
                            or
                            -- filter to exclude inactive shippers
                            (orders_90d is not null)
                            )
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="lite_interim",
                jinja_template="""
                with interim as (
                    select base.measurement_datetime
                        , base.lead_id
                        , base.shipper_id
                        , base.account_id
                        , base.account_name
                        , base.contact_id
                        , base.email
                        , base.system_id
                        , base.sales_channel
                        , min(base.onboarded_date) as onboarded_date
                        , min(base.first_order_completion_date) as first_order_completion_date
                        , max(base.alive_probability) as alive_probability
                        , sum(base.orders_30d) as orders_30d
                        , sum(base.orders_90d) as orders_90d
                    from base
                    where sales_channel = 'DASH Lite'
                    group by {{ range(1, 10) | join(',') }}
                )

                , lifecycle_tb as (
                    select measurement_datetime
                        , interim.lead_id
                        , interim.shipper_id
                        , interim.account_id
                        , interim.account_name
                        , interim.contact_id
                        , interim.email
                        , system_id
                        , nb_referral_count
                        , last_referral_date
                        , nr_redemption_count
                        , last_redemption_date
                        , onboarded_date
                        , interim.sales_channel
                        , first_order_completion_date
                        , orders_30d
                        , orders_90d
                        , alive_probability
                        , case
                            when onboarded_date >= date('{{ measurement_datetime_utc }}') - interval '180' day
                                and first_order_completion_date is null
                                then '1. dormant'

                            when first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day
                            then '2. new'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >= 1
                                and alive_probability > 0.8
                                then '3. active'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >=1
                                and alive_probability <= 0.8
                                then '4. at_risk'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d = 0 or orders_30d is null)
                                and orders_90d >= 1
                                then '5. lapsed'

                        else 'inactive' end as lifecycle
                    from interim
                    left join nb_enriched
                        on interim.measurement_datetime = nb_enriched.lead_creation_date
                        and interim.account_id = nb_enriched.account_id
                        and interim.sales_channel = nb_enriched.sales_channel
                    left join nr_enriched
                        on interim.measurement_datetime = nr_enriched.redemption_date
                        and interim.account_id = nr_enriched.account_id
                        and interim.sales_channel = nr_enriched.sales_channel
                    left join last_referral_tb
                        on interim.account_id = last_referral_tb.account_id
                    left join last_redemption_tb
                        on interim.account_id = last_redemption_tb.account_id
                )

                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id, shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                        and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                    )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm

                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 100 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 70 then '1. VIP'
                                when potential_ppm > 25 and potential_ppm <= 70 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 25 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 110 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 150 then '1. VIP'
                                when potential_ppm > 45 and potential_ppm <= 150 then '2. high'
                                when potential_ppm > 10 and potential_ppm <= 45 then '3. medium'
                                when potential_ppm <= 10 then '4. standard'
                            else '5. unknown' end
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="mobile_interim",
                jinja_template="""
                with interim as (
                    select base.measurement_datetime
                        , base.lead_id
                        , base.shipper_id
                        , base.account_id
                        , base.account_name
                        , base.contact_id
                        , base.email
                        , base.system_id
                        , base.sales_channel
                        , min(base.onboarded_date) as onboarded_date
                        , min(base.first_order_completion_date) as first_order_completion_date
                        , max(base.alive_probability) as alive_probability
                        , sum(base.orders_30d) as orders_30d
                        , sum(base.orders_90d) as orders_90d
                    from base
                    where sales_channel = 'DASH Mobile'
                    group by {{ range(1, 10) | join(',') }}
                )

                , lifecycle_tb as (
                    select measurement_datetime
                        , interim.lead_id
                        , interim.shipper_id
                        , interim.account_id
                        , interim.account_name
                        , interim.contact_id
                        , interim.email
                        , system_id
                        , nb_referral_count
                        , last_referral_date
                        , nr_redemption_count
                        , last_redemption_date
                        , onboarded_date
                        , interim.sales_channel
                        , first_order_completion_date
                        , orders_30d
                        , orders_90d
                        , alive_probability
                        , case
                            when onboarded_date >= date('{{ measurement_datetime_utc }}') - interval '180' day
                                and first_order_completion_date is null
                                then '1. dormant'

                            when first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day
                            then '2. new'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >= 1
                                and alive_probability > 0.8
                                then '3. active'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >=1
                                and alive_probability <= 0.8
                                then '4. at_risk'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d = 0 or orders_30d is null)
                                and orders_90d >= 1
                                then '5. lapsed'

                        else 'inactive' end as lifecycle
                    from interim
                    left join nb_enriched
                        on interim.measurement_datetime = nb_enriched.lead_creation_date
                        and interim.account_id = nb_enriched.account_id
                        and interim.sales_channel = nb_enriched.sales_channel
                    left join nr_enriched
                        on interim.measurement_datetime = nr_enriched.redemption_date
                        and interim.account_id = nr_enriched.account_id
                        and interim.sales_channel = nr_enriched.sales_channel
                    left join last_referral_tb
                        on interim.account_id = last_referral_tb.account_id
                    left join last_redemption_tb
                        on interim.account_id = last_redemption_tb.account_id
                )

                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id, shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                        and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm
                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 100 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 70 then '1. VIP'
                                when potential_ppm > 25 and potential_ppm <= 70 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 25 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 110 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 110 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 150 then '1. VIP'
                                when potential_ppm > 45 and potential_ppm <= 150 then '2. high'
                                when potential_ppm > 10 and potential_ppm <= 45 then '3. medium'
                                when potential_ppm <= 10 then '4. standard'
                            else '5. unknown' end
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="fs_interim",
                jinja_template="""
                with interim as (
                    select base.measurement_datetime
                        , base.lead_id
                        , base.shipper_id
                        , base.account_id
                        , base.account_name
                        , base.contact_id
                        , base.email
                        , base.system_id
                        , base.sales_channel
                        , min(base.onboarded_date) as onboarded_date
                        , min(base.first_order_completion_date) as first_order_completion_date
                        , max(base.alive_probability) as alive_probability
                        , sum(base.orders_30d) as orders_30d
                        , sum(base.orders_90d) as orders_90d
                    from base
                    where sales_channel = 'Field Sales'
                    group by {{ range(1, 10) | join(',') }}
                )

                , lifecycle_tb as (
                    select measurement_datetime
                        , interim.lead_id
                        , interim.shipper_id
                        , interim.account_id
                        , interim.account_name
                        , interim.contact_id
                        , interim.email
                        , system_id
                        , nb_referral_count
                        , last_referral_date
                        , nr_redemption_count
                        , last_redemption_date
                        , onboarded_date
                        , interim.sales_channel
                        , first_order_completion_date
                        , orders_30d
                        , orders_90d
                        , alive_probability
                        , case
                            when onboarded_date >= date('{{ measurement_datetime_utc }}') - interval '180' day
                                and first_order_completion_date is null
                                then '1. dormant'

                            when first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day
                            then '2. new'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d / 20) >= 5
                                and alive_probability > 0.8
                                then '3. active'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d / 20) >= 5
                                and alive_probability <= 0.8
                                then '4. at_risk'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d is null or (orders_30d / 20 < 5))
                                and orders_90d >= 1
                                then '5. lapsed'

                        else 'inactive' end as lifecycle
                    from interim
                    left join nb_enriched
                        on interim.measurement_datetime = nb_enriched.lead_creation_date
                        and interim.account_id = nb_enriched.account_id
                        and interim.sales_channel = nb_enriched.sales_channel
                    left join nr_enriched
                        on interim.measurement_datetime = nr_enriched.redemption_date
                        and interim.account_id = nr_enriched.account_id
                        and interim.sales_channel = nr_enriched.sales_channel
                    left join last_referral_tb
                        on interim.account_id = last_referral_tb.account_id
                    left join last_redemption_tb
                        on interim.account_id = last_redemption_tb.account_id

                )

                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id, shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                        and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                    )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm
                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 6000 then '1. VIP'
                                when potential_ppm > 2000 and potential_ppm <= 6000 then '2. high'
                                when potential_ppm > 500 and potential_ppm <= 2000 then '3. medium'
                                when potential_ppm <= 500 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 5000 then '1. VIP'
                                when potential_ppm > 1600 and potential_ppm <= 5000 then '2. high'
                                when potential_ppm > 600 and potential_ppm <= 1600 then '3. medium'
                                when potential_ppm <= 600 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 4000 then '1. VIP'
                                when potential_ppm > 1250 and potential_ppm <= 4000 then '2. high'
                                when potential_ppm > 450 and potential_ppm <= 1250 then '3. medium'
                                when potential_ppm <= 450 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 7000 then '1. VIP'
                                when potential_ppm > 2200 and potential_ppm <= 7000 then '2. high'
                                when potential_ppm > 400 and potential_ppm <= 2200 then '3. medium'
                                when potential_ppm <= 400 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 10000 then '1. VIP'
                                when potential_ppm > 2500 and potential_ppm <= 10000 then '2. high'
                                when potential_ppm > 800 and potential_ppm <= 2500 then '3. medium'
                                when potential_ppm <= 800 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 10000 then '1. VIP'
                                when potential_ppm > 1800 and potential_ppm <= 10000 then '2. high'
                                when potential_ppm > 500 and potential_ppm <= 1800 then '3. medium'
                                when potential_ppm <= 500 then '4. standard'
                            else '5. unknown' end
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="partnerships_interim",
                jinja_template="""
                with interim as (
                    select base.measurement_datetime
                        , base.lead_id
                        , base.shipper_id
                        , base.account_id
                        , base.account_name
                        , base.contact_id
                        , base.email
                        , base.system_id
                        , base.sales_channel
                        , min(base.onboarded_date) as onboarded_date
                        , min(base.first_order_completion_date) as first_order_completion_date
                        , max(base.alive_probability) as alive_probability
                        , sum(base.orders_30d) as orders_30d
                        , sum(base.orders_90d) as orders_90d
                    from base
                    where sales_channel = 'Partnerships'
                    group by {{ range(1, 10) | join(',') }}
                )

                , lifecycle_tb as (
                    select measurement_datetime
                        , interim.lead_id
                        , interim.shipper_id
                        , interim.account_id
                        , interim.account_name
                        , interim.contact_id
                        , interim.email
                        , system_id
                        , nb_referral_count
                        , last_referral_date
                        , nr_redemption_count
                        , last_redemption_date
                        , onboarded_date
                        , interim.sales_channel
                        , first_order_completion_date
                        , orders_30d
                        , orders_90d
                        , alive_probability
                        , case
                            when onboarded_date >= date('{{ measurement_datetime_utc }}') - interval '180' day
                                and first_order_completion_date is null
                                then '1. dormant'

                            when first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day
                            then '2. new'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >= 1
                                and alive_probability > 0.8
                                then '3. active'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >=1
                                and alive_probability <= 0.8
                                then '4. at_risk'

                            when first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d = 0 or orders_30d is null)
                                and orders_90d >= 1
                                then '5. lapsed'

                        else 'inactive' end as lifecycle
                    from interim
                    left join nb_enriched
                        on interim.measurement_datetime = nb_enriched.lead_creation_date
                        and interim.account_id = nb_enriched.account_id
                        and interim.sales_channel = nb_enriched.sales_channel
                    left join nr_enriched
                        on interim.measurement_datetime = nr_enriched.redemption_date
                        and interim.account_id = nr_enriched.account_id
                        and interim.sales_channel = nr_enriched.sales_channel
                    left join last_referral_tb
                        on interim.account_id = last_referral_tb.account_id
                    left join last_redemption_tb
                        on interim.account_id = last_redemption_tb.account_id
                )

                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id, shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                            and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm
                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 100 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 70 then '1. VIP'
                                when potential_ppm > 25 and potential_ppm <= 70 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 25 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 110 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 110 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 150 then '1. VIP'
                                when potential_ppm > 45 and potential_ppm <= 150 then '2. high'
                                when potential_ppm > 10 and potential_ppm <= 45 then '3. medium'
                                when potential_ppm <= 10 then '4. standard'
                            else '5. unknown' end
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="lead_interim",
                jinja_template="""
                select '{{ measurement_datetime_utc }}' as measurement_datetime
                    , id as lead_id
                    , null as shipper_id
                    , null as account_id
                    , name as account_name
                    , null as contact_id
                    , email
                    , system_id
                    , case
                        -- disqualified leads for my, th, vn directed to DASH Mobile, the rest to DASH Lite
                        when disqualification_reason in ('Too Small') then case
                            when salesforce_lead_enriched.system_id in ('my', 'th', 'vn') then 'DASH Mobile'
                            when salesforce_lead_enriched.system_id in ('sg', 'ph', 'id') then 'DASH Lite'
                            else null end
                    else 'Field Sales' end as sales_channel
                    , null as nb_referral_count
                    , null as last_referral_date
                    , null as nr_redemption_count
                    , null as last_redemption_date
                    , null as onboarded_date
                    , null as first_order_completion_date
                    , 0 as orders_30d
                    , 0 as orders_90d
                    , null as alive_probability
                    , '0. lead' as lifecycle
                    , null as potential_ppm
                    , '5. unknown' as shipper_tier
                from salesforce_lead_enriched
                where True
                    -- remove ninja direct & crossborder
                    and lower(salesforce_lead_enriched.record_type) not like '%ninja direct%'
                    and lower(salesforce_lead_enriched.record_type) not like '%crossborder%'
                    -- remove those with salesforce_account
                    and account_id is null
                    -- only taking disqualified leads with following reasons
                    and (disqualification_reason is null or
                        -- only 'Too Small' will go into self-serve flow, the other dq leads remain in FS flow
                        disqualification_reason in (
                            'Max no. of attempts reached'
                            , 'Too Small'
                            , 'No Potential Business'
                            , 'Poor Information')
                        )
                    -- remove unacquired leads generated more than 3 months ago
                    and salesforce_lead_enriched.creation_datetime
                        >= date('{{ measurement_datetime_utc }}') - interval '180' day
                    and salesforce_lead_enriched.creation_datetime <= date('{{ measurement_datetime_utc }}')
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="core_interim",
                jinja_template="""
                with 30d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_30d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '30' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , 90d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_90d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '90' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , lifecycle_tb as (
                    select '{{ measurement_datetime_utc }}' as measurement_datetime
                        , null as lead_id
                        , null as account_id
                        , se.id as shipper_id
                        , se.shipper_name as account_name
                        , null as contact_id
                        , se.system_id
                        , shipper_prod_gl.email
                        , case
                            when sales_channel = 'Self Serve'
                                and lower(account_creation_source) like '%mobile%' then 'DASH Mobile'
                            when sales_channel = 'Self Serve'
                                and lower(account_creation_source) not like '%mobile%' then 'DASH Lite'
                        else sales_channel end as sales_channel
                        , null as nb_referral_count
                        , null as last_referral_date
                        , null as nr_redemption_count
                        , null as last_redemption_date
                        , se.onboarded_date
                        , se.first_order_completion_date
                        , 30d_tb.orders_30d
                        , 90d_tb.orders_90d
                        , se.alive_probability
                        , case
                            when se.onboarded_date >= date('{{ measurement_datetime_utc }}') - interval '180' day
                                and se.first_order_completion_date is null
                                then '1. dormant'

                            when se.first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day
                                then '2. new'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >= 1
                                and se.alive_probability > 0.8
                                then '3. active'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >=1
                                and se.alive_probability <= 0.8
                                then '4. at_risk'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d = 0 or orders_30d is null)
                                and orders_90d >= 1
                                then '5. lapsed'

                        else 'inactive' end as lifecycle

                    from shippers_enriched as se
                    left join 90d_tb
                        on se.id = 90d_tb.shipper_id
                    left join 30d_tb
                        on se.id = 30d_tb.shipper_id
                    left join shipper_prod_gl
                        on se.id = shipper_prod_gl.id
                    where True
                        -- remove shippers with sf accounts
                        and se.sf_acc_id is null
                        -- only include selected channels
                        and sales_channel in ('Self Serve', 'Field Sales', 'Partnerships')
                        -- assume only Self Serve shippers don't have sf acc
                        -- and se.sales_channel = 'Self Serve'
                        -- and se.account_creation_source like '%DASH%'
                        and (
                            -- filter to include dormant accounts
                            (se.first_order_completion_date is null
                            and se.onboarded_date >=date('{{ measurement_datetime_utc }}') - interval '180' day
                            and se.onboarded_date <=date('{{ measurement_datetime_utc }}'))
                            or
                            -- filter to exclude inactive shippers
                            (90d_tb.orders_90d is not null)
                            )
                )
                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id, shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                        and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm
                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 100 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 70 then '1. VIP'
                                when potential_ppm > 25 and potential_ppm <= 70 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 25 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 110 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 110 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else '5. unknown' end
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 150 then '1. VIP'
                                when potential_ppm > 45 and potential_ppm <= 150 then '2. high'
                                when potential_ppm > 10 and potential_ppm <= 45 then '3. medium'
                                when potential_ppm <= 10 then '4. standard'
                            else '5. unknown' end
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.system_id = ap.system_id
                    and lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="union_table",
                jinja_template="""
                select {{ query_columns }}
                from lite_interim

                union

                select {{ query_columns }}
                from mobile_interim

                union

                select {{ query_columns }}
                from fs_interim

                union

                select {{ query_columns }}
                from partnerships_interim

                union

                select {{ query_columns }}
                from lead_interim

                union

                select {{ query_columns }}
                from core_interim
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "query_columns": query_columns,
                },
            ),
            base.TransformView(
                view_name="shipper_segmentation_daily",
                jinja_template="""
                select
                    date_format(union_table.measurement_datetime, "yyyy-MM-dd") as snapshot_date
                    , union_table.lead_id as sf_lead_id
                    , union_table.account_id as sf_acc_id
                    , union_table.contact_id as sf_contact_id
                    , cast(union_table.shipper_id as string) as shipper_id
                    , union_table.account_name as shipper_name
                    , union_table.sales_channel
                    , union_table.onboarded_date
                    , union_table.first_order_completion_date
                    , union_table.alive_probability
                    , cast(union_table.orders_30d as int) as orders_30d
                    , cast(union_table.orders_90d as int) as orders_90d
                    , union_table.system_id
                    , union_table.lifecycle
                    , union_table.potential_ppm
                    , union_table.shipper_tier
                    , cast(union_table.nb_referral_count as int) as nb_referral_count
                    , date_format(
                        union_table.last_referral_date
                        , "yyyy-MM-dd"
                    ) as last_referral_date
                    , cast(union_table.nr_redemption_count as int) as nr_redemption_count
                    , date_format(
                        union_table.last_redemption_date
                        , "yyyy-MM-dd"
                    ) as last_redemption_date
                from union_table
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_SEGMENTATION_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "sales_channel"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
