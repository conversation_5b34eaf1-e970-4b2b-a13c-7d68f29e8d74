import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON>ta

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.LAZADA_INTERCEPTED_ORDERS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.LAZADA_INTERCEPTED_ORDERS_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_UPDATE_CONTACT_INFORMATION_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_TAGS,
                view_name="order_tags",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_UPDATE_CONTACT_INFORMATION,
                view_name="update_contact_information",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS,
                view_name="update_address_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            )
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="original_address",
                jinja_template="""

                with 
                    original_address_base as (
                        select 
                            order_id
                            , sha2(old_to_address1, 256) as original_to_address1
                            , sha2(old_to_address2, 256) as original_to_address2
                            , sha2(old_to_postcode, 256) as original_to_postcode
                            , old_to_city as original_to_city
                            , old_to_district as original_to_district
                            , old_to_state as original_to_state
                            , rank() over (partition by order_id order by event_creation_datetime) as change_sequence
                        from update_address_events
                    )

                select *
                from original_address_base 
                where change_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="original_contact_info",
                jinja_template="""

                with 
                    original_contact_base as (
                        select 
                            order_id
                            , sha2(original_consignee_name, 256) as original_to_name
                            , concat(sha2(lower(split(original_consignee_email, "@")[0]), 256), "@", 
                                split(original_consignee_email, "@")[1]) as original_to_email
                            , sha2(original_consignee_contact, 256) as original_to_contact
                            , rank() over (partition by order_id order by event_timestamp) as change_sequence
                        from update_contact_information
                    )
                    
                select *
                from original_contact_base 
                where change_sequence = 1
                
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                with
                    final as (

                        select
                            order_tags.order_id
                            , order_milestones.shipper_id 
                            , order_milestones.seller_id
                            , order_milestones.creation_datetime
                            , if(
                                    greatest(order_milestones.insurance_value,order_milestones.cod_value) >= 5000000
                                    , 5000000
                                    , greatest(order_milestones.insurance_value,order_milestones.cod_value)
                            ) as potential_claim_value
                            , order_milestones.lazmall_flag
                            , coalesce(original_address.original_to_address1, order_milestones.to_address1) 
                                as original_to_address1
                            , coalesce(original_address.original_to_address2, order_milestones.to_address2) 
                                as original_to_address2
                            , coalesce(original_address.original_to_postcode, order_milestones.to_postcode) 
                                as original_to_postcode
                            , coalesce(original_address.original_to_city, order_milestones.to_city) 
                                as original_to_city
                            , original_address.original_to_district
                            , original_address.original_to_state
                            , coalesce(original_contact_info.original_to_name, order_milestones.to_name) 
                                as original_to_name
                            , original_contact_info.original_to_email
                            , original_contact_info.original_to_contact
                            , min(date(from_utc_timestamp(order_tags.created_at, '{{ get_local_timezone }}'))) filter 
                                (where tag_id=73) as interception_attempted_timestamp
                            , min(date(from_utc_timestamp(order_tags.created_at, '{{ get_local_timezone }}'))) filter 
                                (where tag_id=75) as intercepted_active_seller_timestamp
                            , min(date(from_utc_timestamp(order_tags.created_at, '{{ get_local_timezone }}'))) filter 
                                (where tag_id=68) as intercepted_deactivated_seller_timestamp
                            , min(date(from_utc_timestamp(order_tags.created_at, '{{ get_local_timezone }}'))) filter 
                                (where tag_id=77) as ghost_parcel_timestamp
                            , '{{ system_id }}' as system_id
                            , order_milestones.created_month
                        from order_tags
                        join order_milestones
                            on order_tags.order_id = order_milestones.order_id
                        join shipper_attributes
                            on order_milestones.shipper_id = shipper_attributes.id
                        left join original_address
                            on order_milestones.order_id = original_address.order_id
                        left join original_contact_info
                            on order_milestones.order_id = original_contact_info.order_id
                        where
                            order_tags.tag_id in (68,73,75,77)
                            and order_tags.deleted_at is null
                            and order_milestones.lazmall_flag = 0
                            -- Lazada only
                            and shipper_attributes.parent_id_coalesce = 341107
                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,20,21

                    )

                select
                    *
                    , least(
                        intercepted_active_seller_timestamp
                        , intercepted_deactivated_seller_timestamp
                        , ghost_parcel_timestamp
                    ) as known_fraud_timestamp
                from final

                """,
                jinja_arguments={"system_id": system_id, "get_local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_INTERCEPTED_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()