import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).USER, view_name="salesforce_user"),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).USER_ROLE, view_name="salesforce_user_role"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (
                        select
                            id
                            , name
                        from salesforce_user
                )
                select
                    salesforce_user.id
                    , salesforce_user.name
                    , salesforce_user.alias
                    , salesforce_user_role.name as role
                    , salesforce_user.title
                    , salesforce_user.business_unit_acquisition_endpoint_c as salesperson_prefix
                    , salesforce_user.email
                    , cast(salesforce_user.is_active as int) as is_active
                    , salesforce_user.manager_id
                    , salesforce_user.manager_1_name_c as manager
                    , salesforce_user.manager_2_name_c as manager_2
                    , manager_3.name as manager_3
                    , salesforce_user.senior_sales_manager_c as senior_sales_manager_id
                    , senior.name as senior_sales_manager
                    , salesforce_user.bd_head_c as bd_head_id
                    , bd_head.name as bd_head_name
                    , cast(salesforce_user.sales_cloud_user_c as int) as is_salescloud_user
                    , salesforce_user.sales_channel_new_c as sales_channel
                    , salesforce_user.sales_territory_c as sales_territory
                    , salesforce_user.sales_team_c as sales_team
                    , salesforce_user.fsr_downstream_user_c as fsr_downstream_user
                    , salesforce_user.fsr_upstream_user_c as fsr_upstream_user
                    , salesforce_user.fsr_sales_team_c as fsr_sales_team
                    , salesforce_user.fsr_case_resolution_team_c as fsr_case_resolution_team
                    , salesforce_user.subteam_c as subteam
                    , salesforce_user.direct_reporting_manager_c as direct_reporting_manager
                    , salesforce_user.direct_sales_area_head_c as direct_sales_area_head
                    , salesforce_user.direct_sales_head_c as direct_sales_head
                    , salesforce_user.sales_area_c as sales_area
                    , salesforce_user.profile_id
                    , date(from_utc_timestamp(salesforce_user.created_date, {{ get_local_timezone }})) as created_date
                    , date(
                        from_utc_timestamp(salesforce_user.last_modified_date, {{ get_local_timezone }})
                    ) as last_modified_date
                    , date(
                        from_utc_timestamp(salesforce_user.end_date_c, {{ get_local_timezone }})
                    ) as end_date
                    , date_format(
                        from_utc_timestamp(salesforce_user.created_date, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                    , lower(salesforce_user.country_code_c) as country
                    , lower(salesforce_user.country_code_c) as system_id
                from salesforce_user
                left join salesforce_user_role on
                    salesforce_user.user_role_id = salesforce_user_role.id
                left join base as manager_3 on
                    salesforce_user.manager_3_c = manager_3.id
                left join base as senior on
                    salesforce_user.senior_sales_manager_c = senior.id
                left join base as bd_head on
                    salesforce_user.bd_head_c = bd_head.id
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("salesforce_user.country_code_c"),
                },
            ),
        ),
        nullified_values=("None", "none"),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_USER_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
