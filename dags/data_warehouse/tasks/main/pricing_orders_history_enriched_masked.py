import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.PRICING_ORDERS_HISTORY_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.PRICING_ORDERS_HISTORY_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.PricingProdGL(input_env, is_masked).PRICING_ORDERS_HISTORY,
                view_name="pricing_orders_history",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , cast(order_id as bigint) as order_id
                    , trigger_event
                    , from_utc_timestamp(trigger_time, {{ get_local_timezone }}) as trigger_datetime
                    , get_json_object(error, '$.error.code') as error_code
                    , get_json_object(error, '$.error.title') as error_title
                {%- for direction in ('from', 'to') %}
                {%- for level in ('l1', 'l2', 'l3') %}
                    , get_json_object(pricing_params, '$.{{ direction }}_location.{{ level }}_id')
                        as {{ direction }}_{{ level }}_id
                    , get_json_object(pricing_params, '$.{{ direction }}_location.{{ level }}_name')
                        as {{ direction }}_{{ level }}_name
                {%- endfor %}
                {%- endfor %}
                    , lower(system_id) as system_id
                    , date_format(created_at, 'yyyy-MM') as created_month
                from pricing_orders_history
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(system_id)")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PRICING_ORDERS_HISTORY_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()