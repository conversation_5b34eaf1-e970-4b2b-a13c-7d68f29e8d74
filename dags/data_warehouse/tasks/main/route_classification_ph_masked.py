import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.ROUTE_CLASSIFICATION_PH_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.ROUTE_CLASSIFICATION_PH_MASKED,
    system_ids=(
        constants.SystemID.PH,
    ),
    depends_on=(
        data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
        data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
        data_warehouse.FleetDAG.Task.PROOFS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, 
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PROOFS_ENRICHED,
                view_name="proofs_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    tx.route_id
                    , zones.id zone_id
                    , zones.name zone_name
                    , fpbd.courier_id
                    , fpbd.courier_name
                    , fpbd.courier_type
                    , fpbd.depot_id
                    , hubs.longitude hubs_longitude
                    , hubs.latitude hubs_latitude
                    , fpbd.zone_start
                    , fpbd.zone_end
                    , fpbd.zone_time_hrs
                    , date_format(from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}'), 'yyyy-MM') as created_month
                    , date(from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}')) as transaction_date
                    , from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}') as service_end_time
                    , concat(proofs_enriched.sign_coordinates_lng, ',', proofs_enriched.sign_coordinates_lat) as delivery_longlat
                    , row_number() over (partition by tx.id order by proofs_enriched.proof_id desc) as tb_rnk
                from transactions tx
                left join proofs_enriched 
                    on tx.id = proofs_enriched.reference_id
                    and proofs_enriched.reference = 'Transaction'
                left join fleet_performance_base_data fpbd
                    on tx.route_id = fpbd.route_id
                    and fpbd.route_date = date(from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}'))
                left join hubs_enriched hubs
                    on fpbd.depot_id = hubs.id
                left join route_logs_enriched route
                    on tx.route_id = route.legacy_id
                left join zones 
                    on route.zone_id = zones.id
                    and zones.deleted_at is null
                where tx.status not in ('Pending', 'Cancelled')
                    and tx.route_id is not null
                    and date(fpbd.zone_end) >= date('2020-02-01')
                    and tx.type = 'DD'
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="interim",
                jinja_template="""
                with sign_coordinates as (
                    select
                        *
                        , row_number() over (partition by transaction_date, courier_id order by service_end_time asc) as rnk
                        , cast(substring_index(delivery_longlat, ',', 1) as double) as longitude
                        , cast(substring_index(delivery_longlat, ',', -1) as double) as latitude
                    from base
                    where tb_rnk = 1
                ),

                remove_invalid_coordinates as (
                    select
                        route_id
                        , zone_id
                        , zone_name
                        , courier_id
                        , courier_name
                        , courier_type
                        , depot_id
                        , hubs_longitude
                        , hubs_latitude
                        , zone_time_hrs
                        , created_month
                        , transaction_date
                        , service_end_time
                        , rnk
                        , longitude
                        , latitude
                        , if((latitude = -1 and longitude = -1) or (latitude = 1 and longitude = 1), 1, 0) as invalid_coordinate_flag
                    from sign_coordinates
                )

                select
                    current.route_id
                    , current.zone_id
                    , current.zone_name
                    , current.courier_id
                    , current.courier_name
                    , current.courier_type
                    , current.depot_id
                    , current.zone_time_hrs
                    , current.created_month
                    , current.transaction_date
                    , current.service_end_time as current_end_time
                    , next.service_end_time as next_end_time
                    , current.rnk as current_rnk
                    , next.rnk as next_rnk
                    , current.longitude as current_longitude
                    , current.latitude as current_latitude
                    , next.longitude as next_longitude
                    , next.latitude as next_latitude
                    , (to_unix_timestamp(next.service_end_time, 'yyyy-MM-dd HH:mm:ss') - to_unix_timestamp(current.service_end_time, 'yyyy-MM-dd HH:mm:ss'))
                        /cast(60 as double) as minutes_elapsed
                    , (6371000*ACOS(COS(radians(current.latitude))*COS(radians(next.latitude))*COS(radians(next.longitude)
                        -radians(current.longitude))+SIN(radians(current.latitude))*SIN(radians(next.latitude)))) as distance_travelled
                    , greatest(current.invalid_coordinate_flag, next.invalid_coordinate_flag) as invalid_distance_travelled_flag
                    , (6371000*ACOS(COS(radians(current.latitude))*COS(radians(current.hubs_latitude))*COS(radians(current.hubs_longitude)
                        -radians(current.longitude))+SIN(radians(current.latitude))*SIN(radians(current.hubs_latitude)))) as distance_from_hubs
                    , row_number() over (partition by current.transaction_date, current.courier_id 
                        order by current.invalid_coordinate_flag asc, current.service_end_time asc) as rnk2
                from remove_invalid_coordinates current
                left join remove_invalid_coordinates as next
                    on current.route_id = next.route_id
                    and current.transaction_date = next.transaction_date
                    and current.rnk+1 = next.rnk
                where next.service_end_time is not null
                """,
            ),
            base.TransformView(
                view_name="interim_filtered",
                jinja_template="""
                with interim_flagged as (
                    select
                        *
                        , 1 as included_courier_type
                        , if(distance_travelled/cast(minutes_elapsed as double) > 1666, 1, 0) as driver_exceeded_100kmph
                    from interim
                ),
            
                lenience as (
                    select
                        route_id
                        , if(count_if(driver_exceeded_100kmph = 1) / count(*) <= 0.05, 1, 0) as driver_exceeded_100kmph_lenience
                        , if(count_if(invalid_distance_travelled_flag = 1) / count(*) <= 0.05, 1, 0) as invalid_coordinate_lenience
                        , if((count_if(driver_exceeded_100kmph = 1) / count(*) <= 0.05) 
                            and (count_if(driver_exceeded_100kmph = 1) / count(*) > 0), 1, 0) as spared_exceeded_100kmph
                        , if((count_if(invalid_distance_travelled_flag = 1) / count(*) <= 0.05) 
                            and (count_if(invalid_distance_travelled_flag = 1) / count(*) > 0), 1, 0) as spared_invalid_coodinates
                    from interim_flagged
                    group by 1
                )
            
                select
                    interim_flagged.*
                    , lenience.spared_exceeded_100kmph
                    , lenience.spared_invalid_coodinates
                from interim_flagged
                left join lenience
                    on interim_flagged.route_id = lenience.route_id
                where
                    (lenience.driver_exceeded_100kmph_lenience = 0 or (lenience.driver_exceeded_100kmph_lenience = 1 and interim_flagged.driver_exceeded_100kmph = 0))
                    and (lenience.invalid_coordinate_lenience = 0 or (lenience.invalid_coordinate_lenience = 1 and interim_flagged.invalid_distance_travelled_flag = 0))
                """,
            ),
            base.TransformView(
                view_name="add_stem_in_out",
                jinja_template="""
                with aggregate as (
                    select
                        base.route_id 
                        , base.zone_id
                        , base.zone_name
                        , base.courier_id
                        , base.courier_name
                        , base.courier_type
                        , base.depot_id hub_id
                        , fpbd.zone_start
                        , fpbd.zone_end
                        , base.zone_time_hrs
                        , base.created_month
                        , base.transaction_date
                        , hubs.name as hub_name
                        , hubs.region as hub_region
                        , hubs.latitude as hub_latitude
                        , hubs.longitude as hub_longitude
                        , hubs.is_delivery_hub as hub_is_delivery_hub
                        , fpbd.parcels_delivered_size_xs
                        , fpbd.parcels_delivered_size_s
                        , fpbd.parcels_delivered_size_m
                        , fpbd.parcels_delivered_size_l
                        , fpbd.parcels_delivered_size_xl
                        , fpbd.parcels_delivered_size_xxl
                        , min_by(base.current_longitude, rnk2) as first_longitude
                        , min_by(base.current_latitude, rnk2) as first_latitude
                        , max_by(base.current_longitude, rnk2) as last_longitude
                        , max_by(base.current_latitude, rnk2) as last_latitude
                        , count_if(base.minutes_elapsed >= 1) as 1m_stop
                        , count_if(base.minutes_elapsed >= 3) as 3m_stop
                        , count_if(base.minutes_elapsed >= 5) as 5m_stop
                        , count_if(base.minutes_elapsed >= 7) as 7m_stop
                        , sum(base.minutes_elapsed) as on_zone_minutes
                        , sum(base.distance_travelled/cast(1000 as double)) as zone_travel_distance
                        , avg(base.distance_from_hubs/cast(1000 as double)) as avg_tx_distance_from_hub
                        , percentile(base.distance_from_hubs/cast(1000 as double), 0.5) as median_tx_distance_from_hub
                        , count_if(isnan(base.distance_travelled)) as p2p_distance_isnan
                        , count_if(isnull(base.distance_travelled)) as p2p_distance_isnull
                        , count_if((base.distance_travelled/cast(base.minutes_elapsed as double)) > 1666) as driver_exceeded_100kmph
                        , sum(base.invalid_distance_travelled_flag) as invalid_coordinates_count
                        , max(fpbd.parcels_delivered) as parcels_delivered
                        , max(fpbd.parcels_on_route) as parcels_on_route
                        , count(*) as total_transactions
                        , max(base.spared_exceeded_100kmph) as spared_exceeded_100kmph
                        , max(base.spared_invalid_coodinates) as spared_invalid_coodinates
                    from interim_filtered base
                    left join fleet_performance_base_data fpbd
                        on base.route_id = fpbd.route_id
                        and base.transaction_date = fpbd.route_date
                    left join hubs_enriched hubs
                        on fpbd.depot_id = hubs.id
                    where 1=1
                        and included_courier_type = 1
                    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23

                )

                select
                    *
                    , (6371000*ACOS(COS(radians(first_latitude))*COS(radians(hub_latitude))*COS(radians(hub_longitude)
                        -radians(first_longitude))+SIN(radians(first_latitude))*SIN(radians(hub_latitude))))/cast(1000 as double) as stem_out_distance
                    , (6371000*ACOS(COS(radians(last_latitude))*COS(radians(hub_latitude))*COS(radians(hub_longitude)
                        -radians(last_longitude))+SIN(radians(last_latitude))*SIN(radians(hub_latitude))))/cast(1000 as double) as stem_in_distance
                from aggregate
                """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                select
                    'ph' as country
                    , route_id
                    , zone_id
                    , zone_name
                    , hub_id
                    , hub_name
                    , hub_region
                    , hub_latitude
                    , hub_longitude
                    , hub_is_delivery_hub
                    , created_month
                    , transaction_date as route_date
                    , courier_id
                    , courier_name
                    , courier_type
                    , zone_start
                    , zone_end
                    , zone_time_hrs on_zone_hours
                    , parcels_delivered_size_xs
                    , parcels_delivered_size_s
                    , parcels_delivered_size_m
                    , parcels_delivered_size_l
                    , parcels_delivered_size_xl
                    , parcels_delivered_size_xxl
                    , 1m_stop
                    , 3m_stop
                    , 5m_stop
                    , 7m_stop
                    , zone_travel_distance
                    , stem_out_distance
                    , stem_in_distance
                    , stem_out_distance + stem_in_distance + zone_travel_distance as route_travel_distance
                    , avg_tx_distance_from_hub
                    , median_tx_distance_from_hub
                    , p2p_distance_isnan
                    , p2p_distance_isnull
                    , driver_exceeded_100kmph
                    , invalid_coordinates_count
                    , parcels_delivered
                    , parcels_on_route parcels_attempted
                    , total_transactions
                    , spared_exceeded_100kmph
                    , spared_invalid_coodinates
                from add_stem_in_out
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ROUTE_CLASSIFICATION_PH,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()