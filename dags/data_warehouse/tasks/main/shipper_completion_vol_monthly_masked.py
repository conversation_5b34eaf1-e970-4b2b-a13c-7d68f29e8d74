### IMPORTANT: This table involves several broadcast joins of large tables such as shipper_attributes, which does not work
# with the default 1g driver memory config on JupyterHub. Increase Spark driver memory if testing this on JupyterHub,
# e.g. config("spark.driver.memory", "8500m").
import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_FULL_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY_FULL,
                view_name="shipper_completion_vol_daily_full",
            ),
            base.InputTable(
                # Be careful of circular dependency: shippers_enriched is dependent on this task, do not use it as input
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_MILESTONES,
                view_name="shipper_milestones",
            ),
        ),
        parquet_tables=(base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR, view_name="calendar"),),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shippers",
                jinja_template="""
                select
                    shipper_attributes.id
                    , shipper_attributes.system_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.sales_channel
                    , shipper_attributes.parent_id
                    , shipper_attributes.parent_name
                    , shipper_attributes.parent_id_coalesce
                    , shipper_attributes.parent_name_coalesce
                    , shipper_attributes.sf_parent_acc_id_coalesce
                    , shipper_attributes.sf_parent_acc_shipper_id_coalesce
                    /*
                    Try to guess the SF parent's system_id. We need this because the SF shippers data is dirty,
                    there are parents with child shippers across multiple system_id.
                    */
                    , sf_parent_acc_shipper.system_id as sf_parent_system_id
                from shipper_attributes
                left join shipper_attributes as sf_parent_acc_shipper on
                    shipper_attributes.sf_parent_acc_shipper_id_coalesce = sf_parent_acc_shipper.id
                """,
            ),
            base.TransformView(view_name="cache_shippers", jinja_template="""cache table shippers"""),
            base.TransformView(
                view_name="shipper_daily_agg",
                jinja_template="""
                select
                    daily_vol.completion_date
                    , daily_vol.created_month
                    , shippers.id as shipper_id
                    , shippers.parent_id_coalesce
                    , daily_vol.system_id
                    , shippers.sf_parent_acc_id_coalesce
                    , shippers.sf_parent_system_id
                    , shippers.sales_channel
                    , sum(daily_vol.total_orders) as total_orders
                    , sum(daily_vol.rts_orders) as rts_orders
                    , sum(daily_vol.cod_orders) as cod_orders
                    , sum(daily_vol.non_rts_cod_orders) as non_rts_cod_orders
                    , sum(daily_vol.ninjapack_orders) as ninjapack_orders
                    , sum(daily_vol.xb_outbound_orders) as xb_outbound_orders
                    , sum(daily_vol.total_delivery_fee) as total_delivery_fee
                    , sum(daily_vol.total_cod_value) as total_cod_value
                from shipper_completion_vol_daily_full as daily_vol
                left join shippers on
                    daily_vol.shipper_id = shippers.id
                group by 1, 2, 3, 4, 5, 6, 7, 8
                """,
            ),
            base.TransformView(
                view_name="shipper_volume",
                jinja_template="""
                with
                    shipper_monthly as (

                        select
                            date(date_trunc('month', daily_vol.completion_date)) as completion_month
                            , daily_vol.shipper_id
                            , daily_vol.system_id
                            , daily_vol.created_month
                            , sum(daily_vol.total_orders) as total_orders
                            , cast(sum(daily_vol.total_orders) / sum(calendar.working_day) as decimal(38, 2)) as ppd
                            , sum(daily_vol.rts_orders) as rts_orders
                            , sum(daily_vol.cod_orders) as cod_orders
                            , sum(daily_vol.non_rts_cod_orders) as non_rts_cod_orders
                            , sum(daily_vol.ninjapack_orders) as ninjapack_orders
                            , sum(daily_vol.xb_outbound_orders) as xb_outbound_orders
                            , sum(daily_vol.total_delivery_fee) as total_delivery_fee
                            , sum(daily_vol.total_cod_value) as total_cod_value
                        from shipper_daily_agg as daily_vol
                        left join calendar on
                            daily_vol.completion_date = calendar.date
                            and daily_vol.system_id = calendar.system_id
                            and calendar.region = 'national'
                        group by 1, 2, 3, 4

                    )
                    , base as (

                        select
                            *
                            , lag(completion_month, 1) over shipper_id_window as completion_month_lag_1m
                            , lag(total_orders, 1) over shipper_id_window as total_orders_lag_1m
                            , lag(completion_month, 12) over shipper_id_window as completion_month_lag_12m
                            , lag(total_orders, 12) over shipper_id_window as total_orders_lag_12m
                            , stddev(nullif(ppd, 0)) over (
                                partition by shipper_id
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as std_orders
                            , avg(nullif(ppd, 0)) over (
                                partition by shipper_id
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as avg_orders
                        from shipper_monthly
                        window shipper_id_window as (
                            partition by shipper_id
                            order by completion_month
                        )

                    )
                    , final as (

                        select
                            *

                            , case
                                -- std_orders is null if there's only 1 row to aggregate over
                                when std_orders is null then 0.9 * avg_orders
                                when avg_orders - std_orders <= 0 then 0.1 * avg_orders
                                else avg_orders - std_orders
                            end as lower_limit_1std

                            , case
                                when std_orders is null then 1.1 * avg_orders
                                else avg_orders + std_orders
                            end as upper_limit_1std

                        from base

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="parent_volume",
                jinja_template="""
                with
                    parent_daily as (

                        select
                            shipper_daily_agg.completion_date
                            , shipper_daily_agg.parent_id_coalesce
                            , shipper_daily_agg.system_id
                            , sum(shipper_daily_agg.total_orders) as total_orders
                        from shipper_daily_agg
                        group by 1, 2, 3

                    )
                    , parent_monthly as (

                        select
                            date(date_trunc('month', parent_daily.completion_date)) as completion_month
                            , parent_daily.parent_id_coalesce
                            , parent_daily.system_id
                            , sum(parent_daily.total_orders) as total_orders
                            , cast(sum(parent_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)) as ppd
                        from parent_daily
                        left join calendar on
                            parent_daily.completion_date = calendar.date
                            and parent_daily.system_id = calendar.system_id
                            and calendar.region = 'national'
                        group by 1, 2, 3

                    )
                    , base as (

                        select
                            *
                            , lag(total_orders, 1) over (
                                partition by parent_id_coalesce
                                order by completion_month
                            ) as total_orders_lag_1m
                            , stddev(nullif(ppd, 0)) over (
                                partition by parent_id_coalesce
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as std_orders
                            , avg(nullif(ppd, 0)) over (
                                partition by parent_id_coalesce
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as avg_orders
                        from parent_monthly

                    )
                    , final as (

                        select
                            *

                            , case
                                -- std_orders is null if there's only 1 row to aggregate over
                                when std_orders is null then 0.9 * avg_orders
                                when avg_orders - std_orders <= 0 then 0.1 * avg_orders
                                else avg_orders - std_orders
                            end as lower_limit_1std

                            , case
                                when std_orders is null then 1.1 * avg_orders
                                else avg_orders + std_orders
                            end as upper_limit_1std

                        from base

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="parent_milestones",
                jinja_template="""
                select
                    parent_id_coalesce
                    , min(first_order_completion_date) as first_order_completion_date
                from shipper_milestones
                group by 1
                """,
            ),
            base.TransformView(
                view_name="sf_parent_volume",
                jinja_template="""
                with
                    parent_daily as (

                        select
                            shipper_daily_agg.completion_date
                            , shipper_daily_agg.sf_parent_acc_id_coalesce
                            , shipper_daily_agg.sf_parent_system_id
                            , sum(shipper_daily_agg.total_orders) as total_orders
                        from shipper_daily_agg
                        where sf_parent_acc_id_coalesce is not null
                        group by 1, 2, 3

                    )
                    , parent_monthly as (

                        select
                            date(date_trunc('month', parent_daily.completion_date)) as completion_month
                            , parent_daily.sf_parent_acc_id_coalesce
                            , parent_daily.sf_parent_system_id
                            , sum(parent_daily.total_orders) as total_orders
                            , cast(sum(parent_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)) as ppd
                        from parent_daily
                        left join calendar on
                            parent_daily.completion_date = calendar.date
                            and parent_daily.sf_parent_system_id = calendar.system_id
                            and calendar.region = 'national'
                        group by 1, 2, 3

                    )
                    , base as (

                        select
                            *
                            , lag(total_orders, 1) over (
                                partition by sf_parent_acc_id_coalesce
                                order by completion_month
                            ) as total_orders_lag_1m
                            , stddev(nullif(ppd, 0)) over (
                                partition by sf_parent_acc_id_coalesce
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as std_orders
                            , avg(nullif(ppd, 0)) over (
                                partition by sf_parent_acc_id_coalesce
                                order by completion_month
                                rows between 12 preceding and 1 preceding
                            ) as avg_orders
                        from parent_monthly

                    )
                    , final as (

                        select
                            *

                            , case
                                -- std_orders is null if there's only 1 row to aggregate over
                                when std_orders is null then 0.9 * avg_orders
                                when avg_orders - std_orders <= 0 then 0.1 * avg_orders
                                else avg_orders - std_orders
                            end as lower_limit_1std

                            , case
                                when std_orders is null then 1.1 * avg_orders
                                else avg_orders + std_orders
                            end as upper_limit_1std

                        from base

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="sf_parent_milestones",
                jinja_template="""
                select
                    sf_parent_acc_id_coalesce
                    , min(first_order_completion_date) as first_order_completion_date
                from shipper_milestones
                left join shippers on
                    shipper_milestones.shipper_id = shippers.id
                where
                    shippers.sf_parent_acc_id_coalesce is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="fs_parent_attributes",
                jinja_template="""
                -- This part has parent_id_coalesce grain. It could potentially be moved to its own table.
                with
                    parent_daily as (

                        select
                            shipper_daily_agg.completion_date
                            , shipper_daily_agg.sf_parent_acc_id_coalesce
                            , shipper_daily_agg.sf_parent_system_id
                            , sum(shipper_daily_agg.total_orders) as total_orders
                        from shipper_daily_agg
                        where
                            shipper_daily_agg.sales_channel = 'Field Sales'
                            and shipper_daily_agg.sf_parent_acc_id_coalesce is not null
                        group by 1, 2, 3

                    )
                    , final as (

                        select
                            parent_daily.sf_parent_acc_id_coalesce
                            , cast(
                                sum(parent_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)
                            ) as first_30d_ppd
                        from parent_daily
                        left join calendar on
                            parent_daily.completion_date = calendar.date
                            and parent_daily.sf_parent_system_id = calendar.system_id
                            and calendar.region = 'national'
                        left join sf_parent_milestones on
                            parent_daily.sf_parent_acc_id_coalesce = sf_parent_milestones.sf_parent_acc_id_coalesce
                        where
                            (
                                parent_daily.completion_date
                                <= sf_parent_milestones.first_order_completion_date + interval 29 days
                            )
                        group by 1

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="shipper_ppd_volume",
                jinja_template="""
                        -- Created for shipper level first, second and third 30d ppd calculation.
                        with
                            shipper_daily as (

                                 select
                                    shipper_daily_agg.completion_date
                                    , shipper_daily_agg.shipper_id
                                    , shipper_daily_agg.system_id
                                    , sum(shipper_daily_agg.total_orders) as total_orders
                                from shipper_daily_agg
                                group by 1, 2, 3

                            )
                            , first_cte as (

                                select
                                    shipper_daily.shipper_id
                                    , cast(
                                        sum(shipper_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)
                                    ) as first_30d_ppd
                                from shipper_daily
                                left join calendar on
                                    shipper_daily.completion_date = calendar.date
                                    and shipper_daily.system_id = calendar.system_id
                                    and calendar.region = 'national'
                                left join shipper_milestones on
                                    shipper_milestones.shipper_id = shipper_daily.shipper_id

                                where
                                    (
                                        shipper_daily.completion_date
                                        <= shipper_milestones.first_order_completion_date + interval 29 days
                                    )
                                group by 1

                            )

                            , second_cte as (

                                select
                                    shipper_daily.shipper_id
                                    , cast(
                                        sum(shipper_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)
                                    ) as second_30d_ppd
                                from shipper_daily
                                left join calendar on
                                    shipper_daily.completion_date = calendar.date
                                    and shipper_daily.system_id = calendar.system_id
                                    and calendar.region = 'national'
                                left join shipper_milestones on
                                    shipper_milestones.shipper_id = shipper_daily.shipper_id

                                where
                                    (
                                        shipper_daily.completion_date
                                        >= shipper_milestones.first_order_completion_date + interval 30 days
                                        and
                                        shipper_daily.completion_date
                                        <= shipper_milestones.first_order_completion_date + interval 59 days
                                    )
                                group by 1

                            )

                            , third_cte as (

                                select
                                    shipper_daily.shipper_id
                                    , cast(
                                        sum(shipper_daily.total_orders) / sum(calendar.working_day) as decimal(38, 2)
                                    ) as third_30d_ppd
                                from shipper_daily
                                left join calendar on
                                    shipper_daily.completion_date = calendar.date
                                    and shipper_daily.system_id = calendar.system_id
                                    and calendar.region = 'national'
                                left join shipper_milestones on
                                    shipper_milestones.shipper_id = shipper_daily.shipper_id

                                where
                                    (
                                        shipper_daily.completion_date
                                        >= shipper_milestones.first_order_completion_date + interval 60 days
                                        and
                                        shipper_daily.completion_date
                                        <= shipper_milestones.first_order_completion_date + interval 89 days
                                    )
                                group by 1

                            )

                        select
                            first_cte.shipper_id
                                , first_cte.first_30d_ppd
                                , second_cte.second_30d_ppd
                                , third_cte.third_30d_ppd
                        from first_cte
                        left join second_cte on
                            first_cte.shipper_id = second_cte.shipper_id
                        left join third_cte on
                            first_cte.shipper_id = third_cte.shipper_id

                        """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select

                            shipper_volume.*
                            , shippers.system_id as country
                            , shippers.shipper_name
                            , shippers.parent_id
                            , shippers.parent_name
                            , shippers.parent_id_coalesce
                            , shippers.parent_name_coalesce
                            , shippers.sf_parent_acc_id_coalesce
                            , shippers.sf_parent_acc_shipper_id_coalesce
                            , shippers.sales_channel
                            , shipper_milestones.first_order_completion_date
                            , date(
                                date_trunc('month', shipper_milestones.first_order_completion_date)
                            ) as first_order_completion_month
                            , date(
                                date_trunc('month', shipper_milestones.last_order_completion_date)
                            ) as last_order_completion_month
                            , parent_milestones.first_order_completion_date
                                as parent_coalesce_first_order_completion_date
                            , date(
                                date_trunc('month', parent_milestones.first_order_completion_date)
                            ) as parent_coalesce_first_order_completion_month
                            , parent_volume.total_orders as parent_coalesce_total_orders
                            , parent_volume.total_orders_lag_1m as parent_coalesce_total_orders_lag_1m

                            , date(
                                date_trunc(
                                    'month'
                                    , case
                                        when shippers.sales_channel = 'Field Sales'
                                            then sf_parent_ms.first_order_completion_date
                                        when shippers.sales_channel = 'Self Serve'
                                            then shipper_milestones.first_order_completion_date
                                    end
                                )
                            ) as cohort_month
                            , shipper_ppd_volume.first_30d_ppd as shipper_first_30d_ppd
                            , shipper_ppd_volume.second_30d_ppd as shipper_second_30d_ppd
                            , shipper_ppd_volume.third_30d_ppd as shipper_third_30d_ppd
                            , fs_parent_attributes.first_30d_ppd as field_sales_first_30d_ppd
                            , case
                                when fs_parent_attributes.first_30d_ppd is null
                                    then null
                                when shippers.sales_channel = 'Field Sales' and fs_parent_attributes.first_30d_ppd > 5
                                    then 1
                                else 0
                            end as field_sales_eligible_flag
                            , parent_volume.ppd as parent_coalesce_ppd
                            , parent_volume.lower_limit_1std as parent_coalesce_lower_limit_1std
                            , parent_volume.upper_limit_1std as parent_coalesce_upper_limit_1std

                            , sf_parent_ms.first_order_completion_date as sf_parent_coalesce_first_order_completion_date
                            , date(
                                date_trunc('month', sf_parent_ms.first_order_completion_date)
                            ) as sf_parent_coalesce_first_order_completion_month
                            , sf_parent_volume.total_orders as sf_parent_coalesce_total_orders
                            , sf_parent_volume.total_orders_lag_1m as sf_parent_coalesce_total_orders_lag_1m
                            , sf_parent_volume.ppd as sf_parent_coalesce_ppd
                            , sf_parent_volume.lower_limit_1std as sf_parent_coalesce_lower_limit_1std
                            , sf_parent_volume.upper_limit_1std as sf_parent_coalesce_upper_limit_1std
                        from shipper_volume
                        left join shippers on
                            shipper_volume.shipper_id = shippers.id
                        left join shipper_milestones on
                            shipper_volume.shipper_id = shipper_milestones.shipper_id
                        left join parent_milestones on
                            shippers.parent_id_coalesce = parent_milestones.parent_id_coalesce
                        left join parent_volume on
                            shippers.parent_id_coalesce = parent_volume.parent_id_coalesce
                            and shipper_volume.completion_month = parent_volume.completion_month
                        left join sf_parent_milestones as sf_parent_ms on
                            shippers.sf_parent_acc_id_coalesce = sf_parent_ms.sf_parent_acc_id_coalesce
                        left join sf_parent_volume on
                            shippers.sf_parent_acc_id_coalesce = sf_parent_volume.sf_parent_acc_id_coalesce
                            and shipper_volume.completion_month = sf_parent_volume.completion_month
                        left join fs_parent_attributes on
                            shippers.sf_parent_acc_id_coalesce
                            = fs_parent_attributes.sf_parent_acc_id_coalesce
                        left join shipper_ppd_volume on
                            shippers.id = shipper_ppd_volume.shipper_id
                        where
                            shipper_volume.total_orders > 0
                            or shipper_volume.total_orders_lag_1m > 0

                    )
                    , volume_categories as (

                        select
                            *

                            , case
                                when first_order_completion_month = completion_month
                                    then 'New'
                                when (
                                    first_order_completion_month < completion_month
                                    and total_orders > 0
                                    and total_orders_lag_1m = 0
                                )
                                    then 'Regained'
                                when total_orders_lag_1m > 0 and total_orders = 0
                                    then 'Lapsed'
                                when ppd > upper_limit_1std
                                    then 'Uptrader'
                                when ppd < lower_limit_1std
                                    then 'Downtrader'
                                else 'Stable'
                            end as shipper_category

                            , case
                                when parent_coalesce_first_order_completion_month = completion_month
                                    then 'New'
                                when (
                                    parent_coalesce_first_order_completion_month < completion_month
                                    and parent_coalesce_total_orders > 0
                                    and parent_coalesce_total_orders_lag_1m = 0
                                )
                                    then 'Regained'
                                when parent_coalesce_total_orders_lag_1m > 0 and parent_coalesce_total_orders = 0
                                    then 'Lapsed'
                                when parent_coalesce_ppd > parent_coalesce_upper_limit_1std
                                    then 'Uptrader'
                                when parent_coalesce_ppd < parent_coalesce_lower_limit_1std
                                    then 'Downtrader'
                                else 'Stable'
                            end as parent_coalesce_category

                            , case
                                when sf_parent_acc_id_coalesce is null
                                    then null
                                when sf_parent_coalesce_first_order_completion_month = completion_month
                                    then 'New'
                                when (
                                    sf_parent_coalesce_first_order_completion_month < completion_month
                                    and sf_parent_coalesce_total_orders > 0
                                    and sf_parent_coalesce_total_orders_lag_1m = 0
                                )
                                    then 'Regained'
                                when sf_parent_coalesce_total_orders_lag_1m > 0 and sf_parent_coalesce_total_orders = 0
                                    then 'Lapsed'
                                when sf_parent_coalesce_ppd > sf_parent_coalesce_upper_limit_1std
                                    then 'Uptrader'
                                when sf_parent_coalesce_ppd < sf_parent_coalesce_lower_limit_1std
                                    then 'Downtrader'
                                else 'Stable'
                            end as sf_parent_coalesce_category

                        from base

                    )
                    , final as (

                        select
                            completion_month
                            , shipper_id
                            , country
                            , shipper_name
                            , parent_id
                            , parent_name
                            , parent_id_coalesce
                            , parent_name_coalesce
                            , sf_parent_acc_id_coalesce
                            , sf_parent_acc_shipper_id_coalesce
                            , sales_channel
                            , first_order_completion_date
                            , first_order_completion_month
                            , last_order_completion_month
                            , total_orders
                            , ppd
                            , rts_orders
                            , cod_orders
                            , non_rts_cod_orders
                            , ninjapack_orders
                            , xb_outbound_orders
                            , total_delivery_fee
                            , total_cod_value
                            , completion_month_lag_1m
                            , total_orders_lag_1m
                            , completion_month_lag_12m
                            , total_orders_lag_12m
                            , shipper_category
                            , parent_coalesce_first_order_completion_date
                            , parent_coalesce_first_order_completion_month
                            , parent_coalesce_total_orders
                            , parent_coalesce_ppd
                            , parent_coalesce_total_orders_lag_1m
                            , parent_coalesce_category
                            , sf_parent_coalesce_first_order_completion_date
                            , sf_parent_coalesce_total_orders
                            , sf_parent_coalesce_ppd
                            , sf_parent_coalesce_total_orders_lag_1m
                            , sf_parent_coalesce_category
                            , shipper_first_30d_ppd
                            , shipper_second_30d_ppd
                            , shipper_third_30d_ppd
                            , field_sales_first_30d_ppd
                            , field_sales_eligible_flag
                            , cohort_month

                            , case
                                when cohort_month = completion_month then 0
                                when sales_channel = 'Field Sales' and sf_parent_coalesce_ppd > 5 then 0
                                when sales_channel = 'Field Sales' and sf_parent_coalesce_ppd <= 5 then 1
                                when sales_channel = 'Self Serve' and shipper_category != 'Lapsed' then 0
                                when sales_channel = 'Self Serve' and shipper_category = 'Lapsed' then 1
                            end as churned_flag

                            , system_id
                            , created_month
                        from volume_categories

                    )

                select
                    *
                from final
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_COMPLETION_VOL_MONTHLY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.optimizer.dynamicPartitionPruning.enabled", "false")
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
