import sys
from pyspark.sql import SparkSession

from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.PRICED_ORDERS_INTERMEDIATE_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.PRICED_ORDERS_INTERMEDIATE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.PRICING_PRICING_DETAILS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.BillingProdGL(input_env, is_masked).PRICED_ORDERS,
                view_name="priced_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.BillingProdGL(input_env, is_masked).VOLUME_DISCOUNT_ORDERS,
                view_name="volume_discount_orders",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PRICING_PRICING_DETAILS,
                view_name="pricing_pricing_details",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="system_id_filtered_priced_orders",
                jinja_template="""

                select
                    * 
                from priced_orders
                where lower(system_id) = '{{ system_id }}'

                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="system_id_filtered_volume_discount_orders",
                jinja_template="""

                select
                    * 
                from volume_discount_orders
                where lower(system_id) = '{{ system_id }}'

                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="system_id_pricing_pricing_details",
                jinja_template="""

                select
                    * 
                from pricing_pricing_details
                where lower(system_id) = '{{ system_id }}'

                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="filtered_priced_orders",
                jinja_template="""
                -- Dedup orders by taking latest record and exclude deleted records

                with 
                    priced_orders_rnk as (
                        select
                            order_id
                            , script_id
                            , pricing_breakdown
                            , pricing_profile_info
                            , parcel_dimension
                            , cod_collected
                            , pudo_info
                            , service_level
                            , service_type
                            , created_at
                            , row_number() over (partition by order_id order by created_at desc) as rnk
                        from system_id_filtered_priced_orders
                        where deleted_id = -1                        
                    )

                select
                    order_id
                    , script_id
                    , pricing_breakdown
                    , cod_collected
                    , service_level
                    , service_type
                    , get_json_object(pricing_profile_info,'$.pricer_rate_card_id') as pricer_rate_card_id
                    , get_json_object(pricing_profile_info,'$.pricing_profile_id') as pricing_profile_id
                    , lower(get_json_object(pricing_breakdown, '$.billing_size')) as billing_size
                    , cast(get_json_object(parcel_dimension, '$.weight') as double) as parcel_dimension_weight
                    , cast(get_json_object(pricing_breakdown, '$.billing_weight') as double) as billing_weight
                    , lower(get_json_object(pricing_breakdown, '$.billing_weight_source')) as billing_weight_source
                    , cast(get_json_object(pricing_breakdown, '$.delivery_fee.amount') as double) as delivery_fee
                    , cast(get_json_object(pricing_breakdown, '$.cod_fee.amount') as double) as cod_fee
                    , cast(get_json_object(pricing_breakdown, '$.rts_fee.amount') as double) as rts_fee
                    , cast(get_json_object(pricing_breakdown, '$.insurance_fee.amount') as double) as insurance_fee
                    , cast(get_json_object(pricing_breakdown, '$.handling_fee.amount') as double) as handling_fee
                    , cast(get_json_object(pricing_breakdown, '$.flat_discount.amount') as double) as flat_discount
                    , get_json_object(pricing_breakdown, '$.from_billing_zone.billing_zone') as from_billing_zone
                    , get_json_object(pricing_breakdown, '$.to_billing_zone.billing_zone') as to_billing_zone
                    , get_json_object(pudo_info, '$.first_mile_type') as first_mile_type
                    , created_at
                from priced_orders_rnk
                where rnk = 1

                """,
            ),
            base.TransformView(
                view_name="priced_base",
                jinja_template="""
                -- Coalesce with pricing pricing details to get from/to billing zone, billing region, billing city
                
                with 
                    ppd_dedup as (
                        select
                            order_id
                            , max_by(from_billing_zone, id) as from_billing_zone
                            , max_by(to_billing_zone, id) as to_billing_zone
                            , max_by(nett_delivery_fee_amount, id) as nett_delivery_fee_amount
                        from system_id_pricing_pricing_details
                        group by 1
                    ),

                    volume_discount_orders_dedup as (
                        select
                            order_id
                            , max_by(nett_fees, id) as nett_fees
                        from system_id_filtered_volume_discount_orders
                        group by 1
                    )
                    
                select
                    po.order_id
                    , po.script_id
                    , po.pricing_breakdown
                    , po.cod_collected
                    , po.service_level
                    , po.service_type
                    , po.pricer_rate_card_id
                    , po.pricing_profile_id
                    , po.billing_size
                    , po.billing_weight
                    , po.billing_weight_source
                    , po.delivery_fee
                    , ppd_dedup.nett_delivery_fee_amount
                    , cast(
                        get_json_object(
                            volume_discount_orders_dedup.nett_fees,
                            '$.fee_breakdown.nett_delivery_fee.amount'
                        ) as double
                    ) as volume_discount_nett_delivery_fee_amount
                    , po.parcel_dimension_weight
                    , po.cod_fee
                    , po.rts_fee
                    , po.flat_discount
                    , po.insurance_fee
                    , po.handling_fee
                    , po.first_mile_type
                    , po.created_at
                    , case
                        when coalesce(po.billing_weight, po.parcel_dimension_weight) <= 0.5 then 0.5
                        else ceiling(coalesce(po.billing_weight, po.parcel_dimension_weight))
                    end as billable_weight
                    , coalesce(po.from_billing_zone, ppd_dedup.from_billing_zone) as from_billing_zone
                    , substring(coalesce(po.from_billing_zone, ppd_dedup.from_billing_zone), 1, 1) as from_region
                    , replace(
                        replace(
                        regexp_extract(coalesce(po.from_billing_zone, ppd_dedup.from_billing_zone)
                        , '[A-Z]+(METRO|SUB)' , 0),'METRO',''),'SUB',''
                    ) as from_city
                    , coalesce(po.to_billing_zone, ppd_dedup.to_billing_zone) as to_billing_zone
                    , substring(coalesce(po.to_billing_zone, ppd_dedup.to_billing_zone), 1, 1) as to_region
                    , replace(
                        replace(
                        regexp_extract(coalesce(po.to_billing_zone, ppd_dedup.to_billing_zone)
                        , '[A-Z]+(METRO|SUB)' , 0),'METRO',''),'SUB',''
                    ) as to_city
                from filtered_priced_orders po
                left join ppd_dedup
                    on po.order_id = ppd_dedup.order_id
                left join volume_discount_orders_dedup
                    on po.order_id = volume_discount_orders_dedup.order_id

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- Consolidate all columns using order_milestones as base
                
                select
                    order_milestones.order_id
                    , order_milestones.tracking_id
                    , order_milestones.granular_status
                    , order_milestones.shipper_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.sales_channel
                    , shipper_attributes.parent_id_coalesce as shipper_parent_id_coalesce
                    , shipper_attributes.parent_name_coalesce as shipper_parent_name_coalesce
                    , shipper_attributes.sf_acc_id
                    , shipper_attributes.sf_parent_acc_id_coalesce
                    , shipper_attributes.sf_parent_acc_name_coalesce
                    , shipper_attributes.sf_parent_acc_shipper_id_coalesce
                    , shipper_attributes.sf_sales_territory
                    , shipper_attributes.sf_sales_team
                    , shipper_attributes.sf_nv_product_line
                    , order_milestones.cod_value
                    , order_milestones.rts_flag
                    , order_milestones.weight
                    , order_milestones.creation_datetime
                    , order_milestones.pickup_datetime
                    , order_milestones.nv_pickup_datetime
                    , order_milestones.inbound_datetime
                    , order_milestones.delivery_success_datetime
                    , priced_base.script_id
                    , priced_base.pricing_breakdown
                    , priced_base.cod_collected
                    , priced_base.first_mile_type
                    , priced_base.service_level
                    , priced_base.service_type
                    , priced_base.pricer_rate_card_id
                    , priced_base.pricing_profile_id
                    , priced_base.billing_size
                    , priced_base.billing_weight
                    , priced_base.billing_weight_source
                    , priced_base.billable_weight as pricing_billable_weight
                    , priced_base.from_billing_zone as pricing_from_billing_zone
                    , priced_base.from_region as pricing_from_region
                    , priced_base.from_city as pricing_from_city
                    , priced_base.to_billing_zone as pricing_to_billing_zone
                    , priced_base.to_region as pricing_to_region
                    , priced_base.to_city as pricing_to_city
                    , priced_base.delivery_fee as billing_delivery_fee
                    , priced_base.nett_delivery_fee_amount
                    , priced_base.volume_discount_nett_delivery_fee_amount
                    , priced_base.cod_fee
                    , priced_base.rts_fee
                    , priced_base.flat_discount
                    , priced_base.insurance_fee
                    , priced_base.handling_fee
                    , from_utc_timestamp(priced_base.created_at, {{ get_local_timezone }}) as pricing_created_at
                    , order_milestones.created_month
                    , order_milestones.system_id
                from order_milestones
                left join shipper_attributes 
                    on order_milestones.shipper_id = shipper_attributes.id
                left join priced_base 
                    on order_milestones.order_id = priced_base.order_id

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("order_milestones.system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PRICED_ORDERS_INTERMEDIATE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()