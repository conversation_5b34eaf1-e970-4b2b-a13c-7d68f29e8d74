import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.ON_TIME_SHIPMENT_TRIPS_FY24_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.ON_TIME_SHIPMENT_TRIPS_FY24_MASKED,
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,

    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 8, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_MAWB,
                view_name="flight_mawb",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).VENDORS,
                view_name="vendors",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="air_haul_shipment_order_info",
                jinja_template="""
                /* Logic to get shipment and order info for AH trips*/
                select
                      flight_info.trip_id
                      , max(vendors.name) as vendor_name
                      , count(distinct ah_shipments.shipment_id) total_trip_shipments
                      , sum(add_to_shipment_orders) total_trip_orders
                from shipments_enriched ah_shipments
                left join shipment_ext_awbs
                    on ah_shipments.mawb = shipment_ext_awbs.ref_id
                left join flight_mawb
                    on shipment_ext_awbs.id = flight_mawb.mawb_id
                left join flight_info
                    on flight_info.id = flight_mawb.flight_info_id
                left join movement_trips_enriched
                     on movement_trips_enriched.trip_id = flight_info.trip_id
                left join vendors
                    on shipment_ext_awbs.vendor_id = vendors.id
                where 1=1
                    and ah_shipments.shipment_type = 'AIR_HAUL'
                group by 1
                """,
            ),

            base.TransformView(
                view_name="duration_group_cte",
                jinja_template="""
                with base as (
                select
                    trip_id
                    , if(lower(movement_classification) like '%airport%', "AIR_HAUL","LAND_HAUL") movement_type_revamp
                    , expected_duration_min
                    , end_time_difference_min
                from movement_trips_enriched
                where movement_classification not like '%OTHERS%' and movement_classification NOT LIKE '%SEAPORT%' and movement_classification NOT LIKE '%RECOVERY%'
                ),

                assign_on_time_flag as (
                    select
                        *
                        , case when expected_duration_min between 0 and 240 THEN 30
                                when expected_duration_min > 240 and expected_duration_min <= 480 THEN 60
                                when expected_duration_min > 480 and expected_duration_min <= 960 THEN 120
                                when expected_duration_min > 960 and expected_duration_min <= 1440 THEN 180
                                when expected_duration_min > 1440 THEN 240
                                ELSE null END AS on_time_max_threshold
                        , case when expected_duration_min between 0 and 240 THEN '0 to 240'
                                when expected_duration_min > 240 and expected_duration_min <= 480 THEN '240 to 480'
                                when expected_duration_min > 480 and expected_duration_min <= 960 THEN '480 to 960'
                                when expected_duration_min > 960 and expected_duration_min <= 1440 THEN '960 to 1440'
                                when expected_duration_min > 1440 THEN '> 1440'
                                ELSE null END AS duration_group
                    from base
                )

                select * from assign_on_time_flag
                """,
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                select
                    mte.system_id
                    , mte.trip_id
                    , origin_hub_id
                    , origin_hub_name
                    , origin_hub_region
                    , origin_hub.facility_type origin_hub_facility_type
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , dest_hub.facility_type dest_hub_facility_type
                    , primary_driver_id
                    , primary_driver_info.display_name primary_driver_display_name
                    , primary_driver_info.vendor_name primary_driver_vendor_name
                    , primary_driver_info.employment_type as primary_driver_employment_type
                    , secondary_driver_id
                    , secondary_driver_info.display_name secondary_driver_display_name
                    , secondary_driver_info.vendor_name secondary_driver_vendor_name
                    , secondary_driver_info.employment_type as secondary_driver_employment_type
                    , movement_type_revamp movement_type
                    , case when movement_type_revamp = 'LAND_HAUL' and movement_classification like '%STATION%' THEN replace(movement_classification, 'CROSSDOCK_STATION','STATION')
                           when movement_type_revamp = 'AIR_HAUL' and movement_classification like '%CROSSDOCK_STATION%' THEN replace(movement_classification, 'CROSSDOCK_STATION','HUB')
                           when movement_type_revamp = 'AIR_HAUL' and movement_classification like '%CROSSDOCK%' THEN replace(movement_classification, 'CROSSDOCK','HUB')
                           when movement_type_revamp = 'AIR_HAUL' and movement_classification like '%STATION%' THEN replace(movement_classification, 'STATION','HUB')
                            ELSE movement_classification
                            END AS movement_classification
                    , air_haul_shipment_order_info.vendor_name airhaul_vendor_name
                    , schedule_id
                    , created_datetime
                    , expected_start_datetime
                    , actual_start_datetime
                    , actual_arrival_datetime
                    , expected_arrival_datetime
                    , mte.expected_duration_min
                    , coalesce(air_haul_shipment_order_info.total_trip_shipments , mte.total_shipments) total_shipments
                    , coalesce(air_haul_shipment_order_info.total_trip_orders , mte.total_orders) total_orders
                    , status
                    , mte.on_time_start_flag
                    , case
                        when 
                            mte.on_time_end_flag is null
                            and status not in ('arrived', 'completed')
                            and cast(datediff(now(), created_datetime) as int) > 14 
                            then 0
                        else mte.on_time_end_flag
                        end as on_time_arrival_flag
                    , duration_group
                    , cast(
                            (
                                unix_timestamp(mte.actual_arrival_datetime)
                                    - unix_timestamp(mte.actual_start_datetime)
                                    ) / 60 as int) as actual_duration_min    
                    , mte.start_time_difference_min
                    , abs(mte.start_time_difference_min) start_time_difference_min_abs
                    , mte.end_time_difference_min
                    , abs(mte.end_time_difference_min) end_time_difference_min_abs
                    , departure_distance_from_hub
                    , arrival_distance_from_hub
                    , if(
                        (arrival_distance_from_hub > 1) or (arrival_distance_from_hub is null), 0, 1) arrival_distance_flag
                    , if(
                        (departure_distance_from_hub > 1) or (departure_distance_from_hub is null), 0, 1) departure_distance_flag     
                    , mte.created_month
                from movement_trips_enriched as mte
                inner join duration_group_cte
                    on mte.trip_id = duration_group_cte.trip_id
                left join hubs_enriched origin_hub
                    on origin_hub.id = mte.origin_hub_id
                    and mte.system_id = origin_hub.system_id
                left join hubs_enriched dest_hub
                    on dest_hub.id = mte.dest_hub_id
                    and mte.system_id = dest_hub.system_id
                left join air_haul_shipment_order_info
                    on air_haul_shipment_order_info.trip_id = mte.trip_id
                left join drivers_enriched primary_driver_info
                    on mte.primary_driver_id = primary_driver_info.id
                left join drivers_enriched secondary_driver_info
                    on mte.secondary_driver_id = secondary_driver_info.id
                where 1=1
                    and lower(status) != ('cancelled')
                    and mte.deleted_flag = 0
                    and lower(origin_hub.facility_type) IN ('station','crossdock_station','crossdock','airport')
                    and lower(dest_hub.facility_type) IN ('station','crossdock_station','crossdock','airport')
                )

                select
                    *
                    , case 
                           when status = 'PENDING' THEN 'Trip Not Used'
                           when status = 'TRANSIT' and on_time_start_flag  = 1 THEN 'Trip not Ended and started on time'
                           when status = 'TRANSIT' and on_time_start_flag  = 0 THEN 'Trip not Ended and started not on time'
                           when on_time_start_flag = 1 and on_time_arrival_flag = 1 THEN 'Trip Ended and Started on time'
                           when on_time_start_flag = 0 and on_time_arrival_flag = 1 THEN 'Trip Ended on time but Started not on time'
                           when on_time_start_flag = 0 and on_time_arrival_flag = 0 THEN 'Trip Ended and Started not on time'
                           when on_time_start_flag = 1 and on_time_arrival_flag = 0 THEN 'Trip Started on time but Ended not on time'
                           when on_time_start_flag = 1 and on_time_arrival_flag = 0 THEN 'Trip Started on time but Ended not on time'
                        ELSE null
                        END AS on_time_adherence
                from base
                """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ON_TIME_SHIPMENT_TRIPS_FY24,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)

def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
