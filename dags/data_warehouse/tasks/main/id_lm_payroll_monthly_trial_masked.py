import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_MONTHLY_TRIAL_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_MONTHLY_TRIAL_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_DAILY_TRIAL_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing",),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ID_LM_PAYROLL_DAILY_TRIAL,
                view_name="id_lm_payroll_daily",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PARCEL_RATES,
                view_name="parcel_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_ZONE_DATA_TRIAL,
                view_name="id_zone_data",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hub_umk",
                jinja_template="""
                with base as (
                
                    select
                        hub_id
                        , min_by(umk_category,id) as umk_category
                        , min_by(daily_umk,id) as daily_umk
                    from id_zone_data
                    group by 1

                ),
                final as (
                
                    select distinct
                        id_lm_payroll_daily.driver_id
                        , id_lm_payroll_daily.route_month
                        , base.umk_category
                        , base.daily_umk
                    from id_lm_payroll_daily
                    left join base
                        on id_lm_payroll_daily.driver_hub_id = base.hub_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                
                with base as (
                
                    select
                        system_id
                        , route_month
                        , driver_id
                        , driver_display_name
                        , route_hub_region_scheme as region_scheme
                        , route_hub_region as region
                        , monthly_density_category
                        , sum(case when total_delivered_parcels >= 1 then 1 else 0 end) as days_worked
                        , sum(case when density_category in ('ex','ey','ez') and total_delivered_parcels >= 1 then 1 else 0 end) as e_days_worked
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky
                        , sum(delivered_non_rts_fs_regular) as delivered_non_rts_fs_regular
                        , sum(delivered_non_rts_fs_bulky) as delivered_non_rts_fs_bulky
                        , sum(delivered_non_rts_cs_regular) as delivered_non_rts_cs_regular
                        , sum(delivered_non_rts_cs_bulky) as delivered_non_rts_cs_bulky
                        , sum(delivered_non_rts_retail_regular) as delivered_non_rts_retail_regular
                        , sum(delivered_non_rts_retail_bulky) as delivered_non_rts_retail_bulky
                        , sum(delivered_non_rts_standard_regular) as delivered_non_rts_standard_regular
                        , sum(delivered_non_rts_standard_bulky) as delivered_non_rts_standard_bulky
                        , sum(delivered_non_rts_fs_cs_retail_regular) as delivered_non_rts_fs_cs_retail_regular
                        , sum(delivered_non_rts_fs_cs_retail_bulky) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(delivered_non_rts_superbulky) as delivered_non_rts_superbulky
                        , sum(delivered_non_rts_bulky_revise) as delivered_non_rts_bulky_revise
                        , sum(delivered_rts_regular) as delivered_rts_regular
                        , sum(delivered_rts_bulky) as delivered_rts_bulky
                        , sum(total_delivered_parcels) as total_delivered_parcels
                        , sum(total_ocd_points) as total_ocd_points
                        , sum(base_parcel_pay) as base_parcel_pay
                        , sum(volume_bonus) as volume_bonus
                        from id_lm_payroll_daily
                    group by {{ range(1, 8) | join(',') }}

                ),
                mid as (
                
                    select
                        base.*
                        , case
                            when base.monthly_density_category = 'ex' then 0
                            when base.monthly_density_category in ('ey','ez') then base.total_ocd_points * parcel_rates.parcel_rate
                            else base.base_parcel_pay
                            end as total_parcel_pay
                        , case
                            when base.monthly_density_category in ('ex','ey','ez') then 0
                            else base.volume_bonus
                            end as total_volume_bonus
                        , hub_umk.umk_category as monthly_umk_category
                        , hub_umk.daily_umk
                    from base
                    left join hub_umk
                        on base.driver_id = hub_umk.driver_id
                        and base.route_month = hub_umk.route_month
                    left join parcel_rates
                        on hub_umk.umk_category = parcel_rates.umk_category
                        and base.route_month = parcel_rates.effective_month
                        and base.monthly_density_category = lower(parcel_rates.density_category)
                        and base.region_scheme = parcel_rates.region

                ),
                pre_final as (
                
                    select
                        system_id
                        , route_month
                        , driver_id
                        , driver_display_name
                        , region_scheme
                        , region
                        , monthly_density_category
                        , monthly_umk_category
                        , daily_umk
                        , days_worked
                        , e_days_worked
                        , picked_regular
                        , picked_bulky
                        , delivered_non_rts_fs_regular
                        , delivered_non_rts_fs_bulky
                        , delivered_non_rts_cs_regular
                        , delivered_non_rts_cs_bulky
                        , delivered_non_rts_retail_regular
                        , delivered_non_rts_retail_bulky
                        , delivered_non_rts_standard_regular
                        , delivered_non_rts_standard_bulky
                        , delivered_non_rts_fs_cs_retail_regular
                        , delivered_non_rts_fs_cs_retail_bulky
                        , delivered_non_rts_superbulky
                        , delivered_non_rts_bulky_revise
                        , delivered_rts_regular
                        , delivered_rts_bulky
                        , total_delivered_parcels
                        , total_ocd_points
                        , total_parcel_pay
                        , total_volume_bonus
                        , case
                            when monthly_density_category = 'ex' then daily_umk * days_worked

                            when monthly_density_category = 'ev' and total_ocd_points < 320 then 0
                            when monthly_density_category = 'ev' and days_worked >= 15 and total_ocd_points >= 320 then (0.7 * daily_umk * 26)
                            when monthly_density_category = 'ev' and days_worked < 15 and total_ocd_points >= 320 then (0.7 * daily_umk * days_worked)

                            -- ey density expected to be deprecated from feb 2025 onward
                            when monthly_density_category = 'ey' and total_ocd_points < 200 then 0
                            when monthly_density_category = 'ey' and total_ocd_points >= 200 then (0.7 * daily_umk * 26)

                            when monthly_density_category = 'ez' and total_ocd_points < 250 then 0
                            when monthly_density_category = 'ez' and days_worked >= 15 and total_ocd_points >= 250 then (0.7 * daily_umk * 26)
                            when monthly_density_category = 'ez' and days_worked < 15 and total_ocd_points >= 250 then (0.7 * daily_umk * days_worked)

                            else 0.7 * daily_umk * e_days_worked
                            end as monthly_umk_payment
                    from mid

                ),
                final as (
                
                        select
                            *
                            , (coalesce(total_parcel_pay,0) + coalesce(total_volume_bonus,0) + coalesce(monthly_umk_payment,0)) as grand_total
                            , route_month as created_month
                        from pre_final

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_LM_PAYROLL_MONTHLY_TRIAL,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()