import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_RESOLVED_DAILY_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_RESOLVED_DAILY_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 5)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    date(tickets.resolution_datetime) as resolution_date
                    , tickets.system_id
                    , date_format(tickets.resolution_datetime, 'yyyy-MM') as created_month
                    , count(1) as total_tickets
                    , count_if(
                        tickets.type = 'MISSING'
                        and tickets.outcome like 'LOST%'
                        and orders_enriched.granular_status = 'Cancelled'
                    ) as loss_tickets
                    , count_if(tickets.type = 'DAMAGED' and tickets.outcome like 'NV LIABLE%') as damage_tickets
                    , count_if(
                        (tickets.type = 'DAMAGED' and tickets.outcome like 'NV LIABLE%')
                        or (
                            tickets.type = 'MISSING'
                            and tickets.outcome like 'LOST%'
                            and orders_enriched.granular_status = 'Cancelled'
                        )
                    ) as liability_tickets
                from pets_tickets_enriched as tickets
                left join orders_enriched on
                    tickets.order_id = orders_enriched.order_id
                    and tickets.system_id = orders_enriched.system_id
                where
                    date(tickets.resolution_datetime)
                        < date(from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }}))
                group by 1, 2, 3
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("tickets.system_id"),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PETS_TICKETS_RESOLVED_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
