import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.LAST_MILE_PUSH_OFF_CUTOFFS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(SystemID.GL,),
)


CUTOFF_CONFIGS = (
    {
        "system_id": SystemID.ID,
        "start_date": "'2000-01-01'",
        "end_date": "'2021-07-31'",
        "logic": """
            case
                when (
                    left(name, 3)=right(name, 3)
                    or region in ('Greater Jakarta', 'West Java', 'East Java', 'Central Java')
                    -- below stations have 2nd wave linehaul, so they require later cutoff
                    or id in (
                        153, 1240, 1241, 1247, 1254, 1293, 1296, 1307, 1311, 1319, 1320, 1341
                    )
                )
                    then '13:30'
                else '09:00'
            end
            """,
    },
    {"system_id": SystemID.ID, "start_date": "'2021-08-01'", "end_date": "'2025-02-16'", "logic": "'15:00'"},
    {
        "system_id": SystemID.ID,
        "start_date": "'2025-02-17'",
        "end_date": "'2099-01-01'",
        "logic": """
            case
                when region in ('Greater Jakarta', 'West Java', 'East Java', 'Central Java') then '15:00'
                else '12:00'
            end
            """,
    },
    {"system_id": SystemID.MM, "start_date": "'2000-01-01'", "end_date": "'2099-01-01'", "logic": "'12:00'"},
    {"system_id": SystemID.MY, "start_date": "'2000-01-01'", "end_date": "'2099-01-01'", "logic": "'12:00'"},
    {
        "system_id": SystemID.PH,
        "start_date": "'2000-01-01'",
        "end_date": """
             if(region in ('GMA', 'Metro Manila'), '2022-07-31', '2022-10-14')
         """,
        "logic": "'07:00'",
    },
    {
        "system_id": SystemID.PH,
        "start_date": """
             if(region in ('GMA', 'Metro Manila'), '2022-08-01', '2022-10-15')
         """,
        "end_date": "'2099-01-01'",
        "logic": "'09:00'",
    },
    {"system_id": SystemID.SG, "start_date": "'2000-01-01'", "end_date": "'2021-07-31'", "logic": "'12:00'"},
    {"system_id": SystemID.SG, "start_date": "'2021-08-01'", "end_date": "'2022-11-13'", "logic": "'18:00'"},
    {"system_id": SystemID.SG, "start_date": "'2022-11-14'", "end_date": "'2099-01-01'", "logic": "'16:00'"},
    {"system_id": SystemID.TH, "start_date": "'2000-01-01'", "end_date": "'2099-01-01'", "logic": "'09:00'"},
    {"system_id": SystemID.VN, "start_date": "'2000-01-01'", "end_date": "'2021-07-31'", "logic": "'12:00'"},
    {"system_id": SystemID.VN, "start_date": "'2021-08-01'", "end_date": "'2099-01-01'", "logic": "'11:00'"},
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                {%- for config in cutoff_configs %}
                select
                    cast(id as bigint) as hub_id
                    , {{ config['logic'] }} as cutoff
                    , date({{ config['start_date'] }}) as start_date
                    , date({{ config['end_date'] }}) as end_date
                    , system_id
                from hubs_enriched
                where
                    system_id = '{{ config['system_id'] }}'
                {% if not loop.last %}union all{%- endif %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "cutoff_configs": CUTOFF_CONFIGS,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAST_MILE_PUSH_OFF_CUTOFFS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
