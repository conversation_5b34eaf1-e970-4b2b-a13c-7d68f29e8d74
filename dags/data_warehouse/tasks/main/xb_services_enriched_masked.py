import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_SERVICES_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_SERVICES_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).SERVICES,
                view_name="services",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , code
                    , description
                    , case type
                    {%- for type, name in service_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                        else 'Others'
                    end as type
                    , case 
                        when lower(description) like '%air%' then 'Air'
                        when lower(description) like '%sea%' then 'Sea'
                        when lower(description) like '%ocean%' then 'Sea'
                        when lower(description) like '%land%' then 'Land'
                        when lower(description) like '%truck%' then 'Land'
                    end as freight_type
                    , case
                        when lower(description) like '%normal%' then 'Normal'
                        when lower(description) like '%sensitive%' then 'Sensitive'
                    end as goods_type
                    , 'gl' as system_id
                    , date_format(created_at, 'yyyy-MM') as created_month
                from services
                """,
                jinja_arguments={
                    "service_type_to_name":{
                        1:"LM",
                        2:"CC",
                        3:"CCLM",
                        4:"MMCCLM",
                        5:"MMCC B2C",
                        6:"E2E",
                        8:"MMCC B2B",
                        9:"B2B Bundle",
                    },
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_SERVICES_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
