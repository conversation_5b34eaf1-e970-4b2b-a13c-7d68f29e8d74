import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.DRIVER_PICKUP_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_PICKUP_SCAN_EVENTS,
                view_name="driver_pickup_scan_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                system_id=system_id,
                input_range=lookback_ranges.input,
                version_datetime='latest',
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
                view_name="orders_jobs_assignments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).FM_SHIPPER_EXCLUSIONS + measurement_datetime_partition,
                view_name="fm_shipper_exclusions",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with latest_job_assignment as (

                    select
                        order_id
                        , max_by(job_id, job_assigned_datetime) as assigned_reservation_id
                        , max_by(job_type, job_assigned_datetime) as assigned_job_type
                    from orders_jobs_assignments_enriched
                    group by 1

                ),
                om_filtered as (

                    select
                        order_id
                        , tracking_id
                        , created_month
                        , system_id
                        , creation_datetime
                        , shipper_id
                        , source_id
                        , order_type
                        , delivery_type
                        , dp_dropoff_dp_id
                        , dp_dropoff_datetime
                        , pickup_datetime
                        , pickup_hub_id
                        , is_pickup_required
                        , inbound_datetime
                        , inbound_type
                        , inbound_hub_id
                        , pickup_flag
                        , inbound_flag
                    from order_milestones

                ),
                base as (

                    select
                        om_filtered.*
                        , latest_job_assignment.assigned_reservation_id
                        , latest_job_assignment.assigned_job_type
                    from om_filtered
                    left join latest_job_assignment
                        on om_filtered.order_id = latest_job_assignment.order_id

                ),
                pickup_success as (

                    select
                        order_id
                        , max_by(driver_id, created_at) driver_id
                        , max_by(route_id, created_at) route_id
                        , max_by(reservation_id, created_at) reservation_id
                        , max_by(waypoint_id, created_at) waypoint_id
                        , max(created_at) created_at
                    from order_events_pickup_success
                    group by 1

                ),
                pickup_exclusion as (

                    select 
                        shipper_id
                        , exclusion_reason
                        , start_date
                        , end_date
                        , system_id
                    from fm_shipper_exclusions  

                ),
                driver_pickup_scan_events_cte as (

                    select
                        order_id
                        , max_by(driver_id, created_at) driver_id
                        , max_by(route_id, created_at) route_id
                        , max_by(route_id, reservation_id) reservation_id
                        , max_by(waypoint_id, created_at) waypoint_id
                        , max(created_at) as created_at
                    from driver_pickup_scan_events
                    group by 1

                ),
                join as (

                    select
                        base.order_id
                        , base.tracking_id
                        , base.created_month
                        , base.system_id
                        , shipper_attributes.parent_id
                        , base.shipper_id
                        , shipper_attributes.shipper_name
                        , shipper_attributes.sales_channel
                        , shipper_attributes.parent_name_coalesce
                        , shipper_attributes.parent_id_coalesce
                        , base.order_type
                        , base.source_id
                        , base.creation_datetime
                        , base.dp_dropoff_dp_id
                        , base.dp_dropoff_datetime
                        , base.is_pickup_required
                        , base.pickup_datetime
                        , base.pickup_hub_id
                        , he_pickup.region pickup_hub_region
                        , base.inbound_datetime
                        , base.inbound_type
                        , base.inbound_hub_id
                        , he_inbound.region origin_hub_region
                        , coalesce(base.pickup_hub_id, base.inbound_hub_id) first_scan_hub 
                        , coalesce(he_pickup.region , he_inbound.region) first_scan_region
                        , base.pickup_flag
                        , base.inbound_flag 
                        ,  coalesce(pickup_success.driver_id, driver_pickup_scan_events_cte.driver_id) as driver_id
                        , exclusion_reason
                        , drivers_enriched.driver_type
                        , coalesce(pickup_success.route_id, driver_pickup_scan_events_cte.route_id) as route_id
                        , coalesce(pickup_success.waypoint_id, driver_pickup_scan_events_cte.waypoint_id, reservations_enriched.waypoint_id) as waypoint_id
                        , coalesce(pickup_success.reservation_id, driver_pickup_scan_events_cte.reservation_id) as reservation_id
                        , base.assigned_job_type
                        , case
                            when pickup_success.order_id is not null then 39
                            when driver_pickup_scan_events_cte.order_id is not null then 25
                            end as type
                        , case when pickup_exclusion.shipper_id is not null then 1 else 0 end as shipper_exclusion_flag
                        , coalesce(date(base.pickup_datetime), date(base.inbound_datetime)) report_date
                    from base
                    left join pickup_success
                        on base.order_id = pickup_success.order_id
                    left join drivers_enriched
                        on pickup_success.driver_id = drivers_enriched.id
                    left join shipper_attributes
                        on base.shipper_id = shipper_attributes.id
                    left join pickup_exclusion
                        on base.shipper_id = pickup_exclusion.shipper_id
                        and base.system_id = pickup_exclusion.system_id
                        and base.creation_datetime >= date(pickup_exclusion.start_date)
                        and base.creation_datetime <= date(pickup_exclusion.end_date)
                    left join hubs_enriched he_inbound
                        on he_inbound.id = base.inbound_hub_id
                    left join hubs_enriched he_pickup
                        on he_pickup.id = base.pickup_hub_id
                    left join driver_pickup_scan_events_cte
                        on driver_pickup_scan_events_cte.order_id = base.order_id
                    left join reservations_enriched
                        on reservations_enriched.reservation_id = base.assigned_reservation_id
                        and reservations_enriched.data_source = base.assigned_job_type
                    where 
                        shipper_attributes.sales_channel != 'Test'

                ),
                final as (
                
                    select 
                        *
                        , case
                            when dp_dropoff_datetime is not null 
                                or driver_type like "Mitra%"
                                then 'PUDO/Mitras'
                            when pickup_datetime is not null then 'Warehouse/B2C/Marketplace'
                            when pickup_datetime is null and shipper_exclusion_flag = 1 then exclusion_reason
                            when pickup_datetime is null and shipper_exclusion_flag = 0 and is_pickup_required = 0 then 'Order Set as Pickup Not Required'
                            when pickup_datetime is null and shipper_exclusion_flag = 0 then 'No pickup_scan'
                            else 'Undefined' end as pickup_channel
                    from join
                
                )

                select * from final
                
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIRST_MILE_VOLUME_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()