import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.COURIER_DISCIPLINE_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.COURIER_DISCIPLINE_REPORT_MASKED,
    system_ids=(
        constants.SystemID.MY,
        constants.SystemID.PH,
    ),
    depends_on=(data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
                data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.COURIER_DISCIPLINE_REPORT_TARGETS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
    )
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).COURIER_DISCIPLINE_REPORT_TARGETS,
                view_name="courier_discipline_report_targets",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverVantageProdGL(input_env, is_masked).PENALTY_V2,
                view_name="penalty_v2",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.DriverVantageProdGL(input_env, is_masked).SUSPENSION_V2,
                view_name="suspension_v2",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_TARGETS,
                view_name="targets",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_TARGETS_WEEKEND,
                view_name="targets_weekend",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_SUSPENSION_CONFIGS,
                view_name="suspension_configs",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_SUSPENSION_LEVELS,
                view_name="suspension_levels",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).COURIER_DISCIPLINE_REPORT_DRIVER_TYPES,
                view_name="driver_types",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).PH_LM_MANUAL_REGION_MAPPING,
                view_name="ph_region_mapping",
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="accumulated_punishments_cte",
                jinja_template="""
                with fleet_base as (
                select
                    fleet_performance_base_data.courier_id as driver_id
                    , fleet_performance_base_data.route_date
                    , drivers_enriched.hub_id as driver_hub_id
                    , drivers_enriched.employment_start_date
                    , coalesce(ph_region_mapping.region, hubs_enriched.region) as driver_hub_region
                    -- For 3 islamic state in MY, Friday is considered non working day but not Saturday.
                    -- This filter will include Friday for these state and Saturdays.
                    , if(calendar.working_day = 0 or calendar.day = 'Saturday' , 1, 0) as weekend_public_holiday_flag
                    , fleet_performance_base_data.system_id
                    , round(sum(fleet_performance_base_data.parcels_delivered) 
                        / sum(fleet_performance_base_data.parcels_on_route),4) as success_rate
                from fleet_performance_base_data
                left join drivers_enriched
                    on fleet_performance_base_data.courier_id = drivers_enriched.id
                left join hubs_enriched
                    on drivers_enriched.hub_id = hubs_enriched.id
                    and drivers_enriched.system_id = hubs_enriched.system_id
                left join ph_region_mapping
                    on ph_region_mapping.hub_id = hubs_enriched.id
                    and fleet_performance_base_data.system_id = 'ph'
                left join driver_types
                    on fleet_performance_base_data.courier_type = driver_types.driver_type
                    and fleet_performance_base_data.route_date >= driver_types.start_date
                    and fleet_performance_base_data.route_date <= driver_types.end_date
                -- Get the weekend/public holidays info from calendar table
                left join calendar
                    on fleet_performance_base_data.route_date = calendar.date
                     and ((calendar.region = 'national' and fleet_performance_base_data.system_id not in ('my'))
                         or (fleet_performance_base_data.system_id = 'my' 
                            and lower(calendar.region) = lower(hubs_enriched.address_city)))
                where driver_types.driver_type is not null
                group by {{ range(1, 8) | join(',') }}
                ),

                success_rate_calculation_cte as (
                select
                    fleet_base.driver_id
                    , fleet_base.driver_hub_id
                    , fleet_base.driver_hub_region
                    , fleet_base.route_date
                    , fleet_base.employment_start_date
                    , fleet_base.success_rate
                    , cast(if(fleet_base.weekend_public_holiday_flag = 1
                        , sr_targets_weekend.kpi_target,sr_targets.kpi_target) as double) as success_rate_target
                    , fleet_base.system_id
                from fleet_base
                left join targets as sr_targets
                    on fleet_base.system_id = sr_targets.system_id
                    and sr_targets.kpi_name = 'success_rate'
                    and fleet_base.driver_hub_region = sr_targets.hub_region
                    and fleet_base.route_date >= sr_targets.start_date
                    and fleet_base.route_date <= sr_targets.end_date
                left join targets_weekend as sr_targets_weekend
                    on fleet_base.system_id = sr_targets_weekend.system_id
                    and sr_targets_weekend.kpi_name = 'success_rate'
                    and fleet_base.driver_hub_region = sr_targets_weekend.hub_region
                    and fleet_base.route_date >= sr_targets_weekend.start_date
                    and fleet_base.route_date <= sr_targets_weekend.end_date
                where 1=1
                    -- Filter out regions that are not in scope according to the targets Gsheet
                    and if(fleet_base.weekend_public_holiday_flag = 1
                        , sr_targets_weekend.kpi_target is not null,sr_targets.kpi_target is not null)
                ),

                merged_cte as (
                select
                    success_rate_calculation_cte.driver_id
                    , success_rate_calculation_cte.driver_hub_id
                    , success_rate_calculation_cte.driver_hub_region
                    , success_rate_calculation_cte.route_date
                    , suspension_configs.suspension_limit
                    , success_rate_calculation_cte.success_rate
                    , success_rate_calculation_cte.success_rate_target
                    -- For drivers that meet the target, hard code as NO_PUNISHMENT
                    , if(success_rate_calculation_cte.success_rate < success_rate_calculation_cte.success_rate_target
                        , penalty_v2.punishment, 'NO_PUNISHMENT') as punishment
                    , date(if(penalty_v2.punishment like '%SUSPENSION%'
                        , penalty_v2.performance_date + interval '2' day, null)) as suspension_start_date
                    , success_rate_calculation_cte.system_id
                from success_rate_calculation_cte
                left join penalty_v2
                    on penalty_v2.driver_id = success_rate_calculation_cte.driver_id
                    and penalty_v2.performance_date = success_rate_calculation_cte.route_date
                    and penalty_v2.status != 'REVOKED'
                    and penalty_v2.system_id = '{{ system_id }}'
                left join suspension_configs
                    on suspension_configs.system_id = success_rate_calculation_cte.system_id
                    and success_rate_calculation_cte.route_date >= suspension_configs.start_date
                    and success_rate_calculation_cte.route_date <= suspension_configs.end_date
                where 1=1
                    and success_rate_calculation_cte.route_date >= (date('{{ measurement_datetime }}') 
                        - cast(suspension_configs.punishment_effective_days||" day" as INTERVAL) - interval '2' day)
                    -- Filter out new couriers that perform routes within the grace period
                    and success_rate_calculation_cte.route_date >= (success_rate_calculation_cte.employment_start_date 
                        + cast(suspension_configs.new_courier_grace_period_days||" day" as INTERVAL))
                ),

                -- To dedupe the tables when duplication issues inflates a driver's accumulated punishment count
                dedupe_cte as (
                    select
                        *
                        , row_number() over(partition by driver_id, route_date order by route_date) as rnk
                    from merged_cte
                    -- Remove this filter, to be implemented at a later date once product team is ready
                 --   where success_rate < success_rate_target
                ),

                -- This creates punishment flag from 1. Punishment (for histrical data), 2. SR, from current day's perf.
                -- This to find the number of active punishment as using punishment column alone won't suffice for D0.
                set_punishment_assigned_flag as (
                select
                    *
                    -- Ignore NO_PUNISHMENT when calculating punishment assigned flag
                    , if(punishment != 'NO_PUNISHMENT' 
                        or (success_rate < success_rate_target and route_date = date('{{ measurement_datetime }}'))
                            , 1,0) as punishment_assigned_flag
                from dedupe_cte
                where rnk = 1
                ),

                total_accumulated_punishment_cte as (
                select
                    driver_id
                    , route_date
                    , sum(sum(punishment_assigned_flag))
                        over(partition by driver_id order by route_date) as accumulated_punishment_count
                from set_punishment_assigned_flag
                group by {{ range(1, 3) | join(',') }}
                ),

                final as (
                select 
                    set_punishment_assigned_flag.*
                    , total_accumulated_punishment_cte.accumulated_punishment_count
                from set_punishment_assigned_flag
                left join total_accumulated_punishment_cte
                    on set_punishment_assigned_flag.driver_id = total_accumulated_punishment_cte.driver_id
                    and set_punishment_assigned_flag.route_date = total_accumulated_punishment_cte.route_date
                order by 1,4
                ),

                last as (
                select *
                from final
                where route_date = date('{{ measurement_datetime }}')
                )

                select *
                from last
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "system_id": system_id,
                },
            ),

            base.TransformView(
                view_name="1st_outcome_cte",
                jinja_template="""
                select
                    accumulated_punishments_cte.driver_id
                    , accumulated_punishments_cte.driver_hub_id
                    , accumulated_punishments_cte.driver_hub_region
                    , accumulated_punishments_cte.route_date
                    , accumulated_punishments_cte.suspension_limit
                    , accumulated_punishments_cte.success_rate
                    , accumulated_punishments_cte.success_rate_target
                    , accumulated_punishments_cte.system_id
                    , suspension_levels.suspension_days
                    , if(accumulated_punishments_cte.route_date = date('{{ measurement_datetime }}') 
                        and suspension_levels.suspension_days is not null,
                            if(suspension_levels.suspension_days = 0, 'WARNING',
                                if(suspension_levels.suspension_days != 9999, 'TEMP_SUSPENSION','PERM_SUSPENSION'))
                                    , accumulated_punishments_cte.punishment) as outcome_1st_check
                    , if(suspension_levels.suspension_days is null or suspension_levels.suspension_days = 0
                        , null, accumulated_punishments_cte.route_date + interval '2' days) as suspension_start_date
                    , if(suspension_levels.suspension_days = 9999 or suspension_levels.suspension_days is null 
                        or suspension_levels.suspension_days = 0, null
                            , accumulated_punishments_cte.route_date + interval '1' day 
                                + cast(suspension_levels.suspension_days|| " day" as INTERVAL)) as suspension_end_date
                    , courier_discipline_report_targets.success_rate_target as next_day_kpi_target
                from accumulated_punishments_cte
                left join suspension_levels
                    on suspension_levels.system_id = accumulated_punishments_cte.system_id
                    and accumulated_punishments_cte.accumulated_punishment_count = suspension_levels.punishment_no
                    -- only join for drivers that require calculation of today's punishment
                    and accumulated_punishments_cte.punishment is null
                left join courier_discipline_report_targets
                    on courier_discipline_report_targets.hub_id = accumulated_punishments_cte.driver_hub_id
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),

            base.TransformView(
                view_name="hub_daily_suspension_cte",
                jinja_template="""
                -- CTE to get the number of active suspended drivers per hub per day
                with active_suspended_driver_level_cte as (
                select
                    penalty_v2.driver_id
                    , drivers_enriched.hub_id as driver_hub_id
                    , penalty_v2.system_id
                    , explode(sequence(suspension_v2.start_date, suspension_v2.end_date)) as suspension_date
                from penalty_v2
                left join drivers_enriched
                    on penalty_v2.driver_id = drivers_enriched.id
                left join suspension_v2
                    on penalty_v2.suspension_id = suspension_v2.id
                where penalty_v2.system_id = '{{ system_id }}'
                    and penalty_v2.status != 'REVOKED'
                ),

                active_suspended_driver_hub_level as (
                select
                    active_suspended_driver_level_cte.driver_hub_id
                    , active_suspended_driver_level_cte.suspension_date
                    , suspension_configs.suspension_limit
                    , count(distinct active_suspended_driver_level_cte.driver_id) as active_drivers_suspended_count
                from active_suspended_driver_level_cte
                left join suspension_configs
                    on active_suspended_driver_level_cte.system_id = suspension_configs.system_id
                    and active_suspended_driver_level_cte.suspension_date >= suspension_configs.start_date
                    and active_suspended_driver_level_cte.suspension_date <= suspension_configs.end_date
                group by {{ range(1, 4) | join(',') }}
                ),

                hub_level as (
                select
                    driver_hub_id
                     , suspension_date
                     , suspension_limit
                     -- If number of active suspended drivers is greater than the limit, no suspension should happen
                     -- Else, find the difference and get the number of drivers that can be suspended
                     , if(suspension_limit <= active_drivers_suspended_count, 0
                         ,suspension_limit - active_drivers_suspended_count) as number_of_drivers_to_suspend
                from active_suspended_driver_hub_level
                )

                select 
                    *
                from hub_level
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with driver_ranking_cte as (
                -- Only perform the performance ranking for drivers that will be temporarily suspended
                select 
                    1st_outcome_cte.*
                    , coalesce(hub_daily_suspension_cte.number_of_drivers_to_suspend
                        , 1st_outcome_cte.suspension_limit) as number_of_drivers_to_suspend
                    , row_number() 
                        over(partition by 1st_outcome_cte.driver_hub_id, 1st_outcome_cte.route_date 
                            order by 1st_outcome_cte.success_rate) as driver_suspension_rank
                from 1st_outcome_cte
                left join hub_daily_suspension_cte
                    on 1st_outcome_cte.driver_hub_id = hub_daily_suspension_cte.driver_hub_id
                    and 1st_outcome_cte.suspension_start_date = hub_daily_suspension_cte.suspension_date
                where outcome_1st_check = 'TEMP_SUSPENSION'
                ),

                suspend_driver_cte as (
                select
                    *
                    -- For drivers who are not suspended due to suspension limit, hard code as NO_PUNISHMENT
                    , if(
                        (driver_suspension_rank <= number_of_drivers_to_suspend 
                            or date('{{ measurement_datetime }}') != route_date and suspension_days > 0)
                        or (suspension_days = 0) , outcome_1st_check, 'NO_PUNISHMENT') as outcome_final
                from driver_ranking_cte
                ),

                performance_compile as (
                select
                    driver_id
                    , driver_hub_id
                    , driver_hub_region
                    , route_date as evaluation_date
                    , to_json(named_struct(
                                            'kpi_name','success_rate',
                                            'kpi_performance', success_rate,
                                            'kpi_target', success_rate_target,
                                            'next_day_kpi_target', next_day_kpi_target)) as kpi1_data 
                    , suspension_days
                    , suspension_start_date
                    , suspension_end_date
                    , outcome_final as punishment
                    , system_id
                    , date_format(route_date, 'yyyy-MM') as created_month
                from suspend_driver_cte
                -- filter out the drivers that were not suspended due to hub limit
                where outcome_final is not null
                -- Union the drivers with TEMP_SUSPENSION that were filtered in driver_ranking_cte
                union all
                select
                    driver_id
                    , driver_hub_id
                    , driver_hub_region
                    , route_date as evaluation_date
                    , to_json(named_struct(
                                            'kpi_name','success_rate',
                                            'kpi_performance', success_rate,
                                            'kpi_target', success_rate_target,
                                            'next_day_kpi_target', next_day_kpi_target)) as kpi1_data 
                    , suspension_days
                    , suspension_start_date
                    , suspension_end_date
                    -- Drivers with outcome that's not TEMP_SUSPENSION will have outcome_1st_check as their punishment
                    , outcome_1st_check as punishment
                    , system_id
                    , date_format(route_date, 'yyyy-MM') as created_month
                from 1st_outcome_cte
                where outcome_1st_check != 'TEMP_SUSPENSION'
                ),

                final as (
                select
                    driver_id
                    , cast(driver_hub_id as bigint) as driver_hub_id
                    , driver_hub_region
                    , evaluation_date
                    , to_json(named_struct('kpi_1', 
                        from_json(kpi1_data, 'kpi_name string, kpi_performance double
                            , kpi_target double, next_day_kpi_target double'))) as kpi_data
                    , suspension_days
                    , suspension_start_date
                    , suspension_end_date
                    , punishment
                    , system_id
                    , created_month
                from performance_compile
                )

                select *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                }
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COURIER_DISCIPLINE_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()