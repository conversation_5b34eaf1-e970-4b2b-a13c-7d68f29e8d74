import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_BASE_DATA_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_PLANNED_PARCELS_EVENTS_MASKED,
    ),
)

special_hybrid_driver_id = '(200046,200487,200501,1013700, 1653789)'


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_BASE_DATA,
                view_name="orders_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_PLANNED_PARCELS_EVENTS,
                view_name="planned_parcels_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_DELIVERY_RATES,
                view_name="delivery_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_RESERVATION_RATES,
                view_name="reservation_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_FREE_PARCELS,
                view_name="free_parcels",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_BALLOON_TARGETS,
                view_name="balloon_targets",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_LOGIC,
                view_name="logic",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).HUBS, view_name="hubs"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="delivery_rates_cte",
                jinja_template=""" 
                with base as (
                select 
                    *
                    ,  date(lag(start_date)
                        over(partition by courier_type, weight_start, weight_end order by start_date desc) 
                            - interval '1' day)
                    as end_date_lag
                from delivery_rates
                where courier_type not like '%INDEPENDENT%'
                )

                select
                    courier_type
                    , weight_start
                    , coalesce(weight_end, 999999) as weight_end
                    , ppr1 as ppr
                    , case when courier_type like '%HYBRID%' and courier_type not like '%SPECIAL%' 
                        then if(weight_start != 8.0, concat(cast(weight_start as int),'-',cast(weight_end as int),'kg'), '>8kg')
                        when courier_type like '%SPECIAL%'
                        then if(weight_start != 16.0, concat(cast(weight_start as int),'-',cast(weight_end as int),'kg'), '>16kg')
                        when courier_type like 'SAMEDAY STAFF%'
                        then if(weight_start != 8.0, concat(cast(weight_start as int),'-',cast(weight_end as int),'kg'), '>8kg')
                        else null
                    end as weight_tier
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="reservation_rates_cte",
                jinja_template=""" 
                with base as (
                select 
                    *
                    ,  date(lag(start_date)
                        over(partition by courier_type order by start_date desc) - interval '1' day)
                        as end_date_lag
                from reservation_rates
                where courier_type not like '%INDEPENDENT%'
                )

                select
                    courier_type
                    , rsvn_ppr
                    , picked_up_parcel_ppr
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="free_parcels_cte",
                jinja_template=""" 
                with base as (
                select 
                    *
                    ,  date(lag(start_date)
                        over(partition by courier_type, free_parcel_threshold order by start_date desc)
                            - interval '1' day)
                    as end_date_lag
                from free_parcels
                )

                select
                    courier_type
                    , free_parcel_threshold
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="balloon_targets_cte",
                jinja_template=""" 
                with base as (
                select 
                    *
                    ,  date(lag(start_date)
                        over(partition by courier_type, parcels_delivered_start, parcels_delivered_end
                            , balloon_bonus_amount order by start_date desc) - interval '1' day)
                    as end_date_lag
                from balloon_targets
                )

                select
                    courier_type
                    , parcels_delivered_start
                    , parcels_delivered_end
                    , cast(balloon_bonus_amount as int) as balloon_bonus_amount
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="logic_cte",
                jinja_template=""" 
                with base as (
                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from logic
                    where lower(courier_type) like '%hybrid%'
                )

                select
                    logic
                    , courier_type
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="waypoint_group_cte",
                jinja_template=""" 
                -- Order_id and waypoint_id is the composite key and rank orders in same waypoint according to weight

                with dedup_rnk as (
                    select 
                        *
                        , row_number() over (partition by order_id, waypoint_id order by delivery_attempt_datetime desc)
                         as rnk
                    from orders_base_data
                    where 1=1
                        and transaction_type = 'DD'
                        and driver_type like '%HYBRID%DRIVER%'
                        and transaction_status = 'Success'

                ),

                dedup as (
                    select
                        order_id
                        , waypoint_id
                        , weight
                    from dedup_rnk
                    where rnk = 1
                )

                select
                    order_id
                    , waypoint_id
                    , weight
                    , row_number() over(partition by waypoint_id order by weight desc) weight_rank
                from dedup
                """,
            ),

            base.TransformView(
                view_name="hybrid_delivery_data",
                jinja_template="""
                with hybrid_delivery_base as (
                    select
                        orders_base_data.order_id
                        , date(orders_base_data.delivery_attempt_datetime) as route_date
                        , orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , orders_base_data.route_id
                        , orders_base_data.weight
                        , logic_cte.logic
                        , waypoint_group_cte.weight_rank
                        , if(waypoint_group_cte.weight_rank > 1 and lower(logic_cte.logic) like '%merged waypoint', 1,0)
                            as merged_deliveries_flag
                        , orders_base_data.transaction_status
                        , coalesce(hybrid_ppr.weight_tier, hybrid_ppr_special.weight_tier, ssd_ppr.weight_tier)
                            as weight_tier
                        , coalesce(hybrid_ppr.ppr, hybrid_ppr_special.ppr, ssd_ppr.ppr) as ppr
                        , free_parcels_cte.free_parcel_threshold
                        , 'delivery' as payscheme_component
                        , date_format(orders_base_data.delivery_attempt_datetime, 'yyyy-MM') created_month
                    from orders_base_data
                    left join delivery_rates_cte as hybrid_ppr
                        on orders_base_data.weight > hybrid_ppr.weight_start
                        and orders_base_data.weight <= hybrid_ppr.weight_end
                        and orders_base_data.driver_type = hybrid_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr.end_date)
                        and orders_base_data.driver_id not in """ + special_hybrid_driver_id + """

                    left join delivery_rates_cte as hybrid_ppr_special
                        on orders_base_data.driver_id in """ + special_hybrid_driver_id + """
                        and orders_base_data.weight > hybrid_ppr_special.weight_start
                        and orders_base_data.weight <= hybrid_ppr_special.weight_end
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr_special.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr_special.end_date)
                        and hybrid_ppr_special.courier_type = 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)'

                    left join delivery_rates_cte as ssd_ppr
                        on orders_base_data.weight > ssd_ppr.weight_start
                        and orders_base_data.weight <= ssd_ppr.weight_end
                        and orders_base_data.driver_type = ssd_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(ssd_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(ssd_ppr.end_date)

                    left join free_parcels_cte
                        on orders_base_data.driver_type = free_parcels_cte.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(free_parcels_cte.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(free_parcels_cte.end_date)
                        and orders_base_data.driver_id not in """ + special_hybrid_driver_id + """

                    left join waypoint_group_cte
                        on orders_base_data.order_id = waypoint_group_cte.order_id
                        and orders_base_data.waypoint_id = waypoint_group_cte.waypoint_id

                    left join logic_cte
                        -- Hardcode the 4 special driver types to the psuedo driver type that we use on the gsheet
                        on if(orders_base_data.driver_id in """ + special_hybrid_driver_id + """
                            ,'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)', orders_base_data.driver_type) 
                                = logic_cte.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= logic_cte.start_date
                        and date(orders_base_data.delivery_attempt_datetime) <= logic_cte.end_date

                    where 1=1
                        and orders_base_data.transaction_type = 'DD'
                        and (
                          orders_base_data.driver_type LIKE '%HYBRID%'
                          or orders_base_data.driver_type LIKE '%SAMEDAY%STAFF%'
                          )
                ),

                -- Apply Merged Waypoint logic
                merged_waypoint_ppr_cte as (
                    select
                        order_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , route_id
                        , weight
                        , transaction_status
                        , weight_tier
                        , case when merged_deliveries_flag = 1
                            then 0.5 * ppr
                            else ppr end as ppr
                        , free_parcel_threshold
                        , payscheme_component
                        , merged_deliveries_flag
                        , created_month
                    from hybrid_delivery_base
                ),   

                payment_rank_cte as (
                    select
                        driver_id
                        , order_id
                        , route_date
                        -- Rank the orders by the ppr (non merged delivery) or weight (merged delivery)
                        , if(lower(driver_type) like '%driver%',
                            row_number() over(partition by driver_id, route_date order by ppr),
                            row_number() over(partition by driver_id, route_date order by weight)) as payment_rank
                    from merged_waypoint_ppr_cte
                    where 1=1
                        and transaction_status = 'Success'
                ),

                payable_lm_parcels_cte as (
                    select
                        merged_waypoint_ppr_cte.*
                        , payment_rank_cte.payment_rank
                        -- Orders will only be paid if their payment rank is greater than the free parcel threshold
                        -- No free parcel threshold for the special guys
                        , if((payment_rank_cte.payment_rank > merged_waypoint_ppr_cte.free_parcel_threshold
                            and merged_waypoint_ppr_cte.driver_id not in """ + special_hybrid_driver_id + """)
                            or merged_waypoint_ppr_cte.driver_id in """ + special_hybrid_driver_id + """
                            , 1,0) as payable_parcel_flag
                    from merged_waypoint_ppr_cte
                    left join payment_rank_cte
                        on merged_waypoint_ppr_cte.order_id = payment_rank_cte.order_id
                        and merged_waypoint_ppr_cte.driver_id = payment_rank_cte.driver_id
                        and merged_waypoint_ppr_cte.route_date = payment_rank_cte.route_date
                ),

                aggregated_route_level as (
                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , route_date
                        , ppr
                        , free_parcel_threshold
                        , created_month
                        , count(distinct order_id) filter(where transaction_status = 'Success') as parcels_delivered
                        --  Breakdown only includes payable parcels
                        , count(distinct order_id) filter(where transaction_status = 'Success' 
                            and payable_parcel_flag = 1 and merged_deliveries_flag = 1) 
                        as merged_parcels_delivered
                        , count(distinct order_id) filter(where transaction_status = 'Success' 
                            and payable_parcel_flag = 1 and merged_deliveries_flag = 0) 
                        as non_merged_parcels_delivered
                        , count(distinct order_id) filter(where transaction_status = 'Success'
                            and payable_parcel_flag = 1) 
                        as payable_lm_parcels
                    from payable_lm_parcels_cte
                    group by {{ range(1, 9) | join(',') }}
                ),

                final as (
                select 
                    * 
                    , payable_lm_parcels * ppr as delivery_bonus_per_weight_tier
                    , merged_parcels_delivered * ppr as merged_delivery_bonus_per_weight_tier
                    , non_merged_parcels_delivered * ppr as non_merged_delivery_bonus_per_weight_tier
                from aggregated_route_level
                )

                select *
                from final
                """,
            ),

            base.TransformView(
                view_name="hybrid_rpu_data",
                jinja_template=""" 
                with hybrid_rpu_base as (
                    select
                        orders_base_data.order_id
                        , date(orders_base_data.pickup_datetime) as route_date
                        , orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , orders_base_data.route_id
                        , orders_base_data.weight
                        , orders_base_data.transaction_status
                        , coalesce(hybrid_ppr.weight_tier, hybrid_ppr_special.weight_tier) as weight_tier
                        , coalesce(hybrid_ppr.ppr, hybrid_ppr_special.ppr) as ppr
                        , 'rpu' as payscheme_component
                        , date_format(orders_base_data.pickup_datetime, 'yyyy-MM') created_month
                    from orders_base_data
                    left join delivery_rates_cte as hybrid_ppr
                        on orders_base_data.weight > hybrid_ppr.weight_start
                        and orders_base_data.weight <= hybrid_ppr.weight_end
                        and orders_base_data.driver_type = hybrid_ppr.courier_type
                        and date(orders_base_data.pickup_datetime) >= date(hybrid_ppr.start_date)
                        and date(orders_base_data.pickup_datetime) <= date(hybrid_ppr.end_date)
                        and orders_base_data.driver_id not in """ + special_hybrid_driver_id + """

                    left join delivery_rates_cte as hybrid_ppr_special
                        on orders_base_data.driver_id in """ + special_hybrid_driver_id + """
                        and orders_base_data.weight > hybrid_ppr_special.weight_start
                        and orders_base_data.weight <= hybrid_ppr_special.weight_end
                        and date(orders_base_data.pickup_datetime) >= date(hybrid_ppr_special.start_date)
                        and date(orders_base_data.pickup_datetime) <= date(hybrid_ppr_special.end_date)
                        and hybrid_ppr_special.courier_type = 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)'

                    where 1=1
                        and orders_base_data.transaction_type = 'PP'
                        and (
                          orders_base_data.driver_type LIKE '%HYBRID%'
                          )
                    ),

                rpu_aggregated as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , ppr
                        , route_date
                        , created_month
                        , count(distinct order_id) filter(where transaction_status = 'Success') 
                        as return_parcels_picked_up
                    from hybrid_rpu_base
                    group by {{ range(1, 8) | join(',') }}
                )

                select 
                    * 
                    , (ppr * return_parcels_picked_up) as customer_pickup_bonus_per_weight_tier
                from rpu_aggregated
            """,
            ),

            base.TransformView(
                view_name="hybrid_reservation_data",
                jinja_template=""" 
                with base as (
                select
                    route_driver_id as driver_id
                    , route_driver_name as driver_name
                    , route_driver_type as driver_type
                    , date(attempted_datetime) as route_date
                    , created_month
                    , count(reservation_id) filter(where status IN ('Success','COMPLETED')) as successful_reservations
                    , sum(payable_picked_up_orders) filter(where status IN ('Success','COMPLETED')
                    ) as payable_picked_up_orders
                from reservations_enriched
                where 1=1
                    and attempted_datetime is not null
                    and route_id is not null
                    and route_driver_type not like '%INDEPENDENT%'
                group by {{ range(1, 6) | join(',') }}
                )

                select
                    base.driver_name
                    , base.driver_id
                    , base.driver_type
                    , base.route_date
                    , 'Reservation' as weight_tier
                    , null as parcels_on_route
                    , null as parcels_delivered
                    , null as merged_parcels_delivered
                    , null as non_merged_parcels_delivered
                    , null as failed_parcels
                    , null as free_parcel_threshold
                    , null as return_parcels_picked_up
                    , null as payable_lm_parcels
                    , base.created_month
                    , sum(base.successful_reservations) as total_reservation
                    , sum(null) as delivery_bonus_per_weight_tier
                    , sum(null) as merged_delivery_bonus_per_weight_tier
                    , sum(null) as non_merged_delivery_bonus_per_weight_tier
                    , sum(null) as customer_pickup_bonus_per_weight_tier
                    , sum(base.successful_reservations * reservation_rates_cte.rsvn_ppr 
                        + base.payable_picked_up_orders * reservation_rates_cte.picked_up_parcel_ppr
                    ) as merchant_pickup_bonus
                from base
                left join reservation_rates_cte
                    on base.driver_type = reservation_rates_cte.courier_type
                    and base.route_date >= reservation_rates_cte.start_date
                    and base.route_date <= reservation_rates_cte.end_date
                group by {{ range(1, 15) | join(',') }}
                """,
            ),

            base.TransformView(
                view_name="parcels_on_route_agg",
                jinja_template=""" 
                with driver_inbound_scans_base as (                
                    select
                        order_id
                        , route_id
                        , max_by(driver_id, created_at) filter(where driver_id is not null) as driver_id
                        , max_by(type, created_at) as type
                        , max(created_at) as created_at
                    from planned_parcels_events
                    group by {{ range(1, 3) | join(',') }}
                ),

                driver_inbound_scans_agg as (
                    select
                        driver_id
                        , date(created_at) as route_date
                        , count(order_id) as parcels_on_route
                    from driver_inbound_scans_base
                    where type = 24
                    group by {{ range(1, 3) | join(',') }}
                )

                select * from driver_inbound_scans_agg
                """,
            ),

            base.TransformView(
                view_name="hybrid_delivery_data_final",
                jinja_template=""" 
                 with ppr_incentives_table as (
                    select
                        coalesce(hybrid_delivery_data.driver_display_name, hybrid_rpu_data.driver_display_name) 
                        as driver_display_name
                        , coalesce(hybrid_delivery_data.driver_id, hybrid_rpu_data.driver_id) as driver_id
                        , coalesce(hybrid_delivery_data.driver_type, hybrid_rpu_data.driver_type) as driver_type
                        , coalesce(hybrid_delivery_data.route_date, hybrid_rpu_data.route_date) as route_date
                        , coalesce(hybrid_delivery_data.weight_tier, hybrid_rpu_data.weight_tier) as weight_tier
                        , hybrid_delivery_data.parcels_delivered
                        , hybrid_delivery_data.merged_parcels_delivered
                        , hybrid_delivery_data.non_merged_parcels_delivered
                        , hybrid_delivery_data.free_parcel_threshold
                        , hybrid_rpu_data.return_parcels_picked_up
                        , hybrid_delivery_data.payable_lm_parcels
                        , null as total_reservation
                        , hybrid_delivery_data.delivery_bonus_per_weight_tier
                        , hybrid_delivery_data.merged_delivery_bonus_per_weight_tier
                        , hybrid_delivery_data.non_merged_delivery_bonus_per_weight_tier
                        , hybrid_rpu_data.customer_pickup_bonus_per_weight_tier
                        , null as merchant_pickup_bonus
                        , coalesce(hybrid_delivery_data.created_month, hybrid_rpu_data.created_month) as created_month
                    from hybrid_delivery_data
                    full outer join hybrid_rpu_data
                        on hybrid_delivery_data.driver_id = hybrid_rpu_data.driver_id
                        and hybrid_delivery_data.route_date = hybrid_rpu_data.route_date
                        and hybrid_delivery_data.weight_tier = hybrid_rpu_data.weight_tier
                    ),
                parcels_on_route_agg as (
                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as parcels_on_route
                    from orders_base_data
                    where orders_base_data.transaction_type = 'DD'
                        and driver_type not like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}
                ),

                failed_parcels_cte as (
                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as failed_parcels
                    from orders_base_data
                    where orders_base_data.transaction_type = 'DD'
                        and transaction_status = 'Fail'
                        and driver_type not like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}
                )

                select 
                    ppr_incentives_table.driver_display_name
                    , ppr_incentives_table.driver_id
                    , ppr_incentives_table.driver_type
                    , ppr_incentives_table.route_date
                    , ppr_incentives_table.weight_tier
                    , parcels_on_route_agg.parcels_on_route
                    , ppr_incentives_table.parcels_delivered
                    , ppr_incentives_table.merged_parcels_delivered
                    , ppr_incentives_table.non_merged_parcels_delivered
                    , coalesce(failed_parcels_cte.failed_parcels,0) as failed_parcels
                    , ppr_incentives_table.free_parcel_threshold
                    , ppr_incentives_table.return_parcels_picked_up
                    , ppr_incentives_table.payable_lm_parcels
                    , ppr_incentives_table.created_month
                    , ppr_incentives_table.total_reservation
                    , ppr_incentives_table.delivery_bonus_per_weight_tier
                    , ppr_incentives_table.merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.non_merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.customer_pickup_bonus_per_weight_tier
                    , ppr_incentives_table.merchant_pickup_bonus
                from ppr_incentives_table
                left join parcels_on_route_agg
                    on ppr_incentives_table.driver_id = parcels_on_route_agg.driver_id
                    and ppr_incentives_table.route_date = parcels_on_route_agg.route_date
                left join failed_parcels_cte
                    on ppr_incentives_table.driver_id = failed_parcels_cte.driver_id
                    and ppr_incentives_table.route_date = failed_parcels_cte.route_date
                """,
            ),
            base.TransformView(
                view_name="combined_data",
                jinja_template=""" 
                with combined_base as (
                    select *
                    from hybrid_delivery_data_final
                    union all
                    select * from 
                    hybrid_reservation_data
                ),

                 special_driver_type_cte as (
                     select 
                         *
                     -- Used to get distinguish the 4 special guys by courier id. Not to be used in final output
                         , if(driver_id in """ + special_hybrid_driver_id + """
                            , 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)', driver_type) 
                         as driver_type_modified
                    from combined_base
                 ),
                 merge_all as (
                {%- for driver_type_in_list in driver_types_list %}
                 select 
                     'my' as system_id
                     , route_date
                     , date_format(route_date, 'yyyy-MM') as route_month
                     , driver_id
                     , driver_type
                     , driver_display_name
                     , if(datediff(route_date, 
                            from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                                , hubs.name ,null) as hub_name
                     , if(datediff(route_date, 
                            from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                                , drivers.hub_id ,null) as hub_id
                     , 'MYR' as currency
                     , '' as cycle_end_ppr_scheme
                     , special_driver_type_cte.created_month
                     , cast(max(free_parcel_threshold) as int) as target_parcel_count
                     , cast(max(parcels_on_route) as int) as planned_parcel_count
                     , cast(coalesce(max(parcels_on_route),0)
                         - coalesce(sum(parcels_delivered),0) - coalesce(max(failed_parcels),0) as int) 
                    as pending_parcel_count
                     , cast(sum(parcels_delivered) as int) as delivered_parcel_count
                     , cast(max(failed_parcels) as int) as failed_parcel_count
                     , cast(sum(payable_lm_parcels) as int) as daily_payable_lm_parcels
                     , cast(sum(delivery_bonus_per_weight_tier) as double) as delivery_daily_bonus
                     , cast(sum(delivery_bonus_per_weight_tier) as double) as delivery_daily_bonus_payroll
                     , cast(sum(null) as double) as waypoint_incentives
                     , cast(coalesce(sum(delivery_bonus_per_weight_tier),0) 
                         + coalesce(sum(merchant_pickup_bonus),0) 
                         + coalesce(sum(customer_pickup_bonus_per_weight_tier),0) as double) as daily_bonus
                     , cast(coalesce(sum(delivery_bonus_per_weight_tier),0) 
                         + coalesce(sum(merchant_pickup_bonus),0) 
                         + coalesce(sum(customer_pickup_bonus_per_weight_tier),0) as double) as daily_bonus_payroll           

                    {%- for category, weight_tier in category_params[driver_type_in_list].items() %}
                     , max('{{ weight_tier }}') as {{ category }}_name
                     , cast(sum(payable_lm_parcels) filter(where weight_tier = '{{ weight_tier }}') as int)
                     as {{ category }}_parcels_delivered
                     , cast(sum(delivery_bonus_per_weight_tier) filter(where weight_tier = '{{ weight_tier }}')
                        as double)
                     as {{ category }}_daily_bonus
                    {%- endfor %}

                        -- Add breakdown for Merged waypoint deliveries and normal deliveries
                        -- This is null for hybrid for now. Actual values to be added in a separate ticket
                   {%- for category, weight_tier in category_params[driver_type_in_list].items() %}
                    , cast(sum(merged_parcels_delivered) 
                        filter(where weight_tier = '{{ weight_tier }}') as int) 
                    as {{ category }}_merged_parcels_delivered
                    , cast(sum(merged_delivery_bonus_per_weight_tier) 
                        filter(where weight_tier = '{{ weight_tier }}') as double) 
                    as {{ category }}_merged_deliveries_daily_bonus
                    , cast(sum(non_merged_parcels_delivered) 
                        filter(where weight_tier = '{{ weight_tier }}') as int) 
                    as {{ category }}_non_merged_parcels_delivered
                    , cast(sum(non_merged_delivery_bonus_per_weight_tier) 
                        filter(where weight_tier = '{{ weight_tier }}') as double) 
                    as {{ category }}_non_merged_deliveries_daily_bonus
                   {%- endfor %}

                    , cast(sum(return_parcels_picked_up) as int) as pickedup_customer_count
                    , cast(sum(customer_pickup_bonus_per_weight_tier) as double) as pickedup_customer_amount
                    , cast(sum(customer_pickup_bonus_per_weight_tier) as double) as pickedup_customer_amount_payroll
                    , cast(sum(total_reservation) as int) as pickedup_merchant_count
                    , cast(sum(merchant_pickup_bonus) as double) as pickedup_merchant_amount
                    , cast(coalesce(sum(merchant_pickup_bonus),0)
                        + coalesce(sum(customer_pickup_bonus_per_weight_tier),0) as double) as daily_pickup_bonus
                    , round(cast(sum(parcels_delivered) / max(parcels_on_route) as double),4) as success_rate
                    , cast(coalesce(sum(parcels_delivered) filter(where weight_tier != '>8kg'),0) 
                        + coalesce(sum(parcels_delivered*2) filter(where weight_tier = '>8kg'),0) 
                    as int) as daily_parcels_delivered_for_balloon_bonus
                from special_driver_type_cte
                left join drivers
                    on special_driver_type_cte.driver_id = drivers.id
                    and drivers.system_id = '{{ system_id }}'
                left join hubs
                    on hubs.hub_id = drivers.hub_id
                    and hubs.system_id = '{{ system_id }}'
                where 
                     driver_type_modified = '{{ driver_type_in_list }}'
                group by {{ range(1, 12) | join(',') }}
                {% if not loop.last %}union all{% endif %}
                {%- endfor %}
                )

                select 
                    *
                from merge_all
                """,
                jinja_arguments={
                    "driver_types_list": [
                        "HYBRID DRIVER - HD - VAN",
                        "HYBRID RIDER - HR - BIKE",
                        "HYBRID DRIVER - HD - VAN(OLD)",
                        "HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)",
                        "SAMEDAY STAFF RIDER - SSR - BIKE",
                        "SAMEDAY STAFF DRIVER - SSD - VAN"],
                    "category_params": {
                        "HYBRID DRIVER - HD - VAN": {
                            "category_1": "0-8kg",
                            "category_2": ">8kg",
                            "category_3": "",
                            "category_4": "",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                        "HYBRID RIDER - HR - BIKE": {
                            "category_1": "0-2kg",
                            "category_2": "2-8kg",
                            "category_3": ">8kg",
                            "category_4": "",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                        "HYBRID DRIVER - HD - VAN(OLD)": {
                            "category_1": "0-8kg",
                            "category_2": ">8kg",
                            "category_3": "",
                            "category_4": "",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                        "HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)": {
                            "category_1": "0-2kg",
                            "category_2": "2-8kg",
                            "category_3": "8-16kg",
                            "category_4": ">16kg",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                        "SAMEDAY STAFF RIDER - SSR - BIKE": {
                            "category_1": "0-2kg",
                            "category_2": "2-8kg",
                            "category_3": ">8kg",
                            "category_4": "",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                        "SAMEDAY STAFF DRIVER - SSD - VAN": {
                            "category_1": "0-2kg",
                            "category_2": "2-8kg",
                            "category_3": ">8kg",
                            "category_4": "",
                            "category_5": "",
                            "category_6": "",
                            "category_7": "",
                            "category_8": "",
                            "category_9": "",
                            "category_10": "",
                            "category_11": "",
                            "category_12": "",
                            "category_13": "",
                        },
                    },
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                    , "system_id": system_id,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template=""" 
                with add_balloon_bonus_cte as (
                    select 
                        *
                        , cast(sum(daily_parcels_delivered_for_balloon_bonus) over(partition by driver_id, route_month 
                            order by route_date asc) as int) 
                         as cumulative_parcels_delivered_for_balloon_bonus
                    from combined_data
                ),

                /* This CTE is to create a table to obtain the targets for balloon bonus */
                balloon_targets_table as (

                    select
                        courier_type
                        , start_date
                        , end_date
                    {%- for tier, amount in ballon_bonus_params.items() %}
                        , cast({{ amount }} as double) as {{ tier }}_balloon_bonus_amount
                        , cast(max(parcels_delivered_start) filter(where balloon_bonus_amount = {{ amount }}) as int) 
                         as {{ tier }}_balloon_targets_start
                        , cast(max(parcels_delivered_end) filter(where balloon_bonus_amount = {{ amount }}) as int)
                         as {{ tier }}_balloon_targets_end
                    {%- endfor %}
                    from balloon_targets_cte
                    group by {{ range(1, 4) | join(',') }}
                ),

                balloon_amount_cte as (
                select 
                    add_balloon_bonus_cte.*
                    , cast(coalesce(balloon_targets_cte.balloon_bonus_amount, 0) as double) as balloon_bonus_amount
                {%- for i in range(5) if i > 0 %}
                    , tier_{{ i }}_balloon_targets_start
                    , tier_{{ i }}_balloon_targets_end
                    , tier_{{ i }}_balloon_bonus_amount
                {%- endfor %}
                from add_balloon_bonus_cte
                left join balloon_targets_cte
                    on add_balloon_bonus_cte.driver_type = balloon_targets_cte.courier_type
                    and date(add_balloon_bonus_cte.route_date) >= date(balloon_targets_cte.start_date)
                    and date(add_balloon_bonus_cte.route_date) <= date(balloon_targets_cte.end_date)
                    and add_balloon_bonus_cte.cumulative_parcels_delivered_for_balloon_bonus 
                        >= balloon_targets_cte.parcels_delivered_start
                    and add_balloon_bonus_cte.cumulative_parcels_delivered_for_balloon_bonus 
                        <= coalesce(balloon_targets_cte.parcels_delivered_end, 999999)
                    and add_balloon_bonus_cte.driver_id not in """ + special_hybrid_driver_id + """
                left join balloon_targets_table
                    on balloon_targets_table.courier_type = add_balloon_bonus_cte.driver_type
                    and date(add_balloon_bonus_cte.route_date) >= date(balloon_targets_table.start_date)
                    and date(add_balloon_bonus_cte.route_date) <= date(balloon_targets_table.end_date)
                    and add_balloon_bonus_cte.driver_id not in """ + special_hybrid_driver_id + """

                ),
                -- Ensure that balloon incentives appear only when the courier met the targets during the route day
                balloon_amount_change_cte as (
                select 
                    *
                    , cast(if(
                        lag(balloon_bonus_amount) over(
                            partition by driver_id, route_month order by route_date asc) != balloon_bonus_amount
                            , balloon_bonus_amount, 0) as double) as daily_balloon_bonus_amount
                from balloon_amount_cte
                )

                select * from  balloon_amount_change_cte
                """,
                jinja_arguments={
                    'ballon_bonus_params': {
                        'tier_1': 200,
                        'tier_2': 400,
                        'tier_3': 600,
                        'tier_4': 800,
                    }
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()