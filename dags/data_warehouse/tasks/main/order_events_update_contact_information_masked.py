import sys

from pyspark.sql import SparkSession
from datetime import timedelta

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_UPDATE_CONTACT_INFORMATION_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_UPDATE_CONTACT_INFORMATION_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                select
                    cast(order_id as long) as order_id
                    , type
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) as event_timestamp
                    , get_json_object(data,'$.to_name.old_value') as original_consignee_name
                    , get_json_object(data,'$.to_name.new_value') as new_consignee_name
                    , get_json_object(data,'$.to_email.old_value') as original_consignee_email
                    , get_json_object(data,'$.to_email.new_value') as new_consignee_email
                    , get_json_object(data,'$.to_contact.old_value') as original_consignee_contact
                    , get_json_object(data,'$.to_contact.new_value') as new_consignee_contact
                    , date_format(created_at, 'yyyy-MM') AS created_month
                    , system_id
                from order_events 
                -- filter for event = update contact information
                where type = 12

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    order_id
                    , type
                    , event_timestamp
                    , original_consignee_name
                    , new_consignee_name
                    , original_consignee_email
                    , new_consignee_email
                    , split(lower(original_consignee_email),'@')[1] as original_consignee_email_domain
                    , split(lower(new_consignee_email),'@')[1] as new_consignee_email_domain
                    , original_consignee_contact
                    , new_consignee_contact
                    , created_month
                    , system_id
                from base

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_EVENTS_UPDATE_CONTACT_INFORMATION,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()