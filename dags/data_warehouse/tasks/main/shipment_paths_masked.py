import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.SHIPMENT_PATHS_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.SHIPMENT_PATHS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATIONS,
                view_name="hub_relations",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_TRIPS,
                view_name="shipment_trips",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_EVENTS,
                view_name="movement_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIPS,
                view_name="movement_trips",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIP_EVENTS,
                view_name="movement_trip_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="assigned_path",
                jinja_template="""
                with
                    base (

                        select
                            shipment_id
                            , max_by(
                                get_json_object(ext_data, '$.path_cache.full_path_hub_ids'), shipment_event_id
                            ) as id_path_raw
                            , max_by(
                                get_json_object(ext_data, '$.path_cache.full_path'), shipment_event_id
                            ) as string_path_raw
                        from movement_events
                        where
                            event = 'SLA_CALCULATION'
                            and status = 'SUCCESS'
                        group by 1

                    )
                    , final as (

                        select
                            shipment_id
                            , from_json(id_path_raw, 'array<string>') as id_path
                            , from_json(string_path_raw, 'array<string>') as string_path
                        from base

                    )
                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="traveled_path",
                jinja_template="""
                with
                    scans as (

                        select
                            shipment_id
                            , lower(hub_system_id) as system_id
                            , hub_id
                            , created_at
                        from shipment_events
                        where
                            event in ('SHIPMENT_VAN_INBOUND','SHIPMENT_HUB_INBOUND')
                            and deleted_at is null

                    )
                    , unscanned as (

                        select
                            trip_unscanned_shipments.shipment_id
                            , hub_relations.destination_hub_system_id as system_id
                            , case
                                when trip_unscanned_shipments.scan_type = 'SHIPMENT_VAN_INBOUND'
                                    then hub_relations.origin_hub_id
                                when trip_unscanned_shipments.scan_type = 'SHIPMENT_HUB_INBOUND'
                                    then hub_relations.destination_hub_id
                                end as hub_id
                            , trip_unscanned_shipments.created_at
                        from trip_unscanned_shipments
                            left join movement_trips
                                on trip_unscanned_shipments.trip_id = movement_trips.id
                            left join hub_relations
                                on movement_trips.hub_relation_id = hub_relations.id
                        where
                            trip_unscanned_shipments.type = 'STAYOVER'
                            and trip_unscanned_shipments.deleted_at is null

                    )
                    , union as (

                        select * from scans
                        UNION ALL
                        select * from unscanned

                    )
                    , hub_change as (

                        select
                            *
                            , lag(hub_id) over (partition by shipment_id order by created_at) as prev_hub_id
                        from union

                    )
                    , hub_change_filtered as (

                        select
                            hub_change.shipment_id
                            , hub_change.created_at
                            , hub_change.hub_id
                            , concat(hubs_enriched.name, ' (', hub_change.system_id, ')') as hub_name_system_id
                        from hub_change
                            left join hubs_enriched
                                on hub_change.system_id = hubs_enriched.system_id
                                and hub_change.hub_id = hubs_enriched.id
                        where
                            hub_change.prev_hub_id is null or hub_change.prev_hub_id != hub_change.hub_id
                        order by 1,2

                    )
                    , final as (

                        select
                            shipment_id
                            , collect_list(hub_id) as id_path
                            , collect_list(hub_name_system_id) as string_path
                        from hub_change_filtered
                        group by 1

                    )
                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    shipments.id as shipment_id
                    , assigned_path.id_path as assigned_id_path
                    , assigned_path.string_path as assigned_string_path
                    , traveled_path.id_path as traveled_id_path
                    , traveled_path.string_path as traveled_string_path
                    , if(
                        lower(string(assigned_path.id_path)) = lower(string(traveled_path.id_path)), 1, 0
                    ) as matching_id_path_flag
                    , if(
                        lower(string(assigned_path.string_path)) = lower(string(traveled_path.string_path)), 1, 0
                    ) as matching_string_path_flag
                    , date_format(
                        from_utc_timestamp(shipments.created_at, {{ get_local_timezone }}), 'yyyy-MM'
                    ) as created_month
                    , lower(shipments.orig_hub_country) as system_id
                from shipments
                left join assigned_path on
                    shipments.id = assigned_path.shipment_id
                left join traveled_path on
                    shipments.id = traveled_path.shipment_id
                where
                    status = 'Completed'
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("shipments.orig_hub_country")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENT_PATHS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
