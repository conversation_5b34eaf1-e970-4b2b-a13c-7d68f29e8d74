import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_EXPORT_JOB_ITEMS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_EXPORT_JOB_ITEMS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).EXPORT_JOB_ITEMS,
                view_name="export_job_items",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).EXPORT_JOBS,
                view_name="export_jobs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).TRANSITS_TAB,
                view_name="transits_tab",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).TRANSITS_BAGS_TAB,
                view_name="transits_bags_tab",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).WAREHOUSES_TAB,
                view_name="warehouses_tab",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="selected_export_job_items",
                jinja_template="""
                with base as (
                    select
                        tracking_id
                        , max_by(id, created_at) filter(where status = 2) as id_main
                        , max_by(id, created_at) filter(where status = 1) as id_backup1
                        , max_by(id, created_at) filter(where status not in (1,2)) as id_backup2
                    from export_job_items
                    where
                        type = 1
                    group by 1
                )

                select
                    base.tracking_id
                    , coalesce(base.id_main, base.id_backup1, base.id_backup2) export_job_item_id
                    , date_format(parcels.created_at, 'yyyy-MM') as created_month
                from base
                inner join parcels
                    on base.tracking_id = parcels.tracking_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    cast(parcels_eji.object_id as bigint) as parcel_id
                    , base.tracking_id
                    , parcels_eji.export_job_id
                    , case workflow_id
                    {%- for id, name in workflow_id_to_name.items() %}
                        when {{ id }} then '{{ name }}'
                    {%- endfor %}
                    end as workflow
                    , case export_jobs.status
                    {%- for status_id, name in export_job_status_to_name.items() %}
                        when {{ status_id }} then '{{ name }}'
                    {%- endfor %}
                    end as export_job_status
                    , export_jobs.name as export_job_name
                    , export_jobs.product_code as export_job_product_code
                    , export_jobs.job_date as export_job_date
                    , case
                        when export_jobs.origin_country in ('TH', 'VN', 'ID')
                        then export_jobs.created_at + interval '7' hour
                        else export_jobs.created_at + interval '8' hour
                    end as export_job_creation_datetime
                    , case
                        when export_jobs.origin_country in ('TH', 'VN', 'ID')
                        then coalesce(parcels_eji.processed_at, bags_eji.received_at) + interval '7' hour
                        else coalesce(parcels_eji.processed_at, bags_eji.received_at) + interval '8' hour
                    end as export_job_processing_datetime
                    , parcels_eji.status as status
                    , case parcels_eji.status
                    {%- for status_id, name in export_job_item_status_to_name.items() %}
                        when {{ status_id }} then '{{ name }}'
                    {%- endfor %}
                    end as export_job_item_status
                    , parcels_eji.granular_status_code
                    , cast(parcels_eji.parent_id as bigint) as parent_id
                    , bags_eji.tracking_id as bag_tracking_id
                    , export_jobs.origin_country
                    , export_jobs.destination_country
                    , cast(
                        get_json_object(parcels_eji.object_metadata, '$.weight') as double
                    ) as export_parcel_weight
                    , cast(
                        get_json_object(parcels_eji.object_metadata, '$.manifest_weight') as double
                    ) as export_parcel_manifest_weight
                    , cast(get_json_object(bags_eji.object_metadata, '$.weight') as double) as export_bag_weight
                    {%- for dimension in ('length', 'width', 'height') %}
                    , cast(
                        get_json_object(bags_eji.object_metadata, '$.dimension.{{ dimension }}') as double
                    ) as export_bag_{{ dimension }}
                    {%- endfor %}
                    , cast(parcels_export_job_warehouses.id as bigint) as export_processing_hub_id
                    , parcels_export_job_warehouses.name as export_processing_hub_name
                    , cast(transit_warehouses_tab.id as bigint) as origin_interhub_id
                    , transit_warehouses_tab.name as origin_interhub_name
                    , 'gl' as system_id
                    , base.created_month
                from selected_export_job_items base
                left join export_job_items as parcels_eji
                    on base.export_job_item_id = parcels_eji.id
                left join export_jobs
                    on parcels_eji.export_job_id = export_jobs.id
                left join warehouses_tab as parcels_export_job_warehouses
                    on export_jobs.warehouse_id = parcels_export_job_warehouses.id
                left join export_job_items as bags_eji
                    on parcels_eji.parent_id = bags_eji.id
                left join transits_bags_tab
                    on bags_eji.export_job_id = transits_bags_tab.export_job_id
                    and upper(bags_eji.tracking_id) = upper(transits_bags_tab.tracking_id)
                left join transits_tab
                    on transits_bags_tab.transit_id = transits_tab.id
                left join warehouses_tab as transit_warehouses_tab
                    on transits_tab.receive_hub_id = transit_warehouses_tab.id
                """,
                jinja_arguments={
                    "workflow_id_to_name":{
                        1:"SM",
                        2:"VIP",
                        3:"VIP/Bag",
                        4:"MMCC",
                    },
                    "export_job_item_status_to_name":{
                        1:"To be Packed",
                        2:"Packed",
                        3:"Excluded",
                    },
                    "export_job_status_to_name":{
                        1:"Packing",
                        2:"Loading",
                        3:"Closed",
                    },
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_EXPORT_JOB_ITEMS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
