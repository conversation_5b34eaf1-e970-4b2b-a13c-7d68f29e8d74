import sys

from pyspark.sql import SparkSession

from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from pyspark.sql.functions import current_timestamp, date_format, monotonically_increasing_id

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from pyspark.sql import functions as F
from pyspark.sql.functions import explode, col
from pyspark.sql.types import StructField, ArrayType, StringType, StructType,BooleanType


airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SHIPPER_REF_PLATFORM_INFO_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SHIPPER_REF_PLATFORM_INFO_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    platform_schema = ArrayType(
        StructType([
            StructField("platformInfo", StringType(), True)
        ])
    )

    user_schema = ArrayType(
        StructType([
            StructField("platformOrderNumber", StringType(), True),
            StructField("platformTags", StringType(), True),
            StructField("platformPromiseMinTime", StringType(), True),
            StructField("platformPromiseMaxTime", StringType(), True),
            StructField("platformName", StringType(), True),
            StructField("platformOrderId", StringType(), True),
            StructField("platformOrderCreationTime", StringType(), True),
            StructField("platformCustomerCreationTime", StringType(), True),
            StructField("platformCreationTime", StringType(), True)
        ])
    )

    # we are loading input cods table for the sake of input_config use. We will remove it in future once
    # input_config is not required
    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    end_month = measurement_datetime.strftime('%Y-%m')
    dte = dt.strptime(end_month, '%Y-%m').date()
    re = dte + relativedelta(months=-6)
    start_month = re.strftime('%Y-%m')

    orders = spark.read.format("delta").load(delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS)\
        .filter(F.col("created_month").between(start_month,end_month))\
        .select("id", "shipper_id", "tracking_id", "shipper_ref_metadata", "created_month", "created_at", "global_shipper_id")

    df_new = orders.withColumn("platformInfo", F.from_json("shipper_ref_metadata", platform_schema)) \
        .select("platformInfo", "id", "shipper_id", "tracking_id", "created_month", "created_at", "global_shipper_id")

    platform_df = df_new.select(explode("platformInfo").alias("platform"), "id", "shipper_id", "tracking_id",
                                "created_month", "created_at", "global_shipper_id") \
        .select("platform.platformInfo", "id", "shipper_id", "tracking_id", "created_month", "created_at", "global_shipper_id")

    platform_new_df = platform_df.withColumn("platInfo", F.from_json("platformInfo", user_schema)) \
        .selectExpr("inline(platInfo)", "id", "shipper_id", "tracking_id", "created_month", "created_at", "global_shipper_id")

    final_df = platform_new_df.withColumn("timestamp", date_format(current_timestamp(), "yyyyMMddHHmmss")) \
        .withColumn("random_number", monotonically_increasing_id()) \
        .select(col("platformOrderNumber").alias("platform_order_number"),
                                      col("id").alias("order_id"),
                                      "shipper_id", "tracking_id",
                                      col("platformTags").alias("platform_tags"),
                                      col("platformPromiseMinTime").alias("platform_promise_min_time"),
                                      col("platformPromiseMaxTime").alias("platform_promise_max_time"),
                                      col("platformName").alias("platform_name"),
                                      col("platformOrderId").alias("platform_order_Id"),
                                      col("platformOrderCreationTime").alias("platform_order_creation_time"),
                                      col("platformCustomerCreationTime").alias("platform_customer_creation_time"),
                                      col("platformCreationTime").alias("platform_creation_time"),
                "created_month", "created_at", "global_shipper_id", "timestamp","random_number" )

    final_df.createOrReplaceTempView("output")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                   SELECT 
                    case when '{{system_id}}' = 'id' then CONCAT(timestamp,random_number,order_id,1)
                    when '{{system_id}}' = 'mm' then CONCAT(timestamp,random_number,order_id,2)
                    when '{{system_id}}' = 'my' then CONCAT(timestamp,random_number,order_id,3)
                    when '{{system_id}}' = 'ph' then CONCAT(timestamp,random_number,order_id,4)
                    when '{{system_id}}' = 'sg' then CONCAT(timestamp,random_number,order_id,5)
                    when '{{system_id}}' = 'th' then CONCAT(timestamp,random_number,order_id,6)
                    when '{{system_id}}' = 'vn' then CONCAT(timestamp,random_number,order_id,7)
                    end as ID
                    , platform_order_number
                    , order_id
                    , cast(global_shipper_id as int) as shipper_id
                    , tracking_id
                    , platform_tags
                    , platform_promise_min_time
                    , platform_promise_max_time
                    , platform_name
                    , platform_order_Id
                    , platform_order_creation_time
                    , platform_customer_creation_time
                    , platform_creation_time
                    , created_month
                    , created_at
                    , '{{system_id}}' as system_id
                    FROM output
                    """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_PLATFORM_INFO,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config( spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )


    run(spark, task_config)
    spark.stop()