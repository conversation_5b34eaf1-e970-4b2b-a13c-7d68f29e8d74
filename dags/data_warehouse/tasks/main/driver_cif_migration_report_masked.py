import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.DRIVER_CIF_MIGRATION_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.DRIVER_CIF_MIGRATION_REPORT_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS,
                view_name="drivers",
            ),
            base.InputTable(
                path=delta_tables.Gsuite(input_env, is_masked).USERS,
                view_name="users",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id as driver_id
                    , cif_email
                    , system_id
                    , updated_at
                from drivers
                where
                    cif_email is not null
                    and employment_end_date is null
                    and deleted_at is null
                    and updated_at >= date('{{ last_measurement_datetime }}') - interval 1 month
                    and updated_at < date('{{ measurement_datetime }}')
                    and id not in (
                      select driver_id from users
                      where is_enrolled_in_2_sv is True
                    )
                """,
                jinja_arguments={
                    "last_measurement_datetime": last_measurement_datetime,
                    "measurement_datetime": measurement_datetime,
                },
            ),
        ),
    )
    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    output_config = base.OutputConfig(
        base_path=parquet_tables_masked.DataWarehouse(env).DRIVER_CIF_MIGRATION_REPORT + measurement_datetime_partition,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
