import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ID_FM_PAYROLL_DAILY_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ID_FM_PAYROLL_DAILY_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing",),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 10)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_FM_PAYROLL_DENSITY_UMK,
                view_name="density_umk",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_FM_PAYROLL_RATES,
                view_name="rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_FM_PAYROLL_VOLUME_BONUSES,
                view_name="volume_bonuses",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="route",
                jinja_template="""
                with base as (
                
                    select 
                        system_id
                        , route_date
                        , date_format(route_date,'yyyy-MM') as route_month
                        , route_id
                        , courier_id
                        , courier_name
                        , courier_type
                        , return_parcels_picked_up
                    from fleet_performance_base_data
                    where courier_type in ('Freelance Retail Pickup Rider', 'Retail Pickup Rider')

                ),
                final as (
                
                    select
                        system_id
                        , route_date
                        , route_month
                        , courier_id
                        , courier_name
                        , courier_type
                        , sum(return_parcels_picked_up) as rpu_parcels
                    from base
                    group by {{ range(1, 7) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="reservations",
                jinja_template="""
                with base as (
                
                    select 
                        date(attempt_datetime) as route_date
                        , route_id
                        , waypoint_id
                        , reservation_id
                        , route_driver_id as courier_id
                        , least(coalesce(picked_up_orders,0),20) as capped_pickups
                    from reservations_enriched
                    where route_driver_type in ('Freelance Retail Pickup Rider', 'Retail Pickup Rider')

                ),
                final as (
                
                    select
                        route_date
                        , courier_id
                        , count(distinct waypoint_id) as distinct_waypoints
                        , sum(capped_pickups) as capped_pickups
                    from base
                    group by {{ range(1, 3) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                
                with base as (
                    select
                        route.system_id
                        , route.route_month as created_month
                        , route.route_date
                        , route.route_month
                        , route.courier_id
                        , route.courier_name
                        , route.courier_type
                        , drivers_enriched.hub_region
                        , drivers_enriched.hub_id
                        , drivers_enriched.hub_name
                        , density_umk.density_category
                        , density_umk.umk_category
                        , coalesce(reservations.distinct_waypoints, 0) as distinct_waypoints
                        , coalesce(reservations.capped_pickups, 0) as capped_pickups
                        , coalesce(route.rpu_parcels, 0) as rpu_parcels
                    from route
                    left join reservations
                        on route.route_date = reservations.route_date
                        and route.courier_id = reservations.courier_id
                    left join drivers_enriched
                        on route.courier_id = drivers_enriched.id
                    left join density_umk
                        on route.route_month = density_umk.effective_month
                        and drivers_enriched.hub_id = density_umk.hub_id
                
                ),
                pay as (
                
                    select
                        base.*
                        , cast(distinct_waypoints * rates.waypoint_rate as double) as waypoint_payment
                        , cast(capped_pickups * rates.parcel_rate as double) as parcel_payment
                        , cast(rpu_parcels * rates.rpu_rate as double) as rpu_payment
                        , cast(volume_bonuses.bonus as double) as ocp_bonus
                    from base
                    left join rates
                        on base.route_month = rates.effective_month
                    left join volume_bonuses
                        on base.route_month = volume_bonuses.effective_month
                        and base.density_category = volume_bonuses.density_category
                        and base.umk_category = volume_bonuses.umk_category
                        and base.distinct_waypoints >= volume_bonuses.range_start
                        and base.distinct_waypoints <= volume_bonuses.range_end
                    

                ),
                final as (
                
                    select
                        pay.*
                        , waypoint_payment + parcel_payment + rpu_payment + ocp_bonus as sum_total
                    from pay
                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_FM_PAYROLL_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
