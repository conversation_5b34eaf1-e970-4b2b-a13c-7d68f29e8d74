import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.WAREHOUSE_SPEED_REPORT_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.WAREHOUSE_SPEED_REPORT_MASKED,
    depends_on=(
        data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
        data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.WAREHOUSE_SPEED_REPORT_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAREHOUSE_SPEED_REPORT_EVENTS,
                view_name="warehouse_speed_report_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.GDrive(input_env).HOLIDAYS_SORT,
                view_name="holidays_sort_gdrive",
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).EVENTS,
                view_name="events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="first_scans",
                jinja_template="""
                with inbound_scans_filtered as (
                    select
                        id
                        , order_id
                        , hub_id
                        , 'INBOUND SCANS' as scan_type
                        , from_utc_timestamp(created_at, '{{ get_local_timezone }}') as scan_time
                    from inbound_scans
                    where type = 2
                )
                , warehouse_sweeps_filtered as (
                    select
                        id
                        , order_id
                        , hub_id
                        , 'WAREHOUSE SWEEPS' as scan_type
                        , from_utc_timestamp(created_at, '{{ get_local_timezone }}') as scan_time
                    from warehouse_sweeps
                )
                , all_scans as (
                    select
                        *
                    from inbound_scans_filtered
                    union all
                    select
                        *
                    from warehouse_sweeps_filtered
                )
                , scans_interim as (
                    select
                        all_scans.order_id
                        , all_scans.scan_time
                        , all_scans.scan_type
                        , coalesce(hubs_enriched.parent_hub_id, all_scans.hub_id) as hub_id
                    from all_scans
                    left join hubs_enriched on
                        all_scans.hub_id = hubs_enriched.id
                )
                , scans_interim_2 as (
                    select
                        order_id
                        , scan_time
                        , scan_type
                        , hub_id
                        , if(
                            lag(hub_id) over (partition by order_id order by scan_time asc) = hub_id
                            , 0
                            , 1
                        ) as hub_change
                    from scans_interim
                )
                , scans_interim_3 as (
                    select
                        order_id
                        , scan_time
                        , scan_type
                        , hub_id
                        , row_number() over (partition by order_id order by scan_time asc) as hub_seq
                    from scans_interim_2
                    where hub_change = 1
                )
                , pre_final as (
                    select
                        scans_interim_3.order_id
                        , scans_interim_3.scan_time
                        , scans_interim_3.scan_type
                        , lag(scans_interim_3.scan_time) over (
                            partition by scans_interim_3.order_id order by scans_interim_3.scan_time asc
                        ) as prev_scan_time
                        , lead(scans_interim_3.scan_time) over (
                            partition by scans_interim_3.order_id order by scans_interim_3.scan_time asc
                        ) as next_scan_time
                        , scans_interim_3.hub_id
                        , hubs_enriched.name hub_name
                        , scans_interim_3.hub_seq
                        , hubs_enriched.sort_hub_flag
                    from scans_interim_3
                    left join hubs_enriched on
                        scans_interim_3.hub_id = hubs_enriched.id
                )
                , final as (
                    select
                        *
                        , coalesce(prev_scan_time,  date('2000-01-01')) as pref_ref
                    from pre_final
                )
                select * from final
                    """,
                jinja_arguments={"get_local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="crossborder_holm",
                jinja_template="""
                with base as (
                
                    select
                        parcels.tracking_id
                        , lower(parcels.to_country_code) as system_id
                        , events.event_time
                    from parcels
                    left join events 
                        on parcels.id = events.parcel_id
                    where events.internal_status_id = 24

                )
                , final as (
                
                    select
                        tracking_id
                        , system_id
                        , from_utc_timestamp(event_time, '{{ get_local_timezone }}') as scan_time
                    from base

                )
                
                select * from final
                where scan_time >= date('2023-07-01')

                """,
                jinja_arguments={"get_local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="crossborder_holm_last",
                jinja_template="""

                select 
                    tracking_id
                    , max(scan_time) as last_holm
                from crossborder_holm
                group by 1

                """,
            ),
            base.TransformView(
                view_name="poh_scan",
                jinja_template="""
                with final as (
                
                    select
                        poh_order_metrics.tracking_id
                        , poh_metrics.hub_id
                        , poh_metrics.handover_time as scan_time
                    from poh_order_metrics
                    left join poh_metrics
                        on poh_order_metrics.hub_handover_id = poh_metrics.id
                    where poh_order_metrics.hub_handover_id is not null

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="poh_scan_first",
                jinja_template="""

                select 
                    tracking_id
                    , min(scan_time) as first_poh
                from poh_scan
                group by 1

                """,
            ),
            base.TransformView(
                view_name="first_pickup_success_event",
                jinja_template="""
                select
                    order_id
                    , min(created_at) as first_pickup
                from order_events_pickup_success
                group by 1
                """,
            ),
            base.TransformView(
                view_name="shipment_into_hub_times",
                jinja_template="""
                select
                    shipment_orders_enriched.order_id
                    , coalesce(hubs_enriched.parent_hub_id, shipment_orders_enriched.dest_hub_id) as hub_id
                    , shipment_orders_enriched.dest_shipment_hub_inbound_datetime as scan_time
                from shipment_orders_enriched
                left join hubs_enriched on
                    shipment_orders_enriched.dest_hub_id = hubs_enriched.id
                """,
            ),
            base.TransformView(
                view_name="combined_inbound",
                jinja_template="""
                with base as (
                
                    select
                        first_scans.order_id
                        , first_scans.scan_time as inbound_end_clock_datetime
                        , first_scans.scan_type as inbound_end_clock_type
                        , first_scans.prev_scan_time
                        , first_scans.next_scan_time
                        , first_scans.hub_id
                        , first_scans.hub_name
                        , first_scans.hub_seq
                        , crossborder_holm_last.last_holm as crossborder_handover_proxy
                        , poh_scan_first.first_poh as poh_proxy
                        , case 
                            when first_scans.scan_time > order_milestones.rts_trigger_datetime then order_milestones.rts_trigger_datetime
                            else null
                            end as rts_trigger_datetime
                        , first_pickup_success_event.first_pickup as pickup_datetime
                        , min(shipment_into_hub_times.scan_time) as shipment_inbound_time
                        , max(crossborder_holm.scan_time) as crossborder_holm_time
                        , min(poh_scan.scan_time) as poh_time
                    from first_scans
                    left join order_milestones
                        on first_scans.order_id = order_milestones.order_id
                    left join shippers_enriched
                        on order_milestones.shipper_id = shippers_enriched.id
                    left join shipment_into_hub_times
                        on first_scans.order_id = shipment_into_hub_times.order_id
                        and first_scans.hub_id = shipment_into_hub_times.hub_id
                        and first_scans.pref_ref < shipment_into_hub_times.scan_time
                        and first_scans.scan_time > shipment_into_hub_times.scan_time
                    left join crossborder_holm
                        on order_milestones.tracking_id = crossborder_holm.tracking_id
                        and first_scans.pref_ref < crossborder_holm.scan_time
                        and first_scans.scan_time > crossborder_holm.scan_time
                    left join poh_scan
                        on order_milestones.tracking_id = poh_scan.tracking_id
                        and first_scans.hub_id = poh_scan.hub_id
                        and first_scans.pref_ref < poh_scan.scan_time
                        and first_scans.scan_time > poh_scan.scan_time
                    left join crossborder_holm_last
                        on order_milestones.tracking_id = crossborder_holm_last.tracking_id
                    left join poh_scan_first
                        on order_milestones.tracking_id = poh_scan_first.tracking_id
                    left join first_pickup_success_event
                        on first_scans.order_id = first_pickup_success_event.order_id
                    where first_scans.sort_hub_flag = 1
                        and shippers_enriched.sales_channel != 'Test'
                    group by {{ range(1, 13) | join(',') }}

                )
                , final as (
                
                    select
                        order_id
                        , hub_id
                        , hub_name
                        , hub_seq
                        , prev_scan_time
                        , next_scan_time
                        , coalesce(next_scan_time, (from_utc_timestamp('{{ measurement_datetime }}', '{{ get_local_timezone }}'))) as next_ref
                        , coalesce(shipment_inbound_time
                                    , crossborder_holm_time
                                    , poh_time
                                    , crossborder_handover_proxy
                                    , poh_proxy
                                    , rts_trigger_datetime
                                    , pickup_datetime
                        ) as inbound_start_clock_datetime
                        , case
                            when shipment_inbound_time is not null then 'shipment_inbound'
                            when crossborder_holm_time is not null then 'crossborder_handover'
                            when poh_time is not null then 'proof_of_handover'
                            when crossborder_handover_proxy is not null then 'crossborder_handover_proxy'
                            when poh_proxy is not null then 'proof_of_handover_proxy'
                            when rts_trigger_datetime is not null then 'rts_trigger_proxy'
                            when pickup_datetime is not null then 'pickup_proxy'
                            else null
                        end as inbound_start_clock_type
                        , inbound_end_clock_datetime
                        , inbound_end_clock_type
                    from base

                )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                    "measurement_datetime": measurement_datetime,
                }
            ),
            base.TransformView(
                view_name="shipment_close_cte",
                jinja_template="""
                select
                    shipment_orders_enriched.order_id
                    , coalesce(hubs_enriched.parent_hub_id, shipment_orders_enriched.orig_hub_id) as hub_id
                    , shipment_orders_enriched.orig_shipment_close_datetime
                    , shipment_orders_enriched.shipment_id
                from shipment_orders_enriched
                left join hubs_enriched on
                    shipment_orders_enriched.orig_hub_id = hubs_enriched.id
                """,
            ),
            base.TransformView(
                view_name="outbound_end_clocks",
                jinja_template="""
                with outbound_start_clocks_interim as (
                    select
                        combined_inbound.order_id
                        , combined_inbound.hub_id
                        , combined_inbound.hub_name
                        , combined_inbound.hub_seq
                        , combined_inbound.prev_scan_time
                        , combined_inbound.next_scan_time
                        , combined_inbound.next_ref
                        , combined_inbound.inbound_start_clock_datetime
                        , combined_inbound.inbound_start_clock_type
                        , combined_inbound.inbound_end_clock_datetime
                        , combined_inbound.inbound_end_clock_type
                        , max_by(shipment_close_cte.shipment_id, shipment_close_cte.orig_shipment_close_datetime) as outbound_shipment_id
                        , max(shipment_close_cte.orig_shipment_close_datetime) as shipment_close_time
                        , max(route_events.created_at) as add_to_route_time
                    from combined_inbound
                    left join shipment_close_cte on
                        combined_inbound.order_id = shipment_close_cte.order_id
                        and combined_inbound.hub_id = shipment_close_cte.hub_id
                        and combined_inbound.inbound_end_clock_datetime
                            < shipment_close_cte.orig_shipment_close_datetime
                        and coalesce(combined_inbound.next_scan_time,  combined_inbound.next_ref)
                            > shipment_close_cte.orig_shipment_close_datetime
                    left join warehouse_speed_report_events as route_events on
                        combined_inbound.order_id = route_events.order_id
                        and combined_inbound.inbound_end_clock_datetime < (route_events.created_at)
                        and coalesce(combined_inbound.next_scan_time,  combined_inbound.next_ref)
                            > route_events.created_at
                        and route_events.type = 32
                    group by {{ range(1, 12) | join(',') }}
                )
                , outbound_start_clocks as (
                    select
                        order_id
                        , hub_id
                        , hub_name
                        , hub_seq
                        , prev_scan_time
                        , next_scan_time
                        , next_ref
                        , inbound_start_clock_datetime
                        , inbound_start_clock_type
                        , inbound_end_clock_datetime
                        , inbound_end_clock_type
                        , coalesce(shipment_close_time, add_to_route_time) as outbound_start_clock_datetime
                        , case
                            when shipment_close_time is not null then 'shipment_close'
                            when add_to_route_time is not null then 'add_to_route'
                            else null
                        end as outbound_start_clock_type
                        , outbound_shipment_id
                    from outbound_start_clocks_interim
                ),
                outbound_end_clocks_interim as (
                    select
                        outbound_start_clocks.order_id
                        , outbound_start_clocks.hub_id
                        , outbound_start_clocks.hub_name
                        , outbound_start_clocks.hub_seq
                        , outbound_start_clocks.prev_scan_time
                        , outbound_start_clocks.next_scan_time
                        , outbound_start_clocks.inbound_start_clock_datetime
                        , outbound_start_clocks.inbound_start_clock_type
                        , outbound_start_clocks.inbound_end_clock_datetime
                        , outbound_start_clocks.inbound_end_clock_type
                        , outbound_start_clocks.outbound_start_clock_datetime
                        , outbound_start_clocks.outbound_start_clock_type
                        , outbound_start_clocks.outbound_shipment_id
                        , max(shipment_oe.created_at) as shipment_van_time
                        , max(route_oe.created_at) as driver_inbound_time
                    from outbound_start_clocks
                    left join warehouse_speed_report_events as shipment_oe on
                        outbound_start_clocks.order_id = shipment_oe.order_id
                        and outbound_start_clocks.hub_id = shipment_oe.hub_id
                        and outbound_start_clocks.inbound_end_clock_datetime < shipment_oe.created_at
                        and outbound_start_clocks.next_ref > shipment_oe.created_at
                        and shipment_oe.type = 54
                    left join warehouse_speed_report_events as route_oe on
                        outbound_start_clocks.order_id = route_oe.order_id
                        and outbound_start_clocks.inbound_end_clock_datetime < route_oe.created_at
                        and outbound_start_clocks.next_ref > route_oe.created_at
                        and route_oe.type = 24
                    group by {{ range(1, 14) | join(',') }}
                )
                select
                    order_id
                    , hub_id
                    , hub_name
                    , hub_seq
                    , prev_scan_time
                    , next_scan_time
                    , inbound_start_clock_datetime
                    , inbound_start_clock_type
                    , inbound_end_clock_datetime
                    , inbound_end_clock_type
                    , outbound_start_clock_datetime
                    , outbound_start_clock_type
                    , outbound_shipment_id
                    , case
                        when (outbound_start_clock_type = 'shipment_close' and shipment_van_time is not null)
                            then shipment_van_time
                        when (outbound_start_clock_type = 'add_to_route' and driver_inbound_time is not null)
                            then driver_inbound_time
                        else null
                    end as outbound_end_clock_datetime
                    , case
                        when (outbound_start_clock_type = 'shipment_close' and shipment_van_time is not null)
                            then 'shipment_van_inbound'
                        when (outbound_start_clock_type = 'add_to_route' and driver_inbound_time is not null)
                            then 'driver_inbound'
                        else null
                    end as outbound_end_clock_type
                from outbound_end_clocks_interim
                """,
            ),
            base.TransformView(
                view_name="adjustment",
                jinja_template="""
                with base as (
                    select
                        order_id
                        , hub_id
                        , hub_name
                        , hub_seq
                        , prev_scan_time as prev_inbound
                        , next_scan_time as next_inbound
                        , inbound_start_clock_datetime
                        , inbound_start_clock_type
                        , inbound_end_clock_datetime
                        , inbound_end_clock_type
                        , outbound_start_clock_datetime
                        , outbound_start_clock_type
                        , outbound_shipment_id
                        , outbound_end_clock_datetime
                        , outbound_end_clock_type
                        , {{ dt('inbound_end_clock_datetime', 'inbound_start_clock_datetime') }} as raw_inbound_time
                        , {{ dt('outbound_start_clock_datetime', 'inbound_end_clock_datetime') }} as raw_processing_time
                        , {{ dt('outbound_end_clock_datetime', 'outbound_start_clock_datetime') }} as raw_outbound_time
                        , {{ dt('outbound_end_clock_datetime', 'inbound_start_clock_datetime') }} as raw_warehouse_time
                    from outbound_end_clocks
                )
                , pets_exclusions as (
                    select distinct
                        order_id
                        , creation_datetime
                        , resolution_datetime
                        , "PETS ticket" as pets_exclusion
                    from pets_tickets_enriched
                )
                , holidays_sort as ( 
                    select distinct
                        date
                    from holidays_sort_gdrive
                    where
                        country = '{{ system_id }}'
                        and region = 'national'
                        and is_deleted = 0
                )
                , adjustment_1 as (
                    select
                        base.order_id
                        , base.hub_id
                        , base.hub_name
                        , base.hub_seq
                        , base.prev_inbound
                        , base.next_inbound
                        , base.inbound_start_clock_datetime
                        , base.inbound_start_clock_type
                        , base.inbound_end_clock_datetime
                        , base.inbound_end_clock_type
                        , base.outbound_start_clock_datetime
                        , base.outbound_start_clock_type
                        , base.outbound_shipment_id
                        , base.outbound_end_clock_datetime
                        , base.outbound_end_clock_type
                        , base.raw_inbound_time
                        , base.raw_processing_time
                        , base.raw_outbound_time
                        , base.raw_warehouse_time
                        , {{
                            adjust_time_on_conditions(
                            clock_time_start = 'base.inbound_start_clock_datetime'
                            , clock_time_end = 'base.inbound_end_clock_datetime'
                            , start_holiday = 'inbound_start_holiday'
                            , subsequent_holidays = 'inbound_subsequent_holidays'
                            , base_time = 'base.raw_inbound_time')
                        }} as prelim_inbound_time
                        , {{
                            adjust_time_on_conditions(
                            clock_time_start = 'base.inbound_end_clock_datetime'
                            , clock_time_end = 'base.outbound_start_clock_datetime'
                            , start_holiday = 'processing_start_holiday'
                            , subsequent_holidays = 'processing_subsequent_holidays'
                            , base_time = 'base.raw_processing_time')
                        }} as prelim_processing_time
                        , {{
                            adjust_time_on_conditions(
                            clock_time_start = 'base.outbound_start_clock_datetime'
                            , clock_time_end = 'base.outbound_end_clock_datetime'
                            , start_holiday = 'outbound_start_holiday'
                            , subsequent_holidays = 'outbound_subsequent_holidays'
                            , base_time = 'base.raw_outbound_time')
                        }} as prelim_outbound_time
                        , {{
                            adjust_time_on_conditions(
                            clock_time_start = 'base.inbound_start_clock_datetime'
                            , clock_time_end = 'base.outbound_end_clock_datetime'
                            , start_holiday = 'warehouse_start_holiday'
                            , subsequent_holidays = 'warehouse_subsequent_holidays'
                            , base_time = 'base.raw_warehouse_time')
                        }} as prelim_warehouse_time
                    from base
                    left join holidays_sort as inbound_start_holiday on
                        date(base.inbound_start_clock_datetime) = inbound_start_holiday.date
                    left join holidays_sort as inbound_subsequent_holidays on
                        date(base.inbound_start_clock_datetime) < inbound_subsequent_holidays.date
                        and date(base.inbound_end_clock_datetime) > inbound_subsequent_holidays.date
                    left join holidays_sort as processing_start_holiday on
                        date(base.inbound_end_clock_datetime) = processing_start_holiday.date
                    left join holidays_sort as processing_subsequent_holidays on
                        date(base.inbound_end_clock_datetime) < processing_subsequent_holidays.date
                        and date(base.outbound_start_clock_datetime) > processing_subsequent_holidays.date
                    left join holidays_sort as outbound_start_holiday on
                        date(base.outbound_start_clock_datetime) = outbound_start_holiday.date
                    left join holidays_sort as outbound_subsequent_holidays on
                        date(base.outbound_start_clock_datetime) < outbound_subsequent_holidays.date
                        and date(base.outbound_end_clock_datetime) > outbound_subsequent_holidays.date
                    left join holidays_sort as warehouse_start_holiday on
                        date(base.inbound_start_clock_datetime) = warehouse_start_holiday.date
                    left join holidays_sort as warehouse_subsequent_holidays on
                        date(base.inbound_start_clock_datetime) < warehouse_subsequent_holidays.date
                        and date(base.outbound_end_clock_datetime) > warehouse_subsequent_holidays.date
                    group by {{ range(1, 20) | join(',') }}
                        , inbound_start_holiday.date
                        , processing_start_holiday.date
                        , outbound_start_holiday.date
                        , warehouse_start_holiday.date
                )
                select
                    adjustment_1.order_id
                    , adjustment_1.hub_id
                    , adjustment_1.hub_name
                    , adjustment_1.hub_seq
                    , adjustment_1.prev_inbound
                    , adjustment_1.next_inbound
                    , adjustment_1.inbound_start_clock_datetime
                    , adjustment_1.inbound_start_clock_type
                    , adjustment_1.inbound_end_clock_datetime
                    , adjustment_1.inbound_end_clock_type
                    , adjustment_1.outbound_start_clock_datetime
                    , adjustment_1.outbound_start_clock_type
                    , adjustment_1.outbound_shipment_id
                    , adjustment_1.outbound_end_clock_datetime
                    , adjustment_1.outbound_end_clock_type
                    , adjustment_1.raw_inbound_time
                    , adjustment_1.raw_processing_time
                    , adjustment_1.raw_outbound_time
                    , adjustment_1.raw_warehouse_time
                    , if(
                        adjustment_1.prelim_inbound_time < 0 and adjustment_1.raw_inbound_time >= 0
                        , 0
                        , adjustment_1.prelim_inbound_time
                    ) as adjusted_inbound_time
                    , if(
                        adjustment_1.prelim_processing_time < 0 and adjustment_1.raw_processing_time >= 0
                        , 0
                        , adjustment_1.prelim_processing_time
                    ) as adjusted_processing_time
                    , if(
                        adjustment_1.prelim_outbound_time < 0 and adjustment_1.raw_outbound_time >= 0
                        , 0
                        , adjustment_1.prelim_outbound_time
                    ) as adjusted_outbound_time
                    , if(
                        adjustment_1.prelim_warehouse_time < 0 and adjustment_1.raw_warehouse_time >= 0
                        , 0
                        , adjustment_1.prelim_warehouse_time
                    ) as adjusted_warehouse_time
                    , if(
                        adjustment_1.prelim_inbound_time = adjustment_1.raw_inbound_time
                        , 0
                        , 1
                    ) as non_working_day_inbound_flag
                    , if(
                        adjustment_1.prelim_processing_time = adjustment_1.raw_processing_time
                        , 0
                        , 1
                    ) as non_working_day_processing_flag
                    , if(
                        adjustment_1.prelim_outbound_time = adjustment_1.raw_outbound_time
                        , 0
                        , 1
                    ) as non_working_day_outbound_flag
                    , if(
                        adjustment_1.prelim_warehouse_time = adjustment_1.raw_warehouse_time
                        , 0
                        , 1
                    ) as non_working_day_warehouse_flag
                    , order_milestones.is_pickup_required
                    , if(
                        adjustment_1.inbound_end_clock_datetime > order_milestones.rts_trigger_datetime,1,0
                    ) rts_leg_flag
                    , max(if(pets_exclusions.pets_exclusion is not null, 1,0)) as pets_exclusion_flag
                from adjustment_1
                left join pets_exclusions on
                    adjustment_1.order_id = pets_exclusions.order_id
                    and adjustment_1.inbound_end_clock_datetime < pets_exclusions.resolution_datetime
                    and adjustment_1.outbound_start_clock_datetime > pets_exclusions.creation_datetime
                left join order_milestones on
                    adjustment_1.order_id = order_milestones.order_id
                group by {{ range(1, 30) | join(',') }}
                """,
                jinja_arguments={
                    "dt": second_difference,
                    "adjust_time_on_conditions": adjust_time_on_conditions,
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with pre_final as (
                    select
                        *
                        , if(adjusted_inbound_time<0, 1,0) as negative_inbound_time_flag
                        , if(adjusted_processing_time<0, 1,0) as negative_processing_time_flag
                        , if(adjusted_outbound_time<0, 1,0) as negative_outbound_time_flag
                        , if(adjusted_warehouse_time<0, 1,0) as negative_warehouse_time_flag
                        , if(inbound_start_clock_datetime is null, 1,0) as missing_inbound_start_clock
                        , if(inbound_end_clock_datetime is null, 1,0) as missing_inbound_end_clock
                        , if(outbound_start_clock_datetime is null, 1,0) as missing_outbound_start_clock
                        , if(outbound_end_clock_datetime is null, 1,0) as missing_outbound_end_clock
                    from
                        adjustment
                )
                , final as (
                    select
                        *
                        , if(
                            negative_inbound_time_flag = 0
                                and missing_inbound_start_clock = 0
                                and missing_inbound_end_clock = 0
                            , 1
                            , 0
                        ) as inbound_measurable
                        , if(
                            pets_exclusion_flag = 0
                                and negative_processing_time_flag = 0
                                and missing_inbound_end_clock = 0
                                and missing_outbound_start_clock = 0
                            , 1
                            , 0
                        ) as processing_measurable
                        , if(
                            negative_outbound_time_flag = 0
                                and missing_outbound_start_clock = 0
                                and missing_outbound_end_clock = 0
                            , 1
                            , 0
                        ) as outbound_measurable
                        , if(
                            pets_exclusion_flag = 0
                                and negative_warehouse_time_flag = 0
                                and missing_inbound_start_clock = 0
                                and missing_outbound_end_clock = 0
                            , 1
                            , 0
                        ) as warehouse_measurable
                        , date_format(inbound_end_clock_datetime, 'yyyy-MM') as inbound_month
                        , date_format(inbound_end_clock_datetime, 'yyyy-MM') as created_month
                    from
                        pre_final
                )
                select * from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).WAREHOUSE_SPEED_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def second_difference(time_1, time_2):
    diff = f"""
        to_unix_timestamp({time_1}) - to_unix_timestamp({time_2})
    """
    return diff


def adjust_time_on_conditions(clock_time_start, clock_time_end, start_holiday, subsequent_holidays, base_time):
    ONE_DAY = 86400
    logic = f"""
    if(
        datediff(date({clock_time_end}),date({clock_time_start})) < 2
        , if(
            {start_holiday}.date is null
            , {base_time}
            , {base_time} - {ONE_DAY} + {second_difference(clock_time_start, f"date_trunc('DD', {clock_time_start})")}
        )
        , if(
            {start_holiday}.date is null
            , {base_time} - ({ONE_DAY} * (count({subsequent_holidays}.date)))
            , {base_time} - ({ONE_DAY} * (count({subsequent_holidays}.date))) - {ONE_DAY}
                + {second_difference(clock_time_start, f"date_trunc('DD', {clock_time_start})")}
        )
    )
    """
    return logic


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
