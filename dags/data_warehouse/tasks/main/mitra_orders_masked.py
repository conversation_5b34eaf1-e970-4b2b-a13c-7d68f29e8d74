import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.MITRA_ORDERS_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.MITRA_ORDERS_MASKED,
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.DPProdGL(input_env, is_masked).DPS,
                view_name="dps",
            ),
            base.InputTable(
                path=delta_tables.DPProdGL(input_env, is_masked).DP_RESERVATIONS,
                view_name="dp_reservations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shippers",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_FRAUD_SHIPPER_WHITELIST_BY_MITRA,
                view_name="id_fraud_shipper_whitelist_by_mitra",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="clean_whitelist",
                jinja_template="""
                -- Remove duplicated entries in gsheet

                select distinct
                    shipper_id
                    , dp_id
                    , start_date
                    , offboard_date
                from id_fraud_shipper_whitelist_by_mitra
                """,
            ),
            base.TransformView(
                view_name="order_list",
                jinja_template="""
                -- get mitra orders in ID

            select
                    dp_reservations.id
                    , dp_reservations.order_id
                    , shippers.id as global_shipper_id
                    , dp_reservations.shipper_id
                    , from_utc_timestamp(dp_reservations.created_at, '{{ local_timezone }}') AS created_at
                    , from_utc_timestamp(dp_reservations.dropped_off_at, '{{ local_timezone }}') as dropped_off_at
                    , dps.id as mitra_id
                    , dps.name AS mitra_name
                    , dps.latitude as mitra_latitude
                    , dps.longitude as mitra_longitude
                    , '{{ system_id }}' as system_id
                    , date_format(dp_reservations.created_at, 'yyyy-MM') as created_month
                from dp_reservations
                inner join dps on
                    dps.id = dp_reservations.dp_id
                inner join shippers on
                    shippers.id = dp_reservations.global_shipper_id
                where
                    dp_reservations.system_id = '{{ system_id }}'
                    and dps.system_id = '{{ system_id }}'
                    and shippers.system_id = '{{ system_id }}'
                    and dp_reservations.deleted_at is null
                    and dps.name <> 'n/a'
                """,
                jinja_arguments={"system_id": system_id, "local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="mitra_orders",
                jinja_template="""
                select
                    order_list.id
                    , order_list.order_id
                    , order_list.global_shipper_id
                    , order_list.shipper_id
                    , order_list.created_at
                    , order_list.dropped_off_at
                    , order_list.mitra_id
                    , order_list.mitra_name
                    , order_list.mitra_latitude
                    , order_list.mitra_longitude
                    , if(clean_whitelist.start_date is not null,'Registered','Unregistered')
                    as mitra_registration_status
                    , order_list.system_id
                    , order_list.created_month
                from order_list
                left join clean_whitelist
                    on order_list.shipper_id = clean_whitelist.shipper_id
                    and order_list.mitra_id = clean_whitelist.dp_id
                    and order_list.dropped_off_at >= to_date(clean_whitelist.start_date)
                    and order_list.dropped_off_at < coalesce(to_date(clean_whitelist.offboard_date),date('2999-01-01'))
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                    -- Get the first mitra name if there are multiple DP reservations

                    select
                        order_id
                        , max_by(dropped_off_at,created_at) as lodged_in_datetime
                        , max_by(mitra_id,created_at) as mitra_id
                        , max_by(mitra_name,created_at) as mitra_name
                        , max_by(mitra_registration_status,created_at) as mitra_registration_status
                        , max_by(mitra_latitude,created_at) as mitra_latitude
                        , max_by(mitra_longitude,created_at) as mitra_longitude
                        , max_by(created_month,created_at) as created_month
                        , system_id
                    from mitra_orders
                    group by 1,9

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MITRA_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()