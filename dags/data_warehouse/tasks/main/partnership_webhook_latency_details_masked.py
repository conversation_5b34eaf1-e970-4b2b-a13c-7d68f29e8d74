import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from datetime import timedelta
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_LATENCY_DETAILS_MASKED + ".py",
    task_name=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_LATENCY_DETAILS_MASKED,
    depends_on=(data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_MASKED,),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse",
                                     partition_columns=("system_id", "shipper", "created_date")),
    ),
)


def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_SLA_CONFIG,
                            view_name="webhook_sla_configuration"
                            ),
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_STATUS_CONFIG,
                            view_name="webhook_status_configuration"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    if measurement_datetime.hour == 2:
        created_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')
    else:
        created_date = measurement_datetime.strftime('%Y-%m-%d')

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK + latest_partition) \
        .filter(F.col("created_date") == created_date).createOrReplaceTempView("webhook_log")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="interim_table",
                jinja_template="""


                select
                    webhook_log.tracking_id
                    , webhook_log.shipper
                    , webhook_log.system_id
                    , webhook_status_configuration.status_code 
                    , webhook_status_configuration.status
                    , webhook_log.event_time
                    , webhook_log.webhook_sent_time 
                    , case
                        when webhook_log.webhook_sent_time is not null and 
                                webhook_log.event_time is not null then 
                            (to_unix_timestamp(webhook_log.webhook_sent_time) - 
                                to_unix_timestamp(webhook_log.event_time))/60
                        else null
                    end as event_to_webhook_minutes_diff
                    , coalesce(webhook_sla_configuration.latency_sla,0) latency_sla
                    , case
                        when webhook_log.webhook_sent_time is not null 
                            and webhook_log.event_time is not null then
                            if((to_unix_timestamp(webhook_log.webhook_sent_time) -
                                to_unix_timestamp(webhook_log.event_time))/60 <=
                                cast(webhook_sla_configuration.latency_sla_in_mins as int) ,1,0)
                        else null
                    end as latency_sla_met
                    , created_date
                from webhook_log
                left join webhook_status_configuration on
                    webhook_log.webhook_event_status = webhook_status_configuration.webhook_event_status
                    and coalesce(webhook_log.webhook_event_state,'empty') = 
                        coalesce(webhook_status_configuration.webhook_event_state,'empty')
                    and cast(coalesce(webhook_log.rts_flag,0) as int) = 
                        cast(coalesce(webhook_status_configuration.rts_flag,0) as int)
                    and (
                        case
                            -- Accomodate for Pricing Updated logic difference across platforms
                            when webhook_status_configuration.url is not null then
                                if(webhook_log.webhook_event_status = 'Pricing Updated', webhook_log.url,'empty') = 
                                    coalesce(webhook_status_configuration.url,'empty')
                            else 
                                 if(webhook_log.webhook_event_status = 'Pricing Updated'
                                    , array_contains(split(replace(webhook_status_configuration.shipper,', ',','),',')
                                    , webhook_log.shipper)
                                    , true)
                        end
                        )
                left join webhook_sla_configuration 
                    on webhook_status_configuration.status_code = cast(webhook_sla_configuration.status_code as int)
                    and webhook_log.shipper = webhook_sla_configuration.shipper
                where 
                    webhook_sla_configuration.latency_flag = 1
                    and response_code = 200
                """,
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""

                select
                    tracking_id
                    , shipper
                    , date(webhook_sent_time) as webhook_sent_date
                    , date(
                        from_utc_timestamp(
                            to_utc_timestamp(webhook_sent_time, {{get_local_timezone}})
                            ,'Asia/Singapore'
                        )
                    ) as webhook_sent_date_sgt
                    , date_format(webhook_sent_time, 'HH:00') as webhook_sent_hour
                    , date_format(
                        from_utc_timestamp(
                            to_utc_timestamp(webhook_sent_time, {{get_local_timezone}})
                            ,'Asia/Singapore'
                        )
                    , 'HH:00') as webhook_sent_hour_sgt
                    , cast(status_code as int) as status_code
                    , status
                    , event_time
                    , webhook_sent_time
                    , from_utc_timestamp(
                        to_utc_timestamp(webhook_sent_time, {{get_local_timezone}})
                        ,'Asia/Singapore'
                    ) as webhook_sent_time_sgt
                    , system_id
                    , latency_sla
                    , latency_sla_met
                    , created_date
                from interim_table
                where latency_sla_met = 0 

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                }
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_LATENCY_DETAILS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "created_date"),
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.measurement_datetime,
    )
    run(spark, task_config)
    spark.stop()