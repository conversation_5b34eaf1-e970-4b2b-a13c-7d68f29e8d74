import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MonitoringDAG.Task.HUBS_COORDINATES_CHANGE_EVENTS_MASKED + ".py",
    task_name=data_warehouse.MonitoringDAG.Task.HUBS_COORDINATES_CHANGE_EVENTS_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.SortProdGL(input_env, is_masked).HUB_COORDS_CHANGE_EVENTS,
                view_name="hub_coords_change_events"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with hubs_base as (
                -- Use hubs enriched for hubs that have zero entries in hub_coords_change_events tables
                    select
                        id as hub_id
                        , latitude as new_latitude
                        , longitude as new_longitude
                        , null as old_latitude
                        , null as old_longitude
                        , date(creation_datetime) as start_date
                        , system_id
                        , date_format(creation_datetime, 'yyyy-MM') as created_month
                        , 'hubs_enriched' as datasource
                    from hubs_enriched
                ),
                
                coords_change_events as (
                    select
                        hub_id
                        , new_lat as new_latitude
                        , new_lon as new_longitude
                        , old_lat as old_latitude
                        , old_lon as old_longitude
                        , date(from_utc_timestamp(created_at, {{ get_local_timezone }})) as start_date
                        , system_id
                        , created_month
                        , 'hub_coords_change_events' as datasource
                    from hub_coords_change_events
                ),
                
                union_cte as (
                    select *
                    from hubs_base
                    union all
                    select *
                    from coords_change_events
                ),
                
                end_date_lag_cte as (
                    select
                        *
                        ,  date(lag(start_date)
                            over(partition by hub_id, system_id order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from union_cte
                ),
                
                lead_cte as (
                    select
                        hub_id
                        , new_latitude
                        , new_longitude
                        , old_latitude
                        , old_longitude
                        , start_date
                        , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                        , lead(old_latitude) over(partition by hub_id, system_id order by start_date) as next_old_latitude
                        , lead(old_longitude) over(partition by hub_id, system_id order by start_date) as next_old_longitude
                        , system_id
                        , created_month
                        , datasource
                    from end_date_lag_cte
                ),
                
                final as (
                    select
                        hub_id
                        -- Hubs enriched only contain the latest latlong.
                        -- If there's a records in hub_coords_change_events, use the old latlong in place of the existing latlong captured in hubs enriched.
                        , if(datasource = 'hubs_enriched' and end_date != '2099-01-01', next_old_latitude, new_latitude) as latitude
                        , if(datasource = 'hubs_enriched' and end_date != '2099-01-01', next_old_longitude, new_longitude) as longitude
                        , start_date
                        , end_date
                        , system_id
                        , created_month
                    from lead_cte
                )
                
                select * from final

                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).HUBS_COORDINATES_CHANGE_EVENTS,
        measurement_datetime=measurement_datetime,
        partition_by=(
            "system_id",
            "created_month",
        ),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()