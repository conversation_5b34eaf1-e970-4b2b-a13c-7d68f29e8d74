import sys

from pyspark.sql import SparkSession

from common.date import Timezone
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.JiraDAG.Task.JIRA_ISSUE_EVENTS_MASKED + ".py",
    task_name=data_warehouse.JiraDAG.Task.JIRA_ISSUE_EVENTS_MASKED,
    system_ids=(constants.SystemID.SG,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    look_back_ranges = base.LookBackRange(None, None)
    is_masked = True
    if not enable_full_run:
        lb_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
        look_back_ranges = base.LookBackRange(input=None, output=lb_ranges.output)

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.Jira(env,is_masked).ISSUES,
                view_name="issues",
                input_range=look_back_ranges.input
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="issue_events",
                jinja_template="""
                with
                    issue_event_json as (

                        -- filter issues by updated datetime and unpack changelog.histories JSON array
                        select
                            id as issue_id
                            , key as issue_key
                            , explode(
                                from_json(get_json_object(changelog, '$.histories'), 'array<string>')
                            ) as event_json
                        from issues
                        {%- if not enable_full_run %}
                        where
                            date_format(
                                from_utc_timestamp(updated, '{{ local_timezone }}'),
                                'yyyy-MM'
                            ) >= '{{ output_range.min }}'
                            and date_format(
                                from_utc_timestamp(updated, '{{ local_timezone }}'),
                                'yyyy-MM'
                            ) <= '{{ output_range.max }}'
                        {%- endif %}

                    ), issue_event_item_json as (

                        -- unpack event_json.items JSON array
                        select
                            cast(get_json_object(event_json, '$.id') as bigint) as id
                            , cast(issue_id as bigint) as issue_id
                            , issue_key
                            , get_json_object(event_json, '$.author.emailAddress') as user
                            , posexplode(
                                from_json(get_json_object(event_json, '$.items'), 'array<string>')
                            ) as (item_id, item_json)
                            , from_utc_timestamp(
                                cast(get_json_object(event_json, '$.created') as timestamp), '{{ local_timezone }}'
                            ) as created_datetime
                        from issue_event_json

                    ), final as (

                        select
                            id
                            , item_id
                            , issue_id
                            , issue_key
                            , user
                            , get_json_object(item_json, '$.field') as item_field
                            , get_json_object(item_json, '$.from') as item_from
                            , get_json_object(item_json, '$.fromString') as item_from_string
                            , get_json_object(item_json, '$.to') as item_to
                            , get_json_object(item_json, '$.toString') as item_to_string
                            , created_datetime
                            , date_format(created_datetime, 'yyyy-MM') as created_month
                        from issue_event_item_json

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "output_range": look_back_ranges.output,
                    "enable_full_run": enable_full_run,
                    "local_timezone": Timezone.SG,
                },
            ),
        ),
        output_range=look_back_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).JIRA_ISSUE_EVENTS,
        measurement_datetime=measurement_datetime,
        system_id=constants.SystemID.SG,
        partition_by=("created_month",),
        output_range=look_back_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()