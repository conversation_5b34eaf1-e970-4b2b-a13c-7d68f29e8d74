import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from data_warehouse.tasks.main import base
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from datetime import datetime as dt
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_SUMMARY_STATS_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_SUMMARY_STATS_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED,
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse",
                                     partition_columns=("system_id", "shipper", "delivery_success_date")),
    ),
)

def get_task_config(spark, env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX,
                            view_name="prefix"
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Data for one completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED + latest_partition) \
        .filter(F.col("delivery_success_date") == completion_date).createOrReplaceTempView("partnership_webhook_orders_enriched")
    
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="daily_stats",
                jinja_template="""

                with latency_base as (
                    -- Lazada Domestic 
                    SELECT 
                        delivery_success_date,
                        tracking_id,
                        shipper,
                        system_id,
                        COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) AS successful_webhooks_sent,
                        COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) AS met_latency_sla,
                        CASE 
                            WHEN COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) > 0 
                            THEN COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) * 1.0 / 
                                 COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END)
                            ELSE NULL 
                        END AS event_transmit_on_time_rate
                    FROM 
                        partnership_webhook_orders_enriched
                    WHERE
                        shipper = 'Lazada Domestic'
                        AND status_code IN (11,12,13,14,15,20,21,22,23,24,25,26,27,28,29,30,31,33,34,37)
                    GROUP BY 
                        1,2,3,4

                    UNION ALL

                    -- Tiktok XB
                    SELECT 
                        delivery_success_date,
                        tracking_id,
                        shipper,
                        system_id,
                        COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) AS successful_webhooks_sent,
                        COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) AS met_latency_sla,
                        CASE 
                            WHEN COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) > 0 
                            THEN COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) * 1.0 / 
                                 COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END)
                            ELSE NULL 
                        END AS event_transmit_on_time_rate
                    FROM 
                        partnership_webhook_orders_enriched
                    WHERE
                        shipper = 'Tiktok XB'
                        AND status_code IN (1,2,3,4,5,6,7,8,9,10,11,12,14,15,20,21,22,23,24,25,26,27,28,29,30,31,33,38,46)
                    GROUP BY 
                        1,2,3,4

                    UNION ALL

                    -- Tiktok domestic
                    SELECT 
                        delivery_success_date,
                        tracking_id,
                        shipper,
                        system_id,
                        COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) AS successful_webhooks_sent,
                        COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) AS met_latency_sla,
                        CASE 
                            WHEN COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END) > 0 
                            THEN COUNT(CASE WHEN latency_sla_met = 1 THEN 1 END) * 1.0 / 
                                 COUNT(CASE WHEN first_webhook_sent_time IS NOT NULL THEN 1 END)
                            ELSE NULL 
                        END AS event_transmit_on_time_rate
                    FROM 
                        partnership_webhook_orders_enriched
                    WHERE
                        shipper = 'Tiktok Domestic'
                        AND status_code IN (11,12,13,14,15,16,22,23,24,25,26,31,36,37,38,46)
                    GROUP BY 
                        1,2,3,4
                    ),

                    completeness_base as (
                        -- Lazada Domestic 
                        SELECT 
                            delivery_success_date,
                            tracking_id,
                            shipper,
                            system_id,
                            count_if(first_webhook_sent_time is not null) as successful_events,
                            count(tracking_id) as total_events,
                            count_if(first_webhook_sent_time is not null)/count(tracking_id) as event_integrity_rate
                        FROM 
                            partnership_webhook_orders_enriched
                        WHERE
                            shipper = 'Lazada Domestic'
                            AND (
                                (system_id = 'sg' AND status_code IN (14,15,22,23,26,27,28,31,33))
                                OR 
                                (system_id != 'sg' AND status_code IN (13,14,15,22,23,26,27,28,31,33))
                            )
                        GROUP BY 
                            1,2,3,4

                        UNION ALL

                        -- Tiktok XB
                        SELECT 
                            delivery_success_date,
                            tracking_id,
                            shipper,
                            system_id,
                            count_if(first_webhook_sent_time is not null) as successful_events,
                            count(tracking_id) as total_events,
                            count_if(first_webhook_sent_time is not null)/count(tracking_id) as event_integrity_rate
                        FROM 
                            partnership_webhook_orders_enriched
                        WHERE
                            shipper = 'Tiktok XB'
                            AND status_code IN (2,3,6,7,8,9,10,11,14,15,22,31,45,46)
                        GROUP BY 
                            1,2,3,4

                        UNION ALL

                        -- Tiktok domestic
                        SELECT 
                            delivery_success_date,
                            tracking_id,
                            shipper,
                            system_id,
                            count_if(first_webhook_sent_time is not null) as successful_events,
                            count(tracking_id) as total_events,
                            count_if(first_webhook_sent_time is not null)/count(tracking_id) as event_integrity_rate
                        FROM 
                            partnership_webhook_orders_enriched
                        WHERE
                            shipper = 'Tiktok Domestic'
                            AND status_code IN (11,14,15,46)
                        GROUP BY 
                            1,2,3,4
                    ),

                    latency_base_final as (
                        SELECT 
                            delivery_success_date,
                            tracking_id,
                            shipper,
                            system_id,
                            CASE 
                                WHEN event_transmit_on_time_rate = 1.0 THEN 'Success' -- only taking parcel with zero SLA breaches 
                                ELSE 'Failed'
                            END AS latency_sla_status
                        FROM latency_base
                    ),

                    completeness_base_final as (
                        SELECT 
                            delivery_success_date,
                            tracking_id,
                            shipper,
                            system_id,
                            CASE 
                                WHEN event_integrity_rate = 1.0 THEN 'Success' -- only taking parcel with zero SLA breaches 
                                ELSE 'Failed'
                            END AS completeness_sla_status
                        FROM completeness_base
                    )

                    select 
                        delivery_success_date,
                        shipper,
                        system_id,
                        'latency_kpi' as kpi_type,
                        count_if(latency_sla_status = 'Success') as success_parcels,  
                        count(tracking_id) as total_parcels,
                        count_if(latency_sla_status = 'Success')/count(tracking_id) as parcels_with_no_breaches
                    from 
                        latency_base_final
                    group by 
                        1,2,3

                    UNION ALL

                    select 
                        delivery_success_date,
                        shipper,
                        system_id,
                        'completeness_kpi' as kpi_type,
                        count_if(completeness_sla_status = 'Success') as success_parcels,  
                        count(tracking_id) as total_parcels,
                        count_if(completeness_sla_status = 'Success')/count(tracking_id) as parcels_with_no_breaches
                    from 
                        completeness_base_final
                    group by 
                        1,2,3
                    order by 
                        delivery_success_date desc    
                    
                """
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path= versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_SUMMARY_STATS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "delivery_success_date"),
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.measurement_datetime,
    )
    run(spark, task_config)
    spark.stop()