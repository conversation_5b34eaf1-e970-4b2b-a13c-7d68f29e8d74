import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
    data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED, data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DRIVER_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_DP_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

PARCEL_SIZES = {"xs", "s", "m", "l", "xl", "xxl"}


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_TAGS, view_name="route_tags"),
            base.InputTable(path=delta_tables.RouteProdGL(input_env, is_masked).TAGS, view_name="tags"),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).INVALID_ATTEMPTS,
                            view_name="invalid_attempts"),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DP_MILESTONES,
                view_name="order_dp_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_SCAN_EVENTS,
                view_name="pickup_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_TRANSACTION_EVENTS,
                view_name="pickup_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_SCAN_EVENTS,
                view_name="driver_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="route_start",
                jinja_template="""
                select
                    route_id
                    , max(event_datetime) as route_start_datetime
                from driver_scan_events
                where
                    result_id in ('S3', 'S4')
                    and event_datetime is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="route_reservations",
                jinja_template="""
                select
                    date(attempt_datetime) as route_date
                    , route_id
                    , waypoint_id
                    , status
                    , attempt_datetime as event_datetime
                from reservations_enriched
                where
                    route_id is not null
                    and attempt_datetime is not null
                """,
            ),
            base.TransformView(
                view_name="route_pickups",
                jinja_template="""
                select
                    date(event_datetime) as route_date
                    , route_id
                    , null as waypoint_id
                    , order_id
                    , 'Success' as status
                    , event_datetime
                from pickup_scan_events
                where
                    scan_result_id = 'S1'
                    and source_table = 'inbound_scans'
                    and route_id is not null
                    and event_datetime is not null
                union all
                select
                    date(event_datetime) as route_date
                    , route_id
                    , waypoint_id
                    , order_id
                    , status
                    , event_datetime
                from pickup_transaction_events
                where
                    order_id is not null
                    and route_id is not null
                    and event_datetime is not null
                """,
            ),
            base.TransformView(
                view_name="route_deliveries",
                jinja_template="""
                select
                    date(event_datetime) as route_date
                    , route_id
                    , waypoint_id
                    , order_id
                    , status
                    , type
                    , event_datetime
                    , bundle_tracking_id
                    , if(valid_flag = 0 and status = 'Fail', 1, 0) as invalid_attempt_flag
                from delivery_transaction_events
                where
                    order_id is not null
                    and route_id is not null
                    and event_datetime is not null
                """,
            ),
            base.TransformView(
                view_name="route_daily",
                jinja_template="""
                with
                    base as (

                        select
                            route_date
                            , route_id
                            , waypoint_id
                            , event_datetime
                        from route_reservations
                        union all
                        select
                            route_date
                            , route_id
                            , waypoint_id
                            , event_datetime
                        from route_pickups
                        union all
                        select
                            route_date
                            , route_id
                            , waypoint_id
                            , event_datetime
                        from route_deliveries

                    )
                    , final as (

                        select
                            base.route_date
                            , base.route_id
                            , min(base.event_datetime) as start_datetime
                            , max(base.event_datetime) as end_datetime
                            , cast(
                                round(
                                    (
                                        to_unix_timestamp(max(base.event_datetime), 'yyyy-MM-dd HH:mm:ss')
                                        - to_unix_timestamp(min(base.event_datetime), 'yyyy-MM-dd HH:mm:ss')
                                    ) / 3600
                                    , 0
                                ) as bigint
                            ) as duration_hrs
                            , count(distinct base.waypoint_id) as total_waypoints
                        from base
                        group by 1, 2

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="route_reservations_daily",
                jinja_template="""
                select
                    route_date
                    , route_id
                    , count(distinct waypoint_id) as total_waypoints
                    , count(distinct waypoint_id) filter (where status IN ('Success','COMPLETED')) as success_waypoints
                    , count(distinct waypoint_id) filter (where status IN ('Fail','FAILED')) as failed_waypoints
                    , count_if(status IN ('Success','COMPLETED')) as successful_reservations
                    , count_if(status IN ('Fail','FAILED')) as failed_reservations
                from route_reservations
                group by 1, 2
                """,
            ),
            base.TransformView(
                view_name="route_pickups_daily",
                jinja_template="""
                select
                    pickup.route_date
                    , pickup.route_id
                    , cast(max(if(orders.cod_value > 0, 1, 0)) as bigint) as cod_flag
                    , count(distinct pickup.order_id) filter (where pickup.status = 'Success') as success_parcels
                    , count(distinct pickup.order_id) filter (
                        where pickup.status = 'Success' and orders.order_type = 'Return'
                    ) as success_return_parcels

                    {%- for size in parcel_sizes %}
                    , count(distinct pickup.order_id) filter (
                        where orders.parcel_size = '{{ size }}' and pickup.status = 'Success'
                    ) as success_size_{{ size }}_parcels
                    {%- endfor %}

                from route_pickups as pickup
                left join orders_enriched as orders on
                    pickup.order_id = orders.order_id
                group by 1, 2
                """,
                jinja_arguments={"parcel_sizes": PARCEL_SIZES},
            ),
            base.TransformView(
                view_name="route_deliveries_daily",
                jinja_template="""
                select
                    delivery.route_date
                    , delivery.route_id
                    , cast(max(if(orders.cod_value > 0, 1, 0)) as bigint) as cod_flag
                    , count(distinct delivery.waypoint_id) as total_waypoints
                    , count(distinct delivery.waypoint_id) filter (
                        where delivery.status = 'Success'
                    ) as success_waypoints
                    , count(distinct delivery.waypoint_id) filter (where delivery.status = 'Fail') as failed_waypoints
                    , count(distinct delivery.waypoint_id) filter (
                        where delivery.status = 'Success' and delivery.type = 'rts'
                    ) as success_rts_waypoints
                    , count(distinct delivery.waypoint_id) filter (
                        where delivery.status = 'Success' and order_dp_milestones.driver_to_dp_datetime is not null
                    ) as success_pudo_waypoints
                    , count(distinct delivery.order_id) as total_parcels
                    , count(distinct delivery.order_id) filter (where delivery.status = 'Success') as success_parcels
                    , count(distinct delivery.order_id) filter (
                        where delivery.invalid_attempt_flag = 1
                    ) as invalid_attempt_parcels
                    , sum(orders.nv_weight) / count(1) as parcel_average_dim

                    {%- for size in parcel_sizes %}
                    , count(distinct delivery.order_id) filter (
                        where orders.parcel_size = '{{ size }}' and delivery.status = 'Success'
                    ) as success_size_{{ size }}_parcels
                    {%- endfor %}

                    , count(distinct delivery.order_id) filter (
                        where delivery.status = 'Success' and delivery.type = 'rts'
                    ) as success_rts_parcels
                    , count(distinct delivery.order_id) filter (
                        where delivery.status = 'Fail' and delivery.type = 'rts'
                    ) as failed_rts_parcels

                    {%- for size in parcel_sizes %}
                    , count(distinct delivery.order_id) filter (
                        where
                            orders.parcel_size = '{{ size }}'
                            and delivery.status = 'Success'
                            and delivery.type = 'rts'
                    ) as success_rts_size_{{ size }}_parcels
                    {%- endfor %}

                    , count(distinct delivery.order_id) filter (
                        where delivery.status = 'Success' and order_dp_milestones.driver_to_dp_datetime is not null
                    ) as success_pudo_parcels
                    , count(distinct delivery.order_id) filter (
                        where delivery.status = 'Fail' and order_dp_milestones.driver_to_dp_datetime is not null
                    ) as failed_pudo_parcels

                    , count(distinct delivery.order_id) filter (where delivery.status = 'Success' and bundle_tracking_id is not null) as success_b2b_parcels
                    , count(distinct delivery.bundle_tracking_id) filter (where delivery.status = 'Success' and bundle_tracking_id is not null) as success_b2b_bundles
                    , count(distinct delivery.waypoint_id) filter (where delivery.status = 'Success' and bundle_tracking_id is not null) as success_b2b_waypoints

                from route_deliveries as delivery
                left join orders_enriched as orders on
                    delivery.order_id = orders.order_id
                left join order_dp_milestones on
                    delivery.order_id = order_dp_milestones.order_id
                group by 1, 2
                """,
                jinja_arguments={"parcel_sizes": PARCEL_SIZES},
            ),
            base.TransformView(
                view_name="route_tags_cte",
                jinja_template="""
                select
                    route_logs.legacy_id as route_id
                    , collect_list(tags.name) as route_tags
                from route_logs
                left join route_tags on
                    route_logs.legacy_id =route_tags.route_id
                    and route_tags.system_id = '{{ system_id }}'
                    and route_logs.system_id = '{{ system_id }}'
                left join tags on
                    route_tags.tag_id=tags.id
                    and tags.system_id = '{{ system_id }}'
                where route_tags.deleted_at is null
                group by 1
                """,
                jinja_arguments={"system_id": system_id},
            ),
            base.TransformView(
                view_name="invalid_attempts_daily",
                jinja_template="""
                with pickup_events as (
                  select
                    reservation_id
                    , route_id
                    , date(event_datetime) route_date
                from pickup_scan_events
                group by 1,2,3
                ),

                base as (
                    select
                        invalid_attempts.pod_validation_task_id
                        , date(from_utc_timestamp(invalid_attempts.attempted_at, '{{ local_timezone }}')) route_date
                        , coalesce(delivery_transaction_events.route_id
                            , pickup_transaction_events.route_id, pickup_events.route_id) route_id
                        , invalid_attempts.appeal_outcome
                        , if((unix_timestamp(now()) 
                            - unix_timestamp(from_utc_timestamp(invalid_attempts.validated_at, '{{ local_timezone }}'))) / 3600 
                            >= 72, 1, 0) 3_days_flag
                        , invalid_attempts.job_type
                    from invalid_attempts
                    left join delivery_transaction_events
                        on invalid_attempts.job_id = delivery_transaction_events.transaction_id
                        and invalid_attempts.job_type IN ('DELIVERY')
                    left join pickup_events
                        on pickup_events.reservation_id = invalid_attempts.job_id
                        and invalid_attempts.job_type IN ('RESERVATION','PICKUP_APPOINTMENT','PUDO_PICKUP_APPOINTMENT')
                    left join pickup_transaction_events
                        on pickup_transaction_events.transaction_id = invalid_attempts.job_id
                        and invalid_attempts.job_type IN ('RETURN_PICKUP')
                    where
                        invalid_attempts.system_id = '{{ system_id }}'
                )

                select
                    route_id
                    , route_date
                    , count(distinct pod_validation_task_id)
                        filter(where (appeal_outcome = 'FAILURE' and job_type IN ('RESERVATION','PICKUP_APPOINTMENT','PUDO_PICKUP_APPOINTMENT'))
                            or (3_days_flag = 0 and appeal_outcome is null)) invalid_pop_attempts_count
                    , count(distinct pod_validation_task_id) 
                        filter(where (appeal_outcome = 'FAILURE' and job_type IN ('DELIVERY','RETURN_PICKUP'))
                            or (3_days_flag = 0 and appeal_outcome is null)) invalid_pod_attempts_count
                from base
                group by 1,2
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    '{{ system_id }}' as country
                    , route_daily.route_date
                    , weekofyear(route_daily.route_date) as route_week
                    , month(route_daily.route_date) as route_month
                    , cast(route_daily.route_id as long) route_id
                    , cast(route.hub_id as bigint) as depot_id
                    , hubs.name as depot_name
                    , hubs.address_city as depot_city
                    , hubs.area as depot_area
                    , hubs.region as depot_region
                    , cast(route.driver_id as bigint) as courier_id
                    , drivers.first_name as courier_name
                    , drivers.display_name as courier_display_name
                    , drivers.driver_type as courier_type
                    , drivers.fleet_type as fleet_type
                    , drivers.veh_type as vehicle_type
                    , drivers.scheme_type
                    , cast(route.archived as bigint) as archived_flag
                    , route_start.route_start_datetime as route_start
                    , coalesce(greatest(route_deliveries_daily.cod_flag, route_pickups_daily.cod_flag), 0) as cod_flag
                    , route_daily.start_datetime as zone_start
                    , route_daily.end_datetime as zone_end
                    , route_daily.duration_hrs as zone_time_hrs
                    , route_daily.total_waypoints
                    , coalesce(route_reservations_daily.total_waypoints, 0) as total_reservation_waypoints
                    , coalesce(route_reservations_daily.success_waypoints, 0) as reservation_success_waypoints
                    , coalesce(route_reservations_daily.failed_waypoints, 0) as reservation_failed_waypoints
                    , coalesce(route_deliveries_daily.total_waypoints, 0) as total_delivery_waypoints
                    , coalesce(route_deliveries_daily.success_waypoints, 0) as delivery_success_waypoints
                    , coalesce(route_deliveries_daily.failed_waypoints, 0) as delivery_failed_waypoints
                    , coalesce(route_deliveries_daily.success_rts_waypoints, 0) as rts_delivery_success_waypoints
                    , coalesce(route_deliveries_daily.success_pudo_waypoints, 0) as pudo_delivery_success_waypoints
                    , coalesce(route_reservations_daily.successful_reservations, 0) as successful_reservations
                    , coalesce(route_reservations_daily.failed_reservations, 0) as failed_reservations
                    , coalesce(route_pickups_daily.success_parcels, 0) as parcels_picked_up
                    , coalesce(route_pickups_daily.success_return_parcels, 0) as return_parcels_picked_up

                    {%- for size in parcel_sizes %}
                    , coalesce(
                        route_pickups_daily.success_size_{{ size }}_parcels, 0
                    ) as parcels_picked_up_size_{{ size }}
                    {%- endfor %}

                    , coalesce(route_deliveries_daily.total_parcels, 0) as parcels_on_route
                    , coalesce(route_deliveries_daily.success_parcels, 0) as parcels_delivered
                    , coalesce(route_deliveries_daily.invalid_attempt_parcels, 0) as invalid_delivery_attempts
                    , coalesce(route_deliveries_daily.parcel_average_dim, 0) as average_dim

                    {%- for size in parcel_sizes %}
                    , coalesce(
                        route_deliveries_daily.success_size_{{ size }}_parcels, 0
                    ) as parcels_delivered_size_{{ size }}
                    {%- endfor %}

                    , coalesce(route_deliveries_daily.success_rts_parcels, 0) as rts_parcels_delivered
                    , coalesce(route_deliveries_daily.failed_rts_parcels, 0) as rts_parcels_failed

                    {%- for size in parcel_sizes %}
                    , coalesce(
                        route_deliveries_daily.success_rts_size_{{ size }}_parcels, 0
                    ) as rts_parcels_delivered_size_{{ size }}
                    {%- endfor %}

                    , coalesce(route_deliveries_daily.success_pudo_parcels, 0) as pudo_parcels_delivered
                    , coalesce(route_deliveries_daily.failed_pudo_parcels, 0) as pudo_parcels_failed
                    , (
                        coalesce(route_deliveries_daily.success_parcels, 0)
                        + coalesce(route_pickups_daily.success_parcels, 0)
                    ) / route_daily.duration_hrs as ppozh
                    , coalesce(invalid_attempts_daily.invalid_pop_attempts_count, 0) as invalid_pop_attempts_count
                    , coalesce(invalid_attempts_daily.invalid_pod_attempts_count, 0) as invalid_pod_attempts_count
                    , coalesce(route_deliveries_daily.success_b2b_parcels, 0) as b2b_parcels_delivered
                    , coalesce(route_deliveries_daily.success_b2b_bundles, 0) as b2b_bundles_delivered
                    , coalesce(route_deliveries_daily.success_b2b_waypoints, 0) as b2b_delivery_success_waypoints
                    , cast(route_tags as string) route_tags
                    , route.vehicle_number
                    , date_format(route_daily.route_date, 'yyyy-MM') as created_month
                from route_daily
                left join route_reservations_daily on
                    route_daily.route_id = route_reservations_daily.route_id
                    and route_daily.route_date = route_reservations_daily.route_date
                left join route_pickups_daily on
                    route_daily.route_id = route_pickups_daily.route_id
                    and route_daily.route_date = route_pickups_daily.route_date
                left join route_deliveries_daily on
                    route_daily.route_id = route_deliveries_daily.route_id
                    and route_daily.route_date = route_deliveries_daily.route_date
                left join route_logs as route on
                    route_daily.route_id = route.legacy_id
                    and route.system_id = '{{ system_id }}'
                left join route_start on
                    route_daily.route_id = route_start.route_id
                left join hubs_enriched as hubs on
                    route.hub_id = hubs.id
                left join drivers_enriched as drivers on
                    route.driver_id = drivers.id
                left join route_tags_cte on
                    route_tags_cte.route_id=route_daily.route_id
                left join invalid_attempts_daily on
                    route_daily.route_id = invalid_attempts_daily.route_id
                    and route_daily.route_date = invalid_attempts_daily.route_date
                """,
                jinja_arguments={"parcel_sizes": PARCEL_SIZES, "system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FLEET_PERFORMANCE_BASE_DATA,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
