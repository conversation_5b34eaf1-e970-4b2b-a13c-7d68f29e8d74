import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.MISSORT_KPI_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.MISSORT_KPI_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.HubsDAG.Task.SCAN_RESULT_ENRICHED_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SCAN_RESULT_ENRICHED,
                view_name="scan_result_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with exclusion_cte as (
                
                    select
                        *
                        -- Exclude VN data from 4th Sep 2024 to 27th Sep 2024 due to migration of sort tasks. 
                        -- Requested by Reg Sort on 12th November 2024
                        , if(system_id = 'vn'
                            and date(prev_scan_datetime) between date '2024-09-04' and date '2024-09-27'
                                , 1, 0) as exclusion_flag
                    from scan_result_enriched
                )

                 , monthly_missorts as (

                        select
                            system_id
                            , date_format(prev_scan_datetime,'yyyy-MM') as created_month
                            , prev_coalesce_parent_hub_id as hub_id
                            , prev_coalesce_parent_hub_id as hub_name
                            , count(distinct(case 
                                                when (prev_scan_type = 'SCAN_TYPE_BULK_ATS_AND_CLOSE') 
                                                    and exclusion_flag = 0
                                                    then tracking_id
                                                else null 
                                                end)) as machine_missorts
                            , count(distinct(case 
                                                when (prev_scan_type != 'SCAN_TYPE_BULK_ATS_AND_CLOSE')
                                                    and exclusion_flag = 0
                                                    then tracking_id
                                                else null 
                                                end)) as human_missorts
                            , count(distinct tracking_id) filter(where exclusion_flag = 0) as total_missorts
                        from exclusion_cte
                        where kpi_missort_flag = 1
                        group by {{ range(1, 5) | join(',') }}

                    )
                    , monthly_scans as (

                        select
                            system_id
                            , created_month
                            , coalesce_parent_hub_id as hub_id
                            , coalesce_parent_hub_name as hub_name
                            , count(distinct(case 
                                                when (scan_type = 'SCAN_TYPE_BULK_ATS_AND_CLOSE')
                                                    and exclusion_flag = 0
                                                    then tracking_id
                                                else null
                                                end)) as total_distinct_machine_scans
                            , count(distinct tracking_id) filter(where exclusion_flag = 0) as total_distinct_scans
                        from exclusion_cte
                        where sort_hub_flag = 1
                        group by {{ range(1, 5) | join(',') }}

                    )
                    , final as (

                        select
                            monthly_scans.system_id
                            , monthly_scans.created_month
                            , monthly_scans.hub_id
                            , monthly_scans.hub_name
                            , coalesce(monthly_missorts.machine_missorts,0) as missort_machine_scans
                            , coalesce(monthly_missorts.human_missorts,0) as missort_human_scans
                            , coalesce(monthly_missorts.total_missorts,0) as total_missort_scans
                            , coalesce(monthly_scans.total_distinct_machine_scans,0) as total_distinct_machine_scans
                            , coalesce(monthly_scans.total_distinct_scans,0) as total_distinct_scans
                        from monthly_scans
                        left join monthly_missorts
                            on monthly_scans.system_id = monthly_missorts.system_id
                            and monthly_scans.created_month = monthly_missorts.created_month
                            and monthly_scans.hub_id = monthly_missorts.hub_id
                    )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MISSORT_KPI,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
