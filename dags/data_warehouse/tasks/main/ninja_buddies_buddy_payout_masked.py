import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_BUDDY_PAYOUT_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_BUDDY_PAYOUT_MASKED,
    system_ids=(
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.SalesforceDAG.Task.NINJA_BUDDIES_LEADS_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_BUDDIES_LEADS,
                view_name="ninja_buddies_leads",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="nb_leads_revised",
                jinja_template="""
                select *
                    , case
                        when month(first_order_completion_date) <= 6 then year(first_order_completion_date)
                        else year(first_order_completion_date)+1
                    end as activation_fy
                from ninja_buddies_leads
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="base",
                jinja_template="""
                    select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , friend_sales_territory
                    , first_order_completion_date

                    -- including close_won column for indonesia
                    , case
                        when first_order_completion_date is not null then 1
                        else 0
                    end as close_won

                    -- including all logic for programme end date
                    , case
                        when activation_fy < 2022 then
                            case
                                when system_id in ('my', 'id')
                                    then ( date_trunc('month', first_order_completion_date + interval '3' month)
                                        - interval '1' day)
                                when system_id in ('th', 'ph', 'vn')
                                    then ( date_trunc('month', first_order_completion_date + interval '2' month)
                                        - interval '1' day)
                                when system_id = 'sg' then first_order_completion_date + interval '60' day
                            else null end
                        when activation_fy = 2022 then
                            case
                                when system_id in ('my', 'id')
                                    then ( date_trunc('month', first_order_completion_date + interval '4' month)
                                        - interval '1' day)
                                when system_id in ('th', 'ph', 'vn')
                                    then ( date_trunc('month', first_order_completion_date + interval '3' month)
                                        - interval '1' day)
                                when system_id = 'sg' then first_order_completion_date + interval '60' day
                                when system_id = 'vn' then
                                    case
                                        when first_order_completion_date < cast('2022-05-17' as date)
                                            then ( date_trunc('month', first_order_completion_date + interval '4' month)
                                                - interval '1' day)
                                        when first_order_completion_date >= cast('2022-05-17' as date) then
                                            case
                                                when day(first_order_completion_date) = 1
                                                    then ( date_trunc(
                                                        'month', first_order_completion_date + interval '3' month
                                                    ) - interval '1' day )
                                                when day(first_order_completion_date) != 1
                                                    then ( date_trunc(
                                                        'month', first_order_completion_date + interval '4' month
                                                    ) - interval '1' day)
                                            else null end
                                    else null end
                            else null end
                        when activation_fy > 2022 then
                            case
                                when system_id in ('my', 'id') then
                                    case
                                        when day(first_order_completion_date) = 1
                                            then ( date_trunc('month', first_order_completion_date + interval '3' month)
                                                - interval '1' day )
                                        when day(first_order_completion_date) != 1
                                            then ( date_trunc('month', first_order_completion_date + interval '4' month)
                                                - interval '1' day)
                                    else null end
                                when system_id in ('th', 'ph', 'vn') then
                                    case
                                        when day(first_order_completion_date) = 1
                                            then ( date_trunc('month', first_order_completion_date + interval '2' month)
                                                - interval '1' day )
                                        when day(first_order_completion_date) != 1
                                            then ( date_trunc('month', first_order_completion_date + interval '3' month)
                                                - interval '1' day)
                                    else null end
                                when system_id = 'sg' then first_order_completion_date + interval '60' day
                            else null end
                    else null end as prog_end_date


                    -- including all logic for m0 date
                    , case
                        when activation_fy < 2022 then null
                        when activation_fy = 2022 then
                            case
                                when (system_id != 'vn')
                                    or (system_id='vn' and first_order_completion_date < cast('2022-05-17' as date))
                                    then date_trunc('month', first_order_completion_date)
                                when (system_id = 'vn') and (first_order_completion_date >= cast('2022-05-17' as date))
                                    then
                                        case
                                            when day(first_order_completion_date) = 1 then null
                                            when day(first_order_completion_date) != 1
                                                then date_trunc('month', first_order_completion_date)
                                        else null end
                            else null end
                        when activation_fy > 2022 then
                            case
                                when day(first_order_completion_date) = 1 then null
                                when day(first_order_completion_date) != 1
                                    then date_trunc('month', first_order_completion_date)
                            else null end
                    else null end as m0_month


                    -- including all logic for m1 date
                    , case
                        when activation_fy < 2022 then date_trunc('month', first_order_completion_date)
                        when activation_fy = 2022 then
                            case
                                when (system_id != 'vn')
                                    or (system_id='vn' and first_order_completion_date < cast('2022-05-17' as date))
                                    then date_trunc('month', first_order_completion_date + interval '1' month)
                                when (system_id = 'vn') and (first_order_completion_date >= cast('2022-05-17' as date))
                                    then
                                        case
                                            when day(first_order_completion_date) = 1
                                                then date_trunc('month', first_order_completion_date)
                                            when day(first_order_completion_date) != 1
                                                then date_trunc('month', first_order_completion_date
                                                    + interval '1' month)
                                        else null end
                            else null end
                        when activation_fy > 2022 then
                            case
                                when day(first_order_completion_date) = 1
                                    then date_trunc('month', first_order_completion_date)
                                when day(first_order_completion_date) != 1
                                    then date_trunc('month', first_order_completion_date + interval '1' month)
                            else null end
                    end as m1_month


                    -- including all logic for m2 date
                    , case
                        when activation_fy < 2022
                            then date_trunc('month', first_order_completion_date + interval '1' month)
                        when activation_fy = 2022 then
                            case
                                when (system_id != 'vn')
                                    or (system_id='vn' and first_order_completion_date < cast('2022-05-17' as date))
                                        then date_trunc('month', first_order_completion_date + interval '2' month)
                                when (system_id = 'vn') and (first_order_completion_date >= cast('2022-05-17' as date))
                                    then
                                        case
                                            when day(first_order_completion_date) = 1
                                                then date_trunc(
                                                    'month', first_order_completion_date + interval '1' month
                                                )
                                            when day(first_order_completion_date) != 1
                                                then date_trunc(
                                                    'month', first_order_completion_date + interval '2' month
                                                )
                                        else null end
                            else null end
                        when activation_fy > 2022 then
                            case
                                when day(first_order_completion_date) = 1
                                    then date_trunc('month', first_order_completion_date + interval '1' month)
                                when day(first_order_completion_date) != 1
                                    then date_trunc('month', first_order_completion_date + interval '2' month)
                            else null end
                    end as m2_month


                    -- including all logic for m3 date
                    , case
                        when activation_fy < 2022
                            then date_trunc('month', first_order_completion_date + interval '2' month)
                        when activation_fy = 2022 then
                            case
                                when (system_id != 'vn')
                                    or (system_id='vn' and first_order_completion_date < cast('2022-05-17' as date))
                                    then date_trunc('month', first_order_completion_date + interval '3' month)
                                when (system_id = 'vn') and (first_order_completion_date >= cast('2022-05-17' as date))
                                    then
                                        case
                                            when day(first_order_completion_date) = 1
                                                then date_trunc(
                                                    'month', first_order_completion_date + interval '2' month
                                                )
                                            when day(first_order_completion_date) != 1
                                                then date_trunc(
                                                    'month', first_order_completion_date + interval '3' month
                                                )
                                        else null end
                            else null end
                        when activation_fy > 2022 then
                            case
                                when day(first_order_completion_date) = 1
                                    then date_trunc('month', first_order_completion_date + interval '2' month)
                                when day(first_order_completion_date) != 1
                                    then date_trunc('month', first_order_completion_date + interval '3' month)
                            else null end
                    end as m3_month

                    , ifnull(m0_orders,0) as m0_orders
                    , ifnull(m1_orders,0) as m1_orders
                    , ifnull(m2_orders,0) as m2_orders
                    , ifnull(m3_orders,0) as m3_orders
                    , first_60d_orders
                    , first_2mos_avg_orders
                    , first_3mos_avg_orders
                from nb_leads_revised
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="vn_after_tb",
                jinja_template="""
                with country_base as (
                    select *
                    from base
                    where system_id='vn'
                        and first_order_completion_date >= cast('2022-05-17' as date)
                )

                , first_month_tb as (
                    select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , friend_sales_territory
                    , first_order_completion_date
                    , prog_end_date
                    , case
                        when datediff(prog_end_date, current_date) <= 0 then 0
                        when datediff(prog_end_date, current_date) > 0 then datediff(prog_end_date, current_date)
                    else null end as days_left
                    , m1_month as eligible_month
                    , (m0_orders + m1_orders) as eligible_vol
                    from country_base
                    order by first_order_completion_date
                )

                , second_month_tb as (
                    select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , friend_sales_territory
                    , first_order_completion_date
                    , prog_end_date
                    , case
                        when datediff(prog_end_date, current_date) <= 0 then 0
                        when datediff(prog_end_date, current_date) > 0 then datediff(prog_end_date, current_date)
                    else null end as days_left
                    , m2_month as eligible_month
                    , m2_orders as eligible_vol
                    from country_base
                    order by first_order_completion_date
                )


                , base_tb as (
                    select * from first_month_tb
                    union
                    select * from second_month_tb
                )

                , buddy_agg_tb as (
                    select system_id
                        , buddy_sp_code
                        , buddy_shipper_id
                        , buddy_shipper_name
                        , null as payment_type
                        , eligible_month
                        , sum(eligible_vol) as total_eligible_vol
                        ,  eligible_month + interval '1' month as payment_month
                        , case
                            when sum(eligible_vol) >= 500
                                then concat('>=', cast(floor(sum(eligible_vol)/500)*500 as string))
                        else null end as payment_tier
                        , floor(sum(eligible_vol)/500)*750000 as payment_amount
                    from base_tb
                    group by 1,2,3,4,5,6,8
                )


                select *
                from buddy_agg_tb
                order by buddy_sp_code, buddy_shipper_name
                """,
            ),
            base.TransformView(
                view_name="my_tb",
                jinja_template="""
                with country_base as (
                    select *
                    from base
                    where system_id='my'
                )

                select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , first_order_completion_date
                    , prog_end_date
                    , 'first_3mos_avg' as payment_type
                    , null as eligible_month
                    , first_3mos_avg_orders as eligible_vol
                    , case
                        when first_3mos_avg_orders >= 1000 then '>=1000'
                        when first_3mos_avg_orders >= 500 then '>=500'
                        when first_3mos_avg_orders >= 250 then '>=250'
                        when first_3mos_avg_orders >= 33.3 then '>=33.3'
                    else null end as payment_tier
                    , case
                        when day(first_order_completion_date) = 1
                            then date_trunc('month', first_order_completion_date) + interval '3' month
                        when day(first_order_completion_date) != 1
                            then date_trunc('month', first_order_completion_date) + interval '4' month
                    else null end as payment_month
                    , case
                        when first_3mos_avg_orders >= 1000 then 600
                        when first_3mos_avg_orders >= 500 then 250
                        when first_3mos_avg_orders >= 250 then 100
                        when first_3mos_avg_orders >= 33.3 then 30
                    else 0 end as payment_amount
                from country_base
                order by first_order_completion_date
                """,
            ),
            base.TransformView(
                view_name="ph_tb",
                jinja_template="""
                with country_base as (
                    select *
                    from base
                    where system_id='ph'
                        and first_order_completion_date is not null
                )

                , first_month_tb as (
                    select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , friend_sales_territory
                    , first_order_completion_date
                    , prog_end_date
                    , case
                        when datediff(prog_end_date, current_date) <= 0 then 0
                        when datediff(prog_end_date, current_date) > 0 then datediff(prog_end_date, current_date)
                    else null end as days_left
                    , date_trunc('month', first_order_completion_date + interval '1' month) as eligible_month
                    , (m0_orders + m1_orders) as eligible_vol
                    from country_base
                    order by first_order_completion_date
                )

                , second_month_tb as (
                    select system_id
                    , buddy_sp_code
                    , buddy_shipper_id
                    , buddy_shipper_name
                    , friend_shipper_id
                    , friend_shipper_name
                    , friend_sales_territory
                    , first_order_completion_date
                    , prog_end_date
                    , case
                        when datediff(prog_end_date, current_date) <= 0 then 0
                        when datediff(prog_end_date, current_date) > 0 then datediff(prog_end_date, current_date)
                    else null end as days_left
                    , date_trunc('month', first_order_completion_date + interval '2' month) as eligible_month
                    , m2_orders as eligible_vol
                    from country_base
                    order by first_order_completion_date
                )


                , base_tb as (
                    select * from first_month_tb
                    union
                    select * from second_month_tb
                )

                , buddy_agg_tb as (
                    select system_id
                        , buddy_sp_code
                        , buddy_shipper_id
                        , buddy_shipper_name
                        , case
                            when friend_sales_territory in ('GMA', 'MM') then 'metro'
                            when lower(friend_sales_territory) like '%luzon%' then 'provincial'
                        else null end as payment_type
                        , eligible_month
                        , sum(eligible_vol) as total_eligible_vol
                        , eligible_month + interval '1' month as payment_month
                    from base_tb
                    group by 1,2,3,4,5,6,8

                )

                , final as (
                    select system_id
                        , buddy_sp_code
                        , buddy_shipper_id
                        , buddy_shipper_name
                        , payment_type
                        , eligible_month
                        , total_eligible_vol
                        , payment_month
                        , case
                            when payment_type = 'metro' then
                                case
                                    when sum(total_eligible_vol) >= 4000 then '>=4000'
                                    when sum(total_eligible_vol) >= 2000 then '>=2000'
                                    when sum(total_eligible_vol) >= 500 then '>=500'
                                    when sum(total_eligible_vol) >= 200 then '>=200'
                                else null end
                            when payment_type ='provincial' then
                                case
                                    when sum(total_eligible_vol) >= 1000 then '>=1000'
                                    when sum(total_eligible_vol) >= 500 then '>=500'
                                    when sum(total_eligible_vol) >= 150 then '>=150'
                                    when sum(total_eligible_vol) >= 50 then '>=50'
                                else null end
                        else null end as payment_tier
                        , case
                            when payment_type = 'metro' then
                                case
                                    when sum(total_eligible_vol) >= 4000 then 30000
                                    when sum(total_eligible_vol) >= 2000 then 7500
                                    when sum(total_eligible_vol) >= 500 then 1100
                                    when sum(total_eligible_vol) >= 200 then 200
                                else null end
                            when payment_type ='provincial' then
                                case
                                    when sum(total_eligible_vol) >= 1000 then 7500
                                    when sum(total_eligible_vol) >= 500 then 2600
                                    when sum(total_eligible_vol) >= 150 then 700
                                    when sum(total_eligible_vol) >= 50 then 200
                                else null end
                        else null end as payment_amount
                    from buddy_agg_tb
                    group by {{ range(1, 9) | join(',') }}
                )
                    select *
                    from final
                    order by buddy_sp_code, buddy_shipper_name
                """,
            ),
            base.TransformView(
                view_name="nb_buddy",
                jinja_template="""
                with union_tb as (
                    select * from vn_after_tb
                    union
                    select * from ph_tb
                )

                select system_id
                    , date_format(cast(payment_month as date), 'yyyy-MM') as created_month
                    , buddy_sp_code
                    , cast(buddy_shipper_id as bigint)
                    , buddy_shipper_name
                    , payment_type
                    , cast(eligible_month as date)
                    , cast(total_eligible_vol as double)
                    , payment_tier
                    , cast(payment_amount as double)
                    , cast(payment_month as date)
                from union_tb
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    select
                        *
                    from nb_buddy
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_BUDDIES_BUDDY_PAYOUT,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()