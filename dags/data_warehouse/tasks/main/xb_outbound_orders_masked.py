import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.XB_OUTBOUND_ORDERS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.XB_OUTBOUND_ORDERS_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 4, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                /*
                NV XB orders are identified as TIDs that appear in multiple system_ids AND at least one of these rows
                has tracking_id = third_party_tracking_id. Rows with tracking_id = third_party_tracking_id capture the
                parcel journey before they are being sent to another NV country for delivery.
                */

                with
                    base as (

                        select
                            tracking_id
                            , count_if(tracking_id = third_party_tracking_id) as matching_tids
                            , count(distinct tracking_id, system_id) as total_rows
                        from orders_enriched
                        group by 1
                        having
                            total_rows >= 2
                            and matching_tids >= 1

                    )
                    , final as (

                        select
                            order_id
                            , tracking_id
                            , third_party_tracking_id
                            , if(tracking_id = third_party_tracking_id, 1, 0) as outbound_flag
                            , system_id
                            , date_format(creation_datetime, 'yyyy-MM') as created_month
                        from orders_enriched
                        left semi join base on
                            orders_enriched.tracking_id = base.tracking_id

                    )

                select
                    *
                from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_OUTBOUND_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
