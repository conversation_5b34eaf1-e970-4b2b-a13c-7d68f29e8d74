import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_RETENTION_MASKED + ".py",
    task_name=data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_RETENTION_MASKED,
    system_ids=(constants.SystemID.VN,),
    depends_on=(data_warehouse.NinjaMartDAG.Task.NINJA_MART_VN_LINE_ITEMS_ENRICHED_MASKED,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("calendar_month",)),
    ),
)

def get_task_config(env, system_id,  measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_MART_VN_LINE_ITEMS_ENRICHED,
                view_name="ninja_mart_vn_line_items_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="ninja_mart_calendar",
                jinja_template="""
                SELECT 
                    DISTINCT DATE(CONCAT(created_month, '-01')) AS month
                FROM 
                    ninja_mart_vn_line_items_enriched
                """,
            ),
            base.TransformView(
                view_name="customer_base",
                jinja_template="""
                SELECT 
                    customer_id
                    , DATE(CONCAT(completed_month, '-01')) AS completed_month
                    , MAX(customer_name) AS customer_name
                    , MAX(shipping_province_name) AS province
                    , COUNT(DISTINCT order_number) AS order_count
                    , SUM(net_revenue) AS monthly_net_rev
                    , COUNT(DISTINCT category_name) AS num_cat_bought
                    , COUNT(DISTINCT sub_category_name) AS num_sub_cat_bought
                    , SORT_ARRAY(COLLECT_SET(category_name)) AS categories
                    , SORT_ARRAY(COLLECT_SET(sub_category_name)) AS sub_categories
                FROM 
                    ninja_mart_vn_line_items_enriched
                WHERE
                    customer_id IS NOT NULL
                    AND completed_month IS NOT NULL 
                    AND cancelled_at IS NULL 
                GROUP BY
                    customer_id, completed_month
                """,
            ),
            base.TransformView(
                view_name="foc_augmented",
                jinja_template="""
                SELECT 
                    foc.customer_id
                    , COALESCE(
                        customer_base.customer_name, MAX(customer_base.customer_name) 
                        OVER (PARTITION BY foc.customer_id)) 
                      AS customer_name
                    , COALESCE(
                        customer_base.province, MAX(customer_base.province) 
                        OVER (PARTITION BY foc.customer_id))
                      AS province
                    , COALESCE(customer_base.order_count, 0) AS order_count
                    , ninja_mart_calendar.month AS calendar_month
                    , foc.foc_month
                    , (customer_base.completed_month IS NOT NULL) AS is_active
                    , COALESCE(customer_base.monthly_net_rev, 0) AS monthly_net_rev
                    , COALESCE(customer_base.num_cat_bought, 0) AS num_cat_bought
                    , COALESCE(customer_base.num_sub_cat_bought, 0) AS num_sub_cat_bought
                    , customer_base.categories
                    , customer_base.sub_categories
                FROM 
                    (
                    SELECT
                        customer_id,
                        MIN(completed_month) AS foc_month
                    FROM 
                        customer_base
                    GROUP BY customer_id
                    ) AS foc
                JOIN 
                    ninja_mart_calendar ON foc_month <= ninja_mart_calendar.month
                LEFT JOIN  
                    customer_base 
                ON 
                    foc.customer_id = customer_base.customer_id
                    AND ninja_mart_calendar.month = customer_base.completed_month
                """,
            ),
            base.TransformView(
                view_name="l2m_augmented",
                jinja_template="""
                SELECT 
                    curr.*
                    , l1m.is_active AS is_active_l1m
                    , l2m.is_active AS is_active_l2m
                    , CASE 
                        WHEN curr.calendar_month = curr.foc_month THEN 'New'
                        WHEN curr.is_active THEN 'Engaged' 
                        WHEN l1m.is_active THEN 'Lapsed M1'
                        WHEN l2m.is_active THEN 'Lapsed M2'
                        ELSE 'Churned' 
                    END AS retention_status
                FROM 
                    foc_augmented curr
                LEFT JOIN 
                    foc_augmented l1m 
                ON 
                    curr.customer_id = l1m.customer_id 
                    AND curr.calendar_month = l1m.calendar_month + INTERVAL '1' MONTH
                LEFT JOIN 
                    foc_augmented l2m 
                ON 
                    l1m.customer_id = l2m.customer_id 
                    AND l1m.calendar_month = l2m.calendar_month + INTERVAL '1' MONTH
                """,
            ),
            base.TransformView(
                view_name="mom_retention_status",
                jinja_template="""
                WITH last_6_months AS (
                    SELECT
                        customer_id,
                        calendar_month,
                        is_active,
                        ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY calendar_month DESC) AS rn
                    FROM
                        l2m_augmented
                )

                ,active_count AS (
                    SELECT
                        customer_id,
                        COUNT(*) AS active_months
                    FROM
                        last_6_months
                    WHERE
                        is_active = 'true'
                        AND rn <= 6
                    GROUP BY
                        customer_id
                )

                SELECT
                    curr.*,
                    CASE
                        WHEN COALESCE(ac.active_months, 0) >= 2 THEN 1
                        ELSE 0
                    END AS is_stable,
                    CONCAT(CONCAT(prev.retention_status, '->'), curr.retention_status) AS mom_retention_status
                FROM 
                    l2m_augmented curr 
                LEFT JOIN 
                    l2m_augmented prev 
                ON 
                    curr.calendar_month = prev.calendar_month + INTERVAL '1' MONTH
                    AND curr.customer_id = prev.customer_id
                LEFT JOIN
                    active_count ac
                ON
                    curr.customer_id = ac.customer_id
                """
            )
        ),
        
    )
    output_config = base.OutputConfig(
        base_path = versioned_parquet_tables_masked.DataWarehouse(env).NINJA_MART_VN_RETENTION,
        measurement_datetime=measurement_datetime,
        partition_by=("calendar_month",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.measurement_datetime,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()