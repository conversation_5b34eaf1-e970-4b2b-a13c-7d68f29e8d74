import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.DRIVER_SCAN_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.DRIVER_SCAN_EVENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- inbound_scans type 3=TRUCK, 4=VAN_FROM_NINJAVAN

                SELECT scans.id AS scan_id
                       , scans.order_id
                       , scans.result AS result_id
                       , CASE
                {%- for id, name in id_to_result.items() %}
                             WHEN scans.result = '{{ id }}' THEN '{{ name }}'
                {%- endfor %}
                         END as result
                       , scans.route_id
                       , cast(route.driver_id as bigint) AS route_driver_id
                       , cast(route.hub_id as bigint) AS route_hub_id
                       , from_utc_timestamp(scans.created_at, '{{ local_timezone }}') AS event_datetime
                       , date_format(scans.created_at, 'yyyy-MM') AS created_month
                FROM inbound_scans AS scans
                LEFT JOIN route_logs AS route 
                    ON scans.route_id = route.legacy_id
                    AND route.system_id = '{{ system_id }}'
                WHERE scans.deleted_at IS NULL
                  AND scans.order_id IS NOT NULL
                  AND scans.type in (3, 4)
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id" : system_id,
                    "id_to_result": {
                        "S1": "RECEIVED_BY_DRIVER_FROM_CUSTOMER",
                        "S2": "RECEIVED_BY_SORTING_HUB",
                        "S2A": "RECEIVED_BY_SORTING_HUB_SAMEDAY",
                        "S3": "RECEIVED_BY_VAN",
                        "S4": "RECEIVED_BY_TRUCK",
                        "S5": "RECEIVED_BY_SORTING_HUB_CMI",
                        "E1": "TRACKING_ID_NOT_FOUND",
                        "E2": "INCORRECT_ROUTE",
                        "E3": "DUPLICATE_SCAN",
                        "E4": "ORDER_COMPLETED",
                        "E5": "OTHERS",
                    },
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVER_SCAN_EVENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
