import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_DIMENSIONS_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_DIMENSIONS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="order_dim_estimates",
                jinja_template="""
                with base as (

                    select
                        system_id
                        , order_id
                        , shipper_id
                        , max_by(raw_weight, scan_datetime)
                            filter(where raw_weight != 0) as latest_weight
                        , max_by(raw_length, scan_datetime)
                            filter(where raw_length != 0 and raw_width != 0 and raw_height != 0) as latest_length
                        , max_by(raw_width, scan_datetime)
                            filter(where raw_length != 0 and raw_width != 0 and raw_height != 0) as latest_width
                        , max_by(raw_height, scan_datetime)
                            filter(where raw_length != 0 and raw_width != 0 and raw_height != 0) as latest_height
                    from dim_weight_scans_base
                    group by {{ range(1, 4) | join(',') }}

                ),
                order_vol as (

                    select
                        *
                        , latest_length * latest_width * latest_height as recorded_volume
                    from base

                ),
                order_vol_weight as (

                    select
                        *
                        , case 
                            when system_id = 'my' and shipper_id in (45012, 42206)
                                then latest_weight * 6000 / 1000000
                            when system_id = 'my' 
                                then latest_weight * 5000 / 1000000
                            when system_id = 'ph' 
                                then latest_weight * 3500 / 1000000
                            else latest_weight * 6000 / 1000000
                            end as weight_vol
                        , case 
                            when system_id = 'my' and shipper_id in (45012, 42206) 
                                then recorded_volume / 6000
                            when system_id = 'my' 
                                then recorded_volume / 5000
                            when system_id = 'ph' 
                                then recorded_volume / 3500
                            else recorded_volume / 6000
                            end as vol_weight
                        , recorded_volume / 1000000 as recorded_volume_cbm
                    from order_vol
                ),
                final as (

                    select
                        *
                        , coalesce(latest_weight, vol_weight, 0.1) as estimated_weight
                        , coalesce(recorded_volume_cbm, weight_vol, 0.0006) as estimated_volume
                    from order_vol_weight

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    orders.id as order_id
                    , order_dim_estimates.estimated_weight
                    , order_dim_estimates.estimated_volume
                    , date_format(orders.created_at, 'yyyy-MM') as created_month
                from orders
                inner join order_dim_estimates on
                    orders.id = order_dim_estimates.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DIMENSIONS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
