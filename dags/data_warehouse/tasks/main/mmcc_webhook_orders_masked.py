import sys

from pyspark.sql import SparkSession

from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from datetime import timedelta
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED
        ),
    ),
    system_ids=(SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    latest_partition = "/measurement_datetime=latest/"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK,
                view_name="mmcc_webhook",
                input_range=lookback_ranges.input,
            ),

        ),
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_PARCELS_ENRICHED + latest_partition,
                            view_name="xb_parcels_enriched",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_EVENTS_ENRICHED + latest_partition,
                            view_name="xb_events_enriched",
                            input_range=lookback_ranges.input,
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="holm_orders",
                jinja_template="""

                with base as (
                    -- holm = Handed Over To Last Mile , lha = Linehaul Arrived
                    select
                        IF(xb_parcels_enriched.shipper_id in (9438676, 9438639), 'Lazada XB MM', 'Tiktok XB MMCC') as shipper
                        , xb_parcels_enriched.shipper_id
                        , xb_parcels_enriched.ref_tracking_id as big_bag_no
                        , xb_parcels_enriched.total_parcel_items
                        , xb_parcels_enriched.parcel_type
                        , CASE 
                            WHEN xb_parcels_enriched.shipper_id IN (9438676, 9438639) 
                                THEN date(xb_events_enriched.linehaul_arrived_datetime)
                            ELSE date(xb_events_enriched.handed_over_to_last_mile_datetime) 
                        END as handed_over_to_last_mile_date
                        , CASE 
                            WHEN xb_parcels_enriched.shipper_id IN (9438676, 9438639) 
                                THEN date_format(xb_events_enriched.linehaul_arrived_datetime, 'yyyy-MM')
                            ELSE date_format(xb_events_enriched.handed_over_to_last_mile_datetime, 'yyyy-MM')
                        END as holm_month
                        , lower(xb_events_enriched.parcel_destination_country) as country
                        , concat(xb_parcels_enriched.from_country, ' - ' , xb_parcels_enriched.to_country) as od_pair
                    from xb_parcels_enriched
                    join xb_events_enriched on
                        xb_parcels_enriched.parcel_id = xb_events_enriched.parcel_id
                )

                select * from base
                where
                    -- to identify MMCC bags in parcels table
                    parcel_type in ('B2C Bag', 'B2B Bag') 
                    and shipper_id in (6257769, 6257706, 6257783, 6257752, 6257669, 9438676, 9438639)
                    -- to get parcels that holm-ed/lha-ed from previous month
                    and handed_over_to_last_mile_date 
                        >= date_trunc('month', date_sub(date_trunc('month', date_sub('{{measurement_datetime}}',1)), 1))
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),

            base.TransformView(
                view_name="base",
                jinja_template="""

                 select
                    mmcc_webhook.uuid
                    , mmcc_webhook.shipper
                    , holm_orders.country as system_id
                    , holm_orders.od_pair 
                    , from_utc_timestamp(
                        mmcc_webhook.webhook_sent_time_utc, {{ get_local_timezone }}
                    ) as webhook_sent_time
                    , mmcc_webhook.log_type
                    , mmcc_webhook.big_bag_no
                    , holm_orders.total_parcel_items
                    , mmcc_webhook.master_awb
                    , mmcc_webhook.webhook_status
                    , mmcc_webhook.webhook_state
                    , mmcc_webhook.global_shipper_id
                    , mmcc_webhook.response_code
                    , mmcc_webhook.success_flag
                    , from_utc_timestamp(
                        mmcc_webhook.event_time_utc, {{ get_local_timezone }}
                    ) as event_time
                    , mmcc_webhook.rts_flag
                    , mmcc_webhook.webhook_request
                    , mmcc_webhook.webhook_generated_detail
                    , mmcc_webhook.created_date
                    , mmcc_webhook.created_month
                    , case
                        when mmcc_webhook.webhook_status like '%Handed Over to Origin Facility%'
                            then 1
                        when mmcc_webhook.webhook_status like '%Handed Over to Linehaul%'
                            then 2
                        when (mmcc_webhook.webhook_status = 'Big Bag Export' and mmcc_webhook.webhook_state = 'Customs Cleared')
                            OR mmcc_webhook.webhook_status = 'Export Cleared'
                            then 6
                        when (mmcc_webhook.webhook_status = 'Big Bag Import' AND mmcc_webhook.webhook_state = 'Customs Cleared')
                            OR mmcc_webhook.webhook_status = 'Customs Cleared'
                            then 7
                        when mmcc_webhook.webhook_status like '%Linehaul Departed%'
                            then 8
                        when mmcc_webhook.webhook_status like '%Linehaul Arrived%' and mmcc_webhook.webhook_state is null
                            then 9
                        when mmcc_webhook.webhook_status like '%Handed Over to Last Mile%'
                            then 10
                        when mmcc_webhook.webhook_status like '%Linehaul Scheduled%' and mmcc_webhook.webhook_state is null
                            then 44
                        when mmcc_webhook.webhook_status = 'Big Bag Export' and mmcc_webhook.webhook_state = 'Clearance Started'
                            then 48
                        when mmcc_webhook.webhook_status = 'Big Bag Import' and mmcc_webhook.webhook_state = 'Clearance Started'
                            then 49
                        else null
                    end as status_code
                    , case
                        when mmcc_webhook.webhook_status like '%Handed Over to Origin Facility%'
                            then 'Handed Over to Origin Facility'
                        when mmcc_webhook.webhook_status like '%Handed Over to Linehaul%'
                            then 'Handed Over to Linehaul'
                        when (mmcc_webhook.webhook_status = 'Big Bag Export' and mmcc_webhook.webhook_state = 'Customs Cleared')
                            OR mmcc_webhook.webhook_status = 'Export Cleared'
                            then 'Export Cleared'
                        when (mmcc_webhook.webhook_status = 'Big Bag Import' AND mmcc_webhook.webhook_state = 'Customs Cleared')
                            OR mmcc_webhook.webhook_status = 'Customs Cleared'
                            then 'Customs Cleared'
                        when mmcc_webhook.webhook_status like '%Linehaul Departed%'
                            then 'Linehaul Departed'
                        when mmcc_webhook.webhook_status like '%Linehaul Arrived%' and mmcc_webhook.webhook_state is null
                            then 'Linehaul Arrived'
                        when mmcc_webhook.webhook_status like '%Handed Over to Last Mile%'
                            then 'Handed Over to Last Mile'    
                        when mmcc_webhook.webhook_status like '%Linehaul Scheduled%' and mmcc_webhook.webhook_state is null
                            then 'Linehaul Scheduled'
                        when mmcc_webhook.webhook_status = 'Big Bag Export' and mmcc_webhook.webhook_state = 'Clearance Started'
                            then 'Export Started'
                        when mmcc_webhook.webhook_status = 'Big Bag Import' and mmcc_webhook.webhook_state = 'Clearance Started'
                            then 'Import Started'
                        else null
                    end as status
                    , holm_orders.handed_over_to_last_mile_date
                    , holm_orders.holm_month
                from mmcc_webhook
                join holm_orders on
                    mmcc_webhook.big_bag_no = holm_orders.big_bag_no

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("holm_orders.country"),
                }
            ),

            base.TransformView(
                view_name="final",
                jinja_template="""

                select *
                from base
                where status_code is not null

                """,
            ),
        ),
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK_ORDERS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "holm_month")
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(input_args.env,
                                  input_args.last_measurement_datetime,
                                  input_args.measurement_datetime,
                                  input_args.enable_full_run,
                                  )
    run(spark, task_config)
    spark.stop()