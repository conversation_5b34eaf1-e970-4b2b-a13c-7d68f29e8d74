import sys

from pyspark.sql import SparkSession

from common.spark import util
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from datetime import datetime as dt
from pyspark.sql import functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import explode, col, lit, collect_set, array, array_join, sum as spark_sum
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID
import pandas as pd

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FieldSalesDAG.Task.MATCHED_SHIPPER_CONTACT_EMAIL_MASKED + ".py",
    task_name=data_warehouse.FieldSalesDAG.Task.MATCHED_SHIPPER_CONTACT_EMAIL_MASKED,
    system_ids=(
        SystemID.VN,  
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 48, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)
        
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shippers",
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPER_ADDRESSES,
                view_name="shipper_addresses",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    end_month = (measurement_datetime + relativedelta(months=-1)).strftime('%Y-%m')
    start_month = (measurement_datetime + relativedelta(months=-4)).strftime('%Y-%m')

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="overlap_shippers_base",
                jinja_template="""
                    with base as (

                        -- FS Shippers

                        select 
                           se.id as shipper_id
                           , se.shipper_name 
                           , se.sales_channel
                           , se.parent_name_coalesce
                           , s.contact as shippers_contact
                           , s.email as shippers_email
                           , vol.created_month as shipper_completion_month
                           -- , concat(address1, ', ', address2) as shipper_address
                           , sum(vol.total_orders) as total_orders
                           , '{{ system_id }}' as system_id
                        from shippers_enriched se 
                        left join shippers s 
                            on se.id = s.id
                        -- left join shipper_addresses address 
                            -- on se.id = address.shipper_id 
                            -- and address.verified = 1
                        left join shipper_completion_vol_monthly vol 
                            on vol.shipper_id = se.id 
                        where se.sales_channel in ('Field Sales')
                        and vol.created_month between date('{{ start_month }}') + interval 1 month and date('{{ end_month }}') 
                        group by 1,2,3,4,5,6,7

                        -- take parent account only 
                        -- and se.sf_acc_id = se.sf_parent_acc_id_coalesce

                        union
                        -- Partnership shippers
                        select 
                           se.id as shipper_id
                           , se.shipper_name 
                           , se.sales_channel
                           , se.parent_name_coalesce
                           , s.contact as shippers_contact
                           , case 
                               when s.email in ('e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
                                               'fbea2d71c3e6fc1368042e0a4daa42dee7e0f2b627978366ee6961611c1c38a3',
                                               'fbea2d71c3e6fc1368042e0a4daa42dee7e0f2b627978366ee6961611c1c38a3',
                                               'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
                                               'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
                                               'd1105cab7afc925c1ef1400c18633957464e46d9fb74d2a4d42564ad1bd8dd3f',

                                               'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
                                               '5feceb66ffc86f38d952786c6d696c79c2dbc239dd4e91b46729d73a27fb57e9',
                                               'f0d4cda6e888c6c3e51ebd923b2601f9d124e3921083155ca5d1947e2eb7c360',
                                               '612d0db4d3137adc636cc0a7085cd59111582065c5160180925f68808f1fdbbe',
                                               '96d96497847c351063d0bf4b5d18bb28c3d490f2ab872a25e4ab4b49031f17d7',
                                               '349b9fef425edf1345564e0d8ad0c2c1485afe64202a159231456d5e53419787',
                                               'bafe8d50ef22850a5bcdb81240e1436bf57375e0415f3a1ca7f3edaff5d721f3',
                                               '0541dda7a726eeb4e42d0614d342493fccfb45f2d0e762d5741f29e1f2bfed36',
                                               '98469dd788db8f8d19744b9a6bda12d358a546ad7dcdb97d93aa811e9a3e7af6',
                                               '4ac41214dbbf94382a644f5934bef0e98187c6c3b42238696fb0f56a6df1e63e',
                                               '8b24bb2843221f954ef0cccf8e8d567e61ef5f806bffd6425d13c6250932abcc',

                                               '1d6ec765ed8eb62d99878e96f494f0fb232d85671c3ca5e72aad1f3197749d65')  then null
                            else s.email
                            end as shippers_email
                           , vol.created_month as shipper_completion_month
                           -- , concat(address1, ', ', address2) as shipper_address
                           , sum(vol.total_orders) as total_orders
                           , '{{ system_id }}' as system_id
                        from shippers_enriched se 
                        left join shippers s 
                            on se.id = s.id
                        -- left join shipper_addresses address 
                            -- on se.id = address.shipper_id 
                            -- and address.verified = 1
                        left join shipper_completion_vol_monthly vol 
                            on vol.shipper_id = se.id 
                        where se.sales_channel = 'Partnerships'
                        and vol.created_month between date('{{ start_month }}') + interval 1 month and date('{{ end_month }}')
                        group by 1,2,3,4,5,6,7
                        ),

                        interim as (
                            SELECT * FROM BASE
                            WHERE total_orders > 0
                        ),
                        
                        final as (
                            SELECT 
                            *,
                            ROW_NUMBER() OVER (
                                PARTITION BY shipper_id 
                                ORDER BY 
                                        total_orders DESC
                                ) AS rn
                            FROM base
                        )
                            
                        select * from final 
                        where rn = 1
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "start_month": start_month,
                    "end_month": end_month,
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MATCHED_SHIPPER_CONTACT_EMAIL,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)

def null_email_dataframe(spark_df):
    """
    Get the distinct shippers_contact that have NULL shippers_email column 

    Input:
            spark dataframe that contains shipper details 
    Output:
            spark dataframe with shippers_contact column 
    """
    null_email = spark_df.filter((spark_df.shippers_email.isNull()) &
                                 (spark_df.shippers_contact.isNotNull())) \
                         .select('shippers_contact') \
                         .distinct()
    return null_email
    
def null_contact_dataframe(spark_df):
    """
    Get the distinct shippers_email that have NULL shippers_contact column

    Input:
            spark dataframe that contains shipper details 
    Output:
            spark dataframe with shippers_email column 
    """
    null_contact = spark_df.filter((spark_df.shippers_contact.isNull()) &
                                   (spark_df.shippers_email.isNotNull())) \
                           .select('shippers_email') \
                           .distinct()
    return null_contact

def process_spark_dataframe(spark_df):
    """
    Create a pandas dataframe that contains a set contact numbers that are associated with a unique shipper email

    Input:
            spark dataframe that contains shipper details
    Output:
            pandas dataframe with the following columns:
                "email"
                "system_id"
                "sales_channel"
                "contact"
    """
    spark_df_filtered = spark_df.withColumnRenamed("shippers_email", "email") \
                                .withColumnRenamed("shippers_contact", "contact") \
                                .withColumnRenamed("system_id", "system_id") \
                                .withColumnRenamed("sales_channel", "sales_channel")
    contact_to_emails = spark_df_filtered.groupBy("contact", "system_id", "sales_channel").agg(collect_set("email").alias("emails"))
    email_to_contacts = spark_df_filtered.groupBy("email", "system_id", "sales_channel").agg(collect_set("contact").alias("contacts"))
    exploded_contacts = contact_to_emails.withColumn("email", explode("emails"))
    exploded_emails = email_to_contacts.withColumn("dummy_contact", explode("contacts"))
    spark_joined_df = exploded_contacts.join(exploded_emails, "email").select(exploded_contacts.contact.alias("contact"), 
                                                                              exploded_contacts.email.alias("email"), 
                                                                              exploded_contacts.system_id.alias("system_id"), 
                                                                              exploded_contacts.sales_channel.alias("sales_channel"))
    contact_email_pairs = spark_joined_df.groupBy("contact", "system_id", "sales_channel").agg(collect_set("email").alias("emails"))
    email_contact_pairs = spark_joined_df.groupBy("email", "system_id", "sales_channel").agg(collect_set("contact").alias("contacts"))
    pandas_df = email_contact_pairs.toPandas()
    return pandas_df

def define_system_id(pandas_df):
    """
    Get system_id of shippers  

    Input:
            pandas dataframe that contains a system_id column
    Output:
            system_id (str)
    """
    array_system_id = pandas_df["system_id"].unique()
    system_id = array_system_id[0]
    return system_id

def update_dict(existing_dict, new_key, new_value):
    """
    Get all the linked emails and contact numbers of a shipper

    Input:
            dictionary with distinct emails and associated contact number of a shipper
    Output:
            dictionary with all the linked emails and contact numbers of a shipper
    """
    # Convert the dictionary keys to tuples if they are not already tuples
    new_dict = dict()
    for key, value in existing_dict.items():
        if not isinstance(key, tuple):
            key = (key,)
        new_dict[key] = value
    
    for keys, values in new_dict.items():
        # Check for intersection between the 2 sets
        if new_value & values: 
            # If the value exists, append the new key to the existing keys
            updated_keys = keys + (new_key,)
            # If the value exists, merge the sets
            new_dict[updated_keys] = new_value | values
            # Remove the old key from the dictionary
            del new_dict[keys]
            break
    else:
        # If the value does not exist, add the new key-value pair
        new_dict[(new_key,)] = new_value
    
    return new_dict

def get_null_email_rows(spark_df, spark_df_merged):
    """
    Get the new rows of shippers whose shippers_contact do not exist in the linked emails and contact numbers dataframe

    Input:
            spark dataframe with distinct shippers_contact with NULL shippers_email
            spark dataframe that contains linked emails and contact numbers of a shipper
    Output:
            spark dataframe with NULL in the emails column and contact values that do not exist in the linked emails and
            contact numbers dataframe
    """
    null_email = null_email_dataframe(spark_df)
    exploded_contact_final = spark_df_merged.withColumn('exploded_contacts', explode(col('contacts')))
    new_contacts = null_email.join(exploded_contact_final, null_email.shippers_contact == exploded_contact_final.exploded_contacts, 'left_anti')
    new_contact_rows = new_contacts.withColumn('emails', lit(None)) \
                                   .withColumn('contacts', array(col('shippers_contact')))
    new_contact_rows = new_contact_rows.select('emails', 'contacts')
    return new_contact_rows
    
def get_null_contact_rows(spark_df, spark_df_merged):
    """
    Get the new rows of shippers whose buyers_email do not exist in the linked emails and contact numbers dataframe

    Input:
            spark dataframe with distinct shippers_email with NULL shippers_contact
            spark dataframe that contains linked emails and contact numbers of a shipper
    Output:
            spark dataframe with NULL in the contact column and email values that do not exist in the linked emails and
            contact numbers dataframe 
    """
    null_contact = null_contact_dataframe(spark_df)
    exploded_email_final = spark_df_merged.withColumn('exploded_emails', explode(col('emails')))
    new_emails = null_contact.join(exploded_email_final, null_contact.shippers_email == exploded_email_final.exploded_emails, 'left_anti')
    new_email_rows = new_emails.withColumn('emails', array(col('shippers_email'))) \
                               .withColumn('contacts', lit(None))
    new_email_rows = new_email_rows.select('emails', 'contacts')
    return new_email_rows

def add_seller_id_and_system_id(pandas_df, spark_df):
    """
    Input:
            pandas dataframe to define system_id and spark dataframe to add the new columns into 
    Output:
            spark dataframe with additional columns named
                "seller_id"
                "system_id"
    """
    system_id = define_system_id(pandas_df)
    spark_df = spark_df.withColumn('seller_id', F.row_number().over(Window.orderBy(F.monotonically_increasing_id())))
    spark_df = spark_df.withColumn('system_id', lit(system_id))
    return spark_df

def pre_processing(interim_spark_df, spark_df):
    # Explode the contacts column
    exploded_interim_spark_df = interim_spark_df.withColumn('contact', explode(interim_spark_df.contacts))
    
    # Select relevant columns
    exploded_interim_spark_df = exploded_interim_spark_df.select('seller_id', 'contact')
    
    # Join the dataframes
    joined_df = spark_df.join(exploded_interim_spark_df, spark_df.shippers_contact == exploded_interim_spark_df.contact, 'inner')
    
    # Aggregate data
    agg_df = (joined_df
                  .groupBy("seller_id")
                  .agg(
                      array_join(collect_set("shipper_name"), ",").alias("shipper_names"),
                      array_join(collect_set("shipper_id"), ",").alias("shipper_ids"),
                      array_join(collect_set("sales_channel"), ",").alias("sales_channels"),
                      array_join(collect_set("parent_name_coalesce"), ",").alias("parent_names"),
                      spark_sum("total_orders").alias("total_orders")
                  ))
    
    # Join Aggregated data with the original joined dataframe
    final_agg_df = (joined_df
                .join(agg_df, on="seller_id", how="inner")
                .select(joined_df["*"], 
                        agg_df["shipper_names"], 
                        agg_df["shipper_ids"], 
                        agg_df["sales_channels"], 
                        agg_df["parent_names"]))
    return final_agg_df

def final_processing(interim_spark_df,pandas_df):
    
    # Select relevant columns
    final_spark_processing = interim_spark_df.select('shipper_id', 'shipper_name', 'sales_channel', 
                                                 'parent_name_coalesce', 'shippers_contact',
                                                 'shippers_email', 'total_orders', 'shipper_names', 
                                                 'parent_names', 'sales_channels', 'shipper_ids')
    # final Aggregate data
    final_spark_df = (final_spark_processing
                      .groupBy('shipper_id','shipper_name', 'sales_channel', 
                               'parent_name_coalesce', 'shippers_contact',
                               'shippers_email', 'total_orders')
                      .agg(
                          array_join(collect_set("shipper_names"), ",").alias("shipper_names"),
                          array_join(collect_set("parent_names"), ",").alias("parent_names"),
                          array_join(collect_set("sales_channels"), ",").alias("sales_channels"),
                          array_join(collect_set("shipper_ids"), ",").alias("shipper_ids")
                      ))
    
    # reassign seller_id
    final_spark_df = add_seller_id_and_system_id(pandas_df, final_spark_df)
    
    return final_spark_df
    
def run(spark, config):
    base.load_data(spark, config.input)
    spark_df = base.transform_data(spark, config.transform) 
    pandas_df = process_spark_dataframe(spark_df)
    pandas_df["contacts"] = pandas_df["contacts"].apply(set)
    email_contact_dict = pandas_df.set_index('email')['contacts'].to_dict()
    existing_dict = dict()
    for email, contact in email_contact_dict.items():
        existing_dict = update_dict(existing_dict, email, contact)
    dict_with_lists = {key: list(value) for key, value in existing_dict.items()}
    interim_pandas_df = pd.DataFrame(dict_with_lists.items(), columns=['emails', 'contacts'])
    interim_pandas_df['emails'] = interim_pandas_df['emails'].apply(list) 
    spark_df_merged = spark.createDataFrame(interim_pandas_df) 
    new_contact_rows = get_null_email_rows(spark_df, spark_df_merged) 
    new_email_rows = get_null_contact_rows(spark_df, spark_df_merged)
    interim_spark_df = spark_df_merged.union(new_contact_rows).union(new_email_rows)
    interim_spark_df = add_seller_id_and_system_id(pandas_df, interim_spark_df)
    interim_spark_df = pre_processing(interim_spark_df,spark_df)
    final_spark_df = final_processing(interim_spark_df,pandas_df)
    base.write_data(final_spark_df, config.output, spark)
    return final_spark_df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()