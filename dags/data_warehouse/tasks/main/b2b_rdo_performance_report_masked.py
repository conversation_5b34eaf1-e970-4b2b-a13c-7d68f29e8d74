import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.B2B_RDO_PERFORMANCE_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.B2B_RDO_PERFORMANCE_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.B2B_BUNDLE_ORDERS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID
            , task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).B2B_BUNDLE_ORDERS,
                view_name="b2b_bundle_orders",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.StationProdGL(input_env, is_masked).RDO_SCANS,
                view_name="rdo_scans",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                -- In the event of fragmented bundle delivery, take the first delivery success datetime
                -- and dest hub of a bundle to compute RDO performance
                select
                    bundle_tracking_id
                    , rdo_required_flag
                    , min_by(delivery_success_datetime,delivery_success_datetime) as delivery_success_datetime
                    , min_by(dest_hub_id,delivery_success_datetime) as dest_hub_id
                    , created_month
                    , system_id
                from b2b_bundle_orders
                where
                    -- Filter for bundles that require RDO scan
                    rdo_required_flag = 1 
                    -- RDO scans only required for b2b bundles with completed orders
                    and granular_status = 'Completed'
                group by 1,2,5,6

                """,
            ),
            base.TransformView(
                view_name="base_with_rdo",
                jinja_template="""

                select
                    base.bundle_tracking_id
                    , base.delivery_success_datetime 
                    , base.dest_hub_id
                    , hubs_enriched.region as dest_hub_region
                    , hubs_enriched.name as dest_hub_name
                    , base.rdo_required_flag
                    , if(rdo_scans.created_at is not null,1,0) as rdo_scan_flag
                    , rdo_scans.route_id as rdo_scan_route_id
                    , from_utc_timestamp(rdo_scans.created_at, '{{ local_timezone }}') as rdo_scan_datetime
                    , base.system_id
                    , base.created_month
                from base
                left join hubs_enriched
                    on base.dest_hub_id = hubs_enriched.id
                    and base.system_id = hubs_enriched.system_id
                left join rdo_scans
                    on base.bundle_tracking_id = rdo_scans.mps_tracking_number
                    and base.system_id = rdo_scans.system_id
                where
                    -- RDO scans started from 26th Aug 2024
                    date(base.delivery_success_datetime) >= date('2024-08-26')
                    -- Remove deleted RDO scans
                    and rdo_scans.deleted_at is null

                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                },
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""

                select
                    bundle_tracking_id
                    , delivery_success_datetime
                    , dest_hub_id
                    , dest_hub_region
                    , dest_hub_name
                    , rdo_required_flag
                    , rdo_scan_flag
                    , rdo_scan_route_id
                    , rdo_scan_datetime
                    , case
                        when rdo_scan_datetime is not null then case
                            when date(delivery_success_datetime) = date(rdo_scan_datetime) then 1
                            else 0
                        end
                        else null
                    end as same_day_rdo_scan_flag
                    , datediff(
                        date(rdo_scan_datetime)
                        , date(delivery_success_datetime)
                    ) as delivery_to_rdo_scan_days
                    , system_id
                    , created_month
                from base_with_rdo

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).B2B_RDO_PERFORMANCE_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
     )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()