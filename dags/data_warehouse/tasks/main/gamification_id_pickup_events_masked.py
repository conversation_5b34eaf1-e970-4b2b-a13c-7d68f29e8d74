import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_PICKUP_EVENTS_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_ID_PICKUP_EVENTS_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.AddressingProdGL.DAG_ID, task_id=delta_tables.AddressingProdGL.Task.ZONES
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_ID, task_id=delta_tables.CoreProdGL.Task_ID.ORDERS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_ID, task_id=delta_tables.CoreProdGL.Task_ID.TRANSACTIONS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.EventsProdGL.DAG_ID, task_id=delta_tables.EventsProdGL.Task.ORDER_EVENTS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.RouteProdGL.DAG_ID, task_id=delta_tables.RouteProdGL.Task.ROUTE_LOGS
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_DAILY_ADJUSTED_MASKED
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime.subtract(hours=18), measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- order_events type 25=DRIVER_PICKUP_SCAN, type 39=PICKUP_SUCCESS

                select
                    id
                    , order_id
                    , system_id
                    , type
                    , get_json_object(data, '$.route_id') as route_id
                    , get_json_object(data, '$.waypoint_id') as waypoint_id
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS created_at
                    , date_format(from_utc_timestamp(created_at, {{ get_local_timezone }}), 'yyyy-MM') AS created_month
                from order_events
                where
                    type in (25,39)
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_ID_PICKUP_EVENTS,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
