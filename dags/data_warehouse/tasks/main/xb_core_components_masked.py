import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_CORE_COMPONENTS_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_CORE_COMPONENTS_MASKED,
    depends_on=(data_warehouse.CrossBorderDAG.Task.XB_PARCEL_ORIGIN_DESTINATION_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_PARCEL_ORIGIN_DESTINATION,
                view_name="xb_parcel_origin_destination",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),  
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="origin_core",
                jinja_template="""
                select
                    base.parcel_id
                    , base.tracking_id
                    , base.parcel_origin_country
                    , base.parcel_destination_country
                    , origin.order_id
                    , origin.creation_datetime
                    , origin.granular_status
                    , shippers.id as shipper_id
                    , shippers.shipper_name
                    , shippers.parent_name_coalesce
                    , shippers.sales_person_code
                    , shippers.sales_channel
                    , origin.shipper_reference_number
                    , origin.from_billing_zone
                    , origin.to_billing_zone
                    , origin.order_type
                    , origin.dp_dropoff_datetime
                    , origin.pickup_datetime
                    , origin.pickup_hub_id
                    , origin.inbound_datetime
                    , origin.inbound_hub_id
                    , origin.nv_length
                    , origin.nv_width
                    , origin.nv_height
                    , origin.nv_weight
                    , origin.parcel_size
                    , origin.original_weight
                    , origin.cod_value
                    , base.created_month
                    , base.system_id
                from xb_parcel_origin_destination as base
                left join order_milestones as origin
                    on lower(base.parcel_origin_country) = origin.system_id
                    and base.tracking_id = origin.tracking_id
                left join shipper_attributes as shippers
                    on lower(base.parcel_origin_country) = shippers.system_id
                    and origin.shipper_id = shippers.id
                """,
            ),
            base.TransformView(
                view_name="dest_core_base",
                jinja_template="""
                select
                    base.parcel_id
                    , destination.order_id
                    , destination.creation_datetime
                    , destination.granular_status
                    , shippers.id as shipper_id
                    , shippers.shipper_name
                    , shippers.parent_name_coalesce
                    , shippers.sales_person_code
                    , shippers.sales_channel
                    , destination.shipper_reference_number
                    , if(destination.cod_value > 0, 1, 0) as cod_flag
                    , destination.from_billing_zone
                    , destination.to_billing_zone
                    , destination.to_l1_name
                    , destination.to_l2_name
                    , destination.to_l3_name
                    , destination.inbound_datetime
                    , destination.inbound_hub_id
                    , destination.dest_hub_id
                    , CASE
                        WHEN least(destination.pickup_datetime, destination.inbound_datetime) > destination.rts_trigger_datetime
                        THEN NULL
                        WHEN destination.inbound_datetime IS NULL THEN destination.pickup_hub_id
                        WHEN destination.pickup_datetime IS NULL THEN destination.inbound_hub_id
                        WHEN destination.pickup_datetime <= destination.inbound_datetime THEN destination.pickup_hub_id
                        WHEN destination.inbound_datetime < destination.pickup_datetime THEN destination.inbound_hub_id
                    END as origin_hub_id
                    , destination.first_valid_delivery_attempt_datetime
                    , destination.first_valid_delivery_failure_reason_id
                    , destination.second_valid_delivery_attempt_datetime
                    , destination.second_valid_delivery_failure_reason_id
                    , destination.third_valid_delivery_attempt_datetime
                    , destination.third_valid_delivery_failure_reason_id
                    , destination.last_valid_delivery_attempt_datetime
                    , destination.last_valid_delivery_failure_reason_id
                    , destination.delivery_attempts
                    , destination.rts_flag
                    , destination.rts_trigger_datetime
                    , destination.rts_reason
                    , destination.rts_dest_hub_id
                    , destination.first_valid_rts_attempt_datetime
                    , destination.rts_attempts
                    , destination.delivery_success_datetime
                    , destination.nv_length
                    , destination.nv_width
                    , destination.nv_height
                    , destination.nv_weight
                    , destination.parcel_size
                    , destination.original_weight
                    , destination.cod_value
                    , destination.comments
                    , base.system_id
                from xb_parcel_origin_destination as base
                left join order_milestones as destination
                    on base.system_id = destination.system_id
                    and base.tracking_id = destination.tracking_id
                left join shipper_attributes as shippers
                    on base.system_id = shippers.system_id
                    and destination.shipper_id = shippers.id
                """,
            ),
            base.TransformView(
                view_name="dest_core",
                jinja_template="""
                select
                    base.*
                {%- for type in ('origin', 'dest', 'rts_dest') %}
                {%- for col in ('name', 'region', 'sla_region', 'address_city') %}
                    , {{ type }}_hub.{{ col }} as {{ type }}_hub_{{ col }}
                {%- endfor %}
                {%- endfor %}
                {%- for rank in ('first', 'second', 'third', 'last') %}
                    , {{ rank }}_fail.description as {{ rank }}_valid_delivery_failure_reason
                {%- endfor %}
                from dest_core_base as base
                {%- for type in ('origin', 'dest', 'rts_dest') %}
                left join hubs_enriched as {{ type }}_hub
                    on base.system_id = {{ type }}_hub.system_id
                    and base.{{ type }}_hub_id = {{ type }}_hub.id
                {%- endfor %}
                {%- for rank in ('first', 'second', 'third', 'last') %}
                left join failure_reasons as {{ rank }}_fail
                    on base.{{ rank }}_valid_delivery_failure_reason_id = {{ rank }}_fail.id
                {%- endfor %}
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    origin.parcel_id
                    , origin.tracking_id
                    , origin.parcel_origin_country
                    , origin.parcel_destination_country
                    , case
                        when origin.order_id is not null and destination.order_id is not null then 1
                        when origin.order_id is not null and destination.order_id is null then 2
                        when origin.order_id is null and destination.order_id is not null then 3
                        when origin.order_id is null and destination.order_id is null then 4
                    end as flow_type
                    , origin.order_id as origin_order_id
                    , destination.order_id as destination_order_id
                    , coalesce(origin.original_weight, destination.original_weight) as shipper_submitted_weight
                    , coalesce(origin.nv_weight, destination.nv_weight) as nv_weight
                    , coalesce(
                        destination.nv_length * destination.nv_width * destination.nv_height 
                        / case
                            when origin.parcel_destination_country in ('SG', 'MY', 'TH') then 5000
                            when origin.parcel_destination_country in ('PH') then 3500
                            when origin.parcel_destination_country in ('VN', 'ID') then 6000
                        end
                        , origin.nv_length * origin.nv_width * origin.nv_height 
                        / case
                            when origin.parcel_origin_country in ('SG', 'MY', 'TH') then 5000
                            when origin.parcel_origin_country in ('PH') then 3500
                            when origin.parcel_origin_country in ('VN', 'ID') then 6000
                        end
                    ) as volumetric_weight
                    {%- for value in ('nv_length', 'nv_width', 'nv_height', 'parcel_size') %}
                    , coalesce(origin.{{ value }}, destination.{{ value }}) as {{ value }}
                    {%- endfor %}
                    , coalesce(destination.cod_value, origin.cod_value) as cod_value
                    , origin.creation_datetime as origin_creation_datetime
                    , origin.granular_status as origin_granular_status
                    , origin.shipper_id as origin_shipper_id
                    , origin.shipper_name as origin_shipper_name
                    , origin.parent_name_coalesce as origin_parent_name_coalesce
                    , origin.sales_person_code as origin_sales_person_code
                    , origin.sales_channel as origin_sales_channel
                    , origin.shipper_reference_number as origin_shipper_reference_number
                    , origin.dp_dropoff_datetime as origin_dp_dropoff_datetime
                    , origin.pickup_datetime as origin_pickup_datetime
                    , origin.inbound_datetime as origin_inbound_datetime
                    , origin.inbound_hub_id as origin_inbound_hub_id
                    , origin.from_billing_zone as origin_from_billing_zone
                    , origin.to_billing_zone as origin_to_billing_zone
                    , origin.order_type as origin_order_type
                    , destination.creation_datetime as destination_creation_datetime
                    , destination.granular_status as destination_granular_status
                    , destination.shipper_id as destination_shipper_id
                    , destination.shipper_name as destination_shipper_name
                    , destination.parent_name_coalesce as destination_parent_name_coalesce
                    , destination.sales_person_code as destination_sales_person_code
                    , destination.sales_channel as destination_sales_channel
                    , destination.shipper_reference_number as destination_shipper_reference_number
                    , destination.cod_flag as destination_cod_flag
                    , destination.from_billing_zone as destination_from_billing_zone
                    , destination.to_billing_zone as destination_to_billing_zone
                    , destination.to_l1_name as destination_to_l1_name
                    , destination.to_l2_name as destination_to_l2_name
                    , destination.to_l3_name as destination_to_l3_name
                    , destination.inbound_hub_id as destination_inbound_hub_id
                    , destination.inbound_datetime as destination_inbound_datetime
                    , destination.origin_hub_id as destination_origin_hub_id
                    , destination.origin_hub_name as destination_origin_hub_name
                    , destination.origin_hub_region as destination_origin_hub_region
                    , destination.origin_hub_sla_region as destination_origin_hub_sla_region
                    , destination.origin_hub_address_city as destination_origin_hub_address_city
                    , destination.dest_hub_id as destination_dest_hub_id
                    , destination.dest_hub_name as destination_dest_hub_name
                    , destination.dest_hub_region as destination_dest_hub_region
                    , destination.dest_hub_sla_region as destination_dest_hub_sla_region
                    , destination.dest_hub_address_city as destination_dest_hub_address_city
                    , destination.first_valid_delivery_attempt_datetime
                    , destination.first_valid_delivery_failure_reason
                    , destination.second_valid_delivery_attempt_datetime
                    , destination.second_valid_delivery_failure_reason
                    , destination.third_valid_delivery_attempt_datetime
                    , destination.third_valid_delivery_failure_reason
                    , destination.last_valid_delivery_attempt_datetime
                    , destination.last_valid_delivery_failure_reason
                    , destination.delivery_attempts
                    , destination.rts_flag as destination_rts_flag
                    , destination.rts_trigger_datetime
                    , destination.rts_reason
                    , destination.rts_dest_hub_id
                    , destination.rts_dest_hub_name
                    , destination.rts_dest_hub_region
                    , destination.rts_dest_hub_sla_region
                    , destination.rts_dest_hub_address_city
                    , destination.first_valid_rts_attempt_datetime
                    , destination.rts_attempts
                    , destination.delivery_success_datetime
                    , destination.comments as destination_comments
                    , origin.system_id
                    , origin.created_month
                from origin_core as origin
                left join dest_core as destination
                    on origin.parcel_id = destination.parcel_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_CORE_COMPONENTS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
