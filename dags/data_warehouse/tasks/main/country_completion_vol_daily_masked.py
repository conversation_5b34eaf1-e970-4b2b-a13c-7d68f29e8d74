import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderAggregatesDAG.Task.COUNTRY_COMPLETION_VOL_DAILY_MASKED + ".py",
    task_name=data_warehouse.OrderAggregatesDAG.Task.COUNTRY_COMPLETION_VOL_DAILY_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
    post_execution_check=True,
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="max_completion_date",
                jinja_template="""
                select
                  max(
                    date(from_utc_timestamp(service_end_time, '{{ get_local_timezone }}'))
                  ) as max_completion_flag_date
                from
                  transactions
                where
                      type = 'DD'
                      and status = 'Success'
                      and deleted_at is null
                      -- remove successful transactions with no service_end_time
                      and service_end_time is not null
                      -- remove dirty future-dated transactions
                      -- and transactions completed past midnight yesterday
                      and date(service_end_time) < date(
                            from_utc_timestamp('{{ measurement_datetime_utc }}', '{{ get_local_timezone }}')
                        )
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                    select
                     '{{ system_id }}' as country
                      , date(from_utc_timestamp(service_end_time, '{{ get_local_timezone }}')) as completion_date
                      , order_id
                      , row_number() over (partition by order_id order by service_end_time desc) as rank
                    from transactions
                    where
                      type = 'DD'
                      and status = 'Success'
                      and deleted_at is null

                      -- remove successful transactions with no service_end_time
                      and service_end_time is not null

                      -- remove dirty future-dated transactions
                      -- and transactions completed past midnight yesterday
                      and date(service_end_time) < date(
                        from_utc_timestamp('{{ measurement_datetime_utc }}', '{{ get_local_timezone }}')
                      )
                )
                select
                  country
                  , completion_date
                  , date_format(completion_date, 'yyyy-MM') AS created_month
                  , max_date.max_completion_flag_date
                  , if(base.completion_date = max_date.max_completion_flag_date, 1, 0) as is_max_completion_date
                  , if(
                    date_format(date(base.completion_date), 'yyyy-MM')
                        = date_format(max_date.max_completion_flag_date, 'yyyy-MM')
                    , 1
                    , 0
                  ) as is_max_completion_month
                  , count(*) as total_orders
                from base
                left join max_completion_date as max_date on true
                where rank = 1
                group by {{ range(1, 6) | join(',') }}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COUNTRY_COMPLETION_VOL_DAILY,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
