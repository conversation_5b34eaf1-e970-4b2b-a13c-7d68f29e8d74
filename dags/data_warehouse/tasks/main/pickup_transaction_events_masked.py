import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.PICKUP_TRANSACTION_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.PICKUP_TRANSACTION_EVENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
            base.InputTable(path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES, view_name="zones"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="last_failure_reason",
                jinja_template="""
                SELECT transaction_id
                       , failure_reason_id
                       , failure_reason_code_id
                FROM
                  ( SELECT transaction_id
                           , failure_reason_id
                           , failure_reason_code_id
                           , row_number() OVER (PARTITION BY transaction_id
                                                ORDER BY created_at) AS rank
                   FROM transaction_failure_reason
                   WHERE deleted_at IS NULL )
                WHERE rank = 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT transactions.id AS transaction_id
                       , transactions.order_id
                       , transactions.status
                       , transactions.postcode
                       , failure_reason.failure_reason_id
                       , from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}') AS event_datetime
                       , transactions.route_id
                       , cast(route.hub_id as bigint) AS route_hub_id
                       , cast(route.driver_id as bigint) AS route_driver_id
                       , route_zone.name AS route_zone
                       , transactions.waypoint_id
                       , waypoints.latitude as waypoint_latitude
                       , waypoints.longitude as waypoint_longitude
                       , waypoint_zone.name AS waypoint_zone
                       , waypoint_zone.hub_id AS waypoint_zone_hub_id
                       , from_utc_timestamp(transactions.created_at, '{{ local_timezone }}') AS creation_datetime
                       , date_format(transactions.created_at, 'yyyy-MM') AS created_month
                FROM transactions
                LEFT JOIN last_failure_reason AS failure_reason
                    ON transactions.id = failure_reason.transaction_id
                LEFT JOIN route_logs AS route 
                    ON route.legacy_id = transactions.route_id
                    AND route.system_id = '{{ system_id }}'
                LEFT JOIN waypoints ON waypoints.legacy_id = transactions.waypoint_id
                    and waypoints.system_id = '{{ system_id }}'    
                LEFT JOIN zones AS waypoint_zone ON waypoints.routing_zone_id = waypoint_zone.legacy_zone_id
                AND lower(waypoint_zone.system_id) = '{{ system_id }}'
                LEFT JOIN zones AS route_zone ON route.zone_id = route_zone.legacy_zone_id
                AND lower(route_zone.system_id) = '{{ system_id }}'
                WHERE transactions.type = 'PP'
                    AND transactions.deleted_at IS NULL
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PICKUP_TRANSACTION_EVENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()