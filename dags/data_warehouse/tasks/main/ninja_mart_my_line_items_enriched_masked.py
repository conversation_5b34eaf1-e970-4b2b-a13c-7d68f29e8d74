import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.NinjaMartDAG.Task.NINJA_MART_MY_LINE_ITEMS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.NinjaMartDAG.Task.NINJA_MART_MY_LINE_ITEMS_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    country = 'my'
    is_masked = True
    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).CREDIT_NOTE_DETAILS,
                view_name="credit_notes_details",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).CREDIT_NOTES,
                view_name="credit_notes",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).INVOICES,
                view_name="invoices",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).INVOICE_BUNDLE_DETAILS,
                view_name="invoice_bundle_details",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).INVOICE_DETAILS,
                view_name="invoice_details",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).LOVS,
                view_name="lovs",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).OUTLETS,
                view_name="outlets",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).PRINCIPALS,
                view_name="principals",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).PRINTOUTS,
                view_name="printouts",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).SKUS,
                view_name="skus",
            ),
            base.InputTable(
                path=delta_tables.Amast(country, input_env, is_masked).VANS,
                view_name="vans",
            ),
        ),

    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="invoice_details_enriched",
                jinja_template="""
                with
                    invoice_details_outlet as (
                        select 
                            invoice_details.id
                            , invoice_details.invoice_id
                            , invoice_details.sku_id
                            , invoices.outlet_id
                            , invoices.van_id
                            , invoice_details.uom_id
                            , invoices.status as invoice_status
                            , invoices.voided as is_voided
                            , date(invoices.date) as invoice_date
                            , date(date_trunc('week', invoices.date)) as invoice_week
                            , date(date_trunc('month', invoices.date)) as invoice_month
                            , date(date_trunc('quarter', invoices.date)) as invoice_quarter
                            , invoice_details.quantity
                            , invoice_details.price
                            , invoice_details.discount
                            , invoice_details.tax
                            , invoice_details.line_total
                            , invoices.rmo_amt
                            , invoices.site_id
                        from invoice_details
                        left join invoices
                            on invoice_details.invoice_id = invoices.id
                    ),
            
                    sku_info_invoices as (
                        select
                            invoice_details_outlet.id
                            , invoice_details_outlet.invoice_id
                            , invoice_details_outlet.sku_id
                            , invoice_details_outlet.outlet_id
                            , outlets.name as outlet_name
                            , invoice_details_outlet.site_id
            
                            -- start of code block to be replaced later when table：sites is available. to be replaced by sites.state
                            , case when invoice_details_outlet.invoice_id like 'IV%'  then substring(invoice_details_outlet.invoice_id,9,2)
                                   when invoice_details_outlet.invoice_id like 'CN%'  then substring(invoice_details_outlet.invoice_id,9,2)
                                   when invoice_details_outlet.invoice_id like 'PI%'  then substring(invoice_details_outlet.invoice_id,9,2)
                                else substring(invoice_details_outlet.invoice_id,8,2) end state
                            -- end of code block to be replaced later when table：sites is available. to be replaced by sites.state
            
                            , invoice_details_outlet.van_id
                            , invoice_details_outlet.uom_id
                            , invoice_details_outlet.invoice_status
                            , invoice_details_outlet.is_voided
                            , invoice_details_outlet.invoice_date
                            , invoice_details_outlet.invoice_week
                            , invoice_details_outlet.invoice_month
                            , invoice_details_outlet.invoice_quarter
                            , skus.name as sku_name
                            , skus.category as sku_category
            
                            -- subcategory except for BAT
                            , case 
                                when lower(skus.id) in ('empdÍksid', 'dhwarisan', 's dksbyd', 'bhf20p6', 's dlibyd', 'dh spectra', ' b dhswitc', 'b dhzest', 'b dhboost', 'b dhmix')
                                then "Premium"
                                when lower(skus.id) in ('rothrili', 'rothriff', 'rothrmit', 'rothripur', 'rothrichar', 'rothribst', 'rothfujibau', 'rothpowerbau', 'rothfuji', 'rothpower')
                                then "VFM"
                                when lower(skus.id) in ('nikepsf20', 'nikepsl20', 'psm', 'pspurple20')
                                then "AP"
                                when lower(skus.id) in ('kyored', 'kyoriginal', 'kyosilver', 'kyoswitch')
                                then "Sub VFM"
                                when lower(skus.id) in ('bison50')
                                then "RYO"
                              else skus.sub_category
                              end as sku_sub_category
            
                            , case when principals.short_code = 'BATM' then 'BAT' else principals.short_code end as principal
                            , skus.active as is_sku_active
                            , invoice_details_outlet.quantity
                            , invoice_details_outlet.price
                            , invoice_details_outlet.discount
                            , invoice_details_outlet.tax
                            , invoice_details_outlet.line_total
                            , invoice_details_outlet.rmo_amt
                            , printouts.id as printout_id
                            , printouts.created_by salesperson
                            , date_format(invoice_date, 'yyyy-MM') created_month
                            , 'my' as system_id
                        from invoice_details_outlet
                        left join skus 
                            on invoice_details_outlet.sku_id = skus.id
                        left join outlets
                            on invoice_details_outlet.outlet_id = outlets.id
                        left join printouts 
                            on invoice_details_outlet.invoice_id = printouts.invoice_id
            
                        -- sites not available now, uncomment this and replace the state colomn with sites.state once available
                        -- left join sites on invoice_details_outlet.site_id = sites.id
            
                        left join principals
                            on skus.principal_id = principals.id
                        where invoice_details_outlet.is_voided = False
                        order by outlet_id, invoice_date desc
                    )
            
                select 
                    *
                from 
                    sku_info_invoices
            """,
            ),
            base.TransformView(
                view_name="invoice_bundle_details_enriched",
                jinja_template="""
                with
                    invoice_bundle_details_outlet as (
                        select 
                            invoice_bundle_details.id
                            , invoice_bundle_details.invoice_id
                            , invoice_bundle_details.sku_id
                            , invoices.outlet_id
                            , invoices.van_id
                            , invoice_bundle_details.uom_id
                            , invoices.status as invoice_status
                            , invoices.voided as is_voided
                            , date(invoices.date) as invoice_date
                            , date(date_trunc('week', invoices.date)) as invoice_week
                            , date(date_trunc('month', invoices.date)) as invoice_month
                            , date(date_trunc('quarter', invoices.date)) as invoice_quarter
                            , invoice_bundle_details.quantity
                            , invoice_bundle_details.price
                            , invoice_bundle_details.discount
                            , invoice_bundle_details.tax
                            , invoice_bundle_details.line_total
                            , invoices.rmo_amt
                            , invoices.site_id
                        from invoice_bundle_details
                        left join invoices
                            on invoice_bundle_details.invoice_id = invoices.id
                    ),
            
                    sku_info_invoice_bundles as (
                        select
                            invoice_bundle_details_outlet.id
                            , invoice_bundle_details_outlet.invoice_id
                            , invoice_bundle_details_outlet.sku_id
                            , invoice_bundle_details_outlet.outlet_id
                            , outlets.name as outlet_name
                            , invoice_bundle_details_outlet.site_id
            
                            -- start of code block to be replaced later when table：sites is available. to be replaced by sites.state
                            , case when invoice_bundle_details_outlet.invoice_id like 'IV%'  then substring(invoice_bundle_details_outlet.invoice_id,9,2)
                                   when invoice_bundle_details_outlet.invoice_id like 'CN%'  then substring(invoice_bundle_details_outlet.invoice_id,9,2)
                                   when invoice_bundle_details_outlet.invoice_id like 'PI%'  then substring(invoice_bundle_details_outlet.invoice_id,9,2)
                                else substring(invoice_bundle_details_outlet.invoice_id,8,2) end state
                            -- end of code block to be replaced later when table：sites is available. to be replaced by sites.state
            
                            , invoice_bundle_details_outlet.van_id
                            , invoice_bundle_details_outlet.uom_id
                            , invoice_bundle_details_outlet.invoice_status
                            , invoice_bundle_details_outlet.is_voided
                            , invoice_bundle_details_outlet.invoice_date
                            , invoice_bundle_details_outlet.invoice_week
                            , invoice_bundle_details_outlet.invoice_month
                            , invoice_bundle_details_outlet.invoice_quarter
                            , skus.name as sku_name
                            , skus.category as sku_category
            
                            -- subcategory except for BAT
                            , case 
                                when lower(skus.id) in ('empdÍksid', 'dhwarisan', 's dksbyd', 'bhf20p6', 's dlibyd', 'dh spectra', ' b dhswitc', 'b dhzest', 'b dhboost', 'b dhmix')
                                then "Premium"
                                when lower(skus.id) in ('rothrili', 'rothriff', 'rothrmit', 'rothripur', 'rothrichar', 'rothribst', 'rothfujibau', 'rothpowerbau', 'rothfuji', 'rothpower')
                                then "VFM"
                                when lower(skus.id) in ('nikepsf20', 'nikepsl20', 'psm', 'pspurple20')
                                then "AP"
                                when lower(skus.id) in ('kyored', 'kyoriginal', 'kyosilver', 'kyoswitch')
                                then "Sub VFM"
                                when lower(skus.id) in ('bison50')
                                then "RYO"
                              else skus.sub_category
                              end as sku_sub_category
            
                            -- renaming of BATM -> BAT
                            , case when principals.short_code = 'BATM' then 'BAT' else principals.short_code end as principal
                            , skus.active as is_sku_active
                            , invoice_bundle_details_outlet.quantity
                            , invoice_bundle_details_outlet.price
                            , invoice_bundle_details_outlet.discount
                            , invoice_bundle_details_outlet.tax
                            , invoice_bundle_details_outlet.line_total
                            , invoice_bundle_details_outlet.rmo_amt
                            , printouts.id as printout_id
                            , printouts.created_by salesperson
                        from invoice_bundle_details_outlet
                        left join skus 
                            on invoice_bundle_details_outlet.sku_id = skus.id
                        left join outlets
                            on invoice_bundle_details_outlet.outlet_id = outlets.id
                        left join printouts 
                            on invoice_bundle_details_outlet.invoice_id = printouts.invoice_id
            
                        -- sites not available now, uncomment this and replace the state colomn with sites.state once available
                        -- left join sites on invoice_details_outlet.site_id = sites.id
            
                        left join principals
                            on skus.principal_id = principals.id
            
                        where invoice_bundle_details_outlet.is_voided = False
                        order by outlet_id, invoice_date desc
                    )
            
                select 
                    *
                from 
                    sku_info_invoice_bundles
                """,
            ),
            base.TransformView(
                view_name="credit_note_details_enriched",
                jinja_template="""
    with
        credit_note_outlet as (
            select 
                credit_notes_details.id
                , credit_notes_details.credit_note_id
                , credit_notes_details.sku_id
                , credit_notes.outlet_id
                , credit_notes.van_id
                , credit_notes_details.uom_id
                , credit_notes.status as credit_note_status
                , credit_notes.voided as is_voided
                , date(credit_notes.date) as credit_note_date
                , date(date_trunc('week', credit_notes.date)) as credit_note_week
                , date(date_trunc('month', credit_notes.date)) as credit_note_month
                , date(date_trunc('quarter', credit_notes.date)) as credit_note_quarter
                , credit_notes_details.quantity
                , credit_notes_details.price
                , credit_notes_details.tax
                , credit_notes_details.line_total
            from credit_notes_details
            left join credit_notes
                on credit_notes_details.credit_note_id = credit_notes.id
        ),

        sku_info_credit_notes as (
            select
                credit_note_outlet.id
                , credit_note_outlet.credit_note_id
                , credit_note_outlet.sku_id
                , credit_note_outlet.outlet_id
                , outlets.name as outlet_name
                , outlets.site_id

                -- start of code block to be replaced later when table：sites is available. to be replaced by sites.state
                , case when credit_note_outlet.credit_note_id like 'IV%'  then substring(credit_note_outlet.credit_note_id,9,2)
                       when credit_note_outlet.credit_note_id like 'CN%'  then substring(credit_note_outlet.credit_note_id,9,2)
                       when credit_note_outlet.credit_note_id like 'PI%'  then substring(credit_note_outlet.credit_note_id,9,2)
                    else substring(credit_note_outlet.credit_note_id,8,2) end state
                -- end of code block to be replaced later when table：sites is available. to be replaced by sites.state

                , credit_note_outlet.van_id
                , credit_note_outlet.uom_id
                , credit_note_outlet.credit_note_status
                , credit_note_outlet.is_voided
                , credit_note_outlet.credit_note_date
                , credit_note_outlet.credit_note_week
                , credit_note_outlet.credit_note_month
                , credit_note_outlet.credit_note_quarter
                , skus.name as sku_name
                , skus.category as sku_category
                , case 
                    when lower(skus.id) in ('empdksid', 'dhwarisan', 's dksbyd', 'bhf20p6', 's dlibyd', 'dh spectra', ' b dhswitc', 'b dhzest', 'b dhboost', 'b dhmix')
                    then "Premium"
                    when lower(skus.id) in ('rothrili', 'rothriff', 'rothrmit', 'rothripur', 'rothrichar', 'rothribst', 'rothfujibau', 'rothpowerbau', 'rothfuji', 'rothpower')
                    then "VFM"
                    when lower(skus.id) in ('nikepsf20', 'nikepsl20', 'psm', 'pspurple20')
                    then "AP"
                    when lower(skus.id) in ('kyored', 'kyoriginal', 'kyosilver', 'kyoswitch')
                    then "Sub VFM"
                    when lower(skus.id) in ('bison50')
                    then "RYO"
                    else skus.sub_category
                  end as sku_sub_category
                , case when principals.short_code = 'BATM' then 'BAT' else principals.short_code end as principal
                , skus.active as is_sku_active
                , credit_note_outlet.quantity
                , credit_note_outlet.price
                , credit_note_outlet.tax
                , credit_note_outlet.line_total
                , printouts.id as printout_id
                , printouts.created_by salesperson
            from credit_note_outlet
            left join skus 
                on credit_note_outlet.sku_id = skus.id
            left join outlets
                on credit_note_outlet.outlet_id = outlets.id
            left join printouts 
                on credit_note_outlet.credit_note_id = printouts.credit_note_id

            -- sites not available now, uncomment this and replace the state colomn with sites.state once available
            -- left join sites on invoice_details_outlet.site_id = sites.id

            left join principals
                on skus.principal_id = principals.id
            where credit_note_outlet.is_voided = False
            order by outlet_id, credit_note_date desc
        )

        select *
        from sku_info_credit_notes
            """,
            ),

            base.TransformView(
                view_name="unioned_table",
                jinja_template="""
                select 
                    'my_invoice_details'                        AS data_source
                    , invoice_id                                AS order_number
                    , sku_id                                    AS sku_code 
                    , sku_name                                  AS sku_name
                    , cast(null as string)                      AS barcode  -- placeholder column          
                    , van_id                                    AS van_id   -- salesperson id 
                    , salesperson                               AS salesperson_name
                    , site_id                                   AS site_id  -- id of the warehouse
                    , site_id                                   AS warehouse_name  -- to be changed later
                    , state                                     AS shipping_province -- province/state of the customer
                    , outlet_id                                 AS customer_id     --
                    , outlet_id                                 AS outlet_id       -- redundant column (same as outlet_id) so this the table structure fits in another framework  
                    , outlet_name                               AS customer_name
                    , if(price=0, line_total, price*quantity)   AS GMV_local       -- subtotal before discount and after excluding VAT*
                    , line_total                                AS net_revenue_local -- subtotal after tax and discount
                    , line_total                                AS cash_collected  --post discount line_total
                    , cast(null as double)                      AS allocated_cashback  -- cashback rewards given on the line item
                    , discount                                  AS discount        
                    , rmo_amt                                   AS rmo_amt
                    , sku_category                              AS category_name   
                    , sku_sub_category                          AS subcategory_name
                    , principal                                 AS principal        -- umbrella term for brands
                    , uom_id                                    AS uom              -- unit of measure
                    , quantity                                  AS quantity         
                    , cast(null as double)                      AS unit_price_before_discount -- placeholder column
                    , tax                                       AS tax_rate         
                    , cast(null as date)                        AS created_date     -- placeholder column
                    , invoice_date                              AS completed_date   -- date of issuance of the invoice
                    , date_format(invoice_date, 'yyyy-MM')      AS completed_month
                    , date_format(invoice_date, 'yyyy-MM')      AS created_month
                    , 'my'                                      AS system_id
        
                from 
                    invoice_details_enriched
                union all
                select 
                    'my_invoice_bundle_details'                 AS data_source
                    , invoice_id                                AS order_number
                    , sku_id                                    AS sku_code
                    , sku_name                                  AS sku_name
                    , 'TO BE IDENTIFIED'                        AS barcode            
                    , van_id                                    AS van_id   
                    , salesperson                               AS salesperson_name
                    , site_id                                   AS site_id     
                    , site_id                                   AS warehouse_name   
                    , state                                     AS shipping_province
                    , outlet_id                                 AS customer_id
                    , outlet_id                                 AS outlet_id
                    , outlet_name                               AS customer_name
                    , if(price=0, line_total, price*quantity)   AS GMV_local 
                    , line_total                                AS net_revenue_local
                    , line_total                                AS cash_collected
                    , null                                      AS allocated_cashback
                    , discount                                  AS discount
                    , rmo_amt                                   AS rmo_amt
                    , sku_category                              AS category_name
                    , sku_sub_category                          AS subcategory_name
                    , principal                                 AS principal
                    , uom_id                                    AS uom
                    , quantity                                  AS quantity
                    , null                                      AS unit_price_before_discount
                    , tax                                       AS tax_rate
                    , null                                      AS created_date
                    , invoice_date                              AS completed_date   -- date of issuance of the invoice
                    , date_format(invoice_date, 'yyyy-MM')      AS completed_month
                    , date_format(invoice_date, 'yyyy-MM')      AS created_month
                    , 'my'                                      AS system_id
                from 
                    invoice_bundle_details_enriched
                union all
                select 
                    'my_credit_note'                            AS data_source
                    , credit_note_id                            AS order_number
                    , sku_id                                    AS sku_code
                    , sku_name                                  AS sku_name
                    , null                                      AS barcode
                    , van_id                                    AS van_id
                    , salesperson                               AS salesperson_name 
                    , site_id                                   AS site_id     
                    , site_id                                   AS warehouse_name   
                    , state                                     AS shipping_province
                    , outlet_id                                 AS customer_id
                    , outlet_id                                 AS outlet_id
                    , outlet_name                               AS customer_name
                    , 0.0 - abs(line_total)                     AS GMV_local 
                    , 0.0 - abs(line_total)                     AS net_revenue_local -- tax is 0.
                    , 0.0 - abs(line_total)                     AS cash_collected
                    , NULL                                      AS allocated_cashback
                    , 0.0                                       AS discount
                    , null                                      AS rmo_amt
                    , sku_category                              AS category_name
                    , sku_sub_category                          AS subcategory_name
                    , principal                                 AS principal
                    , uom_id                                    AS uom
                    , quantity                                  AS quantity
                    , null                                      AS unit_price_before_discount
                    , tax                                       AS tax_rate
                    , null                                      AS created_date
                    , credit_note_date                          AS completed_date   -- date of issuance of the credit_note
                    , date_format(credit_note_date, 'yyyy-MM')  AS completed_month
                    , date_format(credit_note_date, 'yyyy-MM')  AS created_month
                    , 'my'                                      AS system_id
                from 
                    credit_note_details_enriched
                """,
            ),
            base.TransformView(
                view_name="post_union_remapping",
                jinja_template="""
                select 
                    data_source
                    , order_number
                    , sku_code
                    , sku_name
                    , barcode
                    , van_id
                    , vans.name as salesperson_name 
                    , unioned_table.site_id     
                    , warehouse_name   
                    , shipping_province
                    , customer_id
                    , outlet_id
                    , customer_name
                    , GMV_local 
                    , net_revenue_local
                    , cash_collected
                    , allocated_cashback
                    , discount
                    , rmo_amt
                    , coalesce(lovs_a.display, category_name) category_name
                    , coalesce(lovs_b.display, subcategory_name) subcategory_name
                    , principal
                    , uom
                    , quantity
                    , unit_price_before_discount
                    , tax_rate
                    , created_date
                    , completed_date
                    , completed_month
                    , created_month
                    , system_id
                from 
                    unioned_table
                left join lovs lovs_a on 
                    unioned_table.category_name = lovs_a.id
                    and lovs_a.group_id = 'sku_categories'
                left join lovs lovs_b on 
                    unioned_table.subcategory_name = lovs_b.id
                    and lovs_b.group_id = 'sku_subcategories'
                left join vans on
                    unioned_table.van_id = vans.id
                """,
            ),
        ),

    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).NINJA_MART_MY_LINE_ITEMS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()

