import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.FIRST_MILE_ROUTING_METRICS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.FIRST_MILE_ROUTING_METRICS_ENRICHED_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RESERVATION_REMOVE_FROM_ROUTE_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RESERVATION_CHANGE_OF_ROUTE_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RESERVATION_ROUTED_EVENTS_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
                               ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVATION_ROUTED_EVENTS,
                view_name="reservation_routed_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVATION_REMOVE_FROM_ROUTE_EVENTS,
                view_name="reservation_remove_from_route_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).RESERVATION_CHANGE_OF_ROUTE_EVENTS,
                view_name="reservation_change_of_route_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="address_hub_assignment_cte",
                jinja_template="""
                with base as (
                select
                    address_id
                    , route_hub_id
                    , system_id
                    , count(*) job_count
                from reservations_enriched
                where 1=1
                    and route_hub_id is not null
                group by 1,2,3
                order by 1,2,3
                ),

                assignment_cte as (
                    select 
                        *
                        , row_number() over(partition by address_id, system_id order by job_count desc) rnk
                    from base
                )

                select
                    address_id
                    , route_hub_id as hub_id
                    , system_id
                    , job_count
                from assignment_cte
                where rnk = 1
                """,
            ),
            base.TransformView(
                view_name="add_to_route ",
                jinja_template="""
                select 
                    reservation_id
                    , route_id
                    , user_id
                    , user_name
                    , add_to_route_datetime as event_datetime
                    , pickup_type
                    , row_number() over(partition by reservation_id, system_id order by add_to_route_datetime asc) as rnk
                from reservation_routed_events
                """,
            ),

            base.TransformView(
                view_name="change_of_route",
                jinja_template="""
                select 
                    reservation_id
                    , route_id
                    , old_route_id
                    , user_id
                    , user_name
                    , event_datetime
                    , pickup_type
                    , row_number() over(partition by reservation_id, system_id order by event_datetime asc) as rnk
                from reservation_change_of_route_events
                """,
            ),

            base.TransformView(
                view_name="remove_from_route",
                jinja_template="""
                select 
                    reservation_id
                    , route_id
                    , user_id
                    , user_name
                    , event_datetime
                    , pickup_type
                    , row_number() over(partition by reservation_id, system_id order by event_datetime asc) as rnk
                from reservation_remove_from_route_events
                """,
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with merge as (
                    select
                        reservations_enriched.reservation_id
                        , reservations_enriched.creation_datetime
                        , cast(coalesce(reservations_enriched.route_hub_id, address_hub_assignment_cte.hub_id) as int) as assigned_hub_id
                        , hubs_enriched.name as assigned_hub_name
                        , hubs_enriched.region as assigned_hub_region
                        , if(reservations_enriched.creation_datetime < reservations_enriched.ready_datetime, 
                            reservations_enriched.ready_datetime,reservations_enriched.creation_datetime) as start_clock
                        , if(reservations_enriched.creation_datetime < reservations_enriched.ready_datetime, 
                            'ready datetime','creation datetime') as start_clock_reference
                        , reservations_enriched.attempt_datetime
                        , reservations_enriched.data_source
                        , reservations_enriched.system_id
                        , reservations_enriched.created_month
                        , count(distinct add_to_route.user_name) filter(where add_to_route.rnk = 1 
                            and (add_to_route.user_name = 'Route_service_v2 INTERNAL_SERVICES' or add_to_route.user_name = 'FIRST_MILE')) as auto_routing_flag
                        {%- for cte, description in cte_mapping.items() %}
                        {%- for name,number in rank_params.items() %}
                        , cast(min({{ cte }}.user_id) filter(where {{ cte }}.rnk = {{ number }}) as long) as {{ name }}_{{ description }}_user_id
                        , min({{ cte }}.event_datetime) filter(where {{ cte }}.rnk = {{ number }}) as {{ name }}_{{ description }}_datetime
                        {%- endfor %}
                        , cast(max({{ cte }}.user_id) as long) as last_{{ description }}_user_id
                        , max({{ cte }}.event_datetime) as last_{{ description }}_datetime

                        , cast(max(last_{{ cte }}_before_attempt.user_id) as long) as last_{{ description }}_before_attempt_user_id
                        , max(last_{{ cte }}_before_attempt.event_datetime) as last_{{ description }}_before_attempt_datetime
                        {%- endfor %}
                        , coalesce(count(last_add_to_route_before_attempt.event_datetime),0) + coalesce(count(last_change_of_route_before_attempt.event_datetime),0) number_of_assignments_before_attempt
                    from reservations_enriched
                        {%- for cte in cte_list %}
                    left join {{ cte }}
                        on reservations_enriched.reservation_id = {{cte}}.reservation_id
                        and ((reservations_enriched.data_source = 'Reservation' and {{ cte }}.pickup_type = 1)
                            or (reservations_enriched.data_source = 'Pickup Appointment Job' and {{ cte }}.pickup_type = 2))
                        {%- endfor %}
                        {%- for cte in cte_list %}
                    left join {{ cte }} as last_{{ cte }}_before_attempt
                        on reservations_enriched.reservation_id = last_{{ cte }}_before_attempt.reservation_id
                        and (last_{{ cte }}_before_attempt.event_datetime < reservations_enriched.attempt_datetime or reservations_enriched.attempt_datetime is null)
                        and ((reservations_enriched.data_source = 'Reservation' and last_{{ cte }}_before_attempt.pickup_type = 1)
                            or (reservations_enriched.data_source = 'Pickup Appointment Job' and last_{{ cte }}_before_attempt.pickup_type = 2))
                        {%- endfor %}
                    left join address_hub_assignment_cte
                        on reservations_enriched.address_id = address_hub_assignment_cte.address_id
                    left join hubs_enriched
                        on coalesce(reservations_enriched.route_hub_id, address_hub_assignment_cte.hub_id) = hubs_enriched.id
                   --     and hubs_enriched.system_id = address_hub_assignment_cte.system_id
                    group by {{range(1,12)|join(',')}}
                ),

                lead_time as (
                select 
                    *
                    , greatest(last_add_to_route_before_attempt_datetime,last_change_of_route_before_attempt_datetime) as last_assignment_event_datetime
                    , to_unix_timestamp(greatest(last_add_to_route_before_attempt_datetime,last_change_of_route_before_attempt_datetime)) - to_unix_timestamp(start_clock) as lead_time_seconds
                    , to_unix_timestamp(greatest(last_add_to_route_before_attempt_datetime,last_change_of_route_before_attempt_datetime)) - to_unix_timestamp(creation_datetime) as lead_time_seconds_from_creation
                from merge
                )

                select * from lead_time
                """,
                jinja_arguments={
                    'rank_params': {
                        'first': 1,
                        'second': 2,
                        'third': 3,
                    },
                    'cte_mapping': {
                        'add_to_route': 'add_to_route',
                        'change_of_route': 'change_of_route',
                        'remove_from_route': 'remove_from_route',
                    },
                    'cte_list': [
                        'add_to_route',
                        'change_of_route',
                        'remove_from_route'
                    ]
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIRST_MILE_ROUTING_METRICS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
