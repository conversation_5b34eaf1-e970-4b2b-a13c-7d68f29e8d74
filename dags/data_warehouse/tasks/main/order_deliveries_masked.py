import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_DELIVERIES_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_DELIVERIES_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DELIVERY_TRANSACTION_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS,
                view_name="delivery_transaction_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS,
                view_name="update_address_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="valid_transactions",
                jinja_template="""
                select
                    order_id
                    , status
                    , type
                    , event_datetime
                    , timeslot_start
                    , timeslot_end
                    , failure_reason_id
                    , route_hub_id
                    , route_driver_id
                from delivery_transaction_events
                where
                    valid_flag = 1
                """,
            ),
            base.TransformView(
                view_name="attempts",
                jinja_template="""
                select
                    order_id
                    , count_if(type = 'delivery') as delivery_attempts
                    , count_if(type = 'rts') as rts_attempts
                from valid_transactions
                group by 1
                """,
            ),
            base.TransformView(
                view_name="delivery_breakdowns",
                jinja_template="""
                with
                    ranked as (

                        select
                            *
                            , row_number() over(partition by order_id, type order by event_datetime) as order_type_rank
                            , row_number() over(
                                partition by order_id, type
                                order by event_datetime desc
                            ) as order_type_rank_desc
                            , row_number() over(
                                partition by order_id, status
                                order by event_datetime desc
                            ) as order_status_rank_desc
                        from valid_transactions

                    )
                    , final as (

                        select
                            orders.id as order_id
                            , attempts.delivery_attempts
                            , attempts.rts_attempts

                            {%- for rank in delivery_attempt_config["ranks"] %}
                            {%- set name_prefix = rank_config[rank]["name_prefix"] %}
                            {%- for ref, name_suffix in delivery_attempt_config["ref_column_to_name_suffix"] %}
                            , {{ name_prefix }}_delivery.{{ ref }}
                            as {{ name_prefix }}_valid_delivery_attempt_{{ name_suffix }}
                            {%- endfor %}
                            {%- endfor %}

                            {%- for rank in rts_attempt_config["ranks"] %}
                            {%- set name_prefix = rank_config[rank]["name_prefix"] %}
                            {%- for ref, name_suffix in rts_attempt_config["ref_column_to_name_suffix"] %}
                            , {{ name_prefix }}_rts.{{ ref }} as {{ name_prefix }}_valid_rts_attempt_{{ name_suffix }}
                            {%- endfor %}
                            {%- endfor %}

                            {%- for ref, name_suffix in delivery_success_columns %}
                            , delivery_success.{{ ref }} as delivery_success_{{ name_suffix }}
                            {%- endfor %}

                            , date_format(orders.created_at, 'yyyy-MM') as created_month
                        from attempts
                        {%- for rank in delivery_attempt_config["ranks"] %}
                        {%- set name_prefix = rank_config[rank]["name_prefix"] %}
                        {%- set definition = rank_config[rank]["definition"] %}
                        left join ranked as {{ name_prefix }}_delivery on
                            attempts.order_id = {{ name_prefix }}_delivery.order_id
                            and {{ name_prefix }}_delivery.type = 'delivery'
                            and {{ name_prefix }}_delivery.{{ definition }}
                        {%- endfor %}
                        {%- for rank in rts_attempt_config["ranks"] %}
                        {%- set name_prefix = rank_config[rank]["name_prefix"] %}
                        {%- set definition = rank_config[rank]["definition"] %}
                        left join ranked as {{ name_prefix }}_rts on
                            attempts.order_id = {{ name_prefix }}_rts.order_id
                            and {{ name_prefix }}_rts.type = 'rts'
                            and {{ name_prefix }}_rts.{{ definition }}
                        {%- endfor %}
                        left join ranked as delivery_success on
                            attempts.order_id = delivery_success.order_id
                            and delivery_success.status = 'Success'
                            and delivery_success.order_status_rank_desc = 1
                        inner join orders on
                            attempts.order_id = orders.id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "rank_config": {
                        1: {"definition": "order_type_rank = 1", "name_prefix": "first"},
                        2: {"definition": "order_type_rank = 2", "name_prefix": "second"},
                        3: {"definition": "order_type_rank = 3", "name_prefix": "third"},
                        -1: {"definition": "order_type_rank_desc = 1", "name_prefix": "last"},
                    },
                    "delivery_attempt_config": {
                        "ranks": (1, 2, 3, -1),
                        "ref_column_to_name_suffix": (
                            ("event_datetime", "datetime"),
                            ("timeslot_start", "timeslot_start"),
                            ("timeslot_end", "timeslot_end"),
                            ("failure_reason_id", "failure_reason_id"),
                            ("status", "status"),
                            ("route_hub_id", "hub_id"),
                        ),
                    },
                    "rts_attempt_config": {
                        "ranks": (1, 2, 3, -1),
                        "ref_column_to_name_suffix": (
                            ("event_datetime", "datetime"),
                            ("failure_reason_id", "failure_reason_id"),
                            ("status", "status"),
                            ("route_hub_id", "hub_id"),
                        ),
                    },
                    "delivery_success_columns": (
                        ("event_datetime", "datetime"),
                        ("route_hub_id", "hub_id"),
                        ("route_driver_id", "driver_id"),
                    ),
                },
            ),
            base.TransformView(
                view_name="first_postcode_change_after_first_attempt",
                jinja_template="""
                select
                    base.order_id
                    , min_by(events.order_event_id, events.event_datetime) update_address_event_order_event_id
                    , min_by(events.old_to_postcode, events.event_datetime)
                        as previous_to_postcode_before_update_address_events
                from delivery_breakdowns base
                left join update_address_events events
                    on base.order_id = events.order_id
                    and events.event_datetime > base.first_valid_delivery_attempt_datetime
                    and (events.old_to_postcode is not null or events.new_to_postcode is not null)
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.*
                    , if(events.update_address_event_order_event_id is not null, 1, 0) event_flag
                    ,  events.previous_to_postcode_before_update_address_events
                from delivery_breakdowns base
                left join first_postcode_change_after_first_attempt events
                    on base.order_id = events.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DELIVERIES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
