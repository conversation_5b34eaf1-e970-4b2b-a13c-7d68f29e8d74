import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.INVOICE_DISPUTE_TID_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.INVOICE_DISPUTE_TID_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=()),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.BillingProdGL(input_env, is_masked).INVOICE_DISPUTE_TIDS,
            view_name="invoice_dispute_tids"),
            base.InputTable(path=delta_tables.BillingProdGL(input_env, is_masked).INVOICE_DISPUTES,
            view_name="invoice_disputes"),
            base.InputTable(path=delta_tables.BillingProdGL(input_env, is_masked).INVOICE_DISPUTE_TYPES,
            view_name="invoice_dispute_types"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="invoice_dispute_tids",
                jinja_template="""
                select 
                    invoice_dispute_tids.id
                    , invoice_dispute_tids.invoice_dispute_id
                    , invoice_dispute_tids.tracking_id
                    , invoice_dispute_tids.order_id
                    , invoice_disputes.shipper_id
                    , from_utc_timestamp(invoice_disputes.dispute_filed_date,
                        {{ get_local_timezone }}) as dispute_filed_date
                    , date(date_trunc('week', from_utc_timestamp(invoice_disputes.dispute_filed_date,
                        {{ get_local_timezone }}))) as dispute_filed_week
                    , date(date_trunc('month', from_utc_timestamp(invoice_disputes.dispute_filed_date,
                        {{ get_local_timezone }}))) as dispute_filed_month
                    , from_utc_timestamp(invoice_dispute_tids.inbound_date,
                        {{ get_local_timezone }}) as inbound_datetime
                    , from_utc_timestamp(invoice_dispute_tids.complete_date,
                        {{ get_local_timezone }}) as complete_datetime
                    , if(invoice_disputes.closed_at is null, 0, 1) as is_case_closed
                    , invoice_disputes.status as case_status
                    , invoice_dispute_tids.status as tid_status
                    , invoice_dispute_tids.is_multiple
                    , invoice_dispute_tids.invoice_dispute_type_name
                    , invoice_dispute_tids.resolution_type
                    , invoice_dispute_tids.resolution_note
                    , invoice_dispute_tids.resolution
                    , get_json_object(invoice_dispute_tids.shipper_data, '$.proposed_weight') as proposed_weight
                    , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.billing_weight') as billing_weight
                    , if(identifier = 'weight', round((cast(get_json_object(invoice_dispute_tids.shipper_data,
                        '$.proposed_weight') as double) - cast(get_json_object(invoice_dispute_tids.pricing_breakdown,
                        '$.billing_weight') as double))*2, 0)/2, null) as delta
                    , invoice_dispute_types.identifier
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.delivery_fee.amount')
                        , null)
                    as revised_delivery_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.cod_fee.amount')
                        , null)
                    as revised_cod_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.rts_fee.amount')
                        , null)
                    as revised_rts_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.delivery_fee.amount')
                        , null)
                    as original_delivery_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.cod_fee.amount')
                        , null)
                    as original_cod_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name = 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.rts_fee.amount')
                        , null)
                    as original_rts_fee_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.delivery_fee.amount')
                        , null)
                    as revised_delivery_fee_non_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.cod_fee.amount')
                        , null)
                    as revised_cod_fee_non_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.rts_fee.amount')
                        , null)
                    as revised_rts_fee_non_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.delivery_fee.amount')
                        , null)
                    as original_delivery_fee_non_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.cod_fee.amount')
                        , null)
                    as original_cod_fee_non_weight
                    , if(invoice_dispute_tids.resolution = 2 and invoice_dispute_type_name != 'Billing Weight'
                        , get_json_object(invoice_dispute_tids.pricing_breakdown, '$.rts_fee.amount')
                        , null)
                    as original_rts_fee_non_weight
                    , lower(invoice_disputes.system_id) as system_id
                from invoice_dispute_tids
                left join invoice_disputes
                    on invoice_dispute_tids.invoice_dispute_id = invoice_disputes.id
                left join invoice_dispute_types
                    on invoice_dispute_tids.invoice_dispute_type_id = invoice_dispute_types.id
                where shipper_id not in (78482, 7559750, 72128)
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(invoice_disputes.system_id)")},
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).INVOICE_DISPUTE_TID_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=(),
        enable_csv=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("mapreduce.fileoutputcommitter.marksuccessfuljobs", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()