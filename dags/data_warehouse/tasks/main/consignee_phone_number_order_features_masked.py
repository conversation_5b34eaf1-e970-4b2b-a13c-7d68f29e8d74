import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES_MASKED,
    depends_on=(data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_ORDERS_BASE_MASKED,),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_BASE,
                view_name="lazada_orders_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="number_daily_order_count",
                jinja_template="""

                select
                    original_to_number
                    , created_date
                    , count(order_id) as order_count
                from lazada_orders_base
                group by 1,2                              

                """,
            ),
            base.TransformView(
                view_name="number_dates",
                jinja_template="""

                select
                    distinct original_to_number
                    , created_date
                from lazada_orders_base

                """,
            ),
            base.TransformView(
                view_name="number_l30d_window_features",
                jinja_template="""

                select
                    number_dates.original_to_number
                    , number_dates.created_date
                    , count(lazada_orders_base.order_id) as l30d_orders_created
                    , count_if(lazada_orders_base.granular_status = 'Completed') as l30d_orders_completed
                from number_dates
                left join lazada_orders_base on
                    number_dates.original_to_number = lazada_orders_base.original_to_number
                    and lazada_orders_base.created_date >= number_dates.created_date - interval '30' day
                    and lazada_orders_base.created_date < number_dates.created_date
                group by 1, 2  

                """,
            ),
            base.TransformView(
                view_name="number_l90d_window_features",
                jinja_template="""

                 select
                    number_dates.original_to_number
                    , number_dates.created_date
                    , count(lazada_orders_base.known_fraud_timestamp) as l90d_orders_intercepted
                    , count(lazada_orders_base.resolution_datetime) as l90d_orders_claimed
                from number_dates
                left join lazada_orders_base on
                    number_dates.original_to_number = lazada_orders_base.original_to_number
                    and lazada_orders_base.created_date >= number_dates.created_date - interval '90' day
                    and lazada_orders_base.created_date < number_dates.created_date
                group by 1, 2                      

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    lazada_orders_base.order_id
                    , lazada_orders_base.tracking_id
                    , lazada_orders_base.creation_datetime
                    , lazada_orders_base.created_date
                    , lazada_orders_base.granular_status
                    , lazada_orders_base.shipper_id
                    , lazada_orders_base.shipper_name
                    , lazada_orders_base.seller_id
                    , lazada_orders_base.seller_name
                    , lazada_orders_base.original_to_address1
                    , lazada_orders_base.original_to_address2
                    , lazada_orders_base.original_to_address
                    , lazada_orders_base.original_to_name
                    , lazada_orders_base.original_to_email
                    , lazada_orders_base.original_to_number
                    , lazada_orders_base.potential_claim_value
                    , number_daily_order_count.order_count as daily_order_count
                    , number_l30d_window_features.l30d_orders_created
                    , number_l30d_window_features.l30d_orders_completed
                    , number_l30d_window_features.l30d_orders_completed / number_l30d_window_features.l30d_orders_created as l30d_orders_completion_rate
                    , number_l90d_window_features.l90d_orders_intercepted
                    , number_l90d_window_features.l90d_orders_claimed
                    , lazada_orders_base.system_id
                    , lazada_orders_base.created_month
                from lazada_orders_base
                left join number_daily_order_count on 
                    lazada_orders_base.original_to_number = number_daily_order_count.original_to_number
                    and lazada_orders_base.created_date = number_daily_order_count.created_date
                left join number_l30d_window_features on 
                    lazada_orders_base.original_to_number = number_l30d_window_features.original_to_number
                    and lazada_orders_base.created_date = number_l30d_window_features.created_date    
                left join number_l90d_window_features on 
                    lazada_orders_base.original_to_number = number_l90d_window_features.original_to_number
                    and lazada_orders_base.created_date = number_l90d_window_features.created_date                

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()