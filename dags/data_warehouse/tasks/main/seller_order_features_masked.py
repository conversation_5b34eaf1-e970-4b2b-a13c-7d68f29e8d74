import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.SELLER_ORDER_FEATURES_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.SELLER_ORDER_FEATURES_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.LAZADA_INTERCEPTED_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_INTERCEPTED_ORDERS,
                view_name="lazada_intercepted_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="seller_milestones",
                jinja_template="""

                    select
                        orders_enriched.shipper_id
                        , coalesce(orders_enriched.seller_id, 'missing') as seller_id
                        , date(min(orders_enriched.creation_datetime)) as first_order_creation_date
                    from orders_enriched
                    inner join shipper_attributes
                        on orders_enriched.shipper_id = shipper_attributes.id
                    where
                        -- Process Lazada non-lazmall orders only
                        shipper_attributes.parent_id_coalesce = 341107
                        and orders_enriched.lazmall_flag = 0
                    group by 1,2

                """,
            ),
            base.TransformView(
                view_name="orders_filtered",
                jinja_template="""

                    select
                        order_milestones.shipper_id
                        , shipper_attributes.shipper_name
                        , coalesce(order_milestones.seller_id, 'missing') as seller_id
                        , coalesce(order_milestones.seller_name, 'missing') as seller_name
                        , shipper_attributes.onboarded_date
                        , order_milestones.order_id
                        , order_milestones.creation_datetime
                        , date(order_milestones.creation_datetime) as creation_date
                        , order_milestones.cod_value
                        , order_milestones.insurance_value
                        , if(
                            greatest(order_milestones.insurance_value,order_milestones.cod_value) >= 5000000
                            , 5000000
                            , greatest(order_milestones.insurance_value,order_milestones.cod_value)
                        ) as potential_claim_value
                        , order_milestones.granular_status
                        , order_milestones.system_id
                        , order_milestones.created_month
                    from order_milestones
                    inner join shipper_attributes
                        on order_milestones.shipper_id = shipper_attributes.id
                    where
                        -- Process Lazada non-lazmall orders only
                        shipper_attributes.parent_id_coalesce = 341107
                        and order_milestones.lazmall_flag = 0

                """,
            ),
            base.TransformView(
                view_name="seller_creation_vol_daily",
                jinja_template="""

                    select
                        shipper_id
                        , seller_id
                        , creation_date
                        , count(order_id) as daily_order_count
                    from orders_filtered 
                    group by 1,2,3

                """,
            ),
            base.TransformView(
                view_name="orders_claimed",
                jinja_template="""

                    select
                        order_id
                        , min(resolution_datetime) as resolution_datetime
                    from pets_tickets_enriched
                    where
                        status = 'RESOLVED'
                        and type = 'SLA BREACH'
                        and (outcome like '%NV LIABLE%' or outcome = 'RESUME DELIVERY')
                    group by 1

                """,
            ),
            base.TransformView(
                view_name="orders_base",
                jinja_template="""

                    select
                        orders_filtered.shipper_id
                        , orders_filtered.shipper_name
                        , orders_filtered.seller_id
                        , orders_filtered.seller_name
                        , orders_filtered.onboarded_date
                        , orders_filtered.order_id
                        , orders_filtered.granular_status
                        , orders_filtered.creation_datetime
                        , orders_filtered.creation_date
                        , orders_filtered.cod_value
                        , orders_filtered.insurance_value
                        , orders_filtered.potential_claim_value
                        , seller_creation_vol_daily.daily_order_count
                        , datediff(orders_filtered.creation_datetime, orders_filtered.onboarded_date) as days_from_onboarded
                        , datediff(orders_filtered.creation_datetime, seller_milestones.first_order_creation_date) as days_from_first_order_creation
                        , lazada_intercepted_orders.known_fraud_timestamp
                        , orders_claimed.resolution_datetime
                        , orders_filtered.system_id
                        , orders_filtered.created_month
                    from orders_filtered
                    left join seller_milestones on
                        orders_filtered.shipper_id = seller_milestones.shipper_id
                        and orders_filtered.seller_id = seller_milestones.seller_id
                    left join seller_creation_vol_daily on
                        orders_filtered.shipper_id = seller_creation_vol_daily.shipper_id
                        and orders_filtered.seller_id = seller_creation_vol_daily.seller_id
                        and orders_filtered.creation_date = seller_creation_vol_daily.creation_date
                    left join lazada_intercepted_orders on
                        orders_filtered.order_id = lazada_intercepted_orders.order_id
                    left join orders_claimed on 
                        orders_filtered.order_id = orders_claimed.order_id                  

                """,
            ),
            base.TransformView(
                view_name="seller_dates",
                jinja_template="""

                    select
                        distinct shipper_id
                        , seller_id
                        , creation_date
                    from orders_base

                """,
            ),
            base.TransformView(
                view_name="seller_creation_l30d_window_features",
                jinja_template="""

                    select
                        seller_dates.shipper_id
                        , seller_dates.seller_id
                        , seller_dates.creation_date
                        , avg(seller_creation_vol_daily.daily_order_count) as l30d_avg_daily_count
                        , stddev(seller_creation_vol_daily.daily_order_count) as l30d_standard_deviation
                    from seller_dates
                    left join seller_creation_vol_daily on
                        seller_dates.shipper_id = seller_creation_vol_daily.shipper_id
                        and seller_dates.seller_id = seller_creation_vol_daily.seller_id
                        and seller_creation_vol_daily.creation_date >= seller_dates.creation_date - interval '30' day
                        and seller_creation_vol_daily.creation_date < seller_dates.creation_date
                    group by 1, 2, 3 

                """,
            ),
            base.TransformView(
                view_name="seller_orders_l30d_window_features",
                jinja_template="""

                    select
                        seller_dates.shipper_id
                        , seller_dates.seller_id
                        , seller_dates.creation_date
                        , count(orders_base.order_id) as l30d_orders_created
                        , count_if(orders_base.granular_status = 'Completed') as l30d_orders_completed
                        , count_if(orders_base.potential_claim_value >= 500000) as l30d_hv_orders_created
                        , count_if(orders_base.granular_status = 'Completed' and orders_base.potential_claim_value >= 500000) as l30d_hv_orders_completed
                    from seller_dates
                    left join orders_base on
                        seller_dates.shipper_id = orders_base.shipper_id
                        and seller_dates.seller_id = orders_base.seller_id
                        and orders_base.creation_date >= seller_dates.creation_date - interval '30' day
                        and orders_base.creation_date < seller_dates.creation_date
                    group by 1, 2, 3     

                """,
            ),
            base.TransformView(
                view_name="seller_orders_l90d_window_features",
                jinja_template="""

                    select
                        seller_dates.shipper_id
                        , seller_dates.seller_id
                        , seller_dates.creation_date
                        , count(orders_base.known_fraud_timestamp) as l90d_orders_intercepted
                        , count(orders_base.resolution_datetime) as l90d_orders_claimed
                    from seller_dates
                    left join orders_base on
                        seller_dates.shipper_id = orders_base.shipper_id
                        and seller_dates.seller_id = orders_base.seller_id
                        and orders_base.creation_date >= seller_dates.creation_date - interval '90' day
                        and orders_base.creation_date < seller_dates.creation_date
                    group by 1, 2, 3         

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                    select
                        orders_base.shipper_id
                        , orders_base.shipper_name
                        , orders_base.seller_id
                        , orders_base.seller_name
                        , orders_base.onboarded_date
                        , orders_base.order_id
                        , orders_base.granular_status
                        , orders_base.cod_value
                        , orders_base.insurance_value
                        , orders_base.potential_claim_value
                        , orders_base.creation_datetime
                        , orders_base.creation_date
                        , orders_base.daily_order_count
                        , orders_base.days_from_onboarded
                        , orders_base.days_from_first_order_creation
                        , orders_base.known_fraud_timestamp
                        , orders_base.resolution_datetime
                        , if(
                            orders_base.days_from_first_order_creation <= 7 
                            and orders_base.potential_claim_value >= 500000
                            , 1, 0) as new_seller_with_hv_order_flag
                        , daily_l30d.l30d_avg_daily_count
                        , daily_l30d.l30d_standard_deviation
                        , (orders_base.daily_order_count - daily_l30d.l30d_avg_daily_count) / daily_l30d.l30d_standard_deviation as z_score
                        , l30d.l30d_orders_created
                        , l30d.l30d_orders_completed
                        , l30d.l30d_orders_completed / l30d.l30d_orders_created as l30d_order_completion_rate
                        , l30d.l30d_hv_orders_created
                        , l30d.l30d_hv_orders_completed
                        , l30d.l30d_hv_orders_completed / l30d.l30d_hv_orders_created as l30d_hv_order_completion_rate
                        , l90d.l90d_orders_intercepted
                        , l90d.l90d_orders_claimed
                        , if(l90d.l90d_orders_intercepted != 0, 1, 0) as l90d_intercepted_seller_flag
                        , if(l90d.l90d_orders_claimed != 0, 1, 0) as l90d_claimed_seller_flag
                        , orders_base.system_id
                        , orders_base.created_month
                    from orders_base
                    left join seller_creation_l30d_window_features daily_l30d on
                        orders_base.shipper_id = daily_l30d.shipper_id
                        and orders_base.seller_id = daily_l30d.seller_id
                        and orders_base.creation_date = daily_l30d.creation_date                        
                    left join seller_orders_l30d_window_features l30d on 
                        orders_base.shipper_id = l30d.shipper_id
                        and orders_base.seller_id = l30d.seller_id
                        and orders_base.creation_date = l30d.creation_date
                    left join seller_orders_l90d_window_features l90d on 
                        orders_base.shipper_id = l90d.shipper_id
                        and orders_base.seller_id = l90d.seller_id
                        and orders_base.creation_date = l90d.creation_date

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SELLER_ORDER_FEATURES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()