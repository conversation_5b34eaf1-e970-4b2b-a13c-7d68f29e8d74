import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.VALID_SCAN_EVENTS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED_BASE,
                view_name="pets_tickets_enriched_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).VALID_SCAN_EVENTS,
                view_name="valid_scan_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENT_TYPES,
                view_name="order_event_types",
            ),
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
        ),
        version_datetime=measurement_datetime
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="selected_scan_events",
                jinja_template="""
                -- order_events type
                -- 8=ADDED_TO_SHIPMENT, 24=DRIVER_INBOUND_SCAN, 25=DRIVER_PICKUP_SCAN,
                -- 26=HUB_INBOUND_SCAN, 27=PARCEL_ROUTING_SCAN, 28=ROUTE_INBOUND_SCAN
                select
                    scan.order_id
                    , scan.type
                    , type_name.name scan_name
                    , scan.scan_hub_id
                    , if(scan.type in (24,25)
                        , drivers.display_name
                        , concat(user_info.first_name, ' ', user_info.last_name)
                    ) as user_name
                    , if(scan.type in (24,25), 'driver_id', 'aaa_user_id') as user_id_type
                    , if(scan.type in (24,25), scan.driver_id, scan.user_id) as user_id
                    , scan.valid_scan_datetime
                from valid_scan_events as scan
                left join user_info
                    on scan.user_id = user_info.user_id
                left join drivers_enriched as drivers
                    on scan.driver_id = drivers.id
                left join order_event_types type_name
                    on scan.type = type_name.id
                where
                    type in (8, 24, 25, 26, 27, 28)
                """,
            ),
            base.TransformView(
                view_name="last_scan_before_creation",
                jinja_template="""
                select
                    base.id
                    , max(scan.valid_scan_datetime) as last_scan_before_creation_datetime
                    , max_by(scan.scan_hub_id, scan.valid_scan_datetime) as last_scan_before_creation_hub_id
                    , max_by(scan.user_id_type, scan.valid_scan_datetime) as last_scan_before_creation_user_id_type
                    , max_by(scan.user_id, scan.valid_scan_datetime) as last_scan_before_creation_user_id
                    , max_by(scan.user_name, scan.valid_scan_datetime) as last_scan_before_creation_user
                    , max_by(scan.scan_name, scan.valid_scan_datetime) as last_scan_before_creation_scan_type
                from pets_tickets_enriched_base base
                left join selected_scan_events scan
                    on base.order_id = scan.order_id
                    and base.creation_datetime > scan.valid_scan_datetime
                group by 1
                """,
            ),
            base.TransformView(
                view_name="last_scan_before_resolution",
                jinja_template="""
                select
                    base.id
                    , max(scan.valid_scan_datetime) as last_scan_before_resolution_datetime
                    , max_by(scan.scan_hub_id, scan.valid_scan_datetime) as last_scan_before_resolution_hub_id
                    , max_by(scan.user_id_type, scan.valid_scan_datetime) as last_scan_before_resolution_user_id_type
                    , max_by(scan.user_id, scan.valid_scan_datetime) as last_scan_before_resolution_user_id
                    , max_by(scan.user_name, scan.valid_scan_datetime) as last_scan_before_resolution_user
                    , max_by(scan.scan_name, scan.valid_scan_datetime) as last_scan_before_resolution_scan_type
                from pets_tickets_enriched_base base
                left join selected_scan_events scan
                    on base.order_id = scan.order_id
                    and coalesce(
                        base.resolution_datetime
                        , base.cancellation_datetime
                        , from_utc_timestamp('{{ measurement_datetime }}', {{ get_local_timezone }})
                    ) > scan.valid_scan_datetime
                group by 1
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("base.system_id"),
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.id
                    , base.country
                    , base.order_id
                    , base.tracking_id
                    , orders.granular_status as order_granular_status
                    , shippers.id as shipper_id
                    , base.type
                    , base.sub_type
                    , base.status
                    , base.outcome
                    , base.creation_datetime
                    , base.in_progress_datetime
                    , base.on_hold_datetime
                    , base.cancellation_datetime
                    , base.resolution_datetime
                    , base.assigned_dept
                    , base.investigating_hub_id
                    , base.investigating_hub_name
                    , base.investigating_hub_region
                    , base.creator_name
                    , base.resolver_name
                    , base.ticket_notes
                    , base.tampered_or_swapped_flag
                    , base.suspicious_reasons
                    , base.investigation_results
                    , base.entry_source_id
                    , base.age
                    , base.days_to_resolution
                    , before_creation.last_scan_before_creation_datetime
                    , before_creation.last_scan_before_creation_hub_id
                    , before_creation.last_scan_before_creation_user_id_type
                    , before_creation.last_scan_before_creation_user_id
                    , before_creation.last_scan_before_creation_user
                    , before_creation.last_scan_before_creation_scan_type
                    , before_resolution.last_scan_before_resolution_datetime
                    , before_resolution.last_scan_before_resolution_hub_id
                    , before_resolution.last_scan_before_resolution_user_id_type
                    , before_resolution.last_scan_before_resolution_user_id
                    , before_resolution.last_scan_before_resolution_user
                    , before_resolution.last_scan_before_resolution_scan_type
                    , base.created_month
                from pets_tickets_enriched_base as base
                left join orders_enriched as orders
                    on base.order_id = orders.order_id
                left join shipper_attributes shippers
                    on orders.shipper_id = shippers.id
                left join last_scan_before_creation as before_creation
                    on base.id = before_creation.id
                left join last_scan_before_resolution as before_resolution
                    on base.id = before_resolution.id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PETS_TICKETS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
