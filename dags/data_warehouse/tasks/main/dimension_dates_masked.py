import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SupportDAG.Task.DIMENSION_DATES_MASKED + ".py",
    task_name=data_warehouse.SupportDAG.Task.DIMENSION_DATES_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=tuple()),),
)

START_DATE = "2014-01-01"
END_DATE = "2050-12-31"


def get_task_config(env, measurement_datetime):
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="dates",
                jinja_template="""
                select
                    explode(
                        sequence(to_date('{{ start_date }}'), to_date('{{ end_date }}'), interval 1 day)
                    ) as calendar_date
                    """,
                jinja_arguments={
                    "start_date": START_DATE,
                    "end_date": END_DATE,
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with calendar_view AS(
                    select
                        year(calendar_date) * 10000 + month(calendar_date) * 100 + day(calendar_date) as date_int
                        , calendar_date
                        , month(calendar_date) as month_of_year
                        , date_format(calendar_date, 'MMMM') as month_name
                        , year(calendar_date) AS year_number
                        , year(calendar_date) * 100 + month(calendar_date) * 1 as year_month_number
                        , date_format(calendar_date, 'y-MM') as year_month
                        , dayofweek(calendar_date) AS day_of_week
                        , dayofyear(calendar_date) as day_of_year
                        , dayofmonth(calendar_date) as day_of_month
                        , weekofyear(calendar_date) as week_of_year
                        , quarter(calendar_date) as quarter_of_year
                        , date_format(calendar_date, 'EEEE') as calendar_day
                        , if(weekday(calendar_date) < 5, 1,0) as is_week_day
                        , if(
                            calendar_date == last_day(calendar_date)
                            , 1
                            , 0
                        ) as is_last_day_of_month
                        , if (
                            calendar_date == date('{{ measurement_datetime }}')
                            , 1
                            , 0
                        ) as is_current_day
                        , if (
                            date_format(calendar_date, 'yyyy-MM') = date_format('{{ measurement_datetime }}', 'yyyy-MM')
                            , 1
                            , 0
                        ) as is_current_month
                        , if (
                            date_format(calendar_date, 'yyyy') = date_format('{{ measurement_datetime }}', 'yyyy')
                            , 1
                            , 0
                        ) as is_current_year
                        , if (
                            quarter(calendar_date) = quarter('{{ measurement_datetime }}')
                                and year(calendar_date) = year('{{ measurement_datetime }}')
                            , 1
                            , 0
                        ) is_current_quarter
                    from  dates
                    )
                select
                    *
                from calendar_view
                    """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DIMENSION_DATES,
        measurement_datetime=measurement_datetime,
    )
    return base.TaskConfig(input=base.InputConfig(), transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
