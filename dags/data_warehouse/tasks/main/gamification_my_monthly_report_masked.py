import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_MONTHLY_REPORT_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_MONTHLY_REPORT_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    formatted_timestamp = measurement_datetime.strftime('%Y-%m-%d %H:%M:%S') + 'Z'

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_DAILY_REPORT,
                view_name="daily_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template=""" 
                with base as (
                    select
                        system_id
                        , year(route_date) as year
                        , month(route_date) as month
                        , concat(created_month,'-',cast(cycle_number as string)) as year_month_cycle 
                        , driver_id
                        , driver_type
                        , driver_display_name
                        , currency
                        , created_month
                        , if(driver_type like '%INDEPENDENT%' and cycle_number = 2
                            ,date(concat(year(route_date),'-',month(route_date),'-',16))
                            ,date(concat(year(route_date),'-',month(route_date),'-',01))) as cycle_start_date
                        , if(driver_type like '%INDEPENDENT%' and cycle_number = 1,
                            date(concat(year(route_date),'-',month(route_date),'-',15))
                            , last_day(route_date)) as cycle_end_date
                        , cast(sum(daily_bonus) as double) as total_daily_bonus
                        , cast(sum(planned_parcel_count) as int) as planned_parcel_count
                        , cast(sum(delivered_parcel_count) as int) as delivered_parcel_count
                        , cast(sum(failed_parcel_count) as int) as failed_parcel_count
                        , round(cast(sum(delivered_parcel_count) / sum(planned_parcel_count) as double),4) as monthly_successs_rate
                        , cast(sum(delivery_daily_bonus) as double) as total_delivery_daily_bonus
                        , cast(max(daily_balloon_bonus_amount) as double) as total_balloon_bonus
                        {%- for i in range(5) if i > 0 %}
                        , cast(max(tier_{{ i }}_balloon_targets_start) as int) as tier_{{ i }}_balloon_targets_start
                        , cast(max(tier_{{ i }}_balloon_targets_end) as int) as tier_{{ i }}_balloon_targets_end
                        , cast(max(tier_{{ i }}_balloon_bonus_amount) as double) as tier_{{ i }}_balloon_bonus_amount
                        {%- endfor %}
                    from daily_report
                    group by 1,2,3,4,5,6,7,8,9,10,11
                )

                select
                    *
                    , cast('""" + formatted_timestamp + """' as timestamp) as updated_at
                from base
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_MONTHLY_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()