import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_PH_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_PH_MASKED,
    system_ids=(constants.SystemID.PH,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.MM_DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("cost_segment", "event_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MM_DRIVERS_ENRICHED,
                view_name="mm_drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_JOURNEYS,
                view_name="hub_journeys",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_SCAN_EVENTS,
                view_name="pickup_scan_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).FM_SHIPPER_EXCLUSIONS + measurement_datetime_partition,
                view_name="fm_shipper_exclusions",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_COST_CONFIG_PH,
                view_name="cost_card_cost_config_ph",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_AIRSEA_COST_CONFIG_PH,
                view_name="cost_card_airsea_cost_config_ph",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_CONFIG_SECONDARY_PH,
                view_name="cost_card_mm_config_secondary_ph",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_CONFIG_REF_ID_COST_PH,
                view_name="cost_card_mm_config_ref_id_cost_ph",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_CONFIG_REF_ID_TRIP_ID_PH,
                view_name="cost_card_mm_config_ref_id_trip_id_ph",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_CONFIG_SEA_LANDHAUL_EXCLUSION,
                view_name="cost_card_mm_config_sea_landhaul_exclusion",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="filtered_om",
                jinja_template="""

                select
                    order_milestones.order_id
                    , order_milestones.system_id
                    , least(coalesce(order_milestones.weight,0), 200) as weight
                    , order_milestones.parcel_size as size
                    , order_milestones.pickup_datetime
                    , order_milestones.dp_dropoff_datetime
                    , order_milestones.inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.delivery_success_datetime
                    , order_milestones.pickup_hub_id
                    , order_milestones.inbound_hub_id
                    , order_milestones.delivery_success_hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.rts_flag
                    , order_milestones.is_pickup_required
                    , order_milestones.third_party_tracking_id
                    , order_milestones.shipper_id
                    , shippers_enriched.parent_id_coalesce as shipper_parent_id_coalesce
                    , shippers_enriched.parent_name_coalesce as shipper_parent_name_coalesce
                    , shippers_enriched.sales_channel
                    , order_milestones.creation_datetime
                    , date_format(order_milestones.creation_datetime, 'yyyy-MM') as created_month
                from order_milestones
                left join shippers_enriched
                    on order_milestones.shipper_id = shippers_enriched.id
                where shippers_enriched.sales_channel != 'Test'

                """,
            ),
            base.TransformView(
                view_name="trip_orders",
                jinja_template="""
                with base as (
                
                    select distinct
                        trip_id
                        , order_id
                    from middle_mile_trip_relationships

                ),
                trip_orders as (
                    
                    select
                        filtered_om.system_id
                        , filtered_om.created_month
                        , base.trip_id
                        , base.order_id
                        , filtered_om.rts_trigger_datetime
                        , filtered_om.rts_flag
                        , filtered_om.weight
                        , filtered_om.size
                    from base 
                    left join filtered_om
                        on base.order_id = filtered_om.order_id
                    where filtered_om.order_id is not null

                ),
                trip_orders_details as (

                    select
                        trip_orders.*
                        , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.completion_datetime) as trip_datetime
                        , hubs_enriched.facility_type
                    from trip_orders
                    left join movement_trips_enriched
                        on trip_orders.trip_id = movement_trips_enriched.trip_id
                    left join hubs_enriched
                        on movement_trips_enriched.origin_hub_id = hubs_enriched.id

                ),
                crossdock_first_event as (
                
                    select
                        order_id
                        , min(trip_datetime) as first_crossdock_event_time
                    from trip_orders_details
                    where 
                        facility_type = 'CROSSDOCK'
                    group by 1

                ),
                final as (

                    select
                        trip_orders_details.*
                    from trip_orders_details
                    left join crossdock_first_event 
                        on trip_orders_details.order_id = crossdock_first_event.order_id
                    where trip_orders_details.trip_datetime >= crossdock_first_event.first_crossdock_event_time
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="trip_details",
                jinja_template="""

                select 
                    movement_trips_enriched.system_id
                    , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.created_datetime) as trip_datetime
                    , date_format(coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.created_datetime), 'yyyy-MM') as month
                    , movement_trips_enriched.trip_id
                    , movement_trips_enriched.origin_hub_id
                    , lower(movement_trips_enriched.origin_hub_region) as origin_hub_region
                    , movement_trips_enriched.dest_hub_id
                    , lower(movement_trips_enriched.dest_hub_region) as dest_hub_region
                    , case 
                        when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'LAND_HAUL'
                            and (movement_trips_enriched.origin_hub_id = 1017 and movement_trips_enriched.dest_hub_region = 104411)
                                or (movement_trips_enriched.origin_hub_id = 104411 and movement_trips_enriched.dest_hub_id = 1017)
                                or (movement_trips_enriched.origin_hub_id = 1017 and movement_trips_enriched.dest_hub_id = 105542)
                                or (movement_trips_enriched.origin_hub_id = 105542 and movement_trips_enriched.dest_hub_id = 1017))
                            then 'intersort'
                        when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'LAND_HAUL') then 'pri_land_trip'
                        when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'AIR_HAUL') then 'pri_air_trip'
                        when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'SEA_HAUL') then 'pri_sea_trip'
                        when (movement_trips_enriched.linehaul_purpose_type = 'secondary') then 'sec_trip'
                        end as type
                    , origin_hub.facility_type as origin_facility
                    , dest_hub.facility_type as dest_facility
                    , movement_trips_enriched.estimated_distance as distance
                    , movement_trips_enriched.total_order_volume
                    , movement_trips_enriched.expected_baseline_cbm as truck_capacity
                    , movement_trips_enriched.calculated_utilization
                    , sum(trip_orders.weight) as total_weight
                    , count(trip_orders.order_id) as total_orders
                from movement_trips_enriched
                left join trip_orders
                    on movement_trips_enriched.trip_id = trip_orders.trip_id
                left join hubs_enriched as origin_hub
                    on movement_trips_enriched.origin_hub_id = origin_hub.id
                left join hubs_enriched as dest_hub
                    on movement_trips_enriched.dest_hub_id = dest_hub.id
                left join mm_drivers_enriched
                    on coalesce(movement_trips_enriched.primary_driver_id, movement_trips_enriched.secondary_driver_id) = mm_drivers_enriched.driver_id
                left join cost_card_mm_config_sea_landhaul_exclusion
                    on mm_drivers_enriched.vendor_id = cost_card_mm_config_sea_landhaul_exclusion.vendor_id
                    and movement_trips_enriched.origin_hub_id = cost_card_mm_config_sea_landhaul_exclusion.origin_hub_id
                    and movement_trips_enriched.dest_hub_id = cost_card_mm_config_sea_landhaul_exclusion.dest_hub_id
                where movement_trips_enriched.status = 'COMPLETED'
                    and cost_card_mm_config_sea_landhaul_exclusion.vendor_id is null
                group by {{ range(1, 16) | join(',') }}

                """,
            ),
            base.TransformView(
                view_name="ref_trip_cpp",
                jinja_template="""
                with base as (

                    select
                        trip_details.trip_id
                        , trip_details.type
                        , trip_details.month
                        , trip_details.trip_datetime
                        , trip_details.origin_hub_id
                        , trip_details.dest_hub_id
                        , trip_details.dest_hub_region
                        , trip_details.distance
                        , cost_card_mm_config_ref_id_trip_id_ph.ref_id
                    from trip_details
                    left join cost_card_mm_config_ref_id_trip_id_ph
                        on trip_details.trip_id = cost_card_mm_config_ref_id_trip_id_ph.trip_id
                        
                ),
                cpkm_base as (

                    select
                        ref_id
                        , sum(distance) as distance
                    from base
                    group by 1

                ),
                cpkm as (

                    select
                        cpkm_base.ref_id
                        , cost_card_mm_config_ref_id_cost_ph.cost/ cast(cpkm_base.distance as double) as cpkm
                    from cpkm_base
                    left join cost_card_mm_config_ref_id_cost_ph
                        on cpkm_base.ref_id = cost_card_mm_config_ref_id_cost_ph.ref_id

                ),
                secondary_allocation as (

                    select
                        base.*
                        , cpkm.cpkm * base.distance as allocated_cost
                    from base
                    left join cpkm
                        on base.ref_id = cpkm.ref_id
                    where base.type = 'sec_trip'
                
                ),
                primary_trips as (

                    select
                        base.*
                        , cpkm.cpkm * base.distance as temp_allocated_cost
                    from base
                    left join cpkm
                        on base.ref_id = cpkm.ref_id
                    where base.type != 'sec_trip'

                ),
                sequence_analysis as (

                    select 
                        current.*
                        , previous.origin_hub_id as prior_origin_hub_id
                    from primary_trips as current
                    left join primary_trips as previous
                        on current.ref_id = previous.ref_id
                        and current.trip_datetime > previous.trip_datetime

                ),
                backhaul_start_trip as (

                    select
                        ref_id
                        , min(trip_datetime) as backhaul_start_time
                    from sequence_analysis
                    where dest_hub_id = prior_origin_hub_id
                    group by 1

                ),
                primary_allocation_base as (
                
                    select
                        ref_id
                        , sum(temp_allocated_cost) as total_cost
                    from primary_trips
                    group by 1

                ),
                primary_allocation_logic as (
                
                    select
                        primary_allocation_base.ref_id
                        , primary_allocation_base.total_cost
                        , case 
                            when backhaul_start_trip.ref_id is null then primary_allocation_base.total_cost
                            else 0.7 * primary_allocation_base.total_cost
                        end as fronthaul_cost
                        , case 
                            when backhaul_start_trip.ref_id is null then 0
                            else 0.3 * primary_allocation_base.total_cost
                        end as backhaul_cost
                    from primary_allocation_base
                    left join backhaul_start_trip
                        on primary_allocation_base.ref_id = backhaul_start_trip.ref_id
                
                ),
                haul_type as (

                    select 
                        primary_trips.*
                        , case 
                            when backhaul_start_trip.ref_id is null then 'fronthaul'
                            when primary_trips.trip_datetime < backhaul_start_trip.backhaul_start_time then 'fronthaul'
                            else 'backhaul'
                        end as haul_type
                    from primary_trips
                    left join backhaul_start_trip
                        on primary_trips.ref_id = backhaul_start_trip.ref_id

                ), 
                haul_distance as (
                
                    select
                        haul_type.ref_id
                        , sum(case when haul_type = 'fronthaul' then distance else 0 end) as fronthaul_distance
                        , sum(case when haul_type = 'backhaul' then distance else 0 end) as backhaul_distance
                    from haul_type
                    group by 1

                ),
                primary_allocation_costs as (

                    select 
                        primary_allocation_logic.*
                        , haul_distance.fronthaul_distance
                        , haul_distance.backhaul_distance
                        , coalesce(primary_allocation_logic.fronthaul_cost / cast(fronthaul_distance as double), 0) as fronthaul_cpkm
                        , coalesce(primary_allocation_logic.backhaul_cost / cast(backhaul_distance as double), 0) as backhaul_cpkm
                    from primary_allocation_logic
                    left join haul_distance
                        on primary_allocation_logic.ref_id = haul_distance.ref_id

                ), 
                primary_allocation as (

                    select 
                        haul_type.trip_id
                        , haul_type.type
                        , haul_type.month
                        , haul_type.trip_datetime
                        , haul_type.origin_hub_id
                        , haul_type.dest_hub_id
                        , haul_type.dest_hub_region
                        , haul_type.distance
                        , haul_type.ref_id
                        , case 
                            when haul_type.haul_type = 'fronthaul' then primary_allocation_costs.fronthaul_cpkm * haul_type.distance
                            when haul_type.haul_type = 'backhaul' then primary_allocation_costs.backhaul_cpkm * haul_type.distance
                        end as allocated_cost
                    from haul_type
                    left join primary_allocation_costs
                        on haul_type.ref_id = primary_allocation_costs.ref_id

                ),
                final as (
                
                    select * from secondary_allocation
                    UNION ALL
                    select * from primary_allocation
                
                )

                select * from final
                """,
            ),
            base.TransformView(
                view_name="pri_trip_cpp",
                jinja_template="""
                with base as (

                    select
                        trip_id
                        , sum(allocated_cost) as cost
                        , collect_list(ref_id) as ref_id_list
                    from ref_trip_cpp
                    where type != 'sec_trip'
                    group by 1

                ),
                final as (
                
                    select
                        base.trip_id
                        , base.cost/trip_details.total_weight as cpkg
                        , base.ref_id_list as source
                    from base
                    left join trip_details
                        on base.trip_id = trip_details.trip_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="sec_trip_cpp",
                jinja_template="""
                with secondary_costs_base as (

                    select
                        ref_id
                        , min(month) as month
                        , min_by(origin_hub_id, trip_datetime) as origin_hub_id
                        , min_by(dest_hub_region, trip_datetime) as dest_hub_region
                        , sum(allocated_cost) as allocated_cost
                    from ref_trip_cpp
                    where type = 'sec_trip'
                    group by 1
                
                ),
                secondary_costs_ref_id as (

                    select
                        month
                        , origin_hub_id
                        , dest_hub_region
                        , sum(allocated_cost) as allocated_cost
                    from secondary_costs_base
                    group by {{ range(1, 4) | join(',') }}

                ),
                secondary_costs as (

                    select
                        coalesce(secondary_costs_ref_id.month, cost_card_mm_config_secondary_ph.created_month) as month
                        , coalesce(secondary_costs_ref_id.origin_hub_id, cost_card_mm_config_secondary_ph.hub_id) as hub_id
                        , coalesce(secondary_costs_ref_id.dest_hub_region, lower(cost_card_mm_config_secondary_ph.region)) as region
                        , sum(coalesce(secondary_costs_ref_id.allocated_cost,0) + coalesce(cost_card_mm_config_secondary_ph.cost,0)) as cost
                    from secondary_costs_ref_id
                    full join cost_card_mm_config_secondary_ph
                        on lower(cost_card_mm_config_secondary_ph.cost_segment) = 'mm'
                        and lower(cost_card_mm_config_secondary_ph.type) = 'secondary'
                        and secondary_costs_ref_id.month = cost_card_mm_config_secondary_ph.created_month
                        and secondary_costs_ref_id.origin_hub_id = cost_card_mm_config_secondary_ph.hub_id
                        and secondary_costs_ref_id.dest_hub_region = lower(cost_card_mm_config_secondary_ph.region)
                    group by {{ range(1, 4) | join(',') }}

                ),
                base as (
                
                    select
                        *
                        , case 
                            when origin_facility = 'CROSSDOCK' then origin_hub_id
                            when dest_facility = 'CROSSDOCK' then dest_hub_id
                            end as crossdock_hub_id
                        , case 
                            when origin_facility = 'CROSSDOCK' then dest_hub_region
                            when dest_facility = 'CROSSDOCK' then origin_hub_region
                            end as non_crossdock_hub_region
                    from trip_details
                    where type = 'sec_trip'
                        and (origin_facility = 'CROSSDOCK' or dest_facility = 'CROSSDOCK')

                ),
                order_count as (
                
                    select 
                        month
                        , crossdock_hub_id
                        , non_crossdock_hub_region
                        , sum(total_weight) as total_weight
                    from base
                    group by {{ range(1, 4) | join(',') }}

                ),
                cpp as (
                
                    select
                        order_count.month
                        , order_count.crossdock_hub_id
                        , order_count.non_crossdock_hub_region
                        , secondary_costs.cost
                        , secondary_costs.cost/order_count.total_weight as cpkg
                    from order_count
                    left join secondary_costs
                        on order_count.month = secondary_costs.month
                        and order_count.crossdock_hub_id = secondary_costs.hub_id
                        and order_count.non_crossdock_hub_region = secondary_costs.region

                ),
                final as (
                
                    select
                        base.trip_id
                        , cpp.cpkg
                    from base
                    left join cpp
                        on base.month = cpp.month
                        and base.crossdock_hub_id = cpp.crossdock_hub_id
                        and base.non_crossdock_hub_region = cpp.non_crossdock_hub_region

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="waybill_cpp",
                jinja_template="""
                with waybill_details_base as (
                
                    select distinct
                        shipment_orders_enriched.order_id
                        , coalesce(shipments_enriched.mawb, shipments_enriched.swb) as ref_id
                    from shipment_orders_enriched
                    left join shipments_enriched
                        on shipment_orders_enriched.shipment_id = shipments_enriched.shipment_id
                    where coalesce(shipments_enriched.mawb, shipments_enriched.swb) is not null
                        and shipments_enriched.status = 'Completed'

                ),
                waybill_details as (
                
                    select
                        waybill_details_base.ref_id
                        , sum(filtered_om.weight) as total_weight
                        , count(filtered_om.order_id) as total_orders
                    from waybill_details_base
                    left join filtered_om
                        on waybill_details_base.order_id = filtered_om.order_id
                    group by 1
                ),
                final as (
                
                    select
                        waybill_details.ref_id
                        , cost_card_airsea_cost_config_ph.type
                        , waybill_details.total_weight
                        , waybill_details.total_orders
                        , cost_card_airsea_cost_config_ph.cost
                        , cast(cost_card_airsea_cost_config_ph.cost as double)/cast(waybill_details.total_weight as double) as cpkg
                        , cast(cost_card_airsea_cost_config_ph.cost as double)/cast(waybill_details.total_orders as double) as cpp
                    from waybill_details
                    left join cost_card_airsea_cost_config_ph
                        on waybill_details.ref_id = cost_card_airsea_cost_config_ph.ref_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="fm_costs",
                jinja_template="""
                with first_pickup as (
                
                        select
                            order_id
                            , min_by(route_driver_id, event_datetime) as driver_id
                        from pickup_scan_events
                        where source_table = 'inbound_scans'
                    group by 1

                    ),
                    fm_base as (
                    
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.rts_flag
                        , filtered_om.rts_trigger_datetime
                        , coalesce(filtered_om.pickup_datetime, filtered_om.inbound_datetime) as event_datetime
                        , coalesce(pickup_hub.region, inbound_hub.region) as region
                        , coalesce(pickup_hub.id, inbound_hub.id) as hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , case 
                            when filtered_om.pickup_datetime is not null then 'pickup'
                            when filtered_om.inbound_datetime is not null then 'inbound'
                            end as type
                        , case
                            when filtered_om.dp_dropoff_datetime is not null or drivers_enriched.driver_type like "Mitra%" then 'PUDO/Mitras'
                            when filtered_om.pickup_datetime is not null then 'Warehouse/B2C/Marketplace'
                            when filtered_om.pickup_datetime is null and fm_shipper_exclusions.shipper_id is not null then exclusion_reason
                            when filtered_om.pickup_datetime is null and fm_shipper_exclusions.shipper_id is null and filtered_om.is_pickup_required = 0 then 'Order Set as Pickup Not Required'
                            when filtered_om.pickup_datetime is null and fm_shipper_exclusions.shipper_id is null then 'No pickup_scan'
                            else 'Undefined' end as pickup_channel
                    from filtered_om
                    left join hubs_enriched as pickup_hub
                        on filtered_om.pickup_hub_id = pickup_hub.id
                    left join hubs_enriched as inbound_hub
                        on filtered_om.inbound_hub_id = inbound_hub.id
                    left join fm_shipper_exclusions
                        on filtered_om.shipper_id = fm_shipper_exclusions.shipper_id
                        and filtered_om.creation_datetime >= date(fm_shipper_exclusions.start_date)
                        and filtered_om.creation_datetime <= date(fm_shipper_exclusions.end_date)
                    left join first_pickup
                        on filtered_om.order_id = first_pickup.order_id
                    left join drivers_enriched
                        on first_pickup.driver_id = drivers_enriched.id

                ),
                fm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'fm' as cost_segment
                        , lower(region) as origin_hub_region
                        , hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , weight
                        , size
                        , type
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from fm_base
                        where
                            pickup_channel not in ('Cross Border'
                                                    , 'Cross Border (Direct Injection)'
                                                    , 'Direct Injection'
                                                    , 'Non-FM'
                                                    , 'NV internal'
                                                    , 'Order Set as Pickup Not Required'
                                                    , '')

                ),
                final as (
                
                    select
                        fm_events.order_id
                        , fm_events.created_month
                        , fm_events.system_id
                        , fm_events.cost_segment
                        , fm_events.event_datetime
                        , fm_events.month
                        , fm_events.origin_hub_region
                        , fm_events.origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , fm_events.weight
                        , fm_events.size
                        , 'parcel' as type
                        , cost_card_cost_config_ph.cost
                        , fm_events.type as remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from fm_events
                    left join cost_card_cost_config_ph
                        on fm_events.cost_segment = lower(cost_card_cost_config_ph.cost_segment)
                        and fm_events.origin_hub_region = lower(cost_card_cost_config_ph.region)
                        and fm_events.month = cost_card_cost_config_ph.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="sort_costs",
                jinja_template="""
                with sort_events as (
                
                    select
                        hub_journeys.order_id
                        , filtered_om.created_month
                        , hub_journeys.system_id
                        , 'sort' as cost_segment
                        , hub_journeys.event_datetime
                        , date_format(hub_journeys.event_datetime, 'yyyy-MM') as month
                        , hub_journeys.region as origin_hub_region
                        , hub_journeys.coalesce_hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , 'not applicable' as type
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null
                        and hub_journeys.facility_type = 'CROSSDOCK'

                ),
                final as (
                
                    select
                        sort_events.order_id
                        , sort_events.created_month
                        , sort_events.system_id
                        , sort_events.cost_segment
                        , sort_events.event_datetime
                        , sort_events.month
                        , sort_events.origin_hub_region
                        , sort_events.origin_hub_id
                        , sort_events.dest_hub_region
                        , sort_events.dest_hub_id
                        , sort_events.weight
                        , sort_events.size
                        , sort_events.type
                        , cost_card_cost_config_ph.cost
                        , cost_card_cost_config_ph.remarks
                        , 'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from sort_events
                    left join cost_card_cost_config_ph
                        on sort_events.cost_segment = lower(cost_card_cost_config_ph.cost_segment)
                        and sort_events.origin_hub_id = cost_card_cost_config_ph.hub_id
                        and sort_events.month = cost_card_cost_config_ph.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="lm_costs",
                jinja_template="""
                with lm_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.delivery_success_datetime as event_datetime
                        , coalesce(delivery_success_hub.region, dest_hub.region) as region
                        , coalesce(filtered_om.delivery_success_hub_id, filtered_om.dest_hub_id) as hub_id
                        , case 
                            when filtered_om.delivery_success_hub_id is not null then 'delivery' 
                            when filtered_om.dest_hub_id is not null then 'proxy'
                            end as type
                        , filtered_om.weight
                        , filtered_om.size
                    from filtered_om
                    left join hubs_enriched as delivery_success_hub
                        on filtered_om.delivery_success_hub_id = delivery_success_hub.id
                    left join hubs_enriched as dest_hub
                        on filtered_om.dest_hub_id = dest_hub.id
                    where
                        filtered_om.delivery_success_datetime is not null
                        and filtered_om.third_party_tracking_id is null

                ),
                lm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'lm' as cost_segment
                        , 'not applicable' as origin_hub_region
                        , 'not applicable' as origin_hub_id
                        , region as dest_hub_region
                        , hub_id as dest_hub_id
                        , weight
                        , size
                        , type
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from lm_base

                ),
                final as (
                
                    select
                        lm_events.order_id
                        , lm_events.created_month
                        , lm_events.system_id
                        , lm_events.cost_segment
                        , lm_events.event_datetime
                        , lm_events.month
                        , lm_events.origin_hub_region
                        , lm_events.origin_hub_id
                        , lm_events.dest_hub_region
                        , lm_events.dest_hub_id
                        , lm_events.weight
                        , lm_events.size
                        , lm_events.type
                        , cost_card_cost_config_ph.cost
                        , cost_card_cost_config_ph.remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from lm_events
                    left join cost_card_cost_config_ph
                        on lm_events.cost_segment = lower(cost_card_cost_config_ph.cost_segment)
                        and lm_events.dest_hub_id = cost_card_cost_config_ph.hub_id
                        and lm_events.size = lower(cost_card_cost_config_ph.size)
                        and lm_events.month = cost_card_cost_config_ph.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="mm_costs",
                jinja_template="""
                with mm_base as (
                
                    select
                        trip_orders.order_id
                        , trip_orders.created_month
                        , trip_orders.system_id
                        , trip_orders.rts_trigger_datetime
                        , trip_orders.rts_flag
                        , trip_orders.weight
                        , trip_orders.size
                        , trip_details.trip_id
                        , trip_details.trip_datetime as event_datetime
                        , trip_details.origin_hub_region
                        , trip_details.origin_hub_id
                        , trip_details.dest_hub_region
                        , trip_details.dest_hub_id
                        , trip_details.type
                        , trip_details.distance
                        , trip_details.total_order_volume
                        , trip_details.truck_capacity
                        , trip_details.calculated_utilization
                    from trip_orders
                    left join trip_details
                        on trip_orders.trip_id = trip_details.trip_id

                ),
                mm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'mm' as cost_segment
                        , trip_id
                        , origin_hub_region
                        , origin_hub_id
                        , dest_hub_region
                        , dest_hub_id
                        , weight
                        , size
                        , type
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                        , rts_trigger_datetime
                        , rts_flag
                        , distance
                        , total_order_volume
                        , truck_capacity
                        , calculated_utilization
                    from mm_base
                    where event_datetime is not null

                ),
                mm_pri_trip_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , mm_events.weight * pri_trip_cpp.cpkg as cost
                        , cast(pri_trip_cpp.source as string) as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join pri_trip_cpp
                        on mm_events.trip_id = pri_trip_cpp.trip_id
                    where mm_events.type = 'pri_land_trip' or mm_events.type = 'intersort'

                ),
                mm_waybill_shipments as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.rts_trigger_datetime
                        , filtered_om.rts_flag
                        , filtered_om.weight
                        , filtered_om.size
                        , coalesce(shipments_enriched.mawb, shipments_enriched.swb) as ref_id
                        , case 
                            when shipments_enriched.mawb is not null then 'awb'
                            when shipments_enriched.swb is not null then 'swb'
                            end as type
                        , coalesce(shipments_enriched.orig_shipment_close_datetime, shipments_enriched.shipment_completion_datetime) as event_datetime
                        , shipments_enriched.orig_hub_region as origin_hub_region
                        , shipments_enriched.orig_hub_id as origin_hub_id
                        , shipments_enriched.dest_hub_region
                        , shipments_enriched.dest_hub_id
                    from filtered_om
                    left join shipment_orders_enriched
                        on filtered_om.order_id = shipment_orders_enriched.order_id
                    left join shipments_enriched
                        on shipment_orders_enriched.shipment_id = shipments_enriched.shipment_id
                    where coalesce(shipments_enriched.mawb, shipments_enriched.swb) is not null
                        and shipments_enriched.status = 'Completed'

                ),
                mm_waybill_shipments_costs as (
                
                    select
                        mm_waybill_shipments.order_id
                        , mm_waybill_shipments.created_month
                        , mm_waybill_shipments.system_id
                        , 'mm' as cost_segment
                        , mm_waybill_shipments.event_datetime
                        , date_format(mm_waybill_shipments.event_datetime, 'yyyy-MM') as month
                        , mm_waybill_shipments.origin_hub_region
                        , mm_waybill_shipments.origin_hub_id
                        , mm_waybill_shipments.dest_hub_region
                        , mm_waybill_shipments.dest_hub_id
                        , mm_waybill_shipments.weight
                        , mm_waybill_shipments.size
                        , case 
                            when mm_waybill_shipments.type = 'awb' then 'pri_air_trip'
                            when mm_waybill_shipments.type = 'swb' then 'pri_sea_trip'
                            end as type
                        , waybill_cpp.cpkg * mm_waybill_shipments.weight as cost
                        , mm_waybill_shipments.type as remarks
                        , mm_waybill_shipments.ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from mm_waybill_shipments
                    left join waybill_cpp
                        on mm_waybill_shipments.ref_id = waybill_cpp.ref_id

                ),
                mm_sec_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , case
                            when (
                                mm_events.origin_hub_id in (1017,104411,105542)
                                or mm_events.dest_hub_id in (1017,104411,105542)
                            ) then 'dd'
                            else 'secondary'
                        end as type
                        , mm_events.weight * sec_trip_cpp.cpkg as cost
                        , 'not applicable' as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join sec_trip_cpp
                        on mm_events.trip_id = sec_trip_cpp.trip_id
                    where 
                        mm_events.type = 'sec_trip'

                ),
                mm_crossborder_costs_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.system_id
                        , filtered_om.created_month
                        , 'mm' as cost_segment
                        , filtered_om.inbound_datetime as event_datetime
                        , date_format(filtered_om.inbound_datetime, 'yyyy-MM') as month
                        , hubs_enriched.region as origin_hub_region
                        , filtered_om.inbound_hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , 'not_applicable' as ref_id
                    from filtered_om
                    left join hubs_enriched
                        on filtered_om.inbound_hub_id = hubs_enriched.id
                    where 
                        filtered_om.sales_channel = 'Cross Border'
                        and filtered_om.shipper_parent_id_coalesce = 7314943

                ),
                mm_crossborder_weight as (
                
                    select
                        month
                        , origin_hub_id as hub_id
                        , sum(weight) as total_weight
                    from mm_crossborder_costs_base
                    group by {{ range(1, 3) | join(',') }}

                ),
                mm_crossborder_cpp as (
                
                    select
                        mm_crossborder_weight.month
                        , mm_crossborder_weight.hub_id
                        , cost_card_cost_config_ph.cost / mm_crossborder_weight.total_weight as cpkg
                    from mm_crossborder_weight
                    left join cost_card_cost_config_ph
                        on lower(cost_card_cost_config_ph.cost_segment) = 'mm'
                        and lower(cost_card_cost_config_ph.type) = 'crossborder_inbound'
                        and mm_crossborder_weight.month = cost_card_cost_config_ph.created_month
                        and mm_crossborder_weight.hub_id = cost_card_cost_config_ph.hub_id
                ),
                mm_crossborder_costs as (
                
                    select
                        mm_crossborder_costs_base.order_id
                        , mm_crossborder_costs_base.created_month
                        , mm_crossborder_costs_base.system_id
                        , mm_crossborder_costs_base.cost_segment
                        , mm_crossborder_costs_base.event_datetime
                        , mm_crossborder_costs_base.month
                        , mm_crossborder_costs_base.origin_hub_region
                        , mm_crossborder_costs_base.origin_hub_id
                        , mm_crossborder_costs_base.dest_hub_region
                        , mm_crossborder_costs_base.dest_hub_id
                        , mm_crossborder_costs_base.weight
                        , mm_crossborder_costs_base.size
                        , 'pri_air_trip' as type
                        , mm_crossborder_costs_base.weight * mm_crossborder_cpp.cpkg as cost
                        , 'crossborder_inbound' as remarks
                        , mm_crossborder_costs_base.ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from mm_crossborder_costs_base
                    left join mm_crossborder_cpp
                        on mm_crossborder_costs_base.month = mm_crossborder_cpp.month
                        and mm_crossborder_costs_base.origin_hub_id = mm_crossborder_cpp.hub_id
                    where mm_crossborder_cpp.hub_id is not null

                ),
                final as (
                
                    select * from mm_pri_trip_costs
                    UNION ALL
                    select * from mm_waybill_shipments_costs
                    UNION ALL
                    select * from mm_sec_costs
                    UNION ALL
                    select * from mm_crossborder_costs

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="hub_costs",
                jinja_template="""
                with hub_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.weight
                        , filtered_om.size
                        , hub_journeys.event_datetime
                        , hub_journeys.coalesce_hub_id
                        , hub_journeys.region
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null

                ),
                hub_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'hub' as cost_segment
                        , region as origin_hub_region
                        , coalesce_hub_id as origin_hub_id
                        , 'not applicable' as dest_hub_region
                        , 'not applicable' as dest_hub_id
                        , weight
                        , size
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from hub_base

                ),
                final as (
                
                    select
                        hub_events.order_id
                        , hub_events.created_month
                        , hub_events.system_id
                        , hub_events.cost_segment
                        , hub_events.event_datetime
                        , hub_events.month
                        , hub_events.origin_hub_region
                        , hub_events.origin_hub_id
                        , hub_events.dest_hub_region
                        , hub_events.dest_hub_id
                        , hub_events.weight
                        , hub_events.size
                        , cost_card_cost_config_ph.type
                        , cost_card_cost_config_ph.cost
                        , case
                            when hubs_enriched.name like 'FM%' then 'FM'
                            when hubs_enriched.name like 'ASN%' then 'FM'
                            when hubs_enriched.facility_type in ('DP') then 'FM'
                            when hubs_enriched.facility_type in ('AIRPORT','CROSSDOCK','DG_HUB','SEAPORT') then 'WH'
                            when hubs_enriched.facility_type in ('STATION') then 'LM'
                            else 'LM'
                        end as remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from hub_events
                    left join cost_card_cost_config_ph
                        on hub_events.cost_segment = lower(cost_card_cost_config_ph.cost_segment)
                        and hub_events.origin_hub_id = cost_card_cost_config_ph.hub_id
                        and hub_events.month = cost_card_cost_config_ph.created_month
                    left join hubs_enriched
                        on hub_events.origin_hub_id = hubs_enriched.id

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with combined_costs as (
                
                    select * from fm_costs
                    UNION ALL
                    select * from sort_costs
                    UNION ALL
                    select * from mm_costs
                    UNION ALL
                    select * from lm_costs
                    UNION ALL
                    select * from hub_costs

                ),
                sequence as (
                    select 
                        *
                        , row_number() over (partition by order_id order by event_datetime, cost_segment, type) as event_sequence
                    from combined_costs

                ),
                final as (
                
                    select 
                        system_id
                        , order_id
                        , event_sequence
                        , event_datetime
                        , lower(origin_hub_region) as origin_hub_region
                        , origin_hub_id
                        , lower(dest_hub_region) as dest_hub_region
                        , dest_hub_id
                        , weight
                        , lower(size) as size
                        , lower(type) as type
                        , cast(cost as double) as cost
                        , lower(remarks) as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                        , lower(cost_segment) as cost_segment
                        , month as event_month
                        , month as created_month
                        , created_month as order_created_month
                    from sequence
                    where month >= '2023-01' 

                )

                select * from final
                
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_EVENTS_PH,
        measurement_datetime=measurement_datetime,
        partition_by=("cost_segment", "event_month"),
        output_range=lookback_ranges.output,
        use_native_overwrite=True
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
