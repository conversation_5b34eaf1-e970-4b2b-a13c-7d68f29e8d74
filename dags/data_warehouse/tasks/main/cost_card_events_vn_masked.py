import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_VN_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_VN_MASKED,
    system_ids=(constants.SystemID.VN,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.MM_DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("cost_segment", "event_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MM_DRIVERS_ENRICHED,
                view_name="mm_drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_JOURNEYS,
                view_name="hub_journeys",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_SCAN_EVENTS,
                view_name="pickup_scan_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_MILE_VOLUME_ORDERS,
                view_name="first_mile_volume_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_COST_CONFIG_VN,
                view_name="cost_card_cost_config_vn",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_HUB_MAPPING_VN,
                view_name="cost_card_mm_hub_mapping_vn",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_MM_DRIVER_MAPPING_VN,
                view_name="cost_card_mm_driver_mapping_vn",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_RTS_DRIVER_TYPES_VN,
                view_name="cost_card_rts_driver_types_vn",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).TRIP_COST_TIERS,
                view_name="trip_cost_tiers",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MM_TRIP_STITCH_COST,
                view_name="mm_trip_stitch_cost",
                system_id=system_id,    
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="filtered_om",
                jinja_template="""

                select
                    order_milestones.order_id
                    , order_milestones.system_id
                    , least(coalesce(order_milestones.weight,0), 200) as weight
                    , order_milestones.parcel_size as size
                    , order_milestones.pickup_datetime
                    , order_milestones.dp_dropoff_datetime
                    , order_milestones.inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.delivery_success_datetime
                    , order_milestones.pickup_hub_id
                    , order_milestones.inbound_hub_id
                    , order_milestones.delivery_success_hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.rts_flag
                    , order_milestones.is_pickup_required
                    , order_milestones.third_party_tracking_id
                    , order_milestones.order_type
                    , order_milestones.shipper_id
                    , shippers_enriched.parent_id_coalesce as shipper_parent_id_coalesce
                    , shippers_enriched.parent_name_coalesce as shipper_parent_name_coalesce
                    , shippers_enriched.sales_channel
                    , order_milestones.creation_datetime
                    , date_format(order_milestones.creation_datetime, 'yyyy-MM') as created_month
                from order_milestones
                left join shippers_enriched
                    on order_milestones.shipper_id = shippers_enriched.id
                where shippers_enriched.sales_channel != 'Test'

                """,
            ),
            base.TransformView(
                view_name="trip_orders",
                jinja_template="""
                with base as (
                
                    select distinct
                        trip_id
                        , order_id
                    from middle_mile_trip_relationships

                ),
                trip_orders as (
                    
                    select
                        filtered_om.system_id
                        , filtered_om.created_month
                        , base.trip_id
                        , base.order_id
                        , filtered_om.rts_trigger_datetime
                        , filtered_om.rts_flag
                        , filtered_om.weight
                        , filtered_om.size
                    from base 
                    left join filtered_om
                        on base.order_id = filtered_om.order_id
                    where filtered_om.order_id is not null

                ),
                trip_orders_details as (

                    select
                        trip_orders.*
                        , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.completion_datetime) as trip_datetime
                        , hubs_enriched.facility_type
                    from trip_orders
                    left join movement_trips_enriched
                        on trip_orders.trip_id = movement_trips_enriched.trip_id
                    left join hubs_enriched
                        on movement_trips_enriched.origin_hub_id = hubs_enriched.id

                ),
                crossdock_first_event as (
                
                    select
                        order_id
                        , min(trip_datetime) as first_crossdock_event_time
                    from trip_orders_details
                    where 
                        facility_type = 'CROSSDOCK'
                    group by 1

                ),
                final as (

                    select
                        trip_orders_details.*
                    from trip_orders_details
                    left join crossdock_first_event 
                        on trip_orders_details.order_id = crossdock_first_event.order_id
                    where trip_orders_details.trip_datetime >= crossdock_first_event.first_crossdock_event_time
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="trip_details",
                jinja_template="""
                with local_bus as (
                
                    select distinct
                        hub_id
                    from cost_card_mm_hub_mapping_vn
                    where facility_type = 'local_bus'

                ),
                mm_trips as(
                
                    select distinct
                        trip_id
                    from trip_orders
                    
                ),
                base as (
                
                    select
                        movement_trips_enriched.system_id
                        , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.created_datetime) as event_datetime
                        , movement_trips_enriched.trip_id
                        , movement_trips_enriched.origin_hub_id
                        , movement_trips_enriched.origin_hub_region
                        , movement_trips_enriched.dest_hub_id
                        , movement_trips_enriched.dest_hub_region
                        , movement_trips_enriched.movement_classification
                        , movement_trips_enriched.primary_driver_id
                        , movement_trips_enriched.primary_driver_name
                        , movement_trips_enriched.estimated_distance as distance
                        , movement_trips_enriched.total_order_volume
                        , movement_trips_enriched.expected_baseline_cbm as truck_capacity
                        , movement_trips_enriched.calculated_utilization
                        , case 
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' 
                                and movement_trips_enriched.movement_type = 'LAND_HAUL') then 'pri_land_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' 
                                and movement_trips_enriched.movement_type = 'AIR_HAUL') then 'pri_air_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' 
                                and movement_trips_enriched.movement_type = 'SEA_HAUL') then 'pri_sea_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'secondary') then 'secondary'
                            end as type
                        , case 
                            when origin.hub_id is not null then 1
                            when dest.hub_id is not null then 1
                            else 0
                            end as local_bus_flag
                    from movement_trips_enriched
                    left join mm_trips
                        on movement_trips_enriched.trip_id = mm_trips.trip_id
                    left join local_bus as origin
                        on movement_trips_enriched.origin_hub_id = origin.hub_id
                    left join local_bus as dest
                        on movement_trips_enriched.dest_hub_id = dest.hub_id
                    where movement_trips_enriched.status = 'COMPLETED'
                        and mm_trips.trip_id is not null
                ),

                pre_processing as (
                
                    select
                        system_id
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                        , trip_id
                        , origin_hub_id
                        , origin_hub_region
                        , dest_hub_id
                        , dest_hub_region
                        , primary_driver_id
                        , primary_driver_name
                        , distance
                        , total_order_volume
                        , truck_capacity
                        , calculated_utilization
                        , type
                        , case 
                            when movement_classification = 'CROSSDOCK - CROSSDOCK' and primary_driver_name like '%Intrazone%' then 'intrazone'
                            when movement_classification = 'CROSSDOCK - CROSSDOCK' 
                                and ((origin_hub_id = 15) or (primary_driver_name like '%-HN-%')) then 'hn_interzone'
                            when movement_classification = 'CROSSDOCK - CROSSDOCK' 
                                and ((origin_hub_id = 12) or (primary_driver_name like '%-HCM-%')) then 'hcm_interzone'
                            when movement_classification = 'CROSSDOCK - CROSSDOCK' then 'interzone'
                            when local_bus_flag = 1 then 'local_bus'
                            when origin_hub_region = 'HN' and dest_hub_region = 'HN' then 'hn_intracity'
                            when origin_hub_region = 'HCM' and dest_hub_region = 'HCM' then 'hcm_intracity'
                            else 'intrazone'
                            end as general_type
                    from base

                ),
                trip_types as (
                
                    select
                        system_id
                        , event_datetime
                        , month
                        , trip_id
                        , origin_hub_id
                        , origin_hub_region
                        , dest_hub_id
                        , dest_hub_region
                        , primary_driver_id
                        , primary_driver_name
                        , distance
                        , total_order_volume
                        , truck_capacity
                        , calculated_utilization
                        , type
                        , general_type
                        , case 
                            when general_type = 'local_bus' then 'local_bus'
                            when primary_driver_name like '%Back%' then 'virtual_trip'

                            when general_type = 'hcm_intracity' and origin_hub_id = 12 then 'first_trip_hcm_intracity'
                            when general_type = 'hcm_intracity' and dest_hub_id = 12 then 'last_trip_hcm_intracity'
                            when general_type = 'hn_intracity' and origin_hub_id = 15 then 'first_trip_hn_intracity'
                            when general_type = 'hn_intracity' and dest_hub_id = 15 then 'last_trip_hn_intracity'

                            when general_type = 'hn_interzone' and origin_hub_id = 15 then 'first_trip_hn_interzone'
                            when general_type = 'hcm_interzone' and origin_hub_id = 12 then 'first_trip_hcm_interzone'

                            when general_type = 'intrazone' and origin_hub_id = 12 then 'first_trip_wh_hcm'
                            when general_type = 'intrazone' and origin_hub_id = 15 then 'first_trip_wh_hn'
                            when general_type = 'intrazone' and dest_hub_id = 12 then 'last_trip_wh_hcm'
                            when general_type = 'intrazone' and dest_hub_id = 15 then 'last_trip_wh_hcm'

                            when general_type = 'intrazone' and origin_hub_id = 104202 then 'first_trip_wh_bmw'
                            when general_type = 'intrazone' and origin_hub_id in (1226,762,1264,805) then 'first_trip_wh_south_central'
                            when general_type = 'intrazone' and origin_hub_id = 1153 then 'first_trip_north_central'
                            when general_type = 'intrazone' and dest_hub_id in (1226,762,1264,805) then 'last_trip_wh_south_central'
                            else concat(general_type, '_following_trip')
                            end as trip_type
                    from pre_processing
                ),
                driver_details as (
                
                    select
                        trip_types.*
                        , mm_drivers_enriched.employment_type
                        , mm_drivers_enriched.vendor_id
                        , mm_drivers_enriched.vendor_name
                    from trip_types
                    left join mm_drivers_enriched
                        on trip_types.primary_driver_id = mm_drivers_enriched.driver_id

                ),
                trip_weight as (
                
                    select
                        trip_orders.trip_id
                        , sum(trip_orders.weight) as total_weight
                    from trip_orders
                    group by 1

                ),
                final as (
                
                    select
                        driver_details.system_id
                        , driver_details.event_datetime
                        , driver_details.month
                        , driver_details.trip_id
                        , driver_details.origin_hub_id
                        , driver_details.origin_hub_region
                        , driver_details.dest_hub_id
                        , driver_details.dest_hub_region
                        , driver_details.type
                        , driver_details.general_type
                        , driver_details.trip_type
                        , driver_details.employment_type
                        , driver_details.vendor_id
                        , driver_details.vendor_name
                        , driver_details.distance
                        , driver_details.total_order_volume
                        , driver_details.truck_capacity
                        , driver_details.calculated_utilization
                        , trip_weight.total_weight
                        , driver_details.distance * trip_weight.total_weight as total_distance_weight
                    from driver_details
                    left join trip_weight
                        on driver_details.trip_id = trip_weight.trip_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="pri_trip_cpp",
                jinja_template="""
                with base as (

                    select
                        *
                    from trip_details
                    where general_type in ('hn_interzone','hcm_interzone','interzone')
                    and trip_type != 'virtual_trip'

                ),    
                trip_costs as (
                
                    select
                        trip_id
                        , cost
                        , inferred_method
                    from trip_cost_tiers

                ),
                final as (
                
                    select
                        base.system_id
                        , base.trip_id
                        , base.general_type
                        , base.employment_type
                        , base.vendor_name
                        , trip_costs.cost/base.total_distance_weight as cpkgm
                        , trip_costs.inferred_method as remarks
                    from base
                    left join trip_costs
                        on base.trip_id = trip_costs.trip_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="sec_trip_cpp",
                jinja_template="""
                with base as (
                
                    select
                        *
                        , case 
                            when employment_type in ('In-House - Full-Time', 'In-House - Part-Time', 'Part-time / Freelance') then 'inhouse'
                            when employment_type in ('Outsourced - Manpower Agency', 'Outsourced - Vendors') then '3PL'
                            else employment_type
                            end as grouped_employment_type
                    from trip_details
                    where general_type not in ('hn_interzone','hcm_interzone','interzone')
                    and trip_type != 'virtual_trip'

                ),
                local_bus as (
                
                    select 
                        month
                        , general_type
                        , sum(total_distance_weight) as total_distance_weight
                    from base
                    where general_type = 'local_bus'
                    group by {{ range(1, 3) | join(',') }}

                ),
                intrazone as (
                
                    select 
                        month
                        , general_type
                        , grouped_employment_type
                        , sum(total_distance_weight) as total_distance_weight
                    from base
                    where general_type = 'intrazone'
                    group by {{ range(1, 4) | join(',') }}

                ),
                intracity as (
                
                    select
                        month
                        , general_type
                        , sum(total_distance_weight) as total_distance_weight
                    from base
                    where general_type in ('hn_intracity', 'hcm_intracity')
                    group by {{ range(1, 3) | join(',') }}

                ),
                local_bus_cpp as (
                
                    select
                        local_bus.month
                        , local_bus.general_type
                        , cost_card_cost_config_vn.cost
                        , cost_card_cost_config_vn.remarks
                        , cost_card_cost_config_vn.cost/local_bus.total_distance_weight as cpkgm
                    from local_bus
                    left join cost_card_cost_config_vn
                        on lower(cost_card_cost_config_vn.cost_segment) = 'mm'
                        and local_bus.general_type = lower(cost_card_cost_config_vn.type)
                        and local_bus.month = cost_card_cost_config_vn.created_month

                ),
                intrazone_cpp as (
                
                    select
                        intrazone.month
                        , intrazone.general_type
                        , intrazone.grouped_employment_type
                        , cost_card_cost_config_vn.cost
                        , cost_card_cost_config_vn.remarks
                        , cost_card_cost_config_vn.cost/intrazone.total_distance_weight as cpkgm
                    from intrazone
                    left join cost_card_cost_config_vn
                        on lower(cost_card_cost_config_vn.cost_segment) = 'mm'
                        and intrazone.general_type = lower(cost_card_cost_config_vn.type)
                        and lower(intrazone.grouped_employment_type) = lower(cost_card_cost_config_vn.employment_type)
                        and intrazone.month = cost_card_cost_config_vn.created_month

                ),
                intracity_cpp as (
                
                    select
                        intracity.month
                        , intracity.general_type
                        , cost_card_cost_config_vn.cost
                        , cost_card_cost_config_vn.remarks
                        , cost_card_cost_config_vn.cost/intracity.total_distance_weight as cpkgm
                    from intracity
                    left join cost_card_cost_config_vn
                        on lower(cost_card_cost_config_vn.cost_segment) = 'mm'
                        and intracity.general_type = lower(cost_card_cost_config_vn.type)
                        and intracity.month = cost_card_cost_config_vn.created_month

                ),
                final as (
                
                    select
                        base.system_id
                        , base.trip_id
                        , base.general_type
                        , base.employment_type
                        , base.vendor_name
                        , coalesce(local_bus_cpp.cpkgm, intrazone_cpp.cpkgm, intracity_cpp.cpkgm) as cpkgm
                        , coalesce(local_bus_cpp.remarks, intrazone_cpp.remarks, intracity_cpp.remarks) as remarks
                    from base
                    left join local_bus_cpp
                        on base.month = local_bus_cpp.month
                        and base.general_type = local_bus_cpp.general_type
                    left join intrazone_cpp
                        on base.month = intrazone_cpp.month
                        and base.general_type = intrazone_cpp.general_type
                        and base.grouped_employment_type = intrazone_cpp.grouped_employment_type
                    left join intracity_cpp
                        on base.month = intracity_cpp.month
                        and base.general_type = intracity_cpp.general_type

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="trip_cpp",
                jinja_template="""

                select * from pri_trip_cpp
                UNION ALL
                select * from sec_trip_cpp

                """,
            ),
            base.TransformView(
                view_name="fm_costs",
                jinja_template="""
                with first_pickup as (
                
                        select
                            order_id
                            , system_id
                            , min_by(route_driver_id, event_datetime) as driver_id
                        from pickup_scan_events
                        where source_table = 'inbound_scans'
                    group by {{ range(1, 3) | join(',') }}

                ),
                fm_base as (
                
                select
                    filtered_om.order_id
                    , filtered_om.created_month
                    , filtered_om.system_id
                    , filtered_om.rts_flag
                    , filtered_om.rts_trigger_datetime
                    , coalesce(filtered_om.pickup_datetime, filtered_om.inbound_datetime) as event_datetime
                    , coalesce(pickup_hub.region, inbound_hub.region) as region
                    , coalesce(pickup_hub.id, inbound_hub.id) as hub_id
                    , filtered_om.weight
                    , filtered_om.size
                    , case 
                        when filtered_om.pickup_datetime is not null then 'pickup'
                        when filtered_om.inbound_datetime is not null then 'inbound'
                        end as type
                    , first_mile_volume_orders.pickup_channel
                from filtered_om
                left join hubs_enriched as pickup_hub
                    on filtered_om.pickup_hub_id = pickup_hub.id
                left join hubs_enriched as inbound_hub
                    on filtered_om.inbound_hub_id = inbound_hub.id
                left join first_mile_volume_orders
                    on filtered_om.order_id = first_mile_volume_orders.order_id
                left join first_pickup
                    on filtered_om.order_id = first_pickup.order_id
                left join drivers_enriched
                    on first_pickup.driver_id = drivers_enriched.id
                where first_mile_volume_orders.pickup_channel not in ('Cross Border'
                                                                        , 'Cross Border (Direct Injection)'
                                                                        , 'Direct Injection'
                                                                        , 'Non-FM'
                                                                        , 'NV internal'
                                                                        , 'Order Set as Pickup Not Required'
                                                                        , '')

                ),
                fm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'fm' as cost_segment
                        , lower(region) as origin_hub_region
                        , hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , weight
                        , size
                        , type
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from fm_base

                ),
                final as (
                
                    select
                        fm_events.order_id
                        , fm_events.created_month
                        , fm_events.system_id
                        , fm_events.cost_segment
                        , fm_events.event_datetime
                        , fm_events.month
                        , fm_events.origin_hub_region
                        , fm_events.origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , fm_events.weight
                        , fm_events.size
                        , 'parcel' as type
                        , 'not applicable' as vn_mm_type
                        , 'not applicable' as vendor_name
                        , 'not applicable' as employment_type
                        , cost_card_cost_config_vn.cost
                        , fm_events.type as remarks
                        , 'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from fm_events
                    left join cost_card_cost_config_vn
                        on fm_events.cost_segment = lower(cost_card_cost_config_vn.cost_segment)
                        and fm_events.origin_hub_region = lower(cost_card_cost_config_vn.region)
                        and fm_events.size = lower(cost_card_cost_config_vn.size)
                        and fm_events.month = cost_card_cost_config_vn.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="sort_costs",
                jinja_template="""
                with sort_events as (
                
                    select
                        hub_journeys.order_id
                        , filtered_om.created_month
                        , hub_journeys.system_id
                        , 'sort' as cost_segment
                        , hub_journeys.event_datetime
                        , date_format(hub_journeys.event_datetime, 'yyyy-MM') as month
                        , hub_journeys.region as origin_hub_region
                        , hub_journeys.coalesce_hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , 'not applicable' as type
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null
                        and hub_journeys.facility_type = 'CROSSDOCK'

                ),
                final as (
                
                    select
                        sort_events.order_id
                        , sort_events.created_month
                        , sort_events.system_id
                        , sort_events.cost_segment
                        , sort_events.event_datetime
                        , sort_events.month
                        , sort_events.origin_hub_region
                        , sort_events.origin_hub_id
                        , sort_events.dest_hub_region
                        , sort_events.dest_hub_id
                        , sort_events.weight
                        , sort_events.size
                        , sort_events.type
                        , 'not applicable' as vn_mm_type
                        , 'not applicable' as vendor_name
                        , 'not applicable' as employment_type
                        , cost_card_cost_config_vn.cost
                        , cost_card_cost_config_vn.remarks
                        , 'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from sort_events
                    left join cost_card_cost_config_vn
                        on sort_events.cost_segment = lower(cost_card_cost_config_vn.cost_segment)
                        and sort_events.origin_hub_id = cost_card_cost_config_vn.hub_id
                        and sort_events.month = cost_card_cost_config_vn.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="lm_costs",
                jinja_template="""
                with lm_route_tagging as (
                
                    select distinct
                        route_id
                        , courier_type
                        , case 
                            when route_tags like '%SSS%' then 'sss'
                            when route_tags like '%LLL%' then 'lll'
                            when route_tags like '%XXL%' then 'xxl'
                            end as size
                    from fleet_performance_base_data

                ),
                lm_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.delivery_success_datetime as event_datetime
                        , coalesce(delivery_success_hub.region, dest_hub.region) as region
                        , coalesce(filtered_om.delivery_success_hub_id, filtered_om.dest_hub_id) as hub_id
                        , case 
                            when filtered_om.delivery_success_hub_id is not null then 'delivery'
                            when filtered_om.dest_hub_id is not null then 'proxy'
                            end as data_source
                        , case
                            when date(filtered_om.delivery_success_datetime) < date('2023-04-01') then 'na'
                            when filtered_om.rts_flag = 1 then 'rts_delivery'
                            when filtered_om.order_type = 'Return'
                                and coalesce(delivery_success_hub.region, dest_hub.region) in ('HN','HCM') 
                                and cost_card_rts_driver_types_vn.driver_type is not null 
                                then 'rts_delivery'
                            when filtered_om.rts_flag = 0 then 'forward_delivery'
                            end as type
                        , filtered_om.weight
                        , lm_route_tagging.size as route_id
                        , lm_route_tagging.size as route_size
                        , case 
                            when filtered_om.size = 'xxl' then 'xxl'
                            when filtered_om.size = 'xl' then 'lll'
                            when filtered_om.size in ('xxs','xs','s','m','l') then 'sss'
                            end as resize
                        , filtered_om.size as om_size
                    from filtered_om
                    left join hubs_enriched as delivery_success_hub
                        on filtered_om.delivery_success_hub_id = delivery_success_hub.id
                    left join hubs_enriched as dest_hub
                        on filtered_om.dest_hub_id = dest_hub.id
                    left join transactions
                        on filtered_om.order_id = transactions.order_id
                        and transactions.type = 'DD'
                        and transactions.status = 'Success'
                    left join lm_route_tagging
                        on transactions.route_id = lm_route_tagging.route_id
                    left join cost_card_rts_driver_types_vn
                        on lm_route_tagging.courier_type = cost_card_rts_driver_types_vn.driver_type
                    where
                        filtered_om.delivery_success_datetime is not null
                        and filtered_om.third_party_tracking_id is null

                ),
                lm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'lm' as cost_segment
                        , 'not applicable' as origin_hub_region
                        , 'not applicable' as origin_hub_id
                        , region as dest_hub_region
                        , hub_id as dest_hub_id
                        , weight
                        , case
                            when date(event_datetime) >= date('2023-04-01') then om_size
                            when region in ('HCM','HN') and route_size is not null then route_size
                            when region in ('HCM','HN') and route_size is null then 'sss'
                            when region in ('North','South') then resize
                            end as size
                        , type
                        , data_source
                        , route_id
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from lm_base

                ),
                final as (
                
                    select
                        lm_events.order_id
                        , lm_events.created_month
                        , lm_events.system_id
                        , lm_events.cost_segment
                        , lm_events.event_datetime
                        , lm_events.month
                        , lm_events.origin_hub_region
                        , lm_events.origin_hub_id
                        , lm_events.dest_hub_region
                        , lm_events.dest_hub_id
                        , lm_events.weight
                        , lm_events.size
                        , lm_events.type
                        , 'not applicable' as vn_mm_type
                        , 'not applicable' as vendor_name
                        , 'not applicable' as employment_type
                        , cost_card_cost_config_vn.cost
                        , lm_events.data_source as remarks
                        , lm_events.route_id as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from lm_events
                    left join cost_card_cost_config_vn
                        on lm_events.cost_segment = lower(cost_card_cost_config_vn.cost_segment)
                        and lm_events.dest_hub_id = cost_card_cost_config_vn.hub_id
                        and lm_events.size = lower(cost_card_cost_config_vn.size)
                        and lm_events.type = lower(cost_card_cost_config_vn.type)
                        and lm_events.month = cost_card_cost_config_vn.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="mm_costs",
                jinja_template="""
                with mm_base as (
                
                    select
                        trip_orders.order_id
                        , trip_orders.created_month
                        , trip_orders.system_id
                        , trip_orders.rts_trigger_datetime
                        , trip_orders.rts_flag
                        , trip_orders.weight
                        , trip_orders.size
                        , trip_details.trip_id
                        , trip_details.event_datetime
                        , trip_details.month as event_month
                        , trip_details.origin_hub_region
                        , trip_details.origin_hub_id
                        , trip_details.dest_hub_region
                        , trip_details.dest_hub_id
                        , trip_details.type
                        , trip_details.general_type
                        , trip_details.vendor_name
                        , trip_details.employment_type
                        , trip_details.distance
                        , trip_details.total_order_volume
                        , trip_details.truck_capacity
                        , trip_details.calculated_utilization
                    from trip_orders
                    left join trip_details
                        on trip_orders.trip_id = trip_details.trip_id

                ),
                mm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'mm' as cost_segment
                        , trip_id
                        , origin_hub_region
                        , origin_hub_id
                        , dest_hub_region
                        , dest_hub_id
                        , weight
                        , size
                        , type
                        , general_type as vn_mm_type
                        , vendor_name
                        , employment_type
                        , distance
                        , total_order_volume
                        , truck_capacity
                        , calculated_utilization
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                        , rts_trigger_datetime
                        , rts_flag
                    from mm_base
                    where event_datetime is not null

                ),
                final as (
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , mm_events.vn_mm_type
                        , mm_events.vendor_name
                        , mm_events.employment_type
                        , mm_events.weight * mm_events.distance * trip_cpp.cpkgm as cost
                        , trip_cpp.remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join trip_cpp
                        on mm_events.trip_id = trip_cpp.trip_id

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="hub_costs",
                jinja_template="""
                with hub_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.weight
                        , filtered_om.size
                        , hub_journeys.event_datetime
                        , hub_journeys.coalesce_hub_id
                        , hub_journeys.region
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null

                ),
                hub_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'hub' as cost_segment
                        , region as origin_hub_region
                        , coalesce_hub_id as origin_hub_id
                        , 'not applicable' as dest_hub_region
                        , 'not applicable' as dest_hub_id
                        , weight
                        , size
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from hub_base

                ),
                final as (
                
                    select
                        hub_events.order_id
                        , hub_events.created_month
                        , hub_events.system_id
                        , hub_events.cost_segment
                        , hub_events.event_datetime
                        , hub_events.month
                        , hub_events.origin_hub_region
                        , hub_events.origin_hub_id
                        , hub_events.dest_hub_region
                        , hub_events.dest_hub_id
                        , hub_events.weight
                        , hub_events.size
                        , cost_card_cost_config_vn.type
                        , 'not applicable' as vn_mm_type
                        , 'not applicable' as vendor_name
                        , 'not applicable' as employment_type
                        , cost_card_cost_config_vn.cost
                        , cost_card_cost_config_vn.remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from hub_events
                    left join cost_card_cost_config_vn
                        on hub_events.cost_segment = lower(cost_card_cost_config_vn.cost_segment)
                        and hub_events.origin_hub_id = cost_card_cost_config_vn.hub_id
                        and hub_events.month = cost_card_cost_config_vn.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with combined_costs as (
                
                    select * from fm_costs
                    UNION ALL
                    select * from sort_costs
                    UNION ALL
                    select * from mm_costs
                    UNION ALL
                    select * from lm_costs
                    UNION ALL
                    select * from hub_costs

                ),
                sequence as (
                    select 
                        *
                        , row_number() over (partition by order_id order by event_datetime, cost_segment, type) as event_sequence
                    from combined_costs

                ),
                final as (
                
                    select 
                        system_id
                        , order_id
                        , event_sequence
                        , event_datetime
                        , lower(origin_hub_region) as origin_hub_region
                        , origin_hub_id
                        , lower(dest_hub_region) as dest_hub_region
                        , dest_hub_id
                        , weight
                        , lower(size) as size
                        , lower(type) as type
                        , lower(vn_mm_type) as vn_mm_type
                        , lower(vendor_name) as vendor_name
                        , lower(employment_type) as employment_type
                        , cast(cost as double) as cost
                        , lower(remarks) as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                        , lower(cost_segment) as cost_segment
                        , month as event_month
                        , month as created_month
                        , created_month as order_created_month
                    from sequence
                    where month >= '2023-01' 

                )

                select * from final
                
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_EVENTS_VN,
        measurement_datetime=measurement_datetime,
        partition_by=("cost_segment", "event_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
