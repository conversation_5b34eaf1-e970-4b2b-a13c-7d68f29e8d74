import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.SHIPPER_PICKUP_ASSIGNEES_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.SHIPPER_PICKUP_ASSIGNEES_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    system_ids=(SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    all_core_tables = []
    for system_id in (SystemID.ID, SystemID.MY, SystemID.PH, SystemID.SG, SystemID.TH, SystemID.VN):
        core_tables = [
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS, view_name=f"reservations_{system_id}"
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ROUTE_WAYPOINT,
                view_name=f"route_waypoint_{system_id}",
            ),
        ]
        all_core_tables += core_tables

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                system_id=SystemID.ID,
                view_name="order_milestones_id",
            ),
        ),
        delta_tables=(
            *all_core_tables,
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).HUBS, view_name="hubs"),
            base.InputTable(path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS, view_name="shippers"),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_TYPES, view_name="driver_types"),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
            ),

        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="mitra",
                jinja_template="""
                /*
                ID pickup assignees are hardcoded as Mitra if the shipper's last inbounded order is dropped off at a DP
                */

                select
                    shipper_id
                    , 'id' as system_id
                    , max_by(if(dp_dropoff_datetime is not null, 'Mitra', null), inbound_datetime) as pickup_assignee
                from order_milestones_id
                group by 1
                having
                    pickup_assignee is not null
                """,
            ),
            base.TransformView(
                view_name="pickup_assignee_by_driver_type",
                jinja_template="""
                with
                    combined_sg as (
                        select
                            coalesce(waypoints_sg.id, route_waypoint_sg.waypoint_id) as waypoint_id
                            , coalesce(waypoints_sg.route_id, route_waypoint_sg.route_id) as route_id
                        from route_waypoint_sg
                        full outer join waypoints as waypoints_sg on
                            route_waypoint_sg.waypoint_id = waypoints_sg.legacy_id
                            and waypoints_sg.system_id='sg'
                    )

                select
                    reservations_sg.shipper_id
                    , 'sg' as system_id
                    , max_by(driver_types.name, reservations_sg.created_at) as pickup_assignee
                from reservations_sg
                inner join combined_sg on
                    reservations_sg.waypoint_id = combined_sg.waypoint_id
                inner join route_logs as route_logs_sg on
                    combined_sg.route_id = route_logs_sg.legacy_id
                    and route_logs_sg.system_id = 'sg'
                inner join drivers on
                    route_logs_sg.driver_id = drivers.id
                    and drivers.system_id = 'sg'
                inner join driver_types on
                    drivers.driver_type_id = driver_types.id
                    and drivers.system_id = driver_types.system_id
                group by 1
                """,
            ),
            base.TransformView(
                view_name="pickup_assignee_by_hub_name",
                jinja_template="""
                with
                {% for system_id in system_ids %}
                    {%- if not loop.first %}, {% endif %}
                    combined_{{ system_id }} as (

                        select
                            coalesce(
                                waypoints.legacy_id , route_waypoint_{{ system_id }}.waypoint_id
                            ) as waypoint_id
                            , coalesce(
                                waypoints.route_id , route_waypoint_{{ system_id }}.route_id
                            ) as route_id
                        from route_waypoint_{{ system_id }}
                        full outer join waypoints on
                            route_waypoint_{{ system_id }}.waypoint_id = waypoints.legacy_id
                            and waypoints.system_id = '{{ system_id }}'
                    )
                {%- endfor %}
                {% for system_id in system_ids %}
                select
                    reservations_{{ system_id }}.shipper_id
                    , '{{ system_id }}' as system_id
                    , max_by(hubs.name, reservations_{{ system_id }}.created_at) as pickup_assignee
                from reservations_{{ system_id }}
                inner join combined_{{ system_id }} on
                    reservations_{{ system_id }}.waypoint_id = combined_{{ system_id }}.waypoint_id
                inner join route_logs on
                    combined_{{ system_id }}.route_id = route_logs.legacy_id
                    and route_logs.system_id = '{{ system_id }}'
                inner join hubs on
                    route_logs.hub_id = hubs.hub_id
                    and hubs.system_id = '{{ system_id }}'
                group by 1
                {% if not loop.last %}union all{% endif %}
                {%- endfor %}
                """,
                jinja_arguments={"system_ids": (SystemID.ID, SystemID.MY, SystemID.PH, SystemID.TH, SystemID.VN),
                                 "system_id": system_id},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    shippers_clean as (

                        select
                            id
                            , lower(system_id) as system_id
                            , created_month
                        from shippers

                    )
                    , pickup_assignees_all as (

                        {% for view in views %}
                        select
                            shipper_id
                            , pickup_assignee
                            , system_id
                        from {{ view }}
                        {% if not loop.last %}union all{% endif %}
                        {%- endfor %}

                    ),
                    final as (

                        select
                            shippers_clean.id as shipper_id
                            , coalesce(
                                mitra.pickup_assignee, pickup_assignees_all.pickup_assignee
                            ) as pickup_assignee
                            , shippers_clean.system_id
                            , shippers_clean.created_month
                        from shippers_clean
                        left join pickup_assignees_all on
                            shippers_clean.id = pickup_assignees_all.shipper_id
                            and shippers_clean.system_id = pickup_assignees_all.system_id
                        left join mitra on
                            shippers_clean.id = mitra.shipper_id
                            and shippers_clean.system_id = mitra.system_id

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "views": (
                        "pickup_assignee_by_driver_type",
                        "pickup_assignee_by_hub_name",
                    ),
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_PICKUP_ASSIGNEES,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()