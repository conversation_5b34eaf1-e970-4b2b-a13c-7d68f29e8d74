import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS_MASKED,
    ),
)

pickup_success_job_type_start_date = "'2025-02'"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 4, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="gamification_my_order_events_pickup_success",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS,
                view_name="gamification_my_reservation_picked_up_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS,
                view_name="reservations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ROUTE_WAYPOINT,
                view_name="route_waypoint",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PICKUP_APPOINTMENT_JOBS,
                view_name="pickup_appointment_jobs",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.ControlProdGL(input_env, is_masked).PUDO_PICKUP_APPOINTMENT_JOBS,
                view_name="pudo_pickup_appointment_jobs",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).JOB_WAYPOINTS,
                view_name="job_waypoints",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).WAYPOINTS,
                view_name="waypoints",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS,
                view_name="drivers",
                system_id=system_id,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_TYPES,
                view_name="driver_types",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="last_route_waypoint",
                jinja_template="""
                with
                    combined as (
                        select
                            waypoint_id
                            , route_id
                            , created_at
                        from route_waypoint

                        union all

                        select
                            legacy_id as waypoint_id
                            , route_id
                            , created_at
                        from waypoints
                        where waypoints.system_id = '{{ system_id }}'
                    )

                select
                    waypoint_id
                    , max_by(route_id, created_at) as route_id
                from combined
                group by 1
                """,
                jinja_arguments={"system_id": system_id},
            ),

            base.TransformView(
                view_name="order_events_agg",
                jinja_template="""
                -- Original aggregation of pickup success events
                -- Have to make 2 different tables to preserve historical logic
                select
                    reservation_id
                    , min(pickup_arrival_datetime) as first_pickup_arrival_datetime
                from gamification_my_order_events_pickup_success
                where created_month < """ + pickup_success_job_type_start_date + """
                group by 1
                """,
            ),

            base.TransformView(
                view_name="order_events_agg_by_job_type",
                jinja_template="""
                -- Add job_type column to differentiate between the jobs as id can overlap
                select
                    reservation_id
                    , job_type
                    , min(pickup_arrival_datetime) as first_pickup_arrival_datetime
                from gamification_my_order_events_pickup_success
                where created_month >= """ + pickup_success_job_type_start_date + """
                group by 1,2
                """,
            ),

            base.TransformView(
                view_name="reservation_picked_up_orders_dedupe",
                jinja_template="""
                -- CTE to dedupe reservation_picked_up_orders due to timezone issue
                select
                    reservation_id
                    , data_source
                    , sum(picked_up_orders) as picked_up_orders
                from gamification_my_reservation_picked_up_orders
                group by 1,2
                """,
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with reservations_cte as (
                    select
                        cast(reservations.id as long) reservation_id
                        , cast(reservations.waypoint_id as long) waypoint_id
                        , case

                            {%- for id, name in id_to_status %}
                            when reservations.status = {{ id }} then '{{ name }}'
                            {%- endfor %}

                        end as status                        
                        , cast(route_logs.legacy_id as long) as route_id
                        , cast(route_logs.driver_id as bigint) as route_driver_id
                        , drivers.display_name as route_driver_name
                        , driver_types.name as route_driver_type
                        , coalesce(order_events_agg.first_pickup_arrival_datetime
                            , order_events_agg_by_job_type.first_pickup_arrival_datetime) as attempted_datetime
                        , reservation_picked_up_orders_dedupe.picked_up_orders
                    -- Parcels are only paid for jobs that have >= 6 orders picked up
                        , if(reservation_picked_up_orders_dedupe.picked_up_orders < 5, 0
                            , reservation_picked_up_orders_dedupe.picked_up_orders - 5) as payable_picked_up_orders
                        , 'Reservation' as data_source
                        , 'my' as system_id
                        , reservations.created_month
                    from reservations
                    left join last_route_waypoint on
                        reservations.waypoint_id = last_route_waypoint.waypoint_id
                    left join route_logs on
                        last_route_waypoint.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'
                    -- Add 2 joins,1st join is for before job_type data is populated
                    left join order_events_agg
                        on order_events_agg.reservation_id = reservations.id
                    -- Second join factors into account Job Type
                    left join order_events_agg_by_job_type
                        on order_events_agg_by_job_type.reservation_id = reservations.id
                        and order_events_agg_by_job_type.job_type = 'RESERVATION'
                    left join drivers
                        on route_logs.driver_id = drivers.id
                        and drivers.system_id = '{{ system_id }}'
                    left join driver_types
                        on drivers.driver_type_id = driver_types.id
                        and driver_types.system_id = '{{ system_id }}'
                    left join reservation_picked_up_orders_dedupe
                        on reservations.id = reservation_picked_up_orders_dedupe.reservation_id
                        and reservation_picked_up_orders_dedupe.data_source = 'Reservation'
                ),

                paj_cte as (
                    select
                        cast(pickup_appointment_jobs.id as long) as reservation_id
                        , cast(job_waypoints.waypoint_id as long) as waypoint_id
                        , pickup_appointment_jobs.status
                        , cast(route_logs.legacy_id as long) as route_id
                        , cast(route_logs.driver_id as bigint) as route_driver_id
                        , drivers.display_name as route_driver_name
                        , driver_types.name as route_driver_type
                        , coalesce(order_events_agg.first_pickup_arrival_datetime
                            , order_events_agg_by_job_type.first_pickup_arrival_datetime) as attempted_datetime
                        , reservation_picked_up_orders_dedupe.picked_up_orders
                    -- Parcels are only paid for jobs that have >= 6 orders picked up
                        , if(reservation_picked_up_orders_dedupe.picked_up_orders < 5, 0
                            , reservation_picked_up_orders_dedupe.picked_up_orders - 5) as payable_picked_up_orders
                        , 'Pickup Appointment Job' as data_source
                        , 'my' as system_id
                        , pickup_appointment_jobs.created_month
                    from pickup_appointment_jobs
                    left join job_waypoints on
                        pickup_appointment_jobs.id = job_waypoints.job_id
                        and job_waypoints.job_type = 'Pickup Appointment'
                        and job_waypoints.system_id = '{{ system_id }}'
                    left join waypoints on
                        job_waypoints.waypoint_id = waypoints.legacy_id
                        and waypoints.system_id = '{{ system_id }}'
                   left join route_logs on
                        waypoints.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    -- Add 2 joins,1st join is for before job_type data is populated
                    left join order_events_agg
                        on order_events_agg.reservation_id = pickup_appointment_jobs.id
                    -- Second join factors into account Job Type
                    left join order_events_agg_by_job_type
                        on order_events_agg_by_job_type.reservation_id = pickup_appointment_jobs.id
                        and order_events_agg_by_job_type.job_type = 'PICKUP_APPOINTMENT'
                    left join drivers
                        on route_logs.driver_id = drivers.id
                        and drivers.system_id = '{{ system_id }}'
                    left join driver_types
                        on drivers.driver_type_id = driver_types.id
                        and driver_types.system_id = '{{ system_id }}'
                    left join reservation_picked_up_orders_dedupe
                        on pickup_appointment_jobs.id = reservation_picked_up_orders_dedupe.reservation_id
                        and reservation_picked_up_orders_dedupe.data_source = 'Pickup Appointment Job'
                ),

                pudo_paj_cte as (
                    select
                        cast(pudo_pickup_appointment_jobs.id as long) as reservation_id
                        , cast(job_waypoints.waypoint_id as long) as waypoint_id
                        , pudo_pickup_appointment_jobs.status
                        , cast(route_logs.legacy_id as long) as route_id
                        , cast(route_logs.driver_id as bigint) as route_driver_id
                        , drivers.display_name as route_driver_name
                        , driver_types.name as route_driver_type
                        , coalesce(order_events_agg.first_pickup_arrival_datetime
                            , order_events_agg_by_job_type.first_pickup_arrival_datetime) as attempted_datetime
                        , reservation_picked_up_orders_dedupe.picked_up_orders
                    -- Parcels are only paid for jobs that have >= 6 orders picked up
                        , if(reservation_picked_up_orders_dedupe.picked_up_orders < 5, 0
                            , reservation_picked_up_orders_dedupe.picked_up_orders - 5) as payable_picked_up_orders
                        , 'Pudo Pickup Appointment Job' as data_source
                        , 'my' as system_id
                        , pudo_pickup_appointment_jobs.created_month
                    from pudo_pickup_appointment_jobs
                    left join job_waypoints on
                        pudo_pickup_appointment_jobs.id = job_waypoints.job_id
                        and job_waypoints.job_type = 'PUDO_PICKUP_APPOINTMENT'
                        and job_waypoints.system_id = '{{ system_id }}'
                    left join waypoints on
                        job_waypoints.waypoint_id = waypoints.legacy_id
                        and waypoints.system_id = '{{ system_id }}'
                   left join route_logs on
                        waypoints.route_id = route_logs.legacy_id
                        and route_logs.system_id = '{{ system_id }}'
                        and waypoints.system_id = '{{ system_id }}'
                    -- Add 2 joins,1st join is for before job_type data is populated
                    left join order_events_agg
                        on order_events_agg.reservation_id = pudo_pickup_appointment_jobs.id
                    -- Second join factors into account Job Type
                    left join order_events_agg_by_job_type
                        on order_events_agg_by_job_type.reservation_id = pudo_pickup_appointment_jobs.id
                        and order_events_agg_by_job_type.job_type = 'PUDO_PICKUP_APPOINTMENT'
                    left join drivers
                        on route_logs.driver_id = drivers.id
                        and drivers.system_id = '{{ system_id }}'
                    left join driver_types
                        on drivers.driver_type_id = driver_types.id
                        and driver_types.system_id = '{{ system_id }}'
                    left join reservation_picked_up_orders_dedupe
                        on pudo_pickup_appointment_jobs.id = reservation_picked_up_orders_dedupe.reservation_id
                        and reservation_picked_up_orders_dedupe.data_source = 'Pudo Pickup Appointment Job'
                )

                select *
                from reservations_cte
                union all
                select *
                from paj_cte
                union all
                select *
                from pudo_paj_cte
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "id_to_status": enumerate(("Pending", "Success", "Fail", "Reject", "Cancel")),
                },
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATIONS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()