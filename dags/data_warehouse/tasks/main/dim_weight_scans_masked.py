import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(
        data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
        data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        data_warehouse.HubsDAG.Task.PARCEL_MEASUREMENT_SCAN_ENRICHED_MASKED
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched"
            ), 
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DESTINATIONS,
                view_name="order_destinations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVE_TRACKING_IDS_ENRICHED,
                view_name="reserve_tracking_ids",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARCEL_MEASUREMENT_SCAN_ENRICHED,
                view_name="parcel_measurement_scan_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.order_event_id
                    , base.order_id
                    , order_destinations.tracking_id
                    , base.shipper_id
                    , base.user_id
                    , base.user_name
                    , base.user_grant_type
                    , base.device
                    , base.device_type
                    , base.scan_hub_id
                    , scan_hub.name as scan_hub_name
                    , scan_hub.sort_hub_flag as scan_hub_sort_flag
                    , order_destinations.delivery_dest_hub_id as dest_hub_id
                    , dest_hub.name as dest_hub_name
                    , base.raw_weight
                    , base.raw_length
                    , base.raw_width
                    , base.raw_height

                    {%- for type in ('old', 'new') %}
                    , base.{{ type }}_weight
                    , base.{{ type }}_length
                    , base.{{ type }}_width
                    , base.{{ type }}_height
                    , base.{{ type }}_parcel_size
                    , base.{{ type }}_vol_weight
                    {%- endfor %}

                    , cast(reserve_tracking_ids.weight as double) as shipper_weight
                    , cast(reserve_tracking_ids.length as double) as shipper_length
                    , cast(reserve_tracking_ids.width as double) as shipper_width
                    , cast(reserve_tracking_ids.height as double) as shipper_height
                    , lower(reserve_tracking_ids.size) as shipper_parcel_size
                    , base.scan_datetime
                    , if(
                        row_number() over (
                            partition by base.order_id, base.system_id
                            order by base.scan_datetime desc, base.order_event_id desc
                        ) = 1
                        , 1
                        , 0
                    ) as latest_scan_flag
                    , parcel_measurement_scan_enriched.dimweight_image_link_flag as machine_image_flag
                    , greatest(parcel_measurement_scan_enriched.height_image_link_flag
                            , parcel_measurement_scan_enriched.length_image_link_flag
                            , parcel_measurement_scan_enriched.width_image_link_flag
                            , parcel_measurement_scan_enriched.weight_image_link_flag) as manual_image_flag
                    , base.system_id
                    , base.created_month
                from dim_weight_scans_base as base
                left join order_destinations on
                    base.order_id = order_destinations.order_id
                    and base.system_id = order_destinations.system_id
                left join reserve_tracking_ids on
                    order_destinations.tracking_id = reserve_tracking_ids.tracking_id
                    and base.system_id = reserve_tracking_ids.system_id
                left join hubs_enriched as scan_hub on
                    base.scan_hub_id = scan_hub.id
                    and base.system_id = scan_hub.system_id
                left join hubs_enriched as dest_hub on
                    order_destinations.delivery_dest_hub_id = dest_hub.id
                    and base.system_id = dest_hub.system_id
                left join parcel_measurement_scan_enriched on
                    base.scan_uuid = parcel_measurement_scan_enriched.scan_uuid
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DIM_WEIGHT_SCANS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
        enable_compaction=False,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
