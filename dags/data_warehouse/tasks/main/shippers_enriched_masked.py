import sys

from pyspark.sql import SparkSession

from common.date import to_measurement_datetime_str
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        data_warehouse.ShippersDAG.Task.SHIPPER_MILESTONES_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceExportDAG.DAG_ID,
            task_id=data_warehouse.SalesforceExportDAG.Task.SHIPPER_PICKUP_ASSIGNEES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShipperLifetimeValueDAG.DAG_ID,
            task_id=data_warehouse.ShipperLifetimeValueDAG.Task.SHIPPER_LIFETIME_VALUES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShipperLifetimeValueDAG.DAG_ID,
            task_id=data_warehouse.ShipperLifetimeValueDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES_MASKED,
        ),
    ),
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_LIFETIME_VALUES,
                view_name="shipper_lifetime_values",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARENT_SHIPPER_LIFETIME_VALUES,
                view_name="parent_shipper_lifetime_values",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_MILESTONES,
                view_name="shipper_milestones",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_PICKUP_ASSIGNEES,
                view_name="shipper_pickup_assignees",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="dates",
                jinja_template="""
                select
                    system_id
                    , date_trunc(
                        'month', from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }})
                    ) - interval 1 month as previous_month
                    , date_trunc(
                        'month', from_utc_timestamp('{{ measurement_datetime_utc }}', {{ get_local_timezone }})
                    ) as current_month
                from values ('{{ system_ids | join ("'), ('")}}') as (system_id)
                """,
                jinja_arguments={
                    "system_ids": {
                        SystemID.ID,
                        SystemID.MM,
                        SystemID.MY,
                        SystemID.PH,
                        SystemID.SG,
                        SystemID.TH,
                        SystemID.VN,
                    },
                    "measurement_datetime_utc": measurement_datetime,
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="previous_month_ltv",
                jinja_template="""
                with
                    base as (

                        select
                             shipper_id
                            , system_id
                            , report_date
                            , max_by(alive_probability, measurement_datetime) as alive_probability
                        from shipper_lifetime_values
                        where measurement_datetime <= '{{ measurement_datetime_str }}'
                        group by 1, 2, 3

                    )
                    , final as (

                        select
                             shipper_id
                            , base.system_id
                            , min(base.alive_probability) as min_alive_probability
                            , avg(base.alive_probability) as avg_alive_probability
                        from base
                        left join dates on
                            base.system_id = dates.system_id
                        where
                            date_trunc('month', base.report_date) = dates.previous_month
                        group by 1, 2

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime_str": to_measurement_datetime_str(measurement_datetime),
                },
            ),
            base.TransformView(
                view_name="parent_milestones",
                jinja_template="""
                select
                    parent_id_coalesce
                    , min(first_order_completion_date) as first_order_completion_date
                from shipper_milestones
                group by 1
                """,
            ),
            base.TransformView(
                view_name="parent_category",
                jinja_template="""
                select
                    volume.parent_id_coalesce
                    , max_by(volume.parent_coalesce_category, volume.completion_month) as last_category
                    , max(volume.completion_month) as last_category_month
                from shipper_completion_vol_monthly as volume
                left join dates on
                    volume.system_id = dates.system_id
                where
                    -- Exclude current month because volume related classifications (e.g. Uptrader/ Downtrader)
                    -- are not accurate until the month is complete.
                    volume.completion_month < dates.current_month
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select distinct
                    shipper.id
                    , shipper.shipper_name
                    , case
                        when
                            -- lazada
                            shipper.parent_id_coalesce in (283987, 291101, 332853, 48930, 324763, 341159, 79238, 341153,
                            341167, 68220, 68968, 341107, 341121, 102516, 317481, 102534, 102518, 102630, 319845)
                            -- shopee
                            or shipper.parent_id_coalesce in (42982, 4187666, 1340474, 1449130, 160271, 3585384, 484710,
                            702985, 1215982, 216977, 1069100, 497542, 700865)
                            -- tiktok
                            or shipper.parent_id_coalesce in (7717788, 7474545, 7823651, 9090233, 9078111)
                        then regexp_extract(shipper.shipper_name, '\\\\((.*?)\\\\)',1)
                    end as seller_name
                    , shipper.country
                    , shipper.parent_id
                    , shipper.parent_name
                    , shipper.parent_id_coalesce
                    , shipper.parent_name_coalesce
                    , shipper.parent_corporate_id
                    , shipper.parent_corporate_name
                    , shipper.branch_id
                    , shipper.reporting_name
                    , shipper.marketplace
                    , shipper.billing_name
                    , shipper.sales_person
                    , shipper.sales_person_code
                    , shipper.sales_person_team
                    , shipper.industry
                    , shipper.distribution_channel_name
                    , shipper.account_type_name
                    , shipper.account_creation_source
                    , shipper.prepaid_account
                    , shipper.service_level
                    , shipper.frozen_flag

                    {%- for setting in notification_settings %}
                    , shipper.{{ setting }}_notification_flag
                    {%- endfor %}

                    , shipper_pickup_assignees.pickup_assignee
                    , shipper.pickup_scan_exclusion_flag
                    , shipper.pickup_scan_exclusion_reason
                    , shipper.platform_flag
                    , shipper.onboarded_date
                    , shipper.active
                    , shipper.deleted_date
                    , shipper.external_ref
                    , shipper.sf_acc_id
                    , shipper.sf_parent_acc_id_coalesce
                    , shipper.sf_parent_acc_name_coalesce
                    , shipper.sf_parent_acc_shipper_id_coalesce
                    , shipper.sf_shipper_origin
                    , shipper.sf_shipping_type
                    , shipper.sf_nv_product_line
                    , shipper.sf_expected_monthly_vol
                    , shipper.sf_salesperson
                    , shipper.sf_salesperson_alias
                    , shipper.sf_salesperson_manager
                    , shipper.sf_sales_territory
                    , shipper.sf_sales_team
                    , shipper.sf_sales_channel
                    , shipper.sf_business_type
                    , shipper.sf_latest_lead_gen_channel
                    , shipper.sf_latest_lead_source
                    , shipper.sf_latest_lead_source_details
                    , shipper.sales_channel
                    , shipper.original_sales_channel
                    , shipper.business_unit
                    , shipper.acquisition_endpoint
                    , shipper_milestones.first_order_placed_date
                    , datediff(
                        shipper_milestones.first_order_placed_date, shipper.onboarded_date
                    ) as days_to_activation
                    , shipper_milestones.last_order_placed_date
                    , shipper_milestones.first_order_completion_date
                    , shipper_milestones.order_frequency
                    , parent_milestones.first_order_completion_date as parent_coalesce_first_order_completion_date

                    , case
                        when months_between(dates.previous_month, parent_category.last_category_month) <= 2
                            then parent_category.last_category
                        when months_between(dates.previous_month, parent_category.last_category_month) >= 3
                            then 'Inactive'
                        when months_between(dates.previous_month, date_trunc('month', shipper.onboarded_date)) >= 3
                            then 'Dormant'
                    end as parent_coalesce_category_agg

                    , latest_ltv.alive_probability
                    , latest_ltv.lifetime_value
                    , parent_latest_ltv.alive_probability as parent_alive_probability
                    , parent_latest_ltv.lifetime_value as parent_lifetime_value
                    , previous_month_ltv.min_alive_probability as previous_month_min_alive_probability
                    , previous_month_ltv.avg_alive_probability as previous_month_avg_alive_probability
                    , case
                        when shipper.system_id = 'ph'
                            then case
                                when shipper.sales_channel in ('Partnerships', 'Cross Border')
                                    then case
                                        {% for name, parent_id_coalesce in
                                        country_reporting_segment_to_parent_id_coalesce.items() %}
                                            when shipper.parent_id_coalesce in ({{ parent_id_coalesce | join(",") }})
                                                then '{{ name }}'
                                        {%- endfor %}
                                        else shipper.sales_channel
                                    end
                                else shipper.sales_channel
                            end
                        else null
                    end as country_reporting_segment

                    , shipper.system_id
                    , shipper.created_month
                from shipper_attributes as shipper
                left join shipper_milestones on
                    shipper.id = shipper_milestones.shipper_id
                left join parent_milestones on
                    shipper.parent_id_coalesce = parent_milestones.parent_id_coalesce
                left join parent_category on
                    shipper.parent_id_coalesce = parent_category.parent_id_coalesce
                left join shipper_pickup_assignees on
                    shipper.id = shipper_pickup_assignees.shipper_id
                left join dates on
                    shipper.system_id = dates.system_id
                left join previous_month_ltv on
                    shipper.id = previous_month_ltv.shipper_id
                    and shipper.system_id = previous_month_ltv.system_id
                left join shipper_lifetime_values as latest_ltv on
                    shipper.id = latest_ltv.shipper_id
                    and shipper.system_id = latest_ltv.system_id
                    and latest_ltv.measurement_datetime = '{{ measurement_datetime_str }}'
                left join parent_shipper_lifetime_values as parent_latest_ltv on
                    shipper.sf_parent_acc_id_coalesce = parent_latest_ltv.parent_id
                    and shipper.system_id = parent_latest_ltv.system_id
                    and parent_latest_ltv.measurement_datetime = '{{ measurement_datetime_str }}'
                """,
                jinja_arguments={
                    "notification_settings": (
                        "pickup_fail_customer",
                        "pickup_fail_shipper",
                        "transit_customer",
                        "transit_shipper",
                        "delivery_fail_customer",
                        "delivery_fail_shipper",
                        "completed_customer",
                        "completed_shipper",
                    ),
                    "measurement_datetime_str": to_measurement_datetime_str(measurement_datetime),
                    "country_reporting_segment_to_parent_id_coalesce": {
                        "Shopee": (160271, 484710, 1449130, 78426),
                        "Lazada": (79238, 341153, 341159, 7443955),
                        "Grab": (849952,),
                        "SPX": (3585384,),
                        "TikTok": (7823651,),
                        "TikTok XB": (7314943, 10416620),
                    },
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
