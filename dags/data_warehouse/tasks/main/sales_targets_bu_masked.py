import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GSheetsDAG.Task.SALES_TARGETS_BU_MASKED + ".py",
    task_name=data_warehouse.GSheetsDAG.Task.SALES_TARGETS_BU_MASKED,
    system_ids=(SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="id_views", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="my_views", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="th_views", partition_columns=("system_id",)),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views", partition_columns=("system_id",)),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).SALES_TARGETS_BU,
                view_name="sales_targets_bu_raw",
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_sales_targets",
                jinja_template="""
                with
                    latest_vol_target as (

                        select *
                        from
                            (
                            select
                            *
                            , row_number() over(
                                partition by country, period, sales_channel order by updated_at desc
                            ) as is_latest_target
                            from sales_targets_bu_raw


                            )
                        where is_latest_target  = 1
                        order by period desc

                    )

                    select
                          business_unit
                        , country
                        , date(created_at) as created_at
                        , cast(monthly_value as int) as monthly_value
                        , date(period) as period
                        , parent_reporting_segment
                        , reporting_segment
                        , sales_channel
                        , sales_channel_id
                        , date(updated_at) as updated_at
                        , country as system_id
                    from latest_vol_target

            """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALES_TARGETS_BU,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
