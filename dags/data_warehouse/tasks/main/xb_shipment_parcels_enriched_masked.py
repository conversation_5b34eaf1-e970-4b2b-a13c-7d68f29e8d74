import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_SHIPMENT_PARCELS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_SHIPMENT_PARCELS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).SHIPMENT_PARCELS,
                view_name="shipment_parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).SHIPMENTS,
                view_name="shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).VENDORS,
                view_name="vendors",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="selected_shipment_parcels",
                jinja_template="""
                with base as (

                    select
                        parcel_id
                        , max_by(id, created_at) filter(where status = 0) as id_backup
                        , max_by(id, created_at) filter(where status > 0) as id_main
                    from shipment_parcels
                    where parcel_id is not null
                    group by 1

                )

                select
                    base.parcel_id
                    , coalesce(base.id_main, base.id_backup) as shipment_parcel_id
                    , date_format(parcels.created_at, 'yyyy-MM') as created_month
                from base
                inner join parcels
                    on base.parcel_id = parcels.id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.parcel_id
                    , shipment_parcels.shipment_id
                    , shipment_parcels.requested_bag_tracking_id as vendor_bag_id
                    , shipments.reference_id as mawb
                    , shipments.vendor_id as linehaul_vendor_id
                    , vendors.name as linehaul_vendor_name
                    , case shipments.type
                    {%- for type, name in shipment_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                        else 'Error'
                    end as shipment_type
                    , get_json_object(shipments.metadata, '$.vessel_no') as shipment_vessel_no
                    , case cast(get_json_object(shipments.metadata, '$.transport_service') as int)
                    {%- for ts_code, name in shipment_transport_service_to_name.items() %}
                        when {{ ts_code }} then '{{ name }}'
                    {%- endfor %}
                    end as shipment_booking_type
                    , shipments.origin_port as shipment_origin_port
                    , shipments.origin_country as shipment_origin_country
                    , shipments.destination_port as shipment_destination_port
                    , shipments.destination_country as shipment_destination_country
                    , case shipments.status
                    {%- for status, name in shipment_status_to_name.items() %}
                        when {{ status }} then '{{ name }}'
                    {%- endfor %}
                        else 'Error'
                    end as shipment_status
                    , get_json_object(shipments.metadata, '$.notes') as shipment_notes
                    , 'gl' as system_id
                    , base.created_month
                from selected_shipment_parcels base
                left join shipment_parcels as shipment_parcels
                    on base.shipment_parcel_id = shipment_parcels.id
                left join shipments as shipments
                    on shipment_parcels.shipment_id = shipments.id
                left join vendors
                    on shipments.vendor_id = vendors.id
                """,
                jinja_arguments={
                    "shipment_type_to_name":{
                        1:"Land",
                        2:"Air",
                        3:"Sea",
                    },
                    "shipment_status_to_name":{
                        0:"Draft",
                        1:"Confirmed",
                        2:"In Transit",
                        3:"Completed",
                        4:"Partially Completed",
                        5:"Cancelled",
                        6:"Failed",
                    },
                    "shipment_transport_service_to_name":{
                        1:"Non-Chartered Flight",
                        2:"Chartered Flight",
                    },
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_SHIPMENT_PARCELS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
