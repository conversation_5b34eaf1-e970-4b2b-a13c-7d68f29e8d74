import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_CLAIM_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_CLAIM_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.SalesforceDAG.Task.SALESFORCE_CLAIM_ENRICHED_BASE_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="salesforce"),),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CLAIM_ENRICHED_BASE,
                view_name="salesforce_claim_enriched_base",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    id
                    , status
                    , case_number
                    , creation_datetime
                    , closed_datetime
                    , is_closed
                    , closed_reason
                    , invalid_reason
                    , case_owner_fsr_sales_team
                    , tracking_id
                    , cs_issue_type
                    , cs_issue_subtype
                    , account_id
                    , account_name
                    , account_sales_channel
                    , origin
                    , shipper_id
                    , order_outcome
                    , order_id
                    , is_parent
                    , parent_id
                    , currency_iso_code
                    , invoice_expiry_date
                    , invoice_value
                    , liability_cap
                    , owner_id
                    , invoice_date
                    , days_before_expiry
                    , cod_amount
                    , insured_value
                    , case_history_id
                    , case_history_created_by_id
                    , pending_payment_datetime
                    , recovery_response_business_days
                    , claim_process_payment_business_days
                    , system_id
                    , created_month
                from salesforce_claim_enriched_base
                """,
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_CLAIM_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
