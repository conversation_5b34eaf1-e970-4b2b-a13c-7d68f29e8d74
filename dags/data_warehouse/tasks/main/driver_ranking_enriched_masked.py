import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.DRIVER_RANKING_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.DRIVER_RANKING_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="current_month_ranking",
                jinja_template="""
                SELECT
                    current_date as date_of_extract
                    , fpbd.country as country
                    , fpbd.courier_type as courier_type
                    , fpbd.courier_id
                    , de.first_name as courier_name
                    , de.hub_id
                    , de.hub_name as courier_station
                    , de.hub_region as courier_region
                    , fpbd.created_month
                    , sum(fpbd.parcels_delivered) as parcels_delivered_cm
                    , CASE WHEN (sum(fpbd.parcels_delivered) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type ORDER BY sum(fpbd.parcels_delivered) DESC)
                    END as country_delivery_rank
                    , CASE WHEN (sum(fpbd.parcels_delivered) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type, de.hub_region
                                ORDER BY sum(fpbd.parcels_delivered) DESC)
                    END as region_delivery_rank
                    , CASE WHEN (sum(fpbd.parcels_delivered) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type, de.hub_region, de.hub_id
                                ORDER BY sum(fpbd.parcels_delivered) DESC)
                    END as hub_delivery_rank
                    , sum(fpbd.parcels_picked_up) as parcels_picked_up_cm
                    , CASE WHEN (sum(parcels_picked_up) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type ORDER BY sum(fpbd.parcels_picked_up) DESC)
                    END as country_pickup_rank
                    , CASE WHEN (sum(parcels_picked_up) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type, de.hub_region
                                ORDER BY sum(fpbd.parcels_picked_up) DESC)
                    END as region_pickup_rank
                    , CASE WHEN (sum(parcels_picked_up) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd.country, fpbd.courier_type, de.hub_region, de.hub_id
                                ORDER BY sum(fpbd.parcels_picked_up) DESC)
                    END as hub_pickup_rank
                FROM fleet_performance_base_data as fpbd
                INNER JOIN drivers_enriched as de
                    ON fpbd.courier_id = de.id
                WHERE
                    month(fpbd.route_date) = month(current_date)
                    AND year(fpbd.route_date) = year(current_date)
                    AND fpbd.courier_type <> 'Mitra - Fleet'
                GROUP BY 1,2,3,4,5,6,7,8,9
                ORDER BY 1,2,3,4,5,6,7,8
                """,
            ),
            base.TransformView(
                view_name="previous_month_volumes",
                jinja_template="""
                SELECT
                    fpbd1.country as ctry
                    , fpbd1.courier_type as ctype
                    , fpbd1.courier_id as courier_id
                    , sum(fpbd1.parcels_delivered) as parcels_delivered_lm
                    , sum(fpbd1.parcels_picked_up) as parcels_picked_up_lm
                FROM fleet_performance_base_data as fpbd1
                WHERE
                    month(fpbd1.route_date) = month(current_date - INTERVAL '1' MONTH)
                    AND year(fpbd1.route_date) = year(current_date - INTERVAL '1' MONTH)
                    AND fpbd1.courier_type <> 'Mitra - Fleet'
                GROUP BY 1,2,3
                ORDER BY 1,2,3
                """,
            ),
            base.TransformView(
                view_name="base_query_lm1",
                jinja_template="""
                SELECT
                    fpbd1.country as ctry
                    , fpbd1.courier_type as ctype
                    , fpbd1.courier_id as courier_id
                    , sum(fpbd1.parcels_delivered) as parcels_delivered_lm
                    , CASE WHEN (sum(fpbd1.parcels_delivered) = 0) THEN 0
                        ELSE dense_rank() OVER
                            (PARTITION BY fpbd1.country, fpbd1.courier_type
                                ORDER BY sum(fpbd1.parcels_delivered) DESC)
                    END as country_delivery_rank_lm
                FROM fleet_performance_base_data as fpbd1
                WHERE
                    month(fpbd1.route_date) = month(current_date - INTERVAL '1' MONTH)
                    AND year(fpbd1.route_date) = year(current_date - INTERVAL '1' MONTH)
                    AND fpbd1.courier_type <> 'Mitra - Fleet'
                GROUP BY 1,2,3
                ORDER BY 1,2,3
                """,
            ),
            base.TransformView(
                view_name="previous_month_ranking_lm",
                jinja_template="""
                WITH
                    base_query_lm1 AS (
                    SELECT
                        fpbd1.country as ctry
                        , fpbd1.courier_type as ctype
                        , fpbd1.courier_id as courier_id
                        , sum(fpbd1.parcels_delivered) as parcels_delivered_lm
                        , CASE WHEN (sum(fpbd1.parcels_delivered) = 0) THEN 0
                            ELSE dense_rank() OVER
                                (PARTITION BY fpbd1.country, fpbd1.courier_type
                                    ORDER BY sum(fpbd1.parcels_delivered) DESC)
                        END as country_delivery_rank_lm
                    FROM fleet_performance_base_data as fpbd1
                    WHERE
                        month(fpbd1.route_date) = month(current_date - INTERVAL '1' MONTH)
                        AND year(fpbd1.route_date) = year(current_date - INTERVAL '1' MONTH)
                        AND fpbd1.courier_type <> 'Mitra - Fleet'
                    GROUP BY 1,2,3
                    ORDER BY 1,2,3
                    ),
                    base_query_lm2 AS (
                    SELECT
                        base_query_lm1.ctry as country
                        , base_query_lm1.ctype
                        , base_query_lm1.courier_id
                        , base_query_lm1.parcels_delivered_lm as pdlm
                        , base_query_lm1.country_delivery_rank_lm
                    FROM base_query_lm1
                    WHERE
                        base_query_lm1.country_delivery_rank_lm <= 10
                        AND base_query_lm1.parcels_delivered_lm > 0
                    )

                SELECT
                    base_query_lm2.country as country
                    , base_query_lm2.ctype as courier_type
                    , max(base_query_lm2.pdlm) as max_parcels_lm
                    , max(base_query_lm2.country_delivery_rank_lm) as max_rank_lm
                FROM base_query_lm2
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="previous_month_ranking_fm",
                jinja_template="""
                WITH
                    base_query_fm1 AS (
                    SELECT
                        fpbd1.country as ctry
                        , fpbd1.courier_type as ctype
                        , fpbd1.courier_id
                        , sum(fpbd1.parcels_picked_up) as parcels_picked_up_lm
                        , CASE WHEN (sum(fpbd1.parcels_picked_up) = 0) THEN 0
                            ELSE dense_rank() OVER
                                (PARTITION BY fpbd1.country, fpbd1.courier_type
                                    ORDER BY sum(fpbd1.parcels_picked_up) DESC)
                        END as country_pickup_rank_fm
                    FROM fleet_performance_base_data as fpbd1
                    WHERE
                         month(fpbd1.route_date) = month(current_date - INTERVAL '1' MONTH)
                         AND year(fpbd1.route_date) = year(current_date - INTERVAL '1' MONTH)
                         AND fpbd1.courier_type <> 'Mitra - Fleet'
                    GROUP BY 1,2,3
                    ORDER BY 1,2,3
                    ),
                    base_query_fm2 AS (
                    SELECT
                        base_query_fm1.ctry as country
                        , base_query_fm1.ctype
                        , base_query_fm1.courier_id
                        , base_query_fm1.parcels_picked_up_lm as ppfm
                        , base_query_fm1.country_pickup_rank_fm
                    FROM base_query_fm1
                    WHERE
                        base_query_fm1.country_pickup_rank_fm <= 10
                        AND base_query_fm1.parcels_picked_up_lm > 0
                    )

                SELECT
                    base_query_fm2.country as country
                    , base_query_fm2.ctype as courier_type
                    , max(base_query_fm2.ppfm) as max_parcels_fm
                    , max(base_query_fm2.country_pickup_rank_fm) as max_rank_fm
                FROM base_query_fm2
                GROUP BY 1,2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT
                    cast(
                        date_format(current_month_ranking.date_of_extract, 'dd-MMM-yyyy') as string
                    ) as extract_date
                    , cast(current_month_ranking.country as string) as country
                    , cast(current_month_ranking.courier_type as string) as courier_type
                    , cast(current_month_ranking.courier_id as bigint) as courier_id
                    , cast(current_month_ranking.courier_name as string) as courier_name
                    , cast(current_month_ranking.hub_id as int) as hub_id
                    , cast(current_month_ranking.courier_station as string) as courier_station
                    , cast(current_month_ranking.courier_region as string) as courier_region
                    , cast(previous_month_volumes.parcels_delivered_lm as bigint) as parcels_delivered_last_month
                    , cast(current_month_ranking.parcels_delivered_cm as bigint) as parcels_delivered_mtd
                    , cast(current_month_ranking.country_delivery_rank as bigint) as country_delivery_rank
                    , cast(current_month_ranking.region_delivery_rank as bigint) as region_delivery_rank
                    , cast(current_month_ranking.hub_delivery_rank as bigint) as hub_delivery_rank
                    , cast(previous_month_ranking_lm.max_rank_lm as bigint) as max_rank_lm
                    , cast(previous_month_ranking_lm.max_parcels_lm as bigint) as max_parcels_lm
                    , cast(previous_month_volumes.parcels_picked_up_lm as bigint) as parcels_picked_up_last_month
                    , cast(current_month_ranking.parcels_picked_up_cm as bigint) as parcels_picked_up_mtd
                    , cast(current_month_ranking.country_pickup_rank as bigint) as country_pickup_rank
                    , cast(current_month_ranking.region_pickup_rank as bigint) as region_pickup_rank
                    , cast(current_month_ranking.hub_pickup_rank as bigint) as hub_pickup_rank
                    , cast(previous_month_ranking_fm.max_rank_fm as bigint) as max_rank_fm
                    , cast(previous_month_ranking_fm.max_parcels_fm as bigint) as max_parcels_fm
                    , current_month_ranking.created_month
                FROM current_month_ranking
                LEFT JOIN previous_month_volumes
                    ON current_month_ranking.courier_id = previous_month_volumes.courier_id
                    AND current_month_ranking.country = previous_month_volumes.ctry
                LEFT JOIN previous_month_ranking_lm
                    ON current_month_ranking.country = previous_month_ranking_lm.country
                    AND current_month_ranking.courier_type = previous_month_ranking_lm.courier_type
                LEFT JOIN previous_month_ranking_fm
                    ON current_month_ranking.country = previous_month_ranking_fm.country
                    AND current_month_ranking.courier_type = previous_month_ranking_fm.courier_type
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVER_RANKING_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
