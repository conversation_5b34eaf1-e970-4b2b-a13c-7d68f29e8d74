import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_HUB_MOVEMENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_HUB_MOVEMENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(data_warehouse.OrderEventsDAG.Task.ORDER_MOVEMENTS_MASKED,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MOVEMENTS,
                view_name="order_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                SELECT country
                       , order_id
                       , granular_status
                       , hub_id
                       , hub_sequence
                       , min_by(created_month, entry_datetime) AS created_month
                       , min_by(order_event_id, entry_datetime) AS order_event_id
                       , MIN(entry_datetime) AS entry_datetime
                       , MAX(exit_datetime) AS exit_datetime
                FROM
                    (SELECT *
                            , SUM(IF(LAG(hub_id, 1) OVER(PARTITION BY order_id
                                                         ORDER BY entry_datetime, order_event_id) = hub_id, 0, 1))
                                  OVER(PARTITION BY order_id
                                       ORDER BY entry_datetime, order_event_id) AS hub_sequence
                     FROM order_movements
                     WHERE hub_id IS NOT NULL)
                GROUP BY {{ range(1, 6) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT order_event_id
                       , country
                       , order_id
                       , granular_status
                       , hub_id
                       , hub_sequence
                       , entry_datetime
                       , exit_datetime
                       , cast(round((to_unix_timestamp(exit_datetime,'yyyy-MM-dd HH:mm:ss')
                                     - to_unix_timestamp(entry_datetime, 'yyyy-MM-dd HH:mm:ss'))/60, 0) AS bigint)
                         AS duration_minutes
                       , datediff(exit_datetime, entry_datetime) AS duration_days
                       , created_month
                FROM base
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_MOVEMENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
