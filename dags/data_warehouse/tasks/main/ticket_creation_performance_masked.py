import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.TICKET_CREATION_PERFORMANCE_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.TICKET_CREATION_PERFORMANCE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.RecoveryDAG.Task.EXCLUSION_REQUESTS_MASKED,
        data_warehouse.RecoveryDAG.Task.EXCLUSION_REQUEST_DETAILS_MASKED,
        data_warehouse.RecoveryDAG.Task.TICKET_CREATION_LAST_SCANS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_HUB_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_RECOVERY_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_HUB_MOVEMENTS,
                view_name="order_hub_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TICKET_CREATION_LAST_SCANS,
                view_name="ticket_creation_last_scans",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_RECOVERY,
                view_name="calendar_recovery",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).EXCLUSION_REQUEST_DETAILS,
                view_name="exclusion_request_details",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).EXCLUSION_REQUESTS,
                view_name="exclusion_requests",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="measured_flag",
                jinja_template="""
                    select
                        current.order_id
                        , current.system_id
                        , current.tracking_id
                        , current.calrec_region
                        , current.start_date
                        , current.end_date
                        , current.still_active_flag
                        , current.measurement_date
                        , current.valid_scan_flag
                        , current.last_valid_scan_datetime_by_date_end
                        , current.last_valid_scan_datetime_by_date_start
                        , current.recovery_working_day_flag
                        , current.rnk
                        , current.order_tag_exclusion_flag
                        , current.on_hold_flag
                        , current.in_shipment_flag
                        , current.selected_granular_flag
                        , current.non_missing_ticket_flag
                        , current.missing_ticket_flag
                        , current.missing_ticket_creation_datetime
                        , case
                            when current.rnk = 1 then 0
                            when current.selected_granular_flag = 1 then 0
                            when current.order_tag_exclusion_flag = 1 then 0
                            when current.in_shipment_flag = 1 then 0
                            when current.on_hold_flag = 1 and current.missing_ticket_flag = 1 then 1
                            when current.on_hold_flag = 1 then 0
                            when current.non_missing_ticket_flag = 1 then 0
                            when current.valid_scan_flag = 1 then 0
                            when before.valid_scan_flag = 1 then 0
                            when current.recovery_working_day_flag = 0 then 0
                            else 1
                        end as measured_flag
                        , current.created_month
                    from ticket_creation_last_scans as current
                    left join ticket_creation_last_scans as before
                        on current.order_id = before.order_id
                        and current.rnk-1 = before.rnk
                """,
            ),
            base.TransformView(
                view_name="revised_measured_met",
                jinja_template="""
                    with met_flag as (
                        select
                            order_id
                            , system_id
                            , tracking_id
                            , calrec_region
                            , start_date
                            , end_date
                            , still_active_flag
                            , measurement_date
                            , valid_scan_flag
                            , last_valid_scan_datetime_by_date_end
                            , last_valid_scan_datetime_by_date_start
                            , recovery_working_day_flag
                            , rnk
                            , order_tag_exclusion_flag
                            , on_hold_flag
                            , in_shipment_flag
                            , selected_granular_flag
                            , non_missing_ticket_flag
                            , missing_ticket_flag
                            , missing_ticket_creation_datetime
                            , measured_flag
                            , if(measured_flag = 1 and missing_ticket_flag = 1, 1, 0) met_flag
                            , created_month
                        from measured_flag
                    )

                    select
                        current.order_id
                        , current.system_id
                        , current.tracking_id
                        , current.calrec_region
                        , current.start_date
                        , if(current.still_active_flag = 1, null, current.end_date) end_date
                        , current.still_active_flag
                        , current.measurement_date
                        , current.valid_scan_flag
                        , current.last_valid_scan_datetime_by_date_end
                        , current.last_valid_scan_datetime_by_date_start
                        , current.recovery_working_day_flag
                        , current.rnk
                        , current.on_hold_flag
                        , current.order_tag_exclusion_flag
                        , current.in_shipment_flag
                        , current.selected_granular_flag
                        , current.non_missing_ticket_flag
                        , current.missing_ticket_flag
                        , current.missing_ticket_creation_datetime
                        , current.measured_flag
                        , current.met_flag
                        , case
                            when current.measured_flag = 0 then 0
                            when current.measured_flag = 1 and (before.measured_flag = 1 and before.met_flag = 0) then 1
                            when current.measured_flag = 1 and (before.measured_flag = 1 and before.met_flag = 1) then 0
                            when before.measured_flag = 0 then current.measured_flag
                        end as revised_measured_flag
                        , case
                            when before.met_flag = 0 or before.met_flag is null then current.met_flag
                            when before.met_flag = 1 then 0
                        end as revised_met_flag
                        , current.created_month
                    from met_flag as current
                    left join met_flag as before
                        on current.order_id = before.order_id
                        and current.rnk - 1 = before.rnk
                """,
            ),
            base.TransformView(
                view_name="latest_hub",
                jinja_template="""
                    with adjusted_base as (
                        select
                            system_id
                            , order_id
                            , tracking_id
                            , start_date
                            , end_date
                            , measurement_date
                            , valid_scan_flag
                            , last_valid_scan_datetime_by_date_start
                            , last_valid_scan_datetime_by_date_end
                            , missing_ticket_creation_datetime
                            , revised_measured_flag measured
                            , revised_met_flag met
                            , created_month
                        from revised_measured_met
                        where
                            revised_measured_flag = 1
                    )

                    , add_hub_location as (
                        select
                            base.system_id
                            , base.order_id
                            , base.tracking_id
                            , base.start_date
                            , base.end_date
                            , base.measurement_date
                            , base.valid_scan_flag
                            , base.measured
                            , base.met
                            , hub_location.hub_id
                            , row_number() over (partition by base.system_id, base.order_id,
                                base.measurement_date order by hub_location.entry_datetime desc) as hub_rnk
                            , base.created_month
                        from adjusted_base as base
                        left join order_hub_movements as hub_location
                            on base.system_id = hub_location.system_id
                            and base.order_id = hub_location.order_id
                            and (base.measurement_date >= date(hub_location.entry_datetime)
                                and base.measurement_date <= date(hub_location.exit_datetime))
                    )
                    select
                        *
                    from add_hub_location
                    where hub_rnk = 1
                """,
            ),
            base.TransformView(
                view_name="latest_extension",
                jinja_template="""
                    with expanded_extension as (
                        select
                            extensions.system_id
                            , extensions.request_id
                            , requests.creation_datetime request_creation_datetime
                            , extensions.category
                            , extensions.category_value
                            , cal_rec.next_working_day_0 extended_date
                            , row_number() over (partition by extensions.system_id,
                                extensions.category, extensions.category_value,
                            cal_rec.next_working_day_0 order by requests.creation_datetime desc) as extension_rnk
                        from exclusion_request_details as extensions
                        left join (select distinct request_id, creation_datetime from exclusion_requests) as requests
                            on extensions.request_id = requests.request_id
                        left join (select distinct  system_id, next_working_day_0 from calendar_recovery) as cal_rec
                            on extensions.system_id = cal_rec.system_id
                            and cal_rec.next_working_day_0 >= date(extensions.extension_start)
                            and cal_rec.next_working_day_0 <= date(extensions.extension_end)
                        where
                            extensions.creation = 1
                    )
                    
                    select *
                    from expanded_extension
                    where extension_rnk = 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    with add_extension_flag as (
                        select
                            base.system_id
                            , base.order_id
                            , base.tracking_id
                            , base.start_date
                            , base.end_date
                            , base.measurement_date
                            , base.hub_id
                            , base.valid_scan_flag
                            , if(country_ext.request_id is not null or
                                hub_ext.request_id is not null or tid_ext.request_id is not null, 1, 0)
                            as extension_flag
                            , (case
                                when greatest(country_ext.request_creation_datetime, hub_ext.request_creation_datetime,
                                    tid_ext.request_creation_datetime) = tid_ext.request_creation_datetime
                                then tid_ext.request_id
                                when greatest(country_ext.request_creation_datetime, hub_ext.request_creation_datetime,
                                    tid_ext.request_creation_datetime) = hub_ext.request_creation_datetime
                                then hub_ext.request_id
                                when greatest(country_ext.request_creation_datetime, hub_ext.request_creation_datetime,
                                    tid_ext.request_creation_datetime) = country_ext.request_creation_datetime
                                then country_ext.request_id
                            end) as extension_request_id
                            , base.measured
                            , base.met
                            , base.created_month
                        from latest_hub as base
                        left join latest_extension as country_ext on
                            country_ext.category = 'country'
                            and base.system_id = country_ext.category_value
                            and base.measurement_date = country_ext.extended_date
                        left join latest_extension as hub_ext on
                            hub_ext.category = 'hub_id'
                            and base.hub_id = hub_ext.category_value
                            and base.measurement_date = hub_ext.extended_date
                        left join latest_extension as tid_ext on
                            tid_ext.category = 'tracking_id'
                            and base.tracking_id = tid_ext.category_value
                            and base.measurement_date = tid_ext.extended_date
                    )

                    select
                        system_id
                        , order_id
                        , tracking_id
                        , start_date
                        , end_date
                        , measurement_date
                        , hub_id
                        , valid_scan_flag
                        , measured
                        , extension_flag
                        , extension_request_id
                        , if(extension_flag = 1 and measured = 1 and met = 0, 0, measured) as adjusted_measured
                        , met
                        , created_month
                    from add_extension_flag
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TICKET_CREATION_PERFORMANCE,
        measurement_datetime=measurement_datetime,
        output_range=lookback_ranges.output,
        partition_by=("created_month",),
        system_id=system_id,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
