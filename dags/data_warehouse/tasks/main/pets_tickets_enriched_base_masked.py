import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED + ".py",
    task_name=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_BASE_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID, task_id=data_warehouse.OrderEventsDAG.Task.TICKET_RESOLVED_EVENTS_MASKED
        ),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TICKET_RESOLVED_EVENTS,
                view_name="ticket_resolved_events",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.TicketingProdGL(input_env, is_masked).TICKETS,
                view_name="tickets",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.TicketingProdGL(input_env, is_masked).TICKET_CUSTOM_FIELDS,
                view_name="ticket_custom_fields",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.TicketingProdGL(input_env, is_masked).TICKET_LOGS,
                view_name="ticket_logs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).TICKET_TYPES, view_name="ticket_types"),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).TICKET_SUBTYPES, view_name="ticket_subtypes"),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).CUSTOM_FIELDS, view_name="custom_fields"),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).GROUPS, view_name="groups"),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).STATUSES, view_name="statuses"),
            base.InputTable(path=delta_tables.TicketingProdGL(input_env, is_masked).USERS, view_name="users"),
            base.InputTable(path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,view_name="user_info",),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="ticket_status_events",
                jinja_template="""
                with
                    ticket_status_change as (

                        select
                            ticket_id
                            , split(log, "'")[1] as original_status
                            , split(log, "'")[3] as new_status
                            , created_at as creation_datetime_utc
                        from ticket_logs
                        where
                            log like 'Status change%'

                    )
                    , ticket_auto_resolve as (

                        select
                            ticket_id
                            , null as original_status
                            , 'RESOLVED' as new_status
                            , created_at as creation_datetime_utc
                        from ticket_logs
                        where
                            log like 'Ticket auto-resolved%'

                    )
                    , final as (

                        select
                            *
                        from ticket_status_change
                        union all
                        select
                            *
                        from ticket_auto_resolve

                    )

                select *
                from final
                """,
            ),
            base.TransformView(
                view_name="ticket_milestones",
                jinja_template="""
                select
                    ticket_id
                    {%- for status, col_name in milestones.items() %}
                    , max(if(new_status = '{{ status }}', creation_datetime_utc, null)) as {{ col_name }}_datetime_utc
                    {%- endfor %}
                from ticket_status_events
                group by 1
                """,
                jinja_arguments={
                    "milestones": {
                        "IN PROGRESS": "in_progress",
                        "RESOLVED": "resolution",
                        "ON HOLD": "on_hold",
                        "CANCELLED": "cancellation",
                    }
                },
            ),
            base.TransformView(
                view_name="ticket_outcome",
                jinja_template="""
                select
                    ticket_custom_fields.ticket_id
                    , max_by(
                        ticket_custom_fields.data
                        , coalesce(ticket_custom_fields.updated_at, ticket_custom_fields.created_at)
                    ) as outcome
                from ticket_custom_fields
                inner join custom_fields on
                    ticket_custom_fields.custom_field_id = custom_fields.id
                where
                    custom_fields.field_name like 'ORDER OUTCOME%'
                group by 1
                """,
            ),
            base.TransformView(
                view_name="ticket_custom",
                jinja_template="""
                select
                    ticket_id
                    , max_by(data, coalesce(updated_at, created_at)) filter(where custom_field_id = 32) as ticket_notes
                    , max_by(data, coalesce(updated_at, created_at)) filter(where custom_field_id = 97)
                        as investigation_results
                    , max_by(data, coalesce(updated_at, created_at)) filter(where custom_field_id = 98)
                        as suspicious_reasons
                    , max_by(data, coalesce(updated_at, created_at)) filter(where custom_field_id = 102)
                        as tampered_or_swapped
                from ticket_custom_fields
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="resolver",
                jinja_template="""
                    select
                        ticket_resolved_events.ticket_id
                        , min_by(
                            concat(first_name, ' ', last_name)
                            , event_datetime
                        ) as user_name
                    from ticket_resolved_events
                    left join user_info
                        on ticket_resolved_events.user_id = user_info.user_id
                    group by 1
                    """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    base as (

                        select
                            tickets.id
                            , lower(tickets.country) as country
                            , tickets.order_id
                            , upper(tickets.tracking_id) as tracking_id
                            , ticket_types.name as type
                            , ticket_subtypes.name as sub_type
                            , statuses.name as status
                            , case
                                when ticket_outcome.outcome = 'COLLECTED' then 'COLLECTED BY CONSIGNEE'
                                else ticket_outcome.outcome
                            end as outcome
                            , from_utc_timestamp(tickets.created_at, {{ get_local_timezone }}) as creation_datetime
                            , from_utc_timestamp(
                                ticket_milestones.in_progress_datetime_utc, {{ get_local_timezone }}
                            ) as in_progress_datetime
                            , from_utc_timestamp(
                                ticket_milestones.on_hold_datetime_utc, {{ get_local_timezone }}
                            ) as on_hold_datetime
                            , from_utc_timestamp(
                                ticket_milestones.cancellation_datetime_utc, {{ get_local_timezone }}
                            ) as cancellation_datetime
                            , if(
                                statuses.name = 'RESOLVED'
                                , from_utc_timestamp(
                                    ticket_milestones.resolution_datetime_utc
                                    , {{ get_local_timezone }}
                                )
                                , null
                            ) as resolution_datetime
                            , from_utc_timestamp(
                                '{{ measurement_datetime_utc }}'
                                , {{ get_local_timezone }}
                            ) as measurement_datetime_temp
                            , case
                                when groups.name = 'Fleet (First Mile)' then 'FM'
                                when groups.name = 'Freight (Middle Mile)' then 'MM'
                                when groups.name = 'Sort (Warehouse)' then 'Sort'
                                when groups.name = 'Fleet (Last Mile)' then 'LM'
                                when groups.name = 'Recovery' then 'Recovery'
                                when groups.name = 'PUDO' then 'PUDO'
                                when groups.name = 'Cross Border' then 'XB'
                                else 'Others'
                            end as assigned_dept
                            , tickets.investigating_hub_id
                            , hubs.name as investigating_hub_name
                            , hubs.region as investigating_hub_region
                            , hubs.address_city as investigating_hub_address_city
                            , users.name as creator_name
                            , resolver.user_name as resolver_name
                            , ticket_custom.ticket_notes
                            , case
                                when ticket_types.name != 'DAMAGED' then null
                                when ticket_outcome.outcome is null then null
                                when ticket_outcome.outcome = '' then null
                                when ticket_custom.tampered_or_swapped = 'true' then 1
                                else 0
                            end as tampered_or_swapped_flag
                            , if(ticket_custom.suspicious_reasons is null or ticket_custom.suspicious_reasons = ''
                                , null
                                , ticket_custom.suspicious_reasons
                            ) as suspicious_reasons
                            , if(ticket_custom.investigation_results is null or ticket_custom.investigation_results = ''
                                , null
                                , ticket_custom.investigation_results
                            ) as investigation_results
                            , tickets.entry_source_id
                            , date_format(tickets.created_at, 'yyyy-MM') as created_month
                            , lower(tickets.country) as system_id
                        from tickets
                        left join resolver on
                            tickets.id = resolver.ticket_id
                        left join ticket_custom on
                            tickets.id = ticket_custom.ticket_id
                        left join ticket_types on
                            tickets.type_id = ticket_types.id
                        left join ticket_subtypes on
                            tickets.subtype_id = ticket_subtypes.id
                        left join ticket_outcome on
                            tickets.id = ticket_outcome.ticket_id
                        left join groups on
                            tickets.current_assignee_group_id = groups.id
                        left join statuses on
                            tickets.status_id = statuses.id
                        left join ticket_milestones on
                            tickets.id = ticket_milestones.ticket_id
                        left join hubs_enriched as hubs on
                            tickets.investigating_hub_id = hubs.id
                            and lower(tickets.country) = hubs.system_id
                        left join users on
                            tickets.creator_user_id = users.id
                        where
                            tickets.order_id is not null
                            and tickets.deleted_at is null

                    )
                    , final as (

                        select
                            base.id
                            , base.country
                            , base.order_id
                            , base.tracking_id
                            , base.type
                            , base.sub_type
                            , base.status
                            , base.outcome
                            , base.creation_datetime
                            , base.in_progress_datetime
                            , base.on_hold_datetime
                            , base.cancellation_datetime
                            , base.resolution_datetime
                            , base.assigned_dept
                            , base.investigating_hub_id
                            , base.investigating_hub_name
                            , base.investigating_hub_region
                            , base.creator_name
                            , base.resolver_name
                            , base.ticket_notes
                            , base.tampered_or_swapped_flag
                            , base.suspicious_reasons
                            , base.investigation_results
                            , base.entry_source_id
                            , if(
                                base.resolution_datetime is null
                                , coalesce(
                                    cal_today_reg.working_day_cum - cal_create_reg.working_day_cum
                                    , cal_today.working_day_cum - cal_create.working_day_cum
                                )
                                , null
                            ) as age
                            , coalesce(
                                cal_resolve_reg.working_day_cum - cal_create_reg.working_day_cum
                                , cal_resolve.working_day_cum - cal_create.working_day_cum
                            ) as days_to_resolution
                            , base.created_month
                            , base.system_id
                        from base
                        {%- for suffix, ref_date in calendar_joins %}
                        left join calendar as cal_{{ suffix }} on
                            date({{ ref_date }}) = cal_{{ suffix }}.date
                            and base.system_id = cal_{{ suffix }}.system_id
                            and cal_{{ suffix }}.region = 'national'
                        left join calendar as cal_{{ suffix }}_reg on
                            date({{ ref_date }}) = cal_{{ suffix }}_reg.date
                            and base.system_id = cal_{{ suffix }}.system_id
                            and base.investigating_hub_address_city = cal_{{ suffix }}_reg.region
                            and cal_{{ suffix }}_reg.country = 'my'
                        {%- endfor %}

                    )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("tickets.country"),
                    "measurement_datetime_utc": measurement_datetime,
                    "calendar_joins": (
                        ("today", "base.measurement_datetime_temp"),
                        ("create", "base.creation_datetime"),
                        ("resolve", "base.resolution_datetime"),
                    ),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PETS_TICKETS_ENRICHED_BASE,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
