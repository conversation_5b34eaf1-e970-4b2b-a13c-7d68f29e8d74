import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- order_events type 11=UPDATE_ADDRESS

                select
                    id as order_event_id
                    , order_id
                    , from_utc_timestamp(time, {{ get_local_timezone }}) AS event_datetime
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS event_creation_datetime
                    , get_json_object(data, '$.to_address_1.old_value') old_to_address1
                    , get_json_object(data, '$.to_address_1.new_value') new_to_address1
                    , get_json_object(data, '$.to_address_2.old_value') old_to_address2
                    , get_json_object(data, '$.to_address_2.new_value') new_to_address2
                    , get_json_object(data, '$.to_postcode.old_value') old_to_postcode
                    , get_json_object(data, '$.to_postcode.new_value') new_to_postcode
                    , get_json_object(data, '$.to_city.old_value') old_to_city
                    , get_json_object(data, '$.to_city.new_value') new_to_city
                    , get_json_object(data, '$.to_district.old_value') old_to_district
                    , get_json_object(data, '$.to_district.new_value') new_to_district
                    , get_json_object(data, '$.to_state.old_value') old_to_state
                    , get_json_object(data, '$.to_state.new_value') new_to_state
                    , date_format(time, 'yyyy-MM') AS created_month
                    , system_id
                from order_events
                where type = 11
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).UPDATE_ADDRESS_EVENTS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
