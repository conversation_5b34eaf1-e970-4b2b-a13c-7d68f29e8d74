import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_PARCEL_RECEIVING_TASKS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_PARCEL_RECEIVING_TASKS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED,
        data_warehouse.CrossBorderDAG.Task.XB_SHIPMENT_PARCELS_ENRICHED_MASKED,
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_PARCELS_ENRICHED,
                view_name="xb_parcels_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_SHIPMENT_PARCELS_ENRICHED,
                view_name="xb_shipment_parcels_enriched",
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).RECEIVING_JOBS,
                view_name="receiving_jobs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).RECEIVING_TASK_ITEMS,
                view_name="receiving_task_items",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.XbOpsProdGL(input_env, is_masked).WAREHOUSES_TAB,
                view_name="warehouses_tab",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="receiving_task_items_flat",
                jinja_template="""
                select
                    tracking_id
                {%- for type, type_name in type_to_name.items() %}
                {%- for seq, seq_name in seq_to_name.items() %}
                    , {{ seq }}(items.job_id) filter(where jobs.type = {{ type }}) 
                        as {{ seq_name }}_{{ type_name }}_job_id
                    , {{ seq }}_by(items.received_at, items.job_id) filter(where jobs.type = {{ type }}) 
                        as {{ seq_name }}_{{ type_name }}_received_at
                    , {{ seq }}_by(warehouses.code, items.job_id) filter(where jobs.type = {{ type }})
                        as {{ seq_name }}_{{ type_name }}_warehouse_code
                    , {{ seq }}_by(warehouses.name, items.job_id) filter(where jobs.type = {{ type }})
                        as {{ seq_name }}_{{ type_name }}_warehouse_name
                {%- endfor %}    
                {%- endfor %}
                from receiving_task_items as items
                left join receiving_jobs as jobs
                    on items.job_id = jobs.id
                left join warehouses_tab as warehouses
                    on jobs.warehouse_id = warehouses.id
                where
                    items.status = 1
                group by 1
                """,
                jinja_arguments={
                    "type_to_name":{
                        2:"hoof",
                        4:"holm",
                    },
                    "seq_to_name":{
                        "min":"first",
                        "max":"last",
                    },
                }
            ),
            base.TransformView(
                view_name="parcel_level_join_prep",
                jinja_template="""
                select
                    parcels.parcel_id
                    , parcels.tracking_id
                    , parcels.parcel_type
                    , if(upper(shipment_parcels.shipment_destination_country) in ('TH', 'VN', 'ID')
                        , 7, 8) as holm_hour_adjustment
                    , case
                        when parcels.parcel_type in ('B2B Bag', 'B2C Bag') then 'ref_tracking_id'
                        when parcels.parcel_type = 'MMCC Parcel' then null
                        else 'vendor_bag_id'
                    end as rti_join_key_type
                    , case
                        when parcels.parcel_type in ('B2B Bag', 'B2C Bag') then parcels.ref_tracking_id
                        when parcels.parcel_type = 'MMCC Parcel' then null
                        else upper(shipment_parcels.vendor_bag_id)
                    end as rti_join_key
                    , parcels.created_month
                from xb_parcels_enriched as parcels
                left join xb_shipment_parcels_enriched as shipment_parcels
                    on parcels.parcel_id = shipment_parcels.parcel_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.parcel_id
                    , base.tracking_id
                    , base.rti_join_key
                    , base.rti_join_key_type
                    , rti.first_hoof_job_id
                    , rti.first_hoof_warehouse_code
                    , rti.first_hoof_warehouse_name
                    , rti.last_hoof_job_id
                    , rti.last_hoof_warehouse_code
                    , rti.last_hoof_warehouse_name
                {%- for seq in ('first', 'last') %}
                    , rti.{{ seq }}_holm_job_id
                    , rti.{{ seq }}_holm_warehouse_code
                    , rti.{{ seq }}_holm_warehouse_name
                    , rti.{{ seq }}_holm_received_at + cast(base.holm_hour_adjustment || ' hour' as interval)
                        as {{ seq }}_holm_received_datetime
                {%- endfor %}
                    , 'gl' as system_id
                    , base.created_month
                from parcel_level_join_prep as base
                left join receiving_task_items_flat as rti
                    on base.rti_join_key = rti.tracking_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_PARCEL_RECEIVING_TASKS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
