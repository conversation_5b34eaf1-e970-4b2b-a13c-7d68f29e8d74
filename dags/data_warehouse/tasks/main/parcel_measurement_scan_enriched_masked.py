import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.PARCEL_MEASUREMENT_SCAN_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.PARCEL_MEASUREMENT_SCAN_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED, data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.SortVendorProdGL(input_env, is_masked).PARCEL_MEASUREMENT_SCAN,
                view_name="parcel_measurement_scan",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                with base as (
                
                    select
                        scan_uuid
                        , max_by(id, created_at) as id
                    from parcel_measurement_scan
                    group by 1
                
                ), 
                filtered as (
                
                    select distinct
                        id
                    from base

                ),
                parcel_measurement_scan_dedup as (
                
                    select 
                        parcel_measurement_scan.*
                    from filtered
                    left join parcel_measurement_scan
                        on filtered.id = parcel_measurement_scan.id

                ),
                final as (
                    select
                        cast(id as int) as parcel_measurement_id
                        , scan_uuid
                        , from_utc_timestamp(created_at, {{ get_local_timezone }}) as scan_datetime
                        , date_format(from_utc_timestamp(created_at, {{ get_local_timezone }}), "yyyy-MM") as scan_month
                        , system_id
                        , tracking_id
                        , inbound_type
                        , inbounded_by
                        , height
                        , length
                        , width
                        , weight
                        , if(dimweight_image_link is not null,1,0) as dimweight_image_link_flag
                        , if(height_image_link is not null,1,0) as height_image_link_flag
                        , if(length_image_link is not null,1,0) as length_image_link_flag
                        , if(width_image_link is not null,1,0) as width_image_link_flag
                        , if(weight_image_link is not null,1,0) as weight_image_link_flag
                    from parcel_measurement_scan_dedup
                )

                select * from final

                    """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with prefinal as (
                    select
                        base.parcel_measurement_id
                        , base.scan_uuid
                        , base.scan_datetime
                        , base.scan_month
                        , base.system_id
                        , base.tracking_id
                        , base.inbound_type
                        , base.inbounded_by
                        , base.height
                        , base.length
                        , base.width
                        , base.weight
                        , base.dimweight_image_link_flag
                        , base.height_image_link_flag
                        , base.length_image_link_flag
                        , base.width_image_link_flag
                        , base.weight_image_link_flag
                        , order_milestones.order_id
                        , order_milestones.created_month
                        , dim_weight_scans_base.order_event_id
                        , dim_weight_scans_base.scan_hub_id
                    from base
                    left join order_milestones
                        on base.tracking_id = order_milestones.tracking_id
                        and base.system_id = order_milestones.system_id
                    left join dim_weight_scans_base
                        on base.scan_uuid = dim_weight_scans_base.scan_uuid
                )
                , final as (
                    select
                        prefinal.parcel_measurement_id
                        , prefinal.scan_uuid
                        , prefinal.order_event_id
                        , prefinal.scan_datetime
                        , prefinal.scan_month
                        , prefinal.created_month
                        , prefinal.system_id
                        , prefinal.scan_hub_id as hub_id
                        , hubs_enriched.name as hub_name
                        , prefinal.tracking_id
                        , prefinal.order_id
                        , prefinal.inbound_type
                        , prefinal.inbounded_by
                        , prefinal.height
                        , prefinal.length
                        , prefinal.width
                        , prefinal.weight
                        , prefinal.dimweight_image_link_flag
                        , prefinal.height_image_link_flag
                        , prefinal.length_image_link_flag
                        , prefinal.width_image_link_flag
                        , prefinal.weight_image_link_flag
                    from prefinal
                    left join hubs_enriched
                        on prefinal.scan_hub_id = hubs_enriched.id
                        and prefinal.system_id = hubs_enriched.system_id

                )
                select * from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARCEL_MEASUREMENT_SCAN_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
