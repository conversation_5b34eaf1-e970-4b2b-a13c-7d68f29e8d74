import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_DAILY_ADJUSTED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ID_LM_PAYROLL_DAILY_ADJUSTED_MASKED,
    system_ids=(
        constants.SystemID.ID,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.DRIVER_PICKUP_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing",),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    details_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 12)
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 2)
    if enable_full_run:
        details_lookback_ranges = base.LookBackRange(None, None)
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                system_id=system_id,
                input_range=details_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_PICKUP_SCAN_EVENTS,
                view_name="driver_pickup_scan_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAYPOINTS_ENRICHED,
                view_name="waypoints_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_POINTS,
                view_name="id_delivery_parcel_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PICKUP_PARCEL_POINTS,
                view_name="id_pickup_parcel_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS,
                view_name="id_delivery_parcel_exception_points",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DELIVERY_PARCEL_EXCEPTION_POINTS_MAP,
                view_name="id_delivery_parcel_exception_points_map",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_PARCEL_RATES,
                view_name="parcel_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_VOLUME_BONUSES,
                view_name="volume_bonus",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_HUB_DATA_ADJUSTED,
                view_name="id_hub_data",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_ZONE_DATA_ADJUSTED,
                view_name="id_zone_data",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).ID_LM_PAYROLL_DENSITY_PRIORITY,
                view_name="id_density_priority",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES,
                view_name="zones",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                system_id=system_id,
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).RESERVATIONS,
                view_name="reservations",
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=details_lookback_ranges.input,
                version_datetime="latest",
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_TAGS,
                view_name="order_tags",
                input_range=lookback_ranges.input,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="reservations_cleaned",
                jinja_template="""
                with base as (
                
                    select 
                        legacy_id as waypoint_id
                        , route_id
                        , created_at
                    from waypoints_enriched

                ),
                last_route_waypoint as (
                
                    select
                        waypoint_id
                        , max_by(route_id, created_at) as route_id
                    from base
                    group by 1

                ),
                
                final as (
                
                    select distinct
                        last_route_waypoint.route_id
                    from reservations
                    left join last_route_waypoint
                        on reservations.waypoint_id = last_route_waypoint.waypoint_id
                    where reservations.deleted_at is null
                        and last_route_waypoint.route_id is not null
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="id_zone_data_cleaned",
                jinja_template="""
                with final as (
                
                    select
                        *
                    from id_zone_data
                    where density_category != '#N/A'
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="route_attributes",
                jinja_template="""
                with base as (

                    select
                        route_logs.system_id
                        , route_logs.legacy_id as route_id
                        , route_logs.date as route_logs_date
                        , route_logs.driver_id
                        , drivers_enriched.display_name as driver_display_name
                        , drivers_enriched.driver_type
                        , drivers_enriched.hub_id as driver_hub_id
                        , drivers_enriched.hub_region as driver_hub_region
                        , route_logs.hub_id as route_hub_id
                        , hubs_enriched.name as route_hub_name
                        , hubs_enriched.region as route_hub_region
                        , case
                            when lower(hubs_enriched.region) like '%sumatera%' then 'sumatera'
                            else lower(hubs_enriched.region)
                            end as route_hub_region_scheme
                        , route_logs.zone_id
                        , zones.name as zone_name
                        , lower(id_zone_data_cleaned.final_density) as zone_density
                        , lower(id_zone_data_cleaned.density_category) as zone_density_category
                        , lower(id_zone_data_cleaned.umk_category) as zone_umk_category
                        , lower(id_hub_data.final_density) as driver_hub_density
                        , lower(id_hub_data.density_category) as driver_hub_density_category
                        , lower(id_hub_data.umk_category) as driver_hub_umk_category
                    from route_logs
                    left join drivers_enriched
                        on route_logs.driver_id = drivers_enriched.id
                    left join hubs_enriched
                        on route_logs.hub_id = hubs_enriched.id
                    left join zones
                        on route_logs.zone_id = zones.legacy_zone_id
                        and zones.deleted_at is null
                    left join id_zone_data_cleaned
                        on route_logs.zone_id = id_zone_data_cleaned.zone_id
                        and date_format(route_logs.date, 'yyyy-MM') = id_zone_data_cleaned.effective_month
                    left join id_hub_data
                        on drivers_enriched.hub_id = id_hub_data.hub_id
                        and date_format(route_logs.date, 'yyyy-MM') = id_hub_data.effective_month

                ),
                final as (
                
                    select
                        *
                        , case 
                            when route_hub_region_scheme = 'greater jakarta' then 'greater jakarta'
                            when route_hub_region_scheme in ('east java', 'central java', 'west java') then 'java'
                            when route_hub_region_scheme in ('sumatera','kalimantan','sulawesi','bali', 'nusa', 'maluku', 'papua', 'bali nusa') then 'ex java'
                            end as region_group
                        , coalesce(zone_density,driver_hub_density) as density
                        , coalesce(zone_density_category,driver_hub_density_category) as density_category
                        , coalesce(zone_umk_category,driver_hub_umk_category) as umk_category
                    from base

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="latest_dws",
                jinja_template="""
                with final as (
                
                    select
                        order_id
                        , max_by(new_parcel_size, scan_datetime) as latest_size
                        , max_by(new_width, scan_datetime) as latest_width
                        , max_by(new_height, scan_datetime) as latest_height
                        , max_by(new_length, scan_datetime) as latest_length
                        , max_by(new_weight, scan_datetime) as latest_weight
                    from dim_weight_scans_base
                    group by 1
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="route_deliveries",
                jinja_template="""
                with data as (

                    select
                        'id' as system_id
                        , transactions.order_id
                        , transactions.route_id
                        , transactions.service_end_time
                        , lower(shipper_attributes.sales_channel) as sales_channel
                        , case
                            when orders.parcel_size_id = 0 then 's'
                            when orders.parcel_size_id = 1 then 'm'
                            when orders.parcel_size_id = 2 then 'l'
                            when orders.parcel_size_id = 3 then 'xl'
                            when orders.parcel_size_id = 4 then 'xxl'
                            when orders.parcel_size_id = 5 then 'xs'
                            end as orders_size
                        , latest_dws.latest_size
                        , cast(orders.rts as bigint) as rts_flag
                        , cast(get_json_object(orders.dimensions, '$.width') as double) as orders_width
                        , cast(get_json_object(orders.dimensions, '$.height') as double) as orders_height
                        , cast(get_json_object(orders.dimensions, '$.length') as double) as orders_length
                        , cast(get_json_object(orders.dimensions, '$.weight') as double) as orders_weight
                        , latest_dws.latest_width
                        , latest_dws.latest_height
                        , latest_dws.latest_length
                        , latest_dws.latest_weight
                        , case when order_tags.order_id is not null then 1 else 0 end as bulky_revise_flag
                    from transactions
                    left join orders
                        on transactions.order_id = orders.id
                    left join latest_dws
                        on transactions.order_id = latest_dws.order_id
                    left join shipper_attributes
                        on orders.global_shipper_id = shipper_attributes.id
                    left join order_tags
                        on transactions.order_id = order_tags.order_id
                        and order_tags.tag_id = 97
                    where transactions.deleted_at is null
                        and transactions.type = 'DD'
                        and transactions.status = 'Success'
                    
                ),
                base as (
                
                    select
                        *
                        , coalesce(latest_size, orders_size) as size
                        , coalesce(latest_width, orders_width) as width
                        , coalesce(latest_height, orders_height) as height
                        , coalesce(latest_length, orders_length) as length
                        , coalesce(latest_weight, orders_weight) as weight
                        , date(from_utc_timestamp(service_end_time, {{ get_local_timezone }})) as route_date
                    from data

                ),
                attributes as (

                    select
                        base.*
                        , route_attributes.driver_hub_id
                        , route_attributes.region_group
                        , route_attributes.density_category
                    from base
                    left join route_attributes
                        on base.route_id = route_attributes.route_id

                ),
                category as (

                    select
                        route_id
                        , route_date
                        , date_format(route_date, 'yyyy-MM') as route_month
                        , driver_hub_id
                        , region_group
                        , density_category
                        , case
                            when region_group in ('greater jakarta', 'ex java', 'java') then
                                case
                                    when rts_flag = 1 and size in ('xs','s','m') then 'rts regular'
                                    when rts_flag = 1 and size in ('l','xl','xxl') then 'rts bulky'
                                    when rts_flag = 0 and (width > 50 or height > 50 or length >50 or weight >5) then 'superbulky'
                                    when rts_flag = 0 and bulky_revise_flag = 1 then 'bulky revise'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'standard regular'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'standard bulky'
                                    when rts_flag = 0 and sales_channel = 'field sales' and size in ('xs', 's', 'm') then 'fs regular'
                                    when rts_flag = 0 and sales_channel = 'field sales' and size in ('l', 'xl', 'xxl') then 'fs bulky'
                                    when rts_flag = 0 and sales_channel = 'corp sales' and size in ('xs', 's', 'm') then 'cs regular'
                                    when rts_flag = 0 and sales_channel = 'corp sales' and size in ('l', 'xl', 'xxl') then 'cs bulky'
                                    when rts_flag = 0 and sales_channel = 'retail' and size in ('xs', 's', 'm') then 'retail regular'
                                    when rts_flag = 0 and sales_channel = 'retail' and size in ('l', 'xl', 'xxl') then 'retail bulky'
                                    end
                            end as display_category
                        , case 
                            when region_group in ('greater jakarta', 'ex java', 'java') then
                                case
                                    when rts_flag = 1 and size in ('xs','s','m') then 'rts regular'
                                    when rts_flag = 1 and size in ('l','xl','xxl') then 'rts bulky'
                                    when rts_flag = 0 and (width > 50 or height > 50 or length >50 or weight >5) then 'superbulky'
                                    when rts_flag = 0 and bulky_revise_flag = 1 then 'bulky revise'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'standard regular'
                                    when rts_flag = 0 and sales_channel not in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'standard bulky'
                                    when rts_flag = 0 and sales_channel in ('field sales', 'corp sales', 'retail') and size in ('xs', 's', 'm') then 'non standard regular'
                                    when rts_flag = 0 and sales_channel in ('field sales', 'corp sales', 'retail') and size in ('l', 'xl', 'xxl') then 'non standard bulky'
                                end
                            end as join_category
                    from attributes

                ),
                join as (
                
                    select
                        category.*
                        , id_delivery_parcel_points.points
                    from category
                    left join id_delivery_parcel_points
                        on category.region_group = id_delivery_parcel_points.region_group
                        and category.density_category = id_delivery_parcel_points.density
                        and category.join_category = id_delivery_parcel_points.category

                ),
                exception_join as (
                
                    select
                        join.*
                        , coalesce(id_delivery_parcel_exception_points_map.points, id_delivery_parcel_exception_points.points, join.points) as adjusted_points
                    from join
                    left join id_delivery_parcel_exception_points
                        on join.route_month = id_delivery_parcel_exception_points.effective_month
                        and join.join_category = id_delivery_parcel_exception_points.category
                        and join.driver_hub_id = id_delivery_parcel_exception_points.hub_id
                    left join id_delivery_parcel_exception_points_map
                        on id_delivery_parcel_exception_points.effective_month = id_delivery_parcel_exception_points_map.effective_month
                        and id_delivery_parcel_exception_points.category = id_delivery_parcel_exception_points_map.category
                        and join.density_category = id_delivery_parcel_exception_points_map.density_category

                ),
                final as (
                
                    select  
                        route_id
                        , route_date
                        , sum(case when display_category = 'fs regular' then 1 else 0 end) as delivered_non_rts_fs_regular
                        , sum(case when display_category = 'fs bulky' then 1 else 0 end) as delivered_non_rts_fs_bulky
                        , sum(case when display_category = 'cs regular' then 1 else 0 end) as delivered_non_rts_cs_regular
                        , sum(case when display_category = 'cs bulky' then 1 else 0 end) as delivered_non_rts_cs_bulky
                        , sum(case when display_category = 'retail regular' then 1 else 0 end) as delivered_non_rts_retail_regular
                        , sum(case when display_category = 'retail bulky' then 1 else 0 end) as delivered_non_rts_retail_bulky
                        , sum(case when display_category = 'standard regular' then 1 else 0 end) as delivered_non_rts_standard_regular
                        , sum(case when display_category = 'standard bulky' then 1 else 0 end) as delivered_non_rts_standard_bulky
                        , sum(case when display_category in ('fs regular', 'cs regular', 'retail regular') then 1 else 0 end) as delivered_non_rts_fs_cs_retail_regular
                        , sum(case when display_category in ('fs bulky', 'cs bulky', 'retail bulky') then 1 else 0 end) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(case when display_category = 'rts regular' then 1 else 0 end) as delivered_rts_regular
                        , sum(case when display_category = 'rts bulky' then 1 else 0 end) as delivered_rts_bulky
                        , sum(case when display_category = 'superbulky' then 1 else 0 end) as delivered_non_rts_superbulky
                        , sum(case when display_category = 'bulky revise' then 1 else 0 end) as delivered_non_rts_bulky_revise

                        , sum(case when join_category = 'standard regular' then adjusted_points else 0 end) as delivered_non_rts_standard_regular_points
                        , sum(case when join_category = 'standard bulky' then adjusted_points else 0 end) as delivered_non_rts_standard_bulky_points
                        , sum(case when join_category = 'non standard regular' then adjusted_points else 0 end) as delivered_non_rts_fs_cs_retail_regular_points
                        , sum(case when join_category = 'non standard bulky' then adjusted_points else 0 end) as delivered_non_rts_fs_cs_retail_bulky_points
                        , sum(case when join_category = 'rts regular' then adjusted_points else 0 end) as delivered_rts_regular_points
                        , sum(case when join_category = 'rts bulky' then adjusted_points else 0 end) as delivered_rts_bulky_points
                        , sum(case when join_category = 'superbulky' then adjusted_points else 0 end) as delivered_non_rts_superbulky_points
                        , sum(case when join_category = 'bulky revise' then adjusted_points else 0 end) as delivered_non_rts_bulky_revise_points
                    from exception_join
                    group by {{ range(1, 3) | join(',') }}
                )

                select * from final

                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id")
                },
            ),
            base.TransformView(
                view_name="route_pickups",
                jinja_template="""
                with pickup_scans as(
                
                    select order_id, route_id, waypoint_id, created_at from order_events_pickup_success
                    UNION ALL
                    select order_id, route_id, waypoint_id, created_at from driver_pickup_scan_events

                ),
                grouped as (
                
                    select
                        order_id
                        , route_id
                        , waypoint_id
                        , min(date(created_at)) as route_date
                    from pickup_scans
                    group by {{ range(1, 4) | join(',') }}

                ),
                sizes as (
                
                    select
                        grouped.order_id
                        , grouped.route_id
                        , grouped.waypoint_id
                        , grouped.route_date
                        , case
                            when orders.parcel_size_id = 0 then 's'
                            when orders.parcel_size_id = 1 then 'm'
                            when orders.parcel_size_id = 2 then 'l'
                            when orders.parcel_size_id = 3 then 'xl'
                            when orders.parcel_size_id = 4 then 'xxl'
                            when orders.parcel_size_id = 5 then 'xs'
                            end as orders_size
                        , latest_dws.latest_size
                    from grouped
                    left join orders
                        on grouped.order_id = orders.id
                    left join latest_dws
                        on grouped.order_id = latest_dws.order_id
                ),
                final_size as (
                
                    select
                        *
                        , coalesce(latest_size, orders_size) as size
                    from sizes
                    
                ),
                category as (
                
                    select
                        route_id
                        , waypoint_id
                        , route_date
                        , least(coalesce(sum(case when size in ('xs','s','m') then 1 else 0 end), 0), 100) as picked_regular
                        , sum(case when size in ('l','xl','xxl') then 1 else 0 end) as picked_bulky 
                    from final_size
                    group by {{ range(1, 4) | join(',') }}

                ),
                pre_final as (
                
                    select
                        route_id
                        , route_date
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky
                    from category
                    group by {{ range(1, 3) | join(',') }}
                ),
                final as (
                
                    select
                        *
                        , picked_regular * (select points from id_pickup_parcel_points where category = 'regular pickup') as picked_regular_points
                        , picked_bulky * (select points from id_pickup_parcel_points where category = 'bulky pickup') as picked_bulky_points
                    from pre_final
                    
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="route_points",
                jinja_template="""
                with route_values as (
                
                    select
                        coalesce(route_deliveries.route_id, route_pickups.route_id) as route_id
                        , coalesce(route_deliveries.route_date, route_pickups.route_date) as route_date
                        , route_deliveries.delivered_non_rts_fs_regular
                        , route_deliveries.delivered_non_rts_fs_bulky
                        , route_deliveries.delivered_non_rts_cs_regular
                        , route_deliveries.delivered_non_rts_cs_bulky
                        , route_deliveries.delivered_non_rts_retail_regular
                        , route_deliveries.delivered_non_rts_retail_bulky
                        , route_deliveries.delivered_non_rts_standard_regular
                        , route_deliveries.delivered_non_rts_standard_bulky
                        , route_deliveries.delivered_non_rts_fs_cs_retail_regular
                        , route_deliveries.delivered_non_rts_fs_cs_retail_bulky
                        , route_deliveries.delivered_non_rts_superbulky
                        , route_deliveries.delivered_non_rts_bulky_revise
                        , route_deliveries.delivered_rts_regular
                        , route_deliveries.delivered_rts_bulky
                        , route_pickups.picked_regular
                        , route_pickups.picked_bulky

                        , route_deliveries.delivered_non_rts_standard_regular_points
                        , route_deliveries.delivered_non_rts_standard_bulky_points
                        , route_deliveries.delivered_non_rts_fs_cs_retail_regular_points
                        , route_deliveries.delivered_non_rts_fs_cs_retail_bulky_points
                        , route_deliveries.delivered_non_rts_superbulky_points
                        , route_deliveries.delivered_non_rts_bulky_revise_points
                        , route_deliveries.delivered_rts_regular_points
                        , route_deliveries.delivered_rts_bulky_points
                        , route_pickups.picked_regular_points
                        , route_pickups.picked_bulky_points
                    from route_deliveries
                    full outer join route_pickups
                        on route_deliveries.route_id = route_pickups.route_id
                        and route_deliveries.route_date = route_pickups.route_date
                ),
                base as (
                
                    select
                        route_attributes.*
                        , coalesce(route_values.route_date, route_attributes.route_logs_date) as route_date
                        , coalesce(route_values.delivered_non_rts_fs_regular,0) as delivered_non_rts_fs_regular
                        , coalesce(route_values.delivered_non_rts_fs_bulky,0) as delivered_non_rts_fs_bulky
                        , coalesce(route_values.delivered_non_rts_cs_regular,0) as delivered_non_rts_cs_regular
                        , coalesce(route_values.delivered_non_rts_cs_bulky,0) as delivered_non_rts_cs_bulky
                        , coalesce(route_values.delivered_non_rts_retail_regular,0) as delivered_non_rts_retail_regular
                        , coalesce(route_values.delivered_non_rts_retail_bulky,0) as delivered_non_rts_retail_bulky
                        , coalesce(route_values.delivered_non_rts_standard_regular,0) as delivered_non_rts_standard_regular
                        , coalesce(route_values.delivered_non_rts_standard_bulky,0) as delivered_non_rts_standard_bulky
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_regular,0) as delivered_non_rts_fs_cs_retail_regular
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_bulky,0) as delivered_non_rts_fs_cs_retail_bulky
                        , coalesce(route_values.delivered_non_rts_superbulky,0) as delivered_non_rts_superbulky
                        , coalesce(route_values.delivered_non_rts_bulky_revise,0) as delivered_non_rts_bulky_revise
                        , coalesce(route_values.delivered_rts_regular,0) as delivered_rts_regular
                        , coalesce(route_values.delivered_rts_bulky,0) as delivered_rts_bulky
                        , coalesce(route_values.picked_regular,0) as picked_regular
                        , coalesce(route_values.picked_bulky,0) as picked_bulky

                        , coalesce(route_values.delivered_non_rts_standard_regular_points,0) as delivered_non_rts_standard_regular_points
                        , coalesce(route_values.delivered_non_rts_standard_bulky_points,0) as delivered_non_rts_standard_bulky_points
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_regular_points,0) as delivered_non_rts_fs_cs_retail_regular_points
                        , coalesce(route_values.delivered_non_rts_fs_cs_retail_bulky_points,0) as delivered_non_rts_fs_cs_retail_bulky_points
                        , coalesce(route_values.delivered_non_rts_superbulky_points,0) as delivered_non_rts_superbulky_points
                        , coalesce(route_values.delivered_non_rts_bulky_revise_points,0) as delivered_non_rts_bulky_revise_points
                        , coalesce(route_values.delivered_rts_regular_points,0) as delivered_rts_regular_points
                        , coalesce(route_values.delivered_rts_bulky_points,0) as delivered_rts_bulky_points
                        , coalesce(route_values.picked_regular_points,0) as picked_regular_points
                        , coalesce(route_values.picked_bulky_points,0) as picked_bulky_points
                    from route_attributes
                    left join route_values
                        on route_attributes.route_id = route_values.route_id
                    left join reservations_cleaned
                        on route_attributes.route_id = reservations_cleaned.route_id
                    where 
                        route_values.route_id is not null
                        or reservations_cleaned.route_id is not null

                ),
                final as (
                
                    select
                        *
                        , (delivered_non_rts_standard_regular_points
                            + delivered_non_rts_standard_bulky_points
                            + delivered_non_rts_fs_cs_retail_regular_points
                            + delivered_non_rts_fs_cs_retail_bulky_points
                            + delivered_non_rts_superbulky_points
                            + delivered_non_rts_bulky_revise_points
                            + delivered_rts_regular_points
                            + delivered_rts_bulky_points
                            + picked_regular_points
                            + picked_bulky_points
                            ) as total_ocd_points
                    from base

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="daily_base",
                jinja_template="""
                with base as (
                
                    select
                        system_id
                        , route_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , driver_hub_id
                        , driver_hub_region
                        , driver_hub_density_category
                        , driver_hub_umk_category
                        , route_hub_region_scheme
                        , route_hub_region
                        , route_hub_id
                        , route_hub_name
                        , zone_id
                        , zone_name
                        , coalesce(zone_density,driver_hub_density) as density
                        , coalesce(zone_density_category,driver_hub_density_category) as density_category
                        , coalesce(zone_umk_category,driver_hub_umk_category) as umk_category
                        , total_ocd_points as total_points
                    from route_points

                ),
                density_ranking as (
                
                    select
                        base.*
                        , cast(id_density_priority.rank as int) as density_rank
                        , cast(umk_category as int) as umk_rank
                    from base
                    left join id_density_priority
                        on base.density_category = lower(id_density_priority.density_category)

                ),
                combined_rank as (
                
                    select
                        *
                        , row_number() over (partition by route_date, driver_id order by total_points desc, density_rank asc, umk_rank desc) as pref_rank
                    from density_ranking
                
                ),
                filtered as (
                
                    select
                        system_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , driver_hub_id
                        , driver_hub_region
                        , driver_hub_density_category
                        , driver_hub_umk_category
                        , route_hub_region_scheme
                        , route_hub_region
                        , route_hub_id
                        , route_hub_name
                        , zone_id
                        , zone_name
                        , density
                        , density_category
                        , umk_category
                    from combined_rank 
                    where pref_rank = 1

                ),
                sum as (

                    select
                        route_date
                        , driver_id
                        , sum(delivered_non_rts_fs_regular) as delivered_non_rts_fs_regular
                        , sum(delivered_non_rts_fs_bulky) as delivered_non_rts_fs_bulky
                        , sum(delivered_non_rts_cs_regular) as delivered_non_rts_cs_regular
                        , sum(delivered_non_rts_cs_bulky) as delivered_non_rts_cs_bulky
                        , sum(delivered_non_rts_retail_regular) as delivered_non_rts_retail_regular
                        , sum(delivered_non_rts_retail_bulky) as delivered_non_rts_retail_bulky
                        , sum(delivered_rts_regular) as delivered_rts_regular
                        , sum(delivered_rts_bulky) as delivered_rts_bulky
                        , sum(delivered_non_rts_fs_cs_retail_regular) as delivered_non_rts_fs_cs_retail_regular
                        , sum(delivered_non_rts_fs_cs_retail_bulky) as delivered_non_rts_fs_cs_retail_bulky
                        , sum(delivered_non_rts_standard_regular) as delivered_non_rts_standard_regular
                        , sum(delivered_non_rts_standard_bulky) as delivered_non_rts_standard_bulky
                        , sum(delivered_non_rts_superbulky) as delivered_non_rts_superbulky
                        , sum(delivered_non_rts_bulky_revise) as delivered_non_rts_bulky_revise
                        , sum(picked_regular) as picked_regular
                        , sum(picked_bulky) as picked_bulky

                        , sum(delivered_rts_regular_points) as delivered_rts_regular_points
                        , sum(delivered_rts_bulky_points) as delivered_rts_bulky_points
                        , sum(delivered_non_rts_fs_cs_retail_regular_points) as delivered_non_rts_fs_cs_retail_regular_points
                        , sum(delivered_non_rts_fs_cs_retail_bulky_points) as delivered_non_rts_fs_cs_retail_bulky_points
                        , sum(delivered_non_rts_standard_regular_points) as delivered_non_rts_standard_regular_points
                        , sum(delivered_non_rts_standard_bulky_points) as delivered_non_rts_standard_bulky_points
                        , sum(delivered_non_rts_superbulky_points) as delivered_non_rts_superbulky_points
                        , sum(delivered_non_rts_bulky_revise_points) as delivered_non_rts_bulky_revise_points
                        , sum(picked_regular_points) as picked_regular_points
                        , sum(picked_bulky_points) as picked_bulky_points
                        , sum(total_ocd_points) as total_ocd_points
                    from route_points
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (
                
                    select
                        filtered.system_id
                        , filtered.route_date
                        , date_format(filtered.route_date, 'yyyy-MM') as route_month
                        , filtered.driver_id
                        , filtered.driver_display_name
                        , filtered.driver_type
                        , filtered.driver_hub_id
                        , filtered.driver_hub_region
                        , filtered.driver_hub_density_category
                        , filtered.driver_hub_umk_category
                        , filtered.route_hub_region_scheme
                        , filtered.route_hub_region
                        , filtered.route_hub_id
                        , filtered.route_hub_name
                        , filtered.zone_id
                        , filtered.zone_name
                        , filtered.density
                        , filtered.density_category
                        , filtered.umk_category
                        , sum.delivered_non_rts_fs_regular
                        , sum.delivered_non_rts_fs_bulky
                        , sum.delivered_non_rts_cs_regular
                        , sum.delivered_non_rts_cs_bulky
                        , sum.delivered_non_rts_retail_regular
                        , sum.delivered_non_rts_retail_bulky
                        , sum.delivered_non_rts_standard_regular
                        , sum.delivered_non_rts_standard_bulky
                        , sum.delivered_non_rts_fs_cs_retail_regular
                        , sum.delivered_non_rts_fs_cs_retail_bulky
                        , sum.delivered_non_rts_superbulky
                        , sum.delivered_non_rts_bulky_revise
                        , sum.delivered_rts_regular
                        , sum.delivered_rts_bulky
                        , sum.picked_regular
                        , sum.picked_bulky

                        , sum.delivered_non_rts_standard_regular_points
                        , sum.delivered_non_rts_standard_bulky_points
                        , sum.delivered_non_rts_fs_cs_retail_regular_points
                        , sum.delivered_non_rts_fs_cs_retail_bulky_points
                        , sum.delivered_non_rts_superbulky_points
                        , sum.delivered_non_rts_bulky_revise_points
                        , sum.delivered_rts_regular_points
                        , sum.delivered_rts_bulky_points
                        , sum.picked_regular_points
                        , sum.picked_bulky_points
                        , sum.total_ocd_points
                    from filtered
                    left join sum
                        on filtered.route_date = sum.route_date
                        and filtered.driver_id = sum.driver_id
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="courier_monthly_density",
                jinja_template="""
                with base as (
                
                    select
                        daily_base.driver_id
                        , daily_base.route_month
                        , daily_base.density_category
                        , cast(id_density_priority.rank as int) as density_rank
                        , count(daily_base.route_date) as density_frequency
                    from daily_base
                    left join id_density_priority
                        on daily_base.density_category = lower(id_density_priority.density_category)
                    group by {{ range(1, 5) | join(',') }}

                ),
                ranking as (
                
                    select
                        driver_id
                        , route_month
                        , density_category
                        , density_rank
                        , density_frequency
                        , row_number() over (partition by route_month, driver_id order by density_frequency desc, density_rank asc) as pref_rank
                    from base

                ),
                final as(
                
                    select
                        driver_id
                        , route_month
                        , density_category
                    from ranking
                    where pref_rank = 1

                )
                
                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                
                with base as (
                    select
                        daily_base.system_id
                        , daily_base.route_date
                        , daily_base.route_month
                        , courier_monthly_density.density_category as monthly_density_category
                        , daily_base.driver_id
                        , daily_base.driver_display_name
                        , daily_base.driver_type
                        , daily_base.driver_hub_id
                        , daily_base.driver_hub_region
                        , daily_base.route_hub_region_scheme
                        , daily_base.route_hub_region
                        , daily_base.route_hub_id
                        , daily_base.route_hub_name
                        , daily_base.zone_id
                        , daily_base.zone_name
                        , daily_base.density
                        , daily_base.density_category
                        , daily_base.umk_category
                        , daily_base.picked_regular
                        , daily_base.picked_bulky
                        , daily_base.delivered_non_rts_fs_regular
                        , daily_base.delivered_non_rts_fs_bulky
                        , daily_base.delivered_non_rts_cs_regular
                        , daily_base.delivered_non_rts_cs_bulky
                        , daily_base.delivered_non_rts_retail_regular
                        , daily_base.delivered_non_rts_retail_bulky
                        , daily_base.delivered_non_rts_standard_regular
                        , daily_base.delivered_non_rts_standard_bulky
                        , daily_base.delivered_non_rts_fs_cs_retail_regular
                        , daily_base.delivered_non_rts_fs_cs_retail_bulky
                        , daily_base.delivered_non_rts_superbulky
                        , daily_base.delivered_non_rts_bulky_revise
                        , daily_base.delivered_rts_regular
                        , daily_base.delivered_rts_bulky
                        , daily_base.picked_regular_points
                        , daily_base.picked_bulky_points
                        , cast(daily_base.delivered_non_rts_standard_regular_points as double) as delivered_non_rts_standard_regular_points
                        , cast(daily_base.delivered_non_rts_standard_bulky_points as double) as delivered_non_rts_standard_bulky_points
                        , cast(daily_base.delivered_non_rts_fs_cs_retail_regular_points as double) as delivered_non_rts_fs_cs_retail_regular_points
                        , cast(daily_base.delivered_non_rts_fs_cs_retail_bulky_points as double) as delivered_non_rts_fs_cs_retail_bulky_points
                        , daily_base.delivered_non_rts_superbulky_points
                        , daily_base.delivered_non_rts_bulky_revise_points
                        , daily_base.delivered_rts_regular_points
                        , daily_base.delivered_rts_bulky_points
                        , coalesce(daily_base.delivered_non_rts_standard_regular_points * daily_rates.parcel_rate, 0) as delivered_non_rts_standard_regular_pay
                        , coalesce(daily_base.delivered_non_rts_standard_bulky_points * daily_rates.parcel_rate, 0) as delivered_non_rts_standard_bulky_pay
                        , coalesce(daily_base.delivered_non_rts_fs_cs_retail_regular_points * daily_rates.parcel_rate, 0) as delivered_non_rts_fs_cs_retail_regular_pay
                        , coalesce(daily_base.delivered_non_rts_fs_cs_retail_bulky_points * daily_rates.parcel_rate, 0) as delivered_non_rts_fs_cs_retail_bulky_pay
                        , coalesce(daily_base.delivered_non_rts_superbulky_points * daily_rates.parcel_rate, 0) as delivered_non_rts_superbulky_pay
                        , coalesce(daily_base.delivered_non_rts_bulky_revise_points * daily_rates.parcel_rate, 0) as delivered_non_rts_bulky_revise_pay
                        , coalesce(daily_base.delivered_rts_regular_points * daily_rates.parcel_rate, 0) as delivered_rts_regular_pay
                        , coalesce(daily_base.delivered_rts_bulky_points * daily_rates.parcel_rate, 0) as delivered_rts_bulky_pay
                        , daily_base.total_ocd_points
                        , coalesce(daily_base.total_ocd_points * daily_rates.parcel_rate, 0) as base_parcel_pay
                        , coalesce(volume_bonus.bonus, 0) as volume_bonus
                        from daily_base
                        left join courier_monthly_density
                            on daily_base.route_month = courier_monthly_density.route_month
                            and daily_base.driver_id = courier_monthly_density.driver_id
                        left join parcel_rates as daily_rates
                            on daily_base.umk_category = daily_rates.umk_category
                            and daily_base.route_month = daily_rates.effective_month
                            and daily_base.density_category = lower(daily_rates.density_category)
                            and daily_base.route_hub_region_scheme = daily_rates.region
                        left join volume_bonus
                            on daily_base.route_hub_region_scheme = volume_bonus.region
                            and daily_base.route_month = volume_bonus.effective_month
                            and daily_base.density = lower(volume_bonus.density)
                            and daily_base.total_ocd_points >= volume_bonus.range_start
                            and daily_base.total_ocd_points < volume_bonus.range_end
                
                ),
                final as (
                
                    select
                        *
                        , (delivered_non_rts_standard_regular
                            + delivered_non_rts_standard_bulky
                            + delivered_non_rts_fs_cs_retail_regular
                            + delivered_non_rts_fs_cs_retail_bulky
                            + delivered_non_rts_superbulky
                            + delivered_non_rts_bulky_revise
                            + delivered_rts_regular
                            + delivered_rts_bulky
                            ) as total_delivered_parcels
                        , base_parcel_pay + volume_bonus as sum_total
                        , route_month as created_month
                    from base

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_LM_PAYROLL_DAILY_ADJUSTED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
        update_latest_with_historical=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
