import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CrossBorderDAG.Task.XB_VENDOR_RATE_CARDS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.CrossBorderDAG.Task.XB_VENDOR_RATE_CARDS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.CrossBorderDAG.Task.XB_VENDOR_RATE_CARDS_EXPANDED_FEES_MASKED,
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="pricing"),),
)


def get_task_config(env, measurement_datetime):
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).XB_VENDOR_RATE_CARDS_EXPANDED_FEES,
                view_name="xb_vendor_rate_cards_expanded_fees",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).VENDOR_RATE_CARDS,
                view_name="vendor_rate_cards",
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).VENDOR_PORTS,
                view_name="vendor_ports",
            ),
            base.InputTable(
                path=delta_tables.ThreePlProdGL(input_env, is_masked).VENDORS,
                view_name="vendors",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- Group fees type by vendor_rate_card_id
                with fees_flat as (

                    select
                        vendor_rate_card_id
                    {%- for col_name, type_string in col_name_to_type_string.items() %}
                        , max(currency) filter(where type = '{{ type_string }}') as {{ col_name }}_fee_currency
                        , max(unit) filter(where type = '{{ type_string }}') as {{ col_name }}_fee_unit
                        , max(value) filter(where type = '{{ type_string }}') as {{ col_name }}_fee_value
                    {%- endfor %}
                        , max(effective_date_start) filter(where type = 'FuelPrice') as fuel_fee_effective_date_start
                        , max(currency) filter(where type = 'RangesFees') as ranges_fee_currency
                    from xb_vendor_rate_cards_expanded_fees
                    group by 1

                )

                select
                    'gl' as system_id
                    , base.id vendor_rate_card_id
                    , base.vendor_id
                    , vendors.name vendor_name
                    , origin_port.country as origin_country
                    , origin_port.code as origin_port
                    , destination_port.country as destination_country
                    , destination_port.code as destination_port
                    , case base.transport_type
                    {%- for type, name in transport_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                    end as transport_type
                    , case base.freight_type
                    {%- for type, name in freight_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                    end as freight_type
                    , case base.service_type
                    {%- for type, name in service_type_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                    end as service_type
                    
                    -- Flight details are maintained in an array
                {%- for rank, prefix in rank_to_prefix.items() %}
                    , get_json_object(base.rates, '$.flights[{{ rank }}].origin_port') as {{ prefix }}_flight_origin_port
                    , get_json_object(base.rates, '$.flights[{{ rank }}].destination_port')
                        as {{ prefix }}_flight_destination_port
                    , get_json_object(base.rates, '$.flights[{{ rank }}].number') as {{ prefix }}_flight_number
                    , replace(replace(replace(get_json_object(base.rates, '$.flights[{{ rank }}].frequency'), ']'), '['), '"')
                        as {{ prefix }}_flight_frequency
                    , get_json_object(base.rates, '$.flights[{{ rank }}].eta') as {{ prefix }}_flight_eta
                    , get_json_object(base.rates, '$.flights[{{ rank }}].etd') as {{ prefix }}_flight_etd
                {%- endfor %}
                    
                    , get_json_object(base.rates, '$.export_type') as export_type
                    , get_json_object(base.rates, '$.ad_hoc') as ad_hoc
                    {%- for col_name, type_string in col_name_to_type_string.items() %}
                    , fees.{{ col_name }}_fee_currency
                    , fees.{{ col_name }}_fee_unit
                    , fees.{{ col_name }}_fee_value
                    {%- endfor %}
                    , fees.fuel_fee_effective_date_start
                    , fees.ranges_fee_currency
                    , get_json_object(base.rates, '$.rounding_logic') rounding_logic
                    , base.charging_mechanism as charging_mechanism_id
                    , cast(get_json_object(base.charging_mechanism_value, '$.multiplication_factor') as string)
                        as multiplication_factor
                    , cast(get_json_object(base.charging_mechanism_value, '$.threshold_value') as double)
                        as threshold_value
                    , cast(get_json_object(base.charging_mechanism_value, '$.chargeable_weight_multiplier') as double)
                        as chargeable_weight_multiplier
                    , cast(get_json_object(base.charging_mechanism_value, '$.bsa_committed_volume') as double)
                        as bsa_committed_volume
                    , cast(get_json_object(base.charging_mechanism_value, '$.minimum_bsa_committed_volume') as double)
                        as minimum_bsa_committed_volume
                    , get_json_object(base.rates, '$.remarks') as remarks
                    , case base.status
                    {%- for type, name in status_to_name.items() %}
                        when {{ type }} then '{{ name }}'
                    {%- endfor %}
                    end as status
                    , base.effective_day as effective_start_date
                    , date_format(base.created_at, 'yyyy-MM') as created_month
                from vendor_rate_cards as base
                left join vendors as vendors
                    on base.vendor_id = vendors.id
                left join vendor_ports as origin_port
                    on base.origin_port_id = origin_port.id
                left join vendor_ports as destination_port
                    on base.destination_port_id = destination_port.id
                left join fees_flat as fees
                    on base.id = fees.vendor_rate_card_id
                """,
                jinja_arguments={
                    "col_name_to_type_string":{
                        "mawb":"MAWBFees",
                        "tc":"TCPrice",
                        "hub_to_port":"HubToPortPrice",
                        "other":"OtherFees",
                        "import_clearance":"ImportClearanceFees",
                        "dangerous_goods":"DangerousGoodsFees",
                        "fuel":"FuelPrice",
                    },
                    "transport_type_to_name":{
                        1:"Air",
                        2:"Ocean",
                        3:"Land",
                    },
                    "freight_type_to_name":{
                        1:"Sensitive",
                        2:"Normal",
                    },
                    "service_type_to_name":{
                        1:"MM",
                        2:"MMCC",
                    },
                    "status_to_name":{
                        1:"Active",
                        2:"Draft",
                        3:"Inactive",
                    },
                    "rank_to_prefix":{
                        0:"first",
                        1:"second",
                    },
                }
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).XB_VENDOR_RATE_CARDS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
