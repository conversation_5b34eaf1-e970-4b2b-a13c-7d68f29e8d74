import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.DRIVER_TYPES_ENRICHED_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVER_TYPES_ENRICHED,
                view_name="driver_types_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_ZONE_PREFERENCES, view_name="driver_zone_preferences"
            ),
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_CONTACTS, view_name="driver_contacts"),
            base.InputTable(path=delta_tables.AddressingProdGL(input_env, is_masked).ZONES, view_name="zones"),
            base.InputTable(path=delta_tables.HubProdGL(input_env, is_masked).MM_DRIVER_DETAILS, view_name="mm_driver_details"),
            base.InputTable(path=delta_tables.HubProdGL(input_env, is_masked).LANDHAUL_VENDORS, view_name="landhaul_vendors"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="first_active_contact_details",
                jinja_template="""
                select
                    driver_id
                    -- order by id instead of created_at because there are multiple rows with the same created_at
                    , min_by(contact_details, id) as contact_details
                from driver_contacts
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="preferred_zone",
                jinja_template="""
                select
                    driver_id
                    , max_by(zone_id, rank) as zone_id
                from driver_zone_preferences
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    drivers.system_id as country
                    , drivers.id
                    , drivers.first_name
                    , drivers.last_name
                    , drivers.display_name
                    , contact_details.contact_details
                    , drivers.hub_id
                    , hubs.name as hub_name
                    , hubs.region as hub_region
                    , zones.name as zone
                    , drivers.employment_type
                    , gdrive.driver_type
                    , if(
                        drivers.system_id = 'sg'
                        , trim(substring_index(substring_index(first_name,  '-', 2), '-', -1))
                        , null
                    ) as driver_subtype
                    , gdrive.fleet_type
                    , gdrive.scheme_type
                    , gdrive.veh_type
                    , date(
                        from_utc_timestamp(drivers.employment_start_date, {{ get_local_timezone }})
                    ) as employment_start_date
                    , date(
                        from_utc_timestamp(drivers.employment_end_date, {{ get_local_timezone }})
                    ) as employment_end_date
                    , drivers.comments
                    , landhaul_vendors.name as vendor_name
                    , date(from_utc_timestamp(drivers.created_at, {{ get_local_timezone }})) as creation_date
                    , if(drivers.deleted_at is not null, 1, 0) as is_deleted
                    , drivers.system_id
                    , date_format(drivers.created_at, 'yyyy-MM') as created_month
                from drivers
                left join hubs_enriched as hubs on
                    drivers.hub_id = hubs.id
                    and drivers.system_id = hubs.system_id
                left join preferred_zone on
                    drivers.id = preferred_zone.driver_id
                left join zones on
                    preferred_zone.zone_id = zones.legacy_zone_id
                    and drivers.system_id = lower(zones.system_id)
                left join driver_types_enriched as gdrive on
                    drivers.driver_type_id = gdrive.id
                left join first_active_contact_details as contact_details on
                    drivers.id = contact_details.driver_id
                left join mm_driver_details on
                    drivers.id = mm_driver_details.driver_id
                    and mm_driver_details.deleted_at is null
                left join landhaul_vendors on
                    mm_driver_details.vendor_id = landhaul_vendors.id
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("drivers.system_id")},
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DRIVERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
