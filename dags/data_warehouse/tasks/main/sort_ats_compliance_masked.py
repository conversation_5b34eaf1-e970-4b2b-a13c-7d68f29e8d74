import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.HubsDAG.Task.SORT_ATS_COMPLIANCE_MASKED + ".py",
    task_name=data_warehouse.HubsDAG.Task.SORT_ATS_COMPLIANCE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.HubsDAG.Task.SORT_ATS_COMPLIANCE_BASE_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SORT_ATS_COMPLIANCE_BASE,
                view_name="sort_ats_compliance_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="ats_data",
                jinja_template="""
                with final as (
                
                    select
                        shipment_orders_enriched.system_id
                        , shipment_orders_enriched.order_id
                        , shipment_orders_enriched.shipment_id
                        , shipment_orders_enriched.last_add_to_shipment_datetime as ats_datetime
                        , shipment_orders_enriched.del_from_shipment_datetime as ats_reversal_datetime
                        , shipments_enriched.dest_hub_id
                    from shipment_orders_enriched
                    left join shipments_enriched
                        on shipment_orders_enriched.shipment_id = shipments_enriched.shipment_id

                )

                select * from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("system_id"),
                },
            ),
            base.TransformView(
                view_name="hub_seq",
                jinja_template="""
                with hub_flag as (
                
                    select
                        system_id
                        , cast(order_id as bigint) as order_id
                        , id
                        , scan_datetime
                        , coalesce_parent_hub_id
                        , coalesce_parent_hub_name
                        , case 
                            when lag(coalesce_parent_hub_id) over (
                                                    partition by order_id 
                                                    order by id asc
                                                    ) != coalesce_parent_hub_id then 1 
                            else 0 
                            end as hub_change_flag
                    from sort_ats_compliance_base
                    
                ),  
                seq_prep as (
                
                    select
                        *
                        , sum(hub_change_flag) over (
                                                    partition by order_id
                                                    order by id asc
                                                    )
                            as hub_seq
                    from hub_flag
                
                ),
                hub_sequence as (
                
                    select
                        system_id
                        , order_id
                        , hub_seq
                        , coalesce_parent_hub_id
                        , coalesce_parent_hub_name
                        , min(scan_datetime) as first_scan_datetime
                        , max(scan_datetime) as last_scan_datetime
                    from seq_prep
                group by {{ range(1, 6) | join(',') }}
                
                ), 
                prev_next_datetimes as (
                
                    select
                        system_id
                        , order_id
                        , hub_seq
                        , coalesce_parent_hub_id as hub_id
                        , coalesce_parent_hub_name as hub_name
                        , first_scan_datetime
                        , last_scan_datetime
                        , lag(last_scan_datetime) over (
                            partition by order_id
                            order by hub_seq asc
                            ) as prev_hub_last_scan_datetime
                        , lead(first_scan_datetime) over (
                            partition by order_id
                            order by hub_seq asc
                            ) as next_hub_first_scan_datetime
                        , lead(coalesce_parent_hub_id) over (
                            partition by order_id
                            order by hub_seq asc
                            ) as next_hub_id
                    from hub_sequence
                
                ),
                journey_count as(
                
                    select
                        hub_sequence.system_id
                        , hub_sequence.order_id
                        , max(hub_sequence.hub_seq) as order_journey_count
                    from hub_sequence
                group by {{ range(1, 3) | join(',') }}
                
                ),
                final as (
                
                    select
                        prev_next_datetimes.system_id
                        , prev_next_datetimes.order_id
                        , prev_next_datetimes.first_scan_datetime
                        , prev_next_datetimes.last_scan_datetime
                        , prev_next_datetimes.prev_hub_last_scan_datetime
                        , prev_next_datetimes.next_hub_first_scan_datetime
                        , prev_next_datetimes.next_hub_id
                        , prev_next_datetimes.hub_id
                        , prev_next_datetimes.hub_name
                        , prev_next_datetimes.hub_seq
                        , case 
                            when prev_next_datetimes.hub_seq = journey_count.order_journey_count
                                and prev_next_datetimes.hub_id = coalesce(order_milestones.delivery_success_hub_id
                                                                        , order_milestones.last_valid_rts_attempt_hub_id
                                                                        , order_milestones.last_valid_delivery_attempt_hub_id
                                                                        , order_milestones.rts_dest_hub_id
                                                                        , order_milestones.dest_hub_id)
                            then 1 else 0
                            end as last_hub_flag
                        , case
                            when order_milestones.rts_trigger_datetime is null then 0
                            when first_scan_datetime > order_milestones.rts_trigger_datetime then 1
                            else 0
                            end as rts_leg_flag
                    from prev_next_datetimes
                    left join order_milestones
                        on prev_next_datetimes.system_id = order_milestones.system_id
                        and prev_next_datetimes.order_id = order_milestones.order_id
                    left join shippers_enriched
                        on order_milestones.system_id = shippers_enriched.system_id
                        and order_milestones.shipper_id = shippers_enriched.id
                    left join journey_count
                        on prev_next_datetimes.system_id = journey_count.system_id
                        and prev_next_datetimes.order_id = journey_count.order_id
                    where shippers_enriched.sales_channel != 'Test'

                )

                select * from final
                """,
            ),  
            base.TransformView(
                view_name="recovery_exclusions",
                jinja_template="""
                with exclusions as (
                
                    select
                        order_id
                        , creation_datetime
                        , least(resolution_datetime, cancellation_datetime, now()) as resolution_datetime
                    from pets_tickets_enriched
                    where type != 'MISSING'

                ), 
                scans as (

                    select
                        order_id
                        , next_hub_first_scan_datetime as scan_datetime
                        , hub_seq
                    from hub_seq

                ),
                final as  (
                
                    select
                        scans.order_id
                        , scans.hub_seq
                        , max(case when exclusions.order_id is not null then 1 else 0 end) as recovery_exclusion_flag
                    from scans
                    left join exclusions
                        on scans.order_id = exclusions.order_id
                        and scans.scan_datetime >= exclusions.creation_datetime
                        and scans.scan_datetime <= exclusions.resolution_datetime
                    group by {{ range(1, 3) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with base as (
                
                    select
                        hub_seq.system_id
                        , hub_seq.order_id
                        , hub_seq.first_scan_datetime
                        , hub_seq.last_scan_datetime
                        , hub_seq.prev_hub_last_scan_datetime
                        , hub_seq.next_hub_first_scan_datetime
                        , hub_seq.next_hub_id
                        , hub_seq.hub_id
                        , hub_seq.hub_name
                        , hub_seq.hub_seq
                        , hub_seq.last_hub_flag
                        , hubs_enriched.sort_hub_flag
                        , hub_seq.rts_leg_flag
                        , recovery_exclusions.recovery_exclusion_flag
                        , max(case when 
                                    ats_data.ats_datetime > hub_seq.last_scan_datetime
                                    and ats_data.ats_datetime < coalesce(hub_seq.next_hub_first_scan_datetime,current_timestamp())
                                    and (ats_data.ats_reversal_datetime is null or ats_data.ats_datetime > ats_data.ats_reversal_datetime or ats_data.ats_reversal_datetime > hub_seq.next_hub_first_scan_datetime)
                                    then 1 else 0 
                                    end) as ats_kpi_flag
                        , max_by(ats_data.shipment_id, case when 
                                                            ats_data.ats_datetime > hub_seq.last_scan_datetime 
                                                            and ats_data.ats_datetime < coalesce(hub_seq.next_hub_first_scan_datetime,current_timestamp())
                                                            and (ats_data.ats_reversal_datetime is null or ats_data.ats_datetime > ats_data.ats_reversal_datetime or ats_data.ats_reversal_datetime > hub_seq.next_hub_first_scan_datetime)
                                                            then ats_data.ats_datetime
                                                            end) as ats_shipment_id
                        , max_by(ats_data.dest_hub_id, case when 
                                                            ats_data.ats_datetime > hub_seq.last_scan_datetime 
                                                            and ats_data.ats_datetime < coalesce(hub_seq.next_hub_first_scan_datetime,current_timestamp())
                                                            and (ats_data.ats_reversal_datetime is null or ats_data.ats_datetime > ats_data.ats_reversal_datetime or ats_data.ats_reversal_datetime > hub_seq.next_hub_first_scan_datetime)
                                                            then ats_data.ats_datetime
                                                            end) as ats_dest_hub_id
                    from hub_seq
                    left join ats_data
                        on hub_seq.system_id = ats_data.system_id
                        and hub_seq.order_id = ats_data.order_id
                    left join hubs_enriched
                        on hub_seq.system_id = hubs_enriched.system_id
                        and hub_seq.hub_id = hubs_enriched.id
                    left join recovery_exclusions
                        on hub_seq.order_id = recovery_exclusions.order_id
                        and hub_seq.hub_seq = recovery_exclusions.hub_seq
                    group by {{ range(1, 15) | join(',') }}

                ),
                final as (
                
                    select
                        system_id
                        , date_format(first_scan_datetime, 'yyyy-MM') as created_month
                        , order_id
                        , first_scan_datetime
                        , last_scan_datetime
                        , prev_hub_last_scan_datetime
                        , next_hub_first_scan_datetime
                        , hub_id
                        , hub_name
                        , hub_seq
                        , last_hub_flag
                        , sort_hub_flag
                        , rts_leg_flag
                        , recovery_exclusion_flag
                        , ats_kpi_flag
                        , ats_shipment_id
                        , ats_dest_hub_id
                        , next_hub_id
                    from base
                
                )

                select * from final
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SORT_ATS_COMPLIANCE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()