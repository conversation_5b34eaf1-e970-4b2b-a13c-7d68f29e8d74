# The output from this task is used for exporting to SFMC only. It should not be exposed to Metabase or used for
# analytics. As such, column naming in this table also need not follow DWH conventions.
import sys

from pyspark.sql import SparkSession
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.SS_SEGMENTATION_DAILY_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.SS_SEGMENTATION_DAILY_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_CONTACT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
        ),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
)

def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    scvd_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    scvm_lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 12, 0)
    if enable_full_run:
        scvd_lookback_ranges = base.LookBackRange(None, None)
        scvm_lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shipper_prod_gl",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
                input_range=scvd_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
                input_range=scvm_lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_LEAD_ENRICHED,
                view_name="salesforce_lead_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CONTACT_ENRICHED,
                view_name="salesforce_contact_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="lead_interim",
                jinja_template="""
                select '{{ measurement_datetime_utc }}' as measurement_datetime
                    , salesforce_lead_enriched.id as lead_id
                    , null as shipper_id
                    , null as account_id
                    , salesforce_lead_enriched.name as account_name
                    , null as contact_id
                    , salesforce_lead_enriched.email
                    , salesforce_lead_enriched.system_id
                    , 'Self Serve' as sales_channel
                    --, salesforce_lead_enriched.creation_datetime as lead_creation_datetime
                    , null as onboarded_date
                    , null as first_order_completion_date
                    , 0 as orders_30d
                    , 0 as orders_90d
                    , coalesce(salesforce_lead_enriched.disqualify_suspect_date
                        , salesforce_lead_enriched.disqualify_prospect_date) as disqualified_date
                    , null as alive_probability
                    , '0. lead' as lifecycle
                    , 0.0 as potential_ppm
                    , null as shipper_tier
                    , salesforce_lead_enriched.lead_gen_channel
                    , salesforce_lead_enriched.lead_source
                    , salesforce_lead_enriched.lead_source_details
                from salesforce_lead_enriched
                left join salesforce_account_enriched
                    on salesforce_lead_enriched.account_id = salesforce_account_enriched.id
                where True
                    -- remove ninja direct & crossborder
                    and lower(salesforce_lead_enriched.record_type) not like '%ninja direct%'
                    and lower(salesforce_lead_enriched.record_type) not like '%crossborder%'
                    -- remove those with salesforce_account before measurement_datetime
                    and (salesforce_lead_enriched.account_id is null
                        or
                        salesforce_account_enriched.creation_datetime >= date('{{ measurement_datetime_utc }}')
                        )
                    -- only taking disqualified leads with following reasons
                    and disqualification_reason in (
                            'Too Small'
                            , 'Max no. of attempts reached'
                            --, 'No Potential Business'
                            --, 'Poor Information'
                        )
                    -- remove unacquired leads disqualified more than 180 days ago
                    and coalesce(salesforce_lead_enriched.disqualify_suspect_date
                            , salesforce_lead_enriched.disqualify_prospect_date)
                        >= date('{{ measurement_datetime_utc }}') - interval '180' day
                    and coalesce(salesforce_lead_enriched.disqualify_suspect_date
                            , salesforce_lead_enriched.disqualify_prospect_date)
                        <= date('{{ measurement_datetime_utc }}')
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="core_interim",
                jinja_template="""
                with 30d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_30d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '30' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , 90d_tb as (
                    select shipper_id
                        , sum(total_orders) as orders_90d
                    from shipper_completion_vol_daily
                    where completion_date >= date('{{ measurement_datetime_utc }}') - interval '90' day
                        and completion_date <= date('{{ measurement_datetime_utc }}')
                    group by 1
                )

                , latest_contact as (
                    select account_id
                        , max_by(id, coalesce(creation_datetime
                            , '{{ measurement_datetime_utc }}')) as contact_id
                        , max_by(phone, coalesce(creation_datetime
                            , '{{ measurement_datetime_utc }}')) as phone
                        , max_by(email, coalesce(creation_datetime
                            , '{{ measurement_datetime_utc }}')) as email
                    from salesforce_contact_enriched
                    where email != 'None' and email is not null
                    group by 1
                )

                , lifecycle_tb as (
                    select '{{ measurement_datetime_utc }}' as measurement_datetime
                        , salesforce_lead_enriched.id as lead_id
                        , se.sf_acc_id as account_id
                        , se.id as shipper_id
                        , se.shipper_name as account_name
                        , latest_contact.contact_id
                        , se.system_id
                        , shipper_prod_gl.email
                        , case
                            when se.sales_channel = 'Self Serve'
                                and lower(account_creation_source) like '%mobile%' then 'DASH Mobile'
                            when se.original_sales_channel = 'Field Sales'
                                and se.sales_channel = 'Self Serve' then 'DASH Mobile'
                        else 'DASH Lite' end as sales_channel
                        , se.onboarded_date
                        , se.first_order_completion_date
                        , se.first_order_placed_date
                        , se.last_order_placed_date
                        , 30d_tb.orders_30d
                        , 90d_tb.orders_90d
                        , se.alive_probability
                        , case
                            when se.onboarded_date >= date('{{ measurement_datetime_utc }}') 
                                    - interval '180' day 
                                and se.first_order_completion_date is null
                                then '1. dormant'

                            when se.first_order_completion_date
                                >= date('{{ measurement_datetime_utc }}') - interval '30' day 
                                then '2. new'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >= 1 
                                and se.alive_probability > 0.8
                                then '3. active'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and orders_30d >=1
                                and se.alive_probability <= 0.8
                                then '4. at_risk'

                            when se.first_order_completion_date
                                < date('{{ measurement_datetime_utc }}') - interval '30' day
                                and (orders_30d = 0 or orders_30d is null)
                                and orders_90d >= 1 
                                then '5. lapsed'

                        else 'inactive' end as lifecycle

                    from shippers_enriched as se
                    left join 90d_tb 
                        on se.id = 90d_tb.shipper_id
                    left join 30d_tb  
                        on se.id = 30d_tb.shipper_id
                    left join shipper_prod_gl
                        on se.id = shipper_prod_gl.id
                    left join salesforce_lead_enriched
                        on se.sf_acc_id = salesforce_lead_enriched.account_id
                    left join latest_contact
                        on se.sf_acc_id = latest_contact.account_id
                    where True
                        -- only include selected channels
                        and sales_channel in ('Self Serve')
                        and (
                            -- filter to include dormant accounts 
                            (se.first_order_completion_date is null
                            and se.onboarded_date >=date('{{ measurement_datetime_utc }}') 
                                - interval '180' day
                            and se.onboarded_date <=date('{{ measurement_datetime_utc }}'))
                            or
                            -- filter to exclude inactive shippers
                            (90d_tb.orders_90d is not null)
                            )
                )
                , ppm_tb as (
                    select system_id
                        , completion_month
                        , shipper_id
                        , row_number() over (partition by system_id
                            , shipper_id order by total_orders desc) as row_num
                        , total_orders
                    from shipper_completion_vol_monthly
                    where completion_month >= date('{{ measurement_datetime_utc }}') - interval '1' year
                        and completion_month <= date('{{ measurement_datetime_utc }}')
                    order by system_id, shipper_id
                )

                , all_peaks as (
                    select system_id
                        , shipper_id
                        , avg(total_orders) as potential_ppm
                    from ppm_tb
                    where row_num <= 2
                    group by system_id, shipper_id
                    )

                select lifecycle_tb.*
                    , ap.potential_ppm
                    -- different tier cuts for different countries
                    , case
                        when lifecycle_tb.system_id = 'sg' then
                            case
                                when potential_ppm > 100 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 100 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else null end 
                        when lifecycle_tb.system_id = 'my' then
                            case
                                when potential_ppm > 70 then '1. VIP'
                                when potential_ppm > 25 and potential_ppm <= 70 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 25 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else null end 
                        when lifecycle_tb.system_id = 'ph' then
                            case
                                when potential_ppm > 110 then '1. VIP'
                                when potential_ppm > 30 and potential_ppm <= 110 then '2. high'
                                when potential_ppm > 8 and potential_ppm <= 30 then '3. medium'
                                when potential_ppm <= 8 then '4. standard'
                            else null end 
                        when lifecycle_tb.system_id = 'id' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else null end 
                        when lifecycle_tb.system_id = 'vn' then
                            case
                                when potential_ppm > 45 then '1. VIP'
                                when potential_ppm > 15 and potential_ppm <= 45 then '2. high'
                                when potential_ppm > 5 and potential_ppm <= 15 then '3. medium'
                                when potential_ppm <= 5 then '4. standard'
                            else null end 
                        when lifecycle_tb.system_id = 'th' then
                            case
                                when potential_ppm > 150 then '1. VIP'
                                when potential_ppm > 45 and potential_ppm <= 150 then '2. high'
                                when potential_ppm > 10 and potential_ppm <= 45 then '3. medium'
                                when potential_ppm <= 10 then '4. standard'
                            else null end 
                    else null end as shipper_tier
                from lifecycle_tb
                left join all_peaks as ap
                    on lifecycle_tb.system_id = ap.system_id
                    and lifecycle_tb.shipper_id = ap.shipper_id
                where lifecycle != 'inactive'
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="union_table",
                jinja_template="""
                select coalesce(core_interim.measurement_datetime
                        , lead_interim.measurement_datetime) as measurement_datetime
                    , core_interim.account_id
                    , coalesce(core_interim.account_name, lead_interim.account_name) as account_name
                    , coalesce(lead_interim.lead_id, core_interim.lead_id) as lead_id
                    , core_interim.shipper_id
                    , coalesce(core_interim.contact_id, lead_interim.contact_id) as contact_id
                    , coalesce(core_interim.email, lead_interim.email) as email
                    , coalesce(core_interim.sales_channel, lead_interim.sales_channel) as sales_channel
                    --, lead_interim.lead_creation_datetime
                    , lead_interim.disqualified_date
                    , core_interim.onboarded_date
                    , core_interim.first_order_completion_date
                    , core_interim.first_order_placed_date
                    , core_interim.last_order_placed_date
                    , core_interim.alive_probability
                    , core_interim.orders_30d
                    , core_interim.orders_90d
                    , coalesce(core_interim.lifecycle, lead_interim.lifecycle) as lifecycle
                    , core_interim.potential_ppm
                    , coalesce(core_interim.shipper_tier, lead_interim.shipper_tier) as shipper_tier
                    , lead_interim.lead_gen_channel
                    , lead_interim.lead_source
                    , lead_interim.lead_source_details
                    , coalesce(core_interim.system_id, lead_interim.system_id) as system_id
                from core_interim
                full outer join lead_interim
                    on core_interim.email = lead_interim.email

                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="ss_segmentation_daily",
                jinja_template="""
                select 
                    date_format( 
                        date_trunc('month', measurement_datetime )
                        , "yyyy-MM-dd") as created_month
                    , measurement_datetime as snapshot_date
                    , lead_id as sf_lead_id
                    , account_id as sf_acc_id
                    , contact_id as sf_contact_id
                    , cast(shipper_id as string) as shipper_id
                    , account_name as shipper_name
                    , email as email
                    , sales_channel
                    , disqualified_date
                    , onboarded_date
                    , first_order_completion_date
                    , first_order_placed_date
                    , last_order_placed_date
                    , alive_probability
                    , cast(orders_30d as int) as orders_30d
                    , cast(orders_90d as int) as orders_90d
                    , system_id 
                    , lifecycle
                    , potential_ppm
                    , shipper_tier
                    , lead_gen_channel
                    , lead_source
                    , lead_source_details
                from union_table
                where sales_channel is not null
                    and system_id is not null
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SS_SEGMENTATION_DAILY,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()