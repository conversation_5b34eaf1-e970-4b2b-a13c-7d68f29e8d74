import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_DRIVER_APP_ADOPTION_KPI_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_DRIVER_APP_ADOPTION_KPI_MASKED,
    depends_on=(
        data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 3)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EVENTS,
                view_name="shipment_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SHIPMENT_SCANS,
                view_name="trip_shipment_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_EXT_AWBS,
                view_name="shipment_ext_awbs",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_SEAWAY_BILLS,
                view_name="shipment_seaway_bills",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SEAWAY_BILLS,
                view_name="seaway_bills",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).VENDORS,
                view_name="vendors",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SEA_VENDORS,
                view_name="sea_vendors",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="manifest_scan",
                jinja_template="""
                select
                    shipment_id
                from shipment_events
                where event = 'SUBMIT_MANIFEST_SCAN'
            """,
            ),
            base.TransformView(
                view_name="trip_unscanned_info",
                jinja_template="""
                select
                    trip_unscanned_shipments.*
                    , movement_trips_enriched.origin_hub_name trip_origin
                    , movement_trips_enriched.dest_hub_name trip_dest
                    , movement_trips_enriched.movement_classification
                    , movement_trips_enriched.primary_driver_id
                from trip_unscanned_shipments
                left join movement_trips_enriched
                    on movement_trips_enriched.trip_id = trip_unscanned_shipments.trip_id
                where type = 'STAYOVER' and scan_type = 'SHIPMENT_VAN_INBOUND'
                """,
            ),

            base.TransformView(
                view_name="sea_vendor_cte",
                jinja_template="""
                select
                    shipment_seaway_bills.shipment_id
                    , sea_vendors.name as seahaul_vendor_name
                from shipment_seaway_bills
                left join seaway_bills
                    on shipment_seaway_bills.bill_id = seaway_bills.id
                left join sea_vendors
                    on sea_vendors.id = seaway_bills.vendor_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                 with table_base as (
                    select 
                        shipments_enriched.shipment_id
                        , shipments_enriched.shipment_creation_datetime
                        , shipment_events.hub_system_id
                        , shipment_events.hub_id
                        , shipments_enriched.orig_hub_id
                        , shipments_enriched.orig_hub_name
                        , shipments_enriched.orig_hub_region
                        , shipments_enriched.dest_hub_id
                        , shipments_enriched.dest_hub_name
                        , shipments_enriched.dest_hub_region
                        , shipment_events.event
                        , shipments_enriched.shipment_type
                        , shipment_events.status
                        , coalesce(vendors.name,  sea_vendor_cte.seahaul_vendor_name) as vendor
                        , from_utc_timestamp(shipment_events.created_at, '{{ local_timezone }}') as scan_datetime
                        , shipment_events.user_id
                        , 4 as base_scans_required
                        , cast(get_json_object(get_json_object(shipment_events.ext_data, '$.trip_data'),'$.trip_id') as bigint) as trip_id
                        , get_json_object(shipment_events.ext_data, '$.source') as scan
                        , shipment_events.created_month
                        , shipments_enriched.system_id
                    from shipments_enriched
                    left join shipment_events
                        on shipment_events.shipment_id = shipments_enriched.shipment_id
                    left join shipment_ext_awbs
                        on shipments_enriched.mawb = shipment_ext_awbs.ref_id
                    left join vendors
                        on shipment_ext_awbs.vendor_id = vendors.id
                    left join hubs_enriched origin_hub
                        on shipments_enriched.orig_hub_id = origin_hub.id
                        and shipments_enriched.system_id = origin_hub.system_id
                    left join hubs_enriched dest_hub
                        on shipments_enriched.dest_hub_id = dest_hub.id
                        and shipments_enriched.system_id = dest_hub.system_id
                    left join sea_vendor_cte
                        on sea_vendor_cte.shipment_id = shipments_enriched.shipment_id
                    where 1=1 
                        and lower(shipments_enriched.shipment_type) not in ('others')
                        and (
                            lower(origin_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                            and
                            lower(dest_hub.facility_type) IN ('crossdock', 'station','crossdock_station')
                            )
                        ),

                 scanned as (       
                        select 
                            table_base.shipment_id
                            , table_base.orig_hub_id shipment_origin_hub_id
                            , table_base.orig_hub_name shipment_origin_hub_name
                            , table_base.orig_hub_region shipment_origin_hub_region
                            , table_base.dest_hub_id shipment_dest_hub_id
                            , table_base.dest_hub_name shipment_dest_hub_name
                            , table_base.dest_hub_region shipment_dest_hub_region
                            , table_base.shipment_creation_datetime
                            , table_base.vendor
                            , table_base.shipment_type
                            , table_base.scan_datetime
                            , table_base.base_scans_required
                            , table_base.trip_id
                            , movement_trips_enriched.primary_driver_id
                            , drivers_enriched.display_name primary_driver_display_name
                            , movement_trips_enriched.actual_start_datetime
                            , trip_origin.id scan_origin_hub_id
                            , trip_origin.name scan_origin_hub_name
                            , trip_origin.region scan_origin_hub_region
                            , trip_origin.facility_type scan_origin_facility_type
                            , trip_dest.id scan_dest_hub_id
                            , trip_dest.name scan_dest_hub_name
                            , trip_dest.region scan_dest_hub_region
                            , trip_dest.facility_type scan_dest_facility_type
                            , concat(trip_origin.facility_type,'-',trip_dest.facility_type) movement_type
                            , if(manifest_scan.shipment_id is null, 0, 1) manifest_scan_flag
                            , table_base.scan
                            , table_base.system_id
                            , table_base.created_month
                        from table_base 
                        left join manifest_scan
                            on table_base.shipment_id = manifest_scan.shipment_id
                        left join movement_trips_enriched
                            on movement_trips_enriched.trip_id = table_base.trip_id
                        left join hubs_enriched trip_origin
                            on trip_origin.id = movement_trips_enriched.origin_hub_id
                            and trip_origin.system_id = movement_trips_enriched.system_id
                        left join hubs_enriched trip_dest
                            on trip_dest.id = movement_trips_enriched.dest_hub_id
                            and trip_dest.system_id = movement_trips_enriched.system_id
                        left join drivers_enriched
                            on movement_trips_enriched.primary_driver_id = drivers_enriched.id
                            and movement_trips_enriched.system_id = drivers_enriched.system_id
                        where 1=1 and lower(table_base.scan) like '%shipment%inbound%'
                        ),

                    distinct_driver_trip as (
                    select
                        primary_driver_id
                        , shipment_id
                        , trip_id
                        , date(actual_start_datetime) as trip_date
                        , actual_start_datetime
                    from scanned
                    group by 1,2,3,4,5
                    ),

                    driver_first_last_trip_cte as (
                    select
                        primary_driver_id
                        , shipment_id
                        , trip_id
                        , row_number() over(partition by shipment_id, primary_driver_id, trip_date order by actual_start_datetime asc) drivers_first_trip_flag
                        , row_number() over(partition by shipment_id, primary_driver_id, trip_date order by actual_start_datetime desc) drivers_last_trip_flag
                    from distinct_driver_trip
                    ),

                    -- For each shipment, identify when is the earliest airport or seaport scan
                    earliest_port_scan as (
                        select
                            shipment_id
                            -- For movements that ends at the ports, use hub inbound scan
                            , min(scan_datetime) filter(where (movement_type like '%AIRPORT' or movement_type like '%SEAPORT') and scan like '%HUB%') as earliest_hub_inbound_scan
                            -- For movements that starts at the ports, use van inbound scan
                            , min(scan_datetime) filter(where (movement_type like 'AIRPORT%' or movement_type like 'SEAPORT%') and scan like '%VAN%') as earliest_van_inbound_scan
                            -- Identify the lastest hub inbound scan for the port - hub leg. This is to remove stayovers that occur after.
                            , min(scan_datetime) filter(where (movement_type like 'AIRPORT%' or movement_type like 'SEAPORT%') and scan like '%HUB%') as earliest_hub_inbound_scan_port_to_hub
                        from scanned
                        group by 1
                    ),

                    stayover_flagged as (
                        select
                            scanned.*
                            -- For hub to airport movement indicate stayover at vaninboud scan
                            , case
                                when scanned.shipment_type in ('AIR_HAUL','SEA_HAUL') 
                                    and trip_unscanned_info.type = 'STAYOVER'
                                    -- This is to remove stayover scans at the other leg eg if for identifying van inbound stayover, ignore the van inbound stayovers from PORT - HUB leg.
                                    and (
                                        (scanned.scan_datetime
                                                < coalesce(earliest_port_scan.earliest_hub_inbound_scan, earliest_port_scan.earliest_van_inbound_scan)
                                            and scanned.scan like '%VAN%' 
                                            and driver_first_last_trip_cte.drivers_first_trip_flag = 1
                                        )
                                        -- For hub inbounds, ignore the stayovers that occur from HUB - PORT leg
                                        or (scanned.scan_datetime 
                                                > coalesce(earliest_port_scan.earliest_van_inbound_scan, earliest_port_scan.earliest_hub_inbound_scan)
                                            and scanned.scan like '%HUB%'
                                        )
                                    ) THEN 1
                                ELSE 0
                            END AS stayover_indicator
                            , row_number() over(partition by scanned.shipment_id, scanned.scan_datetime, scanned.scan, scanned.scan_dest_hub_id, scanned.scan_origin_hub_id order by scanned.scan_datetime) rnk
                            , drivers_first_trip_flag
                            , drivers_last_trip_flag
                        from scanned
                        left join driver_first_last_trip_cte
                            on scanned.primary_driver_id = driver_first_last_trip_cte.primary_driver_id
                            and scanned.trip_id = driver_first_last_trip_cte.trip_id
                            and scanned.shipment_id = driver_first_last_trip_cte.shipment_id
                        left join earliest_port_scan
                            on scanned.shipment_id = earliest_port_scan.shipment_id
                        left join trip_unscanned_info
                            on scanned.shipment_id = trip_unscanned_info.shipment_id
                            and ((scanned.scan_dest_hub_name = trip_unscanned_info.trip_origin
                            and scanned.scan like '%VAN_INBOUND%'
                            and scanned.movement_type not like '%PORT%')
                            or (scanned.scan_origin_hub_name = trip_unscanned_info.trip_origin
                            and (scanned.scan_datetime <= earliest_port_scan.earliest_hub_inbound_scan_port_to_hub or earliest_port_scan.earliest_hub_inbound_scan_port_to_hub is null) 
                            and scanned.scan like '%HUB_INBOUND%'))
                    ),

                    final as (
                    select
                        shipment_id
                        , shipment_origin_hub_id
                        , shipment_origin_hub_name
                        , shipment_origin_hub_region
                        , shipment_dest_hub_id
                        , shipment_dest_hub_name
                        , shipment_dest_hub_region
                        , shipment_creation_datetime
                        , vendor
                        , shipment_type
                        , scan_datetime
                        , base_scans_required
                        , trip_id
                        , primary_driver_id
                        , primary_driver_display_name
                        , scan_origin_hub_id
                        , scan_origin_hub_name
                        , scan_origin_hub_region
                        , scan_origin_facility_type
                        , scan_dest_hub_id
                        , scan_dest_hub_name
                        , scan_dest_hub_region
                        , scan_dest_facility_type
                        , movement_type
                        , manifest_scan_flag
                        , stayover_indicator
                        , scan
                        , system_id
                        , created_month
                    from stayover_flagged
                    where rnk = 1
                    )

                    select * from final

                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MIDDLE_MILE_DRIVER_APP_ADOPTION_KPI,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()