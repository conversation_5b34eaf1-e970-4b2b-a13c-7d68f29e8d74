import sys

from pyspark.sql import SparkSession

from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.MITRA_TREND_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.MITRA_TREND_REPORT_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.LAZADA_ORDERS_ENRICHED_MASKED,
    ),
    system_ids=(constants.SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id",)),
    ),
)


def get_task_config(env, system_id, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_ORDERS_ENRICHED,
                view_name="lazada_orders_enriched",
                system_id=system_id
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    mitra_id
                    , mitra_name

                    -- Total number of parcels created in current week
                    , count(order_id) filter (
                        where creation_date >=
                        date(from_utc_timestamp(current_date, '{{ local_timezone }}')- interval 6 day)
                    ) as cw_parcels_created

                    -- Total COD amount in current week
                    , sum(cod_value) filter (
                        where creation_date >=
                        date(from_utc_timestamp(current_date, '{{ local_timezone }}') - interval 6 day)
                    ) as cw_cod_amount

                    -- Total COD amount last week
                    , sum(cod_value) filter (
                        where creation_date >=
                        date(from_utc_timestamp(current_date, '{{ local_timezone }}') - interval 13 day)
                        and creation_datetime <=
                        from_utc_timestamp(current_date, '{{ local_timezone }}') - interval 7 day
                    ) as lw_cod_amount
                    
                    , system_id

                from lazada_orders_enriched
                where first_pick_up = 'Mitra'
                group by 1,2,6

                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper())},
            ),

        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MITRA_TREND_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("mapreduce.fileoutputcommitter.marksuccessfuljobs", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()