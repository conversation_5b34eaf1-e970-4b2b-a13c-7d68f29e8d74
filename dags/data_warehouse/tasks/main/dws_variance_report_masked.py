import sys

from pyspark.sql import SparkSession
from datetime import timed<PERSON><PERSON>

from common import date
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SLABreachDAG.Task.DWS_VARIANCE_REPORT_MASKED + ".py",
    task_name=data_warehouse.SLABreachDAG.Task.DWS_VARIANCE_REPORT_MASKED,
    depends_on=(
        data_warehouse.SLABreachDAG.Task.DWS_SCANS_EVENTS_MASKED,
        data_warehouse.SLABreachDAG.Task.PRIORITISED_LAZADA_ORDERS_MASKED,
    ),
    system_ids=(SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).DWS_SCANS_EVENTS,
                view_name="dws_scans_events",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PRIORITISED_LAZADA_ORDERS,
                view_name="prioritised_lazada_orders",
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""

                select
                    prioritised_lazada_orders.order_id
                    , dws_scans_events.event_datetime
                    , prioritised_lazada_orders.original_weight
                    , dws_scans_events.weight as dws_weight
                    , case
                        when
                            abs(dws_scans_events.weight - prioritised_lazada_orders.original_weight) >= 1
                            and (
                                    ((dws_scans_events.weight - prioritised_lazada_orders.original_weight)/prioritised_lazada_orders.original_weight)*100 < -50
                                    or ((dws_scans_events.weight - prioritised_lazada_orders.original_weight)/prioritised_lazada_orders.original_weight)*100 > 90
                                )
                        then 1
                        else 0
                    end as dws_variance_flag
                    , dws_scans_events.created_month
                    , dws_scans_events.system_id
                from prioritised_lazada_orders
                join dws_scans_events
                    on prioritised_lazada_orders.order_id = dws_scans_events.order_id
                where dws_scans_events.weight is not null

                """,
            ),
            base.TransformView(
                view_name="first_flag",
                jinja_template="""

                -- Get first dws variance flag and scan info

                select
                    order_id
                    , event_datetime as dws_variance_first_flag_timestamp
                    , original_weight
                    , dws_weight as dws_variance_first_dws_value
                    , created_month
                    , system_id
                from (

                    select
                        order_id
                        , event_datetime
                        , original_weight
                        , dws_weight
                        , dws_variance_flag
                        , row_number() over (partition by order_id order by event_datetime) as event_sequence
                        , created_month
                        , system_id
                    from base
                    where dws_variance_flag = 1

                )
                where event_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="current_flag",
                jinja_template="""

                -- Get current dws variance flag and scan info

                select
                    order_id
                    , dws_weight as dws_variance_current_dws_value
                    , dws_variance_flag as dws_variance_current_flag
                    , created_month
                    , system_id
                from (

                    select
                        order_id
                        , event_datetime
                        , original_weight
                        , dws_weight
                        , dws_variance_flag
                        , row_number() over (partition by order_id order by event_datetime desc) as event_sequence_desc
                        , created_month
                        , system_id
                    from base
                    where dws_variance_flag = 1

                )
                where event_sequence_desc = 1

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    current_flag.order_id as order_id
                    , first_flag.original_weight
                    , first_flag.dws_variance_first_flag_timestamp
                    , first_flag.dws_variance_first_dws_value
                    , current_flag.dws_variance_current_flag
                    , current_flag.dws_variance_current_dws_value
                    , current_flag.created_month as created_month
                    , current_flag.system_id as system_id
                from current_flag
                left join first_flag
                    on current_flag.order_id = first_flag.order_id

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DWS_VARIANCE_REPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()