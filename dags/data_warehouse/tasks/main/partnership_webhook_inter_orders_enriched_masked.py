import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from datetime import timedelta
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.WebhookFreqSnapshotDAG.DAG_ID,
            task_id=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED
        ),
    ),
    system_ids=(SystemID.GL,),
)


def get_task_config(spark, env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = "/measurement_datetime=latest/"
    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_PREFIX + latest_partition,
                            view_name="prefix"
                            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES + latest_partition,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                ),
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_PARCELS_ENRICHED + latest_partition,
                            view_name="xb_parcels_enriched",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_EVENTS_ENRICHED + latest_partition,
                            view_name="xb_events_enriched",
                            input_range=lookback_ranges.input,
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Load Past 1 month Webhook Data from the completion_date
    completion_date = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = (measurement_datetime - timedelta(days=61)).strftime("%Y-%m-%d")

    spark.read.format("parquet").load(
        parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK + latest_partition) \
        .filter((F.col("created_date") <= completion_date) & (F.col("created_date") >= start_date) & (
                F.col("shipper") == 'Tiktok XB')).createOrReplaceTempView("webhook_log")

    spark.read.format("parquet").load(parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_SLA_CONFIG) \
        .createOrReplaceTempView("webhook_sla_configuration")

    spark.read.format("parquet").load(parquet_tables_masked.GSheets(input_env).PARTNERSHIP_WEBHOOK_STATUS_CONFIG) \
        .createOrReplaceTempView("webhook_status_configuration")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="holm_orders",
                jinja_template="""

                select
                     order_milestones.order_id
                    , order_milestones.tracking_id
                    , order_milestones.granular_status
                    , order_milestones.creation_datetime
                    , events.handed_over_to_last_mile_datetime
                    , date(events.handed_over_to_last_mile_datetime) as handed_over_to_last_mile_date
                    , order_milestones.system_id
                from order_milestones
                left join xb_events_enriched events
                    on order_milestones.tracking_id = events.tracking_id
                    and order_milestones.system_id = events.system_id 
                join prefix on 
                     order_milestones.tracking_id like prefix.prefixes || '%'
                     and order_milestones.system_id = prefix.system_id
                where  
                    -- Filter for HOLM Orders only
                    date(events.handed_over_to_last_mile_datetime) = date_sub('{{measurement_datetime}}',1)
                    and prefix.shipper = 'Tiktok XB'
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="holm_order_webhook",
                jinja_template="""

                 select
                    webhook_log.*
                    , granular_status
                    , cast(webhook_status_configuration.status_code as int) as status_code
                    , webhook_status_configuration.status
                    , handed_over_to_last_mile_datetime
                    , handed_over_to_last_mile_date
                from webhook_log
                join holm_orders on 
                    webhook_log.tracking_id = holm_orders.tracking_id
                    and webhook_log.system_id = holm_orders.system_id 
                left join webhook_status_configuration on
                    webhook_log.webhook_event_status = webhook_status_configuration.webhook_event_status
                    and coalesce(webhook_log.webhook_event_state,'empty') = 
                        coalesce(webhook_status_configuration.webhook_event_state,'empty')
                    and cast(coalesce(webhook_log.rts_flag,0) as int) = 
                        cast(coalesce(webhook_status_configuration.rts_flag,0) as int)
                    and (
                        case
                            -- Accomodate for Pricing Updated logic difference across platforms
                            when webhook_status_configuration.url is not null then
                                if(webhook_log.webhook_event_status = 'Pricing Updated', webhook_log.url,'empty') = 
                                    coalesce(webhook_status_configuration.url,'empty')
                            else 
                                if(webhook_log.webhook_event_status = 'Pricing Updated'
                                    , array_contains(split(replace(webhook_status_configuration.shipper,', ',','),',')
                                    , webhook_log.shipper)
                                    , true)
                        end
                        ) 
                """
            ),
            base.TransformView(
                view_name="source_of_truth",
                jinja_template="""

                {%- for status, value in completeness_config.items() %}
                (
                    select
                        tracking_id
                        , handed_over_to_last_mile_date
                        , handed_over_to_last_mile_datetime
                        , '{{ status }}' as status
                        , cast({{ value[1] }} as integer) as status_code
                        , '{{ value[0] }}' as shipper
                        , '{{ value[2] }}' as latency_sla
                        , cast({{ value[3] }} as integer) as latency_sla_in_mins
                        , system_id
                    from holm_orders
                )
                {% if not loop.last %} union all {% endif %}
                {%- endfor %}

                """,
                jinja_arguments={
                    "completeness_config": spark.sql("""
                        select 
                            shipper
                            , sla.status_code
                            , status.status 
                            , sla.latency_sla
                            , sla.latency_sla_in_mins
                        from webhook_sla_configuration sla
                        join (
                                select 
                                    distinct status_code
                                    , status
                                from webhook_status_configuration

                            ) status
                             on sla.status_code = status.status_code
                        where 
                            secondary_completeness_flag = 1
                    """).rdd.map(lambda row: (row[2], (row[0], row[1], row[3], row[4]))).reduceByKey(
                        lambda x, y: {**x, **y}).collectAsMap(),
                },
            ),

            base.TransformView(
                view_name="successful_webhook",
                jinja_template="""

                select
                    tracking_id
                    , status_code
                    , min_by(event_time, webhook_sent_time) as first_event_time
                    , min_by(webhook_sent_time, webhook_sent_time) as first_webhook_sent_time
                from holm_order_webhook
                where response_code = 200
                group by 1,2

                """,
            ),

            base.TransformView(
                view_name="first_webhook_attempt",
                jinja_template="""

                select
                    tracking_id
                    , status_code
                    , min_by(response_code, webhook_sent_time) as response_code
                from holm_order_webhook
                group by 1,2

                """,
            ),

            base.TransformView(
                view_name="base",
                jinja_template="""

                select 
                    source_of_truth.tracking_id
                    , source_of_truth.shipper
                    , source_of_truth.handed_over_to_last_mile_datetime
                    , source_of_truth.handed_over_to_last_mile_date
                    , source_of_truth.status
                    , source_of_truth.status_code
                    , source_of_truth.system_id
                    , if(first_webhook_attempt.tracking_id is not null,1,0) as webhook_attempted_flag
                    , first_webhook_attempt.response_code as first_response_code
                    , successful_webhook.first_event_time
                    , successful_webhook.first_webhook_sent_time
                    , if(
                        successful_webhook.first_event_time is not null
                        and successful_webhook.first_webhook_sent_time is not null
                        , (
                            to_unix_timestamp(successful_webhook.first_webhook_sent_time)
                            - to_unix_timestamp(successful_webhook.first_event_time)
                        )/60
                        , null
                    ) as event_to_webhook_minutes_diff
                    , case 
                        when successful_webhook.first_event_time is not null
                            and successful_webhook.first_webhook_sent_time is not null then 
                            if((to_unix_timestamp(successful_webhook.first_webhook_sent_time) - 
                                to_unix_timestamp(successful_webhook.first_event_time))/60 <=
                                cast(source_of_truth.latency_sla_in_mins as int),1,0)
                        else null
                     end as latency_sla_met
                    , source_of_truth.latency_sla
                    , source_of_truth.handed_over_to_last_mile_date as report_date
                from source_of_truth  
                left join successful_webhook on
                    source_of_truth.tracking_id = successful_webhook.tracking_id
                    and source_of_truth.status_code = successful_webhook.status_code
                left join first_webhook_attempt on
                    source_of_truth.tracking_id = first_webhook_attempt.tracking_id
                    and source_of_truth.status_code = first_webhook_attempt.status_code

                """,
            ),
        )
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "report_date"),
        update_latest_with_historical=True,
        enable_compaction=False,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(
        spark,
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )
    run(spark, task_config)
    spark.stop()