import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SLA_EXTENSIONS_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SLA_EXTENSIONS_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED
        ),
    ),
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="sla_config"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED, view_name="orders_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.GDrive(input_env).SLA_EXTENSIONS, view_name="sla_extensions_gdrive"),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    orders_enriched.system_id as country
                    , orders_enriched.tracking_id
                    , gdrive.delivery_leg
                    , orders_enriched.system_id
                    , orders_enriched.created_month
                    , sum(gdrive.extension_days) as extension_days
                    , max_by(gdrive.extension_reason, gdrive.creation_date) as extension_reason
                from sla_extensions_gdrive as gdrive
                inner join orders_enriched on
                    gdrive.tracking_id = orders_enriched.tracking_id
                    and lower(gdrive.country) = orders_enriched.system_id
                group by
                    1, 2, 3, 4, 5
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SLA_EXTENSIONS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
