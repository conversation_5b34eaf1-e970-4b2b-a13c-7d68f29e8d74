import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.DPDAG.Task.DP_OPERATING_HOURS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.DPDAG.Task.DP_OPERATING_HOURS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(path=delta_tables.DPProdGL(input_env, is_masked).DP_OPERATING_HOURS, view_name="dp_operating_hours"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    dp_operating_hours.id
                    , dp_operating_hours.dp_id
                    , day_of_week_map.name as day_of_week
                    , date_format(dp_operating_hours.start_time, 'HH:mm:ss') as open_time
                    , date_format(dp_operating_hours.end_time, 'HH:mm:ss') as close_time
                    , cast(deleted_at is null as int) as is_active
                    , dp_operating_hours.system_id
                    , date_format(dp_operating_hours.created_at, 'yyyy-MM') as created_month
                from dp_operating_hours
                left join values {{ day_of_week_map | join(',') }} as day_of_week_map(id, name) on
                    dp_operating_hours.day_of_week = day_of_week_map.id
                """,
                jinja_arguments={
                    "day_of_week_map": enumerate(
                        ("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"), start=1
                    )
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).DP_OPERATING_HOURS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
