import json
import sys

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.functions import ceil
from data_warehouse.utils import logger
from pyspark.sql.functions import year, month, dayofmonth

# EXPECT_COLUMN_VALUES_TO_BE_UNIQUE = "expect_column_values_to_be_unique"
# EXPECT_COLUMN_DISTINCT_VALUES_TO_BE_IN_SET = "expect_column_distinct_values_to_be_in_set"
# EXPECT_COLUMN_VALUES_TO_MATCH_REGEX = "expect_column_values_to_match_regex"
GET_THE_STATS_OF_THE_TABLE = "get_the_stats_of_the_table"
GET_THE_ORDERS_COUNT_PER_DAY = "get_the_orders_count_per_day"

# table_path = "obs://nv-data-prod-datawarehouse/{table}"
# stat_table_path = "obs://nv-data-{env}-datawarehouse/dwh_daily_stats"
# order_table_path = "obs://nv-data-{env}-datawarehouse/orders_daily_stats"

masked_table_path = "obs://nv-data-prod-data-warehouse/{table}"
masked_stat_table_path = "obs://nv-data-{env}-data-warehouse/dwh_daily_stats"
masked_order_table_path = "obs://nv-data-{env}-data-warehouse/orders_daily_stats"

# result_store_path = "obs://nv-data-{env}-datawarehouse/data_integrity"
logger = logger.get_logger(__file__)
MASKED_IDENTIFIER = "_masked"


# def expect_column_values_to_be_unique(env, spark, table, primary_keys):
#     primary_keys_str = ", ".join(primary_keys)
#     _result_store_path = result_store_path.format(env=env) + f"/{table}/{EXPECT_COLUMN_VALUES_TO_BE_UNIQUE}"
#
#     query = f"""
#         select {primary_keys_str}, count(1) as count
#         from batch
#         group by {primary_keys_str}
#         having count > 1
#     """
#
#     result = spark.sql(query)
#
#     if result.count() > 0:
#         result.write.mode("overwrite").parquet(_result_store_path)
#         return False
#     return True


def get_the_stats_of_the_table_masked(env, spark, table, primary_keys, where_condition):
    primary_keys_str = ", ".join(primary_keys)
    _stat_table_path = masked_stat_table_path.format(env=env)
    query2 = f"""
            with stats as 
            (select 
            current_date as stat_collection_date, 
            '{table}' as table_name, 
            count(*) as no_of_records
            from batch
            ),
            uniq as 
            (select {primary_keys_str} from batch {where_condition} group by {primary_keys_str})
            select 
            stats.stat_collection_date,
            stats.table_name, 
            stats.no_of_records, 
            (select count(*) from uniq) as no_of_uniq_records from stats
            """
    stats = spark.sql(query2)
    df_one = stats.withColumn("diff", stats["no_of_records"] - stats["no_of_uniq_records"])
    df = (
        df_one.withColumn("year", year(df_one.stat_collection_date))
        .withColumn("month", month(df_one.stat_collection_date))
        .withColumn("day", dayofmonth(df_one.stat_collection_date))
    )
    df.write.mode("overwrite").partitionBy("year", "month", "day").parquet(_stat_table_path)
    # value = df.select('diff').collect()[0][0]

    if df.count() == 0:
        return False
    return True


def get_the_orders_count_per_day_masked(env, spark, table, primary_keys):
    primary_keys_str = ", ".join(primary_keys)
    _order_table_path = masked_order_table_path.format(env=env)

    ord_df = spark.read.parquet(_order_table_path)
    ord_df.createOrReplaceTempView("ord_df")

    df = spark.sql(f"""select {primary_keys_str} from batch""")
    df1 = df.withColumn("today_date", to_date("creation_datetime"))
    df1.createOrReplaceTempView("batch_df")

    query2 = f"""
                select 
                current_date as stat_collection_date,
                '{table}' as table_name,
                country,
                count(*) as order_count,
                'order_id' as groupBy_column
                from batch_df 
                where today_date= date_sub(current_date, 1) 
                group by country
                """
    orders = spark.sql(query2)
    orders_per_day = (
        orders.withColumn("year", year(orders.stat_collection_date))
        .withColumn("month", month(orders.stat_collection_date))
        .withColumn("day", dayofmonth(orders.stat_collection_date))
    )

    prev_df = spark.sql(
        "select distinct country, order_count as prev_order_count from ord_df "
        "where stat_collection_date =date_sub(current_date, 2)"
    )

    if prev_df.count() > 0:
        prev_sevendays_df = spark.sql(
            "select country, avg(order_count) as prev_order_count from ord_df "
            "where stat_collection_date >= date_sub(current_date, 8) group by country"
        )
        if prev_sevendays_df.count() > 0:
            days_df = prev_sevendays_df.select("country", ceil("prev_order_count").alias("7days_order_count"))
            c_df = orders_per_day.join(prev_df, prev_df.country == orders_per_day.country, "inner").select(
                orders_per_day["*"], prev_df["prev_order_count"]
            )
            curr_df = c_df.join(days_df, days_df.country == c_df.country, "inner").select(
                c_df["*"], days_df["7days_order_count"]
            )

            final_df = curr_df.withColumn(
                "daily_diff", curr_df["prev_order_count"] - curr_df["order_count"]
            ).withColumn("last_seven_days_diff", curr_df["7days_order_count"] - curr_df["order_count"])

            final_df.select(
                "stat_collection_date",
                "table_name",
                "country",
                "order_count",
                "daily_diff",
                "last_seven_days_diff",
                "groupBy_column",
                "year",
                "month",
                "day",
            ).write.mode("overwrite").format("parquet").partitionBy("year", "month", "day").save(_order_table_path)

            # value = final_df.select('daily_diff').collect()[0][0]

            if final_df == 0:
                return False
            return True

        else:
            logger.info("No data for last 7 days")
            c_df = orders_per_day.join(prev_df, prev_df.country == orders_per_day.country, "inner").select(
                orders_per_day["*"], prev_df["prev_order_count"]
            )
            final_df = c_df.withColumn("daily_diff", c_df["prev_order_count"] - c_df["order_count"])
            final_df.write.mode("overwrite").format("parquet").partitionBy("year", "month", "day").save(
                _order_table_path
            )
    else:
        logger.info("No data for last 2 days")
        orders_per_day.write.mode("overwrite").format("parquet").partitionBy("year", "month", "day").save(
            _order_table_path
        )


# def expect_column_distinct_values_to_be_in_set(env, spark, table, column, value_set):
#     _result_store_path = result_store_path.format(env=env) + f"/{table}/{EXPECT_COLUMN_DISTINCT_VALUES_TO_BE_IN_SET}"
#
#     jinja_arguments = {"column_name": column, "value_set": value_set}
#
#     template = """
#         with required_values as (
#             {% for value in value_set -%}
#             select
#                 '{{ value }}' as value_field
#             {% if not loop.last %}union all{% endif %}
#             {% endfor %}
#         )
#
#         select distinct value_field from required_values
#         except all
#         select distinct {{ column_name }} from batch
#     """
#
#     query = jinja2.Template(template).render(jinja_arguments)
#     result = spark.sql(query)
#
#     if result.count() > 0:
#         result.write.mode("overwrite").parquet(_result_store_path)
#         return False
#     return True


# def expect_column_values_to_match_regex(env, spark, table, column, regex):
#     _result_store_path = result_store_path.format(env=env) + f"/{table}/{EXPECT_COLUMN_VALUES_TO_MATCH_REGEX}"
#
#     query = f"""
#         select {column}
#         from batch
#         where {column} not rlike '{regex}'
#     """
#     result = spark.sql(query)
#
#     if result.count() > 0:
#         result.write.mode("overwrite").parquet(_result_store_path)
#         return False
#     return True


# def validate_data(env, spark, table: str, execution_date: str) -> None:
#     with open(f"great_expectations/expectations/data_warehouse/{table}.json", "r") as file:
#         expectations = json.load(file)
#
#     _table_path = table_path.format(table=table) + (
#         "/measurement_datetime=latest" if (expectations.get("has_measurement_datetime") != "false") else ""
#     )
#
#     batch_df = spark.read.parquet(_table_path)
#
#     if expectations.get("is_full_scan") != "true":
#         batch_df = batch_df.filter(f"created_month >= date('{execution_date}') - interval 6 months")
#     batch_df.createOrReplaceTempView("batch")
#
#     for expectation in expectations["expectations"]:
#         expectation_type = expectation["expectation_type"]
#
#         if expectation_type == EXPECT_COLUMN_VALUES_TO_BE_UNIQUE:
#             is_unique = expect_column_values_to_be_unique(env, spark, table, expectation["kwargs"]["column_list"])
#
#             if not is_unique:
#                 raise Exception(f"{EXPECT_COLUMN_VALUES_TO_BE_UNIQUE} is not successful.")
#
#         elif expectation_type == GET_THE_STATS_OF_THE_TABLE:
#             get_stat = get_the_stats_of_the_table(env, spark, table, expectation["kwargs"]["column_list"], expectation["kwargs"]["where_condition"])
#
#             if not get_stat:
#                 raise Exception(f"{GET_THE_STATS_OF_THE_TABLE} is not successful and does not have data for the day.")
#
#         elif expectation_type == GET_THE_ORDERS_COUNT_PER_DAY:
#             get_orders = get_the_orders_count_per_day(env, spark, table, expectation["kwargs"]["column_list"])
#
#             if not get_orders:
#                 raise Exception(f"{GET_THE_ORDERS_COUNT_PER_DAY} is not successful and does not met the threshold limit.")
#
#         elif expectation_type == EXPECT_COLUMN_DISTINCT_VALUES_TO_BE_IN_SET:
#             is_value_in_set = expect_column_distinct_values_to_be_in_set(
#                 env, spark, table, expectation["kwargs"]["column"], expectation["kwargs"]["value_set"]
#             )
#
#             if not is_value_in_set:
#                 raise Exception(f"{EXPECT_COLUMN_DISTINCT_VALUES_TO_BE_IN_SET} is not successful.")
#         elif expectation_type == EXPECT_COLUMN_VALUES_TO_MATCH_REGEX:
#             is_regex_match = expect_column_values_to_match_regex(
#                 env, spark, table, expectation["kwargs"]["column"], expectation["kwargs"]["regex"]
#             )
#
#             if not is_regex_match:
#                 raise Exception(f"{EXPECT_COLUMN_VALUES_TO_MATCH_REGEX} is not successful.")
#
#     logger.info("All expectations passed")


def validate_data_masked(env, spark, table: str, execution_date: str) -> None:
    if table.endswith(MASKED_IDENTIFIER):
        table_name = "_".join(table.split("_")[:-1])
    else:
        table_name = table
    with open(f"great_expectations/expectations/data_warehouse/{table_name}.json", "r") as file:
        expectations = json.load(file)
    _table_path = masked_table_path.format(table=table_name) + (
        "/measurement_datetime=latest" if (expectations.get("has_measurement_datetime") != "false") else ""
    )

    batch_df = spark.read.parquet(_table_path)

    if expectations.get("is_full_scan") != "true":
        batch_df = batch_df.filter(f"created_month >= date('{execution_date}') - interval 6 months")
    batch_df.createOrReplaceTempView("batch")

    for expectation in expectations["expectations"]:
        expectation_type = expectation["expectation_type"]

        if expectation_type == GET_THE_STATS_OF_THE_TABLE:
            get_stat = get_the_stats_of_the_table_masked(
                env, spark, table, expectation["kwargs"]["column_list"], expectation["kwargs"]["where_condition"]
            )

            if not get_stat:
                raise Exception(f"{GET_THE_STATS_OF_THE_TABLE} is not successful and does not have data for the day.")

        elif expectation_type == GET_THE_ORDERS_COUNT_PER_DAY:
            get_orders = get_the_orders_count_per_day_masked(env, spark, table, expectation["kwargs"]["column_list"])

            if not get_orders:
                raise Exception(
                    f"{GET_THE_ORDERS_COUNT_PER_DAY} is not successful and does not met the threshold limit."
                )

    logger.info("All expectations passed")


if __name__ == "__main__":
    env, table, task_instance_key_str, execution_date = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")

    try:
        # validate_data(env, spark, table, execution_date)
        validate_data_masked(env, spark, table, execution_date)
    finally:
        spark.stop()
