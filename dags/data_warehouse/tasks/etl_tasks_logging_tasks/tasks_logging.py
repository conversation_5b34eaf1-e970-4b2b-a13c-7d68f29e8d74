import sys

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col
from pyspark.sql.types import StringType, TimestampType

from data_warehouse.utils import obs, logger
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI

logger = logger.get_logger(__file__)


def load_data(spark: SparkSession, env: str) -> DataFrame:
    """Load all the temp log parquet files recursively into spark dataframe"""

    load_path = MASKED_DATA_WAREHOUSE_BASE_URI.format(env) + "/etl_tasks_log_temp"

    logger.info(f"Loading temp log data from {load_path}")
    return spark.read.option("recursiveFileLookup", "true").format("parquet").load(load_path)


def process_data(df: DataFrame) -> DataFrame:
    """Process log df to add derived column and convert data types"""

    logger.info("Processing temp log data")
    logger.info(f"Total number of records in dataframe is {df.count()}")
    return (
        df.withColumn("task_start_datetime", col("task_start_datetime").cast(TimestampType()))
        .withColumn("load_finish_datetime", col("load_finish_datetime").cast(TimestampType()))
        .withColumn("task_finish_datetime", col("task_finish_datetime").cast(TimestampType()))
        .withColumn("created_at", col("created_at").cast(TimestampType()))
        .withColumn("task_duration", col("task_finish_datetime") - col("task_start_datetime"))
        # cast to string type because cannot store interval datatype to external storage
        .withColumn("task_duration", col("task_duration").cast(StringType()))
    )


def write_data(df: DataFrame, env: str) -> None:
    """Append processed dataframe into final table"""

    full_log_path = MASKED_DATA_WAREHOUSE_BASE_URI.format(env) + "/etl_tasks_log_full"
    partition_columns = ["task_name", "system_ids"]

    logger.info(f"Appending processed log data to {full_log_path}")
    (df.coalesce(1).write.mode("append").partitionBy(*partition_columns).save(full_log_path))
    logger.info(f"Append finished")


def delete_temp_logs(env):
    """Delete temporary logs in temp directory after they have been inserted into full logs table"""

    bucket_name = f"nv-data-{env}-data-warehouse"
    directory = "etl_tasks_log_temp"

    logger.info(f"Deleting the logs temp directory: {directory}")
    obs.delete_directory(bucket_name=bucket_name, directory=directory)


def run(spark, env):
    bucket_name = f"nv-data-{env}-data-warehouse"
    temp_directory = "etl_tasks_log_temp"

    try:
        obs.get_directory_blobs(bucket_name=bucket_name, directory=temp_directory)
    except obs.InvalidDirectoryException:
        logger.info(f"Directory {temp_directory} not found")
        return

    df = load_data(spark, env)
    df_processed = process_data(df)
    write_data(df_processed, env)

    delete_temp_logs(env)
    logger.info("Done")


if __name__ == "__main__":
    env = sys.argv[1]
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, env)
    spark.stop()
