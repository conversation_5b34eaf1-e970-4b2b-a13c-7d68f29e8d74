from common.stringcase import kebab_case
from data_warehouse.tasks.dwh_export_tasks import base
from datetime import timedelta
from metadata import data_warehouse, versioned_parquet_tables_masked

SQL = """
select
    status_code
    , status
    , tt_action_code_mapping
    , is_tokopedia
    , od_pair
    , shipper
    , latency_sla
    , total_parcels
    , coalesce(webhook_attempt,0) as webhook_attempt
    , coalesce(webhook_sent,0) as webhook_sent 
    , coalesce(webhook_met_latency_sla,0) as webhook_met_latency_sla 
    , webhook_success_rate
    , event_integrity_rate
    , event_transmit_on_time_rate
    , date_type
    , delivery_success_date
    , system_id
from input_table
order by delivery_success_date desc
"""

TASK_NAME = data_warehouse.DataWarehouseExportDAG.Task.PARTNERSHIP_WEBHOOK_EXPORT_MASKED

input_env = "prod"


def get_task_config(env):
    airflow_config = base.AirflowConfig(
        task_id=TASK_NAME,
        task_name=kebab_case(TASK_NAME),
        conf={
            "spark.executor.instances": "60",
            "spark.executor.cores": "6",
            "spark.executor.memory": "10300m",
            "spark.kubernetes.executor.request.cores": "3600m",
            "spark.driver.memory": "8500m",
            "spark.kubernetes.driver.request.cores": "2200m",
        },
        input_path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_STATS_ENRICHED
        + "/measurement_datetime=latest",
        sql=SQL,
        obs_check_prefix=versioned_parquet_tables_masked.DataWarehouse(
            input_env
        ).PARTNERSHIP_WEBHOOK_STATS_ENRICHED.split("/")[-1]
        + "/measurement_datetime=latest",
        alert_channel="google_chat",
        output_path=f"obs://nv-data-{input_env}-webhook-report/partnership_webhook_stats_enriched",
        depends_on_external=(
            base.DependsOnExternal(
                dag_id=data_warehouse.WebhookSnapshotDAG.DAG_ID,
                task_id=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_STATS_ENRICHED_MASKED,
            ),
        ),
    )
    return airflow_config
