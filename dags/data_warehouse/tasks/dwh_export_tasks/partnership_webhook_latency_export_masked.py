from common.stringcase import kebab_case
from data_warehouse.tasks.dwh_export_tasks import base
from datetime import timedelta
from metadata import data_warehouse, versioned_parquet_tables_masked

SQL = """
select
    webhook_sent_date
    , webhook_sent_date_sgt
    , webhook_sent_hour
    , webhook_sent_hour_sgt
    , status_code
    , status
    , latency_sla
    , webhook_sent 
    , webhook_met_latency_sla 
    , event_transmit_on_time_rate
    , system_id
    , shipper
    , created_date
from input_table
order by webhook_sent_date desc, webhook_sent_hour desc
"""

TASK_NAME = data_warehouse.WebhookExportDAG.Task.PARTNERSHIP_WEBHOOK_LATENCY_EXPORT_MASKED

input_env = "prod"


def get_task_config(env):
    airflow_config = base.AirflowConfig(
        task_id=TASK_NAME,
        task_name=kebab_case(TASK_NAME),
        conf={
            "spark.executor.instances": "4",
            "spark.executor.memory": "11200m",
            "spark.kubernetes.executor.request.cores": "1800m",
            "spark.driver.memory": "5600m",
            "spark.kubernetes.driver.request.cores": "900m",
        },
        input_path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_LATENCY_REPORT
        + "/measurement_datetime=latest",
        sql=SQL,
        obs_check_prefix=versioned_parquet_tables_masked.DataWarehouse(
            input_env
        ).PARTNERSHIP_WEBHOOK_LATENCY_REPORT.split("/")[-1]
        + "/measurement_datetime=latest",
        alert_channel="google_chat",
        output_path=f"obs://nv-data-{input_env}-webhook-report/partnership_webhook_latency_report",
        depends_on_external=(
            base.DependsOnExternal(
                dag_id=data_warehouse.WebhookFreqSnapshotDAG.DAG_ID,
                task_id=data_warehouse.WebhookFreqSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_LATENCY_REPORT_MASKED,
            ),
        ),
    )
    return airflow_config
