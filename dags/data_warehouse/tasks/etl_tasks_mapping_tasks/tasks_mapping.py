import datetime
import sys
from typing import Dict, List, Tuple

import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, explode_outer
from pyspark.sql.types import ArrayType, MapType, StringType, StructField, StructType

from data_warehouse.tasks.main.config import dag_configs
from data_warehouse.utils import logger
from metadata import parquet_tables_masked

logger = logger.get_logger(__file__)

SCHEMA = StructType(
    [
        Struct<PERSON>ield("task_name", StringType(), True),
        StructField("py_file", StringType(), True),
        StructField("dag_id", StringType(), True),
        StructField("system_ids", ArrayType(StringType()), True),
        StructField("depends_on", ArrayType(StringType()), True),
        <PERSON>ruct<PERSON>ield("depends_on_external", ArrayType(MapType(StringType(), StringType())), True),
        <PERSON>ruct<PERSON>ield("execution_timeout_hours", StringType(), True),
        <PERSON>ruct<PERSON><PERSON>("hive_metastore_config", ArrayType(MapType(StringType(), StringType())), True),
    ]
)


def transform_external_dependency(external_dependency: Tuple) -> List[Dict[str, str]]:
    """Transform external dependencies into list of dict"""

    res = []
    if not external_dependency:
        return

    for external_dependency_obj in external_dependency:
        external_dependency_dict = {
            "dag_id": external_dependency_obj.dag_id,
            "task_id": external_dependency_obj.task_id,
        }
        res.append(external_dependency_dict)

    return res


def transform_hive_metastore_configs(metastore_configs: Tuple) -> List[Dict[str, str]]:
    """Transform metastore configs into list of dict"""

    res = []
    if not metastore_configs:
        return

    for metastore_obj in metastore_configs:
        metastore_dict = {
            "hive_schema": metastore_obj.hive_schema,
            "partition_columns": list(metastore_obj.partition_columns),
        }
        res.append(metastore_dict)

    return res


def get_tasks_dags_mapping(config: Dict) -> pd.DataFrame:
    """Takes dag config as input and produce mapping table between tasks and dags"""

    data = []
    for dag_id, task_configs_dict in config.items():
        task_configs = task_configs_dict.get("task_configs")
        for airflow_config in task_configs:
            py_file = airflow_config.py_file
            task_name = airflow_config.task_name
            system_ids = list(airflow_config.system_ids)
            depends_on = list(airflow_config.depends_on)
            depends_on_external = transform_external_dependency(airflow_config.depends_on_external)
            execution_timeout_hours = airflow_config.execution_timeout / datetime.timedelta(hours=1)
            hive_metastore_config = transform_hive_metastore_configs(airflow_config.hive_metastore_config)

            row = (
                task_name,
                py_file,
                dag_id,
                system_ids,
                depends_on,
                depends_on_external,
                execution_timeout_hours,
                hive_metastore_config,
            )
            data.append(row)

    pdf = pd.DataFrame(
        data=data,
        columns=[
            "task_name",
            "py_file",
            "dag_id",
            "system_ids",
            "depends_on",
            "depends_on_external",
            "execution_timeout_hours",
            "hive_metastore_config",
        ],
    )

    return pdf.sort_values(by=["dag_id", "task_name"])


def run(spark, env):
    dest_path = parquet_tables_masked.DataWarehouse(env).ETL_TASKS_MAPPING
    logger.info(f"The destination path is {dest_path}")

    etl_tasks_mapping_pdf = get_tasks_dags_mapping(dag_configs)
    logger.info(f"Number of rows in etl_tasks_mapping_pdf is {len(etl_tasks_mapping_pdf)}")
    logger.info(f"Number of columns in etl_tasks_mapping_pdf is {len(etl_tasks_mapping_pdf.columns)}")

    etl_tasks_mapping_explode_df = (
        spark.createDataFrame(data=etl_tasks_mapping_pdf, schema=SCHEMA)
        .withColumn("system_ids_explode", explode_outer(col("system_ids")))
        .withColumn("depends_on_explode", explode_outer(col("depends_on")))
        .withColumn("depends_on_external_explode", explode_outer(col("depends_on_external")))
        .withColumn("depends_on_external_explode_task", col("depends_on_external_explode").getItem("task_id"))
        .withColumn("depends_on_external_explode_dag", col("depends_on_external_explode").getItem("dag_id"))
        .drop(col("depends_on_external_explode"))
    )

    logger.info(f"Number of rows in etl_tasks_mapping_explode_df is {etl_tasks_mapping_explode_df.count()}")
    logger.info("Writing spark df into destination parquet table")
    etl_tasks_mapping_explode_df.coalesce(1).write.mode("overwrite").parquet(dest_path)

    logger.info("Done")


if __name__ == "__main__":
    env = sys.argv[1]
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, env)
    spark.stop()
