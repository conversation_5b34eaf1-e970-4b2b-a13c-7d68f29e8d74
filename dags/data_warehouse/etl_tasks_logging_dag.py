from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from data_warehouse.tasks.main.base import HiveMetastoreTaskConfig
from data_warehouse.utils import airflow
from metadata import constants, data_warehouse

DAG_ID = data_warehouse.TasksLoggingDAG.DAG_ID
TASK = data_warehouse.TasksLoggingDAG.Task.ETL_TASKS_LOG_FULL_MASKED
TASK_ID = f"{TASK}_gl"
HIVE_CONF = HiveMetastoreTaskConfig(
    hive_schema="data_warehouse",
    partition_columns=("task_name", "system_ids"),
)

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
cz_tasks_path = Variable.get("SPARK_APPLICATION_PATH", default_var="obs://nv-data-prod-data-warehouse/clickzetta/spark")
USE_LH_SPARK = Variable.get("USE_LH_SPARK", default_var=os.environ.get("USE_LH_SPARK", "False")).lower() == "true"

# Construct the application path based on whether we're using LH-Spark or not
application = f"{cz_tasks_path}/airflow_tasks/etl_tasks_logging_tasks/tasks_logging.py" if USE_LH_SPARK else f"{tasks_path}/etl_tasks_logging_tasks/tasks_logging.py"
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2023, 3, 17),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": constants.Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    max_active_runs=1,
    schedule_interval="30 2,14 * * *",  # run twice a day at 10.30 am and 10.30 pm
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    catchup=False,
    tags=["data_warehouse"],
) as dag:
    logging_task = SparkSubmitOperator(
        task_id=TASK_ID,
        name=kebab_case(TASK_ID),
        application=application,
        application_args=[env],
        conn_id="spark_default",
        conf={
            **spark_conf[tables_to_size.get(TASK_ID, "small")],
            "spark.sql.adaptive.enabled": True,
            "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
            "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
        },
        execution_timeout=constants.Timeout.TWO_HOURS,
    )
    update_sing_hms_task = airflow.get_update_sing_hms_task(env, tasks_path, TASK, HIVE_CONF)

    logging_task >> update_sing_hms_task
