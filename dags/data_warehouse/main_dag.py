import json
import os
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from common.airflow import notifications as notif
from common.spark import spark_app_utils
from common.stringcase import kebab_case
from common.utils import helper
from config.config import Config
from common.utils.nv_obs import strip_uri
from custom_operators.spark_k8s import SparkK8s
from data_warehouse.tasks.main.config import dag_configs
from data_warehouse.utils import airflow
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI, SystemID, Timeout
from metadata.spark_conf import SPARK_CONF
from metadata.table_sizes import TABLE_SIZES
from airflow.models import Variable
from huawei.sensors.obs_objects_with_prefix_existence_sensor import HuaweiOBSObjectsWithPrefixExistenceSensor

DEFAULT_SCHEDULE = "0 18 * * *"  # Daily 2am SGT
# Path configurations for different execution environments
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
# Path for lh-spark environment - points to the location where packaged code is stored
# Get the application path from Variable, with a default fallback value
cz_tasks_path = Variable.get("SPARK_APPLICATION_PATH", default_var="obs://nv-data-prod-data-warehouse/clickzetta/spark")

# Environment configuration
env = Variable.get("env")
spark_conf = SPARK_CONF[env]["dwh"]
size_to_tables = TABLE_SIZES[env]["dwh"]
image_name = os.environ["SPARK_IMAGE_NAME"]
tag = os.environ["SPARK_IMAGE_TAG"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
table_sizes_airflow = {}

# Feature flag to switch between SparkK8s and SparkSubmitOperator
# Set USE_LH_SPARK to True to use SparkSubmitOperator, False to use SparkK8s
USE_LH_SPARK = Variable.get("USE_LH_SPARK", default_var=os.environ.get("USE_LH_SPARK", "False")).lower() == "true"

# Connection ID for SparkSubmitOperator
# This can be retrieved from Airflow variables, environment variables, or use default
SPARK_CONN_ID = Variable.get("spark_conn_id", default_var=os.environ.get("SPARK_CONN_ID", "spark_default"))

default_args = {
    "owner": "airflow",
    "start_date": datetime(2020, 2, 28),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

MASKED_IDENTIFIER = "_masked"

def _create_dag(dag_id, tasks_config, schedule_interval, max_active_runs, alert_channel):
    task_operators = {}
    ext_sensors = {}
    with DAG(
            catchup=False,
            dag_id=dag_id,
            default_args=default_args,
            max_active_runs=max_active_runs,
            schedule_interval=schedule_interval,
            sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
            tags=["data_warehouse"],
            user_defined_macros={'pendulum': pendulum},
    ) as dag:
        for config in tasks_config:
            task_name = config.task_name
            start = DummyOperator(task_id=f"{task_name}_start")
            end = DummyOperator(task_id=f"{task_name}_end")
            for system_id in config.system_ids:
                task_id = f"{task_name}_{system_id}"
                input_args = {
                    "env": env,
                    "last_measurement_datetime_str": "{{ execution_date.replace(tzinfo=pendulum.timezone('UTC')) }}",
                    "measurement_datetime_str": "{{ next_execution_date.replace(tzinfo=pendulum.timezone('UTC')) }}",
                    "enable_full_run_str": False,
                    "system_id": system_id,
                }
                if USE_LH_SPARK:
                    # LH-Spark implementation using SparkSubmitOperator
                    base_spark_conf = {  
                        "spark.dynamicAllocation.enabled" : "true",
                        "spark.dynamicAllocation.maxExecutors": "2",
                        "spark.dynamicAllocation.minExecutors": "0",
                        "spark.driver.memory": "2g",
                        "spark.driver.cores": "2",
                        "spark.executor.memory": "2g",
                        "spark.executor.cores": "2",
                    }
                    
                    # Check if the specific Clickzetta acceleration settings are present
                    has_cz_settings = False
                    if hasattr(config, 'spark_conf_overrides') and config.spark_conf_overrides:
                        cz_overrides = config.spark_conf_overrides
                        # once spark.cz.accelerate.mode.enabled set, submit job into fallback vc to isolate
                        has_cz_settings = cz_overrides.get("spark.cz.accelerate.mode.enabled") == "false"
                        cz_overrides["spark.cz.vcluster"] = "spark_vc_fallback"
                        print(f"Clickzetta settings for {task_id}: {cz_overrides}")

                    # Get size-based spark conf from metadata if Clickzetta settings are present
                    if has_cz_settings:
                        task_size = tables_to_size.get(task_id, "small")
                        if task_size in spark_conf:
                            base_spark_conf = spark_conf[task_size]

                    # Merge overrides from task config
                    merged_spark_conf = {**base_spark_conf, **(config.spark_conf_overrides or {})}
                    print(f"Merged spark conf for {task_id}: {merged_spark_conf} and has_cz_settings: {has_cz_settings}")
                    
                    # For the main DAG, construct the application path for the 'main' directory
                    py_file_name = os.path.basename(config.py_file)
                    
                    # Construct the application path based on whether we're using LH-Spark or not
                    application_path = f"{cz_tasks_path}/airflow_tasks/main/{py_file_name}" if USE_LH_SPARK else f"{tasks_path}/main/{config.py_file}"
                    
                    task = SparkSubmitOperator(
                        task_id=task_id,
                        name=kebab_case(task_id),
                        application=application_path,
                        application_args=[json.dumps(input_args, separators=(",", ":"))],
                        conn_id=SPARK_CONN_ID,
                        properties_file="/opt/lh-spark/conf/spark-defaults.conf",
                        conf=merged_spark_conf,
                        execution_timeout=config.execution_timeout,
                        params={"enable_full_run": False, "alert_channel": alert_channel},
                    )
                    start >> task >> end
                else:
                    # Original SparkK8s implementation
                    app_config = helper.build_config(task_name, system_id, env, f"{Config.IMAGE_NAME}:{Config.TAG}", "dwh",
                                                    group="dwh")
                    app_config["main_application_file"] = f"{tasks_path}/main/{config.py_file}"
                    app_config["table_sizes_airflow"] = table_sizes_airflow
                    app_config["name"] = f"{task_name}_{system_id}_job"
                    base_spark_conf = spark_conf[tables_to_size.get(task_id, "small")]
                    
                    # Start with base spark conf for the task size
                    merged_spark_conf = {**base_spark_conf,
                                         "spark.sql.adaptive.enabled": 'true',
                                         "spark.kubernetes.driver.label.ninja-spark-dwh-etl": task_id,
                                         "spark.kubernetes.executor.label.ninja-spark-dwh-etl": task_id}
                    
                    # Check if the specific Clickzetta acceleration settings are present
                    has_cz_settings = False
                    if hasattr(config, 'spark_conf_overrides') and config.spark_conf_overrides:
                        cz_overrides = config.spark_conf_overrides
                        has_cz_settings = (
                            cz_overrides.get("spark.cz.accelerate.mode.enabled") == "false" and
                            cz_overrides.get("spark.cz.accelerate.mode.convert.fallback.enabled") == "true" and
                            cz_overrides.get("spark.cz.accelerate.mode.execute.fallback.enabled") == "true"
                        )
                        
                    # Apply any task-specific Spark conf overrides
                    if hasattr(config, 'spark_conf_overrides') and config.spark_conf_overrides:
                        # If we have Clickzetta settings and need to use metadata-based config
                        if has_cz_settings:
                            merged_spark_conf.update(config.spark_conf_overrides)
                        else:
                            # Otherwise just apply the overrides without changing base configuration
                            merged_spark_conf.update(config.spark_conf_overrides)
                    app_config["spark_config"] = merged_spark_conf
                    application_file_delta = spark_app_utils.get_application_file_str(app_config, app_type='dwh')
                    task = SparkK8s(
                        task_id=f"{task_id}",
                        namespace=os.environ["AIRFLOW__KUBERNETES_EXECUTOR__NAMESPACE"],
                        application_file=application_file_delta,
                        kubernetes_conn_id="kubernetes_default",
                        in_cluster=True,
                        reattach_on_restart=True,
                        get_logs=True,
                        log_events_on_failure=True,
                        delete_on_termination=True,
                        do_xcom_push=False,
                        arguments=[json.dumps(input_args, separators=(",", ":"))],
                        env_vars={"EXECUTION_DATE": "{{ execution_date }}"},
                        pool="k8s_spark_pool",
                        execution_timeout=config.execution_timeout,
                    )
                    start >> task >> end
            task_operators[task_name] = {"start": start, "end": end}

            for ext_dep in config.depends_on_external:
                ext_dag_task_id = f"{ext_dep.dag_id}_{ext_dep.task_id}"
                if ext_dag_task_id not in ext_sensors:
                    ext_sensors[ext_dag_task_id] = airflow.get_dwh_external_task_sensor(
                        ext_dep.dag_id, ext_dep.task_id, dag_id, ext_dep.execution_delta
                    )
                ext_sensors[ext_dag_task_id] >> start

            hms_config = config.hive_metastore_config
            if hms_config:
                update_sing_hms_tasks = {}
                for conf in hms_config:
                    # Use appropriate tasks_path based on the execution environment
                    hms_tasks_path = cz_tasks_path if USE_LH_SPARK else tasks_path
                    update_sing_hms_tasks[conf.hive_schema] = airflow.get_update_sing_hms_task(env, hms_tasks_path,
                                                                                               task_name, conf, SPARK_CONN_ID)
                    end >> update_sing_hms_tasks[conf.hive_schema]

            # Check if task is successfully written in OBS
            if config.post_execution_check:
                obs_sensors = {}
                for system_id in config.system_ids:
                    if task_name.endswith(MASKED_IDENTIFIER):
                        task = "_".join(task_name.split("_")[:-1])
                        prefix_path = (
                                task + "/measurement_datetime={{ next_execution_date.strftime('%Y-%m-%d %H-%M-%S') }}"
                        )
                        if system_id != SystemID.GL:
                            prefix_path += f"/system_id={system_id}"
                        
                        # Get OBS bucket URI for dwh
                        obs_bucket = strip_uri(MASKED_DATA_WAREHOUSE_BASE_URI.format(env))
                        obs_sensors[system_id] = HuaweiOBSObjectsWithPrefixExistenceSensor(
                            task_id=f"{task_name}_{system_id}_check_obs_existence",
                            bucket_name=obs_bucket,
                            prefix=prefix_path,
                            conn_id="hwc",
                            execution_timeout=Timeout.ONE_MINUTE,
                            retries=0,
                        )
                        if hms_config:
                            end >> obs_sensors[system_id] >> update_sing_hms_tasks[conf.hive_schema]
                        else:
                            end >> obs_sensors[system_id]

        for config in tasks_config:
            for dep in config.depends_on:
                task_operators[dep]["end"] >> task_operators[config.task_name]["start"]
    globals()[dag_id] = dag
    return dag


for dag_id, dag_config in dag_configs.items():
    _create_dag(
        dag_id,
        dag_config["task_configs"],
        dag_config.get("schedule", DEFAULT_SCHEDULE),
        dag_config.get("max_active_runs", 1),
        dag_config.get("alert_channel", "google_chat"),
    )