from datetime import datetime, timedelta
from importlib import import_module
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator

from common.airflow import notifications as notif
from data_warehouse.utils import airflow
from metadata import constants, data_warehouse

DAG_ID = data_warehouse.ShipperLifetimeValueDAG.DAG_ID
TASKS = (
    data_warehouse.ShipperLifetimeValueDAG.Task.SHIPPER_LIFETIME_VALUES_MASKED,
    data_warehouse.ShipperLifetimeValueDAG.Task.PARENT_SHIPPER_LIFETIME_VALUES_MASKED,
)
SYSTEM_IDS = (
    constants.SystemID.ID,
    constants.SystemID.MY,
    constants.SystemID.PH,
    constants.SystemID.SG,
    constants.SystemID.TH,
    constants.SystemID.VN,
)

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2020, 12, 31),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": constants.Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    # catchup=env == "prod",
    dag_id=DAG_ID,
    catchup=False,
    default_args=default_args,
    max_active_runs=5,
    schedule_interval="0 18 * * *",
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["data_warehouse"],
) as dag:
    for task in TASKS:
        MASKED_IDENTIFIER = "_masked"
        if task.endswith(MASKED_IDENTIFIER):
            task_name = "_".join(task.split("_")[:-1])
            wait_for_base_table_task = airflow.get_dwh_external_task_sensor(
                data_warehouse.ShippersDAG.DAG_ID, str(task_name + "_base_masked")
            )
            end_task = DummyOperator(task_id=f"{task}_end", trigger_rule="all_done")
            python_module = import_module(f"data_warehouse.tasks.shipper_lifetime_tasks.{task}")
            for system_id in SYSTEM_IDS:
                python_task = PythonOperator(
                    task_id=f"{task}_{system_id}",
                    python_callable=python_module.run,
                    op_kwargs={
                        "env": env,
                        "system_id": system_id,
                        "execution_date": "{{ next_ds }}",
                    },
                    execution_timeout=constants.Timeout.ONE_HOUR,
                )
                wait_for_base_table_task >> python_task >> end_task
        else:
            wait_for_base_table_task = airflow.get_dwh_external_task_sensor(
                data_warehouse.ShippersDAG.DAG_ID, str(task + "_base")
            )

            # The LTV tasks can fail because of model fitting issues, and such issues might take a while to resolve,
            # so their failure should not block downstream DWH tasks. trigger_rule here is set as 'all_done' so that
            # the end task will succeed even if one or more if the upstream tasks fail.

            end_task = DummyOperator(task_id=f"{task}_end", trigger_rule="all_done")
            python_module = import_module(f"data_warehouse.tasks.shipper_lifetime_tasks.{task}")
            for system_id in SYSTEM_IDS:
                python_task = PythonOperator(
                    task_id=f"{task}_{system_id}",
                    python_callable=python_module.run,
                    op_kwargs={
                        "env": env,
                        "system_id": system_id,
                        "execution_date": "{{ next_ds }}",
                    },
                    execution_timeout=constants.Timeout.ONE_HOUR,
                )
                wait_for_base_table_task >> python_task >> end_task
