from datetime import datetime
import numpy as np
import pendulum


class Timezone:
    CN = "Asia/Shanghai"
    ID = "Asia/Jakarta"
    MY = "Asia/Kuala_Lumpur"
    PH = "Asia/Manila"
    SG = "Asia/Singapore"
    TH = "Asia/Bangkok"
    VN = "Asia/Ho_Chi_Minh"
    MM = "Asia/Yangon"
    # add more tz's here


def get_last_midnight_in_utc(tz=Timezone.ID):
    """Returns a datetime object of the previous midnight of timezone `tz` in UTC"""
    midnight_utc = pendulum.today(tz).in_tz("UTC")
    return midnight_utc


def to_datetime(ts):
    """Converts ts in ISO-8601 format, e.g., 2019-01-01T00:00:00+00:00, to pendulum datetime object"""
    return pendulum.parse(ts)


def date_from_ts(ts, date_fmt="%Y-%m-%d"):
    """Extracts date from ts in YYYY-MM-DD format by default, and custom format if date_fmt is specified."""
    return to_datetime(ts).strftime(date_fmt)


def datetime_from_str(str, date_fmt="%Y-%m-%d"):
    """Converts a string into a datetime object with the specified format"""
    return datetime.strptime(str, date_fmt)


def time_from_ts(ts):
    """Extracts time in HH-mm-SS format from ts"""
    return to_datetime(ts).strftime("%H-%M-%S")


def to_measurement_datetime_str(measurement_datetime):
    """Converts a datetime object into string with standard format for DWH measurement_datetime."""
    return measurement_datetime.strftime("%Y-%m-%d %H-%M-%S")


def num_bus_days(start_dt_str, end_dt_str, weekmask="1111110", holidays=None):
    """
    Returns the number of business days (including fractional days) between start_dt and end_dt,

    :param start_dt_str: Local start datetime
    :param end_dt_str: Local end datetime
    :param weekmask: 7-element string of "1" or "0" indicating if Mon through Sun is a business day
    :param holidays: list of dates to consider as non-business days
    :return: No. of business days
    """
    holidays = holidays or []
    start_dt = pendulum.parse(start_dt_str, tz="local")
    sdate = start_dt.date()
    sdate_p1 = sdate.add(days=1)
    end_dt = pendulum.parse(end_dt_str, tz="local")
    edate = end_dt.date()
    edate_p1 = edate.add(days=1)
    # get no. of business days for the whole period
    bus_days = np.busday_count(sdate, edate_p1, weekmask, holidays)
    # subtract start period if start datetime falls on a business day
    if np.busday_count(sdate, sdate_p1, weekmask, holidays) > 0:
        # start date expressed as datetime
        sdatetime = start_dt.start_of("day")
        start_days = start_dt.diff(sdatetime).total_days()
        bus_days -= start_days
    # subtract end period if end datetime falls on a business day
    if np.busday_count(edate, edate_p1, weekmask, holidays) > 0:
        # end date + 1 expressed as datetime
        edatetime = end_dt.add(days=1).start_of("day")
        end_days = edatetime.diff(end_dt).total_days()
        bus_days -= end_days
    return bus_days


def get_schema_day(schema_name):
    """Returns an integer representing day of the week based on the schema name."""
    return sum([ord(c) for c in schema_name]) % 7
