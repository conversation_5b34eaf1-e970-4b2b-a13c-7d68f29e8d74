def split_to_chunks(lst, chunk_size):
    if chunk_size < 0:
        raise Exception("Chunk size should be a positive integer")
    return (lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size))


def merge_dictionaries(dict1, dict2):
    for key, value in dict2.items():
        if key not in dict1:
            dict1[key] = value
        else:
            dict1[key] = list(set(dict1[key] + value))
    return dict1
