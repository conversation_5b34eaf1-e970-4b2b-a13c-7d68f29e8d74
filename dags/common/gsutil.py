import subprocess

from common import list as listutil


def get_aggregate_size(obj_names):
    """
    Return aggregate size of objects in bytes
    :param obj_names:       list of object names (gs://<bucket>/<object>)
    """
    chunk_size = 400  # A ballpark figure to deal with ARG_MAX, the max number of bytes for a shell command
    size = 0
    chunks = listutil.split_to_chunks(obj_names, chunk_size)
    for chunk in chunks:
        result = subprocess.run(["gsutil", "du", *chunk], check=True, stdout=subprocess.PIPE)
        output = result.stdout.strip()
        for line in output.split(b"\n"):
            size += int(line.split()[0])
    return size
