import re

from pyspark.sql.functions import col, concat, from_json, lit, lower, sha2, split, to_json

from metadata.pii import EMAIL_FIELD_MARK

EMAIL_SEPARATOR = "@"


def mask_column(df, field):
    """
    Manipulates column values of all rows of input Spark DataFrame.
    Performs PII masking at column level.
    Returns a Spark DataFrame with the column replaced.

    :param df:          Input Spark DataFrame
    :param field:       PII field name
    """
    if df.select(field).dtypes[0][1] != "string":
        df = df.withColumn(field, col(field).cast("string"))
    return df.withColumn(field, sha2(col(field), 0))


def mask_email_column(df, field):
    """
    Performs PII masking for email field.
    Returns a Spark DataFrame with the column replaced.

    :param df:          Input Spark DataFrame
    :param field:       PII field name with EMAIL MARK
    """
    field = field.lstrip(EMAIL_FIELD_MARK)
    return df.withColumn(
        field,
        concat(
            sha2(lower(split(df[field], EMAIL_SEPARATOR).getItem(0)), 0),
            lit(EMAIL_SEPARATOR),
            split(df[field], EMAIL_SEPARATOR).getItem(1),
        ),
    )


def mask_json_field_in_column(spark, df, df_column_name, keys):
    """
    Supports the following cases

     1. Inner JSON field accessed using pure dot notation is a string or numeric type.
        PII values are usually string.

        key = df_column_name.level_1.level_2

        df_column_name contains this input JSON field as string.
        Input:
        {
            'level_1': {
                'level_2': 'pii_value'
            }
        }

        df_column_name will be transformed into this output JSON field as string.
        Notice that the value is replaced by hash('pii_value').
        The schema is also preserved.
        Output:
        {
            'level_1': {
                'level_2': '023729bf394869135c787a47d5cb1e35c694388284baf57b92fe7371f7353b93'
            }
        }

     2. Inner JSON field accessed using dot notation is still a complex type, e.g., struct, array, or map.
        key = df_column_name.level_1.level_2

        df_column name contains this input JSON field as string
        Input:
        {
            'level_1': {
                'level_2': {
                    'level_3a': 'pii_value',
                    'level_3b': 'pii_value',
                    'level_3c': 'non_pii_value'
                }
            }
        }

        df_column_name will be transformed into this output JSON field as string
        Notice that the value is replaced by hash(string representation of level_2)
        and that its child keys are no longer available.
        Output:
        {
            'level_1': {
                'level_2': '927312817df64fa260d63d33c30a9e70ed2e449f7fce4a5f0c56bd7b7626aee3'
            }
        }

    :param spark:           Spark session
    :param df:              Input Spark DataFrame
    :param df_column_name:  Column name of JSON field that is saved as a string
    :param keys:            PII fields within JSON column
    """
    json_field_schema = spark.read.json(df.rdd.map(lambda data: data[df_column_name])).schema
    spark_conf = dict(spark.sparkContext.getConf().getAll())
    # Evenly distribute data among executors.
    # JSON processing is much slower on skewed data.
    num_executors = int(spark_conf["spark.executor.instances"]) * int(spark_conf["spark.executor.cores"])
    df_json = df.withColumn(df_column_name, from_json(df_column_name, json_field_schema)).repartition(num_executors)
    df_json.cache()
    print(f"Processing {df_column_name} and its fields {keys}")

    def is_child_key(df_input, parent_col, child_key):
        select_string = parent_col
        fields = child_key.split(".")
        for f in fields:
            if not df_input.select(select_string).dtypes[0][1].startswith("struct"):
                return False
            if f in df_input.select(f"{select_string}.*").columns:
                select_string += f".{f}"
            else:
                return False
        return True

    for key in keys:
        if not is_child_key(df_json, df_column_name, key):
            continue
        field = f"{df_column_name}.{key}"
        field_type = df_json.select(field).schema.fields[0].jsonValue()["type"]
        # Complex fields return a dict with information about their type and fields.
        # Non-complex fields return the type itself, e.g., 'string' or 'long'
        is_complex_type = type(field_type) == dict
        if is_complex_type:
            df_json = df_json.withColumn(df_column_name, df_json[df_column_name].withField(key, to_json(field)))
        elif field_type != "string":
            df_json = df_json.withColumn(
                df_column_name, df_json[df_column_name].withField(key, col(field).cast("string"))
            )
        df_json = df_json.withColumn(df_column_name, df_json[df_column_name].withField(key, sha2(field, 0)))

    print(f"Converting `{df_column_name}` JSON back to string to preserve its column type")
    df_masked = df_json.withColumn(df_column_name, to_json(df_column_name))

    return df_masked


def prepare_fields(df_columns, pii_fields):
    """
    Separates PII fields into a list of columns that can be masked as is
    and a map of JSON fields within a column that need to be handled

    Input:
    df_columns = ['column_1', 'column_2', 'column_3']
    pii_fields = ['column_1', 'column_2', 'column_3.level_1a', 'column_3.level_1b']

    Output:
    columns = ['column_1', 'column_2']
    json_fields = {'column_3': ['level_1a', 'level_1b']}

    :param df_columns:      List of column names
    :param pii_fields:      List of PII fields
    """
    # JSON fields within df_column must be denoted by df_column_name.level_1.level_2
    pattern = r"^\w+\.{1}[a-zA-Z0-9_\.]+[^\.]$"
    columns = []
    json_fields = {}

    for field in pii_fields:
        if field in df_columns or field.startswith(EMAIL_FIELD_MARK):
            columns.append(field)
        elif re.match(pattern, field):
            df_column_name, keys = field.split(".", maxsplit=1)
            if df_column_name in df_columns:
                if df_column_name not in json_fields:
                    json_fields[df_column_name] = []
                json_fields[df_column_name].append(keys)
    return columns, json_fields


def mask(spark, df, pii_fields):
    """
    Returns a Spark DataFrame with PII fields masked using SHA-256

    :param spark:       Spark session
    :param df:          Input Spark DataFrame where PII fields are available in plain text
    :param pii_fields:  List of PII fields
    """

    df_masked = df
    df_columns = df.columns
    columns, json_fields = prepare_fields(df_columns, pii_fields)

    for column in columns:
        if column.startswith(EMAIL_FIELD_MARK):
            df_masked = mask_email_column(df_masked, column)
        else:
            df_masked = mask_column(df_masked, column)

    for df_column_name, keys in json_fields.items():
        df_masked = mask_json_field_in_column(spark, df_masked, df_column_name, keys)

    return df_masked
