_UNIT_SUFFIX_TO_SIZE = {"k": 1e3, "m": 1e6, "g": 1e9, "t": 1e12}


def bytes_spec_to_num_bytes(spec):
    """Takes in spark config defined in units of bytes and return the number of bytes"""
    spec = spec.rstrip("Bb")
    suffix = spec[-1]

    if suffix.lower() in _UNIT_SUFFIX_TO_SIZE:
        size_multiplier = _UNIT_SUFFIX_TO_SIZE[suffix]
        coefficient = float(spec[:-1])
    else:
        size_multiplier = 1
        coefficient = float(spec)

    if coefficient < 0:
        raise Exception("Bytes specified should be a positive integer")

    return int(coefficient * size_multiplier)
