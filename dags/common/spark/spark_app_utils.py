import json
import math
import os
import logging
from common.utils import helper

logger = logging.getLogger(__name__)


def generate_application_file_for_task(task_type, tasks_path, schema, table, config):
    task_configurations = {
        "post_process_cdc": {
            "main_application_file": f"{tasks_path}/post_process_cdc.py",
            "name": f"process_cdc_{schema}_{table}"
        },
        "update_delta": {
            "main_application_file": f"{tasks_path}/update_delta.py",
            "name": f"pii_delta_{schema}_{table}"
        },
        "compaction_delta": {
            "main_application_file": f"{tasks_path}/compact_delta.py",
            "name": f"pii_compact_delta_{schema}_{table}"
        }
    }

    if task_type not in task_configurations:
        raise ValueError(f"Unknown task type: {task_type}")

    config["main_application_file"] = task_configurations[task_type]["main_application_file"]
    config["name"] = task_configurations[task_type]["name"]

    return get_application_file_str(config)


def get_application_file_str(config, app_type='data-lake'):
    print(config)
    schema_table = config["schema_table"]
    # Get configurations from spark-defaults.conf
    spark_conf = get_spark_conf(config)
    application_dict = {
        "apiVersion": "sparkoperator.k8s.io/v1beta2",
        "kind": "SparkApplication",
        "metadata": {
            "name": config["name"].replace("_", "-"),
            "namespace": config["namespace"]
        },
        "spec": {
            "type": "Python",
            "timeToLiveSeconds": int(config["time_to_live"]),
            "mode": "cluster",
            "pythonVersion": "3",
            "sparkVersion": config["spark_version"],
            "mainApplicationFile": config["main_application_file"],
            "sparkConf": spark_conf,
            "driver": get_driver_conf(config, schema_table, driver_type=app_type),
            "executor": get_executor_conf(config, schema_table, executor_type=app_type),
        }
    }
    return json.dumps(application_dict)


def get_driver_conf(config, schema_table, driver_type='data-lake'):
    spark_conf = helper.get_spark_conf_by_table(config["env"], schema_table,
                                                table_sizes_airflow=config['table_sizes_airflow'],
                                                group=config["group"])

    return {
        "affinity": {
            "nodeAffinity": {
                "requiredDuringSchedulingIgnoredDuringExecution": {
                    "nodeSelectorTerms": [
                        {
                            "matchExpressions": [
                                {
                                    "key": "group",
                                    "operator": "In",
                                    "values": [driver_type]
                                },
                                {
                                    "key": "role",
                                    "operator": "In",
                                    "values": ["driver"]
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "tolerations": [
            {
                "key": "group",
                "operator": "Equal",
                "value": driver_type,
                "effect": "NoSchedule"
            },
            {
                "key": "role",
                "operator": "Equal",
                "value": "driver",
                "effect": "NoSchedule"
            }
        ],
        "initContainers": [
            {
                "name": "init-mtu",
                "image": "busybox",
                "command": ["sh", "-c"],
                "args": [
                    "ip link set eth0 mtu 1460;"
                ],
                "securityContext": {
                    "privileged": True
                }
            }
        ],
        "coreRequest": spark_conf.get("spark.kubernetes.executor.request.cores", "900m"),
        "labels": {
            "version": config["version"]
        },
    }


def get_executor_conf(config, schema_table, executor_type='data-lake'):
    spark_conf = helper.get_spark_conf_by_table(config["env"], schema_table,
                                                table_sizes_airflow=config["table_sizes_airflow"],
                                                group=config["group"])

    return {
        "affinity": {
            "nodeAffinity": {
                "requiredDuringSchedulingIgnoredDuringExecution": {
                    "nodeSelectorTerms": [
                        {
                            "matchExpressions": [
                                {
                                    "key": "group",
                                    "operator": "In",
                                    "values": [executor_type]
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "tolerations": [
            {
                "key": "group",
                "operator": "Equal",
                "value": executor_type,
                "effect": "NoSchedule"
            }
        ],
        "initContainers": [
            {
                "name": "init-mtu",
                "image": "busybox",
                "command": ["sh", "-c"],
                "args": [
                    "ip link set eth0 mtu 1460;"
                ],
                "securityContext": {
                    "privileged": True
                }
            }
        ],
        "coreRequest": spark_conf.get("spark.kubernetes.executor.request.cores", "1800m"),
        "labels": {
            "version": config["version"]
        },
    }


def get_spark_conf(config):
    spark_conf = {}
    spark_home = os.getenv('SPARK_HOME', '/home/<USER>/.local/lib/python3.8/site-packages/pyspark')
    spark_defaults_path = os.path.join(spark_home, 'conf', 'spark-defaults.conf')

    logger.info(f"Reading Spark configurations from: {spark_defaults_path}")

    # Configuration keys to exclude
    excluded_configs = {
        'spark.kubernetes.driver.podTemplateFile',
        'spark.kubernetes.executor.podTemplateFile'
    }

    try:
        with open(spark_defaults_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    try:
                        key, value = line.split(None, 1)
                        # Skip excluded configurations
                        if key not in excluded_configs:
                            spark_conf[key] = value
                            logger.debug(f"Loaded Spark config: {key}={value}")
                        else:
                            logger.debug(f"Skipping excluded config: {key}")
                    except ValueError:
                        logger.warning(f"Skipping invalid configuration line: {line}")
                        continue
    except FileNotFoundError:
        logger.error(f"Could not find spark-defaults.conf at {spark_defaults_path}")
        logger.error(f"SPARK_HOME is set to: {spark_home}")

    spark_conf['spark.databricks.delta.schema.autoMerge.enabled'] = 'true'

    if config.get('task_type') in ["delta", "process", "dwh", "compaction"]:
        task_specific_config = config.get('spark_config', {})
        logger.info(f"Adding task-specific configurations for type: {config.get('task_type')}")
        logger.debug(f"Task-specific configurations: {task_specific_config}")
        spark_conf.update(task_specific_config)

    logger.info(f"Final Spark configuration contains {len(spark_conf)} settings")
    return spark_conf