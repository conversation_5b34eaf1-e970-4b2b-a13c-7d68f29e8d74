from pyspark.sql.functions import col, lit
from common.date import Timezone


def add_literal_columns(df, cols):
    """Creates columns of literal value and adds them to the input DataFrame
    :param df:      Input Spark DF
    :param cols:    dict of column names and values, e.g., {"df_new_column": "default"}
    """
    for name, value in cols.items():
        df = df.withColumn(name, lit(value))
    return df


def cast_columns(df, cols):
    """
    Converts columns of the input DataFrame
    :param df:      Input Spark DF
    :param cols:    dict of column names and target data types, e.g., {"df_col_a": "string", "df_col_b": "integer"}
    """
    for name, value in cols.items():
        df = df.withColumn(name, col(name).cast(value))
    return df


def cast_df(df, dtypes, is_strong=False):
    """
    Returns a DataFrame based on df with its columns type-casted according to dtypes.

    :param df:          DataFrame whose columns will be cast
    :param dtypes:      Spark dtypes
    :param is_strong:   Whether or not to enforce a strong type-casting.
                        If `true`, df.columns not present in dtypes are omitted and columns found in dtypes but not in
                        df.columns are added with null literal values
                        If `false`, df.columns not present in dtypes are left as they are
                        and columns found in dtypes but not in df.columns are ignored
    """
    dtypes_dict = dict(dtypes)
    dtypes_columns = list(dtypes_dict.keys())
    missing = set(dtypes_columns) - set(df.columns)
    df_result = df
    if is_strong:
        missing_lit = dict(zip(missing, [None] * len(missing)))
        df_result = add_literal_columns(df, missing_lit)
        df_result = df_result.select(dtypes_columns)
    to_cast = {column: dtype for column, dtype in dtypes_dict.items() if column not in missing}
    return cast_columns(df_result, to_cast)


def add_typed_literal_columns(df, cols):
    """Creates columns of literal value with the specified type and adds them to the input DataFrame
    :param df:      Input Spark DataFrame
    :param cols:    dict of column names and (value, data type) tuples, e.g., {"df_new_column": (0, "boolean")}
    """
    col_values, col_types = {}, {}
    for name, col_info in cols.items():
        col_values[name], col_types[name] = col_info
    return cast_columns(add_literal_columns(df, col_values), col_types)


def get_local_timezone(country_column):
    local_timezone = f"""
        case
          when lower({country_column}) = 'cn' then '{Timezone.CN}'
          when lower({country_column}) = 'id' then '{Timezone.ID}'
          when lower({country_column}) = 'my' then '{Timezone.MY}'
          when lower({country_column}) = 'ph' then '{Timezone.PH}'
          when lower({country_column}) = 'sg' then '{Timezone.SG}'
          when lower({country_column}) = 'th' then '{Timezone.TH}'
          when lower({country_column}) = 'vn' then '{Timezone.VN}'
          when lower({country_column}) = 'mm' then '{Timezone.MM}'
          else NULL
        end
    """

    return local_timezone

