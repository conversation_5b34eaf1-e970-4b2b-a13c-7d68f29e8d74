from pyspark.sql.functions import desc, desc_nulls_first, row_number
from pyspark.sql.window import Window


def get_latest_records(df_cdc, primary_keys):
    """
    Returns a Spark DataFrame with the latest records for the primary_keys
    based on CDC columns nv_data_ts, nv_data_xid, and nv_data_xoffset.

    For <PERSON>, for the same ts and xid, the latest record has NULL xoffset.

    For TiCDC, an "update" statement may be published as "insert" then "delete" with the same ts and xid (tso),
        and NULL xoffset. In such cases, order by change type to take the "insert" message as latest.

    :param df_cdc:          Input Spark DataFrame of CDC records
    :param primary_keys:    Primary keys of source table
    """

    df_latest = (
        df_cdc.withColumn(
            "row_number",
            row_number().over(
                Window.partitionBy(primary_keys).orderBy(
                    [
                        desc("nv_data_ts"),
                        desc("nv_data_xid"),
                        desc_nulls_first("nv_data_xoffset"),
                        desc("nv_data_change_type"),
                    ]
                )
            ),
        )
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df_latest
