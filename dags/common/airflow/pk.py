from airflow.models import Variable


def manage_primary_keys_variable(schema, table, primary_keys):
    """
    Updates Airflow variable with_other_pk if and only if a table uses columns other than 'id' as primary key.
    with_other_pk is of the form { schema1.table1: [pk1], schema2.table2 [pk2, pk3 ...] }

    :param schema:          Schema name
    :param table:           Table name
    :param primary_keys:    List of column names used as primary key e.g., ['id', 'system_id']
    """
    airflow_variable_name = "with_other_pk"
    if primary_keys == ["id"]:
        return

    with_other_pk = Variable.get(airflow_variable_name, deserialize_json=True, default_var={})
    with_other_pk[f"{schema}.{table}"] = primary_keys
    Variable.set(airflow_variable_name, with_other_pk, serialize_json=True)
