import json
import logging

import requests

# Define the URL for Athena
url = "https://api.ninjavan.co/global/sda/1.0/grafana/webhook"


# Make the POST request
def send_notification_to_athena(context) -> None:
    """
    Making the request to <PERSON> for creating the ticket for the given context of given DAG failures
    """

    try:
        ti = context.get("task_instance")
        task_failed_message = f"Task failed. DAG:{ti.dag_id} Task:{ti.task_id}"
        payload = {
            "message": json.dumps(
                {
                    "instance": "Singapore",
                    "description": f"{task_failed_message}",
                    "component": "Data",
                }
            )
        }

        logging.info("Sending notification to <PERSON><PERSON>")

        response = requests.post(url, json=payload, timeout=10)

        # Check the response
        if response.status_code == 200:
            logging.info("POST request successful.")
            logging.info("Status Code: %s", response.status_code)
            logging.info("Response Text: %s", response.text)
        else:
            logging.error("POST request failed.")
            logging.error("Status Code: %s", response.status_code)
            logging.error("Response Text: %s", response.text)

    except requests.exceptions.RequestException as e:
        logging.error("An error occurred during the request: %s", str(e))
