from airflow.operators.email import EmailOperator


def _email(to, subject, html_content):
    return EmailOperator(task_id="email_alert", to=to, subject=subject, html_content=html_content)


def send_dag_run_failure_alert(context):
    """
    Sends an Email for a DagRun failure.
    For use as DAG's on_failure_callback function.

    :param context:         Contains references to Airflow execution context
    :type context:          dict
    """
    dr = context.get("dag_run")
    webserver_base_url = context.get("conf").get("webserver", "base_url")

    to = context.get("task").email
    subject = f"Airflow alert: {dr}"
    html_content = f"""
        DAG Run failed<br>
        <b>Reason</b>: {context.get("reason")}<br>
        <b>URL</b>: <a href="{webserver_base_url}/tree?dag_id={dr.dag_id}">Link</a><br>
        """

    _email(to, subject, html_content).execute(context)
