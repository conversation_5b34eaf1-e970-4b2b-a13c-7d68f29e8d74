from common.airflow.notifications import athena, chat, email


def create_dag_run_failure_callback(email_on_failure):
    def send_dag_run_failure_alert(context):
        """
        Sends an email (if enabled) and chat message for a DagRun failure.
        For use as DAG's on_failure_callback function.
        Note: only use on DAGs that have timeout defined

        :param context:         Contains references to Airflow execution context
        :type context:          dict
        """
        if email_on_failure:
            email.send_dag_run_failure_alert(context)
        chat.send_dag_run_failure_alert(context)

    return send_dag_run_failure_alert
