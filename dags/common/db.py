def get_spark_jdbc_config(host, port, user, password, schema):
    """Returns url & properties to be used by spark jdbc"""
    jdbc_flags = {"zeroDateTimeBehavior": "convertToNull", "tinyInt1isBit": "false"}
    serialized_jdbc_flags = "&".join([f"{k}={v}" for k, v in jdbc_flags.items()])
    url = f"jdbc:mysql://{host}:{port}/{schema}?{serialized_jdbc_flags}"

    conn_props = {"user": user, "password": password, "driver": "com.mysql.cj.jdbc.Driver"}
    return url, conn_props
