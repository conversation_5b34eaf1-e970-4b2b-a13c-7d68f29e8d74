from dataclasses import asdict

from metadata.pii import SCHEMAS
from metadata.constants import DataLakeGcsBucketUris, DataLakeGcsNinjaMartBucketUris, LegacyGcsBucketUris


def strip_uri(gcs_uri):
    prefix = "gs://"
    # Bucket names in our GCP project always start with `nv-data` so it is safe to strip their URI using lstrip.
    return gcs_uri.lstrip(prefix).rstrip("/")


def get_nv_data_bucket_uri(env, bucket_type, schema="any", strip=False):
    """
    Gets relevant GCS data lake bucket URI to use.
    Schemas will be using the legacy URIs until they have been migrated to use the PII pipeline.

    :param env:             Airflow environment
    :param schema:          Schema name. If "any", refer to new bucket.
    :param bucket_type:     "raw", "processed", or "db"
    :param strip:           Boolean, whether to strip URI
    :return:                GCS bucket
    :rtype:                 string
    """

    pii_schemas = SCHEMAS[env]

    if schema.startswith("ninjamart"):
        buckets = DataLakeGcsNinjaMartBucketUris(env)
    else:
        buckets = DataLakeGcsBucketUris(env)

    bucket = asdict(buckets)[bucket_type]
    if strip:
        return strip_uri(bucket)

    return bucket
