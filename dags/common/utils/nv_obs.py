from dataclasses import asdict
import logging
from metadata.constants import DataLakeObsBucketUris, DataLakeObsNinjaMartBucketUris


def strip_uri(object_uri):
    prefix = "obs://"
    # Bucket names in our HWC project always start with `nv-data` so it is safe to strip their URI using lstrip.
    return object_uri.lstrip(prefix).rstrip("/")


def get_nv_data_bucket_uri(env, bucket_type, schema="any", strip=False):
    """
    Gets relevant Obs data lake bucket URI to use.
    Schemas will be using the legacy URIs until they have been migrated to use the PII pipeline.

    :param env:             Airflow environment
    :param schema:          Schema name. If "any", refer to new bucket.
    :param bucket_type:     "raw", "processed", or "db"
    :param strip:           Boolean, whether to strip URI
    :return:                OBS bucket
    :rtype:                 string
    """

    if schema.startswith("ninjamart"):
        buckets = DataLakeObsNinjaMartBucketUris(env)
    else:
        buckets = DataLakeObsBucketUris(env)

    bucket = asdict(buckets)[bucket_type]
    if strip:
        return strip_uri(bucket)

    return bucket


def get_obs_client():
    from obs import ObsClient

    try:
        # Attempt to get the connection details from Airflow's connection system.

        from airflow.hooks.base import BaseHook

        obs_conn = BaseHook.get_connection("hwc")
        access_key = obs_conn.login
        secret_key = obs_conn.password
        server = obs_conn.host

    except Exception as e:
        # If there's an error, fall back to environment variables.

        import os

        access_key = os.environ.get("OBS_ACCESS_KEY")
        secret_key = os.environ.get("OBS_SECRET_KEY")
        server = os.environ.get("OBS_ENDPOINT")

        if not access_key or not secret_key or not server:
            raise ValueError("Missing OBS configuration in both Airflow connection and environment variables.")

    return ObsClient(access_key_id=access_key, secret_access_key=secret_key, server=server)

def fetch_max_object_in_batch(obs_client, bucket_name, prefix, marker=None):
    """
    Fetch the maximum (latest) object in a batch of up to 1000 objects from an OBS storage bucket.

    Args:
        bucket_name (str): Name of the OBS bucket.
        prefix (str): Prefix to filter the objects by.
        marker (str, optional): Marker to start listing the objects from. Defaults to None.

    Returns:
        dict: The object with the latest 'lastModified' timestamp in the current batch.
        str: The next marker to continue listing objects if more are available.
    """
    blobs = obs_client.listObjects(bucketName=bucket_name, prefix=prefix, marker=marker)
    max_object = max(blobs.body.contents, key=lambda x: x["lastModified"])
    next_marker = blobs.get("body").get("next_marker")
    logging.debug(max_object, next_marker)
    return max_object, next_marker


def fetch_latest_object(obs_client, bucket_name, prefix, marker=None):
    """
    Fetch the latest object across all batches from an OBS storage bucket based on the 'lastModified' timestamp.

    Args:
        bucket_name (str): Name of the OBS bucket.
        prefix (str): Prefix to filter the objects by.
        marker (str, optional): Marker to start listing the objects from. Defaults to None.

    Returns:
        dict: The object with the latest 'lastModified' timestamp across all batches.
    """
    latest_object, next_marker = fetch_max_object_in_batch(obs_client, bucket_name, prefix, marker)

    while next_marker:
        current_object, next_marker = fetch_max_object_in_batch(obs_client, bucket_name, prefix, next_marker)
        latest_object = max([current_object, latest_object], key=lambda x: x["lastModified"])

    return latest_object


def fetch_delta_checkpoint(obs_client, bucket_name, prefix):
    """
    Fetch the _last_checkpoint file name with given prefix, if it exists.
    Delta generates a _last_checkpoint file in log.
    It contains the last checkpoint version ID and its size.
    e.g., {"version":50,"size":28}.
    """
    import json

    version = None
    response = obs_client.getObject(bucket_name, prefix, loadStreamInMemory=True)

    if response["status"] == 404:
        return version

    try:
        if "body" in response and "buffer" in response["body"]:
            buffer_content = response["body"]["buffer"].decode("utf-8")
            last_checkpoint_json = json.loads(buffer_content)
            version = last_checkpoint_json.get("version")

    except (UnicodeDecodeError, json.JSONDecodeError) as e:
        logging.error(f"Error decoding or parsing JSON: {e}")
        return version

    if version is None:
        return version

    # The formatted checkpoint file name generated by Delta is left padded up to 20 digits.
    return f"{version:020d}.checkpoint.parquet"


def get_delta_log_metrics(obs_client, bucket_name, prefix):
    """
    Get Delta log metrics from the latest commit in OBS.
    
    Args:
        obs_client: OBS client instance
        bucket_name (str): Name of the OBS bucket
        prefix (str): Prefix to the Delta log directory
        
    Returns:
        tuple: (latest_version, operation_metrics, last_modified)
    """
    import json
    
    # List all json files in the Delta log directory
    response = obs_client.listObjects(bucketName=bucket_name, prefix=prefix, delimiter=".json")
    if not response.body.contents:
        raise Exception(f"No json logs found in '{bucket_name}/{prefix}'.")
    
    # Get the latest log file
    latest_log = max(response.body.contents, key=lambda x: x["lastModified"])
    latest_log_key = latest_log["key"]
    
    # Get the content of the latest log file
    log_response = obs_client.getObject(bucket_name, latest_log_key, loadStreamInMemory=True)
    if log_response.status != 200:
        raise Exception(f"Failed to get log file {latest_log_key}: {log_response.status}")
        
    latest_log_content = log_response.body.buffer.decode('utf-8')
    latest_log_data = [json.loads(line) for line in latest_log_content.strip().split('\n')]
    latest_version = latest_log_key.split('/')[-1].split('.')[0]
    latest_commit = next((entry for entry in latest_log_data if "commitInfo" in entry), None)
    
    operation_metrics = {}
    if latest_commit and "commitInfo" in latest_commit:
        operation_metrics = latest_commit["commitInfo"].get("operationMetrics", {})
    
    return latest_version, operation_metrics, latest_log["lastModified"]
