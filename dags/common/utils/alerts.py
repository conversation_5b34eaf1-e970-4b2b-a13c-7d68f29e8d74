import requests
import json
import logging

def raise_grafana_alert(alert_name, summary, state, message):

    url = "https://api.ninjavan.co/global/sda/2.0/grafana/webhook"

    payload = json.dumps({
        "alerts": [
            {
                "panelURL": ""
            }
        ],
        "commonAnnotations": {
            "summary": f'{summary}'
        },
        "commonLabels": {
            "alertname": f'{alert_name}'
        },
        "state": state,
        "message": message
    })
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code == 200:
        return True
    else:
        print(response.text)
        return False
    

def raise_gchat_alert(message, connection):
    url = f"{connection['host']}/?key={connection['key']}&token={connection['token']}"
    logging.info("Sending notification to Google Chat")
    response = requests.post(
                url=url,
                data=json.dumps({"text": message}),
                headers={"Content-Type": "application/json"},
            )

    if response.status_code == 200:
        return True
    else:
        print(response.text)
        return False
