import os
from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from maintainx.operators.maintainx_to_gcs_operator import MaintainXToGCSOperator
from metadata.maintainx import MAINTAINX_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = os.environ.get("AIRFLOW__KUBERNETES__NAMESPACE", "dev")

default_args = {
    "owner": "airflow",
    "start_date": "2023-07-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    dag_id="datalake_maintainx",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # Daily 00:00am SGT,
    catchup=False,  # Don't need catchup because we can only retrieve the latest data
    concurrency=3,
    max_active_runs=1,
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in MAINTAINX_CONFIG.items():
        entity_config = {"entity": entity, **config}
        load_objects = MaintainXToGCSOperator(
            task_id=f"load_maintainx_objects_{entity}",
            entity_config=entity_config,
            gcs_bucket=gcs_bucket + "-raw",
            gcs_folder_path=f"maintainx/objects/{entity}",
            maintainx_conn_id="maintainx_default",
            gcs_conn_id="google_cloud_default",
        )
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[gcs_bucket, entity, "{{ds}}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "1",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
        )
        load_objects >> merge_delta
