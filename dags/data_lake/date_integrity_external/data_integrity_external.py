from datetime import timedelta
from pathlib import Path

from google.cloud import storage
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor
from common.airflow import notifications as notif
from metadata.constants import Timeout
from metadata.data_integrity_external_tables import INCLUDED_TABLES
from metadata.appsflyer import APPSFLYER_CONFIG


env = Variable.get("env")
gs_raw_bucket = f"nv-data-{env}-data-lake-raw"
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")


default_args = {
    "owner": "airflow",
    "start_date": "2023-05-10",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


def new_data_exist(object_path, object_check_date, object_partition_col, **context):
    storage_client = storage.Client()
    bucket = storage_client.get_bucket(gs_raw_bucket)
    blobs = bucket.list_blobs(prefix=object_path)

    if object_check_date == "t-1":
        nv_updated_date = context["next_ds"]
    elif object_check_date == "t-2":
        nv_updated_date = context["ds"]

    partition_blobs = [blob for blob in blobs if blob.name.find(f"{object_partition_col}={nv_updated_date}") != -1]
    if len(partition_blobs) == 0:
        exception_str = f"No new data found for path {object_path} on {nv_updated_date}"
        raise Exception(exception_str)


def get_object_partition_col(schema):
    if schema in ("appsflyer_raw_data_reports", "appsflyer_aggregated_report"):
        object_partition_col = APPSFLYER_CONFIG[schema.replace('appsflyer_', '')]['src_partition_column']
    else:
        object_partition_col = "nv_updated_date"
    return object_partition_col


for schema, metadata in INCLUDED_TABLES.items():
    dag_id = f"datalake_external_data_integrity_{schema}"
    db_dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval="0 23 * * *",  # Daily 07:00am SGT
        catchup=False,
        concurrency=2,
        tags=["data_lake", "external_tables"],
    )
    globals()[dag_id] = db_dag

    with db_dag as dag:
        skip_check_new_data_exist_tables = metadata.get("skip_check_new_data_exist_tables", [])

        for table in metadata['tables']:
            object_path = metadata.get("object_path", schema)
            object_check_date = metadata.get("object_check_date", "t-1")
            object_uri = (metadata.get("object_uri", "gs://{gs_raw_bucket}/{object_path}/objects/{table}/")).format(
                gs_raw_bucket=gs_raw_bucket, object_path=object_path, table=table
            )
            object_partition_col = get_object_partition_col(schema)

            if table not in skip_check_new_data_exist_tables:
                new_data_existence_task = PythonOperator(
                    task_id=f"{schema}_{table}.data_integrity_check_new_data_existence",
                    python_callable=new_data_exist,
                    op_args=[object_uri.replace(f"gs://{gs_raw_bucket}/", ''), object_check_date, object_partition_col],
                    execution_timeout=Timeout.FIFTEEN_MINUTES,
                    provide_context=True
                )
            else:
                new_data_existence_task = None

            if "{table}" in metadata['main_table_uri']:
                table_uri = metadata['main_table_uri'].format(table=table, env=env)
            else:
                table_uri = metadata['main_table_uri'].format(env=env)

            unique_check_task = SparkSubmitOperator(
                task_id=f"{schema}_{table}.data_integrity_check_unique",
                application=f"{tasks_path}/check_unique.py",
                application_args=[
                    schema,
                    table,
                    table_uri.format(env=env),
                    "{{ ds }}",
                    f'{metadata["primary_keys"]}'
                ],
                conn_id="spark_default",
                execution_timeout=Timeout.ONE_HOUR,
            )

            external_dag_id = metadata.get("upstream_external_dag_id", f"datalake_{schema}")

            updated_row_count_check_task = SparkSubmitOperator(
                task_id=f"{schema}_{table}.data_integrity_check_updated_row_count",
                application=f"{tasks_path}/check_updated_row_count.py",
                application_args=[
                    table_uri.format(env=env),
                    object_uri,
                    schema,
                    table,
                    object_partition_col,
                    "{{ ds }}",
                    "{{ next_ds }}",
                ],
                conn_id="spark_default",
                execution_timeout=Timeout.ONE_HOUR,
            )

            if new_data_existence_task:
                new_data_existence_task >> unique_check_task >> updated_row_count_check_task
            else:
                unique_check_task >> updated_row_count_check_task
