import sys

from pyspark.sql import SparkSession
from data_warehouse.utils import logger
from metadata.salesforce import SALESFORCE_CONFIG
from metadata.zendesk import ZENDESK_CONFIG
from metadata.appsflyer import APPSFLYER_CONFIG
from common.stringcase import snake_case


logger = logger.get_logger(__file__)


def is_updated_row_count_equal(
        main_table_path, object_path, execution_date, next_execution_date, updated_at_field, object_partition_col
    ):
    new_updated_row_count = spark.read.parquet(object_path)\
        .filter(f'{object_partition_col}="{execution_date}" and date({updated_at_field}) = "{next_execution_date}"')\
        .count()

    main_table_updated_rows_count = spark.read.format('delta')\
        .load(main_table_path) \
        .filter(f'date({updated_at_field}) = "{next_execution_date}"').count()
    return main_table_updated_rows_count == new_updated_row_count


def find_updated_at_field(schema, table_name):
    if schema == "salescloud":
        for obj in SALESFORCE_CONFIG["objects"]:
            if snake_case(obj["name"]) == table_name:
                return snake_case(obj["updated_at_field"])
    elif schema == "zendesk":
        return ZENDESK_CONFIG[table]["updated_at_field"]
    elif schema in ("appsflyer_raw_data_reports", "appsflyer_aggregated_report"):
        return APPSFLYER_CONFIG[schema.replace('appsflyer_', '')]['event_timestamp']
    elif schema in ('sfmc_campaign', 'sfmc_mobile_push_demographics'):
        return "created_at"
    else:
        return "updated_at"


if __name__ == "__main__":
    main_table_path, object_path, schema, table, object_partition_col, execution_date, next_execution_date = \
        sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()

    logger.info(f"main_table_path: {main_table_path}")
    logger.info(f"object_path: {object_path}")

    updated_at_field = find_updated_at_field(schema, table)
    logger.info(f"updated_at_field: {updated_at_field}")
    logger.info(f"object_partition_col: {object_partition_col}")

    try:
        if not is_updated_row_count_equal(
            main_table_path, object_path, execution_date, next_execution_date, updated_at_field, object_partition_col
        ):
            raise Exception(f"Updated row count is not equal for table {main_table_path} on {execution_date}")
        else:
            logger.info(f"Updated row count is equal for table {main_table_path} on {execution_date}")
    finally:
        spark.stop()