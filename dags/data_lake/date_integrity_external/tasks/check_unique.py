import sys

from pyspark.sql import SparkSession
from datetime import timedelta
from pyspark.sql.functions import count
from common.date import datetime_from_str
from metadata.data_integrity_external_tables import INCLUDED_TABLES

PARTITION_COLUMN = "nv_created_month"


def has_duplicate(df, primary_keys = []):
    df = df.groupBy(primary_keys).agg(count("*").alias('count')).filter('count > 1')
    return df.count() > 0


def should_skip_date_filter(schema, table):
    return table in INCLUDED_TABLES[schema].get('skip_date_filter', [])


if __name__ == "__main__":
    schema, table, delta_path, execution_date, primary_keys = sys.argv[1:]
    primary_keys = list(eval(primary_keys))
    execution_date = datetime_from_str(execution_date)
    _30_days_before = execution_date - timedelta(days=30)
    start_year_month = _30_days_before.strftime("%Y-%m")
    end_year_month = execution_date.strftime("%Y-%m")

    spark = SparkSession.builder.getOrCreate()

    try:
        df = spark.read.format('delta').load(delta_path)
        should_skip_date_filter = should_skip_date_filter(schema, table)
        print("should_skip_date_filter: ", should_skip_date_filter)

        if not should_skip_date_filter:
            df = df.filter(f'{PARTITION_COLUMN} between "{start_year_month}" and {end_year_month}')
        else:
            primary_keys.remove(PARTITION_COLUMN)
        if has_duplicate(df, primary_keys):
            raise Exception(f"Duplicate happens table {delta_path} between {start_year_month} and {end_year_month}")
    finally:
        spark.stop()