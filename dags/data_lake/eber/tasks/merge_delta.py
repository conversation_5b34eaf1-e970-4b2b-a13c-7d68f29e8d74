import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number, from_unixtime, col

from common.spark import pii
from metadata.constants import LegacyObsBucketUris
from metadata.eber import EBER_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
ID = "id"
CREATED_AT = "created_at"
OBJECT_BUCKET = LegacyObsBucketUris.raw


def get_delta_path(obs_bucket, entity):
    """
    Returns path at which an Eber entity delta table is stored.

    :param obs_bucket:  OBS bucket
    :param entity:      Eber entity (e.g. users)
    :return:            OBS path
    """
    return f"obs://{obs_bucket}/eber/delta/{entity}"


def get_obj_path(env, entity):
    """
    Returns path at which incremental change data of an Eber entity is stored.

    :param obs_bucket:  OBS bucket
    :param entity:      Eber entity (e.g. users, transactions)
    :return:            OBS path
    """
    return f"{OBJECT_BUCKET.format(env)}/eber/objects/{entity}"


def get_object(spark, entity, path, date_str, pii_fields):
    """
    Fetches incremental change data of an Eber entity for a specified updated date.

    :param spark:       Spark session
    :param entity:      Eber entity
    :param path:        OBS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :param pii_fields:  PII fields to be masked
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"

    # Set comprehensive Spark configurations to handle timestamp schema mismatches and timestamp_ntz issues
    spark.conf.set("spark.sql.parquet.int96RebaseModeInRead", "LEGACY")
    spark.conf.set("spark.sql.parquet.enableVectorizedReader", "false")
    spark.conf.set("spark.sql.legacy.parquet.nanosAsLong", "true")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    spark.conf.set("spark.sql.parquet.datetimeRebaseModeInRead", "LEGACY")
    spark.conf.set("spark.sql.parquet.int96TimestampConversion", "true")
    spark.conf.set("spark.sql.ansi.enabled", "false")
    spark.conf.set("spark.sql.files.ignoreCorruptFiles", "true")
    spark.conf.set("spark.sql.files.ignoreMissingFiles", "true")

    # Try different approaches to read the data to handle timestamp_ntz conversion issues
    max_attempts = 3
    attempt = 1
    last_exception = None

    while attempt <= max_attempts:
        try:
            if attempt == 1:
                # First attempt: Read with minimal options but with path existence check
                print(f"Attempt {attempt}/{max_attempts}: Reading parquet with minimal options...")
                df = spark.read.option("timeZone", "UTC").parquet(path)
            elif attempt == 2:
                # Second attempt: Disable schema merging and vectorized reader
                print(f"Attempt {attempt}/{max_attempts}: Reading with schema merging disabled...")
                df = spark.read.option("mergeSchema", "false") \
                    .option("timeZone", "UTC") \
                    .option("vectorizedReader", "false") \
                    .option("recursiveFileLookup", "true") \
                    .parquet(path)
            else:
                # Third attempt: Use permissive mode and infer schema
                print(f"Attempt {attempt}/{max_attempts}: Reading with permissive mode and schema inference...")
                df = spark.read.option("mode", "PERMISSIVE") \
                    .option("timeZone", "UTC") \
                    .option("inferSchema", "true") \
                    .option("recursiveFileLookup", "true") \
                    .option("pathGlobFilter", "*.parquet") \
                    .parquet(path)

            # If we reach here, read was successful
            print(f"Successfully read parquet data from {path}")
            print(f"Schema: {df.schema}")
            break

        except Exception as e:
            last_exception = e
            print(f"Attempt {attempt}/{max_attempts} failed: {str(e)}")
            attempt += 1

    # If all attempts failed, raise the last exception
    if attempt > max_attempts:
        print(f"All {max_attempts} attempts to read parquet data failed")
        raise last_exception

    # Check if dataframe is empty
    if df.rdd.isEmpty():
        print(f"Warning: Dataframe is empty for path {path}")
        # Return empty dataframe with expected schema
        return spark.createDataFrame([], df.schema)

    # Convert timestamp fields if they are stored as bigint or have timestamp_ntz issues
    # Handle common timestamp fields
    timestamp_fields = ["created_at", "updated_at"]

    # Add entity-specific timestamp fields from config
    if entity in EBER_CONFIG:
        if "created_at_field" in EBER_CONFIG[entity]:
            timestamp_fields.append(EBER_CONFIG[entity]["created_at_field"])
        if "updated_at_field" in EBER_CONFIG[entity]:
            timestamp_fields.append(EBER_CONFIG[entity]["updated_at_field"])

    # Deduplicate the list
    timestamp_fields = list(set(timestamp_fields))

    # Convert all timestamp fields from long to timestamp if needed
    for field in timestamp_fields:
        if field in df.columns:
            try:
                field_type = df.schema[field].dataType.typeName()
                print(f"Field {field} has type {field_type}")

                if field_type == "long" or field_type == "integer":
                    print(f"Converting {field} from {field_type} to timestamp")
                    df = df.withColumn(field, from_unixtime(col(field)))
                elif "timestamp" in field_type.lower():
                    # Handle all timestamp types by converting to string and back to timestamp
                    print(f"Converting {field} from {field_type} to standard timestamp")
                    df = df.withColumn(field, col(field).cast("string").cast("timestamp"))
            except Exception as e:
                print(f"Warning: Error processing timestamp field {field}: {str(e)}")
                # Continue with other fields

    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, month_fmt))
    )
    # get latest update by id
    updated_at = EBER_CONFIG[entity]["updated_at_field"]
    df = (
        df.withColumn("row_number", row_number().over(Window.partitionBy(SYSTEM_ID, ID).orderBy(desc(updated_at))))
        .filter("row_number = 1")
        .drop("row_number")
    )
    if pii_fields:
        df = pii.mask(spark, df, pii_fields)
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Eber entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)


def merge(spark, entity, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param entity:  Eber entity
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    updated_at = EBER_CONFIG[entity]["updated_at_field"]
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} >= delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, env, obs_bucket, entity, date_str, pii_fields):
    try:
        print(f"Starting merge_delta for entity: {entity}, date: {date_str}")
        obj_path = get_obj_path(env, entity)
        print(f"Reading data from path: {obj_path}")

        try:
            obj_df = get_object(spark, entity, obj_path, date_str, pii_fields)

            # Check if dataframe is empty or has no rows
            row_count = obj_df.count()
            print(f"Found {row_count} records to process")

            if row_count == 0:
                print(f"No records to update for entity {entity} on date {date_str}")
                return

            delta_path = get_delta_path(obs_bucket, entity)
            print(f"Delta table path: {delta_path}")

            # Check if delta table exists
            is_delta = DeltaTable.isDeltaTable(spark, delta_path)
            print(f"Is existing delta table: {is_delta}")

            if is_delta:
                print(f"Merging {row_count} records into existing delta table")
                merge(spark, entity, obj_df, delta_path)
            else:
                print(f"Creating new delta table with {row_count} records")
                create(obj_df, delta_path)

            print(f"Successfully completed merge_delta for entity: {entity}")

        except Exception as e:
            print(f"Error processing entity {entity}: {str(e)}")
            raise

    except Exception as e:
        print(f"Failed to execute merge_delta: {str(e)}")
        raise


if __name__ == "__main__":
    try:
        # Validate command line arguments
        if len(sys.argv) < 6:
            print("Error: Insufficient arguments")
            print("Usage: <script> <obs_bucket> <env> <entity> <date_str> <pii_str>")
            sys.exit(1)

        obs_bucket, env, entity, date_str, pii_str = sys.argv[1:]
        print(f"Starting job with parameters: obs_bucket={obs_bucket}, env={env}, entity={entity}, date={date_str}")

        # Parse PII fields from string representation
        try:
            pii_fields = [
                f.strip() for f in pii_str[(pii_str.index("[") + 1): (pii_str.index("]"))].replace("'", "").split(",")
            ]
            print(f"PII fields to mask: {pii_fields}")
        except (ValueError, IndexError) as e:
            print(f"Error parsing PII fields from '{pii_str}': {str(e)}")
            print("Using empty PII fields list instead")
            pii_fields = []

        # Initialize Spark session with additional configurations for better error handling
        print("Initializing Spark session...")
        spark = SparkSession.builder \
            .config("spark.sql.debug.maxToStringFields", 100) \
            .config("spark.sql.execution.arrow.pyspark.enabled", "false") \
            .getOrCreate()

        # Execute the main processing function
        print("Starting data processing...")
        merge_delta(spark, env, obs_bucket, entity, date_str, pii_fields)
        print("Data processing completed successfully")

    except Exception as e:
        print(f"ERROR: Job failed with exception: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)

    finally:
        # Always stop the Spark session
        if 'spark' in locals():
            print("Stopping Spark session...")
            spark.stop()
            print("Spark session stopped")

    print("Job completed successfully")
    sys.exit(0)