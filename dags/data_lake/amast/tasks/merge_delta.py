import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format
from metadata.amast import AMAST_CONFIG

NV_CREATED_MONTH = "nv_created_month"


def get_object(spark, path, date_str, partition_field):
    month_fmt = "yyyy-MM"
    object_path = f"{path}/nv_updated_date={date_str}"
    print(f"object_path: {object_path}")

    df = spark.read.option("mergeSchema", "true").parquet(object_path)

    if partition_field:
        df = df.withColumn(NV_CREATED_MONTH, date_format(partition_field, month_fmt))
    return df


def merge(spark, entity, obj_df, path, system_id):
    delta_table = DeltaTable.forPath(spark, path)
    primary_keys = AMAST_CONFIG[system_id][entity]["primary_keys"]

    updated_column = AMAST_CONFIG[system_id][entity].get("filter_column")

    merge_condition = " AND ".join(f"delta.{col} = cdc.{col}" for col in primary_keys)

    (
        delta_table.alias("delta")
        .merge(obj_df.alias("cdc"), merge_condition)
        .whenMatchedUpdateAll(condition=f"cdc.{updated_column} >= delta.{updated_column}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, env, obs_bucket, entity, system_id, date_str):
    obj_path = f"obs://nv-data-{env}-data-lake-raw/amast/{entity}/{system_id}"
    partition_field = AMAST_CONFIG[system_id][entity].get("partition_column")
    obj_df = get_object(spark, obj_path, date_str, partition_field)

    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = f"obs://{obs_bucket}/amast/{entity}/{system_id}"
    print(f"delta_path: {delta_path}")

    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, entity, obj_df, delta_path, system_id)
    else:
        if partition_field:
            obj_df.write.mode("overwrite").format("delta").partitionBy(NV_CREATED_MONTH).save(delta_path)
        else:
            obj_df.write.mode("overwrite").format("delta").save(delta_path)


if __name__ == "__main__":
    env, entity, system_id, date_str = sys.argv[1:]
    obs_bucket = f"nv-data-{env}-data-lake"
    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, env, obs_bucket, entity, system_id, date_str)
    spark.stop()