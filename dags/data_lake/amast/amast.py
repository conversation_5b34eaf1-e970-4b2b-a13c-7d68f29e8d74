from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from amast.operators.amast_to_obs_operator import AmastToOBSOperator
from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.amast import AMAST_CONFIG
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2023-10-16",
    "retries": 1,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "5g",
    "spark.executor.cores": "2",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "1",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
}

with DAG(
    dag_id="datalake_amast",
    default_args=default_args,
    schedule_interval="0 22 * * *",  # Daily 06:00am SGT
    concurrency=2,
    catchup=False,
    max_active_runs=1,
    tags=["data_lake"],
) as dag:
    obs_bucket = f"nv-data-{env}-data-lake-raw"

    for system_id, entities in AMAST_CONFIG.items():
        for entity, config in entities.items():
            task_id = f"merge_delta_{entity}_{system_id}"

            load_amast_objects = AmastToOBSOperator(
                task_id=f"load_amast_objects_{entity}_{system_id}",
                entity=entity,
                obs_bucket=obs_bucket,
                system_id=system_id,
                execution_timeout=Timeout.ONE_HOUR,
                conn_id="hwc",
            )

            merge_delta = SparkSubmitOperator(
                task_id=task_id,
                name=kebab_case(task_id),
                application=f"{tasks_path}/merge_delta.py",
                application_args=[
                    env,
                    entity,
                    system_id,
                    "{{ds}}",
                ],
                conn_id="spark_default",
                conf=spark_conf,
                execution_timeout=Timeout.ONE_HOUR,
            )

            load_amast_objects >> merge_delta