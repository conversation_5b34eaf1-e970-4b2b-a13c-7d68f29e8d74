import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import db, pk
from common.stringcase import kebab_case

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")


env = Variable.get("env")
# mysql_tables is a variable of the form
#
#   {
#       schema1: [table1, table2 ...],
#       schema2: [table3, table4 ...]
#   }
#
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2019, 11, 4),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}


def fetch_primary_keys(schema, table):
    """
    Returns name and type of columns used as primary keys.
    It has an intended side-effect of updating an Airflow variable to later handle special cases.
    :param schema:      Schema name
    :param table:       Table name
    :return:            Primary keys in {'column_name': 'data_type'} format
    """
    fields = db.get_column_info(schema, table, mysql_connections)
    primary_keys = {f["COLUMN_NAME"]: f["DATA_TYPE"] for f in fields if f["COLUMN_KEY"] == "PRI"}
    pk.manage_primary_keys_variable(schema, table, list(primary_keys.keys()))
    return primary_keys


with DAG(
    dag_id="datalake_db_snapshot",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    user_defined_filters={"tojson": lambda s: json.dumps(s, separators=(",", ":"))},
    tags=["data_lake", "db_cdc"],
) as dag:

    for schema, tables in mysql_tables.items():

        get_conn = PythonOperator(
            task_id=f"get_{schema}_conn", python_callable=db.get_schema_conn, op_args=[schema, mysql_connections]
        )

        for table in tables:
            get_pk = PythonOperator(
                task_id=f"get_pk_{schema}_{table}", python_callable=fetch_primary_keys, op_args=[schema, table]
            )

            task_id = f"{schema}.{table}_snapshot"
            take_snapshot = SparkSubmitOperator(
                task_id=task_id,
                name=kebab_case(task_id),
                application=f"{tasks_path}/create_db_snapshot.py",
                application_args=[
                    env,
                    schema,
                    table,
                    f"{{{{ ti.xcom_pull(task_ids='get_pk_{schema}_{table}') | tojson }}}}",
                    f"{{{{ ti.xcom_pull(task_ids='get_{schema}_conn') | tojson }}}}",
                ],
                conn_id="spark_default",
                conf={"spark.executor.instances": "5", "spark.executor.memory": "10g", "spark.driver.memory": "10g"},
            )

            get_conn >> get_pk >> take_snapshot
