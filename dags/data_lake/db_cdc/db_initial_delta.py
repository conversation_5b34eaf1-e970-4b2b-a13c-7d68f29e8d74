from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2019, 12, 20),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}


with DAG(
    dag_id="datalake_db_initial_delta",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    tags=["data_lake", "db_cdc"],
) as dag:
    for schema, tables in mysql_tables.items():
        for table in tables:
            task_id = f"{schema}.{table}_initial_delta"
            SparkSubmitOperator(
                task_id=task_id,
                name=kebab_case(task_id),
                application=f"{tasks_path}/create_initial_delta.py",
                application_args=[env, schema, table],
                conn_id="spark_default",
                conf={"spark.executor.instances": "5", "spark.executor.memory": "10g", "spark.driver.memory": "10g"},
            )
