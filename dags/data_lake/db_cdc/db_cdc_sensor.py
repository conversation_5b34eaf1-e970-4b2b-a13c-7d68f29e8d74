from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.operators.python import Python<PERSON>perator

from huawei.sensors.obs_objects_with_prefix_existence_sensor import HuaweiOBSObjectsWithPrefixExistenceSensor

from common.airflow import notifications as notif
from common.utils.nv_obs import get_nv_data_bucket_uri
from metadata.constants import Timeout
from metadata.ticdc import TICDC_SCHEMAS
from metadata.maxwell import MAXWELL_SCHEMAS


INTERVAL_DURATION = 60
ALERT_CHANNEL = "high_priority_alert"

env = Variable.get("env")
num_intervals = INTERVAL_DURATION // 15

maxwell_schemas = MAXWELL_SCHEMAS.get(env)
ticdc_schemas = TICDC_SCHEMAS.get(env)

default_args = {
    "owner": "airflow",
    "start_date": datetime(2020, 1, 17, 0, 0, 0),
    "retries": 3,
    "retry_delay": timedelta(minutes=2),
    "executor_config": {"KubernetesExecutor": {"request_memory": "128Mi"}},
    "priority_weight": 10,
    "pool": "sensor_pool",
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


def raise_cdc_alert(**kwargs):
    """
    If we do not have data for an hour at CDC instance replication level, Grafana alert shall be raised.
    This only applies for production schemas.
    """
    cdc_instance_alert_disabled = ("misc",)
    should_alert = kwargs.get("env") == "prod"
    cdc_instance = kwargs.get("cdc_instance")

    if cdc_instance in cdc_instance_alert_disabled or not should_alert:
        print(f"Grafana alert is disabled.")
        return None

    schemas = maxwell_schemas.get(cdc_instance) or ticdc_schemas.get(cdc_instance)
    instance_down = 0

    for schema in schemas:
        tables = kwargs["task_instance"].xcom_pull(task_ids=f"{schema}.list_tables_with_changes", key="return_value")

        if tables:
            print(f"Changes for tables {tables} detected from CDC instance {cdc_instance}")
            return None

        if tables is None or len(tables) == 0:
            instance_down += 1
            if tables is None:
                message_template = "No tables returned from upstream for schema: {schema}:{cdc_instance}"
            else:
                message_template = "No changes found for tables in schema {schema}:{cdc_instance}"
            print(message_template.format(schema=schema, cdc_instance=cdc_instance))

    if len(schemas) == instance_down:
        alert_name = f"CDC instance {cdc_instance} is down for the hour"
        summary = f"{cdc_instance} is down. The following schemas {schemas} are affected in execution hour {kwargs['dag_run']}"
        try:
            print("Raising a Grafana alert")
            raise_grafana_alert(alert_name, summary)
        except Exception as e:
            print(e)
            notif.chat.send_ti_failure_alert(kwargs)


def raise_grafana_alert(alert_name, summary):
    import requests
    import json

    url = "https://api.ninjavan.co/global/sda/2.0/grafana/webhook"

    payload = json.dumps(
        {
            "alerts": [{"panelURL": ""}],
            "commonAnnotations": {"summary": f"{summary}"},
            "commonLabels": {"alertname": f"{alert_name}"},
            "state": "Alerting",
            "message": "Data",
        }
    )
    headers = {"Content-Type": "application/json"}
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)


def list_changes(bucket_name, schema, path, execution_date, **kwargs):
    import logging
    from datetime import timedelta

    from common.utils.nv_obs import get_obs_client

    """Creates a list of tables with changes per DB."""
    client = get_obs_client()
    tables_with_changes = set()
    delimiter = "/part"

    for i in range(num_intervals):
        ts = execution_date + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={ts:%Y-%m-%d}/time={ts:%H-%M-%S}/database={schema}"
        logging.info(f"Listing changes in '{bucket_name}/{prefix}'")
        response = client.listObjects(bucketName=bucket_name, prefix=prefix, delimiter=delimiter)
        body = response["body"]

        if response["status"] == 200 and body and "commonPrefixs" in body:
            common_prefixes = body["commonPrefixs"]
            for common_prefix in common_prefixes:
                sub_path = common_prefix["prefix"]
                table = sub_path.replace(f"{prefix}/table=", "").replace(delimiter, "")
                tables_with_changes.add(table)

    return list(tables_with_changes)


def _get_path(schema):
    """
    Check CDC message stream based on connection and schema
    """
    # Combine schemas from all TiCDC instances into a single list.
    schemas = sum(ticdc_schemas.values(), [])
    return "ticdc_stream" if schema in schemas else "cdc_stream"


with DAG(
    dag_id="datalake_cdc_sensor",
    schedule_interval=f"*/{INTERVAL_DURATION} * * * *",
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    tags=["data_lake", "db_cdc"],
) as dag:

    for cdc_instance, cdc_schemas in {**maxwell_schemas, **ticdc_schemas}.items():
        cdc_check_instance = PythonOperator(
            task_id=f"{cdc_instance}.check_instance",
            op_kwargs={"cdc_instance": cdc_instance, "env": env},
            python_callable=raise_cdc_alert,
            retries=1,
        )

        for schema in cdc_schemas:
            bucket_raw = get_nv_data_bucket_uri(env=env, bucket_type="raw", schema=schema, strip=True)
            schema_sensor = HuaweiOBSObjectsWithPrefixExistenceSensor(
                task_id=f"{schema}.check_next_partition",
                bucket_name=bucket_raw,
                prefix=f"{_get_path(schema)}/data/date={{{{ next_ds }}}}/"
                + f"time={{{{ next_execution_date.strftime('%H-%M-%S') }}}}/database={schema}",
                execution_timeout=Timeout.FIVE_MINUTES,
                on_failure_callback=None,
                retries=1,
            )

            schema_wait = BashOperator(
                task_id=f"{schema}.wait",
                bash_command="sleep 900",
                trigger_rule="none_skipped",
            )

            schema_list_tables_from_bucket_raw = PythonOperator(
                task_id=f"{schema}.list_tables_with_changes",
                python_callable=list_changes,
                op_kwargs={
                    "bucket_name": bucket_raw,
                    "schema": schema,
                    "path": _get_path(schema),
                },
                retries=1,
            )

            schema_sensor >> schema_wait >> schema_list_tables_from_bucket_raw >> cdc_check_instance
