import json
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import current_timestamp, date_format, lit, unix_timestamp
from pyspark.sql.utils import AnalysisException

from common.date import date_from_ts
from data_warehouse.utils import logger
from metadata.constants import DataIntegrityFix

PARTITION_COLUMN = DataIntegrityFix.PARTITION_COLUMN
logger = logger.get_logger(__file__)


class MismatchPatcher:
    def __init__(self, env, schema, table, execution_date, primary_keys):
        self.env = env
        self.schema = schema
        self.table = table
        self.primary_keys = primary_keys
        self.execution_date = execution_date
        self.delta_snapshot_merge = self.generate_merge_statement()

    def get_gcs_path(self, mismatch_type):
        return DataIntegrityFix.MISMATCH_FOLDER_URI.format(
            env=self.env,
            schema=self.schema,
            table=self.table,
            mismatch_type=mismatch_type,
            execution_date=self.execution_date,
        )

    def generate_merge_statement(self):
        delta_snapshot_merge = [f"delta.{primary_key} = snapshot.{primary_key}" for primary_key in self.primary_keys]
        return " and ".join(delta_snapshot_merge)

    def patch_missing(self, delta_table, missing_df):
        logger.info("Start patching missing records")
        missing_df = append_nv_columns(missing_df, "insert")

        partitions_list = [f"'{i[PARTITION_COLUMN]}'" for i in missing_df.select(PARTITION_COLUMN).distinct().collect()]
        logger.info(f"Partitions to be fixed: {str(partitions_list)}")
        logger.info(f"Number of rows to be inserted: {missing_df.count()}")

        self.process_patch(delta_table, missing_df, self.delta_snapshot_merge, partitions_list)

        logger.info("Missing records patched done")

    def patch_extra(self, delta_table, extra_df):
        logger.info("Start patching extra records")
        df_delete = extra_df.withColumn("nv_data_change_type", lit("delete"))

        partitions_list = [f"'{i[PARTITION_COLUMN]}'" for i in df_delete.select(PARTITION_COLUMN).distinct().collect()]

        for partition in partitions_list:
            new_df_partition = df_delete.filter(f"{PARTITION_COLUMN} = {partition}")
            _partitions_list = [
                f"'{i[PARTITION_COLUMN]}'" for i in new_df_partition.select(PARTITION_COLUMN).distinct().collect()
            ]
            partitions = ", ".join(_partitions_list)
            logger.info(f"Processing {_partitions_list}")

            (
                delta_table.alias("delta")
                .merge(
                    new_df_partition.alias("snapshot"),
                    f"{self.delta_snapshot_merge} AND delta.created_at = snapshot.created_at "
                    f"and delta.{PARTITION_COLUMN} in ({partitions})",
                )
                .whenMatchedDelete(condition="snapshot.nv_data_change_type = 'delete'")
                .execute()
            )
        logger.info("Extra records patched done")

    def process_patch(self, delta_table, mismatch_df, delta_snapshot_merge_str, partitions_list):
        for partition in partitions_list:
            new_df_partition = mismatch_df.filter(f"{PARTITION_COLUMN} = {partition}")
            _partitions_list = [
                f"'{i[PARTITION_COLUMN]}'" for i in new_df_partition.select(PARTITION_COLUMN).distinct().collect()
            ]
            partitions = ", ".join(_partitions_list)
            logger.info(f"Processing {_partitions_list}")

            (
                delta_table.alias("delta")
                .merge(
                    new_df_partition.alias("snapshot"),
                    f"{delta_snapshot_merge_str} AND delta.{PARTITION_COLUMN} in ({partitions})",
                )
                .whenMatchedDelete(condition="snapshot.nv_data_change_type = 'delete'")
                .whenMatchedUpdateAll(
                    condition="""
                    snapshot.nv_data_change_type = 'update'
                     AND (
                         snapshot.nv_data_ts > delta.nv_data_ts
                         OR (snapshot.nv_data_ts = delta.nv_data_ts AND snapshot.nv_data_xid > delta.nv_data_xid)
                         OR (snapshot.nv_data_ts = delta.nv_data_ts AND snapshot.nv_data_xid = delta.nv_data_xid
                            AND (snapshot.nv_data_xoffset > delta.nv_data_xoffset OR snapshot.nv_data_xoffset IS NULL)
                         )
                     )"""
                )
                .whenNotMatchedInsertAll(condition="snapshot.nv_data_change_type <> 'delete'")
                .execute()
            )

    def patch_mismatch(self, delta_table, mismatch_df):
        logger.info("Start patching mismatch records")
        mismatch_df = append_nv_columns(mismatch_df, "update")
        partitions_list = [
            f"'{i[PARTITION_COLUMN]}'" for i in mismatch_df.select(PARTITION_COLUMN).distinct().collect()
        ]
        self.process_patch(delta_table, mismatch_df, self.delta_snapshot_merge, partitions_list)
        logger.info("Mismatch records patched done")


def get_mismatch_df(spark, gcs_path):
    try:
        df = spark.read.parquet(gcs_path)
        return df
    except AnalysisException:
        logger.info(f"{gcs_path} does not exist")
        return None


def append_nv_columns(df, change_type):
    df = df.withColumn("created_month", date_format("created_at", "yyyy-MM"))
    if change_type == "insert":
        df = df.withColumn("nv_data_change_type", lit("mysql_snapshot"))
        df = df.withColumn("nv_data_ts", lit("0"))
    elif change_type == "update":
        df = df.withColumn("nv_data_change_type", lit("update"))
        df = df.withColumn("nv_data_ts", unix_timestamp(current_timestamp()))
    df = df.withColumn("nv_data_xid", lit("0"))
    df = df.withColumn("nv_data_xoffset", lit("0"))
    return df


if __name__ == "__main__":
    try:
        spark = SparkSession.builder.config("spark.databricks.delta.schema.autoMerge.enabled", True).getOrCreate()
        env, schema, table, execution_date, next_execution_date, primary_keys_str = sys.argv[1:]
        primary_keys = json.loads(primary_keys_str)
        execution_date_gcs = date_from_ts(execution_date)

        patcher = MismatchPatcher(env, schema, table, execution_date_gcs, primary_keys)
        missing_gcs_path = patcher.get_gcs_path("missing")
        extra_gcs_path = patcher.get_gcs_path("extra")
        mismatch_gcs_path = patcher.get_gcs_path("mismatch")
        delta_table_uri = DataIntegrityFix.TARGET_DELTA_GCS_URI.format(env=env, schema=schema, table=table)

        missing_df = get_mismatch_df(spark, missing_gcs_path)
        extra_df = get_mismatch_df(spark, extra_gcs_path)
        mismatch_df = get_mismatch_df(spark, mismatch_gcs_path)

        delta_table = DeltaTable.forPath(spark, delta_table_uri)

        if missing_df:
            patcher.patch_missing(delta_table, missing_df)
        if extra_df:
            patcher.patch_extra(delta_table, extra_df)
        if mismatch_df:
            patcher.patch_mismatch(delta_table, mismatch_df)
    finally:
        spark.stop()
