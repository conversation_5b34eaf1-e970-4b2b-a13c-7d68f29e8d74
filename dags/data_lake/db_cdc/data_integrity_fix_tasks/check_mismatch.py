import json
import sys

from pyspark.sql import SparkSession
from pyspark.sql.functions import unix_timestamp

from common.date import date_from_ts
from data_warehouse.utils import logger
from metadata.constants import DataIntegrityFix

logger = logger.get_logger(__file__)


class MismatchChecker:
    def __init__(self, env, source_df, target_df, schema, table, primary_keys, execution_date):
        self.env = env
        self.source_df = source_df
        self.target_df = target_df
        self.schema = schema
        self.table = table
        self.primary_keys = primary_keys
        self.execution_date = execution_date

    def check_mismatch_rows(self, datetime_column="updated_at"):
        logger.info("Checking for mismatch rows...")
        df = self.source_df.join(self.target_df, self.primary_keys, "inner")
        df = df.filter(
            ((unix_timestamp(source_df[datetime_column]) - unix_timestamp(target_df[datetime_column])) > 1) |
            (~self.source_df[datetime_column].isNull() & self.target_df[datetime_column].isNull())
        )
        full_mismatch_df = self.source_df.join(df.select(self.primary_keys), self.primary_keys, "inner")
        mismatch_rows = df.count()

        if mismatch_rows > 0:
            logger.info(f"{mismatch_rows} mismatched {datetime_column}.")
            save_path = DataIntegrityFix.MISMATCH_FOLDER_URI.format(
                env=self.env,
                schema=self.schema,
                table=self.table,
                mismatch_type="mismatch",
                execution_date=self.execution_date,
            )
            full_mismatch_df.write.mode("overwrite").parquet(save_path)
            logger.info("Write mismatch rows done")
        else:
            logger.info("No mismatch rows.")

    def check_missing_rows(self):
        logger.info("Checking for missing rows...")
        df = self.source_df.join(self.target_df, self.primary_keys, "left_anti")
        full_missing_df = self.source_df.join(df.select(self.primary_keys), self.primary_keys, "inner")
        missing_rows = df.count()

        if missing_rows > 0:
            logger.info(f"{missing_rows} missing rows.")
            save_path = DataIntegrityFix.MISMATCH_FOLDER_URI.format(
                env=self.env,
                schema=self.schema,
                table=self.table,
                mismatch_type="missing",
                execution_date=self.execution_date,
            )
            full_missing_df.write.mode("overwrite").parquet(save_path)
            logger.info("Write missing rows done")
        else:
            logger.info("No missing rows.")

    def check_extra_rows(self):
        logger.info("Checking for extra rows...")
        df = self.target_df.join(self.source_df, self.primary_keys, "left_anti")
        full_extra_df = self.target_df.join(df.select(self.primary_keys), self.primary_keys, "inner")
        extra_rows = df.count()

        if extra_rows > 0:
            logger.info(f"{extra_rows} extra rows.")
            save_path = DataIntegrityFix.MISMATCH_FOLDER_URI.format(
                env=self.env, schema=self.schema,
                table=self.table,
                mismatch_type="extra",
                execution_date=self.execution_date
            )
            full_extra_df.write.mode("overwrite").parquet(save_path)
            logger.info("Write extra rows done")
        else:
            logger.info("No extra rows.")


if __name__ == "__main__":
    env, schema, table, execution_date, next_execution_date, primary_keys = sys.argv[1:]
    primary_keys = list(json.loads(primary_keys).keys())
    start_year_month = date_from_ts(execution_date, date_fmt="%Y-%m")
    end_year_month = date_from_ts(next_execution_date, date_fmt="%Y-%m")
    execution_date_gcs = date_from_ts(execution_date)

    try:
        spark = SparkSession.builder.getOrCreate()

        source_df = (
            spark.read.parquet(DataIntegrityFix.SOURCE_SNAPSHOT_GCS_URI.format(env=env, schema=schema, table=table))
            .filter(f"{DataIntegrityFix.FILTER_COLUMN} between '{execution_date}' and '{next_execution_date}'")
            .na.fill("NULL")
        )

        target_df = (
            spark.read.format("delta")
            .load(DataIntegrityFix.TARGET_DELTA_GCS_URI.format(env=env, schema=schema, table=table))
            .filter(
                f"""
                created_month between '{start_year_month}' and '{end_year_month}' and
                {DataIntegrityFix.FILTER_COLUMN} between '{execution_date}' and '{next_execution_date}'
            """
            )
            .na.fill("NULL")
        )

        checker = MismatchChecker(env, source_df, target_df, schema, table, primary_keys, execution_date_gcs)
        checker.check_missing_rows()
        checker.check_extra_rows()
        checker.check_mismatch_rows()
    finally:
        spark.stop()
