import codecs
import json
import sys

from pyspark.sql import SparkSession

from common import db
from data_warehouse.utils import logger
from metadata.constants import DataIntegrityFix

logger = logger.get_logger(__file__)


def create_db_snapshot(
    spark, env, schema, table, primary_keys, conn_dict, execution_date: str, next_execution_date: str
):
    """
    Creates a DB table snapshot in the GCS data lake with Spark.

    :param spark:           Spark session
    :param env:             Data lake environment
    :param schema:          DB schema
    :param table:           DB table
    :param primary_keys:    Primary key(s) in {'column_name': 'data_type'} format
    :param conn_dict:       DB connection details
    """
    url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)

    has_int_key = False
    parallelism = 1000

    if len(primary_keys) == 1:
        column_name, data_type = [*primary_keys.items()][0]
        if data_type in ("int", "bigint", "long"):
            snapshot_column = column_name
            has_int_key = True
    min_value, max_value = _get_min_max(spark, url, conn_props, table, snapshot_column)
    if has_int_key:
        parallelism = (max_value - min_value) // 5000 + 1

    df = spark.read.jdbc(
        url=url,
        table=table,
        column="id",
        lowerBound=min_value,
        upperBound=max_value,
        numPartitions=parallelism,
        properties=conn_props,
    ).filter(f"{DataIntegrityFix.FILTER_COLUMN} between '{execution_date}' and '{next_execution_date}'")
    gcs_path = f"gs://nv-data-{env}-datalake/snapshots/{schema}/{table}"
    df.write.mode("overwrite").parquet(gcs_path)
    logger.info(f"Writing done to {gcs_path}")


def _get_min_max(spark, url, conn_props, table, snapshot_column):
    query = f"(select min({snapshot_column}) as min_value, max({snapshot_column}) as max_value from {table}) t"
    min_max_lst = spark.read.jdbc(url=url, table=query, properties=conn_props).take(1)
    if not min_max_lst:
        raise RuntimeError(f"Empty table {table}")
    return min_max_lst[0]["min_value"], min_max_lst[0]["max_value"]


if __name__ == "__main__":
    env, schema, table, execution_date, next_execution_date, primary_keys_str, conn_dict_str = sys.argv[1:]
    primary_keys = json.loads(primary_keys_str)
    conn_dict = json.loads(conn_dict_str)
    conn_dict["password"] = codecs.decode(conn_dict["password"], "rot-13")

    try:
        spark = SparkSession.builder.getOrCreate()
        create_db_snapshot(spark, env, schema, table, primary_keys, conn_dict, execution_date, next_execution_date)
    finally:
        spark.stop()
