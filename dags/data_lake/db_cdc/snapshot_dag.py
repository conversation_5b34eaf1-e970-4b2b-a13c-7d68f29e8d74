import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict

from airflow.decorators import dag, task
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from common.airflow import notifications as notif
from metadata.constants import Timeout
from common.airflow import db, pk

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
snapshot = Variable.get("snapshot_tables", deserialize_json=True, default_var={})
intervals = int(Variable.get("intervals", default_var=3))  # Default to 3 intervals if not specified
interval_duration = 24 // intervals

default_args = {
    "owner": "airflow",
    "retries": 2,
    "retry_delay": timedelta(minutes=2),

    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

@dag(
    dag_id="db_snapshot_dag",
    schedule_interval=f"0 */{interval_duration} * * *",  # Run every (24 / intervals) hours
    max_active_runs=1,
    tags=["data_lake", "db_cdc", "db_snapshot"],
    start_date=datetime(2023, 11, 28),
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    default_args=default_args,
    catchup=False,
)
def datalake_db_snapshot_dag(**context):
    mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
    snapshot_tables = snapshot
    for schema, tables in snapshot_tables.items():

        @task(task_id=f"get_{schema}_conn", execution_timeout=Timeout.FIFTEEN_MINUTES)
        def get_conn(schema: str):
            return db.get_schema_conn(schema, mysql_connections)

        conn_info = get_conn(schema)

        for table in tables:

            @task(task_id=f"get_pk_{schema}_{table}", execution_timeout=Timeout.FIFTEEN_MINUTES)
            def fetch_primary_keys(schema: str, table: str):
                fields = db.get_column_info(schema, table, mysql_connections)
                primary_keys = {f["COLUMN_NAME"]: f["DATA_TYPE"] for f in fields if f["COLUMN_KEY"] == "PRI"}
                pk.manage_primary_keys_variable(schema, table, list(primary_keys.keys()))
                return primary_keys

            @task(task_id=f"snapshot_args_{schema}_{table}", execution_timeout=Timeout.FIFTEEN_MINUTES)
            def snapshot_args(schema: str, table: str, interval_duration, conn_info: Dict, **context):
                start_time = context["execution_date"]
                end_time = start_time + timedelta(hours=interval_duration)
                ti = context["ti"]
                logging.info(f"Start date for snapshot is {start_time} and end date is {end_time}")
                spark_submit_args = [
                    env,
                    schema,
                    table,
                    json.dumps(ti.xcom_pull(task_ids=f"get_pk_{schema}_{table}", key="return_value")),
                    json.dumps(ti.xcom_pull(task_ids=f"get_{schema}_conn", key="return_value")),
                    start_time.isoformat() if start_time else None,
                    end_time.isoformat() if end_time else None,
                ]
                return spark_submit_args

            get_args = snapshot_args(schema, table, interval_duration, conn_info, **context)

            snapshot_task = SparkSubmitOperator(
                task_id=f"submit_snapshot_{schema}_{table}",
                application=f"{tasks_path}/create_db_snapshot.py",
                application_args=get_args,
                conn_id="spark_default",
                conf={
                    "spark.executor.instances": "10",
                    "spark.executor.memory": "8g",
                    "spark.driver.memory": "6g",
                },
                execution_timeout=Timeout.TWO_HOURS,
            )

            delta_task = SparkSubmitOperator(
                task_id=f"{schema}.{table}_initial_delta",
                application=f"{tasks_path}/create_initial_delta.py",
                application_args=get_args,
                conn_id="spark_default",
                conf={
                    "spark.executor.instances": "8",
                    "spark.executor.memory": "10g",
                    "spark.driver.memory": "6g",
                },
                execution_timeout=Timeout.TWO_HOURS,
            )

            get_primary_keys = fetch_primary_keys(schema, table)
            get_primary_keys.set_downstream(get_args)
            conn_info[schema] >> get_primary_keys >> snapshot_task >> delta_task


datalake_db_snapshot_dag_instance = datalake_db_snapshot_dag()
