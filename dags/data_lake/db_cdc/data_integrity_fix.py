from datetime import timedelta
from pathlib import Path
from functools import partial

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from common.airflow import pk
from common.airflow.notifications.chat import _gchat
from metadata.cdc_data_integrity import EXCLUDED_TABLES
from metadata.constants import Timeout

ONE_MINUTE = 60


env = Variable.get("env")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
tasks_path = "local://" + str(Path(__file__).resolve().parent / "data_integrity_fix_tasks")
excluded_tables = EXCLUDED_TABLES[env]
included_tables = {
    schema: [table for table in tables if table not in excluded_tables.get(schema, [])]
    for schema, tables in mysql_tables.items()
}

spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}

default_args = {
    "owner": "airflow",
    "start_date": "2023-02-15",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
    "weight_rule": "upstream",
}


def fetch_primary_keys(schema, table):
    """
    Returns name and type of columns used as primary keys.
    It has an intended side-effect of updating an Airflow variable to later handle special cases.
    :param schema:      Schema name
    :param table:       Table name
    :return:            Primary keys in {'column_name': 'data_type'} format
    """
    try:
        fields = db.get_column_info(schema, table, mysql_connections)
        primary_keys = {f["COLUMN_NAME"]: f["DATA_TYPE"] for f in fields if f["COLUMN_KEY"] == "PRI"}
        pk.manage_primary_keys_variable(schema, table, list(primary_keys.keys()))
        return primary_keys
    except Exception:
        return {}


def patch_mismatch_task_success_callback(context, schema, table):
    ti = context.get("task_instance")
    ti_failed_message = f"""
        ✅  Patching was done to fix mismatch for *{schema}.{table}*
            *Execution Date*: {context.get("execution_date")}
            *Log URL*: {ti.log_url}
         """
    connection = "google_chat"
    _gchat(ti_failed_message, connection).execute(context)


def create_dag(dag_id, default_args=default_args):
    dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval="0 20 * * *",  # Daily 04:00am SGT,
        catchup=False,
        concurrency=5,
        tags=["db_cdc_data_integrity", "data_integrity_fix"],
    )
    globals()[dag_id] = dag
    return dag


for schema, tables in included_tables.items():
    dag_id = f"datalake_integrity_fix_{schema}"
    db_dag = create_dag(dag_id=dag_id)
    with db_dag as dag:
        get_conn = PythonOperator(
            task_id=f"get_{schema}_conn", python_callable=db.get_schema_conn, op_args=[schema, mysql_connections]
        )

        for table in tables:
            get_pk = PythonOperator(
                task_id=f"get_pk_{schema}_{table}", python_callable=fetch_primary_keys, op_args=[schema, table]
            )

            wait_integrity_check = ExternalTaskSensor(
                task_id=f"{schema}_{table}.wait_integrity_check",
                external_dag_id=f"datalake_db_cdc_data_integrity_{schema}",
                external_task_id=f"check_integrity_{schema}_{table}",
                poke_interval=ONE_MINUTE,
                allowed_states=["failed"],
                mode="reschedule",
                execution_timeout=Timeout.THREE_HOURS,
                on_failure_callback=None,
                failed_states=["success", "skipped"],
                soft_fail=True,
            )

            wait_delta_cdc_task = ExternalTaskSensor(
                task_id=f"{schema}_{table}.wait_delta_cdc_task",
                external_dag_id=f"datalake_cdc_{schema}",
                external_task_id=f"check_delta_{schema}_{table}",
                poke_interval=ONE_MINUTE,
                mode="reschedule",
                allowed_states=["skipped"],
                execution_timeout=Timeout.THREE_HOURS,
            )

            snapshot_task_id = f"{schema}_{table}.snapshot"
            snapshot_task = SparkSubmitOperator(
                task_id=snapshot_task_id,
                trigger_rule="all_success",
                application=f"{tasks_path}/create_db_snapshot.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ execution_date.subtract(hours=4) }}",
                    "{{ next_execution_date.subtract(hours=4) }}",
                    f"{{{{ ti.xcom_pull(task_ids='get_pk_{schema}_{table}') | tojson }}}}",
                    f"{{{{ ti.xcom_pull(task_ids='get_{schema}_conn') | tojson }}}}",
                ],
                conn_id="spark_default",
                conf={**spark_conf[tables_to_size.get(snapshot_task_id, "medium")]},
                execution_timeout=Timeout.THREE_HOURS,
            )

            check_mismatch_task_id = f"{schema}_{table}.check_mismatch"
            check_mismatch_task = SparkSubmitOperator(
                task_id=check_mismatch_task_id,
                trigger_rule="all_success",
                application=f"{tasks_path}/check_mismatch.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ execution_date.subtract(hours=4) }}",
                    "{{ next_execution_date.subtract(hours=4) }}",
                    f"{{{{ ti.xcom_pull(task_ids='get_pk_{schema}_{table}') | tojson }}}}",
                ],
                conn_id="spark_default",
                conf={**spark_conf[tables_to_size.get(check_mismatch_task_id, "medium")]},
                execution_timeout=Timeout.ONE_HOUR,
            )

            wait_delta_cdc_task_before_patch = ExternalTaskSensor(
                task_id=f"{schema}_{table}.wait_delta_cdc_task_before_patch",
                external_dag_id=f"datalake_cdc_{schema}",
                external_task_id=f"check_delta_{schema}_{table}",
                poke_interval=ONE_MINUTE,
                mode="reschedule",
                allowed_states=["skipped"],
            )

            partial_patch_mismatch_task_success_callback = partial(
                patch_mismatch_task_success_callback,
                schema=schema,
                table=table
            )
            patch_mismatch_task_id = f"{schema}_{table}.patch_mismatch"
            patch_mismatch_task = SparkSubmitOperator(
                task_id=patch_mismatch_task_id,
                trigger_rule="all_success",
                application=f"{tasks_path}/patch_mismatch.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ execution_date.subtract(hours=4) }}",
                    "{{ next_execution_date.subtract(hours=4) }}",
                    f"{{{{ ti.xcom_pull(task_ids='get_pk_{schema}_{table}') | tojson }}}}",
                ],
                conn_id="spark_default",
                conf={**spark_conf[tables_to_size.get(patch_mismatch_task_id, "medium")]},
                execution_timeout=Timeout.ONE_HOUR,
                on_success_callback=partial_patch_mismatch_task_success_callback if env == "prod" else None,
            )

            (
                wait_integrity_check
                >> snapshot_task
                >> wait_delta_cdc_task
                >> check_mismatch_task
                >> wait_delta_cdc_task_before_patch
                >> patch_mismatch_task
            )
