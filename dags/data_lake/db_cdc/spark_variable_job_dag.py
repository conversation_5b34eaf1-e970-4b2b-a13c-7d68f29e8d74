from datetime import timedelta
import os
import tempfile

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.utils.dates import days_ago
from metadata.constants import Timeout
from airflow.operators.python import PythonOperator

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'start_date': days_ago(1),
}

# Create DAG
dag = DAG(
    'spark_variable_job_dag',
    default_args=default_args,
    description='DAG to run Spark patching job with code and configuration from Airflow variables',
    schedule_interval=None,  # Set to None for manual triggering only
    catchup=False,
    tags=['spark', 'variable'],
)

# Function to determine and prepare the Spark script path
def prepare_spark_script(**kwargs):
    # First check if a script path is provided (default approach)
    script_path = Variable.get("spark_script_path", default_var=None)
    is_temp_file = False
    
    # If no path is provided, check if the code is provided directly
    if not script_path:
        # Get the Spark code from the Airflow variable
        spark_code = Variable.get("spark_job_code", default_var=None)
        if not spark_code:
            raise ValueError("Neither 'spark_script_path' nor 'spark_job_code' variables are set")
        
        # Create a temporary directory that will persist during the DAG run
        temp_dir = tempfile.mkdtemp(prefix="spark_", dir="/tmp")
        script_path = os.path.join(temp_dir, "spark_job.py")
        
        # Write the code to the temporary file
        with open(script_path, 'w') as f:
            f.write(spark_code)
        
        # Make the script executable
        os.chmod(script_path, 0o755)
        is_temp_file = True
    
    # Push the script path and whether it's a temp file to XCom for other tasks
    kwargs['ti'].xcom_push(key='spark_script_path', value=script_path)
    kwargs['ti'].xcom_push(key='is_temp_file', value=is_temp_file)
    
    return script_path

# Function to get Spark configuration from Airflow variable
def get_spark_conf():
    # Get Spark configuration from a variable (with a default fallback)
    default_conf = {
        "spark.executor.memory": "8g",
        "spark.executor.cores": "2",
        "spark.executor.instances": "2",
        "spark.driver.memory": "4g",
        "spark.driver.cores": "2",
        "spark.dynamicAllocation.enabled": "false",
    }
    
    try:
        # Try to get the configuration from the variable
        spark_conf = Variable.get("spark_job_conf", deserialize_json=True, default_var=default_conf)
        return spark_conf
    except Exception as e:
        print(f"Error retrieving Spark configuration: {e}")
        return default_conf

# Function to get Spark script arguments from Airflow variable
def get_spark_script_args():
    try:
        return Variable.get("spark_job_args", deserialize_json=True, default_var=[])
    except:
        return []

# Clean up the temporary directory after the DAG run
def cleanup_temp_files(**kwargs):
    try:
        # Only clean up if we created a temporary file
        is_temp_file = kwargs['ti'].xcom_pull(task_ids='prepare_spark_script', key='is_temp_file')
        if is_temp_file:
            script_path = kwargs['ti'].xcom_pull(task_ids='prepare_spark_script', key='spark_script_path')
            if script_path and os.path.exists(os.path.dirname(script_path)):
                # Remove the temporary directory and its contents
                import shutil
                shutil.rmtree(os.path.dirname(script_path))
                print(f"Cleaned up temporary directory: {os.path.dirname(script_path)}")
    except Exception as e:
        print(f"Error during cleanup: {e}")

with dag:
    # Task to prepare the Spark script (either from path or code)
    prepare_script_task = PythonOperator(
        task_id='prepare_spark_script',
        python_callable=prepare_spark_script,
        provide_context=True,
    )
    
    # Task to run the Spark job using the script
    spark_task = SparkSubmitOperator(
        task_id='run_spark_job',
        name='run-spark-job',
        execution_timeout=Timeout.FOUR_HOURS,  # Adjust as needed
        application="{{ ti.xcom_pull(task_ids='prepare_spark_script', key='spark_script_path') }}",
        application_args=get_spark_script_args(),
        conn_id="spark_default",  # Use your Spark connection ID
        conf=get_spark_conf(),
        verbose=True,
    )
    
    # Task to clean up temporary files
    cleanup_task = PythonOperator(
        task_id='cleanup_temp_files',
        python_callable=cleanup_temp_files,
        provide_context=True,
        trigger_rule='all_done',  # Ensure this runs even if previous tasks fail
    )
    
    # Define task dependencies
    prepare_script_task >> spark_task >> cleanup_task
