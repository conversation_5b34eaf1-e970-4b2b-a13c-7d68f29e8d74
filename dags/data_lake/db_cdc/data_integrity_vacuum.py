from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from metadata.constants import Timeout

from google.cloud import storage

env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2023-05-03",
    "retries": 1,
}


def delete_data_integrity_fix_blobs(bucket_name):
    directory = "data_integrity_fix/"
    storage_client = storage.Client()
    bucket = storage_client.get_bucket(bucket_name)
    blobs = bucket.list_blobs(prefix=directory)
    blobs_to_delete = [blob for blob in blobs if blob.name != directory]

    print("Proceed to delete those blobs:")
    for blob in blobs_to_delete:
        print(blob.name)
    bucket.delete_blobs(blobs_to_delete)


with DAG(
    dag_id="data_integrity_vacuum",
    default_args=default_args,
    schedule_interval="0 11 15 * *",  # 7PM SGT on 15th of the month
    catchup=False,
    concurrency=1,
    tags=["data_integrity_vacuum", "data_integrity_fix", "data_lake"],
) as dag:
    PythonOperator(
        task_id='data_integrity_vacuum',
        python_callable=delete_data_integrity_fix_blobs,
        op_args=[f'nv-data-{env}-datalake'],
        execution_timeout=Timeout.FIFTEEN_MINUTES
    )
