from datetime import timed<PERSON><PERSON>
from pathlib import Path
from functools import partial

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.models.taskinstance import TaskInstance
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from common.stringcase import kebab_case
from common.utils.gcs import get_nv_data_bucket_uri
from common.airflow.notifications.chat import _gchat
from metadata.cdc_data_integrity import EXCLUDED_TABLES
from metadata.constants import Timeout

FIFTEEN_MINUTES_IN_SECONDS = 900

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
excluded_tables = EXCLUDED_TABLES[env]
included_tables = {
    schema: [table for table in tables if table not in excluded_tables.get(schema, [])]
    for schema, tables in mysql_tables.items()
}

default_args = {
    "owner": "airflow",
    "start_date": "2021-05-10",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
    "weight_rule": "upstream",
}


def list_supported_tasks(
    mysql_connections: dict,
    schema: str,
    tables: list,
) -> list:
    """
    Returns list of task_ids for tables with index with left-most prefix on created_at. CDC data integrity check only
    supports tables with the index.
    """
    task_ids = []
    if tables:
        tables_str = ", ".join([repr(t) for t in tables])
        query = f"""
            select distinct
                concat('wait_for_delta_cdc_{schema}_', `table_name`) as `supported_tasks`
            from `information_schema`.`statistics`
            where
              `column_name` = 'created_at'
              and `seq_in_index` = 1
              and `table_schema` = '{schema}'
              and `table_name` in ({tables_str})
        """
        conn_id = db.get_conn_id(schema, mysql_connections)
        mysql_hook = MySqlHook(mysql_conn_id=conn_id, schema=schema)
        task_ids = mysql_hook.get_pandas_df(query)["supported_tasks"].tolist()
    return task_ids


def create_dag(dag_id, default_args=default_args):
    dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval="0 20 * * *",  # Daily 04:00am SGT,
        catchup=False,
        concurrency=5,
        tags=["data_lake", "db_cdc", "db_cdc_data_integrity"],
    )
    globals()[dag_id] = dag
    return dag


def get_mysql_count(
    mysql_connections: dict,
    schema: str,
    table: str,
    execution_date: pendulum.DateTime,
    next_execution_date: pendulum.DateTime,
) -> int:
    count_query = f"""
        select
           count(*) as `count`
        from `{schema}`.`{table}`
        where
            `created_at` >= '{execution_date}'
            and `created_at` < '{next_execution_date}'
        """
    conn_id = db.get_conn_id(schema, mysql_connections)
    mysql_hook = MySqlHook(mysql_conn_id=conn_id, schema=schema)
    query_result = mysql_hook.get_records(count_query)
    return query_result[0]["count"]


def check_integrity_task_failed_callback(context, count_mysql_task_id, schema, table):
    ti = context.get("task_instance")
    bucket = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True).split("/")[0]
    gcs_hook = GCSHook(gcp_conn_id="google_cloud_default")
    execution_date = context['execution_date'].subtract(hours=4)

    txt_path = f"data_integrity/tmp/db_cdc/{schema}/{table}/execution_date={execution_date}/delta_count.txt"
    mysql_count = ti.xcom_pull(dag_id=context["dag"].dag_id, task_ids=count_mysql_task_id)
    delta_count = int(gcs_hook.download(object_name=txt_path, bucket_name=bucket))

    ti_failed_message = f"""
        ❌  Mismatch happens for *{schema}.{table}*
            *Execution Date*: {context.get("execution_date")}
            *Log URL*: {ti.log_url}
            *MySQL count*: {mysql_count}
            *Delta count*: {delta_count}
         """
    connection = "google_chat"
    _gchat(ti_failed_message, connection).execute(context)

def is_count_acceptable(
    env: str,
    schema: str,
    table: str,
    gcp_conn_id: str,
    count_mysql_task_id: str,
    task_instance: TaskInstance,
    execution_date: pendulum.DateTime,
    **context,
) -> None:
    """
    Retrieves Delta and MySQL row counts from upstream tasks and checks that the counts fulfills one of the following:
    - Delta row count is equal to MySQL row count.
    - Delta row count exceeds MySQL count by no more than 1% of MySQL row count. This is to account for expected
      discrepancies caused by hard deletions due to the batch processing of the CDC tasks that update Delta tables.

      For example, assuming no lag, if a row gets deleted from MySQL at 10:15am and we run the counts for MySQL and
      Delta right after, the MySQL count will already reflect the deletion but the Delta count will only reflect the
      deletion after the 10:30am batch job finishes, leading to a difference in the counts.
    """
    bucket = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True).split("/")[0]
    txt_path = f"data_integrity/tmp/db_cdc/{schema}/{table}/execution_date={execution_date}/delta_count.txt"
    gcs_hook = GCSHook(gcp_conn_id=gcp_conn_id)
    delta_count = int(gcs_hook.download(object_name=txt_path, bucket_name=bucket))

    mysql_count = task_instance.xcom_pull(dag_id=context["dag"].dag_id, task_ids=count_mysql_task_id)

    delta_rows_excess = delta_count - mysql_count
    delta_rows_excess_cap = mysql_count * 0.01
    if not 0 <= delta_rows_excess <= delta_rows_excess_cap:
        raise Exception(f"Row Count Mismatch! \nMySQL Row Count: {mysql_count} \nDelta Row Count: {delta_count}")


for schema, tables in included_tables.items():
    dag_id = f"datalake_db_cdc_data_integrity_{schema}"
    db_dag = create_dag(dag_id=dag_id)
    with db_dag as dag:
        get_supported_tables_task = BranchPythonOperator(
            task_id=f"get_supported_tables_{schema}",
            python_callable=list_supported_tasks,
            op_kwargs={"mysql_connections": mysql_connections, "schema": schema, "tables": tables},
        )

        for table in tables:
            wait_for_delta_cdc_task = ExternalTaskSensor(
                task_id=f"wait_for_delta_cdc_{schema}_{table}",
                external_dag_id=f"datalake_cdc_{schema}",
                external_task_id=f"delta_tasks_{schema}_{table}.delta_{schema}_{table}",
                # Wait for the CDC run scheduled at next_execution_date instead of next_execution_date - 30min to
                # give a 30min buffer for delays.
                execution_date_fn=lambda _, **context: context["next_execution_date"],
                allowed_states=["success", "skipped"],
                poke_interval=FIFTEEN_MINUTES_IN_SECONDS,
                mode="reschedule",
            )

            count_mysql_task_id = f"count_mysql_{schema}_{table}"
            count_mysql_task = PythonOperator(
                task_id=count_mysql_task_id,
                python_callable=get_mysql_count,
                op_kwargs={
                    "mysql_connections": mysql_connections,
                    "schema": schema,
                    "table": table,
                    "execution_date": "{{ execution_date.subtract(hours=4) }}",
                    "next_execution_date": "{{ next_execution_date.subtract(hours=4) }}",
                },
                execution_timeout=Timeout.TWO_MINUTES,
            )

            count_delta_task_id = f"count_delta_{schema}_{table}"
            count_delta_task = SparkSubmitOperator(
                task_id=count_delta_task_id,
                name=kebab_case(count_delta_task_id),
                application=f"{tasks_path}/count_delta.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ execution_date.subtract(hours=4) }}",
                    "{{ next_execution_date.subtract(hours=4) }}",
                ],
                conn_id="spark_default",
                conf={
                    "spark.executor.instances": "2",
                    "spark.executor.memory": "17000m",
                    "spark.executor.cores": "4",
                    "spark.driver.memory": "8500m",
                    "spark.driver.cores": "2",
                },
                execution_timeout=Timeout.ONE_HOUR,
            )

            partial_check_integrity_task_failed_callback = partial(
                check_integrity_task_failed_callback,
                count_mysql_task_id=count_mysql_task_id,
                schema=schema,
                table=table
            )
            check_integrity_task = PythonOperator(
                task_id=f"check_integrity_{schema}_{table}",
                python_callable=is_count_acceptable,
                on_failure_callback=partial_check_integrity_task_failed_callback if env == "prod" else None,
                op_kwargs={
                    "env": env,
                    "schema": schema,
                    "table": table,
                    "gcp_conn_id": "google_cloud_default",
                    "count_mysql_task_id": count_mysql_task_id,
                    "execution_date": "{{ execution_date.subtract(hours=4) }}",
                },
            )

            (
                get_supported_tables_task
                >> wait_for_delta_cdc_task
                >> [
                    count_mysql_task,
                    count_delta_task,
                ]
                >> check_integrity_task
            )
