from datetime import datetime, <PERSON><PERSON><PERSON>
from functools import partial
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.operators.bash import Ba<PERSON>Operator
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from common.stringcase import kebab_case
from common.list import merge_dictionaries
from common.utils.nv_obs import get_nv_data_bucket_uri
from metadata.compaction import TABLES as COMPACTION_TABLES
from metadata.data_count_sanity import DATA_COUNT_TABLES
from metadata.constants import Timeout
from metadata.metabase import TABLES as METABASE_TABLES
from metadata.pii import SCHEMAS
from metadata.spark_conf import SPARK_CONF, SPARK_DRIVER_CONF
from metadata.task_sizes import CDC_TASK_SIZES

ALERT_CHANNEL = "high_priority_alert"

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")
interval_duration = 60
num_intervals = interval_duration // 15
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
spark_conf = SPARK_CONF[env]["cdc"]
pii_tables = Variable.get("pii_fields", deserialize_json=True)
critical_tables = [table for table_dict in DATA_COUNT_TABLES.values() for table in table_dict]

compaction_schemas = merge_dictionaries(COMPACTION_TABLES[env], METABASE_TABLES[env]).keys()
pii_schemas = SCHEMAS[env]
default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 9, 15, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if env in ("prod", "dev")
    else None,
    "pool": "cdc_pool",
}


def create_dag(dag_id, schedule_interval=f"*/{interval_duration} * * * *", default_args=default_args):
    dag = DAG(
        dag_id=dag_id,
        schedule_interval=schedule_interval,
        default_args=default_args,
        max_active_runs=1,
        concurrency=16,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        params={"alert_channel": ALERT_CHANNEL},
        tags=["data_lake", "db_cdc", "db_cdc_delta"],
        dagrun_timeout=timedelta(hours=2),
    )
    globals()[dag_id] = dag
    return dag


def is_table_supported_for_cdc(schema, table):
    from common.airflow import db
    import logging
    from common.airflow import pk

    """
    Information_schema columns index mapping
    """
    _COLUMN_KEY = 16
    _COLUMN_NAME = 3
    _DATA_TYPE = 7
    try:
        fields = db.get_column_info(schema, table, mysql_connections)
    except Exception:
        logging.info(f"Error when trying to describe table: {schema}.{table}")
        return False
    logging.info(f"Fields pulled: {fields}")
    primary_keys = []
    has_created_at = False
    for f in fields:
        if "COLUMN_NAME" in f:
            if f["COLUMN_KEY"] == "PRI":
                primary_keys.append(f["COLUMN_NAME"])
            elif f["COLUMN_NAME"] == "created_at" and f["DATA_TYPE"] in ("timestamp", "datetime"):
                has_created_at = True
        else:
            if f[_COLUMN_KEY] == "PRI":
                primary_keys.append(f[_COLUMN_NAME])
            if f[_COLUMN_NAME] == "created_at" and f[_DATA_TYPE] in ("timestamp", "datetime"):
                has_created_at = True

    if not (primary_keys and has_created_at):
        return False

    pk.manage_primary_keys_variable(schema, table, primary_keys)
    return True


def list_table_tasks(schema, **context):
    import logging
    from airflow.models import Variable

    tables_with_changes = []
    task_ids = f"{schema}.list_tables_with_changes"
    change_list = context["ti"].xcom_pull(dag_id=f"datalake_cdc_{schema}_legacy", task_ids=task_ids)
    logging.info(
        f"""List of DB tables with changes from datalake_cdc_sensor: {change_list},
                pulled from :{{task_ids}}"""
    )

    # mysql_tables has schema to table mapping schema as key and tables from schema as values
    for table in change_list:
        if table not in mysql_tables[schema]:
            logging.info(f"Table {schema}.{table} is not in mysql_tables")
            if not is_table_supported_for_cdc(schema, table):
                logging.info(f"Table {schema}.{table} is not supported for cdc")
                continue
            logging.info(f"Table {schema}.{table} is supported for cdc. Adding it to mysql_tables")
            if schema not in mysql_tables:
                mysql_tables[schema] = []
            mysql_tables[schema].append(table)
            Variable.set("mysql_tables", mysql_tables, serialize_json=True)
        tables_with_changes.append(f"process_cdc_messages_{schema}_{table}")
    return tables_with_changes


# Mapping from Cron (0=Sunday) to Pendulum (0=Monday)
cron_to_pendulum_mapping = {
    0: 6,  # Sunday to 6
    1: 0,  # Monday to 0
    2: 1,  # Tuesday to 1
    3: 2,  # Wednesday to 2
    4: 3,  # Thursday to 3
    5: 4,  # Friday to 4
    6: 5,  # Saturday to 5
}


def get_last_compaction_date(schema_name: str, execution_date: pendulum.DateTime):
    from common.date import get_schema_day

    schema_day = get_schema_day(schema_name)
    dt = execution_date.subtract(weeks=1).add(minutes=interval_duration)
    return (
        dt.start_of("day")
        if dt.day_of_week == cron_to_pendulum_mapping.get(schema_day)
        else dt.previous(cron_to_pendulum_mapping.get(schema_day))
    )


def get_last_scheduled_execution_date(execution_date):
    """Allows sensor to find the last run even though DAG is triggered out of schedule, such as when running E2E tests,
    e.g., sensor with execution_date = '2020-01-01T11:00:00.500' will find '2020-01-01T10:00:00.000' run
    """
    return execution_date.replace(minute=0, second=0, microsecond=0).subtract(minutes=interval_duration)


def check_and_extract_delta_metrics(env, schema, table, delta_task_id, is_pii, **context):
    import json
    import logging
    import pendulum

    from airflow.api.common.experimental import get_task_instance

    from common.utils.nv_obs import get_nv_data_bucket_uri, get_obs_client, fetch_delta_checkpoint, fetch_latest_object

    """Checks if the upstream Delta merge task successfully committed a new Delta version."""
    client = get_obs_client()
    obs_db_path = get_nv_data_bucket_uri(env, bucket_type="db", schema="legacy", strip=True)
    if is_pii:
        obs_db_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True)

    obs_bucket, base_path = obs_db_path.split("/")
    prefix = f"{base_path}/{schema}/{table}/_delta_log"
    last_checkpoint_prefix = f"{base_path}/{schema}/{table}/_delta_log/_last_checkpoint"
    logging.info(f"Checking in '{obs_bucket}/{prefix}'")

    last_checkpoint_parquet = fetch_delta_checkpoint(client, obs_bucket, prefix=last_checkpoint_prefix)
    logging.info(f"Most recent .checkpoint.parquet file is {last_checkpoint_parquet}.")
    last_checkpoint_marker = f"{prefix}/{last_checkpoint_parquet}" if last_checkpoint_parquet else None
    logging.info(f"{last_checkpoint_marker}")

    latest_commit_metadata = fetch_latest_object(client, obs_bucket, prefix, marker=last_checkpoint_marker)

    latest_delta_log_prefix = latest_commit_metadata["key"]
    logging.info(f"Last delta log: {latest_delta_log_prefix}")
    last_delta_log_update = pendulum.from_format(
        latest_commit_metadata["lastModified"], "YYYY/MM/DD HH:mm:ss", tz="UTC"
    )

    delta_ti = get_task_instance.get_task_instance(context["dag"].dag_id, delta_task_id, context["logical_date"])
    delta_start_date = pendulum.instance(delta_ti.start_date).in_tz("UTC")

    # Raise Airflow specific exception.
    if last_delta_log_update < delta_start_date:
        raise Exception("Delta merge not found.")

    response = client.getObject(obs_bucket, latest_delta_log_prefix, loadStreamInMemory=True)
    logging.debug(response)
    decoded_string = response.body.buffer.decode("utf-8")
    latest_log_data = [json.loads(line) for line in decoded_string.strip().split("\n") if line.strip()]
    logging.info(latest_log_data)
    latest_version_id = latest_delta_log_prefix.split("/")[-1].split(".")[0]
    latest_commit = next((entry for entry in latest_log_data if "commitInfo" in entry), None)

    if latest_commit and "commitInfo" in latest_commit:
        operation_metrics = latest_commit["commitInfo"].get("operationMetrics", {})
    else:
        logging.error(f"No commitInfo found in the latest log: {latest_delta_log_prefix}")
        operation_metrics = {}

    metrics_dict = {
        "version": latest_version_id,
        "numTargetRowsCopied": int(operation_metrics.get("numTargetRowsCopied", 0)),
        "numTargetRowsDeleted": int(operation_metrics.get("numTargetRowsDeleted", 0)),
        "numTargetFilesAdded": int(operation_metrics.get("numTargetFilesAdded", 0)),
        "numTargetRowsInserted": int(operation_metrics.get("numTargetRowsInserted", 0)),
        "numTargetRowsUpdated": int(operation_metrics.get("numTargetRowsUpdated", 0)),
        "numSourceRows": int(operation_metrics.get("numSourceRows", 0)),
        "numOutputRows": int(operation_metrics.get("numOutputRows", 0)),
        "numTargetFilesRemoved": int(operation_metrics.get("numTargetFilesRemoved", 0)),
        "schema": schema,
        "table": table,
        "execution_date": str(context["logical_date"]),
    }
    context["ti"].xcom_push(key=f"delta_metrics_{schema}_{table}", value=metrics_dict)
    return metrics_dict


def trigger_raw_to_delta_comparison_task(schema, table, **kwargs):
    return TriggerDagRunOperator(
        task_id=f"trigger_raw_to_delta_comparison_{schema}_{table}",
        wait_for_completion=False,
        trigger_dag_id="raw_to_delta_sanity_dag",
        conf={"schema": schema, "table": table, "execution_date": "{{ logical_date }}"},
    )


def create_delta_task(env, delta_task_id, schema, table, is_pii=False):
    delta_default_airflow_var = ["id"]
    delta_task = SparkSubmitOperator(
        task_id=delta_task_id,
        name=kebab_case(delta_task_id),
        execution_timeout=Timeout.TWO_HOURS,
        application=f"{tasks_path}/update_delta.py",
        application_args=[
            env,
            schema,
            table,
            "{{ ts }}",
            f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
            f"{{{{ ti.xcom_pull(dag_id='datalake_cdc_{schema}_legacy', task_ids='get_{schema}_conn') }}}}",
            f"{is_pii}",
        ],
        conn_id="spark_default",
        conf={
            **(spark_conf[CDC_TASK_SIZES.get(delta_task_id, "medium")]),
            **SPARK_DRIVER_CONF,
            "spark.databricks.delta.schema.autoMerge.enabled": "true",
        },
    )
    return delta_task


def clear_delta_callback(delta_task, context):
    # Temporarily disable alerts when clear delta task fails.
    # notif.chat.send_ti_failure_alert(context)
    delta_task.clear(start_date=context["execution_date"], end_date=context["execution_date"], downstream=True)


def list_changes(bucket_name, schema, path, execution_date, **kwargs):
    from common.utils.nv_obs import get_obs_client
    from datetime import timedelta
    import logging

    print(bucket_name, schema, path, execution_date, kwargs)

    """Creates a list of tables with changes per DB."""
    client = get_obs_client()
    tables_with_changes = set()
    delimiter = "/part"

    for i in range(num_intervals):
        ts = execution_date + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={ts:%Y-%m-%d}/time={ts:%H-%M-%S}/database={schema}"
        logging.info(f"Listing changes in '{bucket_name}/{prefix}'")
        response = client.listObjects(bucketName=bucket_name, prefix=prefix, delimiter=delimiter)
        body = response["body"]

        if response["status"] == 200 and body and "commonPrefixs" in body:
            common_prefixes = body["commonPrefixs"]
            for common_prefix in common_prefixes:
                sub_path = common_prefix["prefix"]
                table = sub_path.replace(f"{prefix}/table=", "").replace(delimiter, "")
                tables_with_changes.add(table)

    return list(tables_with_changes)


def _get_path(schema):
    from metadata.ticdc import TICDC_SCHEMAS

    """
    Check CDC message stream based on connection and schema
    """
    # Combine schemas from all TiCDC instances into a single list.
    schemas = sum(TICDC_SCHEMAS.get(env).values(), [])
    return "ticdc_stream" if schema in schemas else "cdc_stream"


for schema, tables in mysql_tables.items():
    dag_id = f"datalake_cdc_{schema}_legacy"
    db_dag = create_dag(dag_id=dag_id)
    is_pii = False

    if schema in pii_schemas:
        is_pii = True

    gcs_sensors = {}
    with db_dag as dag:
        # Note: this is needed because while it seems that `wait_for_downstream` & `max_active_dag_runs` can guarantee
        #  the sequential execution, when we have a new table (appears as a new task), the `depends_on_past` check
        #  prevents the new task from running. This task is similar to having `wait_for_downstream`, but on the
        #  DAG level
        connection = next((conn for conn, schemas in mysql_connections.items() if schema in schemas), None)
        obs_bucket_raw = get_nv_data_bucket_uri(env=env, bucket_type="raw", schema=connection, strip=True)
        database = schema

        if connection == "ninjamart":
            _database = database.replace("ninjamart_", "")
        else:
            _database = database

        # task_id = f"{database}.check_next_partition"
        # if database not in gcs_sensors:
        #     gcs_sensors[database] = GCSObjectsWithPrefixExistenceSensor(
        #         task_id=task_id,
        #         bucket=gs_bucket_raw,
        #         prefix=f"{_get_path(connection, database)}/data/date={{{{ next_ds }}}}/"
        #         + f"time={{{{ next_execution_date.strftime('%H-%M-%S') }}}}/database={_database}",
        #         execution_timeout=Timeout.FIFTEEN_MINUTES,
        #         on_failure_callback=None,
        #         retries=1,
        #         pool="sensor_pool",
        #     )

        # removing check next parition in favor of wait time
        # introducing dynamic wait time, in case of recovery we can adjust this from airflow ui instead of deployment
        wait_time_dynamic = Variable.get("cdc_wait_time")
        no_wait_time_schema = Variable.get("no_wait_time_schema", deserialize_json=True, default_var=[])
        wait_task = BashOperator(
            task_id=f"cdc_wait",
            bash_command=f"sleep {wait_time_dynamic}",
            trigger_rule="none_skipped",
        )

        list_tables_from_obs_bucket_raw = PythonOperator(
            task_id=f"{schema}.list_tables_with_changes",
            python_callable=list_changes,
            op_kwargs={
                "bucket_name": obs_bucket_raw,
                "schema": schema,
                "path": _get_path(schema),
            },
            pool="sensor_pool",
        )
        # gcs_sensors[database] >> wait_task >> list_tables_from_gs_bucket_raw
        # wait_task >> list_tables_from_gs_bucket_raw

        wait_previous_end = ExternalTaskSensor(
            task_id=f"wait_previous_end_{schema}",
            external_dag_id=f"datalake_cdc_{schema}_legacy",
            external_task_id=None,
            execution_date_fn=get_last_scheduled_execution_date,
            allowed_states=["success", "failed"],
            pool="sensor_pool",
            timeout=900,
        )
        # wait_previous_end >> gcs_sensors[database]
        if schema not in no_wait_time_schema:
            wait_task >> list_tables_from_obs_bucket_raw
            wait_previous_end >> wait_task
        else:
            wait_previous_end >> list_tables_from_obs_bucket_raw

        if schema in compaction_schemas:
            wait_compaction_end = ExternalTaskSensor(
                task_id="wait_compaction_end",
                external_dag_id=f"datalake_compact_{schema}",
                external_task_id="end",
                execution_date_fn=partial(get_last_compaction_date, schema),
                allowed_states=["success", "upstream_failed", "failed"],
                mode="reschedule",
                poke_interval=300,
                pool="sensor_pool",
                timeout=900,
            )
            wait_compaction_end >> wait_previous_end

        if not schema.startswith("ninjamart"):
            get_conn = PythonOperator(
                task_id=f"get_{schema}_conn", python_callable=db.get_schema_conn, op_args=[schema, mysql_connections]
            )

        list_tables_to_process = BranchPythonOperator(
            task_id=f"get_tables_with_changes_{schema}", python_callable=list_table_tasks, op_args=[schema]
        )

        for table in tables:
            process_cdc_messages_default_var = {}
            process_cdc_messages_default_list = []
            process_cdc_task_id = f"process_cdc_messages_{schema}_{table}"
            process_cdc = SparkSubmitOperator(
                task_id=process_cdc_task_id,
                name=kebab_case(process_cdc_task_id),
                execution_timeout=Timeout.FORTY_FIVE_MINUTES,
                application=f"{tasks_path}/post_process_cdc.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ ts }}",
                    str(num_intervals),
                    f"""{{{{
                        var.json.pii_fields |
                        extract('{schema}', {process_cdc_messages_default_var}) |
                        extract('{table}', {process_cdc_messages_default_list})
                    }}}}""",
                ],
                conn_id="spark_default",
                conf={
                    **SPARK_DRIVER_CONF,
                    **(spark_conf[CDC_TASK_SIZES.get(process_cdc_task_id, "small")]),
                },
            )

            delta_task_id = f"delta_{schema}_{table}"
            pii_delta_task_id = f"pii_delta_{schema}_{table}"

            if is_pii:
                delta_task = create_delta_task(env, pii_delta_task_id, schema, table, is_pii)
            else:
                delta_task = create_delta_task(env, delta_task_id, schema, table)

            if not schema.startswith("ninjamart"):
                if is_pii:
                    check_and_extract_delta_task = PythonOperator(
                        task_id=f"check_and_extract_delta_pii_{schema}_{table}",
                        python_callable=check_and_extract_delta_metrics,
                        op_args=[env, schema, table, f"{pii_delta_task_id}", is_pii],
                        on_failure_callback=partial(clear_delta_callback, delta_task),
                        pool="sensor_pool",
                    )
                else:
                    check_and_extract_delta_task = PythonOperator(
                        task_id=f"check_and_extract_delta_{schema}_{table}",
                        python_callable=check_and_extract_delta_metrics,
                        op_args=[env, schema, table, f"{delta_task_id}", False],
                        on_failure_callback=partial(clear_delta_callback, delta_task),
                        pool="sensor_pool",
                    )

                list_tables_to_process >> process_cdc >> delta_task >> check_and_extract_delta_task
            elif schema.startswith("ninjamart"):
                list_tables_to_process >> process_cdc >> delta_task

            if table in critical_tables:
                trigger_task = trigger_raw_to_delta_comparison_task(schema, table)
                check_and_extract_delta_task >> trigger_task

        if schema.startswith("ninjamart"):
            list_tables_from_obs_bucket_raw >> list_tables_to_process
        else:
            list_tables_from_obs_bucket_raw >> [get_conn] >> list_tables_to_process