import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from config.config import Config, JobType
from airflow.decorators import dag
from airflow.operators.empty import EmptyOperator
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from custom_operators.spark_k8s import SparkK8s
from common.spark import spark_app_utils
from common.utils import helper
from common.airflow import notifications
from common.date import get_schema_day
from common.list import merge_dictionaries
from metadata.compaction import TABLES as COMPACTION_TABLES
from metadata.constants import Timeout
from metadata.metabase import TABLES as METABASE_TABLES
from metadata.pii import SCHEMAS
from metadata.spark_conf import SPARK_CONF
from metadata.table_sizes import TABLE_SIZES

env = Variable.get("env")
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
table_sizes_airflow= Variable.get("table_sizes", deserialize_json=True)["compaction"]
env_tables = merge_dictionaries(COMPACTION_TABLES[env], METABASE_TABLES[env])
pii_schemas = SCHEMAS[env]

large_tables = TABLE_SIZES[env]["compaction"]["large"]
spark_conf = SPARK_CONF[env]["compaction"]

default_args = {
    "owner": "airflow",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notifications.chat.send_ti_failure_alert if env == "prod" else None,
    "pool": "cdc_pool",
}


def create_compaction_task(env, schema, table, conf, is_higher_priority=False):

    config_compact = helper.build_config(
                schema=schema, table=table, env=Config.ENV,
                image=f"{Config.IMAGE_NAME}:{Config.TAG}",
                task_type="compaction",
                group=JobType.COMPACTION.value,
                table_sizes_airflow=table_sizes_airflow
            )
    config_compact['table_sizes_airflow'] = table_sizes_airflow
    config_compact['spark_config'] = conf
    application_file_compact = spark_app_utils.generate_application_file_for_task("compaction_delta", Config.TASKS_PATH, schema, table, config_compact)
    task_id = f"pii_compact_delta_{schema}_{table}"
    compact_delta = SparkK8s(
        task_id=task_id,
        namespace=os.environ["AIRFLOW__KUBERNETES_EXECUTOR__NAMESPACE"],
        application_file=application_file_compact,
        kubernetes_conn_id="kubernetes_default",
        arguments=[
            Config.ENV,
            schema,
            table,
            json.dumps(conf, separators=(",", ":"))
        ],
        startup_timeout_seconds=1800,
        in_cluster=True,
        reattach_on_restart=True,
        get_logs=True,
        log_events_on_failure=True,
        delete_on_termination=True,
        pool="cdc_pool"
    )
    return compact_delta


for schema, tables in env_tables.items():
    dag_id = f"datalake_compact_{schema}"
    schema_day = get_schema_day(schema)
    # Weekly at midnight UTC on the schema's scheduled day.
    schema_interval = f"0 0 * * {schema_day}"

    @dag(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval=schema_interval,
        start_date=datetime(2022, 10, 15),
        catchup=False,
        tags=["data_lake", "compaction"],
    )
    def schema_dag():
        end = EmptyOperator(task_id="end")

        for table in tables:
            is_higher_priority = f"{schema}.{table}" in large_tables
            conf = spark_conf["large"] if is_higher_priority else spark_conf["medium"]
            compact_delta = create_compaction_task(env, schema, table, conf, is_higher_priority)
            compact_delta >> end

    globals()[dag_id] = schema_dag()