import logging
import sys

from google.cloud import storage
from pyspark.sql import SparkSession

from common.date import date_from_ts
from common.utils.gcs import get_nv_data_bucket_uri


def _get_count(spark: SparkSession, delta_path: str, start_timestamp: str, end_timestamp: str) -> int:
    start_year_month = date_from_ts(start_timestamp, date_fmt="%Y-%m")
    end_year_month = date_from_ts(end_timestamp, date_fmt="%Y-%m")
    date_range_filter = (
        f"created_month >= '{start_year_month}' and created_month <= '{end_year_month}' "
        f"and created_at >= '{start_timestamp}' and created_at < '{end_timestamp}'"
    )
    logging.info(f"Counting rows for '{date_range_filter}'")

    delta_df = spark.read.format("delta").load(delta_path)
    count = delta_df.filter(date_range_filter).count()
    logging.info(f"Total rows: {count}")
    return count


def _upload_count_to_gcs(bucket: str, txt_path: str, count: int) -> None:
    client = storage.Client()
    bucket = client.bucket(bucket)
    blob = bucket.blob(txt_path)
    blob.upload_from_string(str(count))


def run(
    spark: SparkSession,
    env: str,
    schema: str,
    table: str,
    execution_date: str,
    next_execution_date: str,
) -> None:
    gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True)
    bucket = gs_bucket_path_db.split("/")[0]

    delta_path = f"gs://{gs_bucket_path_db}/{schema}/{table}"
    count = _get_count(spark, delta_path, execution_date, next_execution_date)

    txt_path = f"data_integrity/tmp/db_cdc/{schema}/{table}/execution_date={execution_date}/delta_count.txt"
    _upload_count_to_gcs(bucket, txt_path, count)


if __name__ == "__main__":
    env, schema, table, execution_date, next_execution_date = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, env, schema, table, execution_date, next_execution_date)
    spark.stop()
