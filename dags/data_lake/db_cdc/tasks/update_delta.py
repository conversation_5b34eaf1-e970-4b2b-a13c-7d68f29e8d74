import ast
import json
import os
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession

from common import date as date_util
from common import db
from common.spark import cdc, util
from common.utils.nv_obs import get_nv_data_bucket_uri

PARTITION_COLUMN = "created_month"


def get_cdc(spark, bucket, schema, table, date, time, primary_keys):
    df_cdc = spark.read.parquet(f"{bucket}/cdc_processed/{schema}/{table}/cdc_date={date}/cdc_time={time}")
    cdc_latest = cdc.get_latest_records(df_cdc, primary_keys)
    return cdc_latest


def has_new_cols(delta_df, cdc_df):
    """Return new columns that are present in cdc_df"""
    excluded_cols = {"nv_data_old"}
    diff = set(cdc_df.columns) - set(delta_df.columns)
    return bool(diff - excluded_cols)


def update_delta(spark, bucket_raw, bucket_path_db, schema, table, date, time, primary_keys, conn_dict):
    delta_table = DeltaTable.forPath(spark, f"{bucket_path_db}/{schema}/{table}")
    cdc_latest = get_cdc(spark, bucket_raw, schema, table, date, time, primary_keys)
    delta_df = delta_table.toDF()
    
    # Special handling for core-prod-id-orders table which has been failing
    is_problem_table = (schema == "core_prod_id" and table == "orders") or \
                       (schema == "pricing_prod_gl" and table == "pricing_orders")
    
    if is_problem_table:
        print(f"Special handling for {schema}.{table} which has been problematic")
        # Set up checkpoint directory for reliable shuffle storage
        checkpoint_dir = f"{bucket_path_db}/checkpoint/{schema}/{table}"
        print(f"Setting checkpoint directory to {checkpoint_dir}")
        spark.sparkContext.setCheckpointDir(checkpoint_dir)
        
        # Cache the CDC data to improve performance
        cdc_latest.cache()
        cdc_count = cdc_latest.count()
        print(f"Processing {cdc_count} CDC records for {schema}.{table}")
    
    if has_new_cols(delta_df, cdc_latest) and conn_dict is not None:
        print("New columns detected. Proceed to get the dtype from jdbc connection to the DB")
        jdbc_dtypes = _get_jdbc_dtypes(conn_dict)

        # Note: new columns will be casted accordingly, but also columns with updated type as a side-effect.
        # But, that should cause no trouble to us.
        cdc_latest = util.cast_df(cdc_latest, jdbc_dtypes)

    partitions = _find_partitions_with_changes(cdc_latest)
    delta_merge_condition = _generate_merge_condition(primary_keys, partitions)
    
    if is_problem_table:
        try:
            # Set optimized Delta merge parameters
            spark.conf.set("spark.databricks.delta.merge.repartitionBeforeWrite.enabled", "true")
            spark.conf.set("spark.databricks.delta.merge.optimizeWrite.enabled", "true")
            
            # Process in smaller batches if there are many records
            if cdc_count > 10000:
                print(f"Processing {schema}.{table} in batches due to large CDC count: {cdc_count}")
                # Get distinct partition values
                partition_values = [row[PARTITION_COLUMN] for row in cdc_latest.select(PARTITION_COLUMN).distinct().collect()]
                
                for partition_value in partition_values:
                    print(f"Processing partition {partition_value}")
                    # Filter CDC data for this partition
                    partition_cdc = cdc_latest.filter(f"{PARTITION_COLUMN} = '{partition_value}'")
                    partition_count = partition_cdc.count()
                    
                    if partition_count > 0:
                        print(f"Merging {partition_count} records for partition {partition_value}")
                        # Generate merge condition for this partition only
                        partition_condition = f"delta.{PARTITION_COLUMN} = '{partition_value}' AND {' AND '.join([f'delta.{key} = cdc.{key}' for key in primary_keys])}"
                        
                        (
                            delta_table.alias("delta")
                            .merge(partition_cdc.alias("cdc"), partition_condition)
                            .whenMatchedDelete(condition="cdc.nv_data_change_type = 'delete'")
                            .whenMatchedUpdateAll(
                                condition="""
                                    cdc.nv_data_ts > coalesce(delta.nv_data_ts, 0)
                                    OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid > delta.nv_data_xid)
                                    OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid = delta.nv_data_xid
                                        AND (cdc.nv_data_xoffset > delta.nv_data_xoffset OR cdc.nv_data_xoffset IS NULL))
                                    """
                            )
                            .whenNotMatchedInsertAll(condition="cdc.nv_data_change_type <> 'delete'")
                            .execute()
                        )
                print(f"Successfully processed all partitions for {schema}.{table}")
            else:
                # Regular merge for smaller CDC sets
                (
                    delta_table.alias("delta")
                    .merge(cdc_latest.alias("cdc"), delta_merge_condition)
                    .whenMatchedDelete(condition="cdc.nv_data_change_type = 'delete'")
                    .whenMatchedUpdateAll(
                        condition="""
                            cdc.nv_data_ts > coalesce(delta.nv_data_ts, 0)
                            OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid > delta.nv_data_xid)
                            OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid = delta.nv_data_xid
                                AND (cdc.nv_data_xoffset > delta.nv_data_xoffset OR cdc.nv_data_xoffset IS NULL))
                            """
                    )
                    .whenNotMatchedInsertAll(condition="cdc.nv_data_change_type <> 'delete'")
                    .execute()
                )
            # Uncache the data after processing
            cdc_latest.unpersist()
        except Exception as e:
            print(f"Error during merge for {schema}.{table}: {str(e)}")
            # Uncache on error too
            cdc_latest.unpersist()
            raise
    else:
        # Regular merge for other tables
        (
            delta_table.alias("delta")
            # source = cdc, merging source to target
            .merge(cdc_latest.alias("cdc"), delta_merge_condition)
            .whenMatchedDelete(condition="cdc.nv_data_change_type = 'delete'")
            .whenMatchedUpdateAll(
                condition="""
                    cdc.nv_data_ts > coalesce(delta.nv_data_ts, 0)
                    OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid > delta.nv_data_xid)
                    OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid = delta.nv_data_xid
                        AND (cdc.nv_data_xoffset > delta.nv_data_xoffset OR cdc.nv_data_xoffset IS NULL))
                    """
            )
            .whenNotMatchedInsertAll(condition="cdc.nv_data_change_type <> 'delete'")
            .execute()
        )


def create_delta(spark, bucket_raw, bucket_path_db, schema, table, date, time, primary_keys, conn_dict):
    cdc_latest = get_cdc(spark, bucket_raw, schema, table, date, time, primary_keys)

    if conn_dict is not None:
        jdbc_dtypes = _get_jdbc_dtypes(conn_dict)
        cdc_latest = util.cast_df(cdc_latest, jdbc_dtypes)

    (
        cdc_latest.repartition(10, PARTITION_COLUMN)
        .write.mode("overwrite")
        .format("delta")
        .partitionBy(PARTITION_COLUMN)
        .save(f"{bucket_path_db}/{schema}/{table}")
    )


def _get_jdbc_dtypes(conn_dict):
    jdbc_url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)
    jdbc_df = spark.read.jdbc(url=jdbc_url, table=table, properties=conn_props)
    # Cast boolean columns to bigint as process_cdc_messages task infers these columns as int/string type,
    # which are implicitly incompatible with boolean type, causing Delta merge to fail.
    return [(column, "bigint" if dtype == "boolean" else dtype) for column, dtype in jdbc_df.dtypes]


def _find_partitions_with_changes(cdc_latest):
    partitions_list = [f"'{i[PARTITION_COLUMN]}'" for i in cdc_latest.select(PARTITION_COLUMN).distinct().collect()]
    partitions = ", ".join(partitions_list)
    return partitions


def _generate_merge_condition(primary_keys, partitions):
    join_keys = " and ".join([f"delta.{key} = cdc.{key}" for key in primary_keys])
    partition_pruning = f"delta.{PARTITION_COLUMN} in ({partitions})"
    merge_condition = f"{join_keys} and {partition_pruning}"
    return merge_condition


if __name__ == "__main__":
    (env, schema, table, ts, primary_keys_str, conn_dict_str, is_pii) = sys.argv[1:]

    if isinstance(is_pii, str):
        is_pii = ast.literal_eval(is_pii)

    primary_keys = json.loads(primary_keys_str.replace("'", '"'))

    if conn_dict_str != "None":
        conn_dict = json.loads(conn_dict_str.replace("'", '"'))
        conn_dict["user"] = os.environ.get("NV_DB_USER")
        conn_dict["password"] = os.environ.get("NV_DB_PASSWORD")
    else:
        conn_dict = None

    date = date_util.date_from_ts(ts)
    time = date_util.time_from_ts(ts)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")

    bucket_input = get_nv_data_bucket_uri(env, bucket_type="processed", schema=schema, strip=False)
    bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)

    if DeltaTable.isDeltaTable(spark, f"{bucket_path_db}/{schema}/{table}"):
        print(f"Delta table for {schema}.{table} exists. Update it.")
        update_delta(spark, bucket_input, bucket_path_db, schema, table, date, time, primary_keys, conn_dict)
    else:
        print(f"{schema}.{table} is new. Create a Delta table for it.")
        create_delta(spark, bucket_input, bucket_path_db, schema, table, date, time, primary_keys, conn_dict)

    spark.stop()