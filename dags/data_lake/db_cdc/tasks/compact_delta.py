import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession

from common.utils.nv_obs import get_nv_data_bucket_uri

PARTITION_COLUMN = "created_month"


def compact_delta_table(spark, obs_path, schema, table):
    """
    Compacts a Delta table by repartitioning it to a smaller number of files.

    :param spark:       Spark session
    :param path:        Data lake OBS bucket path, e.g., obs://nv-data-prod-data-lake/db
    :param schema:      DB schema
    :param table:       DB table
    :return:
    """
    path = f"{obs_path}/{schema}/{table}"

    delta_table = DeltaTable.forPath(spark, path)
    df = spark.read.format("delta").load(path)
    partitions = df.select(PARTITION_COLUMN).distinct().orderBy(PARTITION_COLUMN, ascending=False).collect()

    for partition in partitions:
        partition_filter = f"{PARTITION_COLUMN} = '{partition[0]}'"
        print(f"Optimizing partition : {partition_filter}")
        delta_table.optimize().where(partition_filter).executeCompaction()

    # HMS integration might not be needed at all in HWC.
    # hive_metastore.generate_delta_manifest(spark, path)


if __name__ == "__main__":
    env, schema, table, spark_conf_str = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    obs_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)

    print(f"Run compaction for Delta table {schema}.{table} in {obs_path}")

    compact_delta_table(spark, obs_path, schema, table)
    spark.stop()