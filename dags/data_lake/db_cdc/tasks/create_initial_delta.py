import codecs
import json
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lit
from pyspark.sql.utils import AnalysisException

from common.spark import util
from common import db



def create_initial_delta_table(spark, env, schema, table, primary_keys, conn_dict):
    """
    Creates the initial delta table in the GCS data lake with Spark.

    :param spark:       Spark session
    :param env:         Data lake environment
    :param schema:      DB schema
    :param table:       DB table
    """
    bucket = f"gs://nv-data-{env}-data-lake"
    partition_column = "created_month"
    snapshot_default_values = {
        "nv_data_change_type": ("mysql_snapshot", "string"),
        "nv_data_ts": (0, "long"),
        "nv_data_xid": (0, "long"),
        "nv_data_xoffset": (0, "long"),
    }

    try:
        snapshot = spark.read.parquet(f"{bucket}/snapshots/{schema}/{table}")
    except AnalysisException as e:
        print(f"Error reading Parquet files from {f'{bucket}/snapshots/{schema}/{table}'}: {str(e)}")
        print("Exiting the script.")
        spark.stop()
        return

    if snapshot.count() == 0:
        print(f"No data in the snapshot at {f'{bucket}/snapshots/{schema}/{table}'}. Exiting the script.")
        spark.stop()
        return

    delta_table_path = f"{bucket}/db/{schema}/{table}"
    snapshot = util.add_typed_literal_columns(snapshot, snapshot_default_values)
    if DeltaTable.isDeltaTable(spark, delta_table_path):
        print(f"Delta table for {schema}.{table} exists. Merging with snapshot.")
        delta_table = DeltaTable.forPath(spark, delta_table_path)
        adjust_schema_and_merge(delta_table, snapshot, partition_column, primary_keys, conn_dict)
    else:
        snapshot = util.add_typed_literal_columns(snapshot, snapshot_default_values)
        print(f"{schema}.{table} is new. Creating a Delta table for it.")
        (snapshot.write.mode("overwrite").partitionBy(partition_column).format("delta").save(delta_table_path))


def adjust_schema_and_merge(delta_table, snapshot, partition_column, primary_keys, conn_dict):
    delta_df = delta_table.toDF()
    delta_schema = delta_table.toDF().schema
    snapshot_schema = snapshot.schema
    new_columns = [col for col in snapshot_schema.fields if col not in delta_schema.fields]
    if new_columns and conn_dict is not None:
        print("New columns detected. Proceed to get the dtype from jdbc connection to the DB")
        jdbc_dtypes = _get_jdbc_dtypes(conn_dict)
        snapshot = util.cast_df(snapshot, jdbc_dtypes)
    partitions = _find_partitions_with_changes(snapshot, partition_column)
    delta_table.alias("delta").merge(
        snapshot.alias("snapshot"),
        _generate_merge_condition(primary_keys, partition_column, partitions),
    ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()


def _find_partitions_with_changes(snapshot, partition_column):
    partitions_list = [f"'{i[partition_column]}'" for i in snapshot.select(partition_column).distinct().collect()]
    partitions = ", ".join(partitions_list)
    return partitions


def _generate_merge_condition(primary_keys, partition_column, partitions):
    join_keys = [f"delta.{key} = snapshot.{key}" for key in primary_keys]
    partition_pruning = f"delta.{partition_column} in ({partitions})"
    merge_condition = " AND ".join(join_keys + [partition_pruning])
    return merge_condition


def _get_jdbc_dtypes(conn_dict):
    jdbc_url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)
    jdbc_df = spark.read.jdbc(url=jdbc_url, table=table, properties=conn_props)
    return [(column, "bigint" if dtype == "boolean" else dtype) for column, dtype in jdbc_df.dtypes]


if __name__ == "__main__":
    env, schema, table, primary_keys_str, conn_dict_str, start_time, end_time = sys.argv[1:]
    primary_keys = json.loads(primary_keys_str.replace("'", '"'))

    if conn_dict_str != "None":
        conn_dict = json.loads(conn_dict_str.replace("'", '"'))
        password = codecs.decode(conn_dict["password"], "rot-13")
        conn_dict["password"] = password
    else:
        conn_dict = None

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    create_initial_delta_table(spark, env, schema, table, primary_keys, conn_dict)
    spark.stop()
