import itertools
import sys
import logging
from datetime import timedelta

from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format, decode, unbase64

from common import date as date_util
from common.spark import pii, util
from common.utils.nv_obs import get_nv_data_bucket_uri, get_obs_client


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def _flatten_data(spark, df, col):
    """Converts JSON string in df[col] to columns"""
    idx = df.columns.index(col)
    return spark.read.json(df.rdd.map(lambda data: data[idx]))


def _get_paths(bucket, db, table, ts, num_intervals):
    if db.startswith("ninjamart"):
        db = db.replace("ninjamart_", "")

    ts_datetime = date_util.to_datetime(ts)
    paths = []
    delimiter = "parquet"
    cdc_paths = ("cdc_stream", "ticdc_stream")
    obs_client = get_obs_client()
    logger.info(f"Checking for CDC paths: {cdc_paths}")

    for i, path in itertools.product(range(int(num_intervals)), cdc_paths):
        dt = ts_datetime + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={dt:%Y-%m-%d}/time={dt:%H-%M-%S}/database={db}/table={table}/"
        response = obs_client.listObjects(bucketName=bucket, prefix=prefix, delimiter=delimiter)
        body = response["body"]

        if response["status"] == 200 and body and "commonPrefixs" in body:
            common_prefixes = body["commonPrefixs"]
            if common_prefixes:
                paths.append(f"obs://{bucket}/{prefix}")
    logger.info(f"Found paths: {paths}")
    logging.info(f"Found paths: {paths}")
    return paths


def _write_cdc(df, bucket):
    (
        df.repartition("cdc_date", "cdc_time")
        .write.mode("append")
        .format("parquet")
        .partitionBy("cdc_date", "cdc_time")
        .save(f"{bucket}/cdc_processed/{db}/{table}/")
    )


def process_cdc(spark, db, table, ts, num_intervals, pii_fields):
    nv_data_cols = {"nv_data_ts": "long", "nv_data_xid": "long", "nv_data_xoffset": "long"}
    date = date_util.date_from_ts(ts)
    time = date_util.time_from_ts(ts)
    input_bucket = get_nv_data_bucket_uri(env, bucket_type="raw", schema=db, strip=True)
    paths = _get_paths(input_bucket, db, table, ts, num_intervals)
    logger.info(f"Reading CDC data from: {paths}")
    raw_cdc = spark.read.parquet(*paths)
    flat = _flatten_data(spark, raw_cdc, "nv_data_cdc")
    flat_cast = util.cast_columns(flat, nv_data_cols)
    df_cdc = util.add_literal_columns(
        flat_cast.withColumn("created_month", date_format("created_at", "yyyy-MM")),
        {"cdc_date": date, "cdc_time": time},
    )

    output_bucket = get_nv_data_bucket_uri(env, bucket_type="processed", schema=db, strip=False)
    if db.startswith("core") and table == "blobs":
        df_cdc = df_cdc.withColumn("data", decode(unbase64(df_cdc.data), "utf-8"))
    df_masked = pii.mask(spark, df_cdc, pii_fields)
    _write_cdc(df_masked, output_bucket)


if __name__ == "__main__":
    env, db, table, ts, num_intervals, pii_fields_str = sys.argv[1:]
    pii_fields = [f.strip() for f in pii_fields_str[1:-1].replace("'", "").split(",")]

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    process_cdc(spark, db, table, ts, num_intervals, pii_fields)
    spark.stop()
