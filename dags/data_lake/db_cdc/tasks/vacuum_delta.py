import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession

from common.utils.nv_obs import get_nv_data_bucket_uri


def vacuum_delta_table(spark, env, schema, table, retention_hours):
    """
    Vacuum removes files no longer referenced by a Delta table and are older than the retention threshold.

    :param spark:               Spark session
    :param env:                 Data lake environment
    :param schema:              DB schema
    :param table:               DB table
    :param retention_hours:     Retention threshold in hours
    """
    gs_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)
    path = f"{gs_path}/{schema}/{table}"
    print(f"Running vacuum on {path} with retention hours: {retention_hours}")
    delta_table = DeltaTable.forPath(spark, path)
    delta_table.vacuum(float(retention_hours))


if __name__ == "__main__":
    env, schema, table, retention_hours = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    vacuum_delta_table(spark, env, schema, table, retention_hours)
    spark.stop()
