# import logging, json
# from datetime import datetime, timedelta
# from google.cloud import storage
# from functools import partial
# from pathlib import Path

# import pendulum
# from airflow import DAG
# from airflow.api.common.experimental import get_task_instance
# from airflow.models import Variable
# from airflow.operators.bash import BashOperator
# from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>Operator, PythonOperator
# from airflow.operators.dagrun_operator import TriggerDagRunOperator
# from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
# from airflow.providers.google.cloud.hooks.gcs import GCSHook
# from airflow.providers.google.cloud.sensors.gcs import GCSObjectsWithPrefixExistenceSensor
# from airflow.sensors.external_task import ExternalTaskSensor
# from airflow.utils.task_group import TaskGroup

# from common.airflow import db
# from common.airflow import notifications as notif
# from common.airflow import pk
# from common.date import get_schema_day
# from common.stringcase import kebab_case
# from common.list import merge_dictionaries
# from common.utils.gcs import get_nv_data_bucket_uri
# from metadata.compaction import TABLES as COMPACTION_TABLES
# from metadata.data_count_sanity import DATA_COUNT_TABLES
# from metadata.constants import Timeout
# from metadata.metabase import TABLES as METABASE_TABLES
# from metadata.pii import SCHEMAS
# from metadata.spark_conf import SPARK_CONF
# from metadata.ticdc import TICDC_SCHEMAS

# ALERT_CHANNEL = "high_priority_alert"

# tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

# env = Variable.get("env")
# interval_duration = 60
# num_intervals = interval_duration // 15
# mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
# mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
# spark_conf = SPARK_CONF[env]["cdc"]
# table_sizes = Variable.get("table_sizes", deserialize_json=True)["cdc"]
# pii_tables = Variable.get("pii_fields", deserialize_json=True)
# tables_to_size = {table: size for size, tables in table_sizes.items() for table in tables}
# critical_tables = [table for table_dict in DATA_COUNT_TABLES.values() for table in table_dict]



# compaction_schemas = merge_dictionaries(COMPACTION_TABLES[env], METABASE_TABLES[env]).keys()
# pii_schemas = SCHEMAS[env]
# default_args = {
#     "owner": "airflow",
#     "start_date": datetime(2024, 8, 27, 0, 0, 0),
#     "retries": 1,
#     "retry_delay": timedelta(minutes=5),
#     "on_failure_callback": (
#         lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
#     )
#     if env in ("prod", "dev")
#     else None,
#     "pool": "cdc_pool",
# }


# def create_dag(dag_id, schedule_interval=f"*/{interval_duration} * * * *", default_args=default_args):
#     dag = DAG(
#         dag_id=dag_id,
#         schedule_interval=schedule_interval,
#         default_args=default_args,
#         max_active_runs=1,
#         concurrency=16,
#         user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
#         params={"alert_channel": ALERT_CHANNEL},
#         tags=["data_lake", "db_cdc", "db_cdc_delta"],
#         dagrun_timeout=timedelta(hours=2),
#     )
#     globals()[dag_id] = dag
#     return dag


# def is_table_supported_for_cdc(schema, table):
#     from common.airflow import db
#     import logging
#     from common.airflow import pk

#     """
#     Information_schema columns index mapping
#     """
#     _COLUMN_KEY = 16
#     _COLUMN_NAME = 3
#     _DATA_TYPE = 7
#     try:
#         fields = db.get_column_info(schema, table, mysql_connections)
#     except Exception:
#         logging.info(f"Error when trying to describe table: {schema}.{table}")
#         return False
#     logging.info(f"Fields pulled: {fields}")
#     primary_keys = []
#     has_created_at = False
#     for f in fields:
#         if "COLUMN_NAME" in f:
#             if f["COLUMN_KEY"] == "PRI":
#                 primary_keys.append(f["COLUMN_NAME"])
#             elif f["COLUMN_NAME"] == "created_at" and f["DATA_TYPE"] in ("timestamp", "datetime"):
#                 has_created_at = True
#         else:
#             if f[_COLUMN_KEY] == "PRI":
#                 primary_keys.append(f[_COLUMN_NAME])
#             if f[_COLUMN_NAME] == "created_at" and f[_DATA_TYPE] in ("timestamp", "datetime"):
#                 has_created_at = True

#     if not (primary_keys and has_created_at):
#         return False

#     pk.manage_primary_keys_variable(schema, table, primary_keys)
#     return True


# def list_table_tasks(schema, **context):
#     import logging
#     from airflow.models import Variable

#     tables_with_changes = []
#     task_ids = f"{schema}.list_tables_with_changes"
#     change_list = context["ti"].xcom_pull(dag_id=f"datalake_cdc_{schema}_v2", task_ids=task_ids)
#     logging.info(f'''List of DB tables with changes from datalake_cdc_sensor: {change_list},
#                 pulled from :{{task_ids}}''')

#     # mysql_tables has schema to table mapping schema as key and tables from schema as values
#     for table in change_list:
#         if table not in mysql_tables[schema]:
#             logging.info(f"Table {schema}.{table} is not in mysql_tables")
#             if not is_table_supported_for_cdc(schema, table):
#                 logging.info(f"Table {schema}.{table} is not supported for cdc")
#                 continue
#             logging.info(f"Table {schema}.{table} is supported for cdc. Adding it to mysql_tables")
#             if schema not in mysql_tables:
#                 mysql_tables[schema] = []
#             mysql_tables[schema].append(table)
#             Variable.set("mysql_tables", mysql_tables, serialize_json=True)
#         tables_with_changes.append(f"process_cdc_messages_{schema}_{table}")
#     return tables_with_changes


# def get_last_compaction_date(schema_name: str, execution_date: pendulum.DateTime):
#     from common.date import get_schema_day

#     schema_day = get_schema_day(schema_name)
#     dt = execution_date.subtract(weeks=1).add(minutes=interval_duration)
#     return dt.start_of("day") if dt.day_of_week == schema_day else dt.previous(schema_day)


# def get_last_scheduled_execution_date(execution_date):
#     """Allows sensor to find the last run even though DAG is triggered out of schedule, such as when running E2E tests,
#     e.g., sensor with execution_date = '2020-01-01T11:00:00.500' will find '2020-01-01T10:00:00.000' run
#     """
#     return execution_date.replace(minute=0, second=0, microsecond=0).subtract(minutes=interval_duration)


# def check_and_extract_delta_metrics(env, schema, table, delta_task_id, is_pii, **context):
#     from airflow.providers.google.cloud.hooks.gcs import GCSHook
#     from common.utils.gcs import get_nv_data_bucket_uri
#     import logging
#     from airflow.api.common.experimental import get_task_instance

#     """Checks if the upstream Delta merge task successfully committed a new Delta version."""
#     hook = GCSHook()
#     client = storage.Client()
#     gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema="legacy", strip=True)
#     if is_pii:
#         gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True)
#     gs_bucket, base_path = gs_bucket_path_db.split("/")
#     prefix = f"{base_path}/{schema}/{table}/_delta_log"
#     logging.info(f"Checking in '{gs_bucket}/{prefix}'")
#     blobs = list(hook.list(bucket_name=gs_bucket, prefix=prefix, delimiter=".json"))
#     if not blobs:
#         raise Exception(f"No json logs found in '{gs_bucket}/{prefix}'.")
#     last_delta_log = max(blobs)
#     logging.info(f"Last delta log: {last_delta_log}")
#     last_delta_log_update = hook.get_blob_update_time(gs_bucket, last_delta_log)

#     delta_ti = get_task_instance.get_task_instance(context["dag"].dag_id, delta_task_id, context["execution_date"])
#     delta_start_date = delta_ti.start_date
#     # raise airflow specific exception
#     if last_delta_log_update < delta_start_date:
#         raise Exception("Delta merge not found.")

#     bucket = client.bucket(gs_bucket)
#     latest_log = bucket.blob(last_delta_log)
#     latest_log_content = latest_log.download_as_string().decode('utf-8')
#     latest_log_data = [json.loads(line) for line in latest_log_content.strip().split('\n')]
#     latest_version = latest_log.name.split('/')[-1].split('.')[0]
#     latest_commit = next((entry for entry in latest_log_data if "commitInfo" in entry), None)

#     if latest_commit and "commitInfo" in latest_commit:
#         operation_metrics = latest_commit["commitInfo"].get("operationMetrics", {})
#     else:
#         logging.error(f"No commitInfo found in the latest log: {latest_log.name}")
#         operation_metrics = {}

#     metrics_dict = {
#         "version": latest_version,
#         "numTargetRowsCopied": int(operation_metrics.get("numTargetRowsCopied", 0)),
#         "numTargetRowsDeleted": int(operation_metrics.get("numTargetRowsDeleted", 0)),
#         "numTargetFilesAdded": int(operation_metrics.get("numTargetFilesAdded", 0)),
#         "numTargetRowsInserted": int(operation_metrics.get("numTargetRowsInserted", 0)),
#         "numTargetRowsUpdated": int(operation_metrics.get("numTargetRowsUpdated", 0)),
#         "numSourceRows": int(operation_metrics.get("numSourceRows", 0)),
#         "numOutputRows": int(operation_metrics.get("numOutputRows", 0)),
#         "numTargetFilesRemoved": int(operation_metrics.get("numTargetFilesRemoved", 0)),
#         "schema": schema,
#         "table": table,
#         "execution_date": str(context["execution_date"]),
#     }
#     context['ti'].xcom_push(key=f'delta_metrics_{schema}_{table}', value=metrics_dict)
#     return metrics_dict

# def trigger_raw_to_delta_comparison_task(schema, table, **kwargs):
#     return TriggerDagRunOperator(
#         task_id=f"trigger_raw_to_delta_comparison_{schema}_{table}",
#         wait_for_completion=False,
#         trigger_dag_id='raw_to_delta_sanity_dag',
#         conf={"schema": schema, "table": table, "execution_date": "{{ execution_date }}"},
#     )


# def create_delta_task(env, task_name, schema, table, is_pii=False):
#     from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
#     from common.stringcase import kebab_case
#     from metadata.constants import Timeout

#     delta_task_id = task_name
#     delta_default_airflow_var = ["id"]
#     delta_task = SparkSubmitOperator(
#         task_id=delta_task_id,
#         name=kebab_case(delta_task_id),
#         execution_timeout=Timeout.FORTY_FIVE_MINUTES,
#         application=f"{tasks_path}/update_delta.py",
#         application_args=[
#             env,
#             schema,
#             table,
#             "{{ ts }}",
#             f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
#             f"{{{{ ti.xcom_pull(dag_id='datalake_cdc_{schema}_v2', task_ids='get_{schema}_conn') }}}}",
#             f"{ is_pii }",
#         ],
#         conn_id="spark_default",
#         conf={
#             **(spark_conf[tables_to_size.get(delta_task_id, "medium")]),
#             "spark.databricks.delta.schema.autoMerge.enabled": "true",
#         },
#     )
#     return delta_task


# def clear_delta_callback(delta_task, context):
#     # Temporarily disable alerts when clear delta task fails.
#     # notif.chat.send_ti_failure_alert(context)
#     delta_task.clear(start_date=context["execution_date"], end_date=context["execution_date"], downstream=True)


# def list_changes(bucket_name, schema, path, execution_date, **kwargs):
#     from airflow.providers.google.cloud.hooks.gcs import GCSHook
#     from datetime import datetime, timedelta
#     import logging

#     """Creates a list of tables with changes per DB."""
#     delimiter = "/part"
#     tables_with_changes = []
#     blob_list = []
#     hook = GCSHook()

#     if schema.startswith("ninjamart"):
#         _schema = schema.replace("ninjamart_", "")
#     else:
#         _schema = schema

#     for i in range(num_intervals):
#         ts = execution_date + timedelta(minutes=i * 15)
#         prefix = f"{path}/data/date={ts:%Y-%m-%d}/time={ts:%H-%M-%S}/database={_schema}"
#         logging.info(f"Listing changes in '{bucket_name}/{prefix}'")
#         # Note: the delimiter param is used so that the list_blobs returns results in a directory-like mode.
#         #  See https://cloud.google.com/storage/docs/json_api/v1/objects/list
#         blobs = hook.list(bucket_name=bucket_name, prefix=prefix, delimiter=delimiter)
#         blob_list.extend(blobs)

#     for blob in blob_list:
#         if "table" in blob:
#             table = blob.split("/")[-2].split("=")[-1]
#             if table not in tables_with_changes:
#                 tables_with_changes.append(table)

#     return tables_with_changes


# def _get_path(connection, schema):
#     from metadata.ticdc import TICDC_SCHEMAS

#     """
#     Check CDC message stream based on connection and schema
#     """
#     return "ticdc_stream" if (schema in TICDC_SCHEMAS[env] or connection == "ninjamart") else "cdc_stream"


# for schema, tables in mysql_tables.items():
#     dag_id = f"datalake_cdc_{schema}_v2"
#     db_dag = create_dag(dag_id=dag_id)
#     is_pii = False

#     if schema in pii_schemas:
#         is_pii = True

#     gcs_sensors = {}
#     with db_dag as dag:
#         # Note: this is needed because while it seems that `wait_for_downstream` & `max_active_dag_runs` can guarantee
#         #  the sequential execution, when we have a new table (appears as a new task), the `depends_on_past` check
#         #  prevents the new task from running. This task is similar to having `wait_for_downstream`, but on the
#         #  DAG level
#         connection = next((conn for conn, schemas in mysql_connections.items() if schema in schemas), None)
#         gs_bucket_raw = get_nv_data_bucket_uri(env=env, bucket_type="raw", schema=connection, strip=True)
#         database = schema

#         if connection == "ninjamart":
#             _database = database.replace("ninjamart_", "")
#         else:
#             _database = database

#         # task_id = f"{database}.check_next_partition"
#         # if database not in gcs_sensors:
#         #     gcs_sensors[database] = GCSObjectsWithPrefixExistenceSensor(
#         #         task_id=task_id,
#         #         bucket=gs_bucket_raw,
#         #         prefix=f"{_get_path(connection, database)}/data/date={{{{ next_ds }}}}/"
#         #         + f"time={{{{ next_execution_date.strftime('%H-%M-%S') }}}}/database={_database}",
#         #         execution_timeout=Timeout.FIFTEEN_MINUTES,
#         #         on_failure_callback=None,
#         #         retries=1,
#         #         pool="sensor_pool",
#         #     )
            
#         # removing check next parition in favor of wait time
#         # introducing dynamic wait time, in case of recovery we can adjust this from airflow ui instead of deployment
#         wait_time_dynamic = Variable.get("cdc_wait_time")
#         wait_task = BashOperator(
#                 task_id=f"cdc_wait",
#                 bash_command=f"sleep {wait_time_dynamic}",
#                 trigger_rule="none_skipped",
#             )

#         list_tables_from_gs_bucket_raw = PythonOperator(
#             task_id=f"{schema}.list_tables_with_changes",
#             python_callable=list_changes,
#             op_kwargs={
#                 "bucket_name": gs_bucket_raw,
#                 "schema": schema,
#                 "path": _get_path(connection, schema),
#             },
#             pool="sensor_pool",
#         )
#         #gcs_sensors[database] >> wait_task >> list_tables_from_gs_bucket_raw
#         wait_task >> list_tables_from_gs_bucket_raw

#         wait_previous_end = ExternalTaskSensor(
#             task_id=f"wait_previous_end_{schema}",
#             external_dag_id=f"datalake_cdc_{schema}_v2",
#             external_task_id=None,
#             execution_date_fn=get_last_scheduled_execution_date,
#             allowed_states=["success", "failed"],
#             pool="sensor_pool",
#             timeout=900,
#         )
#         #wait_previous_end >> gcs_sensors[database]
#         wait_previous_end >> wait_task

#         if schema in compaction_schemas:
#             wait_compaction_end = ExternalTaskSensor(
#                 task_id="wait_compaction_end",
#                 external_dag_id=f"datalake_compact_{schema}",
#                 external_task_id="end",
#                 execution_date_fn=partial(get_last_compaction_date, schema),
#                 allowed_states=["success", "upstream_failed", "failed"],
#                 mode="reschedule",
#                 poke_interval=300,
#                 pool="sensor_pool",
#                 timeout=900,
#             )
#             wait_compaction_end >> wait_previous_end

#         if not schema.startswith("ninjamart"):
#             get_conn = PythonOperator(
#                 task_id=f"get_{schema}_conn", python_callable=db.get_schema_conn, op_args=[schema, mysql_connections]
#             )

#         list_tables_to_process = BranchPythonOperator(
#             task_id=f"get_tables_with_changes_{schema}", python_callable=list_table_tasks, op_args=[schema]
#         )

#         for table in tables:
#             process_cdc_messages_default_var = {}
#             process_cdc_messages_default_list = []
#             if schema == "xb_operations_prod_gl":
#                 pii_schema = "xb_ops_prod_gl"
#             else:
#                 pii_schema = schema
#             process_cdc_task_id = f"process_cdc_messages_{schema}_{table}"
#             process_cdc = SparkSubmitOperator(
#                 task_id=process_cdc_task_id,
#                 name=kebab_case(f"cdc-{schema}-{table}"),
#                 execution_timeout=Timeout.FORTY_FIVE_MINUTES,
#                 application=f"{tasks_path}/post_process_cdc.py",
#                 application_args=[
#                     env,
#                     schema,
#                     table,
#                     "{{ ts }}",
#                     str(num_intervals),
#                     f"""{{{{
#                         var.json.pii_fields |
#                         extract('{pii_schema}', {process_cdc_messages_default_var}) |
#                         extract('{table}', {process_cdc_messages_default_list})
#                     }}}}""",
#                 ],
#                 conn_id="spark_default",
#                 conf={
#                     **(spark_conf["large"] if process_cdc_task_id in table_sizes["large"] else spark_conf["small"]),
#                 },
#             )

#             delta_task_id = f"delta_{schema}_{table}"
#             pii_delta_task_id = f"pii_delta_{schema}_{table}"

#             if is_pii:
#                 delta_task = create_delta_task(env, pii_delta_task_id, schema, table, is_pii)
#             else:
#                 delta_task = create_delta_task(env, delta_task_id, schema, table)


#             if not schema.startswith("ninjamart"):
#                 if is_pii:
#                     check_and_extract_delta_task = PythonOperator(
#                         task_id=f"check_and_extract_delta_pii_{schema}_{table}",
#                         python_callable=check_and_extract_delta_metrics,
#                         op_args=[env, schema, table, f"{pii_delta_task_id}", is_pii],
#                         on_failure_callback=partial(clear_delta_callback, delta_task),
#                         pool="sensor_pool",
#                     )
#                 else:
#                     check_and_extract_delta_task = PythonOperator(
#                         task_id=f"check_and_extract_delta_{schema}_{table}",
#                         python_callable=check_and_extract_delta_metrics,
#                         op_args=[env, schema, table, f"{delta_task_id}", False],
#                         on_failure_callback=partial(clear_delta_callback, delta_task),
#                         pool="sensor_pool",
#                     )

#                 list_tables_to_process >> process_cdc >> delta_task >> check_and_extract_delta_task
#             elif schema.startswith("ninjamart"):
#                 list_tables_to_process >> process_cdc >> delta_task

#             if table in critical_tables:
#                 trigger_task = trigger_raw_to_delta_comparison_task(schema, table)
#                 check_and_extract_delta_task >> trigger_task

#         if schema.startswith("ninjamart"):
#             list_tables_from_gs_bucket_raw >> list_tables_to_process
#         else:
#             list_tables_from_gs_bucket_raw >> [get_conn] >> list_tables_to_process
