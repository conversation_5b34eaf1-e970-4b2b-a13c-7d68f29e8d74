from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from salesforce.operators.salesforce_to_gcs_operator import SalesforceToGCSOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case, snake_case
from metadata.constants import Timeout
from metadata.salesforce import SALESFORCE_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
pii_tables = Variable.get("pii_fields_salesforce", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": "2020-04-12",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    # Temporarily increase timeout until SF team fixes bug that's causing the large volume of cases to be updated daily
    # https://jira.ninjavan.co/browse/SF-1298
    "sla": Timeout.TWO_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "5g",
    "spark.driver.memory": "5g",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
    "spark.databricks.delta.merge.repartitionBeforeWrite.enabled": "true",
    # Note: Spark 3.0's datetime implementation differs from that of Spark 2.4.
    #       As of 15-09-2020, we saw that the offending datetime values in salesforce case object.
    #       In particular, case.inbound_date_time_c and case.last_valid_delivery_attempt_date_time_c
    #       contains value as ancient as "1899-12-30 17:00:00".
    #       - Setting "spark.sql.legacy.parquet.datetimeRebaseModeInRead" to "LEGACY" or "CORRECTED"
    #         doesn't yield any noticeable difference. But we set to "CORRECTED" because the source data
    #         was written with python's pandas & fastparquet, which has the same underlying implementation
    #         as Spark 3.0's
    #       - We set "spark.sql.legacy.parquet.datetimeRebaseModeInWrite" to "CORRECTED" because the data &
    #         BI team has shifted over to Spark 3.0 so compatibility with Spark 2.X doesn't need to
    #         be maintained
    "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
    "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
}


with DAG(
    dag_id="datalake_salesforce_sales_cloud",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # Daily 00:00am SGT,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    catchup=False,
    user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
    tags=["data_lake"],
) as dag:
    for obj_config in SALESFORCE_CONFIG["objects"]:
        obj_name = snake_case(obj_config["name"])
        # default to empty string if None so that the args can be serialised
        created_at_field = snake_case(obj_config["created_at_field"]) or ""
        updated_at_field = snake_case(obj_config["updated_at_field"])

        load_sf_objects = SalesforceToGCSOperator(
            task_id=f"load_salesforce_objects_{obj_name}",
            sf_obj_config=obj_config,
            gcs_bucket=f"nv-data-{env}-data-lake-raw",
            gcs_object_base_path="salesforce/sales_cloud/objects",
            params={"snapshot": False, "snapshot_from_date": None},
            execution_timeout=Timeout.ONE_HOUR,
        )

        task_id = f"merge_delta_{obj_name}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            task_concurrency=1,
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                f"nv-data-{env}-data-lake",
                env,
                obj_name,
                created_at_field,
                updated_at_field,
                "{{ ds }}",
                "{{ params.refresh_data }}",
                f"""{{{{
                    var.json.pii_fields_salesforce |
                    extract('{obj_name}', {None})
                }}}}""",
            ],
            params={"refresh_data": False},
            conn_id="spark_default",
            conf=spark_conf,
            execution_timeout=Timeout.ONE_HOUR,
        )

        load_sf_objects >> [merge_delta]
