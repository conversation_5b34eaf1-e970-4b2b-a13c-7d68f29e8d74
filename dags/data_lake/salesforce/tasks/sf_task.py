import json
import logging
import os
import sys
from tempfile import NamedTemporaryFile

import pandas as pd
import pendulum
from obs import ObsClient
from simple_salesforce import Salesforce

from common.stringcase import snake_case

PARTITION_COLUMN = "nv_updated_date"

_PRIMITIVE_TYPE_TO_PANDAS_TYPE = {
    "xsd:boolean": "bool",
    "xsd:dateTime": "datetime64",
    "xsd:date": "datetime64",
    "xsd:double": "double",
    "xsd:int": "double",  # Note: int32 doesn't support None values, so we use double
    "xsd:base64Binary": "unicode",
    "xsd:string": "unicode",
    "xsd:time": "unicode",  # Note: pandas don't have a datatype to cater for xsd:time of format `00:00:00`
    "xsd:anyType": "unicode",
    "tns:ID": "unicode",  # Note: a wrapper around xsd:string
}


def list_obs_objects(bucket_name, obj_root, partition_column):
    obs_client = ObsClient(
        access_key_id=os.environ["OBS_ACCESS_KEY"],
        secret_access_key=os.environ["OBS_SECRET_KEY"],
        server=os.environ["OBS_ENDPOINT"]
    )
    try:
        objects = []
        marker = ""
        while True:
            resp = obs_client.listObjects(bucket_name, prefix=obj_root, marker=marker)
            if resp.status < 300:
                for content in resp.body.contents:
                    if partition_column in content.key[len(obj_root):]:
                        objects.append(content)
                if resp.body.is_truncated:
                    marker = resp.body.next_marker
                else:
                    break
            else:
                logging.error(f"Failed to list objects: {resp.status}")
                break
        return objects
    finally:
        obs_client.close()


def upload_to_obs(bucket_name, obs_obj_name, local_file_path):
    obs_client = ObsClient(
        access_key_id=os.environ["OBS_ACCESS_KEY"],
        secret_access_key=os.environ["OBS_SECRET_KEY"],
        server=os.environ["OBS_ENDPOINT"]
    )
    try:
        resp = obs_client.putFile(bucket_name, obs_obj_name, local_file_path)
        if resp.status >= 300:
            raise Exception(f"Failed to upload file to OBS: {resp.status}")
    finally:
        obs_client.close()


def check_obs_object_exists(bucket_name, obs_obj_name):
    obs_client = ObsClient(
        access_key_id=os.environ["OBS_ACCESS_KEY"],
        secret_access_key=os.environ["OBS_SECRET_KEY"],
        server=os.environ["OBS_ENDPOINT"]
    )
    try:
        resp = obs_client.getObjectMetadata(bucket_name, obs_obj_name)
        return resp.status < 300
    except:
        return False
    finally:
        obs_client.close()


def process_datetime_columns(df, schema):
    datetime_columns = [column for column, dtype in schema.items() if dtype == "datetime64"]

    for column in datetime_columns:
        # Skip conversion for out-of-bounds dates
        if column in df.columns:
            # First convert to string to allow string comparison
            df[column] = df[column].astype(str)
            # Create a mask for valid dates
            valid_dates_mask = (df[column] > "1677-09-21") & (df[column] < "2262-04-11")
            # Set invalid dates to NaT directly
            df.loc[~valid_dates_mask, column] = pd.NaT
            # Only convert valid dates
            if any(valid_dates_mask):
                # Convert to UTC and remove timezone info for valid dates only
                df.loc[valid_dates_mask, column] = pd.to_datetime(df.loc[valid_dates_mask, column], utc=True).dt.tz_localize(None)


def make_query_pandas_df(from_dt, to_dt, select, from_obj, where=None, include_deleted=True):
    schema = get_pandas_schema(from_obj)

    if from_obj == "ContentDocumentLink":
        soql = f"""SELECT (Select Id,LinkedEntityId, ContentDocumentId, SystemModstamp, IsDeleted, ShareType, Visibility FROM ContentDocumentLinks) FROM ContentDocument WHERE LastModifiedDate >= {from_dt} AND LastModifiedDate < {to_dt}"""
    else:
        soql = construct_soql(schema, select, from_obj, where)

    logging.info(f"Making query to Salesforce:\n{soql}")
    resp = sf_client.query_all(soql, include_deleted=include_deleted)

    logging.info(f"Received results: Total size: {resp['totalSize']}; Done: {resp['done']}")

    if not resp["records"]:
        return pd.DataFrame()

    if from_obj == "ContentDocumentLink":
        data = []
        for record in resp["records"]:
            for link_record in record["ContentDocumentLinks"]["records"]:
                link_data = [
                    link_record["Id"],
                    link_record["LinkedEntityId"],
                    link_record["ContentDocumentId"],
                    link_record["IsDeleted"],
                    link_record["SystemModstamp"],
                    link_record["ShareType"],
                    link_record["Visibility"],
                ]
                data.append(link_data)
        df = pd.DataFrame(
            data,
            columns=[
                "Id",
                "LinkedEntityId",
                "ContentDocumentId",
                "IsDeleted",
                "SystemModstamp",
                "ShareType",
                "Visibility",
            ],
        )
    else:
        df = pd.DataFrame.from_records(resp["records"], exclude=["attributes"])

    process_datetime_columns(df, schema)
    for column, dtype in schema.items():
        if column in df.columns and dtype != "datetime64":  # Skip datetime columns as they're already processed
            df[column] = df[column].astype(dtype)

    return df


def get_pandas_schema(obj):
    """Returns the pandas schema of a SF object"""
    schema = {}
    desc = sf_client.__getattr__(obj).describe()

    for f in desc["fields"]:
        name, sf_type = f["name"], f["soapType"]
        if sf_type in _PRIMITIVE_TYPE_TO_PANDAS_TYPE:
            schema[name] = _PRIMITIVE_TYPE_TO_PANDAS_TYPE[sf_type]
    return schema


def construct_soql(schema, select, from_obj, where):
    fields = schema.keys() if select == "*" else select
    soql = f"SELECT {','.join(fields)}\nFROM {from_obj}\nWHERE {where}"
    return soql


def describe_object(obj):
    """Returns the description of a SF object's schema and its metadata"""
    desc = sf_client.__getattr__(obj).describe()
    return _ordered_dict_to_dict(desc)


def get_available_fields(obj):
    """Returns the names of the fields."""
    desc = describe_object(obj)
    return [f["name"] for f in desc["fields"] if f["soapType"] in _PRIMITIVE_TYPE_TO_PANDAS_TYPE]


def _ordered_dict_to_dict(od):
    return json.loads(json.dumps(od))


if __name__ == "__main__":
    # Connect to Salesforce
    if os.environ.get("domain") == "":
        domain = None
    else:
        domain = os.environ.get("domain")
    sf_client = Salesforce(
        username=os.environ["SF_USERNAME"],
        password=os.environ["SF_PASSWORD"],
        security_token=os.environ["SF_SECURITY_TOKEN"],
        domain=domain,
        instance_url=os.environ["instance_url"],
        version=os.environ["version"],
    )

    obj_name = os.environ["name"]

    logging.info("Extracting variables from config name: {}".format(obj_name))

    updated_at_field = os.environ["updated_at_field"]

    logging.info("Extracting variables from updated_at: {}".format(updated_at_field))

    obj_name_sc = snake_case(obj_name)
    obj_root = f"{os.environ['OBS_OBJECT_BASE_PATH']}/{obj_name_sc}/"
    objects = list_obs_objects(bucket_name=os.environ["OBS_BUCKET"], obj_root=obj_root, partition_column=PARTITION_COLUMN)
    snapshot_from_date_env = os.environ.get("SNAPSHOT_FROM_DATE")
    should_snapshot = len(objects) == 0 or os.environ["SNAPSHOT"] == "true"
    snapshot_from_date = None

    if snapshot_from_date_env:
        snapshot_from_date = pendulum.parse(snapshot_from_date_env)

    from_dt_str = os.environ.get("AIRFLOW_EXECUTION_DATE")
    from_dt = pendulum.parse(from_dt_str)

    logging.info(" from_dt: {}".format(from_dt))

    to_dt_str = os.environ.get("AIRFLOW_NEXT_EXECUTION_DATE")
    to_dt = pendulum.parse(to_dt_str)

    logging.info("to_dt {}".format(to_dt))

    # Format from_dt and to_dt before passing them to make_query_pandas_df
    from_dt_formatted = from_dt.format('YYYY-MM-DDThh:mm:ssZ')  # Format for SOQL
    to_dt_formatted = to_dt.format('YYYY-MM-DDThh:mm:ssZ')  # Format for SOQL

    if should_snapshot:
        where_clause = f"{updated_at_field} < {to_dt_formatted}"
        if os.environ.get("SNAPSHOT_FROM_DATE"):
            snapshot_from_dt = pendulum.parse(os.environ.get("SNAPSHOT_FROM_DATE"))
            where_clause += f" AND {updated_at_field} >= '{snapshot_from_dt.format('YYYY-MM-DDTHH:mm:ssZ')}'"
    else:
        where_clause = f"{updated_at_field} >= {from_dt_formatted} AND {updated_at_field} < {to_dt_formatted}"

    df = make_query_pandas_df(from_dt_formatted, to_dt_formatted, select="*", from_obj=obj_name, where=where_clause)

    if df.empty:
        logging.info("DataFrame is empty. Exiting the job.")
        sys.exit(0)

    df.columns = [snake_case(c) for c in df.columns]
    
    # Process datetime columns before writing to parquet
    # For any column that seems to be a datetime, try to convert it appropriately
    for col in df.select_dtypes(include=['object']):
        try:
          df[col] = pd.to_datetime(df[col])
        except Exception:
         pass 

    partition_path = f"{obj_root}{PARTITION_COLUMN}={from_dt.date()}/"
    fname = f"{obj_name_sc}_{os.environ['TS_NODASH']}.snappy.parquet"
    obs_obj_name = partition_path + fname

    if check_obs_object_exists(os.environ["OBS_BUCKET"], obs_obj_name):
        logging.info(f"Skipping the upload to OBS Bucket because {obs_obj_name} already exists")
    else:
        with NamedTemporaryFile("w") as tmp:
            # Convert all datetime columns to string to avoid fastparquet type inference issues
            df_to_write = df.copy()
            for col in df_to_write.columns:
                if pd.api.types.is_datetime64_any_dtype(df_to_write[col]):
                    df_to_write[col] = df_to_write[col].astype(str)
            
            df_to_write.to_parquet(tmp.name, compression="snappy", engine="fastparquet")
            tmp.flush()

            logging.info(f"Uploading to OBS Bucket: '{os.environ['OBS_BUCKET']}'...")

            upload_to_obs(os.environ["OBS_BUCKET"], obs_obj_name, tmp.name)

            logging.info("File Uploaded!")