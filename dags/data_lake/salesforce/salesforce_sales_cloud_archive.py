import logging
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import ShortCircuitOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.hooks.base import BaseHook
from obs import ObsClient
from kubernetes.client import models as k8s

from common.airflow import notifications as notif
from common.stringcase import kebab_case, snake_case
from common.utils.nv_obs import strip_uri
from metadata.constants import Timeout, DATALAKE_BASE_URI
from metadata.salesforce_archive import SALESFORCE_ARCHIVE_CONFIG

env = Variable.get("env")
sf_connection = Variable.get(key="sf_connection", deserialize_json=True)
pii_tables = Variable.get("pii_fields_salesforce", deserialize_json=True)
hwc_connection = BaseHook.get_connection("hwc")

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
container = os.environ["AIRFLOW__KUBERNETES_EXECUTOR__WORKER_CONTAINER_REPOSITORY"]
tag = os.environ["AIRFLOW__KUBERNETES_EXECUTOR__WORKER_CONTAINER_TAG"]
obs_bucket = strip_uri(DATALAKE_BASE_URI.format(env))
obs_object_base_path = "salesforce/sales_cloud/objects"
PARTITION_COLUMN = "nv_updated_date"

static_env_vars = [
    k8s.V1EnvVar(name="AIRFLOW_EXECUTION_DATE", value="{{ execution_date }}"),
    k8s.V1EnvVar(name="AIRFLOW_NEXT_EXECUTION_DATE", value="{{ next_execution_date }}"),
    k8s.V1EnvVar(name="TS_NODASH", value="{{ ts_nodash }}"),
    k8s.V1EnvVar(name="SF_USERNAME", value=sf_connection["username"]),
    k8s.V1EnvVar(name="SF_PASSWORD", value=sf_connection["password"]),
    k8s.V1EnvVar(name="SF_SECURITY_TOKEN", value=sf_connection["security_token"]),
    k8s.V1EnvVar(name="domain", value=sf_connection["extras"]["domain"]),
    k8s.V1EnvVar(name="instance_url", value=sf_connection["extras"]["instance_url"]),
    k8s.V1EnvVar(name="version", value=sf_connection["extras"]["version"]),
    k8s.V1EnvVar(name="OBS_BUCKET", value=obs_bucket),
    k8s.V1EnvVar(name="OBS_OBJECT_BASE_PATH", value=obs_object_base_path),
    k8s.V1EnvVar(name="OBS_ACCESS_KEY", value=hwc_connection.login),
    k8s.V1EnvVar(name="OBS_SECRET_KEY", value=hwc_connection.password),
    k8s.V1EnvVar(name="OBS_ENDPOINT", value=hwc_connection.host),
    k8s.V1EnvVar(name="SNAPSHOT", value="false"),
    k8s.V1EnvVar(name="SNAPSHOT_FROM_DATE", value=""),
    k8s.V1EnvVar(name="CPU", value=sf_connection["resources"]["CPU"]),
    k8s.V1EnvVar(name="MEM", value=sf_connection["resources"]["MEM"]),
]

default_args = {
    "owner": "airflow",
    "start_date": "2024-03-15",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.TWO_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "15",
    "spark.executor.memory": "10g",
    "spark.driver.memory": "6g",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
    "spark.databricks.delta.merge.repartitionBeforeWrite.enabled": "true",
    # Note: Spark 3.0's datetime implementation differs from that of Spark 2.4.
    #       As of 15-09-2020, we saw that the offending datetime values in salesforce case object.
    #       In particular, case.inbound_date_time_c and case.last_valid_delivery_attempt_date_time_c
    #       contains value as ancient as "1899-12-30 17:00:00".
    #       - Setting "spark.sql.legacy.parquet.datetimeRebaseModeInRead" to "LEGACY" or "CORRECTED"
    #         doesn't yield any noticeable difference. But we set to "CORRECTED" because the source data
    #         was written with python's pandas & fastparquet, which has the same underlying implementation
    #         as Spark 3.0's
    #       - We set "spark.sql.legacy.parquet.datetimeRebaseModeInWrite" to "CORRECTED" because the data &
    #         BI team has shifted over to Spark 3.0 so compatibility with Spark 2.X doesn't need to
    #         be maintained
    "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
    "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
}

with DAG(
        dag_id="datalake_salesforce_archive",
        default_args=default_args,
        schedule_interval="0 16 * * 6",
        sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
        on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
        catchup=False,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        tags=["data_lake"],
) as dag:
    for obj_config in SALESFORCE_ARCHIVE_CONFIG["objects"]:
        obj_name = snake_case(obj_config["name"])
        created_at_field = snake_case(obj_config["created_at_field"]) or ""
        updated_at_field = snake_case(obj_config["updated_at_field"])

        dynamic_env_vars = [
            k8s.V1EnvVar(name="name", value=obj_config["name"]),
            k8s.V1EnvVar(name="created_at_field", value=obj_config["created_at_field"]),
            k8s.V1EnvVar(name="updated_at_field", value=obj_config["updated_at_field"]),
        ]

        all_env_vars = static_env_vars + dynamic_env_vars

        load_sf_objects = KubernetesPodOperator(
            task_id=f"load_salesforce_objects_{obj_name}",
            name="datalake_salesforce_sales_cloud_archive",
            in_cluster=True,
            startup_timeout_seconds=600,
            image=f"{container}:{tag}",
            namespace=os.environ["AIRFLOW__KUBERNETES_EXECUTOR__NAMESPACE"],
            env_vars=all_env_vars,
            cmds=["python", "/opt/airflow/dags/data_lake/salesforce/tasks/sf_task.py"],
            retries=2,
            get_logs=True,
            log_events_on_failure=True,
            retry_delay=timedelta(minutes=4),
            execution_timeout=Timeout.FOUR_HOURS,
        )


        def check_object_exists(obj_name, **context):
            obs_client = ObsClient(
                access_key_id=hwc_connection.login,
                secret_access_key=hwc_connection.password,
                server=hwc_connection.host
            )
            try:
                obj_root = f"{obs_object_base_path}/{obj_name}/"
                partition_path = f"{obj_root}{PARTITION_COLUMN}={context['execution_date'].date()}/"
                fname = f"{obj_name}_{context['ts_nodash']}.snappy.parquet"
                obs_obj_name = partition_path + fname
                logging.info("object path: %s. proceeding", obs_obj_name)

                resp = obs_client.getObjectMetadata(obs_bucket, obs_obj_name)
                if resp.status < 300:
                    logging.info("object already exists: %s. Hence proceeding", obs_obj_name)
                    return True
                else:
                    logging.info("object not exists: %s. Hence skipping", obs_obj_name)
                    return False
            except Exception as e:
                logging.info("Error checking object existence: %s. Hence skipping", str(e))
                return False
            finally:
                obs_client.close()


        check_df_empty_task = ShortCircuitOperator(
            task_id=f"check_object_task_{obj_name}",
            python_callable=check_object_exists,
            op_args=[obj_name],
            provide_context=True,
            dag=dag,
        )

        task_id = f"merge_delta_{obj_name}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            task_concurrency=1,
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                obs_bucket,
                env,
                obj_name,
                created_at_field,
                updated_at_field,
                "{{ ds }}",
                "{{ params.refresh_data }}",
                f"""{{{{
                    var.json.pii_fields_salesforce |
                    extract('{obj_name}', {[]})
                }}}}""",
            ],
            params={"refresh_data": False},
            conn_id="spark_default",
            conf=spark_conf,
            execution_timeout=Timeout.ONE_HOUR,
        )
        run_downstream_tasks = [merge_delta]
        load_sf_objects >> check_df_empty_task >> run_downstream_tasks