import sys

from pyspark.sql import SparkSession

from common import hive_metastore

PARTITION_COLUMNS = ("nv_created_month",)
HIVE_SCHEMA = "datalake_sonarqube"


def update_hive_metastore(spark, gcs_bucket, entity):
    delta_path = f"gs://{gcs_bucket}/sonarqube/delta/{entity}"
    print(f"Updating Hive metastore for {delta_path}")
    hive_metastore.update_metastore(spark, HIVE_SCHEMA, PARTITION_COLUMNS, entity, delta_path)


if __name__ == "__main__":
    env, gcs_bucket, entity = sys.argv[1:]
    spark = SparkSession.builder.enableHiveSupport().getOrCreate()
    update_hive_metastore(spark, gcs_bucket, entity)
    spark.stop()
