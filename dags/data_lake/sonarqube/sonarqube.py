import os
from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case
from metadata.constants import Email
from metadata.sonarqube import SONARQUBE_CONFIG
from custom_operators.sonarqube_to_gcs_operator import SonarQubeToGCSOperator

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = os.environ.get("AIRFLOW__KUBERNETES__NAMESPACE", "dev")

default_args = {
    "owner": "airflow",
    "start_date": "2023-05-09",
    "email": [Email.DATA_ENGR, Email.SRE],
    "email_on_failure": env == "prod",
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="datalake_sonarqube",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # Daily 00:00am SGT,
    concurrency=3,
    max_active_runs=1,
    catchup=False,
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in SONARQUBE_CONFIG.items():
        entity_config = {"entity": entity, **config}
        load_objects = SonarQubeToGCSOperator(
            task_id=f"load_sonarqube_objects_{entity}",
            entity_config=entity_config,
            gcs_bucket=gcs_bucket,
            gcs_folder_path=f"sonarqube/objects/{entity}",
            sonarqube_conn_id="sonarqube_default",
            gcs_conn_id="google_cloud_default",
        )
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[gcs_bucket, entity, "{{ds}}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "1",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
        )
        task_id = f"update_hms_{entity}"
        update_hms = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/update_hive_metastore.py",
            application_args=[env, gcs_bucket, entity],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "1g",
                "spark.driver.memory": "1g",
            },
        )
        load_objects >> merge_delta >> update_hms
