from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from appsflyer.operators.appsflyer_to_gcs_hq1_operator import AppsFlyerToGCSHq1Operator
from appsflyer.operators.appsflyer_to_gcs_operator import AppsFlyerToGCSOperator
from common.airflow import notifications as notif
from metadata.appsflyer import APPSFLYER_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2022-08-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_config = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "5g",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "1",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
}

with DAG(
    dag_id="datalake_appsflyer",
    default_args=default_args,
    schedule_interval="0 22 * * *",  # Daily 06:00am SGT
    concurrency=3,
    max_active_runs=1,
    catchup=False,
    tags=["data_lake"],
) as dag:
    object_gcs_bucket = f"nv-data-{env}-data-lake-raw"
    delta_gcs_bucket = f"nv-data-{env}-data-lake"

    for obj_name, obj_config in APPSFLYER_CONFIG.items():
        merge_delta = SparkSubmitOperator(
            task_id=f"merge_delta_{obj_name}",
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                object_gcs_bucket,
                delta_gcs_bucket,
                obj_name,
                "{{ds}}",
                obj_config["event_timestamp"],
                obj_config["src_partition_column"],
            ],
            conn_id="spark_default",
            conf=spark_config,
        )

        for app_id, config in obj_config["app_ids"].items():
            if obj_name == "raw_data_reports":
                entity_config = {
                    "entity": app_id,
                    "schema": obj_config["schema"],
                    "updated_at_field": obj_config["event_timestamp"],
                    **config,
                }

                load_appsflyer_objects = AppsFlyerToGCSOperator(
                    task_id=f"load_appsflyer_objects_{obj_name}_{app_id}",
                    entity_config=entity_config,
                    gcs_bucket=object_gcs_bucket,
                    gcs_folder_path=f"appsflyer/objects/{obj_name}",
                    appsflyer_conn_id="appsflyer_default",
                    gcs_conn_id="google_cloud_default",
                )

            elif obj_name == "aggregated_report":
                entity_config = {
                    "entity": app_id,
                    **config,
                }

                load_appsflyer_objects = AppsFlyerToGCSHq1Operator(
                    task_id=f"load_appsflyer_objects_{obj_name}_{app_id}",
                    entity_config=entity_config,
                    gcs_bucket=object_gcs_bucket,
                    gcs_folder_path=f"appsflyer/objects/{obj_name}",
                    appsflyer_conn_id="appsflyer_hq1",
                    gcs_conn_id="google_cloud_default",
                )
            load_appsflyer_objects >> merge_delta
