from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from custom_operators.zendesk_to_obs_operator import ZendeskToObsOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout
from metadata.zendesk import ZENDESK_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
pii_tables = Variable.get("pii_fields_zendesk", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": "2020-03-06",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


spark_conf = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "17g",
    "spark.executor.cores": "4",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "5",
}

with DAG(
    dag_id="datalake_zendesk",
    default_args=default_args,
    schedule_interval="0 22 * * *",  # Daily 06:00am SGT
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
    tags=["data_lake"],
) as dag:
    obs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in ZENDESK_CONFIG.items():
        task_id = f"merge_delta_{entity}"
        created_at_field = config["created_at_field"]
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                obs_bucket,
                env,
                entity,
                "{{ds}}",
                created_at_field,
                f"""{{{{ var.json.pii_fields_zendesk | extract('{entity}', {None}) }}}}""",
            ],
            conn_id="spark_default",
            conf=spark_conf,
        )

        merge_delta_unmasked = SparkSubmitOperator(
            task_id=task_id + "_unmasked",
            name=kebab_case(task_id + "_unmasked"),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                f"nv-data-{env}-datalake",
                env,
                entity,
                "{{ds}}",
                created_at_field,
                "[]",
            ],
            conn_id="spark_default",
            conf=spark_conf,
        )
        for system_id in config.get("system_ids", []):
            entity_config = {"system_id": system_id, "entity": entity, **config}
            load_zd_data = ZendeskToObsOperator(
                task_id=f"load_zendesk_data_{entity}_{system_id}",
                zd_entity_config=entity_config,
                obs_bucket=obs_bucket + "-raw",
                obs_folder_path=f"zendesk/objects/{entity}",
                zd_conn_id=f"zendesk_{system_id}",
            )
            load_zd_data >> [merge_delta, merge_delta_unmasked]
