from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from google_analytics.operators.google_analytics_to_gcs_operator import GoogleAnalyticsToGCSOperator
from metadata.google_analytics import GA_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2023-06-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "5g",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "1",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
}

with DAG(
    dag_id="datalake_google_analytics",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # Daily 00:00am SGT,
    concurrency=3,
    max_active_runs=1,
    tags=["data_lake", "external"],
    catchup=False
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in GA_CONFIG.items():
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                gcs_bucket,
                env,
                entity,
                "{{ds}}",
            ],
            conn_id="spark_default",
            conf=spark_conf,
        )

        entity_config = {"entity": entity, **config}
        load_google_analytics_objects = GoogleAnalyticsToGCSOperator(
            task_id=f"load_google_analytics_objects_{entity}",
            entity_config=entity_config,
            gcs_bucket=gcs_bucket + "-raw",
            gcs_folder_path=f"google_analytics/objects/{entity}",
            gcs_conn_id="google_cloud_default",
        )
        load_google_analytics_objects >> merge_delta
