from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications
from common.stringcase import kebab_case
from metadata.constants import Timeout
from metadata.jira import JIR<PERSON>_CONFIG
from plugins.jira.operators.jira_to_obs_operator import JiraToObsOperator

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2021-04-22",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notifications.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    dag_id="datalake_jira",
    default_args=default_args,
    schedule_interval="@daily",
    sla_miss_callback=notifications.chat.send_dag_run_sla_miss_alert,
    catchup=False,
    on_failure_callback=notifications.create_dag_run_failure_callback(env == "prod"),
    tags=["data_lake"],
) as dag:
    obs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in JIRA_CONFIG.items():
        load_jira_data = JiraToObsOperator(
            task_id=f"load_jira_data_{entity}",
            entity_config=config,
            obs_bucket=obs_bucket,
            obs_folder_path=f"jira/objects/{entity}",
            jira_conn_id="jira_default",
            obs_conn_id="hwc",
        )
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[obs_bucket, entity, "{{ds}}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "1",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
        )
        load_jira_data >> merge_delta
