import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, get_json_object, row_number

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"
ID = "id"
CREATED_AT = "created"
UPDATED_AT = "updated"


def get_object(spark, path, date_str):
    """
    Fetches incremental entity change data for a specified run date.

    :param spark:       Spark session
    :param path:        Change data file path
    :param date_str:    Run date
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(CREATED_AT, get_json_object("fields", f"$.{CREATED_AT}").cast("timestamp"))
        .withColumn(UPDATED_AT, get_json_object("fields", f"$.{UPDATED_AT}").cast("timestamp"))
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, month_fmt))
    )
    # get latest update by id
    df = (
        df.withColumn(
            "row_number",
            row_number().over(Window.partitionBy(ID).orderBy(desc(UPDATED_AT))),
        )
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(NV_CREATED_MONTH).save(path)


def merge(spark, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:  Spark session
    :param obj_df: Entity dataframe
    :param path:
    :return:
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [row[NV_CREATED_MONTH] for row in obj_df.select(NV_CREATED_MONTH).distinct().collect()]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{NV_CREATED_MONTH}) IN ({partitions_str}) AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{UPDATED_AT} > delta.{UPDATED_AT}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, obs_bucket, entity, date_str):
    obj_path = f"obs://{obs_bucket}/jira/objects/{entity}"
    obj_df = get_object(spark, obj_path, date_str)
    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = f"obs://{obs_bucket}/jira/delta/{entity}"
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path)
    else:
        create(obj_df, delta_path)


if __name__ == "__main__":
    obs_bucket, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, obs_bucket, entity, date_str)
    spark.stop()
