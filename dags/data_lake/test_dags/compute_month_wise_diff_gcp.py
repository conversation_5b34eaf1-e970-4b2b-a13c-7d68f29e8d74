from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator


from common.airflow import notifications as notif
from data_lake.test_dags.tasks.deltalake_monthwise_count_diff import save_count_diff, combine_parquet_diffs

env = Variable.get("env")

default_args = {"owner": "airflow", "start_date": "2020-05-21", "retries": 0}

spark_conf = {
    "spark.driver.memory": "4g",
    "spark.executor.memory": "4g",
    "spark.executor.cores": "4",
    "spark.executor.instances": "2",
    "spark.dynamicAllocation.maxExecutors": "10",
}

deltalake_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})

with DAG(
    dag_id="compute_month_wise_counts_diff",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    concurrency=300,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["hwc-migration"],
) as dag:
    tasks = []
    for schema, tables in deltalake_tables.items():
        for table in tables:
            task = PythonOperator(
                task_id=f"task-{schema}-{table}",
                python_callable=save_count_diff,
                op_args=[schema, table],
            )

            tasks.append(task)

    combine_task = PythonOperator(
        task_id=f"task-combine-all-diffs", python_callable=combine_parquet_diffs, op_args=[deltalake_tables]
    )

    tasks >> combine_task
