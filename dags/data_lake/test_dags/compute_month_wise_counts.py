from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")

default_args = {"owner": "airflow", "start_date": "2020-05-21", "retries": 0}

spark_conf = {
    "spark.driver.memory": "4g",
    "spark.executor.memory": "4g",
    "spark.executor.cores": "4",
    "spark.executor.instances": "2",
    "spark.dynamicAllocation.maxExecutors": "10",
}

deltalake_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})

with DAG(
    dag_id="compute_month_wise_counts",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    concurrency=300,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["hwc-migration"],
) as dag:
    for schema, tables in deltalake_tables.items():
        for table in tables:
            task = SparkSubmitOperator(
                task_id=f"{schema}-{table}",
                name=f"{schema}-{table}",
                execution_timeout=Timeout.ONE_HOUR,
                application=f"{tasks_path}/deltalake_monthwise_count.py",
                application_args=[schema, table],
                conn_id="spark_default",
                conf=spark_conf,
            )