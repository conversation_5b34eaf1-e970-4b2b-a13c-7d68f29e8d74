import logging
import sys
import json

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def save_count(spark, schema, table, created_month, primary_keys):
    deltalake_bucket = "nv-data-prod-data-lake"
    delta_table_path = f"obs://{deltalake_bucket}/db/{schema}/{table}"
    result_bucket = "nv-data-prod-data-lake-inventory"

    full_table_name = f"{schema}.{table}"
    df = spark.read.format("delta").load(delta_table_path)

    # Get available columns in the table
    available_columns = set(df.columns)

    # Get primary key columns, fallback to ["id"] if not defined
    pk_cols = primary_keys.get(full_table_name, ["id"])

    # Always include created_month, include updated_at only if it exists
    select_cols = pk_cols + ["created_month"]
    if "updated_at" in available_columns:
        select_cols.append("updated_at")

    # Filter and select
    df = df.filter(F.col("created_month") == created_month).select(*select_cols)

    output_path = f"gs://{result_bucket}/hwc/ids/{schema}/{table}/{created_month}"
    (df.write.mode("overwrite").format("parquet").option("compression", "snappy").save(output_path))


if __name__ == "__main__":
    if len(sys.argv) < 3:
        logger.error("Please provide a table")
        sys.exit(1)

    spark = SparkSession.builder.getOrCreate()
    schema = sys.argv[1]
    table = sys.argv[2]
    created_month = sys.argv[3]
    primary_keys = sys.argv[4]

    primary_keys = json.loads(primary_keys)

    save_count(spark, schema, table, created_month, primary_keys)
    spark.stop()
