import logging
import sys

from pyspark.sql import SparkSession

from delta.tables import DeltaTable

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def merge_obs_from_gcs(spark, schema, table, created_month):
    bucket = "nv-data-prod-data-lake"
    delta_table_path = f"obs://{bucket}/db/{schema}/{table}"

    missing_rows_path = f"obs://{bucket}/hwc_data_patch/missing_rows/{schema}/{table}/{created_month}"
    missing_rows_df = spark.read.format("parquet").load(missing_rows_path)

    # Define merge condition (adjust to your primary key)
    merge_condition = f"""
    target.id = source.id AND
    target.created_month = '{created_month}'
    """

    delta_table = DeltaTable.forPath(spark, delta_table_path)

    # Perform the merge
    (
        delta_table.alias("target")
        .merge(missing_rows_df.alias("source"), merge_condition)
        .whenMatchedUpdateAll()  # Updates all columns
        .whenNotMatchedInsertAll()  # Inserts new rows
        .execute()
    )


if __name__ == "__main__":
    spark = SparkSession.builder.getOrCreate()
    schema = sys.argv[1]
    table = sys.argv[2]
    created_month = sys.argv[3]

    merge_obs_from_gcs(spark, schema, table, created_month)
    spark.stop()
