import logging
import io

import pandas as pd

from obs import ObsClient
import pyarrow.parquet as pq
import pyarrow as pa
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.hooks.base import BaseHook

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

hook = GCSHook(gcp_conn_id="google_cloud_default")
gcs_client = hook.get_conn()

hwc_connection = BaseHook.get_connection("hwc")
obs_client = ObsClient(
    access_key_id=hwc_connection.login, secret_access_key=hwc_connection.password, server=hwc_connection.host
)


def download_parquet_from_obs(obs_client, bucket_name, schema, table):
    prefix = f"month_wise_counts/{schema}/{table}/"
    resp = obs_client.listObjects(bucket_name, prefix=prefix)

    if resp.status != 200:
        raise Exception(f"OBS listObjects failed: {resp.errorCode} - {resp.errorMessage}")

    parquet_keys = sorted([obj.key for obj in resp.body.contents if obj.key.endswith(".parquet")])

    if not parquet_keys:
        raise FileNotFoundError(f"No .snappy.parquet files found under {prefix}")

    tables = []
    empty_files = []

    for key in parquet_keys:
        resp = obs_client.getObject(bucket_name, key, loadStreamInMemory=True)
        if resp.status != 200:
            raise Exception(f"OBS getObject failed: {resp.errorCode} - {resp.errorMessage}")

        buffer = io.BytesIO(resp.body.buffer)
        if buffer.getbuffer().nbytes == 0:
            print(f"Skipping empty parquet file: {key}")
            empty_files.append(key)
            continue

        table_part = pq.read_table(buffer)
        tables.append(table_part)

    if not tables:
        raise Exception("All .snappy.parquet files were empty under the given prefix.")

    full_table = pa.concat_tables(tables)
    return full_table.to_pandas()


def download_parquet_from_gcs(gcs_client, bucket_name, schema, table):
    prefix = f"month_wise_counts/{schema}/{table}/"
    bucket = gcs_client.bucket(bucket_name)
    blobs = list(bucket.list_blobs(prefix=prefix))

    parquet_blobs = [blob for blob in blobs if blob.name.endswith(".parquet")]
    if not parquet_blobs:
        raise FileNotFoundError(f"No parquet file found in GCS under {prefix}")

    tables = []
    for blob in parquet_blobs:
        buffer = io.BytesIO()
        blob.download_to_file(buffer)
        buffer.seek(0)
        table = pq.read_table(buffer)
        tables.append(table)

    full_table = pa.concat_tables(tables)
    return full_table.to_pandas()


def upload_dataframe_to_obs(obs_client, df, bucket_name, object_path):
    # Convert DataFrame to PyArrow Table and serialize to memory
    table = pa.Table.from_pandas(df)
    buffer = io.BytesIO()
    pq.write_table(table, buffer)
    buffer.seek(0)

    # Use putObject for in-memory content upload
    resp = obs_client.putObject(bucketName=bucket_name, objectKey=object_path, content=buffer.read())

    if resp.status < 200 or resp.status >= 300:
        raise Exception(f"OBS upload failed: {resp.errorCode} - {resp.errorMessage}")


def combine_parquet_diffs(schema_table_dict):
    bucket_name = "nv-data-prod-data-lake"
    combined_df = pd.DataFrame()

    for schema, tables in schema_table_dict.items():
        for table in tables:
            print(schema, table)
            key = f"month_wise_count_diff/{schema}/{table}/result.parquet"
            try:
                resp = obs_client.getObject(bucket_name, key, loadStreamInMemory=True)
                if resp.status != 200:
                    print(f"OBS getObject failed: {resp.errorCode} - {key}")
                    continue

                buffer = io.BytesIO(resp.body.buffer)
                table = pq.read_table(buffer)
                df = table.to_pandas()

                combined_df = pd.concat([combined_df, df], ignore_index=True)
            except Exception as e:
                print(f"Error while downloading {key}: {e}")

    upload_dataframe_to_obs(obs_client, combined_df, bucket_name, f"month_wise_count_diff/combined_diff.parquet")


def save_count_diff(schema, table):
    # Inputs
    bucket_name = "nv-data-prod-data-lake"
    output_obs_path = f"month_wise_count_diff/{schema}/{table}/result.parquet"

    # Download both datasets
    try:
        gcs_df = download_parquet_from_gcs(gcs_client, bucket_name, schema, table)
    except Exception as e:
        print("GCP count retrieval failed")
        print("Exception trace", e)
        gcs_df = pd.DataFrame(columns=["created_month", "gcp_count", "table"])

    print("GCP count exists, geting HWC")
    try:
        hwc_df = download_parquet_from_obs(obs_client, bucket_name, schema, table)
    except Exception as e:
        print("HWC count retrieval failed")
        print("Exception trace", e)
        hwc_df = pd.DataFrame(columns=["created_month", "hwc_count", "table"])

    # Merge and compute count_diff
    merged_df = pd.merge(gcs_df, hwc_df, on=["created_month", "table"], how="outer")
    merged_df["count_diff"] = merged_df["gcp_count"].fillna(0) - merged_df["hwc_count"].fillna(0)

    # Upload result
    upload_dataframe_to_obs(obs_client, merged_df, bucket_name, output_obs_path)
