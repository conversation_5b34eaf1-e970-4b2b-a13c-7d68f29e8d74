import logging

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def save_count(spark, schema, table):
    cutoff_month = "2024-01"
    delta_bucket = "nv-data-prod-data-lake"
    result_bucket = "nv-data-prod-data-lake-inventory"

    delta_table_path = f"gs://{delta_bucket}/db/{schema}/{table}"
    df = spark.read.format("delta").load(delta_table_path)

    # Month wise count
    df = (
        df.filter(F.col("created_month") >= cutoff_month)
        .groupBy("created_month")
        .agg(F.count("*").alias("hwc_count"))
        .withColumn("table", F.lit(f"{schema}.{table}"))
    )

    output_path = f"gs://{result_bucket}/hwc/result"
    (df.write.mode("overwrite").format("parquet").option("compression", "snappy").save(output_path))


if __name__ == "__main__":
    spark = SparkSession.builder.getOrCreate()
    schema = "3pl_prod_gl"
    table = "partner_shippers"
    save_count(spark, schema, table)
    spark.stop()
