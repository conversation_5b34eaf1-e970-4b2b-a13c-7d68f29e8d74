from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")

default_args = {"owner": "airflow", "start_date": "2025-05-19", "retries": 0}

spark_conf = {
    "spark.driver.memory": "4g",
    "spark.executor.memory": "4g",
    "spark.executor.cores": "4",
    "spark.executor.instances": "2",
    "spark.dynamicAllocation.maxExecutors": "10",
}

with DAG(
    dag_id="gcp_access_test",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    concurrency=300,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["hwc-migration"],
) as dag:
    task = SparkSubmitOperator(
        task_id="task",
        name="gcs-test",
        execution_timeout=Timeout.ONE_HOUR,
        application=f"{tasks_path}/deltalake_gcp_test.py",
        conn_id="spark_default",
        conf=spark_conf,
    )
