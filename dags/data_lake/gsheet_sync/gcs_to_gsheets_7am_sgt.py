import json
from datetime import timedelta
from pathlib import Path

from airflow.decorators import dag
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from metadata.sheet_sync import OBS_GSHEETS_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")
creds = json.loads(Variable.get(key="creds_secret"))
default_args = {
    "owner": "airflow",
    "start_date": "2023-11-20",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


@dag(
    dag_id="datalake_gsheet_sync_7am_sgt",
    default_args=default_args,
    schedule_interval="0 23 * * *",
    catchup=False,
    max_active_runs=1,
    tags=["data_lake", "external_data", "gsheet"],
)
def gsheet_sync():
    for config in OBS_GSHEETS_CONFIG["7am_sgt"]:
        application_args = [
            config.get("obs_url", ""),
            config.get("input_data_format", ""),
            config.get("operation_type", ""),
            config.get("sheet_name", ""),
            config.get("sheet_url", ""),
            *config.get("columns", []),
            json.dumps(creds.get("gsheets", "")),
        ]
        application_args = [arg for arg in application_args if arg is not None]
        obs_to_gsheet_sync = SparkSubmitOperator(
            task_id=f"gsheet_{config['sheet_name']}",
            application=f"{tasks_path}/sheet_sync.py",
            application_args=application_args,
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "4",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "2g",
                "spark.sql.shuffle.partitions": "1",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
        )
    obs_to_gsheet_sync


datalake_gsheet_sync = gsheet_sync()