import sys
import json
from datetime import datetime, timedelta
from pyspark.sql import SparkSession
from pyspark.sql.functions import desc, desc_nulls_first, row_number
from pyspark.sql.window import Window
from delta.tables import DeltaTable

def main(env, spark, schema, table, ts, primary_keys_str, start_date, end_date, interval_hours_str, start_hour, end_hour, is_pii):
    primary_keys = json.loads(primary_keys_str)
    interval_hours = int(interval_hours_str)
    gs_bucket_path_db = f"gs://nv-data-{env}-data-lake/db"
    base_path = f"gs://nv-data-{env}-data-lake-processed/cdc_processed/{schema}/{table}/"
    spark.conf.set("spark.databricks.delta.schema.autoMerge.enabled", "true")

    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = datetime.strptime(end_date, "%Y-%m-%d")

    start_hour = int(start_hour) if start_hour != 'None' else 0
    end_hour = int(end_hour) if end_hour != 'None' else 23 - (23 % interval_hours)

    while start_datetime <= end_datetime:
        date_str = start_datetime.strftime("%Y-%m-%d")
        current_hour = start_hour
        while current_hour <= end_hour:
            hour_str = f"{current_hour:02d}-00-00"
            path = f"{base_path}cdc_date={date_str}/cdc_time={hour_str}/"
            print(hour_str)
            print(path)

            pdf = spark.read.option("basePath", base_path).parquet(path)
            df_latest = pdf.withColumn(
                "row_number",
                row_number().over(
                    Window.partitionBy(primary_keys).orderBy(
                        [
                            desc("nv_data_ts"),
                            desc("nv_data_xid"),
                            desc_nulls_first("nv_data_xoffset"),
                            desc("nv_data_change_type"),
                        ]
                    )
                )
            ).filter("row_number = 1").drop("row_number")

            PARTITION_COLUMN = 'created_month'

            delta_table = DeltaTable.forPath(spark, f"{gs_bucket_path_db}/{schema}/{table}")

            def _find_partitions_with_changes(cdc_latest):
                partitions_list = [f"'{i[PARTITION_COLUMN]}'" for i in cdc_latest.select(PARTITION_COLUMN).distinct().collect()]
                partitions = ", ".join(partitions_list)
                return partitions

            partitions = _find_partitions_with_changes(df_latest)

            def _generate_merge_condition(primary_keys, partitions):
                join_keys = " and ".join([f"delta.{key} = cdc.{key}" for key in primary_keys])
                partition_pruning = f"delta.{PARTITION_COLUMN} in ({partitions})"
                merge_condition = f"{join_keys} and {partition_pruning}"
                return merge_condition

            delta_merge_condition = _generate_merge_condition(primary_keys, partitions)

            (
                delta_table.alias("delta")
                .merge(df_latest.alias("cdc"), delta_merge_condition)
                .whenMatchedDelete(condition="cdc.nv_data_change_type = 'delete'")
                .whenMatchedUpdateAll(
                    condition="""
                        cdc.nv_data_ts > coalesce(delta.nv_data_ts, 0)
                        OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid > delta.nv_data_xid)
                        OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid = delta.nv_data_xid
                            AND (cdc.nv_data_xoffset > delta.nv_data_xoffset OR cdc.nv_data_xoffset IS NULL))
                        """
                )
                .whenNotMatchedInsertAll(condition="cdc.nv_data_change_type <> 'delete'")
                .execute()
            )

            current_hour += interval_hours
        start_datetime += timedelta(days=1)

if __name__ == "__main__":
    (env, schema, table, ts, primary_keys_str, start_date, end_date, interval_hours, start_hour, end_hour, is_pii) = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()
    main(env, spark, schema, table, ts, primary_keys_str, start_date, end_date, interval_hours, start_hour, end_hour, is_pii)