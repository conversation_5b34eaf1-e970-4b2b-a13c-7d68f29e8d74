import json
from datetime import datetime, timedelta
from pathlib import Path
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.models import Variable
from common.stringcase import kebab_case
from metadata.constants import Timeout

# Fetch configuration
try:
    recovery_configs = Variable.get("recovery_configs", deserialize_json=True)
except Exception as e:
    raise ValueError(f"Error fetching or parsing JSON from Variable: {e}")

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
default_spark_conf = {"spark.databricks.delta.schema.autoMerge.enabled": "true"}

def should_run(run):
    if not run:
        raise ValueError("Run parameter is set to False. Skipping task execution.")

def create_dag(dag_id: str, dag_config: dict, spark_config: dict) -> DAG:
    """
    Creating DAG and task for a process.
    """
    default_args = {
        "owner": "airflow",
        "depends_on_past": False,
        "start_date": datetime(2024, 6, 5),
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 1,
        "retry_delay": timedelta(minutes=5),
    }
    
    dag = DAG(
        dag_id,
        description='DAG to recover data from process bucket to delta table',
        schedule_interval=None,
        default_args=default_args,
        concurrency=16,
        max_active_runs=1,
        catchup=False,
        tags=["data_lake", "db_cdc", "db_cdc_delta", "recovery"],
    )

    with dag:
        env = dag_config.get("env", "prod")
        schema = dag_config.get("schema", "direct_prod_gl")
        table = dag_config.get("table", "invoice_details")
        primary_keys = json.dumps(dag_config.get("primary_keys", ["id"]))
        is_pii = dag_config.get("is_pii", "false")
        start_date = dag_config.get("start_date", "2024-06-05")
        end_date = dag_config.get("end_date", "2024-06-14")
        start_hour = dag_config.get("start_hour", None)
        end_hour = dag_config.get("end_hour", None)
        interval_hours = int(dag_config.get("interval_hours", 1))
        spark_conf = dag_config.get("spark_conf", {})

        combined_spark_conf = {**default_spark_conf, **spark_conf}

        spark_submit_args = [
            env,
            schema,
            table,
            "{{ ts }}",
            primary_keys,
            start_date,
            end_date,
            str(interval_hours),
            str(start_hour) if start_hour else 'None',
            str(end_hour) if end_hour else 'None',
            is_pii
        ]

        check_run_task = PythonOperator(
            task_id='check_run',
            python_callable=should_run,
            op_kwargs={'run': dag_config.get("run", False)},
            provide_context=True,
            dag=dag,
        )

        spark_submit_task = SparkSubmitOperator(
            task_id=kebab_case(f"process_to_delta_{table}_recovery"),
            name=kebab_case(f"process_to_delta_{table}_recovery"),
            execution_timeout=Timeout.THREE_HOURS,
            application=f"{tasks_path}/recovery_process_to_delta.py",
            application_args=spark_submit_args,
            conn_id="spark_default",
            conf=combined_spark_conf,
            dag=dag,
        )

        check_run_task >> spark_submit_task

    return dag

for recovery_config in recovery_configs:
    if isinstance(recovery_config, dict):
        schema = recovery_config.get("schema", "None")
        table = recovery_config.get("table", "None")
        dag_id = f"process_to_delta_{schema}_{table}_recovery"
        
        globals()[dag_id] = create_dag(
            dag_id=dag_id, 
            dag_config=recovery_config, 
            spark_config=default_spark_conf
        )
    else:
        raise ValueError("Each recovery configuration should be a dictionary.")