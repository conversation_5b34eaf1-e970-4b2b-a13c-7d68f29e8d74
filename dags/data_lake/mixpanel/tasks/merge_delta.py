import logging
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.utils import AnalysisException
from pyspark.sql.functions import col, date_format, from_unixtime, to_json

from metadata.constants import MIXPANEL_BASE_URI
from metadata.mixpanel import EVENT_TYPE, MIXPANEL_CONFIG, TIME_COLUMN

NV_CREATED_MONTH = "nv_created_month"
CREATED_AT = "created_at"


def _get_delta_path(gcs_bucket, project_id):
    """
    Returns path at which an SFMC entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param project_id:  Mixpanel project id
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/mixpanel/delta/{MIXPANEL_CONFIG[project_id]}"


def _get_obj_path(project_id, execution_date):
    """
    Returns path at which incremental change data of a Mixpanel data is stored.

    :param project_id:      Mixpanel project id
    :param execution_date:  Pendulum object indicates when task runs

    :return:            GCS path
    """
    return f"{MIXPANEL_BASE_URI}/projects/{project_id}/{execution_date.replace('-', '/')}/*"


def _get_object(spark, path):
    """
    Fetches incremental change data of Mixpanel for a specified updated date.

    :param spark:       Spark session
    :param path:        GCS path at which entity change data is stored
    :return:            Spark dataframe of entity change data
    """
    try:
        df = spark.read.option("primitivesAsString", "true").option("prefersDecimal", "true").json(path)
        df = df.withColumn(CREATED_AT, from_unixtime(TIME_COLUMN)).withColumn(
            NV_CREATED_MONTH, date_format(CREATED_AT, "yyyy-MM")
        )
        struct_columns = [c[0] for c in df.dtypes if c[1][:6] == "struct"]
        for struct_col in struct_columns:
            df = df.withColumn(struct_col, to_json(col(struct_col)))
        return df
    except AnalysisException as e:
        print(f"Path does not exist: {path}")
        return None


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Mixpanel entity data
    :param path:    Delta table path
    """
    (obj_df.write.mode("overwrite").format("delta").partitionBy([NV_CREATED_MONTH, EVENT_TYPE]).save(path))
    logging.info(f"Results are save in {path}")


def merge(obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    (obj_df.write.mode("append").format("delta").partitionBy([NV_CREATED_MONTH, EVENT_TYPE]).save(path))
    logging.info("Results are updated")


def merge_delta(spark, gcs_bucket, project_id, execution_date):
    obj_path = _get_obj_path(project_id, execution_date)

    logging.info(f"PRINT PATH: {obj_path}")
    obj_df = _get_object(spark, obj_path)
    if obj_df is None:
        logging.info(f"Path does not exist: {obj_path}, skipping...")
        return
    if obj_df.count() == 0:
        logging.info("No records to update")
        return
    delta_path = _get_delta_path(gcs_bucket, project_id)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(obj_df, delta_path)
    else:
        logging.info("Table does not exist, creating...")
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, project_id, execution_date = sys.argv[1:]

    spark = SparkSession.builder.config("spark.sql.caseSensitive", "true").getOrCreate()
    merge_delta(spark, gcs_bucket, project_id, execution_date)
    spark.stop()
