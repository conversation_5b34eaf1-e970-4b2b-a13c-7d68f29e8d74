import os
from datetime import timedel<PERSON>
from pathlib import Path

from airflow import DAG
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.mixpanel import MIXPANEL_CONFIG
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = os.environ.get("AIRFLOW__KUBERNETES__NAMESPACE", "dev")

default_args = {
    "owner": "airflow",
    "start_date": "2023-06-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    dag_id="datalake_mixpanel",
    default_args=default_args,
    schedule_interval="@daily",
    concurrency=3,
    catchup=False,
    max_active_runs=1,
    tags=["data_lake", "external_data"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for project_id, project_name in MIXPANEL_CONFIG.items():
        task_id = f"merge_delta_{project_name}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[gcs_bucket, project_id, "{{ ds }}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "2",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "10",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
            execution_timeout=Timeout.ONE_HOUR
        )
