from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from gdrive.hooks.gdrive_hook import GoogleDriveHook
from gdrive.operators.gdrive_to_obs_operator import GoogleDriveToObsOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout
from metadata.spark_conf import SPARK_CONF

import logging

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
spark_conf = SPARK_CONF[env]["gdrive"]

default_args = {
    "owner": "airflow",
    "start_date": "2020-03-18",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

ARCHIVE_FOLDER = "archive"
SCHEMA_CSV_FILE = "schema.csv"

folders = Variable.get("gdrive_folders", deserialize_json=True, default_var={})
dag_id = "datalake_gdrive"
object_obs_bucket = f"nv-data-{env}-data-lake-raw"
main_obs_bucket = f"nv-data-{env}-data-lake"


def list_files(gdrive_folder_id, **context):
    """
    Get first Excel file (.csv, .xls, .xlsx) in folder if it exists.

    Also pushes data to an XCom variable "schema_file" if a schema file exists in the folder.

    :param gdrive_folder_id: Google Drive folder ID
    :return: Excel file information if present, else None
    """
    gdrive_hook = GoogleDriveHook()
    logging.info(f"gdrive hook: {gdrive_hook}")
    files = gdrive_hook.get_excel_files(gdrive_folder_id)
    logging.info(f"Getting files from drive folder_id={gdrive_folder_id}, files = [{files}]")
    excel_file = {}
    for file in files:
        if file.get("name") == SCHEMA_CSV_FILE:
            context["ti"].xcom_push(key="schema_file", value=file)
        elif not excel_file:
            excel_file = file
    return excel_file


def archive_file(dag_id, task_id, gdrive_folder_id, **context):
    """
    Archives file in a sub-folder ({ARCHIVE_FOLDER}) in its current folder. This ensures that the file
    will not be read again in subsequent jobs.

    :param dag_id:              DAG ID
    :param task_id:             List task ID (for reading the file ID through XCom)
    :param gdrive_folder_id:    Folder ID
    :param context:             Task context
    """
    gdrive_hook = GoogleDriveHook()
    gdrive_file = context["ti"].xcom_pull(dag_id=dag_id, task_ids=task_id)
    if gdrive_file:
        gdrive_hook.move_file(gdrive_file["id"], gdrive_folder_id, ARCHIVE_FOLDER)


with DAG(
    dag_id=dag_id,
    default_args=default_args,
    schedule_interval="0 16 * * *",
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["data_lake", "gdrive"],
    catchup=False,
) as dag:
    for task_name, folder_config in folders.items():
        folder_id = folder_config["folder_id"]
        primary_keys = folder_config["keys"]

        list_task_id = f"list_files_{task_name}"
        list_files_task = PythonOperator(
            task_id=list_task_id, python_callable=list_files, op_args=[folder_id], provide_context=True
        )

        gdrive_to_obs_task = GoogleDriveToObsOperator( 
            task_id=f"gdrive_to_obs_{task_name}",
            gdrive_file=f"{{{{ ti.xcom_pull(task_ids='{list_task_id}') }}}}",
            obs_bucket=object_obs_bucket,
            obs_folder_path=f"gdrive/{task_name}",
            gdrive_schema_file=f"{{{{ ti.xcom_pull(key='schema_file', task_ids='{list_task_id}') }}}}",
            gdrive_conn_id="google_cloud_default",
            obs_conn_id="hwc",
        )

        task_id = f"merge_delta_{task_name}"
        merge_data_task = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[object_obs_bucket, main_obs_bucket, task_name, "{{ ts_nodash }}", str(primary_keys)],
            conn_id="spark_default",
            conf=spark_conf[folder_config.get("table_size", "small")],
        )

        archive_file_task = PythonOperator(
            task_id=f"move_file_{task_name}",
            python_callable=archive_file,
            op_args=[dag_id, list_task_id, folder_id],
            provide_context=True,
        )
        list_files_task >> gdrive_to_obs_task >> merge_data_task >> archive_file_task
