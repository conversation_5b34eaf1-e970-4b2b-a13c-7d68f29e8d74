from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from metadata.constants import Timeout
from metadata.data_audit_tables import DATA_AUDIT_TABLES

ALERT_CHANNEL = "high_priority_alert"

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
interval_duration = Variable.get("data_sanity_frequency", default_var=1)
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
env = Variable.get("env")
data_audit_tables = DATA_AUDIT_TABLES[env]

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 2, 17, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if env in ("prod", "dev")
    else None,
}


def get_execution_date(execution_date):
    return execution_date.replace(minute=0, second=0, microsecond=0)


def generate_data_audit_dag(schema, tables):
    dag_id = f"data_audit_dag_{schema}"
    dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval=f"0 */{interval_duration} * * *",
        max_active_runs=1,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        tags=["data_lake", "db_cdc", "db_snapshot", "integrity"],
        # params={"alert_channel": ALERT_CHANNEL},
        start_date=datetime(2024, 1, 1),
        catchup=False,
    )

    with dag:

        get_conn = PythonOperator(
            task_id=f"get_{schema}_conn",
            python_callable=db.get_schema_conn,
            op_args=[schema, mysql_connections],
            provide_context=True,
        )

        for table, key_columns in tables.items():

            wait_for_external_task = ExternalTaskSensor(
                task_id=f"wait_for_external_task_{schema}_{table}_end",
                external_dag_id=f"datalake_cdc_{schema}",
                external_task_id=f"pii_delta_{schema}_{table}",
                execution_date_fn=get_execution_date,
                allowed_states=["success", "skipped"],
                mode="reschedule",
                execution_timeout=Timeout.ONE_HOUR,
                poke_interval=120,
                dag=dag,
            )

            compare_cdc_task_id = f"compare_cdc_{schema}_{table}"
            delta_default_airflow_var = ["id"]
            process_cdc_messages_default_var = {}
            process_cdc_messages_default_list = []
            compare_cdc = SparkSubmitOperator(
                task_id=compare_cdc_task_id,
                application=f"{tasks_path}/comparison.py",
                application_args=[
                    env,
                    schema,
                    table,
                    f"{{{{ ti.xcom_pull(task_ids='get_{schema}_conn') | tojson }}}}",
                    f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
                    f"""{{{{
                        var.json.pii_fields |
                        extract('{schema}', {process_cdc_messages_default_var}) |
                        extract('{table}', {process_cdc_messages_default_list})
                    }}}}""",
                    "{{ execution_date }}",
                    "{{ execution_date + macros.timedelta(hours=1) }}",
                ],
                execution_timeout=Timeout.ONE_HOUR,
                conn_id="spark_default",
                conf={
                    "spark.executor.instances": "20",
                    "spark.executor.memory": "3g",
                    "spark.driver.memory": "2g",
                    "spark.properties.file": "/opt/spark/conf/spark.properties",
                },
            )
            get_conn >> wait_for_external_task >> compare_cdc
    return dag


for schema, tables in data_audit_tables.items():
    globals()[f"data_audit_dag_{schema}"] = generate_data_audit_dag(schema, tables)
