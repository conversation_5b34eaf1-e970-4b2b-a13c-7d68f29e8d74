import json,re, subprocess
import logging
import json
import airflow_client.client
from airflow_client.client.api import dag_api
from airflow_client.client.model.error import Error
from datetime import timedelta
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow_client.client.model.clear_task_instances import ClearTaskInstances

from datetime import datetime, timedelta, timezone
from google.cloud import storage
from pathlib import Path
from common.airflow import notifications as notif
from metadata.lagging_hour_check import LAGGING_HOURS_CHECK

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
env = Variable.get("env")
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
airflow_creds = json.loads(Variable.get("airflow_creds_secret"))
storage_client = storage.Client()

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 4, 21, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if env in ("prod", "dev")
    else None,
}


def get_execution_date(execution_date):
    return execution_date.replace(minute=0, second=0, microsecond=0)

lagging_hours = []
    
def get_lagging_hours_list(execution_date, schema, table, env):
    execution_date_dt = datetime.strptime(execution_date.split()[0], "%Y-%m-%d").date()
    lagging_hours = []
    bucket_name = f"nv-data-{env}-data-lake-raw"
    bucket = storage_client.get_bucket(bucket_name)

    for hour in range(24):
        hour_str = str(hour).zfill(2)  # Convert hour to string and zero-fill if needed (e.g., 1 -> "01")
        strPrefix = f"cdc_stream/data/date={execution_date_dt}/time={hour_str}-45-00/database={schema}/table={table}"

        max_write_time = None
        write_times = []

        for blob in bucket.list_blobs(prefix=strPrefix):  # Using the list_blobs method with the specific prefix
            updated_time = blob.updated.replace(tzinfo=timezone.utc)
            write_times.append(updated_time)
            print(f"Updated time for {blob.name}: {updated_time}")

        if write_times:
            max_write_time = max(write_times)
            print("Maximum Write Time:", max_write_time)

        time_partition = f"{hour_str}-45-00"
        hour, minute, second = map(int, time_partition.split("-"))
        partition_datetime = datetime(execution_date_dt.year, execution_date_dt.month, execution_date_dt.day, hour, minute, 0, tzinfo=timezone.utc)
        print(partition_datetime)

        next_utc_hour = partition_datetime + timedelta(minutes=15)
        next_utc_hour_offset = partition_datetime + timedelta(minutes=17)
        print(next_utc_hour)

        if max_write_time and max_write_time > next_utc_hour_offset:
            print("max_write_time is greater than the next UTC hour of the time partition")
            lag_hour = next_utc_hour - timedelta(hours=1)
            formatted_time = lag_hour.strftime("%Y-%m-%dT%H:%M:%SZ")
            lagging_hours.append(formatted_time)
        else:
            print("max_write_time is not greater than the next UTC hour of the time partition")

    return lagging_hours

def clear_tasks_from_xcom(**context):
    host = "https://airflow.ninjavan.co/api/v1" if env == 'prod' else "https://airflow-dev.ninjavan.co/api/v1"
    api_configuration = airflow_client.client.Configuration(
        host=host,
        username=airflow_creds['username'],
        password=airflow_creds['password']
    )

    with airflow_client.client.ApiClient(api_configuration) as api_client:
        dag_api_instance = dag_api.DAGApi(api_client)
        xcom_results = context['ti'].xcom_pull(task_ids=f'get_lagging_hours_list_{schema}_{table}'.replace(" ", "_"), key='results')

        if xcom_results:
            for execution_date in xcom_results:
                dag_id = f'datalake_cdc_{schema}'
                print(execution_date)
                start_time = datetime.strptime(execution_date, '%Y-%m-%dT%H:%M:%SZ')
                end_time = start_time + timedelta(hours=0.5)
                clear_task_instances = ClearTaskInstances(
                    dry_run=False,
                    task_ids=[f'process_cdc_messages_{schema}_{table}'.replace(" ", "_"), f'pii_delta_{schema}_{table}'.replace(" ", "_")],
                    start_date=start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    end_date=end_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    only_failed=False,
                    reset_dag_runs=True,
                )
                api_response = dag_api_instance.post_clear_task_instances(dag_id, clear_task_instances)
                print(api_response)
        else:
            print("No XCom results found")

with DAG(
    dag_id="datalake_cdc_raw_lagging_hours_retry",
    schedule_interval=f"0 0 * * *",
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    tags=["data_lake", "data_sanity"],
) as dag:
    for schema, tables in LAGGING_HOURS_CHECK.items():
        for table in tables:
            task_id_suffix = f"{schema}_{table}".replace(" ", "_")
            lagging_hours_list_task = PythonOperator(
                task_id=f'get_lagging_hours_list_{task_id_suffix}',
                python_callable=get_lagging_hours_list,
                op_kwargs={'execution_date': '{{ ds }}', 'schema': schema, 'table': tables, 'env': env},
                dag=dag
            )

            clear_task = PythonOperator(
                task_id=f'clear_tasks_from_xcom_{task_id_suffix}',
                python_callable=clear_tasks_from_xcom,
                op_kwargs={'execution_date': get_execution_date, 'schema': schema, 'table': tables, 'env': env},
                dag=dag
            )
            lagging_hours_list_task >> clear_task
