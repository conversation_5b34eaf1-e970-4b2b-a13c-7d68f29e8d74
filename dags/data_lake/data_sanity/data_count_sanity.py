import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.hooks.base import BaseHook
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from metadata.constants import Timeout
from metadata.data_count_sanity import DATA_COUNT_TABLES

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
INTERVAL_DURATION = Variable.get("data_sanity_frequency", default_var=1)
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
ALERT_CHANNEL = "high_priority_alert"
connection_object = BaseHook.get_connection('google_chat')
connection = connection_object.extra_dejson
connection['host'] = connection_object.host
env = Variable.get("env")
airflow_user_secret = Variable.get("airflow_user_secret", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 2, 17, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if env in ("prod", "dev")
    else None,
}


def get_execution_date(execution_date):
    return execution_date.replace(minute=0, second=0, microsecond=0)


with DAG(
        dag_id="data_count_sanity_dag",
        default_args=default_args,
        schedule_interval="0 18 * * *",
        max_active_runs=1,
        tags=["data_lake", "db_cdc", "sanity", "integrity"],
        params={"alert_channel": ALERT_CHANNEL},
        start_date=datetime(2024, 5, 15),
        catchup=False,
) as dag:
    for schema, tables in DATA_COUNT_TABLES.items():

        get_conn = PythonOperator(
            task_id=f"get_{schema}_conn",
            python_callable=db.get_schema_conn,
            op_args=[schema, mysql_connections],
            provide_context=True,
        )

        for table, key_columns in tables.items():
            wait_for_external_task = ExternalTaskSensor(
                task_id=f'wait_for_external_task_{schema}_{table}_end',
                external_dag_id=f'datalake_cdc_{schema}',
                external_task_id=f'pii_delta_{schema}_{table}',
                execution_date_fn=get_execution_date,
                allowed_states=['success', 'skipped'],
                mode='reschedule',
                execution_timeout=Timeout.ONE_HOUR,
                poke_interval=120,
                dag=dag,
            )

            compare_cdc_task_id = f"compare_count_cdc_{schema}_{table}"
            compare_cdc = SparkSubmitOperator(
                task_id=compare_cdc_task_id,
                application=f"{tasks_path}/count_comparison.py",
                application_args=[
                    str(env),
                    str(schema),
                    str(table),
                    f"{{{{ ti.xcom_pull(task_ids='get_{schema}_conn') | tojson }}}}",
                    "{{ execution_date }}",
                    "{{ next_execution_date }}",
                    json.dumps(connection),
                    json.dumps(airflow_user_secret)
                ],
                execution_timeout=Timeout.ONE_HOUR,
                conn_id="spark_default",
                conf={
                    "spark.executor.cores": "4",
                    "spark.executor.instances": "20",
                    "spark.executor.memory": "32g",
                    "spark.driver.memory": "5g",
                    "spark.properties.file": "/opt/spark/conf/spark.properties",
                },
            )
            get_conn >> wait_for_external_task >> compare_cdc