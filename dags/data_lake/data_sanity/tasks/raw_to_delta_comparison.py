import json
import logging
import sys
from datetime import datetime, timedelta
from pyspark.sql import Row, SparkSession
from pyspark.sql.types import <PERSON>ructType, <PERSON>ructField, StringType
from pyspark.sql.functions import col, from_json
from common.utils import alerts
from common.utils.nv_obs import get_nv_data_bucket_uri, get_obs_client
from metadata.primary_keys import PRIMARY_KEYS


def _get_paths(bucket, db, table, start_time, end_time):
    """
    Generates paths for the data stored in object storage bucket.

    Args:
        bucket (str): The name of the bucket.
        db (str): The name of the database.
        table (str): The name of the table.
        start_time (datetime): The start time for the data.
        end_time (datetime): The end time for the data.

    Returns:
        list: A list of paths to the data.
    """
    if db.startswith("ninjamart"):
        db = db.replace("ninjamart_", "")

    paths = []
    delimiter = "parquet"
    cdc_paths = ("cdc_stream", "ticdc_stream")
    obs_client = get_obs_client()

    interval = timedelta(minutes=15)
    current_datetime = start_time

    while current_datetime < end_time:
        for path in cdc_paths:
            prefix = f"{path}/data/date={current_datetime:%Y-%m-%d}/time={current_datetime:%H-%M-%S}/database={db}/table={table}/"
            response = obs_client.listObjects(bucketName=bucket, prefix=prefix, delimiter=delimiter)
            body = response["body"]

            if response["status"] == 200 and body and "commonPrefixs" in body:
                common_prefixes = body["commonPrefixs"]
                if common_prefixes:
                    paths.append(f"obs://{bucket}/{prefix}")

        current_datetime += interval

    return paths


def get_raw_records_count(spark, schema, table, start_time, end_time):
    """
    Counts the number of distinct records in the raw data.

    Args:
        spark (SparkSession): The SparkSession object.
        schema (str): The schema name.
        table (str): The table name.
        start_time (datetime): The start time for the data.
        end_time (datetime): The end time for the data.

    Returns:
        int: The count of distinct records.
    """
    input_bucket = get_nv_data_bucket_uri(env, bucket_type="raw", schema=schema, strip=True)
    paths = _get_paths(input_bucket, schema, table, start_time, end_time)
    if not paths:
        logging.info("Raw data is empty.")
        return
    raw_cdc = spark.read.parquet(*paths)
    table_key = f"{schema}.{table}"
    logging.info(f"Table key: {table_key}")
    primary_keys = PRIMARY_KEYS.get(table_key, ["id"])
    logging.info(f"Primary keys for {table_key}: {primary_keys}")
    schema_fields = [StructField(pk, StringType(), True) for pk in primary_keys]
    logging.info(f"Schema fields: {schema_fields}")
    schema_raw = StructType(schema_fields)
    logging.info(f"Schema: {schema}")
    df_with_fields = raw_cdc.withColumn("parsed_data", from_json(col("nv_data_cdc"), schema_raw))
    select_exprs = [col(f"parsed_data.{pk}").alias(pk) for pk in primary_keys]
    df_with_id = df_with_fields.select(*select_exprs)
    distinct_id_count = df_with_id.distinct().count()
    return distinct_id_count


def compare_data(spark, env, schema, table, start_time, end_time, connection, delta_metrics):
    """
    Compares the count of records between raw bucket and delta tables for consistency.

    Args:
        spark (SparkSession): The SparkSession object.
        env (str): The environment name.
        schema (str): The schema name.
        table (str): The table name.
        start_time (datetime): The start time for the data comparison.
        end_time (datetime): The end time for the data comparison.
        connection (dict): The connection details.
        delta_metrics (dict): The metrics of the delta table.

    Raises:
        Exception: If there are mismatched records between Raw bucket and Delta tables.

    Returns:
        None
    """
    try:
        logging.info(delta_metrics)
        accumulated_stats = []
        raw_records_count = get_raw_records_count(spark, schema, table, start_time=start_time, end_time=end_time)
        logging.info(delta_metrics)
        delta_df_count = delta_metrics.get("numSourceRows", 0)
        version = delta_metrics.get("version", "N/A")
        logging.info(delta_df_count)
        stats = Row(
            delta_version=version,
            stat_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            records_from_raw=raw_records_count,
            records_from_delta=delta_df_count,
            execution_date=start_time.strftime("%Y-%m-%d %H:%M:%S").replace(" ", "_").replace(":", "-"),
            records_difference=(raw_records_count or 0) - (delta_df_count or 0),
        )
        accumulated_stats.append(stats)
        new_stats_df = spark.createDataFrame(accumulated_stats)
        logging.info(new_stats_df.show())
        obs_path = f"obs://nv-data-{env}-data-lake/data_sanity/raw_to_delta/schema_name={schema}/table_name={table}"
        new_stats_df.coalesce(1).write.format("parquet").mode("append").partitionBy("execution_date").save(obs_path)
        if str(raw_records_count) != str(delta_df_count):
            message = f"Data mismatch between raw and delta tables for {schema}.{table} at {start_time}"
            alerts.raise_gchat_alert(message, connection)
            logging.error(message)
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}", exc_info=True)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    env, schema, table, start_datetime, connection_json, delta_metrics = sys.argv[1:]
    connection = json.loads(connection_json)
    logging.info(delta_metrics)
    delta_metrics = json.loads(delta_metrics)
    logging.info(delta_metrics)
    spark = SparkSession.builder.getOrCreate()
    start_time = datetime.fromisoformat(start_datetime.replace("Z", "+00:00"))
    end_time = start_time + timedelta(hours=(1))
    compare_data(spark, env, schema, table, start_time, end_time, connection, delta_metrics)
    spark.stop()
