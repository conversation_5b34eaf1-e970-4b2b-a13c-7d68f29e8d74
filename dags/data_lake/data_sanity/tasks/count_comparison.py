import codecs
import json
import logging
import sys

import airflow
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lit, current_timestamp, when
from datetime import datetime, timedelta
from common.utils import alerts
from dateutil.relativedelta import relativedelta
from common import db

ALERT_CHANNEL = "high_priority_alert"

DATE_FORMAT_EXPR = "yyyy-MM-dd HH:00:00"

PERCENTAGE_THRESHOLD = 40


def generate_months_between(start_time, end_time):
    start_time = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
    end_time = datetime.fromisoformat(end_time.replace("Z", "+00:00"))

    months = []
    current_month = start_time.replace(day=1)
    while current_month <= end_time:
        months.append(current_month.strftime('%Y-%m'))
        current_month += relativedelta(months=1)
    return months


def column_exists_in_delta(spark, delta_table_path, column_name):
    try:
        delta_schema = spark.read.format("delta").option("path", delta_table_path).load().schema
        return column_name in [field.name for field in delta_schema.fields]
    except Exception as e:
        print(f"An error occurred while checking the column existence: {str(e)}")
        return False


def fetch_source_data(spark, url, conn_dict, source_query):
    try:
        return spark.read.format("jdbc") \
            .option("url", url) \
            .option("user", conn_dict["user"]) \
            .option("password", conn_dict["password"]) \
            .option("query", source_query) \
            .load()
    except Exception as e:
        logging.error(f"An error occurred while reading the data: {str(e)}", exc_info=True)
        raise


def create_message_parts(result, count_type):
    source_count = result.get(f'source_{count_type}_count', 0)
    target_count = result.get(f'target_{count_type}_count', 0)
    count_difference = source_count - target_count

    message_parts = []
    if count_difference != 0:
        message_parts.append(
            f"Source {count_type} count ({source_count}) {'is greater than' if count_difference > 0 else 'is less than'} "
            f"target {count_type} count ({target_count})"
        )
    return message_parts, count_difference


def send_alerts(result_dict, connection):
    alert_messages = []
    call_message_summary = []

    for result in result_dict:
        base_message = f"Data Count Sanity Alert:\nSchema: {result['schema']}\nTable: {result['table']}\n"

        message_parts = [base_message]
        created_message_parts, created_count_difference = create_message_parts(result, 'created')
        updated_message_parts, updated_count_difference = create_message_parts(result, 'updated')

        message_parts += created_message_parts
        message_parts += updated_message_parts

        if created_count_difference != 0 or updated_count_difference != 0:
            message_parts.append(f"Created hour: {result['source_created_at']}")
            alert_messages.append("\n".join(message_parts))

        source_created_count = result.get('source_created_count', 1)
        source_updated_count = result.get('source_updated_count', 1)

        created_percentage_difference = 0 if source_created_count == 0 else abs(
            created_count_difference) / source_created_count * 100
        updated_percentage_difference = 0 if source_updated_count == 0 else abs(
            updated_count_difference) / source_updated_count * 100

        if created_percentage_difference > PERCENTAGE_THRESHOLD or updated_percentage_difference > PERCENTAGE_THRESHOLD:
            call_message_summary.append("\n".join(message_parts))

    if alert_messages:
        final_alert_summary = "Data Count Sanity Alerts:\n" + "\n".join(alert_messages)
        status = alerts.raise_gchat_alert(final_alert_summary, connection)
        print(f"Alert status: {status}")

    if call_message_summary:
        final_critical_alert_summary = "\n".join(call_message_summary)
        status = alerts.raise_grafana_alert("Data_Count_Sanity_Alert", final_critical_alert_summary, "Alerting", "Data")
        print(f"Critical alert status: {status}")


def count_comparison(spark, env, schema, table, conn_dict, start_time, end_time, connection):
    try:
        url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)
        delta_table_path = f"gs://nv-data-{env}-data-lake/db/{schema}/{table}"
        has_updated_at = column_exists_in_delta(spark, delta_table_path, "updated_at")
        select_clause = "created_at" + (", updated_at" if has_updated_at else "")
        source_query = f"""
            SELECT {select_clause}
            FROM {schema}.{table} 
            WHERE created_at >= '{start_time}' AND created_at <= '{end_time}'
        """

        print(source_query)
        source_df = fetch_source_data(spark, url, conn_dict, source_query)
        source_df.createOrReplaceTempView("source_view")
        source_view_query = f"""
            SELECT DATE_FORMAT(created_at, '{DATE_FORMAT_EXPR}') as source_created_at,
                COUNT(1) as source_created_count,
                SUM(CASE WHEN {'updated_at = "0" OR updated_at IS NULL' if has_updated_at else '1 = 0'} THEN 0 ELSE 1 END) AS source_updated_count
            FROM source_view
            GROUP BY 1
        """

        logging.info(f"Executing source query: {source_view_query}")
        source_aggregated_df = spark.sql(source_view_query)

        months_filter = generate_months_between(start_time, end_time)

        delta_df = spark.read.format('delta').load(delta_table_path).select(
            ["created_at"] + (["updated_at"] if has_updated_at else [])) \
            .where((col("created_month").substr(1, 7).isin(months_filter)) &
                   (col("created_at").cast("timestamp") >= lit(start_time)) &
                   (col("created_at").cast("timestamp") <= lit(end_time)))
        delta_df.createOrReplaceTempView("delta_view")
        delta_view_query = f"""
            SELECT DATE_FORMAT(created_at, '{DATE_FORMAT_EXPR}') as target_created_at, 
                COUNT(1) as target_created_count,
                SUM(CASE WHEN {'updated_at = "0" OR updated_at IS NULL' if has_updated_at else '1 = 0'} THEN 0 ELSE 1 END) AS target_updated_count 
            FROM delta_view
            GROUP BY 1
            """

        logging.info(f"Executing source query: {delta_view_query}")
        delta_df.show()
        target_aggregated_df = spark.sql(delta_view_query)

        result = source_aggregated_df.join(target_aggregated_df,
                                           source_aggregated_df.source_created_at == target_aggregated_df.target_created_at,
                                           "full_outer")
        result = result.withColumn("source_created_count",
                                   when(col("source_created_count").isNull(), 0).otherwise(col("source_created_count"))) \
            .withColumn("target_created_count",
                        when(col("target_created_count").isNull(), 0).otherwise(col("target_created_count"))) \
            .withColumn("source_updated_count",
                        when(col("source_updated_count").isNull(), 0).otherwise(col("source_updated_count"))) \
            .withColumn("target_updated_count",
                        when(col("target_updated_count").isNull(), 0).otherwise(col("target_updated_count"))) \
            .withColumn("created_at_difference", col("source_created_count") - col("target_created_count")) \
            .withColumn("updated_at_difference", col("source_updated_count") - col("target_updated_count")) \
            .withColumn("table", lit(table)) \
            .withColumn("schema", lit(schema)) \
            .withColumn("current_timestamp", current_timestamp()) \
            .withColumn("execution_date", lit(start_time.replace(" ", "_").replace(":", "-")))

        result.coalesce(1).write.format("parquet").mode("append").partitionBy("execution_date").save(
            f"gs://nv-data-{env}-data-lake/db/data_sanity/daily_counts/schema={schema}/table={table}")

        # alert = result.filter((col("created_at_difference") != 0) | (col("updated_at_difference") != 0)) // for temp purpose commented
        alert = result.filter((col("created_at_difference") != 0))
        if alert.count() > 0:
            logging.info(f"Data mismatch found for {schema}.{table} between {start_time} and {end_time}")
            result_data = alert.collect()
            result_dict = [row.asDict() for row in result_data]
            send_alerts(result_dict, connection)
        else:
            logging.info(f"No data mismatch found for {schema}.{table} between {start_time} and {end_time}")

    except Exception as e:
        logging.error(f"An error occurred: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    env, schema, table, conn_dict_str, start_time, end_time, connection_json, airflow_user_secret = sys.argv[1:]
    conn_dict = json.loads(conn_dict_str)
    connection = json.loads(connection_json)
    airflow_user_secret = json.loads(airflow_user_secret)
    conn_dict["user"] = airflow_user_secret["user"]
    conn_dict["password"] = codecs.decode(airflow_user_secret["password"], "rot-13")
    logging.info(f"conn_dict: {conn_dict}")
    start_time = (datetime.fromisoformat(start_time.replace("Z", "+00:00")) - timedelta(hours=2)).isoformat()
    end_time = (datetime.fromisoformat(end_time.replace("Z", "+00:00")) - timedelta(hours=2)).isoformat()
    spark = SparkSession.builder.getOrCreate()
    count_comparison(spark, env, schema, table, conn_dict, start_time, end_time, connection)
    spark.stop()