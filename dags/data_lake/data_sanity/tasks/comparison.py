import codecs
import itertools
import json
import logging
import pendulum
import sys
import hashlib
from datetime import datetime, timedelta

from delta.tables import DeltaTable
from google.cloud import storage
from pyspark.sql import Row, SparkSession
from pyspark.sql.functions import col, concat, date_format, decode, sha2, unbase64

from common import date as date_util
from common import db
from common.spark import cdc, pii, util
from common.utils.gcs import get_nv_data_bucket_uri
from metadata.constants import DATALAKE_BASE_URI

num_intervals = 4

class BloomFilter:
    def __init__(self, size, hash_functions):
        self.size = size
        self.hash_functions = hash_functions
        self.bit_array = [False] * size

    def add(self, item):
        for seed in range(self.hash_functions):
            index = self._hash(item, seed) % self.size
            self.bit_array[index] = True

    def __contains__(self, item):
        for seed in range(self.hash_functions):
            index = self._hash(item, seed) % self.size
            if not self.bit_array[index]:
                return False
        return True

    def _hash(self, item, seed):
        hash_func = hashlib.sha256()
        hash_func.update(str(item).encode('utf-8'))
        hash_func.update(str(seed).encode('utf-8'))
        return int(hash_func.hexdigest(), 16)

def create_bloom_filter(df, columns, size, hash_functions):
    bloom_filter = BloomFilter(size, hash_functions)
    for column in columns:
        df.select(column).foreach(lambda row: bloom_filter.add(row[0]))
    return bloom_filter

def compare_dataframes(df1, df2, columns):
    size = 1000
    hash_functions = 3  # You can adjust this number as needed

    bloom_filter_df1 = create_bloom_filter(df1, columns, size, hash_functions)
    bloom_filter_df2 = create_bloom_filter(df2, columns, size, hash_functions)

    if bloom_filter_df1.size != bloom_filter_df2.size:
        print("Dataframes have different sizes.")
        return False

    if df1.count() != df2.count():
        print("Dataframes have different row counts.")
        return False

    for column in columns:
        df1_values = set(df1.select(column).rdd.map(lambda row: row[0]).collect())
        df2_values = set(df2.select(column).rdd.map(lambda row: row[0]).collect())
        if df1_values != df2_values:
            print("Dataframes have different values in column:", column)
            return False

    print("Dataframes are completely the same.")
    return True

def _flatten_data(spark, df, col):
    """Converts JSON string in df[col] to columns"""
    idx = df.columns.index(col)
    return spark.read.json(df.rdd.map(lambda data: data[idx]))


def _get_paths(bucket, db, table, ts, num_intervals):
    if db.startswith("ninjamart"):
        db = db.replace("ninjamart_", "")

    ts_datetime = date_util.to_datetime(ts)
    paths = []
    delimiter = "parquet"
    cdc_paths = ("cdc_stream", "ticdc_stream")
    gs_bucket = storage.Client().get_bucket(bucket)

    for i, path in itertools.product(range(int(num_intervals)), cdc_paths):
        dt = ts_datetime + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={dt:%Y-%m-%d}/time={dt:%H-%M-%S}/database={db}/table={table}/"
        blobs = gs_bucket.list_blobs(prefix=prefix, delimiter=delimiter)
        if any(blobs):
            paths.append(f"gs://{bucket}/{prefix}")
    return paths


def process_cdc(spark, db, table, ts, date, time, num_intervals, pii_fields, primary_keys):
    nv_data_cols = {"nv_data_ts": "long", "nv_data_xid": "long", "nv_data_xoffset": "long"}
    input_bucket = get_nv_data_bucket_uri(env, bucket_type="raw", schema=db, strip=True)
    paths = _get_paths(input_bucket, db, table, ts, num_intervals)
    if not paths:
        logging.info("Raw data is empty.")
        return None
    raw_cdc = spark.read.parquet(*paths)
    flat = _flatten_data(spark, raw_cdc, "nv_data_cdc")
    flat_cast = util.cast_columns(flat, nv_data_cols)
    df_cdc = util.add_literal_columns(
        flat_cast.withColumn("created_month", date_format("created_at", "yyyy-MM")),
        {"cdc_date": date, "cdc_time": time},
    )
    legacy_bucket = DATALAKE_BASE_URI.format(env)
    output_bucket = get_nv_data_bucket_uri(env, bucket_type="processed", schema=db, strip=False)
    if output_bucket != legacy_bucket:
        if db.startswith("core") and table == "blobs":
            df_cdc = df_cdc.withColumn("data", decode(unbase64(df_cdc.data), "utf-8"))
        df_masked = pii.mask(spark, df_cdc, pii_fields)
    cdc_latest = cdc.get_latest_records(df_masked, primary_keys)
    return cdc_latest


def get_cdc(spark, bucket, schema, table, date, time, primary_keys):
    df_cdc = spark.read.parquet(f"{bucket}/cdc_processed/{schema}/{table}/cdc_date={date}/cdc_time={time}")
    cdc_latest = cdc.get_latest_records(df_cdc, primary_keys)
    return cdc_latest


def compare_data(spark, env, schema, table, primary_keys, pii_fields, start_time=None, end_time=None):
    """
    Compares data between MySQL and Delta tables for consistency.

    Args:
        spark (SparkSession): The SparkSession object.
        env (str): The environment name.
        schema (str): The schema name.
        table (str): The table name.
        primary_keys (dict): The primary keys in {'column_name': 'data_type'} format.
        pii_fields (list): The list of fields that contain PII data.
        start_time (str, optional): The start time for the data comparison. Defaults to None.
        end_time (str, optional): The end time for the data comparison. Defaults to None.

    Raises:
        Exception: If there are mismatched records between MySQL and Delta tables.

    Returns:
        None
    """
    try:
        ts = start_time
        date = date_util.date_from_ts(ts)
        time = date_util.time_from_ts(ts)
        gs_bucket_input = get_nv_data_bucket_uri(env, bucket_type="processed", schema=schema, strip=False)
        latest_processed_df = process_cdc(spark, schema, table, ts, date, time, num_intervals, pii_fields, primary_keys)
        if latest_processed_df is None:
            logging.info("No data to compare.")
            raise ValueError("NoRawDataError: No data to compare")
        latest_processed_df.cache()
        pipeline_processed_df = get_cdc(spark, gs_bucket_input, schema, table, date, time, primary_keys).cache()
        delta_df = spark.read.format("delta").load(f"gs://nv-data-{env}-data-lake/db/{schema}/{table}")
        if 'updated_at' in delta_df.columns:
            delta_df = delta_df.filter(
                ((col("created_at") >= start_time) & (col("created_at") <= end_time))
                | ((col("updated_at") >= start_time) & (col("updated_at") <= end_time))
            )
        else:
            delta_df = delta_df.filter((col("created_at") >= start_time) & (col("created_at") <= end_time))
        delta_df.cache()
        common_columns_cdc = set(latest_processed_df.columns).intersection(set(pipeline_processed_df.columns))
        latest_processed_df_count = latest_processed_df.count()
        pipeline_processed_df_count = pipeline_processed_df.count()
        delta_df_count = delta_df.count()
        stats = [
            Row(
                table_name=table,
                schema=schema,
                stat_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                records_from_raw=latest_processed_df_count,
                records_from_processed=pipeline_processed_df_count,
                records_from_delta=delta_df_count,
                execution_date=start_time,
            )
        ]
        new_stats_df = spark.createDataFrame(stats)
        gcs_path = f"gs://nv-data-{env}-data-lake/data_integrity/{schema}/{table}"
        if DeltaTable.isDeltaTable(spark, gcs_path):
            deltaTable = DeltaTable.forPath(spark, gcs_path)
            deltaTable.alias("existing").merge(
                new_stats_df.alias("new"), "existing.execution_date = new.execution_date"
            ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()
        else:
            new_stats_df.write.format("delta").mode("overwrite").save(gcs_path)
        latest_processed_df.unpersist()
        pipeline_processed_df.unpersist()
        delta_df.unpersist()
        if latest_processed_df_count != pipeline_processed_df_count:
            raise Exception("The counts of latest_processed_df and pipeline_processed_df do not match.")

        if compare_dataframes(latest_processed_df, pipeline_processed_df, common_columns_cdc):
            logging.info("Data between raw and cdc-processed tables is consistent.")
        else:
            raise Exception("Data between raw and cdc-processed tables is not consistent.")
    except ValueError:
        logging.info("No data to compare.")
        pass
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    env, schema, table, conn_dict_str, primary_keys_str, pii_fields_str, start_time, end_time = sys.argv[1:]
    primary_keys = json.loads(primary_keys_str.replace("'", '"'))
    if conn_dict_str != "None":
        conn_dict = json.loads(conn_dict_str.replace("'", '"'))
        password = codecs.decode(conn_dict["password"], "rot-13")
        conn_dict["password"] = password
    else:
        conn_dict = None
    pii_fields = [f.strip() for f in pii_fields_str[1:-1].replace("'", "").split(",")]

    spark = SparkSession.builder.getOrCreate()
    compare_data(spark, env, schema, table, primary_keys, pii_fields, start_time, end_time)
    spark.stop()
