import logging
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number

from common.stringcase import snake_case
from metadata.google_ads import GOOGLE_ADS_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

CREATED_AT = "segments_date"
SYSTEM_ID = "system_id"


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which Google Ads entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Google Ads entity (e.g. ad)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/google_ads/delta/{entity}"


def _get_obj_path(gcs_bucket, entity):
    """
    Returns path at which incremental change data of Google Ads entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Google Ads entity (e.g. ad)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}-raw/google_ads/objects/{entity}"


def _get_object(spark, path, date_str, primary_columns):
    """
    Fetches incremental change data of Google Ads entity for a specified updated date.

    :param spark:             Spark session
    :param path:              GCS path at which entity change data is stored
    :param date_str:          Updated date filter for entity change data
    :param primary_columns:   Primary columns in list
    :return:                  Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, month_fmt))
    )
    # get latest update by id
    df = (
        df.withColumn(
            "row_number", row_number().over(Window.partitionBy([SYSTEM_ID, *primary_columns]).orderBy(desc(CREATED_AT)))
        )
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Google Ads entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)
    logging.info(f"Results are save in {path}")


def merge(spark, obj_df, path, primary_columns):
    """
    Merges entity data into an existing Delta table.

    :param spark:       Spark session
    :param obj_df:      Entity dataframe
    :param path:        Delta table path
    :param primary_columns:  List of primaru colimns
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    merge_str = ""
    for key in primary_columns:
        if merge_str:
            merge_str += f" and delta.{key} = cdc.{key}"
        else:
            merge_str = f"delta.{key} = cdc.{key}"
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} and {merge_str}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{CREATED_AT} > delta.{CREATED_AT}")
        .whenNotMatchedInsertAll()
        .execute()
    )
    logging.info("Results are updated")


def merge_delta(spark, gcs_bucket, entity, date_str):
    primary_columns = [snake_case(c) for c in GOOGLE_ADS_CONFIG[entity]["primary_columns"]]

    obj_path = _get_obj_path(gcs_bucket, entity)
    obj_df = _get_object(spark, obj_path, date_str, primary_columns)
    if obj_df.count() == 0:
        logging.info("No records to update")
        return

    delta_path = _get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path, primary_columns)
    else:
        logging.info("Table does not exist, creating...")
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, gcs_bucket, entity, date_str)
    spark.stop()
