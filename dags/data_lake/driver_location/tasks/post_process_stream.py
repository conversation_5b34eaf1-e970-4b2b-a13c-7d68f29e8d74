import sys

from pyspark.sql import SparkSession
from pyspark.sql.functions import col, floor, from_unixtime, lit


def postprocess_stream(spark, gs_bucket, date):
    """
    Post-process the driver location stream:
    1) Compact the parquet files
    2) Transform the data into a query-friendly format partitioned by ("system_id", "date")
       Note: "date" is derived from "checked_in_at", which represents the time at which the driver GPS location is
             sampled and unaffected by client-side tampering of the device date & time
    """
    src = f"gs://{gs_bucket}/driver_location_stream/data"
    dst = f"gs://{gs_bucket}/driver_location"

    partition_columns = ("system_id", "date")

    driver_loc = spark.read.parquet(src)
    filtered = driver_loc.where(driver_loc.date == date)
    transformed = (
        filtered.drop("time")
        .withColumn("checked_in_at_secs", lit(floor(col("checked_in_at")) / 1000))
        .withColumn("date", from_unixtime("checked_in_at_secs", "yyyy-MM-dd"))
        .drop("checked_in_at_secs")
    )

    (
        transformed.repartition(*partition_columns)
        .write.mode("append")
        .format("parquet")
        .partitionBy(*partition_columns)
        .save(dst)
    )


if __name__ == "__main__":
    gs_bucket, date = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    postprocess_stream(spark, gs_bucket, date)
    spark.stop()
