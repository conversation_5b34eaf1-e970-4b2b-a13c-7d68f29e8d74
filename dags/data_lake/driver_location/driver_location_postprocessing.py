from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")
gs_bucket = f"nv-data-{env}-data-lake"

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": "2019-11-26",
    "retries": 1,
    "retry_delay": timedelta(minutes=15),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
    dag_id="datalake_driver_location_postprocessing",
    default_args=default_args,
    schedule_interval="0 2 * * *",
    max_active_runs=1,
    catchup=False,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["data_lake"],
) as dag:
    task_id = "driver_location_stream_postprocessing"
    main_task = SparkSubmitOperator(
        task_id="driver_location_stream_postprocessing",
        name=kebab_case(task_id),
        execution_timeout=Timeout.ONE_HOUR,
        application=f"{tasks_path}/post_process_stream.py",
        application_args=[gs_bucket, "{{ ds }}"],
        conn_id="spark_default",
        conf={"spark.executor.instances": "2", "spark.executor.memory": "5g", "spark.driver.memory": "5g"},
    )
