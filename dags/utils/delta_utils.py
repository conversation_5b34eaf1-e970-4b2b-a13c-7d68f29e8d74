import json
import logging
import pendulum
from airflow.api.common.experimental import get_task_instance
from common.utils.nv_obs import get_nv_data_bucket_uri, get_obs_client, fetch_delta_checkpoint, fetch_latest_object

def check_and_extract_delta_metrics(env, schema, table, delta_task_id, is_pii, **context):
    """Checks if the upstream Delta merge task successfully committed a new Delta versionand extracts delta metrics."""
    client = get_obs_client()
    obs_db_path = get_nv_data_bucket_uri(env, bucket_type="db", schema="legacy", strip=True)
    if is_pii:
        obs_db_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True)

    obs_bucket, base_path = obs_db_path.split("/")
    prefix = f"{base_path}/{schema}/{table}/_delta_log"
    last_checkpoint_prefix = f"{base_path}/{schema}/{table}/_delta_log/_last_checkpoint"
    logging.info(f"Checking in '{obs_bucket}/{prefix}'")

    last_checkpoint_parquet = fetch_delta_checkpoint(client, obs_bucket, prefix=last_checkpoint_prefix)
    logging.info(f"Most recent .checkpoint.parquet file is {last_checkpoint_parquet}.")
    last_checkpoint_marker = f"{prefix}/{last_checkpoint_parquet}" if last_checkpoint_parquet else None
    logging.info(f"{last_checkpoint_marker}")

    latest_commit_metadata = fetch_latest_object(client, obs_bucket, prefix, marker=last_checkpoint_marker)

    latest_delta_log_prefix = latest_commit_metadata["key"]
    logging.info(f"Last delta log: {latest_delta_log_prefix}")
    last_delta_log_update = pendulum.from_format(
        latest_commit_metadata["lastModified"], "YYYY/MM/DD HH:mm:ss", tz="UTC"
    )

    delta_ti = get_task_instance.get_task_instance(context["dag"].dag_id, delta_task_id, context["execution_date"])
    delta_start_date = pendulum.instance(delta_ti.start_date).in_tz("UTC")

    if last_delta_log_update < delta_start_date:
        raise Exception("Delta merge not found.")

    response = client.getObject(obs_bucket, latest_delta_log_prefix, loadStreamInMemory=True)
    logging.debug(response)
    decoded_string = response.body.buffer.decode("utf-8")
    latest_log_data = [json.loads(line) for line in decoded_string.strip().split("\n") if line.strip()]
    logging.info(latest_log_data)
    latest_version_id = latest_delta_log_prefix.split("/")[-1].split(".")[0]
    latest_commit = next((entry for entry in latest_log_data if "commitInfo" in entry), None)

    if latest_commit and "commitInfo" in latest_commit:
        operation_metrics = latest_commit["commitInfo"].get("operationMetrics", {})
    else:
        logging.error(f"No commitInfo found in the latest log: {latest_delta_log_prefix}")
        operation_metrics = {}

    metrics_dict = {
        "version": latest_version_id,
        "numTargetRowsCopied": int(operation_metrics.get("numTargetRowsCopied", 0)),
        "numTargetRowsDeleted": int(operation_metrics.get("numTargetRowsDeleted", 0)),
        "numTargetFilesAdded": int(operation_metrics.get("numTargetFilesAdded", 0)),
        "numTargetRowsInserted": int(operation_metrics.get("numTargetRowsInserted", 0)),
        "numTargetRowsUpdated": int(operation_metrics.get("numTargetRowsUpdated", 0)),
        "numSourceRows": int(operation_metrics.get("numSourceRows", 0)),
        "numOutputRows": int(operation_metrics.get("numOutputRows", 0)),
        "numTargetFilesRemoved": int(operation_metrics.get("numTargetFilesRemoved", 0)),
        "schema": schema,
        "table": table,
        "execution_date": str(context["execution_date"]),
    }
    context['ti'].xcom_push(key=f'delta_metrics_{schema}_{table}', value=metrics_dict)
    return metrics_dict


def clear_delta_callback(delta_task, context):
    # Temporarily disable alerts when clear delta task fails.
    # notif.chat.send_ti_failure_alert(context)
    delta_task.clear(start_date=context["execution_date"], end_date=context["execution_date"], downstream=True)