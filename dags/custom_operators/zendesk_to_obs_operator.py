import json
import logging

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.zendesk.hooks.zendesk import ZendeskHook


class ZendeskToObsOperator(BaseOperator):
    """
    Zendesk to OBS Operator

    Fetches data from Zendesk and stores it in Object Block Storage (OBS). Uses the Zendesk incremental export API
    (https://developer.zendesk.com/rest_api/docs/support/incremental_export). Data will be stored in the path
    {obs_bucket}/{obs_folder_path}/system_id={system_id}/nv_updated_date={date}

    :param zd_entity_config:    Zendesk entity config to fetch data (e.g. tickets)
    :type zd_entity_config:     dict
    :param obs_bucket:          OBS bucket to upload file to
    :type obs_bucket:           string
    :param obs_folder_path:     OBS folder path
    :type obs_folder_path:      string
    :param zd_conn_id:          Zendesk connection ID
    :type zd_conn_id:           string
    """

    VALID_ENTITIES = {
        "brands",
        "groups",
        "organizations",
        "satisfaction_ratings",
        "ticket_fields",
        "ticket_forms",
        "ticket_metric_events",
        "tickets",
        "users",
    }
    PARTITION_COLUMN = "nv_updated_date"

    def __init__(self, zd_entity_config, obs_bucket, obs_folder_path, zd_conn_id="zendesk_default", **kwargs):
        super().__init__(**kwargs)
        entity = zd_entity_config["entity"]
        if entity not in self.VALID_ENTITIES:
            raise ValueError(f"Unknown zendesk entity: {entity}")
        self.zd_entity_config = zd_entity_config
        self.obs_bucket = obs_bucket
        self.obs_folder_path = obs_folder_path
        self.zd_conn_id = zd_conn_id

    def _fetch_latest_data(self, exec_date):
        """
        Fetches data from Zendesk and returns a pandas DataFrame.

        :param execution_date:      Task execution date
        :type execution_date:       datetime
        :return:                    Pandas DataFrame containing data
        :rtype:                     pandas.DataFrame
        """
        entity = self.zd_entity_config["entity"]
        logging.info(f"Fetching Zendesk {entity} data")
        zendesk_hook = ZendeskHook(self.zd_conn_id)
        zendesk_path_fmt = self.zd_entity_config["path"]
        zendesk_path = zendesk_path_fmt.format(entity=entity)
        fetch_all = self.zd_entity_config["fetch_type"] == "all"
        query_params = {}
        if not fetch_all:
            query_params = {"start_time": exec_date.int_timestamp}
        # https://airflow.apache.org/docs/apache-airflow-providers-zendesk/4.7.1/changelog.html#id33
        response = zendesk_hook.call(zendesk_path, query=query_params)
        logging.info("Fetched data")
        if not response.get(entity):
            logging.info("No records retrieved")
            return pd.DataFrame()
        return pd.DataFrame(response[entity])

    def _normalize_schema(self, df):
        """
        Normalizes the schema of a DataFrame to match the schema defined in the entity config.
        :param df:                  Pandas DataFrame containing data
        :type df:                   pandas.DataFrame
        :return:                    Pandas DataFrame with normalized schema
        :rtype:                     pandas.DataFrame
        """
        entity_schema = self.zd_entity_config.get("schema", {})
        df_columns = df.columns
        df_schema = {column: entity_schema[column] for column in df_columns}
        df = df.astype(df_schema)
        nested_columns = self.zd_entity_config.get("nested_columns", set()).intersection(df_columns)
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        updated_at = self.zd_entity_config["updated_at_field"]
        df[self.PARTITION_COLUMN] = df[updated_at].dt.date
        # filter by updated_at values for endpoints that fetch all data
        # convert exec_date from pendulum DateTime to pandas datetime
        from_dt_dt = pd.to_datetime(from_dt.to_datetime_string(), infer_datetime_format=True)
        filtered_df = df[df[self.PARTITION_COLUMN] >= from_dt_dt]
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        filtered_df = filtered_df.dropna(axis=1, how="all")
        logging.info(f"No. of records: {len(filtered_df.index)}")
        if filtered_df.empty:
            return
        return filtered_df

    def _save_to_obs(self, df):
        """
        Saves a DataFrame to OBS in parquet format.
        :param df:                  Pandas DataFrame containing data
        :type df:                   pandas.DataFrame
        :return:                    None
        """
        system_id = self.zd_entity_config["system_id"]
        obs_hook = HuaweiOBSHook()
        logging.info("Uploading to OBS bucket")
        for partition_date, group_df in df.groupby(self.PARTITION_COLUMN):
            logging.info(f"Writing records into nv_updated_date={partition_date} partition...")
            # Only need this column to partition the data
            group_df = group_df.drop(columns=[self.PARTITION_COLUMN])
            df_parq_obj = group_df.to_parquet(compression="snappy", engine="pyarrow", index=False)
            logging.info("Uploading Zendesk data to OBS")
            file_name = uuid.uuid4().hex
            obs_hook.streaming_upload(
                self.obs_bucket,
                f"{self.obs_folder_path}/system_id={system_id}/nv_updated_date={partition_date}/{file_name}.parquet",
                df_parq_obj,
            )
        logging.info("Uploaded Zendesk data to OBS")

    def execute(self, context):
        """
        Fetches incremental data from Zendesk for a particular entity (e.g. tickets) and stores it in OBS in parquet
        format.

        :param context: Task context
        """
        exec_date = context["execution_date"]
        df = self._fetch_latest_data(exec_date)
        if df.empty:
            logging.info("No records retrieved")
            return
        df = self._normalize_schema(df, exec_date)
        self._save_to_obs(df)
