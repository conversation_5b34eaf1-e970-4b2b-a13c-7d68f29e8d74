import hashlib
import logging

import numpy as np
import pandas as pd
from airflow.models import Base<PERSON>perator, SkipMixin

from custom_hooks.sonarqube_hook import SonarQubeHook



class SonarQubeToGCSOperator(BaseOperator, SkipMixin):
    """
    SonarQube to GCS Operator

    Fetches data from SonarQube (https://docs.sonarqube.org/8.9/extension-guide/web-api) and stores it in Google Cloud
    Storage (GCS).

    Data will be stored in the path {gcs_bucket}/{gcs_folder_path}/nv_updated_date={date}

    :param entity_config:        SonarQube entity config to fetch data (e.g. measures)
    :type entity_config:         dict
    :param gcs_bucket:           GCS bucket to upload data file to
    :type gcs_bucket:            string
    :param gcs_folder_path:      GCS folder path
    :type gcs_folder_path:       string
    :param sonarqube_conn_id:    SonarQube connection ID
    :type sonarqube_conn_id:     string
    :param gcs_conn_id:          GCS connection ID
    :type gcs_conn_id:           string
    """

    PARTITION_COLUMN = "nv_updated_date"

    def __init__(
        self,
        entity_config,
        gcs_bucket,
        gcs_folder_path,
        sonarqube_conn_id="sonarqube_default",
        gcs_conn_id="google_cloud_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.sonarqube_conn_id = sonarqube_conn_id
        self.gcs_conn_id = gcs_conn_id

    def execute(self, context):
        """
        Fetches SonarQube entity data and stores it in GCS in parquet format.

        :param context: Task context
        """
        entity = self.entity_config["entity"]
        start_date = context["execution_date"].strftime("%Y-%m-%dT%H:%M:%S%z")
        end_date = context["next_execution_date"].strftime("%Y-%m-%dT%H:%M:%S%z")
        logging.info(f"Fetching SonarQube {entity} data from {start_date} to {end_date}")

        if entity == "measures":
            df = self._fetch_all_measures_data(start_date, end_date)
        else:
            logging.error(f"{entity} is not supported.")
            return

        if df.empty:
            logging.info("No records retrieved")
            return

        logging.info(f"No. of records: {len(df.index)}")
        df = self._normalize_schema(df)
        self._save_to_gcs(df)

    def _fetch_all_measures_data(self, start_date, end_date):
        """
        Fetches data for the "measures" SonarQube entity for all components in a given date range.

        :param start_date:     Filter for measures created after this date
        :param end_date:       Filter for measures created before this date
        :return:               SonarQube entity data
        :type:                 pandas.DataFrame
        """
        component_keys = self._fetch_all_components()
        data = []
        for key in component_keys:
            logging.info(f"Fetching data for component_key: {key}")
            data.extend(self._fetch_measures_data_by_component(key, start_date, end_date))
        return pd.DataFrame(data)

    def _fetch_all_components(self):
        """
        Fetches the component keys for all projects with ":master" in their name.

        :return:               Component keys for all master branch projects.
        :type:                 list
        """
        query_params = {"ps": 500, "q": ":master", "qualifiers": "TRK"}
        sonarqube_hook = SonarQubeHook(self.sonarqube_conn_id)
        data = sonarqube_hook.get_data("components/search", query_params)
        return [c["key"] for c in data["components"]]

    def _fetch_measures_data_by_component(self, component_key, start_date, end_date):
        """
        Fetches data for the "measures" SonarQube entity for a given date range and component key.

        :param component_key:        Component key of the project to retrieve measures data for
        :param start_date:           Filter for measures created after this date
        :param end_date:             Filter for measures created before this date
        :return:                     List of dicts containing measures data for each created_at timestamp
        :type:                       list
        """

        metrics = [k for k in self.entity_config["schema"].keys() if k not in self.entity_config["non_metric_fields"]]
        query_params = {
            "component": component_key,
            "metrics": ",".join(metrics),
            "from": start_date,
            "to": end_date,
        }
        sonarqube_hook = SonarQubeHook(self.sonarqube_conn_id)
        data = sonarqube_hook.get_data(self.entity_config["path"], query_params)
        return _flatten_measures_data(component_key, data)

    def _normalize_schema(self, df):
        """
        Normalizes dataframe by dropping null columns and updating schema.

        :param df:        pandas DataFrame to be normalized
        :return:          pandas.DataFrame
        """
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        null_columns = df.columns[df.isna().all()].tolist()
        df.drop(null_columns, axis=1, inplace=True)

        schema = self.entity_config.get("schema", {})
        columns = df.columns
        try:
            df_schema = {column: schema[column] for column in columns}
        except KeyError as e:
            new_column = str(e).strip("'")
            raise KeyError(
                f"Data type for '{new_column}' is undefined."
                f"Sample values: {df[df[new_column].notnull()][new_column][:5].tolist()}"
            ) from e
        df = df.astype(df_schema)

        df[self.PARTITION_COLUMN] = df[self.entity_config["updated_at_field"]].dt.date
        return df

    def _save_to_gcs(self, df):
        """
        Writes df to GCS bucket in parquet format.

        :param df:  Data to be saved
        :type df:   pandas.DataFrame
        :return:    None
        """
        dirname = f"gs://{self.gcs_bucket}/{self.gcs_folder_path}"
        logging.info("Uploading to GCS bucket")
        df.to_parquet(
            dirname, compression="snappy", engine="pyarrow", index=False, partition_cols=[self.PARTITION_COLUMN]
        )
        logging.info(f"Uploaded SonarQube data to GCS '{dirname}'")


def _flatten_measures_data(component_key, data):
    """
    Converts raw data JSON blob into a flattened format suitable for converting into Pandas dataframe.

    :param component_key:        Component key of the project
    :param data:                 Raw JSON blob data
    :return:                     List of flattened data
    """
    # Use created_at as key so that we can efficiently identify and append to the existing a row for a given date.
    measures_by_date = {}
    for measure in data["measures"]:
        metric_name = measure["metric"]
        for history_row in measure["history"]:
            created_at = history_row["date"]
            metric_value = history_row.get("value", np.nan)
            if created_at in measures_by_date:
                measures_by_date[created_at][metric_name] = metric_value
            else:
                measures_by_date[created_at] = {
                    # Manually generate ID as it is not provided by SQ. Using a hash of the concatenation of created_at
                    # and component_key because they uniquely identify a row.
                    "id": hashlib.sha256(f"{created_at}_{component_key}".encode("utf-8")).hexdigest(),
                    "project": component_key,
                    metric_name: metric_value,
                    "created_at": created_at,
                }
    # Remove created_at key.
    return [v for k, v in measures_by_date.items()]