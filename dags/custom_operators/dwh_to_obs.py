import logging
import sys

from pyspark.sql import SparkSession


def load(spark, input_path):
    """
    Loads a data warehouse parquet tables as a tempoarary view called "input_table".
    Note that this does not support versioned parquet tables.

    :param spark:              Spark session
    :param input_path:         Path to DWH table.
    :return:                   None
    """
    spark.read.option("mergeSchema", "true").parquet(input_path).createOrReplaceTempView("input_table")


def query(spark, sql):
    """
    Runs the SQL query on the data warehouse.
    Tables referenced need to either be "input_table" or tables that already have Hive metastore integration set up.

    :param spark:       Spark session
    :param sql:         SQL query
    :return:            Spark dataframe of query results
    """
    return spark.sql(sql)


def export(df, obs_output_path):
    """
    Writes the input Spark dataframe as CSV to the specified GCS path.

    :param df:                  Spark dataframe to be exported
    :param obs_output_path:     GCS path to save CSV file to
    """
    df_pandas = df.toPandas()
    if df_pandas.empty:
        logging.info("No records to export.")
        return

    df_pandas.to_csv(f"{obs_output_path}.csv", index=False)
    logging.info("Exported to GCS")


if __name__ == "__main__":
    input_path, sql, obs_output_path = sys.argv[1:]

    spark = SparkSession.builder.enableHiveSupport().getOrCreate()
    if input_path:
        load(spark, input_path)
    df = query(spark, sql)
    export(df, obs_output_path)

    spark.stop()
