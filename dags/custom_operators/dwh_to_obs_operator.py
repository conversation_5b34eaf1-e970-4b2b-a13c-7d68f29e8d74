from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator


class DWHToOBSOperator(SparkSubmitOperator):
    """Data Warehouse to OBS Operator

    Executes an SQL query on the data warehouse and writes the result to a CSV file
    in Huawei Cloud Object Storage (OBS)

    The bucket is fixed to obs://nv-data-services, which the OBS proxy is set up for.
    See https://bitbucket.ninjavan.co/projects/DEVOPS/repos/gcs-proxy for details.

    :param conf:            Spark config
    :param sql:             SQL query
    :type sql:              string
    :param output_path:     GCS output path
    :type output_path:      string
    """

    SPARK_MAIN_PY_FILE = "local:///opt/airflow/dags/custom_operators/dwh_to_obs.py"
    DEFAULT_SPARK_CONF = {
        "spark.executor.instances": "2",
        "spark.executor.memory": "5g",
        "spark.driver.memory": "2g",
        "spark.sql.shuffle.partitions": "1",
        "spark.databricks.delta.schema.autoMerge.enabled": "true",
        "spark.sql.parquet.mergeSchema": "true",
    }

    def __init__(self, conf, sql, output_path, input_path="", conn_id="spark_default", **kwargs):
        super(DWHToOBSOperator, self).__init__(
            application=DWHToOBSOperator.SPARK_MAIN_PY_FILE,
            application_args=[input_path, sql, output_path],
            conf=conf if conf else DWHToOBSOperator.DEFAULT_SPARK_CONF,
            **kwargs,
        )
        self.input_path = input_path
        self.sql = sql
        self.output_path = output_path
        self.conn_id = conn_id
