apiVersion: v1
kind: Pod
metadata:
  name: fm-similar-address-grouping-group
  # namespace set in fm_similar_address_grouping.py
  #namespace: data-science
spec:
  containers:
  - name: fm-similar-address-grouping-group
    # image set in fm_similar_address_grouping.py
    imagePullPolicy: Always
    # args set in fm_similar_address_grouping.py
    resources:
      requests:
        cpu: 1000m
        memory: 25G
    volumeMounts:
    - name: storage-service-account-secret # storage-read-secret #
      readOnly: true
      mountPath: /mnt/secret-stuff #/etc/storage-service-account-secret
    - name: shared-memory
      mountPath: /dev/shm
  nodeSelector:
    group: data-lake
  tolerations:
    - key: group
      operator: Equal
      value: data-lake
  restartPolicy: Never

  volumes:
  - name: storage-service-account-secret # storage-read-secret #
    secret:
      secretName: storage-service-account-key # storage-read-only-key #
  - name: shared-memory
    emptyDir:
      medium: Memory
