import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.providers.google.cloud.sensors.gcs import GCSObjectExistenceSensor
from airflow.providers.http.operators.http import SimpleHttpOperator

from common.stringcase import kebab_case
from data_science.ds_common.notifications_chat import send_ds_failure_alert
from data_science.fm_similar_address_grouping.tasks.config import dag_configs
from data_warehouse.tasks.main import base
from metadata import data_science, data_science_tables
from metadata.constants import SystemID, Timeout

DEFAULT_SCHEDULE = "* 20 * * *"  # Daily 4am SGT

aaa_client_secret = Variable.get("aaa_client_secret")
env = Variable.get("env")
kube_template_path = str(Path(__file__).resolve().parent / "kubernetes/address_grouping.yml")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 6, 14),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": send_ds_failure_alert,
}


def _create_dag(dag_id, dag_config, schedule_interval):
    with DAG(
        catchup=False,
        dag_id=dag_id,
        default_args=default_args,
        max_active_runs=1,
        schedule_interval=schedule_interval,
        tags=["data_science"],
    ) as dag:
        start = DummyOperator(task_id="start")
        mid = DummyOperator(task_id="mid", trigger_rule="all_done")
        end = DummyOperator(task_id="end")

        # Works because only 1 task config given
        ingest_task_config = dag_config["ingest_task_configs"][0]

        task_name = ingest_task_config.task_name

        input_args = {
            "env": env,
            "extraction_date": "{{ data_interval_start.format('YYYY-MM-DD') }}",  # ystd
            "save_date": "{{ data_interval_end.format('YYYY-MM-DD') }}",  # today
        }

        ingest_task = SparkSubmitOperator(
            task_id=f"{task_name}_{SystemID.GL}",
            name=kebab_case(task_name),
            application=f"{tasks_path}/{ingest_task_config.py_file}",
            application_args=[json.dumps(input_args, separators=(",", ":"))],
            conn_id="spark_default",
            conf={
                **spark_conf[tables_to_size.get(task_name, "small")],
                "spark.sql.adaptive.enabled": True,
                "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
            },
            execution_timeout=ingest_task_config.execution_timeout,
            params={"enable_full_run": False, "alert_channel": dag_configs[dag_id].get("alert_channel")},
        )

        start >> ingest_task

        for system_id in ingest_task_config.system_ids:
            input_file = (
                f"{data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).PROCESSED_DATALAKE}"
                + f"/{input_args['save_date']}_{system_id}.parquet"
            )
            intermediate_folder = f"{data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).INTERMEDIATE_TABLES}"
            output_folder = f"{data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).SIMILAR_ADDRESSES}/"

            gcs_base = data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env)._PROJECT_BASE
            input_file_without_gs = f"{gcs_base}/datalake/{input_args['save_date']}_{system_id}.parquet"
            output_file_without_gs = f"{gcs_base}/similar_addresses/{input_args['save_date']}_{system_id}.parquet"

            exist_task = GCSObjectExistenceSensor(
                task_id=f"check_ingest_data_{system_id}",
                bucket=f"nv-data-{env}-datascience",
                object=input_file_without_gs,
                params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
                google_cloud_conn_id="google_cloud_default",
                execution_timeout=Timeout.ONE_MINUTE,
                retries=1,
            )

            container_args = [
                input_file,  # image code will refer to it as folder
                intermediate_folder,
                output_folder,
                system_id,
                input_args["save_date"],
            ]

            # For local testing,
            # change in_cluster to False from True and
            # provide config_file param and
            # change namespace to 'data-science' from env
            group_task = KubernetesPodOperator(
                task_id=f"group_addresses_{system_id}",
                pod_template_file=kube_template_path,
                in_cluster=True,
                startup_timeout_seconds=600,
                image=f"asia-southeast1-docker.pkg.dev/ninja-van-management/docker/fm-similar-address-grouping:{env}-latest",
                namespace=env,
                arguments=container_args,
                params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
            )

            check_task = GCSObjectExistenceSensor(
                task_id=f"check_output_data_{system_id}",
                bucket=f"nv-data-{env}-datascience",
                object=output_file_without_gs,
                params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
                google_cloud_conn_id="google_cloud_default",
                execution_timeout=Timeout.ONE_MINUTE,
                retries=1,
            )

            ingest_task >> exist_task >> group_task >> check_task >> mid

        # Publish to hive
        hive_metastore_config = base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=())
        hive_metastore_app = "local://" + str(Path(__file__).resolve().parent.parent / "ds_common/hive_metastore.py")

        hive_publish_task = SparkSubmitOperator(
            task_id="publish_hive_metastore",
            name=kebab_case("publish_hive"),
            application=hive_metastore_app,
            application_args=[
                env,
                hive_metastore_config.hive_schema,
                json.dumps(hive_metastore_config.partition_columns),
                "SIMILAR_ADDRESSES",
            ],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "1g",
                "spark.driver.memory": "1g",
                "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
            },
            execution_timeout=Timeout.ONE_HOUR,
            params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
        )

        # Get authentication token
        aaa_auth_token_task_id = "get_aaa_auth_token"
        get_aaa_auth_token_task = SimpleHttpOperator(
            task_id=aaa_auth_token_task_id,
            method="POST",
            http_conn_id="ninjavan-api",
            endpoint="global/aaa/login?grant_type=CLIENT_CREDENTIALS",
            data=json.dumps({"clientId": "AIRFLOW", "clientSecret": aaa_client_secret}),
            response_filter=lambda response: response.json()["accessToken"],
            response_check=lambda response: response.status_code == 200,
            do_xcom_push=True,
            params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
        )

        # Publish kafka
        kafka_publish_task = SimpleHttpOperator(
            task_id="publish_kafka_messages",
            method="POST",
            http_conn_id="ninjavan-api",
            endpoint="global/dwh/1.0/reports/similar-addresses",
            headers={
                "Authorization": "Bearer "
                + f"{{{{ ti.xcom_pull(dag_id='{dag_id}', task_ids='get_aaa_auth_token') }}}}",
                "Content-Type": "application/json",
            },
            data=json.dumps({"execution_date": input_args["save_date"]}),
            response_check=lambda response: response.status_code == 200,
            params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
        )

        mid >> hive_publish_task >> get_aaa_auth_token_task >> kafka_publish_task >> end

    globals()[dag_id] = dag
    return dag


dag_id = data_science.FMSimilarAddressGroupingDAG.DAG_ID
_create_dag(dag_id, dag_configs[dag_id], dag_configs[dag_id].get("schedule", DEFAULT_SCHEDULE))
