"""
Data Science FM similar address grouping DAGs config mapping
"""
from dataclasses import dataclass
from importlib import import_module

# from data_science.tasks.pod_auto_validation_tasks import pod_auto_validation_base
from metadata import data_science


@dataclass(frozen=True)
class KubeTaskConfig:
    task_name: str


def get_airflow_config_list(tasks_class):
    # Convert class to list
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]

    airflow_config_list = []
    for t in tasks:
        task_module = import_module(f"data_science.fm_similar_address_grouping.tasks.{t}")
        airflow_config_list.append(task_module.airflow_config)
    return airflow_config_list


def get_kube_config_list(tasks_class):
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]
    kube_config_list = []
    for t in tasks:
        # kube_config_list.append(pod_auto_validation_base.KubeTaskConfig(task_name=t))
        kube_config_list.append(KubeTaskConfig(task_name=t))
    return kube_config_list


dag_configs = {
    data_science.FMSimilarAddressGroupingDAG.DAG_ID: {
        "ingest_task_configs": get_airflow_config_list(data_science.FMSimilarAddressGroupingDAG.IngestTask),
        "group_task_configs": get_kube_config_list(data_science.FMSimilarAddressGroupingDAG.GroupTask),
        "schedule": "0 4 * * *",  # Everyday 12pm (hope)
        "alert_channel": "data_science_gchat",  # gchat channel
    }
}
