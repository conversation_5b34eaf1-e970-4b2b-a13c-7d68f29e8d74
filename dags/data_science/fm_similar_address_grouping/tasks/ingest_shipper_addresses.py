import json
import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_science, data_science_tables

airflow_config = base.AirflowConfig(
    py_file=data_science.FMSimilarAddressGroupingDAG.IngestTask.FM_SIMILAR_ADDRESS_GROUPING_INGEST + ".py",
    task_name=data_science.FMSimilarAddressGroupingDAG.IngestTask.FM_SIMILAR_ADDRESS_GROUPING_INGEST,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
)

if __name__ == "__main__":
    # Get the arguments
    input_args = json.loads(sys.argv[1:][0])
    env = input_args["env"]
    extraction_date = input_args["extraction_date"]
    save_date = input_args["save_date"]
    created_month = extraction_date[:-3]

    # Since unmaksed data only exist in prod
    if env == "prod":
        # Set table path
        input_table_path = data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).SOURCE_ADDRESSES

        # Start spark
        spark = SparkSession.builder.getOrCreate()
        spark.sparkContext.setLogLevel("ERROR")

        table = spark.read.format("delta").load(input_table_path).where(f"created_month = '{created_month}'")
        table.createOrReplaceTempView("shipper_addresses")

        base_data = spark.sql(
            f"""
                SELECT
                    id
                    , address1
                    , address2
                    , neighbourhood
                    , locality
                    , region
                    , postcode
                    , lower(country) as system_id
                FROM shipper_addresses
                WHERE
                    country <> 'MM'
                    AND shipper_addresses.deleted_at IS NULL
                    AND DATE(shipper_addresses.created_at) == DATE('{ extraction_date }')
        """
        )
        base_data.cache()

        # Loop and save the file
        system_ids = [
            constants.SystemID.ID,
            constants.SystemID.MY,
            constants.SystemID.PH,
            constants.SystemID.SG,
            constants.SystemID.VN,
        ]
        for system_id in system_ids:
            country_data = base_data.where(f"system_id = '{system_id}'").toPandas()
            if len(country_data) > 0:
                country_data.to_parquet(
                    f"{data_science_tables.FM_SIMILAR_ADDRESS_GROUPING(env).PROCESSED_DATALAKE}/"
                    + f"{save_date}_{system_id}.parquet"
                )

        # stop spark session
        spark.stop()
