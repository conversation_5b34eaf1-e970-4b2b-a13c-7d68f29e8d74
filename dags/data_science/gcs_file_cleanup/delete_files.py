import re
from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook

from data_science.ds_common.notifications_chat import send_ds_failure_alert
from metadata.constants import Timeout

env = Variable.get("env")

dag_alert_channel = "data_science_gchat"
dag_id = "data_science_gcs_monthly_cleanup"
dag_schedule = "0 10 28 * *"  # Monthly on the 28 at 6pm

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 1, 1),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": send_ds_failure_alert,
}

###################
# GENERAL WARNING #
###################
# The prefix applies to subsets
# if the prefix is folder
# it will match foler_2 as well

# Note that folder may disappear after the process
# This happens if folder is empty or
# if no delimiter set

################
# Instructions #
################

# Delete type 1
# delete files inside a fixed folder
# use delimiter to for specific file type

# Delete type 2
# For a file / folder with format
# folder/.../folder/xxxxx<date>
# xxxx can be anything
# the time can follow after date

# Delete type 3
# adds regex option for increased flexibility
# delete type 2 is a specific use case of this input
# where the pattern is set to yyyy-mm of the last run date


# Input format for delete type 1 and 2
# Bucket name : [(prefix_name_1, delimiter_name_1), ...,
#                (prefix_name_n, delimiter_name_n)]

# Input format for delete type 3
# Bucket name : [(prefix_name_1, delimiter_name_1, regex_1), ...,
#                (prefix_name_n, delimiter_name_n, regex_n)]

delete_type_1 = {}

delete_type_2 = {
    f"nv-data-{env}-datascience": [
        ("fm_similar_address_grouping/datalake", ".parquet"),
        ("fm_similar_address_grouping/similar_addresses", ".parquet"),
    ],
}

delete_type_3 = {}


def delete_as_given(bucket_name, prefix_name, delimiter):
    hook = GCSHook()
    object_list = hook.list(bucket_name=bucket_name, prefix=prefix_name, delimiter=delimiter)
    for object in object_list:
        hook.delete(bucket_name=bucket_name, object_name=object)
    return None


def delete_with_regex(bucket_name, prefix_name, delimiter, regex):
    hook = GCSHook()
    object_list = hook.list(bucket_name=bucket_name, prefix=prefix_name, delimiter=delimiter)
    reduced_object_list = []
    for object in object_list:
        if re.search(regex, object):
            reduced_object_list.append(object)
    for object in reduced_object_list:
        hook.delete(bucket_name=bucket_name, object_name=object)
    return None


def _create_dag(dag_id, alert_channel, schedule_interval):
    with DAG(
        catchup=False,
        dag_id=dag_id,
        default_args=default_args,
        max_active_runs=1,
        schedule_interval=schedule_interval,
        tags=["data_science"],
    ) as dag:
        start = DummyOperator(task_id="start")
        end = DummyOperator(task_id="end")

        for bucket_name, all_prefix_delim_names in delete_type_1.items():
            for index, prefix_delim_tuple in enumerate(all_prefix_delim_names):
                prefix_name, delimiter = prefix_delim_tuple
                print_objects = PythonOperator(
                    task_id=f"delete_type_1_entry_{index}",
                    python_callable=delete_as_given,
                    params={"alert_channel": alert_channel},
                    retries=1,
                    op_kwargs={"bucket_name": bucket_name, "prefix_name": prefix_name, "delimiter": delimiter},
                )
                start >> print_objects >> end

        for bucket_name, all_prefix_delim_names in delete_type_2.items():
            for index, prefix_delim_tuple in enumerate(all_prefix_delim_names):
                prefix_name, delimiter = prefix_delim_tuple
                print_objects = PythonOperator(
                    task_id=f"delete_type_2_entry_{index}",
                    python_callable=delete_with_regex,
                    params={"alert_channel": alert_channel},
                    retries=1,
                    op_kwargs={
                        "bucket_name": bucket_name,
                        "prefix_name": prefix_name,
                        "delimiter": delimiter,
                        "regex": "{{ data_interval_start.format('YYYY-MM') }}",  # last run date
                    },
                )
                start >> print_objects >> end

        for bucket_name, all_prefix_delim_regex_names in delete_type_3.items():
            for index, prefix_delim_regex_tuple in enumerate(all_prefix_delim_regex_names):
                prefix_name, delimiter, regex = prefix_delim_regex_tuple
                print_objects = PythonOperator(
                    task_id=f"delete_type_3_entry_{index}",
                    python_callable=delete_with_regex,
                    params={"alert_channel": alert_channel},
                    retries=1,
                    op_kwargs={
                        "bucket_name": bucket_name,
                        "prefix_name": prefix_name,
                        "delimiter": delimiter,
                        "regex": regex,
                    },
                )
                start >> print_objects >> end

    globals()[dag_id] = dag
    return dag


_create_dag(dag_id, dag_alert_channel, dag_schedule)
