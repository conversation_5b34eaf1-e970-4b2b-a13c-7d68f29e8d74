"""
Data Science MY Address Verification DAGs config mapping
"""
from importlib import import_module

from metadata import data_science


def get_airflow_config_list(tasks_class):
    # Convert class to list
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]

    airflow_config_list = []
    for t in tasks:
        task_module = import_module(f"data_science.address_verification_my.tasks.{t}")
        airflow_config_list.append(task_module.airflow_config)
    return airflow_config_list


dag_configs = {
    data_science.AddressVerificationAccuracyMYDAG.DAG_ID: {
        # Notify pipeline
        "notify_task_configs": get_airflow_config_list(
            data_science.AddressVerificationAccuracyMYDAG.AccuracyNotificationTask
        ),
        "notify_schedule": "0 3 * * *",  # Everyday at 11am
        "notify_channel": (
            "https://chat.googleapis.com/v1/spaces/AAAACu14740/messages?"
            + "key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&"
            + "token=3S0miGk9EXcNvY7RwHkInap-S_S0MOPhkTrkTUrQ08g"
        ),  # actual gchat
        "alert_channel": "data_science_gchat",  # gchat channel
    },
}
