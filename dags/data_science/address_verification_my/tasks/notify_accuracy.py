import json
import sys

from pyspark.sql import SparkSession

from data_science.address_verification_my.spark_wrapper_functions import true_accuracy, workaround_accuracy
from data_warehouse.tasks.main import base
from metadata import constants, data_science

airflow_config = base.AirflowConfig(
    py_file=data_science.AddressVerificationAccuracyMYDAG.AccuracyNotificationTask.ADDRSS_VERIFICATION_MY_ACCURACY
    + ".py",
    task_name=data_science.AddressVerificationAccuracyMYDAG.AccuracyNotificationTask.ADDRSS_VERIFICATION_MY_ACCURACY,
    system_ids=(constants.SystemID.MY,),
)


if __name__ == "__main__":
    # Get the arguments
    input_args = json.loads(sys.argv[1:][0])
    env = input_args["env"]
    start_date_two_weeks = input_args["start_date_two_weeks"]
    end_date_two_weeks = input_args["end_date_two_weeks"]
    start_date_one_week = input_args["start_date_one_week"]
    end_date_one_week = input_args["end_date_one_week"]
    bi_table = input_args["bi_table"]

    # Set up variables
    ls = [("two_weeks", start_date_two_weeks, end_date_two_weeks), ("one_week", start_date_one_week, end_date_one_week)]
    input_env = "prod"

    # Start spark
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")

    if bi_table == "True":
        true_accuracy(spark, ls, env, end_date_one_week)
    else:
        workaround_accuracy(spark, ls, env, start_date_two_weeks, end_date_one_week)

    # Close spark
    spark.stop()
