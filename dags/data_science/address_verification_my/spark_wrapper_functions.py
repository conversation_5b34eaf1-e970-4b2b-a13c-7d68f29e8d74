from metadata import constants, data_science_tables, versioned_parquet_tables_masked


def true_accuracy(spark, ls, env, end_date_one_week):
    input_env = "prod"
    table_path = (
        f"{versioned_parquet_tables_masked.DataWarehouse(input_env).AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT}"
        + f"/measurement_datetime={end_date_one_week} 18-00-00/system_id={constants.SystemID.MY}"
    )

    # Get table
    table = spark.read.format("parquet").load(table_path)
    table.createOrReplaceTempView("df")

    # Loop for results
    for name, start_date, end_date in ls:
        data = spark.sql(
            f"""
                SELECT
                    COUNT(*) AS total_addresses
                    , SUM(CASE WHEN av_status = 'VERIFIED' THEN 1 ELSE 0 END) AS total_verified_addresses
                    , SUM(CASE WHEN av_status = 'VERIFIED' THEN zone_alignment_flag ELSE 0 END)
                    AS total_correct_verified_addresses
                FROM df
                WHERE
                    system_id = 'my'
                    AND av_mode = 'AUTO'
                    AND av_source = 'MODEL_AV'
                    AND delivery_attempt_hub_id IS NOT NULL
                    AND DATE(av_datetime) >= '{ start_date }'
                    AND DATE(av_datetime) <= '{ end_date }'
                    AND av_case = 'forward_leg'
            """
        )
        data.toPandas().to_parquet(f"{data_science_tables.AddressVerificationMY(env).ACCURACY_FOLDER}/{name}.parquet")


def workaround_accuracy(spark, ls, env, start_date_two_weeks, end_date_one_week):
    input_env = "prod"

    # Set table path
    date_suffix = f"measurement_datetime={end_date_one_week} 18-00-00/system_id=my/"
    latest_suffix = "measurement_datetime=latest/system_id=my/"
    update_address_verification_events_path = (
        f"{versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_VERIFICATION_EVENTS}/{latest_suffix}"
    )
    order_milestones_path = f"{versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES}/{date_suffix}"
    update_address_events_path = (
        f"{versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS}/{latest_suffix}"
    )
    delivery_transaction_events_path = (
        f"{versioned_parquet_tables_masked.DataWarehouse(input_env).DELIVERY_TRANSACTION_EVENTS}/{date_suffix}"
    )

    # Get table
    update_address_verification_events = (
        spark.read.format("parquet")
        .option("mergeSchema", "true")
        .load(update_address_verification_events_path)
        .select(
            "order_id",
            "user_id",
            "av_status",
            "av_source",
            "av_mode",
            "av_zone_id",
            "av_hub_id",
            "av_latitude",
            "av_longitude",
            "confidence_score",
            "order_event_id",
            "event_datetime",
            "event_creation_datetime",
        )
        .where("order_id IS NOT NULL")
    )

    order_milestones = (
        spark.read.format("parquet")
        .load(order_milestones_path)
        .where("status = 'Completed' AND force_success_flag = 0")
        .select("order_id", "delivery_success_datetime", "rts_trigger_datetime", "rts_flag")
    )

    update_address_events = (
        spark.read.format("parquet").load(update_address_events_path).select("order_id", "event_datetime")
    )

    delivery_transaction_events = (
        spark.read.format("parquet")
        .load(delivery_transaction_events_path)
        .where("type = 'delivery'")
        .select("order_id", "dest_hub_id", "transaction_id", "legacy_zone_id")
    )

    update_address_verification_events.createOrReplaceTempView("update_address_verification_events")
    order_milestones.createOrReplaceTempView("order_milestones")
    update_address_events.createOrReplaceTempView("update_address_events")
    delivery_transaction_events.createOrReplaceTempView("delivery_transaction_events")

    base_data = spark.sql(
        f"""
            WITH
            dedup_update_address_verification_events AS (
                SELECT
                    order_id
                    , user_id
                    , av_status
                    , av_source
                    , av_mode
                    , av_zone_id
                    , av_hub_id
                    , av_latitude
                    , av_longitude
                    , confidence_score
                    , MIN(order_event_id) AS order_event_id
                    , MIN(event_datetime) AS event_datetime
                    , MIN(event_creation_datetime) AS event_creation_datetime
                FROM update_address_verification_events
                GROUP BY
                    order_id
                    , user_id
                    , av_status
                    , av_source
                    , av_mode
                    , av_zone_id
                    , av_hub_id
                    , av_latitude
                    , av_longitude
                    , confidence_score
            ),
            om_filter_base AS (
                SELECT
                    order_id
                    , CASE
                        WHEN rts_flag = 0 THEN delivery_success_datetime
                        WHEN rts_flag = 1 THEN  (rts_trigger_datetime - INTERVAL 5 MINUTES)
                        END AS forward_leg_cutoff
                    , CASE
                        WHEN rts_flag = 0 THEN delivery_success_datetime
                        WHEN rts_flag = 1 THEN (rts_trigger_datetime + INTERVAL 5 MINUTES)
                        END AS return_leg_cutoff
                FROM order_milestones
            ),
            leg_filter AS (
                SELECT
                    om_filter_base.order_id
                    , MAX(CASE
                        WHEN update_address_events.event_datetime < om_filter_base.forward_leg_cutoff THEN 1
                        ELSE 0 END) AS forward_exclusion_flag
                FROM om_filter_base
                LEFT JOIN update_address_events ON
                    om_filter_base.order_id = update_address_events.order_id
                GROUP BY
                    om_filter_base.order_id
            ),
            om_filtered AS (
                SELECT
                    om_filter_base.*
                FROM om_filter_base
                LEFT JOIN leg_filter ON
                    om_filter_base.order_id = leg_filter.order_id
                WHERE
                    leg_filter.forward_exclusion_flag = 0
            ),
            transactions_pre_union AS (
                SELECT
                    order_id
                    , MAX_BY(dest_hub_id, transaction_id) AS delivery_attempt_hub_id
                    , MAX_BY(legacy_zone_id, transaction_id) AS waypoint_zone_id
                FROM delivery_transaction_events
                GROUP BY
                    order_id
            ),
            transactions_union AS (
                SELECT
                    order_id
                    , 'forward_leg' AS av_case
                    , delivery_attempt_hub_id
                    , waypoint_zone_id
                FROM transactions_pre_union
            ),
            av_joined AS (
                SELECT
                    om_filtered.order_id
                    , av_events.event_datetime AS av_datetime
                    , av_events.av_status
                    , av_events.av_source
                    , av_events.av_mode
                    , av_events.av_zone_id
                    , 'forward_leg' as av_case
                FROM om_filtered
                LEFT JOIN dedup_update_address_verification_events AS av_events ON
                    om_filtered.order_id = av_events.order_id
                WHERE
                    av_events.event_datetime <= om_filtered.forward_leg_cutoff
            ),
            transactions_joined AS (
                SELECT
                    av_joined.*
                    , transactions_union.delivery_attempt_hub_id
                    , transactions_union.waypoint_zone_id
                FROM av_joined
                LEFT JOIN transactions_union ON
                    av_joined.order_id = transactions_union.order_id
                    AND av_joined.av_case = transactions_union.av_case
            ),
            final_bi_dwh AS (
            SELECT
                order_id
                , delivery_attempt_hub_id
                , av_case
                , DATE(av_datetime) AS av_date
                , av_status
                , av_source
                , av_mode
                , IF(waypoint_zone_id = av_zone_id,1,0) as zone_alignment_flag
            FROM transactions_joined
            )
            SELECT
                order_id
                , av_date
                , CASE WHEN av_status = 'VERIFIED' THEN 1 ELSE 0 END AS verified_address
                , CASE WHEN av_status = 'VERIFIED' THEN zone_alignment_flag ELSE 0 END AS correct_verified_address
            FROM final_bi_dwh
            WHERE
                av_mode = 'AUTO'
                AND av_source = 'MODEL_AV'
                AND delivery_attempt_hub_id IS NOT NULL
                AND av_case = 'forward_leg'
                AND av_date >= '{ start_date_two_weeks }'
                AND av_date <= '{ end_date_one_week }'
        """
    )
    base_data.cache()
    base_data.createOrReplaceTempView("base_data")

    # Loop for results
    for name, start_date, end_date in ls:
        data = spark.sql(
            f"""
                SELECT
                    COUNT(*) AS total_addresses
                    , SUM(verified_address) AS total_verified_addresses
                    , SUM(correct_verified_address) AS total_correct_verified_addresses
                FROM base_data
                WHERE
                    av_date >= '{ start_date }'
                    AND av_date <= '{ end_date }'
            """
        )
        data.toPandas().to_parquet(f"{data_science_tables.AddressVerificationMY(env).ACCURACY_FOLDER}/{name}.parquet")
