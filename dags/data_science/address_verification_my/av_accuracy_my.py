import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case
from data_science.address_verification_my.config import dag_configs
from data_science.address_verification_my.python_wrapper_functions import check_bi_table, publish_accuracy
from data_science.ds_common.notifications_chat import send_ds_failure_alert
from metadata import data_science
from metadata.constants import SystemID, Timeout

DEFAULT_SCHEDULE = "* 20 * * *"  # Daily 4am SGT

env = Variable.get("env")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 12, 28),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": send_ds_failure_alert,
}


def _create_dag(dag_id, dag_config, schedule_interval):
    with DAG(
        catchup=False,
        dag_id=dag_id,
        default_args=default_args,
        max_active_runs=1,
        schedule_interval=schedule_interval,
        tags=["data_science"],
    ) as dag:
        start = DummyOperator(task_id="start")
        end = DummyOperator(task_id="end")

        # Works because only 1 task config given
        notify_task_config = dag_config["notify_task_configs"][0]

        task_name = notify_task_config.task_name

        input_args = {
            "env": env,
            "current_date": "{{ data_interval_end.format('YYYY-MM-DD') }}",
            "start_date_two_weeks": "{{ data_interval_end.subtract(days=21).format('YYYY-MM-DD') }}",
            "end_date_two_weeks": "{{ data_interval_end.subtract(days=8).format('YYYY-MM-DD') }}",
            "start_date_one_week": "{{ data_interval_end.subtract(days=7).format('YYYY-MM-DD') }}",
            "end_date_one_week": "{{ data_interval_end.subtract(days=1).format('YYYY-MM-DD') }}",
            "notify_channel": dag_config.get("notify_channel"),
        }

        check_task = PythonOperator(
            task_id=f"check_task_{SystemID.MY}", python_callable=check_bi_table, op_args=[input_args]
        )

        input_args["bi_table"] = f"{{{{ ti.xcom_pull(dag_id='{dag_id}', task_ids='check_task_{SystemID.MY}') }}}}"

        extract_task = SparkSubmitOperator(
            task_id=f"extract_task_{SystemID.MY}",
            name=kebab_case(task_name),
            application=f"{tasks_path}/{notify_task_config.py_file}",
            application_args=[json.dumps(input_args, separators=(",", ":"))],
            conn_id="spark_default",
            conf={
                **spark_conf[tables_to_size.get(task_name, "small")],
                "spark.sql.adaptive.enabled": True,
                "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
            },
            execution_timeout=notify_task_config.execution_timeout,
            params={"enable_full_run": False, "alert_channel": dag_configs[dag_id].get("alert_channel")},
        )

        notify_task = PythonOperator(
            task_id=f"notify_task_{SystemID.MY}", python_callable=publish_accuracy, op_args=[input_args]
        )

        start >> check_task >> extract_task >> notify_task >> end

    globals()[dag_id] = dag
    return dag


dag_id = data_science.AddressVerificationAccuracyMYDAG.DAG_ID
_create_dag(dag_id, dag_configs[dag_id], dag_configs[dag_id].get("notify_schedule", DEFAULT_SCHEDULE))
