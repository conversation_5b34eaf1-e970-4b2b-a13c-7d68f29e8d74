import json

import pandas as pd
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from httplib2 import Http

from metadata import data_science_tables


def check_bi_table(input_args):
    hook = GCSHook()
    end_date_one_week = input_args["end_date_one_week"]
    object_list = hook.list(
        bucket_name="nv-data-prod-data-warehouse",
        prefix=(
            f"auto_address_verification_accuracy_report/measurement_datetime={end_date_one_week} 18-00-00/"
            + "system_id=my"
        ),
        delimiter=".parquet",
    )
    if len(object_list) > 0:
        return True
    return False


def publish_accuracy(input_args):
    env = input_args["env"]
    run_date = input_args["current_date"]
    start_date_two_weeks = input_args["start_date_two_weeks"]
    end_date_two_weeks = input_args["end_date_two_weeks"]
    start_date_one_week = input_args["start_date_one_week"]
    end_date_one_week = input_args["end_date_one_week"]
    chat_url = input_args["notify_channel"]
    bi_table = input_args["bi_table"]

    ls = [("two_weeks", start_date_two_weeks, end_date_two_weeks), ("one_week", start_date_one_week, end_date_one_week)]
    render_text = f"Run date: {run_date}\nCountry: Malaysia 🇲🇾\nSource table: "

    if bi_table == "True":
        render_text += "BI\n"
    else:
        render_text += "Workaround\n"

    for name, start_date, end_date in ls:
        render_text += "\n"

        df = pd.read_parquet(f"{data_science_tables.AddressVerificationMY(env).ACCURACY_FOLDER}/{name}.parquet")
        total_addresses = df.total_addresses[0]
        total_verified_addresses = df.total_verified_addresses[0]
        total_correct_verified_addresses = df.total_correct_verified_addresses[0]
        percentage_verified_addresses = total_verified_addresses / total_addresses * 100
        percentage_correct_verified_addresses = total_correct_verified_addresses / total_verified_addresses * 100

        quantity_circle = "🟢" if percentage_verified_addresses >= 69 else "🔴"
        accuracy_circle = "🟢" if percentage_correct_verified_addresses >= 99.6 else "🔴"

        render_text += f"*{start_date} to {end_date}*\n"
        render_text += f"Total addresses: {total_addresses:,}\n"
        render_text += f"{quantity_circle} Verified addresses: {total_verified_addresses:,} "
        render_text += f"({percentage_verified_addresses:.3f}%)\n"
        render_text += f"{accuracy_circle} Accurate zone predicted: {total_correct_verified_addresses:,} "
        render_text += f"({percentage_correct_verified_addresses:.3f}%)\n"

    # # Send gchat message
    app_message = {"text": render_text}
    http_obj = Http()
    http_obj.request(
        uri=chat_url,
        method="POST",
        body=json.dumps(app_message),
    )
