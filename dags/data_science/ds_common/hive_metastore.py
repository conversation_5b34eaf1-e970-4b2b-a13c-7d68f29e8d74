import json
import sys

from pyspark.sql import SparkSession

from common import hive_metastore
from metadata.parquet_tables import DataScience as DS_Tables


def _get_table_path(table, env):
    return getattr(DS_Tables(env), table.upper())


def update_hive_metastore(spark, env, hive_schema, partition_columns, table):
    table_path = _get_table_path(table, env)
    table_name = table
    hive_metastore.update_metastore(spark, hive_schema, partition_columns, table_name, table_path)


if __name__ == "__main__":
    env, hive_schema, partition_columns_str, table = sys.argv[1:]
    partition_columns = json.loads(partition_columns_str)

    spark = SparkSession.builder.enableHiveSupport().getOrCreate()
    update_hive_metastore(spark, env, hive_schema, partition_columns, table)
    spark.stop()
