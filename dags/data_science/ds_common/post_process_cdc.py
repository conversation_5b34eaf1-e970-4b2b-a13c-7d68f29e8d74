import itertools
import sys
from datetime import timedelta
from google.cloud import storage
from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format

from common import date as date_util
from common.spark import util
from common.utils.gcs import get_nv_data_bucket_uri
from metadata.constants import DATASCIENCE_BASE_URI


def _flatten_data(spark, df, col):
    """Converts JSON string in df[col] to columns"""
    idx = df.columns.index(col)
    return spark.read.json(df.rdd.map(lambda data: data[idx]))


def _get_paths(bucket, db, table, ts, num_intervals):
    ts_datetime = date_util.to_datetime(ts)
    paths = []
    delimiter = "parquet"
    cdc_paths = ("cdc_stream", "ticdc_stream")
    gs_bucket = storage.Client().get_bucket(bucket)

    for i, path in itertools.product(range(int(num_intervals)), cdc_paths):
        dt = ts_datetime + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={dt:%Y-%m-%d}/time={dt:%H-%M-%S}/database={db}/table={table}/"
        blobs = gs_bucket.list_blobs(prefix=prefix, delimiter=delimiter)
        if any(blobs):
            paths.append(f"gs://{bucket}/{prefix}")
    return paths


def _write_cdc(df, bucket):
    (
        df.repartition("cdc_date", "cdc_time")
        .write.mode("append")
        .format("parquet")
        .partitionBy("cdc_date", "cdc_time")
        .save(f"{bucket}/cdc_processed/{db}/{table}/")
    )


def process_cdc(spark, db, table, ts, num_intervals):
    nv_data_cols = {"nv_data_ts": "long", "nv_data_xid": "long", "nv_data_xoffset": "long"}
    date = date_util.date_from_ts(ts)
    time = date_util.time_from_ts(ts)
    input_bucket = get_nv_data_bucket_uri(env, bucket_type="raw", schema=db, strip=True)
    paths = _get_paths(input_bucket, db, table, ts, num_intervals)
    raw_cdc = spark.read.parquet(*paths)
    flat = _flatten_data(spark, raw_cdc, "nv_data_cdc")
    flat_cast = util.cast_columns(flat, nv_data_cols)
    df_cdc = util.add_literal_columns(
        flat_cast.withColumn("created_month", date_format("created_at", "yyyy-MM")),
        {"cdc_date": date, "cdc_time": time},
    )

    output_bucket = DATASCIENCE_BASE_URI.format(env)
    _write_cdc(df_cdc, output_bucket)


if __name__ == "__main__":
    env, db, table, ts, num_intervals = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    process_cdc(spark, db, table, ts, num_intervals)
    spark.stop()
