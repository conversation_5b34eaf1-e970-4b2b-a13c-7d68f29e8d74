from dags.common.airflow.notifications.chat import _gchat


def send_ds_failure_alert(context):
    """
    Refer to dags.common.airflow.notifications.chat for original
    Modified for DS purposes
    """
    ti = context.get("task_instance")
    ti_failed_message = f"""
        ❌  Task instance failed.
            *DAG*: {ti.dag_id}
            *Task*: {ti.task_id}
            *Execution Date*: {context.get("data_interval_end")}
            *Log URL*: {ti.log_url}
         """
    connection = context["params"].get("alert_channel")
    _gchat(ti_failed_message, connection).execute(context)


def send_ds_success_alert(context):
    """
    Refer to dags.common.airflow.notifications.chat for original
    Modified for DS purposes
    """
    ti = context.get("task_instance")
    ti_success_message = f"""
        ✅  Task instance success.
            *DAG*: {ti.dag_id}
            *Task*: {ti.task_id}
            *Execution Date*: {context.get("data_interval_end")}
            *Log URL*: {ti.log_url}
         """
    connection = context["params"].get("alert_channel")
    _gchat(ti_success_message, connection).execute(context)
