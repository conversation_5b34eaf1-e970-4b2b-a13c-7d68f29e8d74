import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.providers.google.cloud.sensors.gcs import GCSObjectsWithPrefixExistenceSensor
from airflow.sensors.bash import BashSensor

from common.stringcase import kebab_case
from data_science.ds_common.notifications_chat import send_ds_failure_alert
from data_science.pod_auto_validation.tasks.config import dag_configs
from metadata import data_science
from metadata.constants import Timeout

DEFAULT_SCHEDULE = "* 20 * * *"  # Daily 4am SGT

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
kube_template_path = str(Path(__file__).resolve().parent / "kubernetes/pod_auto_validation_predict.yml")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
monitoring_execution_hour = 23

default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 4, 17),
    "retries": 3,
    "retry_delay": timedelta(minutes=1),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": send_ds_failure_alert,
}
check_hour_task_bash_command = """
set -e;
if [ $hour == $monitoring_execution_hour ]
then
    exit 0;
else
    exit 1;
fi
"""


def _create_dag(dag_id, dag_config, schedule_interval):
    with DAG(
        catchup=False,
        dag_id=dag_id,
        default_args=default_args,
        max_active_runs=1,
        schedule_interval=schedule_interval,
        tags=["data_science"],
    ) as dag:
        for ingest_task_config in dag_config["ingest_task_configs"]:
            task_name = ingest_task_config.task_name
            start = DummyOperator(task_id=f"{dag_id}_start")
            end = DummyOperator(task_id=f"{dag_id}_end")
            for system_id in ingest_task_config.system_ids:
                task_id = f"{task_name}_{system_id}"
                input_args = {
                    "env": env,
                    "last_measurement_datetime_str": "{{ data_interval_start }}",
                    "measurement_datetime_str": "{{ data_interval_end }}",
                    "measurement_hour_str": "{{ data_interval_end.hour }}",
                    "measurement_datetime_gcs_str": "{{ data_interval_end.format('YYYY-MM-DD HH-mm-SS') }}",
                    "enable_full_run_str": "{{ params.enable_full_run }}",
                    "system_id": system_id,
                }
                task = SparkSubmitOperator(
                    task_id=task_id,
                    name=kebab_case(task_id),
                    application=f"{tasks_path}/{ingest_task_config.py_file}",
                    application_args=[json.dumps(input_args, separators=(",", ":"))],
                    conn_id="spark_default",
                    conf={
                        **spark_conf[tables_to_size.get(task_id, "small")],
                        "spark.sql.adaptive.enabled": True,
                        "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                        "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                        "spark.sql.autoBroadcastJoinThreshold": "-1",
                    },
                    execution_timeout=timedelta(minutes=40),
                    params={"enable_full_run": False, "alert_channel": dag_configs[dag_id].get("alert_channel")},
                )
                check_hour_task = BashSensor(
                    task_id=f"check_execution_hour_{system_id}",
                    bash_command=check_hour_task_bash_command,
                    env={
                        "hour": input_args["measurement_hour_str"],
                        "monitoring_execution_hour": str(monitoring_execution_hour),
                    },
                    retries=0,
                    soft_fail=True,
                    timeout=10,
                )
                start >> task >> check_hour_task
                check_ingest_task = GCSObjectsWithPrefixExistenceSensor(
                    task_id=f"check_ingest_data_{system_id}",
                    bucket=f"nv-data-{env}-datascience",
                    prefix=(
                        "pod_auto_validation/data/measurement_datetime="
                        + f"{input_args['measurement_datetime_gcs_str']}/system_id={system_id}/"
                        + "created_hour="
                    ),
                    google_cloud_conn_id="google_cloud_default",
                    retries=0,
                    soft_fail=True,
                    timeout=10,
                    trigger_rule="none_failed",
                )
                check_hour_task >> check_ingest_task
                limit_tasks = "1000" if env == "dev" else "-1"
                container_args = [
                    "--env",
                    env,
                    "--limit_tasks",
                    limit_tasks,
                    "--system_id",
                    system_id,
                    "--measurement_datetime",
                    input_args["measurement_datetime_str"],
                ]
                # For local testing, change in_cluster to False and provide config_file param
                predict_task = KubernetesPodOperator(
                    task_id=f"pod_auto_validation_predict_{system_id}",
                    pod_template_file=kube_template_path,
                    image=f"asia-southeast1-docker.pkg.dev/ninja-van-management/docker/pod-auto-validation:{env}-latest",
                    in_cluster=True,
                    startup_timeout_seconds=720,
                    namespace=env,
                    arguments=container_args,
                    params={"alert_channel": dag_configs[dag_id].get("alert_channel")},
                    trigger_rule="one_success",
                )
                check_ingest_task >> predict_task
                check_hour_task >> predict_task
                predict_task >> end
    globals()[dag_id] = dag
    return dag


dag_id = data_science.PODAutoValidationDAG.DAG_ID
_create_dag(dag_id, dag_configs[dag_id], dag_configs[dag_id].get("schedule", DEFAULT_SCHEDULE))
