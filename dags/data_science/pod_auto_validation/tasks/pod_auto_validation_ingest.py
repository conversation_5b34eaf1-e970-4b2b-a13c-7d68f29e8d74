import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_science, data_science_tables, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_science.PODAutoValidationDAG.IngestTask.POD_AUTO_VALIDATION_INGEST + ".py",
    task_name=data_science.PODAutoValidationDAG.IngestTask.POD_AUTO_VALIDATION_INGEST,
    system_ids=(constants.SystemID.ID,),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=data_science_tables.PODAutoValidation(env).LATEST_PHOTO_CREATED_AT,
                view_name="latest_photo_created_at",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).ASSIGNMENTS,
                view_name="assignments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).PARCELS,
                view_name="parcels",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).PHOTOS,
                view_name="photos",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.PodValidationProdGL(input_env, is_masked).TASKS,
                view_name="tasks",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_photo_created_at_value",
                jinja_template="""
                SELECT
                    photo_created_at
                FROM latest_photo_created_at
                """,
            ),
            base.TransformView(
                view_name="assignments_base",
                jinja_template="""
                SELECT
                    task_id
                    , user_id
                    , validity
                FROM assignments
                WHERE is_automation = 1
                    AND validity is NULL
                    AND system_id = '{{ system_id }}'
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="tasks_base",
                jinja_template="""
                SELECT
                    id as task_id
                    , job_id
                    , validator_count
                    , nonce_id
                    , failure_reason_id
                    , driver_id
                    , attempted_at
                    , system_id
                    , created_month
                    , date_format(attempted_at, 'yyyy-MM-dd') as created_day
                    , date_format(attempted_at, 'yyyy-MM-dd_HH') as created_hour
                FROM tasks
                WHERE tasks.system_id = '{{ system_id }}'
                    AND (TO_DATE(created_at) = '{{ prev_date }}' OR TO_DATE(created_at) = '{{ curr_date }}')
                    AND was_reattempted = 0
                    AND validator_count = 1
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "curr_date": measurement_datetime.strftime("%Y-%m-%d"),
                    "prev_date": measurement_datetime.subtract(days=1).strftime("%Y-%m-%d"),
                },
            ),
            base.TransformView(
                view_name="parcels_base",
                jinja_template="""
                SELECT
                    id as parcel_id
                    , task_id
                    , tracking_id
                    , contact
                    , system_id
                FROM parcels
                WHERE parcels.system_id = '{{ system_id }}'
                    AND ((TO_DATE(created_at) >= '{{ prev_date }}' AND TO_DATE(created_at) <= '{{ curr_date }}'))
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "curr_date": measurement_datetime.strftime("%Y-%m-%d"),
                    "prev_date": measurement_datetime.subtract(days=30).strftime("%Y-%m-%d"),
                },
            ),
            base.TransformView(
                view_name="photos_base",
                jinja_template="""
                SELECT
                    id as photo_id
                    , nonce_id
                    , url
                    , bucket
                    , filepath
                    , taken_at as photo_taken_at
                    , created_at as photo_created_at
                FROM photos
                WHERE created_at > (
                    SELECT
                        *
                    FROM latest_photo_created_at_value
                )
                """,
            ),
            base.TransformView(
                view_name="tasks_filter",
                jinja_template="""
                SELECT
                    tasks_base.task_id
                    , tasks_base.job_id
                    , tasks_base.validator_count
                    , tasks_base.nonce_id
                    , tasks_base.failure_reason_id
                    , tasks_base.driver_id
                    , tasks_base.attempted_at
                    , tasks_base.system_id
                    , tasks_base.created_month
                    , tasks_base.created_day
                    , tasks_base.created_hour
                    , assignments_base.validity
                FROM assignments_base
                LEFT JOIN tasks_base
                    ON tasks_base.task_id = assignments_base.task_id
                """,
            ),
            base.TransformView(
                view_name="tasks_with_photo",
                jinja_template="""
                SELECT
                    photos_base.photo_id
                    , photos_base.nonce_id
                    , photos_base.url
                    , photos_base.bucket
                    , photos_base.filepath
                    , photos_base.photo_taken_at
                    , photos_base.photo_created_at
                    , tasks_filter.task_id
                    , tasks_filter.job_id
                    , tasks_filter.failure_reason_id
                    , tasks_filter.driver_id
                    , tasks_filter.attempted_at
                    , tasks_filter.validator_count
                    , tasks_filter.validity
                    , tasks_filter.system_id
                    , tasks_filter.created_month
                    , tasks_filter.created_day
                    , tasks_filter.created_hour
                FROM photos_base
                INNER JOIN tasks_filter
                    ON photos_base.nonce_id = tasks_filter.nonce_id
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT
                    tasks_with_photo.*
                    , parcels_base.tracking_id
                    , parcels_base.contact
                FROM tasks_with_photo
                LEFT JOIN parcels_base
                    ON tasks_with_photo.task_id = parcels_base.task_id
                ORDER BY tasks_with_photo.task_id ASC
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=data_science_tables.PODAutoValidation(env).INGEST,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_hour",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
