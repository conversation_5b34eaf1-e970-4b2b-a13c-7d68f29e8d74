"""
Data Science POD Auto Validation DAGs config mapping
"""
from importlib import import_module

from data_science.pod_auto_validation.tasks import pod_auto_validation_base
from metadata import data_science


def get_airflow_config_list(tasks_class):
    # Convert class to list
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]

    airflow_config_list = []
    for t in tasks:
        task_module = import_module(f"data_science.pod_auto_validation.tasks.{t}")
        airflow_config_list.append(task_module.airflow_config)
    return airflow_config_list


def get_kube_config_list(tasks_class):
    tasks = [v for k, v in tasks_class.__dict__.items() if not k.startswith("__")]
    kube_config_list = []
    for t in tasks:
        kube_config_list.append(pod_auto_validation_base.KubeTaskConfig(task_name=t))
    return kube_config_list


dag_configs = {
    data_science.PODAutoValidationDAG.DAG_ID: {
        "ingest_task_configs": get_airflow_config_list(data_science.PODAutoValidationDAG.IngestTask),
        "predict_task_configs": get_kube_config_list(data_science.PODAutoValidationDAG.PredictTask),
        "schedule": "0 * * * *",
        "alert_channel": "data_science_gchat",
    }
}
