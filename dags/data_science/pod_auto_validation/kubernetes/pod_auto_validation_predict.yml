apiVersion: v1
kind: Pod
metadata:
  name: pod-auto-validation-predict
  # namespace set in pod_auto_validation.py
spec:
  containers:
  - name: pod-auto-validation-predict
    imagePullPolicy: Always
    # args set in pod_auto_validation.py
    resources:
      limits:
        nvidia.com/gpu: 1
        memory: 30G
      requests:
        cpu: 7300m
        memory: 25G
    volumeMounts:
    - name: storage-service-account-secret
      readOnly: true
      mountPath: /etc/storage-service-account-secret
    - name: shared-memory
      mountPath: /dev/shm
  nodeSelector:
    group: ds-gpu
  tolerations:
    - key: group
      operator: Equal
      value: ds-gpu
  restartPolicy: Never

  volumes:
  - name: storage-service-account-secret
    secret:
      secretName: storage-service-account-key
  - name: shared-memory
    emptyDir:
      medium: Memory
