import logging
from datetime import datetime, timedelta
from functools import partial
from pathlib import Path

from airflow import DAG
from airflow.api.common.experimental import get_task_instance
from airflow.models import Variable
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.sensors.external_task import ExternalTaskSensor

from common.airflow import db
from common.airflow import notifications as notif
from common.stringcase import kebab_case
from common.utils.gcs import strip_uri
from metadata.constants import DATASCIENCE_BASE_URI, Timeout
from metadata.data_science import MYSQL_TABLES

ALERT_CHANNEL = "data_science_gchat"

tasks_path = "local://" + str(Path(__file__).resolve().parent / "ds_common")

env = Variable.get("env")
mysql_tables = MYSQL_TABLES[env]
interval_duration = 60
num_intervals = interval_duration // 15
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
spark_conf = Variable.get("spark_conf", deserialize_json=True)["cdc"]
table_sizes = Variable.get("table_sizes", deserialize_json=True)["cdc"]

default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 10, 10, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env in ("prod", "dev") else None,
    # "pool": "cdc_pool",
}


def create_dag(dag_id, schedule_interval=f"*/{interval_duration} * * * *", default_args=default_args):
    dag = DAG(
        dag_id=dag_id,
        schedule_interval=schedule_interval,
        default_args=default_args,
        catchup=False,
        max_active_runs=1,
        concurrency=10,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        params={"alert_channel": ALERT_CHANNEL},
        tags=["data_science"],
    )
    globals()[dag_id] = dag
    return dag


def list_table_tasks(schema, **context):
    tables_with_changes = []
    task_ids = f"{schema}.list_tables_with_changes"
    change_list = context["ti"].xcom_pull(dag_id="datalake_cdc_sensor", task_ids=task_ids)
    logging.info(f"Checking task_ids: {task_ids}")
    logging.info(f"List of DB tables with changes from datalake_cdc_sensor: {change_list}")

    for table in change_list:
        if table in mysql_tables[schema]:
            tables_with_changes.append(f"process_cdc_messages_{schema}_{table}")
    return tables_with_changes


def get_last_scheduled_execution_date(execution_date):
    """Allows sensor to find the last run even though DAG is triggered out of schedule, such as when running E2E tests,
    e.g., sensor with execution_date = '2020-01-01T11:00:00.500' will find '2020-01-01T10:00:00.000' run
    """
    return execution_date.replace(minute=0, second=0, microsecond=0).subtract(minutes=interval_duration)


def check_delta(env, schema, table, delta_task_id, **context):
    """Checks if the upstream Delta merge task successfully committed a new Delta version."""
    hook = GCSHook()
    gs_bucket = strip_uri(DATASCIENCE_BASE_URI.format(env))
    prefix = f"delta/{schema}/{table}/_delta_log"
    logging.info(f"Checking in '{gs_bucket}/{prefix}'")
    blobs = list(hook.list(bucket_name=gs_bucket, prefix=prefix, delimiter=".json"))
    if not blobs:
        raise Exception(f"No json logs found in '{gs_bucket}/{prefix}'.")
    last_delta_log = max(blobs)
    last_delta_log_update = hook.get_blob_update_time(gs_bucket, last_delta_log)

    delta_ti = get_task_instance.get_task_instance(context["dag"].dag_id, delta_task_id, context["data_interval_start"])
    delta_start_date = delta_ti.start_date

    if last_delta_log_update < delta_start_date:
        raise Exception("Delta merge not found.")


def create_delta_task(env, task_name, schema, table):
    delta_task_id = task_name
    delta_default_airflow_var = ["id"]
    delta_task = SparkSubmitOperator(
        task_id=delta_task_id,
        name=kebab_case(delta_task_id),
        execution_timeout=Timeout.ONE_HOUR,
        application=f"{tasks_path}/update_delta.py",
        application_args=[
            env,
            schema,
            table,
            "{{ ts }}",
            f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
            f"{{{{ ti.xcom_pull(dag_id='datalake_cdc_{schema}', task_ids='get_{schema}_conn') }}}}",
        ],
        conn_id="spark_default",
        conf={
            **(spark_conf["large"] if f"{schema}.{table}" in table_sizes["large"] else spark_conf["medium"]),
            "spark.databricks.delta.schema.autoMerge.enabled": "true",
        },
    )
    return delta_task


def clear_delta_callback(delta_task, context):
    delta_task.clear(start_date=context["data_interval_start"], end_date=context["data_interval_end"], downstream=True)


for schema, tables in mysql_tables.items():
    dag_id = f"data_science_cdc_{schema}"
    db_dag = create_dag(dag_id=dag_id)
    gs_bucket = ""
    with db_dag as dag:
        wait_previous_end = ExternalTaskSensor(
            task_id=f"wait_previous_end_{schema}",
            external_dag_id=f"data_science_cdc_{schema}",
            external_task_id=None,
            execution_date_fn=get_last_scheduled_execution_date,
            pool="sensor_pool",
        )

        wait_to_list_changes = ExternalTaskSensor(
            task_id=f"list_changes_sensor_{schema}",
            external_dag_id="datalake_cdc_sensor",
            external_task_id=f"{schema}.list_tables_with_changes",
            mode="reschedule",
            soft_fail=True,
            execution_timeout=Timeout.TWO_HOURS,
            poke_interval=120,
            pool="sensor_pool",
        )

        get_conn = PythonOperator(
            task_id=f"get_{schema}_conn", python_callable=db.get_schema_conn, op_args=[schema, mysql_connections]
        )

        list_tables_to_process = BranchPythonOperator(
            task_id=f"get_tables_with_changes_{schema}", python_callable=list_table_tasks, op_args=[schema]
        )

        for table in tables:
            process_cdc_messages_default_var = {}
            process_cdc_messages_default_list = []
            process_cdc_task_id = f"process_cdc_messages_{schema}_{table}"
            process_cdc = SparkSubmitOperator(
                task_id=process_cdc_task_id,
                name=kebab_case(f"cdc-{schema}-{table}"),
                execution_timeout=Timeout.ONE_HOUR,
                application=f"{tasks_path}/post_process_cdc.py",
                application_args=[
                    env,
                    schema,
                    table,
                    "{{ ts }}",
                    str(num_intervals),
                ],
                conn_id="spark_default",
                conf={
                    **(spark_conf["large"] if process_cdc_task_id in table_sizes["large"] else spark_conf["small"]),
                },
            )

            delta_task_id = f"delta_{schema}_{table}"

            delta_task = create_delta_task(env, delta_task_id, schema, table)

            check_delta_task = PythonOperator(
                task_id=f"check_delta_{schema}_{table}",
                python_callable=check_delta,
                op_args=[env, schema, table, f"{delta_task_id}"],
                on_failure_callback=partial(clear_delta_callback, delta_task),
                pool="sensor_pool",
            )

            wait_previous_end >> [wait_to_list_changes, get_conn] >> list_tables_to_process >> \
            process_cdc >> delta_task >> check_delta_task
