import requests
from airflow.hooks.base import BaseHook
from requests.auth import HTTPBasicAuth


class SonarQubeHook(BaseHook):
    """
    SonarQube hook to fetch data with the SonarQube REST API (https://docs.sonarqube.org/8.9/extension-guide/web-api).

    :param conn_id: SonarQube connection ID
    :type conn_id:  string
    """

    def __init__(self, conn_id="sonarqube_default"):
        self.conn = self.get_connection(conn_id)
        extras = self.conn.extra_dejson
        required_keys = {"access_token"}
        if not required_keys <= extras.keys():
            raise ValueError(f"Missing configuration in SonarQube connection: {required_keys}")
        self.access_token = extras.get("access_token")
        self.max_retries = 2

    def get_data(self, path, query_params=None):
        """
        Fetches entity data with the specified relative url path and query parameters.

        :param path_fmt:        relative URL path
        :param query_params:    request query parameters
        :return:                SonarQube entity data
        """
        url = f"{self.conn.host}/{path}"
        auth = HTTPBasicAuth(self.access_token, "")
        data = self._get_with_retry(url, auth, params=query_params)
        return data

    def _get_with_retry(self, url, auth, params=None):
        """
        Fetches entity data with the specified url, headers and query parameters.
        Retries up to ``self.max_retries`` times if the request is unsuccessful.

        :param url:     request URL (e.g. https://sonarqube.ninjavan.co/api/...)
        :param auth:    request basic authentication
        :param params:  request query parameters (optional)
        :return:        tuple of response data
        :raises:        runtime error if request fails after ``self.max_retries`` consecutive retries
        """
        rem_retries = self.max_retries
        resp = requests.post(url, auth=auth, params=params)
        while resp.status_code != 200:
            if rem_retries == 0:
                raise RuntimeError(f"Error fetching SonarQube data: {resp.text}")
            rem_retries -= 1
            resp = requests.post(url, auth=auth, params=params)
        return resp.json()