import os
from enum import Enum
from pathlib import Path
from airflow.models import Variable

class Config:
    ALERT_CHANNEL = "google_chat"
    TASKS_PATH = "local://" + str(Path(__file__).resolve().parent.parent / "data_lake/db_cdc/tasks")
    SPARK_OPERATOR = True
    IMAGE_NAME = os.environ["SPARK_IMAGE_NAME"]
    TAG = os.environ["SPARK_IMAGE_TAG"]
    ENV = Variable.get("env")
    INTERVAL_DURATION = 60
    NUM_INTERVALS = INTERVAL_DURATION // 15
    WAIT_TIME_DYNAMIC = {
        "HIGH": 300,
        "MEDIUM": 900,
        "LOW": 1800
    }
    NO_WAIT_TIME_SCHEMA = Variable.get("no_wait_time_schema", deserialize_json=True, default_var=[])

class JobType(Enum):
    HIVE = "cdc_hive"
    CDC = "cdc"
    COMPACTION = "compaction"
    DWH = "dwh"