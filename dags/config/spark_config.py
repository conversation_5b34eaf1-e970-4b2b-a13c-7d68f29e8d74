from metadata.table_sizes import TABLE_SIZES
from metadata.spark_conf import SPARK_CONF
from config.config import Config


class SparkConfig:
    """
    This class provides access to Spark configuration settings for different environments.
    These settings are used in the Spark tasks in the DAGs.
    """

    @staticmethod
    def get_table_size_mapping(env: str = None, category: str = "cdc") -> dict:
        """
        Get table size mapping for a specific environment and category.

        Args:
            env (str): Environment ('dev' or 'prod'). If None, uses Config.ENV
            category (str): Category of tables ('cdc', 'cdc_hive', etc.)

        Returns:
            dict: Mapping of table names to their sizes
        """
        env = env or Config.ENV
        tables_config = TABLE_SIZES.get(env, TABLE_SIZES["prod"]).get(category, {})
        return {table: size for size, tables in tables_config.items() for table in tables}

    @staticmethod
    def get_spark_conf(env: str = None, task_type: str = "cdc") -> dict:
        """
        Get Spark configuration for a specific environment and task type.

        Args:
            env (str): Environment ('dev' or 'prod'). If None, uses Config.ENV
            task_type (str): Type of task ('cdc', 'cdc_hive', etc.)

        Returns:
            dict: Spark configuration for the specified environment and task type
        """
        env = env or Config.ENV
        return SPARK_CONF.get(env, {}).get(task_type, {})

    TABLES_TO_SIZE_CDC = get_table_size_mapping.__func__("dev" if Config.ENV == "dev" else "prod", "cdc")