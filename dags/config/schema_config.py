from common.list import merge_dictionaries
from metadata.compaction import TABLES as COMPACTION_TABLES
from metadata.metabase import TABLES as METABASE_TABLES
from metadata.pii import SCHEMAS

class SchemaConfig:
    @staticmethod
    def get_compaction_schemas(env):
        return merge_dictionaries(COMPACTION_TABLES[env], METABASE_TABLES[env]).keys()

    @staticmethod
    def get_pii_schemas(env):
        return SCHEMAS[env]