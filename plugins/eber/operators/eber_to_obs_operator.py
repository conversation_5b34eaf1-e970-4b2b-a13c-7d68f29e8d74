import json
import logging

import uuid

import pandas as pd
from airflow.models import Base<PERSON><PERSON>ator, SkipMixin

from eber.hooks.eber_hook import EberHook
from huawei.hooks.obs_hook import HuaweiOBSHook

class EberToOBSOperator(BaseOperator, SkipMixin):
    """
    Eber to OBS Operator

    Fetches data from <PERSON>ber (https://api.eber.co/api/v3/home) and stores it in Object Block Storage (OBS). Depending on
    the entity, either incremental or full data is returned (e.g. full data for users and incremental for transactions).

    Data will be stored in the path {obs_bucket}/{obs_folder_path}/nv_updated_date={date}

    :param entity_config:   Eber entity config to fetch data (e.g. users, transactions)
    :type entity_config:    dict
    :param obs_bucket:      OBS bucket to upload data file to
    :type obs_bucket:       string
    :param obs_folder_path: OBS folder path
    :type obs_folder_path:  string
    :param eber_conn_id:    Eber connection ID
    :type eber_conn_id:     string
    :param obs_conn_id:     OBS connection ID
    :type obs_conn_id:      string
    """

    PARTITION_COLUMN = "nv_updated_date"

    def __init__(
        self,
        entity_config,
        obs_bucket,
        obs_folder_path,
        eber_conn_id="eber_default",
        obs_conn_id="hwc",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.obs_bucket = obs_bucket
        self.obs_folder_path = obs_folder_path
        self.eber_conn_id = eber_conn_id
        self.obs_conn_id = obs_conn_id

    def _fetch_latest_data(self, exec_date):
        """
        Fetches data from the API and returns it as a data frame.
        :param execution_date:      Task execution date
        :type execution_date:       datetime
        :param next_execution_date: Next task execution date
        :type next_execution_date:  datetime
        :return:                    Data frame containing data
        :rtype:                     pandas.DataFrame
        """
        entity = self.entity_config["entity"]
        logging.info(f"Fetching Eber {entity} data")
        eber_hook = EberHook(self.eber_conn_id)
        path_fmt = self.entity_config["path"]
        fetch_all = self.entity_config["fetch_type"] == "all"
        query_params = {"limit": 100}
        if not fetch_all:
            from_field, to_field = self.entity_config["incremental_range_fields"]
            query_params[from_field] = exec_date.to_date_string()
            # add 2 days as the from/to dates follow local timezone
            query_params[to_field] = exec_date.add(days=2).to_date_string()
        data = eber_hook.get_data(path_fmt, query_params=query_params)
        logging.info("Fetched data")

        if not data:
            logging.info("No records retrieved")
            return pd.DataFrame()

        updated_at = self.entity_config["updated_at_field"]
        # filter by updated_at values for endpoints that fetch all data (e.g. users)
        exec_date_dt = exec_date.to_datetime_string()
        df = pd.DataFrame(data)
        if not fetch_all:
            df = df[df[updated_at] >= exec_date_dt]
        logging.info(f"No. of records: {len(df.index)}")
        return df

    def _normalize_schema(self, df, exec_date, fetch_all=False):
        """
        Normalizes the schema of the data frame to match the entity config.
        :param df: Data frame to normalize
        :type df:  pandas.DataFrame
        :param exec_date: Execution date
        :type exec_date: datetime
        :param fetch_all: Whether to fetch all data or incremental data
        :type fetch_all: bool
        :return:   Normalized data frame
        :rtype:    pandas.DataFrame
        """
        
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        df = df.dropna(axis=1, how="all")
        
        # Get the updated_at field from entity_config
        updated_at = self.entity_config["updated_at_field"]

        schema = self.entity_config.get("schema", {})
        columns = df.columns
        try:
            df_schema = {column: schema[column] for column in columns}
            for column in columns:
                dtype = schema[column]
                if dtype == "datetime64":
                    dtype = "datetime64[ns]"
                df_schema[column] = dtype
        except KeyError as e:
            new_column = str(e).strip("'")
            raise KeyError(
                f"Data type for '{new_column}' is undefined. "
                f"Sample values: {df[df[new_column].notnull()][new_column][:5].tolist()}"
            ) from e
        df = df.astype(df_schema)

        nested_columns = self.entity_config.get("nested_columns", set()).intersection(columns)
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        if fetch_all:
            df[self.PARTITION_COLUMN] = exec_date.strftime("%Y-%m-%d")
        else:
            df[self.PARTITION_COLUMN] = df[updated_at].dt.date
        return df

    def _save_to_obs(self, df):
        """
        Saves the data frame to OBS.
        :param df: Data frame to save
        :type df:  pandas.DataFrame
        :return:   None
        """

        system_id = self.entity_config["system_id"]
        obs_hook = HuaweiOBSHook()
        logging.info("Uploading to OBS bucket")
        for partition_date, group_df in df.groupby(self.PARTITION_COLUMN):
            logging.info(f"Writing records into nv_updated_at={partition_date} partition...")
            # Only need this column to partition the data
            group_df = group_df.drop(columns=[self.PARTITION_COLUMN])
            df_parq_obj = group_df.to_parquet(
                compression="snappy", engine="pyarrow", index=False
            )
            file_name = uuid.uuid4().hex

            obs_hook.streaming_upload(
                self.obs_bucket,
                f"{self.obs_folder_path}/system_id={system_id}/nv_updated_date={partition_date}/{file_name}.parquet",
                df_parq_obj,
            )
            logging.info("Uploading Eber data to OBS")
        logging.info("Uploaded Eber data to OBS")

    def execute(self, context):
        """
        Fetches Eber entity data and stores it in OBS in parquet format.

        :param context: Task context
        """
        exec_date = context["execution_date"]
        df = self._fetch_latest_data(exec_date)
        if df.empty:
            logging.info("No records retrieved")
            return
        fetch_all = self.entity_config["fetch_type"] == "all"
        df = self._normalize_schema(df, exec_date, fetch_all)
        self._save_to_obs(df)
