import logging
from tempfile import NamedTemporaryFile

import pandas as pd
from airflow.models import BaseOperator
from huawei.hooks.obs_hook import HuaweiOBSHook

from gsheets.hooks.gsheets_hook import GoogleSheetsHook


class GoogleSheetsToObsOperator(BaseOperator):
    """
    Google Sheets to OBS Operator

    Reads data from a specified sheet in a Google spreadsheet and stores it in OBS. Note that the sheet should have
    unique headers (first non-empty row) and none of the headers should be blank. The file will be uploaded to the path
    {obs_bucket}/{obs_folder_path}/{spreadsheet_id}_{sheet_name}.parquet

    :param spreadsheet_id:  Google Sheets spreadsheet ID (note not sheet ID)
                            (https://docs.google.com/spreadsheets/d/:spreadsheetId/edit#gid=:sheetId)
    :type spreadsheet_id:   string
    :param obs_bucket:      Object Storage (obs) bucket to upload file to
    :type obs_bucket:       string
    :param obs_folder_path: obs folder path
    :type obs_folder_path:  string
    :param sheet_name       Google Sheets sheet name (uses first sheet if not specified)
    :type sheet_name        string
    :param gsheets_conn_id: Google Sheets connection ID
    :type gsheets_conn_id:  string
    :param obs_conn_id:     obs connection ID
    :type obs_conn_id:      string

    """

    def __init__(
        self,
        spreadsheet_id,
        obs_bucket,
        obs_folder_path,
        sheet_name=None,
        gsheets_conn_id="google_cloud_default",
        obs_conn_id="hwc",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.spreadsheet_id = spreadsheet_id
        self.obs_bucket = obs_bucket
        self.obs_folder_path = obs_folder_path
        self.sheet_name = sheet_name.lower().strip() if sheet_name else None
        self.gsheets_conn_id = gsheets_conn_id
        self.obs_conn_id = obs_conn_id

    def execute(self, context):
        """
        Executes the Google Sheets to obs data transfer.

        :param context: Task context (not used)
        """
        logging.info(f"Fetching spreadsheet ID: {self.spreadsheet_id}, sheet name: {self.sheet_name}")
        gsheets_hook = GoogleSheetsHook(self.spreadsheet_id, conn_id=self.gsheets_conn_id)
        gsheets_resp = gsheets_hook.get_spreadsheet()
        logging.info("Fetched spreadsheet")
        filename, df = self._gsheets_resp_to_df(gsheets_resp, self.sheet_name)

        file_path = f"{self.obs_folder_path}/{filename}/1.parquet"
        logging.info(f"Uploading to OBS bucket: {self.obs_bucket}, path: {file_path}")
        obs_hook = HuaweiOBSHook(conn_id=self.obs_conn_id)
        with NamedTemporaryFile("w") as tmp:
            df.to_parquet(tmp.name, compression="snappy", engine="fastparquet", index=False)
            obs_hook.upload(self.obs_bucket, file_path, tmp.name)
        logging.info("Uploaded sheet data to OBS")

    @staticmethod
    def _gsheets_resp_to_df(gsheets_resp, sheet_name=None):
        """
        Converts spreadsheet API response to a Pandas dataframe. Uses data from the sheet if specified, else uses data
        from the first sheet in the response. Also returns a filename that can be used when uploading files.

        :param gsheets_resp:    spreadsheet API response
        :type gsheets_resp:     dict
        :param sheet_name:      sheet name
        :type sheet_name:       string
        :return:                file name and Pandas dataframe
        """
        spreadsheet_title = gsheets_resp["properties"]["title"]
        sheets = gsheets_resp["sheets"]
        # Filter by sheet_name if defined and found, else get first sheet
        sheet = sheets[0]
        if sheet_name:
            for s in sheets:
                name = s["properties"]["title"].lower().strip()
                if sheet_name == name:
                    sheet = s
                    break
        sheet_title = sheet["properties"]["title"]
        output = []
        data = sheet["data"][0]["rowData"]
        for row in data:
            row_data = []
            if not row:
                continue
            for value in row["values"]:
                # append missing cells too
                row_data.append(value.get("formattedValue"))
            output.append(row_data)
        filename = f"{spreadsheet_title}/{sheet_title}".lower().strip().replace(" ", "_")
        headers = output.pop(0)
        return filename, pd.DataFrame(output, columns=headers)
