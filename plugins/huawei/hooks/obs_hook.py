import logging
import os

from airflow.exceptions import AirflowNotFoundException
from airflow.hooks.base import Base<PERSON>ook
from obs import ObsClient
from typing import List


class HuaweiOBSHook(BaseHook):
    """
    Custom Airflow hook to connect to Huawei Cloud OBS.
    """

    def __init__(self, conn_id="hwc"):
        super().__init__()
        self.client = None
        self.conn_id = conn_id

    def get_conn(self):
        """
        Establish connection to Huawei OBS using the connection details in Airflow's connection manager.
        """
        try:
            # Attempt to get the connection from Airflow
            conn = self.get_connection(self.conn_id)
            logging.info(f"Connection details from Airflow: {conn}")
            access_key = conn.login
            secret_key = conn.password
            endpoint = conn.host
            logging.info(f"Connection details from Airflow: {access_key}, {secret_key}, {endpoint}")
            if not (access_key and secret_key and endpoint):
                raise ValueError("Connection details from Airflow are incomplete (access_key, secret_key, endpoint).")
            logging.info("Using Airflow connection for OBS")
        except AirflowNotFoundException:
            # Fallback to environment variables if connection ID is not found
            logging.warning(f"Connection ID `{self.conn_id}` not found, falling back to environment variables.")
            access_key = os.environ.get("OBS_ACCESS_KEY")
            secret_key = os.environ.get("OBS_SECRET_KEY")
            endpoint = os.environ.get("OBS_ENDPOINT")
            if not (access_key and secret_key and endpoint):
                raise ValueError(
                    "Environment variables for OBS are incomplete (OBS_ACCESS_KEY, OBS_SECRET_KEY, OBS_ENDPOINT)."
                )

        self.client = ObsClient(access_key_id=access_key, secret_access_key=secret_key, server=endpoint)
        logging.info("Connected to Huawei Cloud OBS")
        return self.client

    def download(self, bucket_name, object_name, filename):
        """
        Download a file from Huawei Cloud OBS

        :param bucket_name: Name of the OBS bucket
        :param object_name: Name of the object in the bucket
        :param filename: Path of the file in the local FS to save the downloaded object
        """
        if not self.client:
            self.get_conn()  # Ensure connection is established

        try:
            # Download the file
            resp = self.client.getObject(bucketName=bucket_name, objectKey=object_name, downloadPath=filename)

            if resp.status >= 300:
                logging.error(f"Failed to downoad file from {bucket_name}/{object_name}")
                raise Exception(f"{resp.errorMessage}")

            logging.info(f"Successfully downloaded from {bucket_name}/{object_name}!")
            # Return the path of the download file in the local FS
            return resp.body.url

        except Exception as e:
            logging.error(f"Error downloading file from OBS")
            raise

    def streaming_upload(self, bucket_name, object_key, file) -> None:
        """
        Upload an readable object to HWC OBS
        https://support.huaweicloud.com/intl/en-us/sdk-python-devg-obs/obs_22_0902.html

        :param bucket_name: Name of the OBS bucket
        :param object_key: Key (path) of the object in the bucket
        :param file_path: Local path of the file to upload
        :return: Response from the upload operation
        """
        if not self.client:
            self.get_conn()

        try:
            resp = self.client.putContent(bucket_name, object_key, file)

            # Check if upload was a success
            if resp.status >= 300:
                logging.error(f"Failed to upload file to {bucket_name}/{object_key}. Error: {resp.errorMessage}")
                raise Exception(f"Upload failed: {resp.errorMessage}")

            logging.info(f"Successfully uploaded file to {bucket_name}/{object_key}")
            return resp

        except Exception as e:
            logging.error(f"Error uploading file to OBS: {str(e)}")
            raise

    def upload(self, bucket_name, object_key, file_path):
        """
        Upload a file to Huawei Cloud OBS.

        :param bucket_name: Name of the OBS bucket
        :param object_key: Key (path) of the object in the bucket
        :param file_path: Local path of the file to upload
        :return: Response from the upload operation
        """
        if not self.client:
            self.get_conn()  # Ensure connection is established

        try:
            # Upload the file
            resp = self.client.putFile(bucket_name, object_key, file_path)

            # Check if upload was successful
            if resp.status >= 300:
                logging.error(f"Failed to upload file to {bucket_name}/{object_key}. Error: {resp.errorMessage}")
                raise Exception(f"Upload failed: {resp.errorMessage}")

            logging.info(f"Successfully uploaded file to {bucket_name}/{object_key}")
            return resp

        except Exception as e:
            logging.error(f"Error uploading file to OBS: {str(e)}")
            raise

    def list_objects(self, bucket_name: str, prefix: str, delimiter: str = None) -> List[str]:
        """
        List objects in an OBS bucket.

        :param bucket_name: Name of the OBS bucket
        :param prefix: Filter objects whose keys begin with this prefix
        :return: List of objects matching the criteria

        Note: This function's behavior is undefined if the delimiter is not a file extension!!!

        Behaviour:
            The result will also include the matching files in current directory and the subdirectories as well

            e.g.
            - A/a.csv
            - A/B/b.csv
            - A/C/c.json
            - A/a.json

            If delimiter is passed as `.csv`
            then result = ["A/a.csv", "A/B/b.csv"]
        """
        if not self.client:
            self.get_conn()  # Ensure connection is established

        try:
            resp = self.client.listObjects(bucket_name, prefix=prefix, delimiter=delimiter)
            # Check if listing was successful
            if resp.status >= 300:
                logging.error(f"Failed to list objects in bucket {bucket_name}. Error: {resp.errorMessage}")
                raise Exception(f"List objects failed: {resp.errorMessage}")

            logging.info(f"Successfully retrieved objects from bucket {bucket_name} with prefix '{prefix}'")
            # `commonPrefixs` field has all the fully qualified object names matching `<prefix><sub-folder-path><delimiter>`
            if delimiter is not None:
                return [obj["prefix"] for obj in resp.body.commonPrefixs]

            # When no delimiter is passed, all the objs are returned in a single list including the content of the file
            # Extract only the keys (names of objects)
            return [obj.key for obj in resp.body.contents] if resp.body.contents else []

        except Exception as e:
            logging.error(f"Error listing objects in OBS: {str(e)}")
            raise
