import logging
from airflow.sensors.base import BaseSensorOperator
from huawei.hooks.obs_hook import HuaweiOBSHook


class HuaweiOBSObjectsWithPrefixExistenceSensor(BaseSensorOperator):
    """
    Custom Airflow sensor to check if an object exists in a Huawei OBS bucket.
    """

    template_fields = (
        "bucket_name",
        "prefix",
    )

    def __init__(self, bucket_name, prefix, conn_id="hwc", *args, **kwargs):
        """
        :param bucket_name: Name of the OBS bucket
        :param prefix: Path and name of the object, or prefix to check in the bucket
        :param conn_id: Airflow connection ID to Huawei OBS (default: 'huawei_obs_default')
        """
        super().__init__(*args, **kwargs)
        self.bucket_name = bucket_name
        self.prefix = prefix
        self.conn_id = conn_id

    def poke(self, context):
        """
        This method is called to check if the object or prefix exists.
        Returns True if the object exists, otherwise returns False.
        """
        hook = HuaweiOBSHook(conn_id=self.conn_id)
        client = hook.get_conn()

        try:
            # Use listObjects to check if the prefix or object exists in OBS bucket
            response = client.listObjects(self.bucket_name, prefix=self.prefix)

            if response.body.contents:
                logging.info(f"Prefix '{self.prefix}' exists in bucket '{self.bucket_name}'.")
                return True
            else:
                logging.info(f"Prefix '{self.prefix}' does not exist in bucket '{self.bucket_name}'.")
                return False
        except Exception as e:
            logging.error(f"Failed to check prefix in OBS. Error: {str(e)}")
            return False
