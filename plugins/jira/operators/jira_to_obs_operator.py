import json
import logging
import os
import shutil
import tempfile

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.jira.hooks.jira import JiraHook

from huawei.hooks.obs_hook import HuaweiOBSHook


class JiraToObsOperator(BaseOperator):
    """
    <PERSON>ra to OBS Operator

    Fetches Jira data and stores it in Huawei OBS (OBS). Note that only issue data can be fetched at present
    with this operator.

    Data will be stored in the path obs://{obs_bucket}/{obs_folder_path}/nv_updated_date={date}

    :param entity_config:   Jira entity config to fetch data
    :type entity_config:    dict
    :param obs_bucket:      OBS bucket to upload data file to
    :type obs_bucket:       string
    :param obs_folder_path: OBS folder path
    :type obs_folder_path:  string
    :param jira_conn_id:    Jira connection ID
    :type jira_connd_id:    string
    :param obs_conn_id:     OBS connection ID
    :type obs_conn_id:      string
    """

    PARTITION_COLUMN = "nv_updated_date"
    LOCAL_TEMP_PATH = "/tmp"

    def __init__(
        self,
        entity_config,
        obs_bucket,
        obs_folder_path,
        jira_conn_id="jira_default",
        obs_conn_id="hwc",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.obs_bucket = obs_bucket
        self.obs_folder_path = obs_folder_path
        self.obs_conn_id = obs_conn_id
        self.jira_conn_id = jira_conn_id

    def execute(self, context):
        """
        Fetches Jira data and stores it in OBS in parquet format.

        :param context: Task context
        """
        # Check snapshot or incremental data fetch
        obs_hook = HuaweiOBSHook(self.obs_conn_id)
        blobs = obs_hook.list_objects(bucket_name=self.obs_bucket, prefix=self.obs_folder_path)
        snapshot = len(blobs) == 0 or context["params"].get("snapshot", False)
        jira_hook = JiraHook(jira_conn_id=self.jira_conn_id)
        jira_client = jira_hook.client
        opts = {
            "startAt": 0,  # note: camel case and 0-indexed
            "maxResults": 50,  # note: camel case
            "expand": "changelog",
            "json_result": True,
        }
        # Note that the query is in local (SG) time
        jql_str = "" if snapshot else f"updated >= '{context['ds']}'"
        logging.info(f'Fetching Jira issues with JQL: "{jql_str}"')
        data = []
        response = jira_client.search_issues(jql_str, **opts)
        total = response["total"]
        logging.info(f"No. of query results: {total}")
        data.extend(response["issues"])
        while len(data) < total:
            logging.info(f"No. of records: {len(data)}")
            next_opts = {**opts, "startAt": len(data)}
            response = jira_client.search_issues(jql_str, **next_opts)
            data.extend(response["issues"])
        logging.info(f"No. of records: {len(data)}")
        if len(data) == 0:
            return
        # Upload data as parquet file
        df = pd.DataFrame(data)
        nested_columns = self.entity_config.get("nested_columns", [])
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        exec_date = context["execution_date"].date()
        df[self.PARTITION_COLUMN] = exec_date
        local_dir = f"{self.LOCAL_TEMP_PATH}/jira_{exec_date}"
        os.makedirs(local_dir, exist_ok=True)

        # Write DataFrame to a local parquet file
        df.to_parquet(
            local_dir, compression="snappy", engine="pyarrow", index=False, partition_cols=[self.PARTITION_COLUMN]
        )
        logging.info(f"DataFrame written to temporary directory: {local_dir}")

        # Define the OBS path for upload
        obs_path = f"{self.obs_folder_path}/nv_updated_date={exec_date}"
        self.upload_to_obs(obs_hook, local_dir, obs_path)

        # Clean up the local directory
        shutil.rmtree(local_dir)
        logging.info(f"Temporary local directory {local_dir} removed.")

    def upload_to_obs(self, obs_hook, local_dir, obs_path):
        """Uploads all files from a local directory to OBS."""
        for root, _, files in os.walk(local_dir):
            for file in files:
                # Construct the full path of the local file
                file_path = os.path.join(root, file)

                # Create the corresponding OBS path without repeating `nv_updated_date=YYYY-MM-DD`
                obs_file_path = f"{obs_path}/{file}"  # Directly add the file name to the target OBS path

                logging.info(f"Uploading {file_path} to {obs_file_path}")

                # Upload the file to OBS
                obs_hook.upload(self.obs_bucket, obs_file_path, file_path)
