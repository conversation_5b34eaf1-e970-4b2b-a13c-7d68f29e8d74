import uuid
import json
import logging

import pandas as pd
import pendulum
from airflow.models import Base<PERSON>perator, SkipMixin

from common import date
from metadata.shopify import SHOPIFY_MAX_RESULTS_LIMIT
from shopify.hooks.shopify_hook import ShopifyHook
from huawei.hooks.obs_hook import <PERSON>awei<PERSON>BSHook


DATE_FORMAT = "YYYY-MM-DDTHH:mm:ss"


def _convert_execution_date_tz(system_id, execution_date, next_execution_date):
    """
    Convert execution_date, next_execution_date to local timezone. For example, execution_date with
    2022-02-13T16:00:00 will be converted to 2022-02-14T00:00:00. Results in (2022-02-13, 2022-02-14]
    will be fetched.

    :type execution_date:      pendulum.datetime
    :type next_execution_date: pendulum.datetime
    :return type: str
    """
    local_timezone = pendulum.timezone(getattr(date.Timezone, system_id.upper()))
    # Add one seconds to avoid duplicates. As a result, data is fetched in a half open half closed
    # interval (execution_date, next_execution_date]
    execution_date = local_timezone.convert(execution_date.add(seconds=1)).to_iso8601_string()
    next_execution_date = local_timezone.convert(next_execution_date).to_iso8601_string()
    return execution_date, next_execution_date


def normalize_time(time):
    """
    normalize time to UTC time and change the format to YYYY-MM-DDTHH:mm:ss

    :type time: str
    :return: normalized time
    """
    if not time:
        return
    return str(pendulum.parse(time).in_tz("UTC").format(DATE_FORMAT))


class ShopifyToOBSOperator(BaseOperator, SkipMixin):
    """
    Shopify to OBS Operator

    Fetches data from Shopify API and stores it in Huawei cloud OBS.
    Data will be stored in the path {obs_bucket}/{obs_folder_path}/nv_updated_date={date}

    :param entity_config:   Shopify entity config to fetch data (e.g. orders, customers)
    :type entity_config:    dict
    :param obs_bucket:      OBS bucket to upload data file to
    :type obs_bucket:       string
    :param obs_folder_path: OBS folder path
    :type obs_folder_path:  string
    :param eber_conn_id:    Shopify connection ID
    :type eber_conn_id:     string
    :param obs_conn_id:     OBS connection ID
    :type obs_conn_id:      string
    """

    PARTITION_COLUMN = "nv_updated_date"

    def __init__(
        self,
        entity_config,
        obs_bucket,
        obs_folder_path,
        shopify_conn_id="shopify_default",
        obs_conn_id="hwc",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.obs_bucket = obs_bucket
        self.obs_folder_path = obs_folder_path
        self.shopify_conn_id = shopify_conn_id
        self.obs_conn_id = obs_conn_id

    def _generate_request_params(self, execution_date, next_execution_date):
        """
        Generate request parameters based on execution_date. Data is filtered by incremental_range_fields.
        :param execution_date: Execution date
        :return:               Request query parameters
        """
        query_params = {"limit": SHOPIFY_MAX_RESULTS_LIMIT}
        from_field, to_field = self.entity_config["incremental_range_fields"]
        query_params[from_field] = execution_date
        query_params[to_field] = next_execution_date
        return query_params

    def _fetch_latest_data(self, execution_date, next_execution_date):
        """
        Fetch latest Shopify data by ShopifyHook

        :param execution_date: Execution date
        :return:               Shopify entity data
        :type:                 pandas.DataFrame
        """
        entity = self.entity_config["entity"]
        logging.info(f"Fetching Shopify {entity} data")

        path_fmt = self.entity_config["path"]
        system_id = self.entity_config["system_id"]
        execution_date, next_execution_date = _convert_execution_date_tz(system_id, execution_date, next_execution_date)
        logging.info(f"Fetching Shopify {entity} data from {execution_date} to {next_execution_date}")

        query_params = self._generate_request_params(execution_date, next_execution_date)
        shopify_hook = ShopifyHook(self.shopify_conn_id)
        data = shopify_hook.get_data(path_fmt, entity, query_params)
        return pd.DataFrame(data)

    def _normalize_schema(self, df):
        """
        Normalize dataframe by dropping null columns, updating schema, uniforming timezone, and
        mapping nested columns.

        :param df: pandas DataFrame to be normalized
        :return: pandas.DataFrame
        """
        logging.info(f"Available columns in DataFrame: {df.columns.tolist()}")
        logging.info(f"Configured time fields: {self.entity_config.get('time_fields', {})}")
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        null_columns = df.columns[df.isna().all()].tolist()
        df = df.drop(null_columns, axis=1)

        # change timezone to UTC
        time_fields = set(self.entity_config.get("time_fields", {})) - set(null_columns)
        existing_time_fields = time_fields.intersection(set(df.columns))
        for time_field in existing_time_fields:
            try:
                df[time_field] = df[time_field].apply(lambda dt: normalize_time(dt))
            except KeyError as e:
                logging.warning(f"Column '{time_field}' not found in DataFrame. Skipping normalization.")
                continue
        # update schema
        schema = self.entity_config.get("schema", {})
        columns = df.columns
        try:
            df_schema = {column: schema[column] for column in columns}
        except KeyError as e:
            new_column = str(e).strip("'")
            raise KeyError(
                f"Data type for '{new_column}' is undefined. "
                f"Sample values: {df[df[new_column].notnull()][new_column][:5].tolist()}"
            ) from e
        df = df.astype(df_schema)

        # map nested_columns
        nested_columns = self.entity_config.get("nested_columns", set()).intersection(columns)
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        df[self.PARTITION_COLUMN] = df[self.entity_config["updated_at_field"]].dt.date
        return df

    def _save_to_obs(self, df):
        """
        Save clean data to OBS bucket.

        :param df:  Data to be saved
        :type df:   pandas.DataFrame
        :return:    None
        """
        system_id = self.entity_config["system_id"]
        # pd.to_parquet can automatically create partitions on the blog storage and then store the data
        # but, it does not understand obs://
        # So partition the df and write it manually using OBS sdk
        logging.info("Uploading to OBS bucket")

        obs_hook = HuaweiOBSHook()
        for partition_date, group_df in df.groupby(self.PARTITION_COLUMN):
            logging.info(f"Writing records into nv_updated_at={partition_date} partition...")
            # Only need this column to partition the data
            group_df = group_df.drop(columns=[self.PARTITION_COLUMN])
            df_parq_obj = group_df.to_parquet(
                compression="snappy",
                engine="pyarrow",
                index=False,
            )

            file_name = uuid.uuid4().hex
            obs_hook.streaming_upload(
                self.obs_bucket,
                f"{self.obs_folder_path}/system_id={system_id}/nv_updated_date={partition_date}/{file_name}.parquet",
                df_parq_obj,
            )
            logging.info(f"nv_updated_at={partition_date} partition upload is succesful!")

        logging.info("Uploaded Shopify data to OBS")

    def execute(self, context):
        """
        Fetches Shopify entity data and stores it in GCS in parquet format.

        :param context:         Task context
        """

        execution_date = context["execution_date"]
        next_execution_date = context["next_execution_date"]
        df = self._fetch_latest_data(execution_date, next_execution_date)

        logging.info("Fetched data")

        if df.empty:
            logging.info("No records retrieved")
            return
        df = self._normalize_schema(df)
        self._save_to_obs(df)
