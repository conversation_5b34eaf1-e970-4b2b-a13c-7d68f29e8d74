import os
import shutil

from airflow.models import <PERSON><PERSON>perator, SkipMixin

from amast.hooks.amast_hook import AmastHook
from huawei.hooks.obs_hook import HuaweiOBSHook

PARTITION_COLUMN = "nv_updated_date"
LOCAL_TEMP_PATH = "/tmp"


class AmastToOBSOperator(BaseOperator, SkipMixin):
    def __init__(self, entity, obs_bucket, system_id, conn_id, **kwargs):
        super().__init__(**kwargs)
        self.entity = entity
        self.obs_bucket = obs_bucket
        self.system_id = system_id
        self.conn_id = conn_id

    def execute(self, context):
        exec_date = context["execution_date"].strftime("%Y-%m-%d")
        hook = AmastHook(self.system_id)

        obs_conn = HuaweiOBSHook(self.conn_id)
        obs_client = obs_conn.get_conn()

        print("self.latest_date", exec_date)
        df = hook.daily_pull(self.entity, exec_date)

        if len(df) == 0:
            print(f"No data found for {self.entity} on {exec_date}")
            self._skip_downstream_tasks(context)
            return

        df[PARTITION_COLUMN] = exec_date
        local_dir = f"{LOCAL_TEMP_PATH}/{self.entity}_{exec_date}"
        df.to_parquet(local_dir, compression="snappy", engine="pyarrow", index=False, partition_cols=[PARTITION_COLUMN])
        obs_path = f"amast/{self.entity}/{self.system_id}"
        self.upload_to_obs(obs_client, local_dir, obs_path)
        shutil.rmtree(local_dir)
        print(f"Uploaded to OBS bucket at {obs_path}")

    def upload_to_obs(self, obs_client, local_dir, obs_path):
        """Uploads all files from a local directory to OBS."""
        for root, _, files in os.walk(local_dir):
            for file in files:
                # Construct the full path of the local file
                file_path = os.path.join(root, file)

                # Create the corresponding OBS path
                relative_path = os.path.relpath(file_path, local_dir)
                obs_file_path = os.path.join(obs_path, relative_path)

                print(f"Uploading {file_path} to {obs_file_path}")

                # Upload the file to OBS
                with open(file_path, "rb") as f:
                    resp = obs_client.putObject(self.obs_bucket, obs_file_path, f)
                    if resp.status < 300:
                        print(f"Successfully uploaded {obs_file_path}")
                    else:
                        raise Exception(f"Failed to upload {obs_file_path}: {resp.errorMessage}")

    def _skip_downstream_tasks(self, context):
        downstream_tasks = context["task"].get_flat_relatives(upstream=False)
        if downstream_tasks:
            print(f"Skipping downstream tasks: {downstream_tasks}")
            self.skip(context["dag_run"], context["ti"].execution_date, downstream_tasks)
