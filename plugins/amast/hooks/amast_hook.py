import pandas as pd
import requests
from airflow.hooks.base import BaseHook

from metadata.amast import AMAST_CONFIG


class AmastHook(BaseHook):
    def __init__(self, system_id):
        self.system_id = system_id

        if system_id == "vn":
            conn = self.get_connection("amast_vn")
        elif system_id == "my":
            conn = self.get_connection("amast_my")
        self.url = conn.host
        self.header = {
            "Authorization": f"{conn.password}",
        }

    def daily_pull(self, entity, latest_date):
        filter_column = AMAST_CONFIG[self.system_id][entity]["filter_column"]
        full_url = f"{self.url}/{entity}?{filter_column}=gt.'{latest_date}'"
        endpoint_json = requests.get(full_url, headers=self.header).json()
        endpoint_table = pd.json_normalize(endpoint_json)
        print("There are", endpoint_table.shape[0], "new", entity, "records")
        return endpoint_table
