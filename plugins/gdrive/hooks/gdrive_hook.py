import io
import logging

from airflow.providers.google.common.hooks.base_google import GoogleBaseHook
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload


class GoogleDriveHook(GoogleBaseHook):
    """
    A Google Drive hook for listing and reading files through the Google Drive API.
    (https://developers.google.com/drive/api/v3/about-sdk)

    :param gcp_conn_id: Google Drive connection ID
    :type gcp_conn_id:  string
    """

    def __init__(self, conn_id="google_cloud_default"):
        # https://github.com/apache/airflow/blob/master/airflow/providers/google/suite/hooks/drive.py
        super().__init__(gcp_conn_id=conn_id)
        self.conn_id = conn_id
        self._conn = None

    def get_conn(self):
        """
        Returns a Google Drive service object.

        :return: Google Drive service object
        """
        if not self._conn:
            http_authorized = self._authorize()
            self._conn = build("drive", "v3", http=http_authorized, cache_discovery=False)
        return self._conn

    def get_excel_files(self, gdrive_folder_id):
        """
        Gets information about all Excel files (.csv, .xls, .xlsx) in a given Google Drive folder.
        Returns an empty list if no Excel file is found.

        :param gdrive_folder_id:    Google Drive folder ID (e.g. 18C5XQ1AZHAliARMmM_U7GTdXp1P4CAHy)
        :return: first Excel file data if found, else None
        """
        # TODO (ssk): handle Google Sheets file type
        q = (
            f"'{gdrive_folder_id}' in parents and "
            "(mimeType = 'application/vnd.ms-excel' or "
            "mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or "
            "mimeType = 'text/csv')"
        )
        logging.info(f"query is {q}")
        service = self.get_conn()
        logging.info(f"{service}")
        response = service.files().list(q=q, spaces="drive", fields="files(id, name)").execute()
        logging.info(f"Printing Gdrive Response: {response}")
        return response.get("files", [])

    def download_file(self, gdrive_file_id):
        """
        Downloads file from Google Drive.

        :param gdrive_file_id:  Google Drive file ID
        :return: file as byte array
        """
        service = self.get_conn()
        request = service.files().get_media(fileId=gdrive_file_id)
        fh = io.BytesIO()
        download = MediaIoBaseDownload(fh, request)
        done = False
        while not done:
            status, done = download.next_chunk()
            logging.info(f"Downloaded {int(status.progress() * 100)}%")
        return fh

    def move_file(self, gdrive_file_id, src_gdrive_folder_id, dest_folder_name):
        """
        Moves a Google Drive file from a source folder to the destination folder. Creates the destination folder if it
        does not exist.

        :param gdrive_file_id:      Google Drive file ID
        :param gdrive_folder_id:    Google Drive folder ID
        :param dest_folder_name:    Google Drive destination folder name
        """
        service = self.get_conn()
        dest_gdrive_folder_id = self._get_folder(service, dest_folder_name, src_gdrive_folder_id)
        if not dest_gdrive_folder_id:
            dest_gdrive_folder_id = self._create_folder(service, dest_folder_name, src_gdrive_folder_id)
        file = service.files().get(fileId=gdrive_file_id, fields="parents").execute()
        previous_parents = ",".join(file.get("parents", []))
        logging.info(
            f"Moving file {gdrive_file_id} from folder {src_gdrive_folder_id} to folder {dest_gdrive_folder_id}"
        )
        response = (
            service.files()
            .update(
                fileId=gdrive_file_id,
                addParents=dest_gdrive_folder_id,
                removeParents=previous_parents,
                fields="id, parents",
            )
            .execute()
        )
        logging.info(f"File moved: {response}")

    @staticmethod
    def _get_folder(service, folder_name, parent_folder_id):
        """
        Fetches folder ID of requested folder name in specified parent folder ID.

        :param service:             Google Drive service object
        :param folder_name:         Google Drive folder name
        :param parent_folder_id:    Google Drive parent folder ID
        :return:                    Google Driver folder ID if it exists, else None
        """
        q = (
            f"'{parent_folder_id}' in parents and "
            "mimeType = 'application/vnd.google-apps.folder' and "
            f"name = '{folder_name}'"
        )
        response = service.files().list(q=q, spaces="drive", fields="files(id, name)").execute()
        files = response.get("files", [])
        return files[0]["id"] if files else None

    @staticmethod
    def _create_folder(service, folder_name, parent_folder_id):
        """
        Creates specified folder in specified parent folder.

        :param service:             Google Drive service object
        :param folder_name:         folder name
        :param parent_folder_id:    Google Drive parent folder ID
        :return:
        """
        file_metadata = {
            "name": folder_name,
            "mimeType": "application/vnd.google-apps.folder",
            "parents": [parent_folder_id],
        }
        file = service.files().create(body=file_metadata, fields="id").execute()
        return file.get("id")
