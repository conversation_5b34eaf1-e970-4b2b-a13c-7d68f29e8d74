{"data_asset_type": "Dataset", "expectation_suite_name": "data_warehouse.shipper_sla_days", "expectations": [{"expectation_type": "get_the_stats_of_the_table", "kwargs": {"column_list": ["system_id", "shipper_config_type", "shipper_config", "origin_dest_config_type", "origin_config", "dest_config", "extra_config_type", "extra_config", "sla_type", "start_datetime"], "where_condition": ""}, "meta": {}}]}