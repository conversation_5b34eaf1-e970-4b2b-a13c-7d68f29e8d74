spark.master                                                    local
spark.jars.packages                                             mysql:mysql-connector-java:8.0.14,io.delta:delta-spark_2.12:3.2.0
spark.hadoop.fs.obs.buffer.max.range                            6291456
spark.hadoop.fs.obs.buffer.part.size                            2097152
spark.hadoop.fs.obs.threads.read.core                           500
spark.hadoop.fs.obs.threads.read.max                            1000
spark.hadoop.fs.obs.write.buffer.size                           8192
spark.hadoop.fs.obs.read.buffer.size                            8192
spark.hadoop.fs.obs.connection.maximum                          1000
spark.hadoop.fs.obs.access.key                                  ${env.OBS_ACCESS_KEY}
spark.hadoop.fs.obs.secret.key                                  ${env.OBS_SECRET_KEY}
spark.hadoop.fs.obs.endpoint                                    ${env.OBS_ENDPOINT}
spark.hadoop.fs.obs.buffer.dir                                  /tmp
spark.hadoop.fs.obs.impl                                        org.apache.hadoop.fs.obs.OBSFileSystem
spark.hadoop.fs.AbstractFileSystem.obs.impl                     org.apache.hadoop.fs.obs.OBS
spark.hadoop.fs.obs.connection.ssl.enabled                      false
spark.hadoop.fs.obs.fast.upload                                 true
spark.hadoop.fs.obs.socket.send.buffer                          65536
spark.hadoop.fs.obs.socket.recv.buffer                          65536
spark.hadoop.fs.obs.max.total.tasks                             20
spark.hadoop.fs.obs.threads.max                                 20
spark.sql.extensions                                            io.delta.sql.DeltaSparkSessionExtension
spark.sql.catalog.spark_catalog                                 org.apache.spark.sql.delta.catalog.DeltaCatalog
spark.sql.files.maxPartitionBytes                               128MB
spark.sql.adaptive.coalescePartitions.enabled                   true
spark.sql.adaptive.advisoryPartitionSizeInBytes                 64MB
spark.sql.adaptive.skewJoin.enabled                             true