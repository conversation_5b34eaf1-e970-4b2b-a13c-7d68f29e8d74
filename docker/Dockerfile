# NOTE: paths are relative to the project root since the build context we specify is the project root

FROM apache/airflow:2.10.0-python3.8
USER root

ENV SPARK_HOME /home/<USER>/.local/lib/python3.8/site-packages/pyspark
ENV PATH $PATH:$SPARK_HOME/bin
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update -y \
    && apt-get install -y \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsnappy-dev \
        procps \
        software-properties-common \
        unzip \
        wget \
        apt-transport-https \
    && mkdir -p /etc/apt/keyrings \
    && wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | tee /etc/apt/keyrings/adoptium.asc \
    && echo "deb [signed-by=/etc/apt/keyrings/adoptium.asc] https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update -y \
    && apt-get install -y temurin-8-jdk \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

USER airflow
COPY requirements-spark.txt requirements-spark.txt
RUN pip install --upgrade pip==24.2
RUN pip install -r requirements-spark.txt
RUN pip install delta-spark==3.0.0
COPY requirements-airflow_v2.txt requirements.txt
RUN pip install -r requirements.txt


USER root
RUN mkdir -p $SPARK_HOME/jars \
    && cd $SPARK_HOME/jars \
    && curl -O https://raw.githubusercontent.com/huaweicloud/obsa-hdfs/master/release/hadoop-huaweicloud-3.1.1-hw-54.0.jar \
    && curl -O https://repo1.maven.org/maven2/io/delta/delta-spark_2.12/3.0.0/delta-spark_2.12-3.0.0.jar \
    && curl -O https://repo1.maven.org/maven2/org/apache/spark/spark-protobuf_2.12/3.5.3/spark-protobuf_2.12-3.5.3.jar \
    && curl -O https://repo1.maven.org/maven2/io/delta/delta-storage/3.0.0/delta-storage-3.0.0.jar

RUN ln -s /spark-conf $SPARK_HOME/conf

# zip dags folder for use of spark UDF
COPY dags $AIRFLOW_HOME/dags
RUN mkdir $AIRFLOW_HOME/files \
    && tar -cvzf $AIRFLOW_HOME/files/dags.zip $AIRFLOW_HOME/dags

USER airflow
