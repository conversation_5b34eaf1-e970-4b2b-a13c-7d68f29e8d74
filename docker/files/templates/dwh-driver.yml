---
apiVersion: v1
kind: Pod
metadata:
  annotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: false
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: group
            operator: In
            values:
            - dwh
          - key: role
            operator: In
            values:
            - driver
  tolerations:
  - key: group
    operator: Equal
    value: dwh
    effect: NoSchedule
  - key: role
    operator: Equal
    value: driver
    effect: NoSchedule
