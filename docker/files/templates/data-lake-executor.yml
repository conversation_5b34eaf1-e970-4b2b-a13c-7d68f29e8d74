---
apiVersion: v1
kind: Pod
metadata:
  annotations:
    cluster-autoscaler.kubernetes.io/safe-to-evict: true
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: group
            operator: In
            values:
            - data-lake
  containers:
  - name: spark-kubernetes-executor
    resources:
      requests:
        ephemeral-storage: "2Gi"
  initContainers:
  - name: init-mtu
    image: busybox
    command: ["sh", "-c"]
    args:
      - ip link set eth0 mtu 1460;
    securityContext:
      privileged: true
  tolerations:
  - key: group
    operator: Equal
    value: data-lake
    effect: NoSchedule
