services:

  postgres:
    image: postgres:9.6.23-bullseye
    restart: unless-stopped
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    volumes:
      - "dbdata:/var/lib/postgresql/data"
    ports:
      - "5432:5432"

  initdb:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    depends_on:
      - postgres
    user: "airflow:root"
    volumes:
      - ../variables:/opt/airflow/variables
    entrypoint: /bin/bash
    command:
      - -c
      - |
        airflow db migrate
        if [[ -e /opt/airflow/variables/dev/all.json ]]; then
          airflow variables import /opt/airflow/variables/dev/all.json
        fi
        airflow users create -r Admin -u airflow -e <EMAIL> -f Airflow -l Airflow -p airflow
    env_file: airflow.env

  webserver:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: unless-stopped
    depends_on:
      - initdb
    user: "airflow:root"
    volumes:
      - ../dags:/opt/airflow/dags
      - ../plugins:/opt/airflow/plugins
      - ../logs:/opt/airflow/logs
      - ./spark-conf:/spark-conf
    ports:
      - "8080:8080"
    command: webserver
    healthcheck:
      test: ["CMD-SHELL", "[ -f /opt/airflow/airflow-webserver.pid ]"]
      interval: 30s
      timeout: 30s
      retries: 3
    env_file: airflow.env

  scheduler:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: unless-stopped
    depends_on:
      - initdb
    user: "airflow:root"
    volumes:
      - ../dags:/opt/airflow/dags
      - ../logs:/opt/airflow/logs
      - ../plugins:/opt/airflow/plugins
      - ./spark-conf:/spark-conf
    ports:
      - "4040:4040"
    command: scheduler
    env_file: airflow.env

volumes:
  dbdata:
