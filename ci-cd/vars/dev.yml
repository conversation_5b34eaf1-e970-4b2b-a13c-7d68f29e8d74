---
  hwc_project_id: ninja-van-data-dev

  kubernetes_context: hwc-data-dev-context

  kubernetes_namespace: data-dev

  airflow:
    version: "{{ versions.airflow }}"
    secret_key: "2h0CNqFIsOzRaKdzrW7e9RXMfYuq0Ges"
    fernet_key: "LjrvXDz1foCN0yt5MYWYXlO71QCQaw07674KsgGjwf4="
    domain: "https://airflow-dev.ninjavan.co"

  airflow_web:
    req_cpu: 250
    req_mem: 1536

  airflow_scheduler:
    num_replicas: 1
    req_cpu: 2000
    req_mem: 1100

  airflow_postgres:
    version: 9.6.2
    db_host: ************
    db_schema: airflow
    user: postgres
    password: Ninjitsu89
    pd_size: 100Gi
    req_cpu: 900
    req_mem: 7800
    storage_class: standard

  airflow_postgres_exporter:
    version: v0.5.1

  sql_alchemy_conn: postgresql+psycopg2://{{ airflow_postgres.user }}:{{ airflow_postgres.password }}@{{ airflow_postgres.db_host }}:5432/{{ airflow_postgres.db_schema }}

  git_repo: ssh://*******************:7999/data/airflow.git

  obs_dependencies: nv-data-dev-dependencies
