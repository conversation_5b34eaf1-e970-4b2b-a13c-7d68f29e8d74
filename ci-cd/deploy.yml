---
  - hosts: localhost
    gather_facts: false
    pre_tasks:
      - name: "Including common vars"
        include_vars: "vars/common.yml"
      - name: "Including {{ env }} vars"
        include_vars: "vars/{{ env }}.yml"
    roles:
      - { role: kubernetes, svc: airflow_v2_postgres }
      - { role: kubernetes, svc: spark }
      - { role: kubernetes, svc: airflow_v2 }
#      - { role: kubernetes, svc: airflow_postgres }
#      - { role: kubernetes, svc: airflow_postgres_exporter }
#      - { role: kubernetes, svc: airflow }

