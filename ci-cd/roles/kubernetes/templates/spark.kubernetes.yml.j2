---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ env }}-sparkoperator-spark"
  namespace: "{{ env }}"
  labels:
    app.kubernetes.io/name: sparkoperator
    app.kubernetes.io/instance: "{{ env }}-sparkoperator"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: "{{ env }}-sparkoperator-spark-cr"
  labels:
    app.kubernetes.io/name: sparkoperator
    app.kubernetes.io/instance: "{{ env }}-sparkoperator-spark"
rules:
- apiGroups: [""]
  resources: ["configmaps", "pods", "services"]
  verbs: ["*"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: "{{ env }}-sparkoperator-spark-crb"
  labels:
    app.kubernetes.io/name: sparkoperator
    app.kubernetes.io/instance: "{{ env }}-sparkoperator-spark"
subjects:
  - kind: ServiceAccount
    name: "{{ env }}-sparkoperator-spark"
    namespace: "{{ env }}"
roleRef:
  kind: ClusterRole
  name: "{{ env }}-sparkoperator-spark-cr"
  apiGroup: rbac.authorization.k8s.io
