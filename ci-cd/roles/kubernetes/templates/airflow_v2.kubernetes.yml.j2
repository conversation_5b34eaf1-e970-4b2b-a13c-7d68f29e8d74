---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: "{{ env }}-global-airflow-v2-pdb"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: scheduler
spec:
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow-v2"
      component: scheduler
  maxUnavailable: 1

---
apiVersion: v1
kind: Secret
metadata:
  name: "{{ env }}-global-airflow-v2-env"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: core
type: Opaque
data:
  postgresUser: "{{ airflow_v2_postgres.user | b64encode }}"
  postgresPassword: "{{ airflow_v2_postgres.password | b64encode }}"

---
kind: Secret
apiVersion: v1
metadata:
  name: "{{ env }}-global-airflow-v2-metadata-db"
  labels:
    name: "{{ env }}-global-airflow-v2-metadata-db"
type: Opaque
data:
  connection: "{{ postgresql_alchemy_conn | b64encode }}"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-v2-web-worker-env-cm"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: core
data:
{% include "airflow_v2_cfg_common.properties.j2" %}

  # Webserver and worker config
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_SIZE: "5"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-v2-scheduler-env-cm"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: core
data:
{% include "airflow_v2_cfg_common.properties.j2" %}

  # Scheduler specific config
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_SIZE: "500"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-v2-worker-pod-template-cm"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: worker
data:
  pod_template_file.yaml: |-
    ---
    apiVersion: v1
    kind: Pod
    metadata:
      name: "{{ env }}-global-airflow-v2-worker"
    spec:
      containers:
        - name: worker
          args: []
          command: []
          envFrom:
            - configMapRef:
                name: "{{ env }}-global-airflow-v2-web-worker-env-cm"
          env:
            - name: SPARK_IMAGE_TAG
              value: "{{ spark_image_tag }}"
            - name: SPARK_IMAGE_NAME
              value: "{{ spark_image_name }}"
            - name: IMAGE_REGISTRY
              value: "{{ image_registry }}"
            - name: AIRFLOW__CORE__EXECUTOR
              value: LocalExecutor
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          ports: []
          resources:
            limits:
              memory: 14400Mi
            requests:
              cpu: 500m
              memory: 3600Mi
      hostNetwork: false
      restartPolicy: Never
      serviceAccountName: "{{ env }}-global-airflow-v2"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ env }}-global-airflow-v2"
  labels:
    name: "{{ env }}-global-airflow-v2"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: "{{ env }}-global-airflow-v2"
  labels:
    name: "{{ env }}-global-airflow-v2"
rules:
- apiGroups: [""]
  resources:
  - pods
  - services
  - configmaps
  verbs: ["create", "get", "delete", "list", "watch", "patch", "update"]
- apiGroups: [""]
  resources:
  - "pods/log"
  verbs: ["get", "list"]
- apiGroups: [""]
  resources:
  - "pods/exec"
  verbs: ["create", "get"]
- apiGroups:
  - sparkoperator.k8s.io
  resources:
  - sparkapplications
  - scheduledsparkapplications
  - sparkapplications/status
  - scheduledsparkapplications/status
  verbs:
  - create
  - get
  - delete
  - list
  - watch
  - patch
  - update

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: "{{ env }}-global-airflow-v2"
  labels:
    name: "{{ env }}-global-airflow-v2"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: "{{ env }}-global-airflow-v2"
subjects:
- kind: ServiceAccount
  name: "{{ env }}-global-airflow-v2"
  namespace: "{{ kubernetes_namespace }}"

---
apiVersion: v1
kind: Service
metadata:
  name: "{{ env }}-global-airflow-v2-web-service"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: web
spec:
  type: ClusterIP
  selector:
    name: "{{ env }}-global-airflow-v2"
    component: web
  ports:
    - name: web
      protocol: TCP
      port: 8080

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ env }}-global-airflow-v2-scheduler-deployment"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: scheduler
spec:
  replicas: {{ airflow_v2_scheduler.num_replicas }}
  strategy:
    # Kill the scheduler as soon as possible. It will restart quickly with all the workers,
    # minimizing the time they are not synchronized.
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 100%
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow-v2"
      component: scheduler
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow-v2"
        component: scheduler
    spec:
      tolerations:
      - key: "group"
        operator: "Equal"
        value: "high-cpu"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: group
                operator: In
                values:
                - "high-cpu"
      restartPolicy: Always
      serviceAccountName: "{{ env }}-global-airflow-v2"
      containers:
        - name: scheduler
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args:
            - "scheduler"
          envFrom:
          - configMapRef:
              name: "{{ env }}-global-airflow-v2-scheduler-env-cm"
          env:
            - name: SPARK_IMAGE_TAG
              value: "{{ spark_image_tag }}"
            - name: SPARK_IMAGE_NAME
              value: "{{ spark_image_name }}"
            - name: IMAGE_REGISTRY
              value: "{{ image_registry }}"
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
          # This health check is taken directly from Airflow Helm chart
          # https://github.com/apache/airflow/blob/master/chart/templates/scheduler/scheduler-deployment.yaml
          # If the scheduler stops heartbeating for 5 minutes (10*30s)
          # kill it and let Kubernetes restart it
          livenessProbe:
            failureThreshold: 10
            periodSeconds: 30
            exec:
              command:
              - sh
              - -c
              - "airflow jobs check --job-type SchedulerJob --hostname $(hostname)"
          resources:
            requests:
              cpu: {{ airflow_v2_scheduler.req_cpu }}m
              memory: {{ airflow_v2_scheduler.req_mem }}Mi
          volumeMounts:
            - name: pod-template
              mountPath: /opt/airflow/pod_templates/pod_template_file.yaml
              subPath: pod_template_file.yaml
              readOnly: true
            - name: logs
              mountPath: "/opt/airflow/logs"
        # Always start the garbage collector sidecar
        # clean-logs script is included in official Airflow image
        # https://github.com/apache/airflow/blob/master/scripts/in_container/prod/clean-logs.sh
        - name: scheduler-gc
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args: ["bash", "/clean-logs"]
          volumeMounts:
            - name: logs
              mountPath: "/opt/airflow/logs"
      volumes:
        - name: pod-template
          configMap:
            name: "{{ env }}-global-airflow-v2-worker-pod-template-cm"
        - name: logs
          emptyDir: {}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ env }}-global-airflow-v2-web-deployment"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: web
spec:
  replicas: 1
  minReadySeconds: 120
  strategy:
    # Smooth rolling update of the Web UI
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow-v2"
      component: web
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow-v2"
        component: web
    spec:
      tolerations:
      - key: "group"
        operator: "Equal"
        value: "stateful"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: group
                operator: In
                values:
                - "stateful"
      restartPolicy: Always
      containers:
        - name: web
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args:
            - "webserver"
          ports:
            - name: web
              containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: "{{ env }}-global-airflow-v2-web-worker-env-cm"
          env:
            - name: SPARK_IMAGE_TAG
              value: "{{ spark_image_tag }}"
            - name: SPARK_IMAGE_NAME
              value: "{{ spark_image_name }}"
            - name: IMAGE_REGISTRY
              value: "{{ image_registry }}"
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-v2-metadata-db"
                  key: connection
          resources:
            requests:
              cpu: {{ airflow_v2_web.req_cpu }}m
              memory: {{ airflow_v2_web.req_mem }}Mi
          readinessProbe:
            httpGet:
              path: "/health"
              port: web
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 20
          livenessProbe:
            httpGet:
              path: "/health"
              port: web
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 20