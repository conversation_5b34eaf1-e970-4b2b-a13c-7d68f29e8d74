---
apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ env }}-global-airflow-migration-job"
  labels:
    name: "{{ env }}-global-airflow-migration-job"
    component: core
spec:
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow-migration-job"
        component: core
    spec:
      restartPolicy: OnFailure
      containers:
        - name: run-airflow-migrations
          image: "apache/airflow:{{ versions.airflow }}"
          imagePullPolicy: IfNotPresent
          args:
            - db
            - upgrade
          envFrom:
          - configMapRef:
              name: "{{ env }}-global-airflow-scheduler-env-cm"
          env:
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
