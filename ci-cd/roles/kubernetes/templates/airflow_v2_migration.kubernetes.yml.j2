---
apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ env }}-global-airflow-v2-migration-job"
  labels:
    name: "{{ env }}-global-airflow-v2-migration-job"
    component: core
spec:
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow-v2-migration-job"
        component: core
    spec:
      restartPolicy: OnFailure
      containers:
        - name: run-airflow-v2-migrations
          image: "apache/airflow:{{ versions.airflow_v2 }}"
          imagePullPolicy: IfNotPresent
          args:
            - db
            - init
          env:
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              value: "postgresql+psycopg2://postgres:Ninjitsu89@10.45.229.83:5432/airflow"
            - name: AIRFLOW_CONN_AIRFLOW_DB
              value: "postgresql+psycopg2://postgres:Ninjitsu89@10.45.229.83:5432/airflow"