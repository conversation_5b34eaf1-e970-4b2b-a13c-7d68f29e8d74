---
apiVersion: v1
kind: Secret
metadata:
  name: "{{ env }}-global-airflow-v2-postgresql"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: postgresql
type: Opaque
data:
  postgres-password: "{{ airflow_v2_postgres.password | b64encode }}"

---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: "{{ env }}-global-airflow-v2-postgresql-pvc"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: postgresql
spec:
  accessModes:
    - "ReadWriteOnce"
  resources:
    requests:
      storage: "{{ airflow_v2_postgres.pd_size }}"
  storageClassName: "{{ airflow_v2_postgres.storage_class }}"

---
apiVersion: v1
kind: Service
metadata:
  name: "{{ env }}-global-airflow-v2-postgresql-service"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: postgresql
spec:
  type: ClusterIP
  ports:
  - name: postgresql
    port: 5432
    targetPort: postgresql
  selector:
    name: "{{ env }}-global-airflow-v2"
    component: postgresql

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: "{{ env }}-global-airflow-v2-postgresql-statefulset"
  labels:
    name: "{{ env }}-global-airflow-v2"
    component: postgresql
spec:
  serviceName: "{{ env }}-global-airflow-v2-postgresql-service"
  updateStrategy:
    type: RollingUpdate
  template:
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow-v2"
      component: postgresql
  template:
    metadata:
      labels:
        name: "{{ env }}-global-airflow-v2"
        component: postgresql
    spec:
      tolerations:
      - key: "group"
        operator: "Equal"
        value: "stateful"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: group
                operator: In
                values:
                - "stateful"
      containers:
      - name: postgresql
        image: "postgres:{{ airflow_v2_postgres.version }}"
        imagePullPolicy: ""
        args:
        - postgres
        - -csynchronous_commit=off
        - -cmax_connections=2000
        - -cshared_buffers={{ (airflow_v2_postgres.req_mem * 0.25) | round | int }}MB
        - -cwal_buffers=256MB
        - -ceffective_cache_size={{ (airflow_v2_postgres.req_mem * 0.70) | round | int }}MB
        env:
        - name: POSTGRES_USER
          value: "{{ airflow_v2_postgres.user }}"
          # Required for pg_isready in the health probes.
        - name: PGUSER
          value: "{{ airflow_v2_postgres.user }}"
        - name: POSTGRES_DB
          value: "{{ airflow_v2_postgres.db_schema }}"
        - name: POSTGRES_INITDB_ARGS
          value: ""
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: "{{ env }}-global-airflow-v2-postgresql"
              key: postgres-password
        - name: POD_IP
          valueFrom: { fieldRef: { fieldPath: status.podIP } }
        ports:
        - name: postgresql
          containerPort: 5432
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - exec pg_isready --host $POD_IP
          initialDelaySeconds: 120
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - exec pg_isready --host $POD_IP
          initialDelaySeconds: 5
          timeoutSeconds: 3
          periodSeconds: 5
        resources:
          requests:
            cpu: {{ airflow_v2_postgres.req_cpu }}m
            memory: {{ airflow_v2_postgres.req_mem }}Mi
        volumeMounts:
        - name: data
          mountPath: /var/lib/postgresql/data/pgdata
          subPath: postgresql-db
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: "{{ env }}-global-airflow-v2-postgresql-pvc"
