  ## Postgres DB configuration
  POSTGRES_DB: "{{ airflow_v2_postgres.db_schema }}"
  POSTGRES_HOST: "{{ airflow_v2_postgres.db_host }}"
  POSTGRES_PORT: "5432"

  ## [core]
  AIRFLOW__CORE__DAGS_FOLDER: "/opt/airflow/dags"
  AIRFLOW__CORE__DEFAULT_TIMEZONE: "utc"
  AIRFLOW__CORE__EXECUTOR: "KubernetesExecutor"
  AIRFLOW__CORE__DAG_FILE_PROCESSOR_TIMEOUT: "100"
  AIRFLOW__CORE__DONOT_PICKLE: "False"
  AIRFLOW__CORE__FERNET_KEY: "{{ airflow_v2.fernet_key }}"
  AIRFLOW__CORE__KILLED_TASK_CLEANUP_TIME: "120"
  AIRFLOW__CORE__DAGBAG_IMPORT_TIMEOUT: "60"
  AIRFLOW__CORE__LOAD_DEFAULT_CONNECTIONS: "False"
  AIRFLOW__CORE__LOAD_EXAMPLES: "False"
  AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "32"
  AIRFLOW__CORE__PARALLELISM: "2048"

  ## [database]
  AIRFLOW__DATABASE__SQL_ALCHEMY_MAX_OVERFLOW: "10"
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_ENABLED: "True"
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_RECYCLE: "1800"
  AIRFLOW__DATABASE__LOAD_DEFAULT_CONNECTIONS: "False"
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_SIZE: "5"

  ## [logging]
  AIRFLOW__LOGGING__COLORED_CONSOLE_LOG: "False"
  AIRFLOW__LOGGING__LOGGING_LEVEL: "INFO"
  AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER: "gs://nv-data-{{ env }}-airflow/logs_v2"
  AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID: "google_cloud_default"
  AIRFLOW__LOGGING__REMOTE_LOGGING: "True"

  ## [kubernetes]
  AIRFLOW__KUBERNETES__DAGS_IN_IMAGE: "True"
  AIRFLOW__KUBERNETES__DELETE_WORKER_PODS: "True"
  AIRFLOW__KUBERNETES__IN_CLUSTER: "True"
  AIRFLOW__KUBERNETES__NAMESPACE: "{{ kubernetes_namespace }}"
  AIRFLOW__KUBERNETES__POD_TEMPLATE_FILE: "/opt/airflow/pod_templates/pod_template_file.yaml"
  AIRFLOW__KUBERNETES__WORKER_CONTAINER_IMAGE_PULL_POLICY: "IfNotPresent"
  AIRFLOW__KUBERNETES__WORKER_CONTAINER_REPOSITORY: "{{ image_registry }}/{{ image_name }}"
  AIRFLOW__KUBERNETES__WORKER_CONTAINER_TAG: "{{ image_tag }}"
  AIRFLOW__KUBERNETES__WORKER_PODS_CREATION_BATCH_SIZE: "300"
  AIRFLOW__KUBERNETES__WORKER_SERVICE_ACCOUNT_NAME: "{{ env }}-global-airflow"

  ## [webserver]
  AIRFLOW__WEBSERVER__BASE_URL: "{{ airflow_v2.domain }}"
  AIRFLOW__WEBSERVER__SECRET_KEY: "{{ airflow_v2.secret_key }}"
  AIRFLOW__WEBSERVER__WARN_DEPLOYMENT_EXPOSURE: "False"

  ## [smtp]
  AIRFLOW__SMTP__SMTP_HOST: "smtp.gmail.com"
  AIRFLOW__SMTP__SMTP_MAIL_FROM: "<EMAIL>"
  AIRFLOW__SMTP__SMTP_PASSWORD: "fmpuuobxdsdfbjum"
  AIRFLOW__SMTP__SMTP_PORT: "587"
  AIRFLOW__SMTP__SMTP_SSL: "False"
  AIRFLOW__SMTP__SMTP_STARTTLS: "True"
  AIRFLOW__SMTP__SMTP_USER: "<EMAIL>"

  ## [scheduler]
  ## TODO: Check appropriate value for parsing_processes when running scheduler HA
  AIRFLOW__SCHEDULER__JOB_HEARTBEAT_SEC: "30"
  AIRFLOW__SCHEDULER__ORPHANED_TASKS_CHECK_INTERVAL: "1800"
  AIRFLOW__SCHEDULER__PARSING_PROCESSES: "{{ ((airflow_v2_scheduler.req_cpu / 1000 - 1) * 4) | round | int }}"
  AIRFLOW__SCHEDULER__SCHEDULE_AFTER_TASK_EXECUTION: "False"
  AIRFLOW__SCHEDULER__SCHEDULER_HEARTBEAT_SEC: "30"
  AIRFLOW__SCHEDULER__SCHEDULER_HEALTH_CHECK_THRESHOLD: "120"
  AIRFLOW__SCHEDULER__ZOMBIE_DETECTION_INTERVAL: "1800"
  AIRFLOW__SCHEDULER__SCHEDULER_ZOMBIE_TASK_THRESHOLD: "3600"
  AIRFLOW__SCHEDULER__MAX_TIS_PER_QUERY: "256"

  ## [metrics]
  AIRFLOW__METRICS__STATSD_HOST: "monitoring-statsd-exporter.data-monitoring.svc.cluster.local"
  AIRFLOW__METRICS__STATSD_ON: "True"
  AIRFLOW__METRICS__STATSD_PORT: "9125"
  AIRFLOW__METRICS__STATSD_PREFIX: "airflow.{{ kubernetes_namespace }}"

  ## [api]
  AIRFLOW__API__AUTH_BACKENDS: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
