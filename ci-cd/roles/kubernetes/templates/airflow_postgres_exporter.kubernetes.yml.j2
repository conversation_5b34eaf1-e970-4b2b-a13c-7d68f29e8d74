---
apiVersion: v1
kind: Service
metadata:
  labels:
    name: "{{ env }}-global-airflow-postgres-exporter"
  name: "{{ env }}-global-airflow-postgres-exporter-service"
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 9187
      protocol: TCP
      name: http
  selector:
    name: "{{ env }}-global-airflow-postgres-exporter"

---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    name: "{{ env }}-global-airflow-postgres-exporter"
  name: "{{ env }}-global-airflow-postgres-exporter-cm"
data:
  config.yaml: |
    pg_replication:
      query: "SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()))::INT as lag"
      metrics:
        - lag:
            usage: "GAUGE"
            description: "Replication lag behind master in seconds"

    pg_postmaster:
      query: "SELECT pg_postmaster_start_time as start_time_seconds from pg_postmaster_start_time()"
      metrics:
        - start_time_seconds:
            usage: "GAUGE"
            description: "Time at which postmaster started"

    pg_stat_user_tables:
      query: "SELECT schemaname, relname, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch, n_tup_ins, n_tup_upd, n_tup_del, n_tup_hot_upd, n_live_tup, n_dead_tup, n_mod_since_analyze, last_vacuum, last_autovacuum, last_analyze, last_autoanalyze, vacuum_count, autovacuum_count, analyze_count, autoanalyze_count FROM pg_stat_user_tables"
      metrics:
        - schemaname:
            usage: "LABEL"
            description: "Name of the schema that this table is in"
        - relname:
            usage: "LABEL"
            description: "Name of this table"
        - seq_scan:
            usage: "COUNTER"
            description: "Number of sequential scans initiated on this table"
        - seq_tup_read:
            usage: "COUNTER"
            description: "Number of live rows fetched by sequential scans"
        - idx_scan:
            usage: "COUNTER"
            description: "Number of index scans initiated on this table"
        - idx_tup_fetch:
            usage: "COUNTER"
            description: "Number of live rows fetched by index scans"
        - n_tup_ins:
            usage: "COUNTER"
            description: "Number of rows inserted"
        - n_tup_upd:
            usage: "COUNTER"
            description: "Number of rows updated"
        - n_tup_del:
            usage: "COUNTER"
            description: "Number of rows deleted"
        - n_tup_hot_upd:
            usage: "COUNTER"
            description: "Number of rows HOT updated (i.e., with no separate index update required)"
        - n_live_tup:
            usage: "GAUGE"
            description: "Estimated number of live rows"
        - n_dead_tup:
            usage: "GAUGE"
            description: "Estimated number of dead rows"
        - n_mod_since_analyze:
            usage: "GAUGE"
            description: "Estimated number of rows changed since last analyze"
        - last_vacuum:
            usage: "GAUGE"
            description: "Last time at which this table was manually vacuumed (not counting VACUUM FULL)"
        - last_autovacuum:
            usage: "GAUGE"
            description: "Last time at which this table was vacuumed by the autovacuum daemon"
        - last_analyze:
            usage: "GAUGE"
            description: "Last time at which this table was manually analyzed"
        - last_autoanalyze:
            usage: "GAUGE"
            description: "Last time at which this table was analyzed by the autovacuum daemon"
        - vacuum_count:
            usage: "COUNTER"
            description: "Number of times this table has been manually vacuumed (not counting VACUUM FULL)"
        - autovacuum_count:
            usage: "COUNTER"
            description: "Number of times this table has been vacuumed by the autovacuum daemon"
        - analyze_count:
            usage: "COUNTER"
            description: "Number of times this table has been manually analyzed"
        - autoanalyze_count:
            usage: "COUNTER"
            description: "Number of times this table has been analyzed by the autovacuum daemon"

    pg_statio_user_tables:
      query: "SELECT schemaname, relname, heap_blks_read, heap_blks_hit, idx_blks_read, idx_blks_hit, toast_blks_read, toast_blks_hit, tidx_blks_read, tidx_blks_hit FROM pg_statio_user_tables"
      metrics:
        - schemaname:
            usage: "LABEL"
            description: "Name of the schema that this table is in"
        - relname:
            usage: "LABEL"
            description: "Name of this table"
        - heap_blks_read:
            usage: "COUNTER"
            description: "Number of disk blocks read from this table"
        - heap_blks_hit:
            usage: "COUNTER"
            description: "Number of buffer hits in this table"
        - idx_blks_read:
            usage: "COUNTER"
            description: "Number of disk blocks read from all indexes on this table"
        - idx_blks_hit:
            usage: "COUNTER"
            description: "Number of buffer hits in all indexes on this table"
        - toast_blks_read:
            usage: "COUNTER"
            description: "Number of disk blocks read from this table's TOAST table (if any)"
        - toast_blks_hit:
            usage: "COUNTER"
            description: "Number of buffer hits in this table's TOAST table (if any)"
        - tidx_blks_read:
            usage: "COUNTER"
            description: "Number of disk blocks read from this table's TOAST table indexes (if any)"
        - tidx_blks_hit:
            usage: "COUNTER"
            description: "Number of buffer hits in this table's TOAST table indexes (if any)"

    pg_database:
      query: "SELECT pg_database.datname, pg_database_size(pg_database.datname) as size FROM pg_database"
      metrics:
        - datname:
            usage: "LABEL"
            description: "Name of the database"
        - size:
            usage: "GAUGE"
            description: "Disk space used by the database"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    name: "{{ env }}-global-airflow-postgres-exporter"
  name: "{{ env }}-global-airflow-postgres-exporter-deployment"
spec:
  replicas: 1
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow-postgres-exporter"
  template:
    metadata:
      labels:
        name: "{{ env }}-global-airflow-postgres-exporter"
      annotations:
        checksum/config: 803f15947512c98ae9e15ea2bcb8a461ea8744a90882b3cc3ee47dd124d6cf22
        prometheus.io/path: "/metrics"
        prometheus.io/port: "9187"
        prometheus.io/scrape: "true"
    spec:
      containers:
        - name: postgres-exporter
          args:
          - "--extend.query-path=/etc/config.yaml"
          env:
          - name: DATA_SOURCE_URI
            value: "{{ env }}-global-airflow-postgresql-service.{{ kubernetes_namespace }}.svc.cluster.local:5432/?sslmode=disable"
          - name: DATA_SOURCE_USER
            value: "postgres"
          - name: DATA_SOURCE_PASS
            valueFrom:
              secretKeyRef:
                name: "{{ env }}-global-airflow-postgresql"
                key: postgres-password
          image: "wrouesnel/postgres_exporter:{{ airflow_postgres_exporter.version }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 9187
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {}
          volumeMounts:
            - name: queries
              mountPath: /etc/config.yaml
              subPath: config.yaml
      securityContext:
        {}
      volumes:
      - configMap:
          defaultMode: 420
          name: "{{ env }}-global-airflow-postgres-exporter-cm"
        name: queries
