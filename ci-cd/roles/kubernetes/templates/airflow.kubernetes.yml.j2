---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: "{{ env }}-global-airflow-pdb"
  labels:
    name: "{{ env }}-global-airflow"
    component: scheduler
spec:
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow"
      component: scheduler
  maxUnavailable: 1

---
apiVersion: v1
kind: Secret
metadata:
  name: "{{ env }}-global-airflow-env"
  labels:
    name: "{{ env }}-global-airflow"
    component: core
type: Opaque
data:
  postgresUser: "{{ airflow_postgres.user | b64encode }}"
  postgresPassword: "{{ airflow_postgres.password | b64encode }}"

---
kind: Secret
apiVersion: v1
metadata:
  name: "{{ env }}-global-airflow-metadata-db"
  labels:
    name: "{{ env }}-global-airflow-metadata-db"
type: Opaque
data:
  connection: "{{ sql_alchemy_conn | b64encode }}"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-web-worker-env-cm"
  labels:
    name: "{{ env }}-global-airflow"
    component: core
data:
{% include "airflow_cfg_common.properties.j2" %}

  # Webserver and worker config
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_SIZE: "5"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-scheduler-env-cm"
  labels:
    name: "{{ env }}-global-airflow"
    component: core
data:
{% include "airflow_cfg_common.properties.j2" %}

  # Scheduler specific config
  AIRFLOW__DATABASE__SQL_ALCHEMY_POOL_SIZE: "500"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ env }}-global-airflow-worker-pod-template-cm"
  labels:
    name: "{{ env }}-global-airflow"
    component: worker
data:
  pod_template_file.yaml: |-
    ---
    apiVersion: v1
    kind: Pod
    metadata:
      name: "{{ env }}-global-airflow-worker"
    spec:
      containers:
        - name: worker
          args: []
          command: []
          envFrom:
            - configMapRef:
                name: "{{ env }}-global-airflow-web-worker-env-cm"
          env:
            - name: AIRFLOW__CORE__EXECUTOR
              value: LocalExecutor
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          ports: []
          resources:
            limits:
              memory: 14400Mi
            requests:
              cpu: 500m
              memory: 3600Mi
      hostNetwork: false
      restartPolicy: Never
      serviceAccountName: "{{ env }}-global-airflow"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ env }}-global-airflow"
  labels:
    name: "{{ env }}-global-airflow"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: "{{ env }}-global-airflow"
  labels:
    name: "{{ env }}-global-airflow"
rules:
- apiGroups: [""]
  resources:
  - pods
  - services
  - configmaps
  verbs: ["create", "get", "delete", "list", "watch", "patch", "update"]
- apiGroups: [""]
  resources:
  - "pods/log"
  verbs: ["get", "list"]
- apiGroups: [""]
  resources:
  - "pods/exec"
  verbs: ["create", "get"]
- apiGroups:
  - sparkoperator.k8s.io
  resources:
  - sparkapplications
  - scheduledsparkapplications
  - sparkapplications/status
  - scheduledsparkapplications/status
  verbs:
  - create
  - get
  - delete
  - list
  - watch
  - patch
  - update

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: "{{ env }}-global-airflow"
  labels:
    name: "{{ env }}-global-airflow"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: "{{ env }}-global-airflow"
subjects:
- kind: ServiceAccount
  name: "{{ env }}-global-airflow"
  namespace: "{{ kubernetes_namespace }}"

---
apiVersion: v1
kind: Service
metadata:
  name: "{{ env }}-global-airflow-web-service"
  labels:
    name: "{{ env }}-global-airflow"
    component: web
spec:
  type: ClusterIP
  selector:
    name: "{{ env }}-global-airflow"
    component: web
  ports:
    - name: web
      protocol: TCP
      port: 8080

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ env }}-global-airflow-scheduler-deployment"
  labels:
    name: "{{ env }}-global-airflow"
    component: scheduler
spec:
  replicas: {{ airflow_scheduler.num_replicas }}
  strategy:
    # Kill the scheduler as soon as possible. It will restart quickly with all the workers,
    # minimizing the time they are not synchronized.
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 100%
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow"
      component: scheduler
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow"
        component: scheduler
    spec:
      tolerations:
      - key: "group"
        operator: "Equal"
        value: "high-cpu"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: group
                operator: In
                values:
                - "high-cpu"
      restartPolicy: Always
      serviceAccountName: "{{ env }}-global-airflow"
      containers:
        - name: scheduler
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args:
            - "scheduler"
          envFrom:
          - configMapRef:
              name: "{{ env }}-global-airflow-scheduler-env-cm"
          env:
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
          # This health check is taken directly from Airflow Helm chart
          # https://github.com/apache/airflow/blob/master/chart/templates/scheduler/scheduler-deployment.yaml
          # If the scheduler stops heartbeating for 5 minutes (10*30s)
          # kill it and let Kubernetes restart it
          livenessProbe:
            failureThreshold: 10
            periodSeconds: 30
            exec:
              command:
              - sh
              - -c
              - "airflow jobs check --job-type SchedulerJob --hostname $(hostname)"
          resources:
            requests:
              cpu: {{ airflow_scheduler.req_cpu }}m
              memory: {{ airflow_scheduler.req_mem }}Mi
          volumeMounts:
            - name: pod-template
              mountPath: /opt/airflow/pod_templates/pod_template_file.yaml
              subPath: pod_template_file.yaml
              readOnly: true
            - name: logs
              mountPath: "/opt/airflow/logs"
        # Always start the garbage collector sidecar
        # clean-logs script is included in official Airflow image
        # https://github.com/apache/airflow/blob/master/scripts/in_container/prod/clean-logs.sh
        - name: scheduler-gc
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args: ["bash", "/clean-logs"]
          volumeMounts:
            - name: logs
              mountPath: "/opt/airflow/logs"
      volumes:
        - name: pod-template
          configMap:
            name: "{{ env }}-global-airflow-worker-pod-template-cm"
        - name: logs
          emptyDir: {}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ env }}-global-airflow-web-deployment"
  labels:
    name: "{{ env }}-global-airflow"
    component: web
spec:
  replicas: 1
  minReadySeconds: 120
  strategy:
    # Smooth rolling update of the Web UI
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      name: "{{ env }}-global-airflow"
      component: web
  template:
    metadata:
      annotations:
      labels:
        name: "{{ env }}-global-airflow"
        component: web
    spec:
      tolerations:
      - key: "group"
        operator: "Equal"
        value: "stateful"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: group
                operator: In
                values:
                - "stateful"
      restartPolicy: Always
      containers:
        - name: web
          image: "{{ image_registry }}/{{ image_name }}:{{ image_tag }}"
          imagePullPolicy: IfNotPresent
          args:
            - "webserver"
          ports:
            - name: web
              containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: "{{ env }}-global-airflow-web-worker-env-cm"
          env:
            - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: "{{ env }}-global-airflow-metadata-db"
                  key: connection
          resources:
            requests:
              cpu: {{ airflow_web.req_cpu }}m
              memory: {{ airflow_web.req_mem }}Mi
          readinessProbe:
            httpGet:
              path: "/health"
              port: web
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 20
          livenessProbe:
            httpGet:
              path: "/health"
              port: web
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 20
