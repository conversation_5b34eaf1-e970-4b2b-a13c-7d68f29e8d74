---
  - name: Clean dist {{ svc }} directory
    file:
      path: "dist/{{ svc }}"
      state: absent

  - name: Create dist {{ svc }} directory
    file:
      path: "dist/{{ svc }}"
      state: directory

  - name: Generate {{ svc }} yml
    template:
      src: "{{ svc }}.kubernetes.yml.j2"
      dest: "dist/{{ svc }}/kubernetes.yml"

  - name: Deploy {{ svc }} yml
    command: "kubectl apply -f dist/{{ svc }}/kubernetes.yml --context={{ kubernetes_context }} -n {{ kubernetes_namespace }}"
