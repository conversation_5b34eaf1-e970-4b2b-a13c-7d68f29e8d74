---
  - name: Create dist docker scripts directory
    file:
      path: "dist/docker/{{ svc }}/scripts"
      state: directory

  - name: Create dist dags directory
    file:
      path: "dist/docker/{{ svc }}/dags"
      state: directory

  - name: Copy log4j conf
    synchronize:
      src: "../../../../log4j.properties"
      dest: "dist/docker/{{ svc }}"

  - name: Copy dags
    synchronize:
      src: "../../../../dags"
      dest: "dist/docker/{{ svc }}"

  - name: Zip up dags
    command: "zip -r dags.zip dags"
    args:
      chdir: "dist/docker/{{ svc }}/"

  - name: Create dist plugins directory
    file:
      path: "dist/docker/{{ svc }}/plugins"
      state: directory

  - name: Copy plugins
    synchronize:
      src: "../../../../plugins"
      dest: "dist/docker/{{ svc }}"

  - name: Copy additional required files
    synchronize:
      src: "."
      dest: "dist/docker/{{ svc }}/files"

  - name: Create keys directory
    file:
      path: "dist/docker/{{ svc }}/files/keys"
      state: directory

  - name: Copy {{ svc }} requirements
    synchronize:
      src: "../../../../requirements-{{ svc }}.txt"
      dest: "dist/docker/{{ svc }}"

  - name: Generate spark-defaults.conf
    template:
      src: "spark-defaults.conf.j2"
      dest: "dist/docker/{{ svc }}/spark-defaults.conf"

  - name: Generate spark-defaults-lh.conf
    template:
      src: "spark-defaults-lh.conf.j2"
      dest: "dist/docker/{{ svc }}/spark-defaults-lh.conf"