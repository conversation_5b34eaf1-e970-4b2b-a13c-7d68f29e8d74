FROM apache/airflow:{{ versions.airflow_v2 }}-{{ versions.python_v2 }}
USER root

ENV SPARK_HOME /home/<USER>/.local/lib/{{ versions.python_v2 }}/site-packages/pyspark
ENV LH_SPARK_HOME /opt/lh-spark
ENV PATH $PATH:$SPARK_HOME/bin
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update -y \
    && apt-get install -y \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsnappy-dev \
        procps \
        software-properties-common \
        unzip \
        wget \
        apt-transport-https \
    && mkdir -p /etc/apt/keyrings \
    && wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | tee /etc/apt/keyrings/adoptium.asc \
    && echo "deb [signed-by=/etc/apt/keyrings/adoptium.asc] https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update -y \
    && apt-get install -y temurin-8-jdk \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

USER airflow
RUN pip install --upgrade pip==24.2
RUN pip install delta-spark==3.2.0
COPY --chown=airflow:root ./requirements-airflow_v2.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

USER root

RUN mkdir -p $SPARK_HOME/jars \
    && cd $SPARK_HOME/jars \
    && curl -O https://raw.githubusercontent.com/huaweicloud/obsa-hdfs/master/release/hadoop-huaweicloud-3.1.1-hw-54.0.jar \
    && curl -O https://storage.googleapis.com/hadoop-lib/gcs/gcs-connector-hadoop2-2.0.1.jar

COPY files $AIRFLOW_HOME/files
COPY spark-defaults.conf $SPARK_HOME/conf/
COPY log4j.properties $SPARK_HOME/conf/
COPY plugins $AIRFLOW_HOME/plugins
COPY dags $AIRFLOW_HOME/dags

RUN mkdir -p $LH_SPARK_HOME/bin $LH_SPARK_HOME/conf \
    && cd $LH_SPARK_HOME/bin \
    && curl -O https://storage.googleapis.com/storage-data.ninjavan.co/lakehouse/bin/spark-submit-1.0-cz-2025-429.jar \
    && curl -O https://storage.googleapis.com/storage-data.ninjavan.co/lakehouse/bin/spark3-submit \
    && chmod +x $LH_SPARK_HOME/bin/spark3-submit

COPY spark-defaults-lh.conf $LH_SPARK_HOME/conf/spark-defaults.conf

ENV PATH $PATH:$LH_SPARK_HOME/bin

RUN ln -s $AIRFLOW_HOME/files/keys /keys \
    && chown -R airflow:root /keys $AIRFLOW_HOME $SPARK_HOME $LH_SPARK_HOME

USER airflow