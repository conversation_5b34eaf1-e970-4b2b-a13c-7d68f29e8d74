# lh-spark
spark.master                                                    njv-ap-southeast-3-huaweicloud.api.singdata.com
spark.cz.instance.name                                          d70422ae
spark.cz.vcluster                                               spark_vc
spark.cz.workspace                                              quick_start
spark.cz.client.protocol                                        http
spark.cz.client.async                                           false
spark.cz.user.name                                              singdata_dev
spark.cz.password                                               1qaz@WSedc$RFV
spark.dynamicAllocation.enabled                                 true
spark.cz.resource.pcores-vcores-multiplier                      20

# extra deps
spark.jars                                                      volume://symlink/public_resources/spark/df-adapter-impl_2.12-1.0.0-SNAPSHOT-shaded.jar
spark.files                                                     volume://symlink/public_resources/spark/log4j2.properties

# python
spark.archives                                                  volume://symlink/public_resources/spark/njv-spark-3.8.tar.gz#PY3,obs://nv-data-prod-dependencies/clickzetta/python/dags.zip#DAG
spark.pyspark.python                                            ./PY3/njv-spark-3.8/bin/python3

spark.yarn.appMasterEnv.PYTHONPATH                              ./DAG/dags
spark.executorEnv.PYTHONPATH                                    ./DAG/dags

spark.yarn.appMasterEnv.OBS_ACCESS_KEY                          AWM6EX8CYEHYTXCRLDQA 
spark.yarn.appMasterEnv.OBS_SECRET_KEY                          R3mJsB83MTapzIHjf1WALxveTOBbG9NAHJu5HO9C 
spark.yarn.appMasterEnv.OBS_ENDPOINT                            obs.ap-southeast-3.myhuaweicloud.com
spark.executorEnv.OBS_ACCESS_KEY                                AWM6EX8CYEHYTXCRLDQA 
spark.executorEnv.OBS_SECRET_KEY                                R3mJsB83MTapzIHjf1WALxveTOBbG9NAHJu5HO9C 
spark.executorEnv.OBS_ENDPOINT                                  obs.ap-southeast-3.myhuaweicloud.com
spark.executor.extraClassPath                                   $PWD/__spark_plugin_libs__/*

# delta
spark.sql.extensions                                            io.delta.sql.DeltaSparkSessionExtension,org.apache.spark.ClickZettaSparkExtension
spark.sql.catalog.spark_catalog                                 org.apache.spark.sql.delta.catalog.DeltaCatalog

# hms
spark.hadoop.hive.metastore.uris                                thrift://10.64.46.105:9083
spark.sql.catalogImplementation                                 hive

# obs
spark.hadoop.fs.obs.impl                                        org.apache.hadoop.fs.obs.OBSFileSystem
spark.hadoop.fs.AbstractFileSystem.obs.impl                     org.apache.hadoop.fs.obs.OBS
spark.hadoop.fs.obs.access.key                                  AWM6EX8CYEHYTXCRLDQA
spark.hadoop.fs.obs.secret.key                                  R3mJsB83MTapzIHjf1WALxveTOBbG9NAHJu5HO9C
spark.hadoop.fs.obs.endpoint                                    obs.ap-southeast-3.myhuaweicloud.com

# cz accelerate
spark.cz.accelerate.mode.enabled                                true
spark.cz.accelerate.mode.convert.fallback.enabled               false
spark.cz.accelerate.mode.execute.fallback.enabled               false
spark.cz.hive.catalog                                           spark_hive_catalog
spark.cz.storage.connection                                     spark_storage_obs
spark.cz.query.url                                              *************************************************************************************************************************************************************************
spark.cz.query.timeout                                          24000

# cz accelerate query hints
spark.cz.query.props.cz.optimizer.enable.agg.push.down          false
spark.cz.query.props.cz.storage.parquet.block.size              134217728
spark.cz.query.props.cz.storage.parquet.page.size               1048576
spark.cz.query.props.cz.storage.parquet.dictionary.page.size    2097152
spark.cz.query.props.cz.mapper.file.split.size                  128000000
spark.cz.query.props.cz.optimizer.table.sink.enable.adaptive    false
spark.cz.query.props.cz.sql.dag.shuffle.vertex.manager.hude.dop 20000000
spark.cz.query.props.cz.sql.dag.speculation.enabled             false
spark.cz.query.props.cz.optimizer.stage.max.dop                 1200
spark.cz.query.props.cz.optimizer.pass.through.enabled          false
spark.cz.query.props.cz.storage.column.mapping.mode             field_name_ignore_case
spark.cz.query.props.cz.storage.parquet.compression             snappy
spark.cz.query.props.cz.optimizer.dictionary.encoding.mode      off
spark.cz.query.props.cz.optimizer.enable.agg.remove             false
spark.cz.query.props.cz.optimizer.join.reorder.threshold        0
# spark.cz.query.props.cz.shuffle.pull.merge.enabled            true
# spark.cz.query.props.cz.meta.connector.debug.enable           true
# spark.cz.query.props.cz.meta.skip.file.slice.cache            true