spark.submit.deployMode                                         cluster
spark.kubernetes.container.image                                {{ docker_registry }}/nv-data-{{ branch | lower }}-spark-dags:{{ versions.spark }}-{{ env }}{{ tag }}
spark.kubernetes.container.image.pullPolicy                     IfNotPresent
spark.kubernetes.container.image.pullSecrets                    gar-creds
spark.kubernetes.namespace                                      data-{{ env }}
spark.kubernetes.authenticate.driver.serviceAccountName         airflow-spark
spark.kubernetes.driver.podTemplateFile                         /opt/airflow/files/templates/data-lake-driver.yml
spark.kubernetes.executor.podTemplateFile                       /opt/airflow/files/templates/data-lake-executor.yml
spark.hadoop.fs.obs.buffer.max.range                            6291456
spark.hadoop.fs.obs.buffer.part.size                            2097152
spark.hadoop.fs.obs.threads.read.core                           500
spark.hadoop.fs.obs.threads.read.max                            1000
spark.hadoop.fs.obs.write.buffer.size                           8192
spark.hadoop.fs.obs.read.buffer.size                            8192
spark.hadoop.fs.obs.connection.maximum                          1000
spark.kubernetes.driver.secretKeyRef.NV_DB_USER                 airflow-nv-db-creds:DB_USER
spark.kubernetes.driver.secretKeyRef.NV_DB_PASSWORD             airflow-nv-db-creds:DB_PASSWORD
spark.kubernetes.driver.secretKeyRef.OBS_ACCESS_KEY             airflow-obs-creds:OBS_ACCESS_KEY
spark.kubernetes.driver.secretKeyRef.OBS_SECRET_KEY             airflow-obs-creds:OBS_SECRET_KEY
spark.kubernetes.driver.secretKeyRef.OBS_ENDPOINT               airflow-obs-creds:OBS_ENDPOINT
spark.kubernetes.executor.secretKeyRef.OBS_ACCESS_KEY           airflow-obs-creds:OBS_ACCESS_KEY
spark.kubernetes.executor.secretKeyRef.OBS_SECRET_KEY           airflow-obs-creds:OBS_SECRET_KEY
spark.kubernetes.executor.secretKeyRef.OBS_ENDPOINT             airflow-obs-creds:OBS_ENDPOINT
spark.hadoop.fs.obs.access.key                                  ${env.OBS_ACCESS_KEY}
spark.hadoop.fs.obs.secret.key                                  ${env.OBS_SECRET_KEY}
spark.hadoop.fs.obs.endpoint                                    ${env.OBS_ENDPOINT}
spark.hadoop.fs.obs.buffer.dir                                  /tmp
spark.hadoop.fs.obs.impl                                        org.apache.hadoop.fs.obs.OBSFileSystem
spark.hadoop.fs.AbstractFileSystem.obs.impl                     org.apache.hadoop.fs.obs.OBS
spark.hadoop.fs.obs.connection.ssl.enabled                      false
spark.hadoop.fs.obs.fast.upload                                 true
spark.hadoop.fs.obs.socket.send.buffer                          65536
spark.hadoop.fs.obs.socket.recv.buffer                          65536
spark.hadoop.fs.obs.max.total.tasks                             20
spark.hadoop.fs.obs.threads.max                                 20
spark.hadoop.hive.metastore.uris                                thrift://data-{{ env }}-global-hive-metastore-service:9083
spark.sql.extensions                                            io.delta.sql.DeltaSparkSessionExtension
spark.sql.catalog.spark_catalog                                 org.apache.spark.sql.delta.catalog.DeltaCatalog
spark.sql.files.maxPartitionBytes                               128MB
spark.sql.adaptive.coalescePartitions.enabled                   true
spark.sql.adaptive.advisoryPartitionSizeInBytes                 64MB
spark.sql.adaptive.skewJoin.enabled                             true

spark.kubernetes.driver.secrets.gcp-service-account-key         /keys
spark.kubernetes.executor.secrets.gcp-service-account-key       /keys
spark.hadoop.google.cloud.auth.service.account.enable           true
spark.hadoop.google.cloud.auth.service.account.json.keyfile     /keys/key.json
