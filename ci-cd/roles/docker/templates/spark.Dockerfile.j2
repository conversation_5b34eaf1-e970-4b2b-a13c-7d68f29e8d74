# This Spark base image is generated in JupyterHub repo.
FROM {{ spark_base_img }}

USER root

ENV AIRFLOW_HOME /opt/airflow
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags


# Install dependencies
# Install gcloud as root to a directory other users have access to
RUN apt-get update --allow-releaseinfo-change-suite -y \
    && apt-get install -y \
        python3-dev \
        curl \
    && pip3 install --upgrade pip

RUN cd /opt \
    && curl -O https://storage.googleapis.com/cloud-sdk-release/google-cloud-cli-450.0.0-linux-x86.tar.gz \
    && tar -xvzf google-cloud-cli-450.0.0-linux-x86.tar.gz \
    && ./google-cloud-sdk/install.sh \
    && export PATH="$PATH:/opt/google-cloud-sdk/bin/gcloud" \
    && rm -rf /var/lib/apt/lists/*

ADD https://repo1.maven.org/maven2/io/prometheus/jmx/jmx_prometheus_javaagent/0.20.0/jmx_prometheus_javaagent-0.20.0.jar $SPARK_HOME/jars/
ADD https://raw.githubusercontent.com/huaweicloud/obsa-hdfs/master/release/hadoop-huaweicloud-3.1.1-hw-54.0.jar $SPARK_HOME/jars
ADD https://storage.googleapis.com/hadoop-lib/gcs/gcs-connector-hadoop2-2.0.1.jar $SPARK_HOME/jars 
RUN chmod -R 644 $SPARK_HOME/jars/jmx_prometheus_javaagent-0.20.0.jar \
    && chmod -R 644 $SPARK_HOME/jars/hadoop-huaweicloud-3.1.1-hw-54.0.jar \
    && chmod -R 644 $SPARK_HOME/jars/gcs-connector-hadoop2-2.0.1.jar

COPY ./requirements-spark.txt requirements.txt
RUN pip install -r requirements.txt

COPY spark-defaults.conf $SPARK_HOME/conf/
COPY log4j.properties $SPARK_HOME/conf/


COPY --chmod=755 files $AIRFLOW_HOME/files
COPY --chmod=755 dags $AIRFLOW_HOME/dags
COPY --chmod=755 plugins $AIRFLOW_HOME/plugins

# zip dags folder for use of spark UDF
RUN tar -cvzf $AIRFLOW_HOME/files/dags.zip $AIRFLOW_HOME/dags


RUN chown -R spark:spark $SPARK_HOME/conf \
    && chown spark:spark $SPARK_HOME/jars/jmx_prometheus_javaagent-0.20.0.jar \
    && chown spark:spark $SPARK_HOME/jars/hadoop-huaweicloud-3.1.1-hw-54.0.jar

USER spark