---
- name: Get HWC directory in Bamboo
  set_fact:
    hwc_dir: "{{ lookup('env', 'bamboo_NV_HWC_PATH') }}"

- name: Read HWC credentials
  include_vars: "{{ hwc_dir }}/bamboo.hwc.obs.creds"

- name: Get obsutil path
  set_fact:
    obsutil: "{{ hwc_dir }}/obsutil_linux_amd64_5.4.11/obsutil"

- name: Configure obsutil
  environment:
    HW_ACCESS_KEY: "{{ bamboo_hwc_obs_creds[hwc_project_id].ak }}"
    HW_SECRET_KEY: "{{ bamboo_hwc_obs_creds[hwc_project_id].sk }}"
    HWC_ENDPOINT: "{{ bamboo_hwc_obs_creds[hwc_project_id].endpoint }}"
  shell: "{{ obsutil }} config -i=$HW_ACCESS_KEY -k=$HW_SECRET_KEY -e=$HWC_ENDPOINT"

- name: Upload zipped DAG files to OBS
  shell: >
    {{ obsutil }} cp
    dist/docker/airflow_v2/dags.zip obs://{{ obs_dependencies }}/clickzetta/python/

- name: Upload Airflow tasks to OBS
  shell: >
    {{ obsutil }} sync
    dist/docker/airflow_v2/dags/data_warehouse/tasks/ obs://{{ obs_dependencies }}/clickzetta/spark/airflow_tasks/

- name: Update env vars
  shell: >
    helm get values airflow -o json --kube-context {{ kubernetes_context }} --namespace {{ kubernetes_namespace }} | jq '(.env)' | jq --compact-output --raw-output '. |= map(if .name=="AIRFLOW__KUBERNETES_EXECUTOR__WORKER_CONTAINER_TAG" then .value = "{{ image_tag }}" else . end)'
  register: new_env_values

- name: Restart Helm deployments
  command: >
    helm upgrade --kube-context {{ kubernetes_context }} --namespace {{ kubernetes_namespace }} --version 1.15.0 --reuse-values
    --set images.airflow.tag={{ image_tag }},images.airflow.pullPolicy=IfNotPresent,images.pod_template.tag={{ image_tag }},images.pod_template.pullPolicy=IfNotPresent
    --set-json 'env={{ new_env_values.stdout }}'
    airflow {{ svc_vars.repo_name }}/{{ svc }}
  register: out

- name: Result of restart
  debug:
    msg: "{{ out.stdout_lines }}"